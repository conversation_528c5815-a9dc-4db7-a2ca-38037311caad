## Moodle Integration Local Setup Guide

### Install moodle from their site official instruction

    https://docs.moodle.org/405/en/Installation_quick_guide

### Mandatory Plugins

Install below plugins first than install the galaxy plugin by uploading the zip file.

- Moodle Webhook https://moodle.org/plugins/local_webhooks
- OpenID Connect https://moodle.org/plugins/auth_oidc
- Enable **OpenID Connect** authentication at
    ##### **Site administration > Authentication > Manage authentication**
- Either should use admin token or allow silent email update by disabling **Email address change confirmation** at
    ##### **Site administration > Users > Permissions > User policies**
- Download the Galaxy360 Moodle plugin from **[here](/uploads/competency.zip)** and Upload this zip file to your Moodle installation via
    ##### **Site administration > Plugins > Install plugins**
- Update firstname and lastname mapping for oidc plugin.
    ##### **Administration -> Plugins -> Authentication -> OpenID Connect -> Field mappings**

### Integration Setup Moodle Side

- Visit **{{ $moodle_url }}/admin/settings.php?section=externalservices** where you will see **_Galaxy360 External Web Service_** external service.
    - Click Authorized Users and add your admin user as authorized user.
- Visit **{{ $moodle_url }}/admin/webservice/tokens.php** page and click **\*Create Token** button to add new token.
    - User should be the same user that was authorized to use the external service.
    - In service dropdown choose **_Galaxy360 External Web Service_**
    - Uncheck Valid until enabled checkbox.
- Copy the provided token and paste to moodle setup input field on galaxy.
- Visit **Site Administration > Appearances >Additional HTML** or visit url **{{ $moodle_url }}/admin/settings.php?section=additionalhtml**

    - Paste the code below to input field **_Before BODY is closed_**

    `<script src="/mod/lti/service/competency/amd/src/login_modifier.js"></script>`

- Visit **Authentication > OpenID Connect > IdP and authentication** or visit url **{{ $moodle_url }}/auth/oidc/manageapplication.php**
    - **_Application ID:_** {{ $application_id }}
    - **_Client authentication method:_** Other
    - **_Client Secret:_** {{ $client_secret }}
    - **_Authorization Endpoint:_** {{ $authorization_endpoint }}
    - **_Token Endpoint:_** {{ $token_endpoint }}
    - **_Scope:_** {{ $scopes }}

### Integration Setup Galaxy Side

- Visit **{{ $galaxy_url }}/integrations/moodle/setup**
    - Provide your moodle installation url
    - Paste your token you copied from moodle
    - You can give your connection a name. This will help when trying to reconnect to theold connection.
- Save and click Moodle Connect button.

### After Connection Steps

- First sync grades from right box **_Moodle Grades Mapping_**
- Choose the scale for vet course
    - Choose the scale value for galaxy side marking.
