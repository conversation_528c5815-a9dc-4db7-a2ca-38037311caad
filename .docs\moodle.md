## Moodle Integration Local Setup Guide

- # Install moodle from their site official instruction
    https://docs.moodle.org/405/en/Installation_quick_guide

### Mandatory Plugins

Install below plugins first than install the galaxy plugin by uploading the zip file.

- Moodle Webhook https://moodle.org/plugins/local_webhooks
- OpenID Connect https://moodle.org/plugins/auth_oidc
- Enable **OpenID Connect** authentication at
    ##### **Site administration > Authentication > Manage authentication**
- Either should use admin token or allow silent email update by disabling **Email address change confirmation** at
    ##### **Site administration > Users > Permissions > User policies**
- Download the Galaxy360 Moodle plugin from **[here](/uploads/competency.zip)** and Upload this zip file to your Moodle installation via
    ##### **Site administration > Plugins > Install plugins**
- Update firstname and lastname mapping for oidc plugin.
    ##### **Administration -> Plugins -> Authentication -> OpenID Connect -> Field mappings**

### Integration Setup Moodle Side

- Visit **\_MOODLE_URL/admin/settings.php?section=externalservices** where you will see **_Galaxy360 External Web Service_** external service.
    - Click Authorized Users and add your admin user as authorized user.
- Visit **_MOODLE_URL/admin/webservice/tokens.php_** page and click **\*Create Token** button to add new token.
    - User should be the same user that was authorized to use the external service.
    - In service dropdown choose **_Galaxy360 External Web Service_**
    - Uncheck Valid until enabled checkbox.
- Copy the provided token
- Visit **Authentication > OpenID Connect > IdP and authentication** or visit url **MOODLE_URL/auth/oidc/manageapplication.php**
    - **_Application ID:_** {{ $application_id }}
    - **_Client authentication method:_** Other
    - **_Client Secret:_** {{ $client_secret }}
    - **_Authorization Endpoint:_** {{ $authorization_endpoint }}
    - **_Token Endpoint:_** {{ $token_endpoint }}
    - **_Scope:_** {{ $scopes }}

### Integration Setup Galaxy Side

- Visit **_GALAXY_URL/ntegrations/moodle/setup_**
    - Paset the copied token to Moodle API Token field
    - Provide your moodle installation url
- Save and click Moodle Connect button.

### After Connection Steps

- First sync grades from right box **_Moodle Grades Mapping_**
- Choose the scale for vet course
    - Choose the scale value for galaxy side marking.
