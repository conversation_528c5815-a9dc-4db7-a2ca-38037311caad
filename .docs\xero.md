# Xero Integration Guide

This guide will help you set up the integration between Galaxy360 and Xero for seamless accounting and financial management.

## Initial Setup

### **Create Xero App**

1. Log in to [Xero Developer portal](https://developer.xero.com/myapps)

2. Click "New App" to create a new application
   ![New App](new-app.png)

3. You'll see the app creation form
   ![New App Form](create-new-app-form.png)

4. Fill in required details:

    - **App Name**: Choose a descriptive name (e.g., "Galaxy360 Integration")
    - **OAuth 2.0 redirect URI**: Copy and paste this URL: {{ $redirectUrl }}
    - **Company or personal URL**: Your organization's website

    ![New App Form Fillup](create-new-app-form-fillup.png)

5. Save the app and securely store the following credentials:

    - **Client ID** - You'll need this for Galaxy360 configuration
    - **Client Secret** - You'll need this for Galaxy360 configuration

    ![Configuration](configuration.png)

### **Configure Galaxy360**

1. Navigate to **Settings > Integrations > Xero Setup** in your Galaxy360 dashboard
   ![Xero Setup Page](galaxy-xero-setup.png)

2. Enter the **Client ID** and **Client Secret** from your Xero app

3. Click "Save" to store your credentials

4. Click "Connect to Xero" to initiate the connection

5. You'll be redirected to Xero to authorize the connection
   ![Xero Authentication](xero-authentication.png)

6. Select the appropriate Xero organization to connect with

7. After successful authorization, you'll see the connection status
   ![Xero Connected](xero-connected.png)

### **Enable Webhooks**

Webhooks allow Xero to notify Galaxy360 in real-time when changes occur in your Xero account.

1. In your Xero app dashboard, navigate to the "Webhooks" section
   ![Xero Webhook](webhook-setup.png)

2. You'll see the webhook configuration form
   ![Xero Webhook Form](webhook-form.png)

3. Add your webhook endpoint URL:

    ```
    {{ $webhookUrl }}
    ```

    ![Xero Webhook Setup](webhook-setup-step-1.png)

4. Set up webhook security:

    - Xero will send a handshake request with an **x-xero-signature** header
    - This ensures secure communication between Xero and Galaxy360
      ![Send Intent to Receive](webhook-setup-step-2.png)

5. Webhook requirements:

    - Your server must respond within 5 seconds with status 200
    - Galaxy360 handles this automatically
      ![Intent Receive Progress](webhook-setup-step-3.png)

6. Configure webhook events to monitor:
    - **Invoices**: Get notified when invoices are created or updated
    - **Payments**: Track payment status changes
    - **Contacts**: Keep contact information in sync
      ![Xero Webhook Configure](webhook-setup-step-4.png)

### **Testing the Integration**

After setting up the integration, you can test it by:

1. Creating a student in Galaxy360
2. Syncing the student to Xero
3. Verifying the student appears as a contact in your Xero account

### **Troubleshooting**

If you encounter issues with the Xero integration:

1. Verify your Client ID and Client Secret are entered correctly
2. Check that your webhook URL is accessible from the internet
3. Ensure your Xero subscription has API access
4. Contact support if you continue to experience problems
