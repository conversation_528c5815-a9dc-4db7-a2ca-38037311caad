# Zoho Integration Setup Guide

This guide will help you set up the integration between Galaxy360 and Zoho CRM for seamless customer relationship management and student data synchronization.

## Prerequisites

Before starting the integration setup, ensure you have:

1. **Active Zoho CRM Account**: Sign up at [Zoho CRM](https://www.zoho.com/crm) if you don't have one already
2. **Admin Access**: You need administrator privileges in both Galaxy360 and Zoho CRM
3. **Galaxy360 Setup**: Ensure your Galaxy360 instance is properly configured and running

## Initial Setup

### **Create Zoho Client Application**

1. **Access Zoho API Console**

    Visit the [Zoho API Console](https://api-console.zoho.com) and log in with your Zoho account credentials.

2. **Create New Client**

    Click on "Add Client" to create a new client application for Galaxy360 integration.

3. **Select Client Type**

    Choose "Server-based Applications" as the client type for secure server-to-server communication.

4. **Configure Client Details**

    Fill in the required information:

    - **Client Name**: Enter a descriptive name (e.g., "Galaxy360 Integration")
    - **Homepage URL**: Enter your Galaxy360 domain URL
    - **Authorized Redirect URIs**: Copy and paste this URL: {{ $redirectUrl }}

5. **Configure Scopes**

    Select the following scopes for proper integration functionality:

    ```
    ZohoCRM.settings.ALL ZohoCRM.modules.ALL ZohoCRM.modules.deals.ALL
    ZohoCRM.modules.leads.ALL ZohoCRM.modules.custom.ALL ZohoCRM.users.ALL
    ZohoCRM.org.READ WorkDrive.files.CREATE WorkDrive.users.READ
    WorkDrive.teamfolders.READ
    ```

    **Scope Descriptions:**

    - `ZohoCRM.settings.ALL` - Access CRM settings and configurations
    - `ZohoCRM.modules.ALL` - Access all CRM modules (Leads, Contacts, Accounts, etc.)
    - `ZohoCRM.modules.deals.ALL` - Manage deals and opportunities
    - `ZohoCRM.modules.leads.ALL` - Manage leads and prospects
    - `ZohoCRM.modules.custom.ALL` - Access custom modules and fields
    - `ZohoCRM.users.ALL` - Manage users and permissions
    - `ZohoCRM.org.READ` - Read organization details and settings
    - `WorkDrive.files.CREATE` - Create and upload files in WorkDrive
    - `WorkDrive.users.READ` - Read WorkDrive user information
    - `WorkDrive.teamfolders.READ` - Access team folders for document organization

6. **Save Client Configuration**

    After creating the client, note down the **Client ID** and **Client Secret** - you'll need these for Galaxy360 configuration.

### **Galaxy360 Configuration**

1.  **Navigate to Zoho Setup**

    In Galaxy360, go to **Settings** → **Integrations** → **Zoho Setup** or visit [Zoho Setup](/integrations/zoho/setup) directly.

2.  **Enter API Credentials**

        Fill in the configuration form with the details from your Zoho client:

        - **Client ID**: Enter the Client ID from your Zoho application
        - **Client Secret**: Enter the Client Secret from your Zoho application

    <<<<<<< HEAD - **Zoho Region**: Select your Zoho data center region based on your account location: - **Australia (au)** - For Australian Zoho accounts - **USA (us)** - For US-based Zoho accounts - **Europe (eu)** - For European Zoho accounts - **India (in)** - For Indian Zoho accounts - **China (cn)** - For Chinese Zoho accounts - **Japan (jp)** - For Japanese Zoho accounts - **Canada (ca)** - For Canadian Zoho accounts
    ======= - **Zoho Region**: Select your Zoho data center region based on your account location: Australia, USA, Europe, India, China, Japan, Canada

    > > > > > > > c0f164e4a4e31512fbfa435e9df45068a16ae3a6

3.  **Important Notes**

    - Ensure the **Redirect URL** shown on the setup page matches exactly what you entered in Zoho API Console
    - The redirect URL format is: `https://yourdomain.com/integrations/zoho/connect`
    - Region selection is critical - choose the region where your Zoho account is hosted

4.  **Save Configuration**

    Click "Save Changes" to store your API credentials securely.

5.  **Establish Connection**

    Click the "Zoho Connect" button to initiate the OAuth flow and authorize Galaxy360 to access your Zoho CRM.

    You'll be redirected to Zoho's authorization page where you need to:

    - Log in to your Zoho account (if not already logged in)
    - Review the permissions requested by Galaxy360
    - Click "Accept" to authorize the integration

## Post-Connection Setup

### **Module Mapping Configuration**

After successful connection, configure how Galaxy360 data maps to Zoho CRM modules:

1. **Student Data Mapping**

    - **Leads Module**: Map future students to Zoho Leads
    - **Contacts Module**: Map enrolled students to Zoho Contacts
    - **Accounts Module**: Map educational agents to Zoho Accounts

2. **Custom Field Mapping**

    Configure custom fields to ensure proper data synchronization:

    - Student ID mapping
    - Course information
    - Enrollment status
    - Document references

### **WorkDrive Integration**

If you're using Zoho WorkDrive for document management:

1. **Setup Team Folders**

    Configure team folders for student document storage and organization.

2. **Access Permissions**

    Ensure proper access permissions are set for document sharing and collaboration.

## Data Synchronization

### **Student Sync Process**

The integration supports bidirectional synchronization:

1. **Lead Creation**: New student inquiries are automatically created as Leads in Zoho CRM
2. **Lead Conversion**: When students enroll, Leads are converted to Contacts
3. **Document Sync**: Student documents are uploaded to designated WorkDrive folders
4. **Status Updates**: Enrollment status changes are reflected in both systems

### **Agent Management**

Educational agents can be synchronized between systems:

1. **Agent Profiles**: Agent information is synced to Zoho Accounts
2. **Contact Details**: Email, phone, and website information is maintained
3. **Commission Tracking**: Agent commissions and performance data

## Troubleshooting

### **Common Issues**

1. **Connection Failures**

    - Verify Client ID and Client Secret are correct
    - Ensure redirect URL matches exactly
    - Check that all required scopes are enabled

2. **Sync Errors**

    - Verify module mappings are configured correctly
    - Check for required field conflicts
    - Ensure proper permissions in Zoho CRM

3. **Authentication Issues**
    - Token may have expired - reconnect if needed
    - Verify Zoho region selection is correct
    - Check API rate limits haven't been exceeded

## Security Considerations

- **API Keys**: Keep your Client ID and Client Secret secure
- **Access Control**: Regularly review user permissions in both systems
- **Data Privacy**: Ensure compliance with relevant data protection regulations
- **Regular Audits**: Monitor integration logs for any unusual activity

### Modules and Field Mapping

- You will now see 4 modules. You need to map each of these modules with your respective zoho CRUD modules.
- After you select your zoho module, for example for Future Student you selected Leads from the dropdown, You will now have to map galaxy fields with zoho fields. Click the map button which you can see at the end of the module map row.
