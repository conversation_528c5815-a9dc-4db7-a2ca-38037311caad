# Ignore dependencies and build artifacts
/vendor
/node_modules
/public/build
/public/js
/public/css
/storage
/bootstrap/cache

# Ignore Vite and <PERSON>vel Mix outputs
/public/hot
/public/mix-manifest.json
/public/*.map
/public/landing
/public/v2
/public/css
/public/plugins

# Ignore IDE/editor files
*.log
*.lock
*.tmp
*.swp
.DS_Store
.idea/
.vscode/
.env
.env.*

# Ignore coverage and test artifacts
/coverage
/tests/_output

# Ignore version control metadata
.git
