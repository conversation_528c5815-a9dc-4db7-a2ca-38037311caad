<?php

namespace App\Actions\Fortify;

use App\Models\User;
use App\Users;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Validator;
use Laravel\Fortify\Contracts\ResetsUserPasswords;

class ResetUserPassword implements ResetsUserPasswords
{
    use PasswordValidationRules;

    /**
     * Validate and reset the user's forgotten password.
     *
     * @param  array<string, string>  $input
     */
    public function reset(Users $user, array $input): void
    {
        Validator::make($input, [
            'password' => $this->passwordRules(),
        ])->validate();

        $user->forceFill([
            'password' => Hash::make($input['password']),
        ])->save();

        // GNG-4916 (Auto-verify email when password is set via email link)
        if (! $user->hasVerifiedEmail()) {
            $user->markEmailAsVerified();
        }

        dispatch(new \SSO\Jobs\ResetIdentityProviderPassword($user->id, $input['password'], false));
    }
}
