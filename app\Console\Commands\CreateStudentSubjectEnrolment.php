<?php

/**
 * Student Subject Enrolment Creation Command
 *
 * This command creates a random student subject enrolment record in the rto_student_subject_enrolment table
 * with proper foreign key relationships to existing records in the database.
 *
 * Usage:
 * php artisan app:create-student-subject-enrolment {tenant_id}
 *
 * Example:
 * php artisan app:create-student-subject-enrolment axis
 *
 * The command will:
 * 1. Select random existing records from related tables (students, courses, subjects, etc.)
 * 2. Generate realistic random data for other fields
 * 3. Create a new enrolment record with proper relationships
 * 4. Display the created record details
 */

namespace App\Console\Commands;

use App\Model\v2\CampusVenue;
use App\Model\v2\Courses;
use App\Model\v2\Semester;
use App\Model\v2\Student;
use App\Model\v2\StudentSubjectEnrolment;
use App\Model\v2\Subject;
use App\Model\v2\Tenant;
use App\Model\v2\UnitModule;
use Carbon\Carbon;
use Illuminate\Console\Command;

class CreateStudentSubjectEnrolment extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'app:create-student-subject-enrolment {tenant?}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Create a random student subject enrolment record with proper foreign key relationships. Usage: php artisan app:create-student-subject-enrolment {tenant_id}';

    /**
     * Execute the console command.
     */
    public function handle()
    {
        try {
            // Check if we're running in tenant context or need to specify one
            if (! tenant('id') && ! $this->argument('tenant')) {
                $this->error('Please specify a tenant ID or run this command within a tenant context.');
                $this->info('Usage: php artisan app:create-student-subject-enrolment {tenant_id}');

                return 1;
            }

            // If tenant argument is provided, run within that tenant context
            if ($this->argument('tenant')) {
                $tenant = Tenant::findOrFail($this->argument('tenant'));
                $tenant->run(function () {
                    $this->createEnrolment();
                });

                return 0;
            }

            // Otherwise, run in current tenant context
            return $this->createEnrolment();

        } catch (\Exception $e) {
            $this->error('Error creating student subject enrolment: '.$e->getMessage());

            return 1;
        }
    }

    /**
     * Create the student subject enrolment record
     */
    private function createEnrolment()
    {
        // Get random foreign key values from existing records
        $student = Student::inRandomOrder()->first();
        $course = Courses::inRandomOrder()->first();
        $semester = Semester::inRandomOrder()->first();
        $subject = Subject::inRandomOrder()->first();
        $unit = UnitModule::inRandomOrder()->first();
        $venue = CampusVenue::inRandomOrder()->first();

        // Check if we have all required foreign key records
        if (! $student) {
            $this->error('No students found in the database. Please create a student first.');

            return 1;
        }

        if (! $course) {
            $this->error('No courses found in the database. Please create a course first.');

            return 1;
        }

        if (! $semester) {
            $this->error('No semesters found in the database. Please create a semester first.');

            return 1;
        }

        if (! $subject) {
            $this->error('No subjects found in the database. Please create a subject first.');

            return 1;
        }

        // Create the student subject enrolment record
        $enrolment = StudentSubjectEnrolment::create([
            'college_id' => $student->college_id ?? 1,
            'student_id' => $student->id,
            'course_id' => $course->id,
            'semester_id' => $semester->id,
            'term' => rand(1, 4),
            'enroll_type' => collect(['Full-time', 'Part-time', 'Distance'])->random(),
            'course_stage' => rand(1, 4),
            'subject_id' => $subject->id,
            'unit_id' => $unit ? $unit->id : null,
            'activity_start_date' => Carbon::now()->subDays(rand(1, 30)),
            'activity_finish_date' => Carbon::now()->addDays(rand(30, 180)),
            'study_reason' => collect(['Career advancement', 'Personal interest', 'Job requirement', 'Skill development'])->random(),
            'vanue_location' => $venue ? $venue->id : 1,
            'funding_source_state' => collect(['VIC', 'NSW', 'QLD', 'SA', 'WA', 'TAS', 'NT', 'ACT'])->random(),
            'funding_source_nat' => collect(['Commonwealth', 'State', 'Private', 'International'])->random(),
            'final_outcome' => null,
            'outcome_identifier' => collect(['20', '30', '40', '51', '52', '53', '60', '65', '70', '81', '82', '90'])->random(),
            'comment' => 'Random test enrolment created by console command',
            'batch' => 'BATCH-'.date('Y').'-'.str_pad(rand(1, 999), 3, '0', STR_PAD_LEFT),
            'funding_identifier' => rand(1, 10),
            'course_commencing_id' => null,
            'training_contract_id' => null,
            'apprenticeship_client_id' => null,
            'vet_in_school' => collect(['Y', 'N'])->random(),
            'fee_exemption_id' => null,
            'booking_id' => 'BK-'.date('Ymd').'-'.rand(1000, 9999),
            'course_site_id' => 'CS-'.rand(100, 999),
            'delivery_mode' => collect(['Face-to-face', 'Online', 'Blended', 'Distance'])->random(),
            'predominant_delivery_mode' => collect(['Face-to-face', 'Online', 'Blended', 'Distance'])->random(),
            'purchase_contract_id' => 'PC-'.date('Y').'-'.rand(1000, 9999),
            'con_schedule_id' => 'CS-'.date('Y').'-'.rand(1000, 9999),
            'full_time_learning_option' => collect(['Y', 'N'])->random(),
            'census_date' => Carbon::now()->addDays(rand(1, 60)),
            'mode_of_attendance' => collect(['Full-time', 'Part-time'])->random(),
            'subject_tution_fee' => rand(500, 5000),
            'fee_paid_upfront' => rand(0, 2000),
            'eftsl_study_load' => number_format(rand(10, 100) / 100, 2),
            'subject_attempt' => 1,
            'marks' => null,
            'grade' => null,
            'created_by' => 1,
            'updated_by' => 1,
            'created_at' => Carbon::now(),
            'updated_at' => Carbon::now(),
        ]);

        $this->info('Student Subject Enrolment created successfully!');
        $this->info('Enrolment ID: '.$enrolment->id);
        $this->info('Student: '.$student->first_name.' '.$student->last_name.' (ID: '.$student->id.')');
        $this->info('Course: '.$course->course_name.' (ID: '.$course->id.')');
        $this->info('Subject: '.$subject->subject_name.' (ID: '.$subject->id.')');
        $this->info('Semester: '.$semester->semester_name.' (ID: '.$semester->id.')');
        if ($unit) {
            $this->info('Unit: '.$unit->unit_name.' (ID: '.$unit->id.')');
        }
        if ($venue) {
            $this->info('Venue: '.$venue->venue_name.' (ID: '.$venue->id.')');
        }

        return 0;
    }
}
