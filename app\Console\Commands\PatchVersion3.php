<?php

namespace App\Console\Commands;

use App\Console\Commands\Traits\PatchTrackerTrait;
use App\Model\OfferUpfrontFeeSchedule;
use App\Model\Students;
use App\Model\v2\AssessmentTask;
use App\Model\v2\CertificateAttribute;
use App\Model\v2\Colleges;
use App\Model\v2\Country;
use App\Model\v2\Courses;
use App\Model\v2\CreditProvidersCode;
use App\Model\v2\EmailParameter;
use App\Model\v2\InvoiceNumber;
use App\Model\v2\Language;
use App\Model\v2\LetterParameter;
use App\Model\v2\OfferDocumentChecklist;
use App\Model\v2\Report;
use App\Model\v2\ResceiptNumber;
use App\Model\v2\SetupSection;
use App\Model\v2\Student;
use App\Model\v2\StudentAgentCommission;
use App\Model\v2\StudentCourses;
use App\Model\v2\StudentDetails;
use App\Model\v2\StudentIdFormate;
use App\Model\v2\StudentInitialPayment;
use App\Model\v2\StudentInitialPaymentDetails;
use App\Model\v2\StudentMiscellaneousPayment;
use App\Model\v2\StudentSubjectEnrolment;
use App\Model\v2\SubjectMaterial;
use App\Model\v2\Tenant;
use App\Model\v2\Timetable;
use App\Model\v2\TransactionNumber;
use App\Model\v2\UserRoleType;
use App\Model\v2\Users;
use App\Traits\CommonTrait;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Illuminate\Support\Facades\Schema;
use Illuminate\Support\Str;

class PatchVersion3 extends Command
{
    use CommonTrait;
    use PatchTrackerTrait;

    const PATCH_VERSION_01 = '01'; // Add email Parameter detail field

    const PATCH_VERSION_02 = '02'; // Add default recored in trasaction

    const PATCH_VERSION_03 = '03'; // Add new user in churchill

    const PATCH_VERSION_04 = '04'; // GNG-3449 : Update student id for all college which is repeated

    const PATCH_VERSION_05 = '05'; // GNG-3450 : Update student id as per JIRA

    const PATCH_VERSION_06 = '06'; // GNG-3453 : Add uuid to application

    const PATCH_VERSION_07 = '07'; // Update Agent for churchill's student

    const PATCH_VERSION_08 = '08'; // Update invoice number for existing agent commission data

    const PATCH_VERSION_09 = '09'; // Update Student Id For Hillshire

    const PATCH_VERSION_SYNC_TRACK = 'sync-track'; // Sync Patch

    const PATCH_VERSION_10 = '10'; // Update college id for newly create tenants

    const PATCH_VERSION_11 = '11'; // Update Agent for churchill's student

    const PATCH_VERSION_12 = '12'; // Add New English Test

    const PATCH_VERSION_13 = '13'; // Update language table remove unnessary data

    const PATCH_VERSION_14 = '14'; // Remove dublicate English test recored from axis

    const PATCH_VERSION_15 = '15'; // Update StudentID for chrurchill

    const PATCH_VERSION_16 = '16'; // Update One StudentID for chrurchill

    const PATCH_VERSION_17 = '17'; // Update Activity End Date for existing student

    const PATCH_VERSION_18 = '18'; // patch to update course status to 2 for the incomplete courses

    const PATCH_VERSION_19 = '19'; // update student_course_id to enrollment table

    const PATCH_VERSION_20 = '20'; // Update Agent for churchill's student

    const PATCH_VERSION_21 = '21'; // Update countries table with phone code

    const PATCH_VERSION_22 = '22'; // Update Agent for churchill's student

    const PATCH_VERSION_23 = '23'; // Update is_process value 1 when Agent Commission Paid

    const PATCH_VERSION_24 = '24'; // Update is_process value 1 when Agent Commission Paid

    const PATCH_VERSION_25 = '25'; // Update country table remove unnessary data

    const PATCH_VERSION_26 = '26'; // Update country table remove unnessary data

    const PATCH_VERSION_27 = '27'; // Fix iie student data issue (GNG-4683)

    const PATCH_VERSION_27_02 = '27.02'; // Feed certificate attribute table

    const PATCH_VERSION_28 = '28'; // Fix for all student data with wrong course id in initial payment & payment details table (GNG-4683)

    const PATCH_VERSION_29 = '29'; // Fix issue for column name is missing for table of "rto_student_initial_payment_details" (GNG-4681)

    const PATCH_VERSION_30 = '30'; // GNG-4785 (Delete payment records for students who were not converted from Offer Management to enrolled students.)

    const PATCH_VERSION_31 = '31'; // GNG-4792 (Same logic as patch3_08, but apply only for a single ID specific to the tenant 'AXIS' and Student ID: AXIS1810317)

    const PATCH_VERSION_32 = '32'; // GNG-4777 (Add one more report of Unit Assessment Summary for Students)

    const PATCH_VERSION_33 = '33'; // Add DeanName, DeanSignature & CollegeSignature tags to email and letter parameters

    const TENANT_AXIS_ID = 'axis';

    const TENANT_CHURCHILL_ID = 'churchill';

    const TENANT_HILLSHIRE_ID = 'hillshire';

    const TENANT_IIE_ID = 'iie';

    const TENANT_LMSC_ID = 'lmsc';

    const TENANT_KENTFORD_ID = '_kentford_i7nBUWVik0cuwalU';

    const TENANT_QUEENSFORD_ID = '_queensfordpyp_gvGIz5mdFVA3F4pl';

    const TENANT_OPAL_ID = '_opal_kEzlmkxfMmGtcg9i';

    public $arrEmailParameter = '';

    public function baseVersion()
    {
        return 3;
    }

    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'apply:patch3.0 {ver} {--rollback}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Apply Patch';

    /**
     * Create a new command instance.
     *
     * @return void
     */
    public function __construct()
    {
        parent::__construct();
    }

    /**
     * Execute the console command.
     *
     * @return int
     */
    public function handle()
    {
        $version = $this->argument('ver');
        if (! $this->canPatch($version, $this->option('rollback'))) {
            return;
        }

        if ($this->option('rollback')) {
            $this->patchRolledback($version);
        } else {
            $this->patchStarted($version);
        }

        switch ($version) {

            case self::PATCH_VERSION_01:
                $this->patch3_01();
                break;
            case self::PATCH_VERSION_02:
                $this->patch3_02();
                break;
            case self::PATCH_VERSION_03:
                $this->patch3_03();
                break;
            case self::PATCH_VERSION_04:
                $this->patch3_04();
                break;
            case self::PATCH_VERSION_05:
                $this->patch3_05();
                break;
            case self::PATCH_VERSION_06:
                $this->patch3_06();
                break;
            case self::PATCH_VERSION_07:
                $this->patch3_07();
                break;
            case self::PATCH_VERSION_08:
                $this->patch3_08();
                break;
            case self::PATCH_VERSION_09:
                $this->patch3_09();
                break;
            case self::PATCH_VERSION_10:
                $this->patch3_10();
                break;
            case self::PATCH_VERSION_11:
                $this->patch3_11();
                break;
            case self::PATCH_VERSION_12:
                $this->patch3_12();
                break;
            case self::PATCH_VERSION_13:
                $this->patch3_13();
                break;
            case self::PATCH_VERSION_14:
                $this->patch3_14();
                break;
            case self::PATCH_VERSION_15:
                $this->patch3_15();
                break;
            case self::PATCH_VERSION_16:
                $this->patch3_16();
                break;
            case self::PATCH_VERSION_17:
                $this->patch3_17();
                break;
            case self::PATCH_VERSION_18:
                $this->patch3_18();
                break;
            case self::PATCH_VERSION_19:
                $this->patch3_19();
                break;
            case self::PATCH_VERSION_20:
                $this->patch3_20();
                break;
            case self::PATCH_VERSION_22:
                $this->patch3_22();
                break;
            case self::PATCH_VERSION_23:
                $this->patch3_23();
                break;
            case self::PATCH_VERSION_24:
                $this->patch3_24();
                break;
            case self::PATCH_VERSION_25:
                $this->patch3_25();
                break;
            case self::PATCH_VERSION_26:
                $this->patch3_26();
                break;
            case self::PATCH_VERSION_27:
                $this->patch3_27();
                break;
            case self::PATCH_VERSION_27_02:
                $this->patch3_27_02();
                break;
            case self::PATCH_VERSION_28:
                $this->patch3_28();
                break;
            case self::PATCH_VERSION_29:
                $this->patch3_29();
                break;
            case self::PATCH_VERSION_30:
                $this->patch3_30();
                break;
            case self::PATCH_VERSION_31:
                $this->patch3_31();
                break;
            case self::PATCH_VERSION_32:
                $this->patch3_32();
                break;
            case self::PATCH_VERSION_33:
                $this->patch3_33();
                break;
            case self::PATCH_VERSION_SYNC_TRACK:
                $this->patch3_syncTrack();
                break;

            default:
                return;
        }
        $this->patchEnded($version, $this->option('rollback'));
    }

    public function patch3_01()
    {
        // This patch is for email and letter parameter with detail
        // Define common CSV file processing function
        $processCsvFile = function ($csvFilePath, $model, $modelFields) {
            $csvFile = fopen(base_path($csvFilePath), 'r');
            $firstline = true;
            while (($data = fgetcsv($csvFile, 2000, ',')) !== false) {
                if (! $firstline) {
                    $attributes = array_combine($modelFields, $data);
                    $model::create($attributes);
                }
                $firstline = false;
            }
            fclose($csvFile);
        };
        Tenant::all()->each(function ($tenant) use ($processCsvFile) {
            $tenant->run(function ($tenant) use ($processCsvFile) {
                $this->info('Command run for...'.$tenant->id);

                EmailParameter::truncate();
                $processCsvFile('database/seeds/csvfiles/rto_default_email_parameter.csv', EmailParameter::class, [
                    'id',
                    'parameter_name',
                    'parameter_value',
                    'parameter_detail',
                    'created_at',
                    'updated_at',
                    'created_by',
                    'updated_by',
                ]);
                $this->info('Email Parameter added successfully for...'.$tenant->id);
                LetterParameter::truncate();
                $processCsvFile('database/seeds/csvfiles/rto_default_letter_parameter.csv', LetterParameter::class, [
                    'id',
                    'parameter_name',
                    'parameter_value',
                    'parameter_detail',
                    'created_at',
                    'updated_at',
                    'created_by',
                    'updated_by',
                ]);
                $this->info('Letter Parameter added successfully for...'.$tenant->id);
            });
        });
    }

    public function patch3_02()
    {
        Tenant::all()->each(function ($tenant) {

            $tenant->run(function ($tenant) {

                if ($tenant->id == self::TENANT_LMSC_ID) {

                    $this->info('Command run for...'.$tenant->id);

                    $findResNo = TransactionNumber::get(['transaction_number'])->first();
                    $collegeNo = Colleges::get(['id'])->first();

                    if (empty($findResNo)) {
                        $objSaveResceiptNumber = new TransactionNumber;
                        $objSaveResceiptNumber->id = '1';
                        $objSaveResceiptNumber->college_id = $collegeNo->id;
                        $objSaveResceiptNumber->transaction_number = '001';
                        $objSaveResceiptNumber->created_at = date('Y-m-d H:i:s');
                        $objSaveResceiptNumber->updated_at = date('Y-m-d H:i:s');
                        $objSaveResceiptNumber->created_by = '1';
                        $objSaveResceiptNumber->updated_by = '1';
                        $objSaveResceiptNumber->save();
                    }

                    OfferDocumentChecklist::query()->update(['college_id' => $collegeNo->id]);
                }
            });
        });
    }

    public function patch3_03()
    {
        Tenant::all()->each(function ($tenant) {

            $tenant->run(function ($tenant) {

                $this->info('Command run for...'.$tenant->id);
                // if ($tenant->id == self::TENANT_HILLSHIRE_ID) {

                //     $this->info('Command run for...' . $tenant->id);

                //     $collegeNo = Colleges::get(['id'])->first();
                //     $findUser = Users::query()->where('email','<EMAIL>')->get()->toArray();

                //     if(empty($findUser)){
                //         $objUser = new Users();
                //         $objUser->college_id = $collegeNo->id;
                //         $objUser->name = 'Anuj';
                //         $objUser->username = 'anuj';
                //         $objUser->password = Hash::make('anuj@123');
                //         $objUser->email = '<EMAIL>';
                //         $objUser->role_id = '9';
                //         $objUser->email_verified_at = date('Y-m-d H:i:s',strtotime('-1 days'));
                //         $objUser->created_at = date('Y-m-d H:i:s');
                //         $objUser->updated_at = date('Y-m-d H:i:s');
                //         $objUser->save();

                //         $objUserRoles = new UserRoleType();
                //         $objUserRoles->staff_id = 0;
                //         $objUserRoles->user_id = $objUser->id;
                //         $objUserRoles->role_priority = 1;
                //         $objUserRoles->role_type = 9;
                //         $objUserRoles->save();
                //     }
                // }
            });
        });
    }

    public function patch3_04()
    {
        Tenant::all()->each(function ($tenant) {

            $tenant->run(function ($tenant) {

                // $this->info('Command run for...' . $tenant->id);
                if ($tenant->id == self::TENANT_CHURCHILL_ID) {

                    $this->info('Command run for...'.$tenant->id);

                    $collegeNo = Colleges::get(['id'])->first();
                    $findStudents = Student::query()->where('generated_stud_id', 'CIHE22990')->get()->toArray();

                    if (! empty($findStudents)) {
                        foreach ($findStudents as $findStudent) {
                            $generatedStudId = $this->generateStudentID($collegeNo->id);
                            $this->info('New Student id '.$generatedStudId.' is set for :'.$findStudent['first_name']);
                            $objStudent = Student::find($findStudent['id']);
                            $objStudent->generated_stud_id = $generatedStudId;
                            $objStudent->save();
                            $this->updateGeneratedNumber($collegeNo->id);
                        }
                    }
                }
                if ($tenant->id == self::TENANT_IIE_ID) {

                    $this->info('Command run for...'.$tenant->id);

                    $collegeNo = Colleges::get(['id'])->first();
                    $findStudents = Student::query()->where('generated_stud_id', 'IIE2313663')->get()->toArray();

                    if (! empty($findStudents)) {
                        foreach ($findStudents as $findStudent) {
                            $generatedStudId = $this->generateStudentID($collegeNo->id);
                            $this->info('New Student id '.$generatedStudId.' is set for :'.$findStudent['first_name']);
                            $objStudent = Student::find($findStudent['id']);
                            $objStudent->generated_stud_id = $generatedStudId;
                            $objStudent->save();
                            $this->updateGeneratedNumber($collegeNo->id);
                        }
                    }
                }
                if ($tenant->id == self::TENANT_HILLSHIRE_ID) {

                    $this->info('Command run for...'.$tenant->id);

                    $collegeNo = Colleges::get(['id'])->first();
                    $findStudents = Student::query()->whereIn('generated_stud_id', ['HIC231113', 'HIC231112'])->get()->toArray();

                    if (! empty($findStudents)) {
                        foreach ($findStudents as $findStudent) {
                            $generatedStudId = $this->generateStudentID($collegeNo->id);
                            $this->info('New Student id '.$generatedStudId.' is set for :'.$findStudent['first_name']);
                            $objStudent = Student::find($findStudent['id']);
                            $objStudent->generated_stud_id = $generatedStudId;
                            $objStudent->save();
                            $this->updateGeneratedNumber($collegeNo->id);
                        }
                    }
                }
            });
        });
    }

    public function patch3_05()
    {
        Tenant::all()->each(function ($tenant) {

            $tenant->run(function ($tenant) {

                if ($tenant->id == self::TENANT_HILLSHIRE_ID) {

                    $this->info('Command run for...'.$tenant->id);

                    $updateStudentId = [
                        // 'HIC231117' => 'HIC231115',
                        'HIC231114' => 'HIC231112',
                        'HIC231116' => 'HIC231114',
                        'HIC231118' => 'HIC231116',
                        'HIC231113' => 'HIC231118',
                        'HIC231119' => 'HIC231117',
                        'HIC231120' => 'HIC231113',
                        'HIC231121' => 'HIC231119',
                    ];

                    foreach ($updateStudentId as $key => $value) {
                        Student::where('generated_stud_id', $key)->update([
                            'generated_stud_id' => $value,
                        ]);
                    }
                }
                if ($tenant->id == self::TENANT_AXIS_ID) {

                    $this->info('Command run for...'.$tenant->id);

                    AssessmentTask::where('id', 95)->update([
                        'task_name' => 'Assessment Task 2 - Practical project',
                    ]);
                }
            });
        });
    }

    private function generateStudentID($collegeID)
    {

        // $collegeID = $request->college_id;

        $studentIdFormate = StudentIdFormate::where('college_id', $collegeID)->get();

        $p1 = $studentIdFormate[0]->position1;
        $p2 = $studentIdFormate[0]->position2;
        $p3 = $studentIdFormate[0]->position3;

        $alphabeat = $studentIdFormate[0]->alphabeat;
        $yeardigit = $studentIdFormate[0]->yeardigit;
        $auto_no = $studentIdFormate[0]->auto_increment;

        $str = '';
        if ($p1 == 'countrycode') {
            $str .= '061';
        } elseif ($p2 == 'countrycode') {
            $str .= '061';
        } elseif ($p3 == 'countrycode') {
            $str .= '061';
        }

        if ($p1 == 'alphabeat' && $alphabeat != '') {
            $str .= $alphabeat;
        } elseif ($p2 == 'alphabeat' && $alphabeat != '') {
            $str .= $alphabeat;
        } elseif ($p3 == 'alphabeat' && $alphabeat != '') {
            $str .= $alphabeat;
        }

        if ($p1 == 'yeardigit' && $yeardigit != '') {
            $str .= $yeardigit;
        } elseif ($p2 == 'yeardigit' && $yeardigit != '') {
            $str .= $yeardigit;
        } elseif ($p3 == 'yeardigit' && $yeardigit != '') {
            $str .= $yeardigit;
        }

        // $str .= $auto_no;
        $generateID = $str.$auto_no;

        return $generateID;
    }

    public function updateGeneratedNumber($collegeID)
    {
        // $collegeId = $request->college_id;
        $studIDFormate = StudentIdFormate::where('college_id', $collegeID)->get();
        $studIDFormate[0]->auto_increment = ($studIDFormate[0]->auto_increment + 1);
        $studIDFormate[0]->save();

        return true;
    }

    public function patch3_06()
    {
        Tenant::all()->each(function ($tenant) {

            $tenant->run(function ($tenant) {

                $this->info('Command run for...'.$tenant->id);
                $getAllStudent = Students::query()->get(['id', 'first_name']);

                foreach ($getAllStudent as $record) {
                    Students::where('id', $record->id)->update(['uuid' => (string) Str::uuid()]);
                    $this->info('UUID id generated for student: '.$record->id.' ->'.$record->first_name);
                }
            });
        });
    }

    public function patch3_07()
    {
        Tenant::all()->each(function ($tenant) {
            // Fox Axis only
            if ($tenant->id == self::TENANT_CHURCHILL_ID) {
                $tenant->run(function ($tenant) {
                    $this->info('Command run for '.$tenant->id.' only. ');

                    $getStudentDatas = [
                        ['generated_id' => 'CIHE22897', 'student_id' => 2960, 'agent_id' => 250, 'status' => 'done'],
                    ];

                    $this->updateOnlyAgentInPayment($getStudentDatas);
                });
            }
        });
    }

    public function patch3_test()
    {
        $this->info('PATCH TEST');
    }

    public function updateOnlyAgentInPayment($getStudentDatas)
    {
        foreach ($getStudentDatas as $key => $value) {

            $studentId = $value['student_id'];
            $generatedId = $value['generated_id'];
            $agentId = $value['agent_id'];
            $status = $value['status'];

            if ($status == 'startprocess' && $studentId != '' && $agentId != '') {
                $objStudentDetail = Students::where('generated_stud_id', $generatedId)->where('id', $studentId)->get(['id'])->toArray();

                if (! empty($objStudentDetail)) {
                    $objStudentCourse = StudentCourses::where('student_id', $studentId)->get(['id'])->toArray();
                    if (! empty($objStudentCourse)) {
                        StudentCourses::where('student_id', $studentId)->update([
                            'agent_id' => $agentId,
                        ]);
                        $this->info('Student course updated successfully for student:'.$studentId);

                        StudentInitialPayment::where('student_id', $studentId)->update([
                            'agent_id' => $agentId,
                        ]);
                        $this->info('StudentInitialPayment updated successfully for student:'.$studentId);

                        StudentInitialPaymentDetails::where('student_id', $studentId)->update([
                            'agent_id' => $agentId,
                        ]);
                        $this->info('StudentInitialPaymentDetails updated successfully for student:'.$studentId);

                        StudentAgentCommission::where('student_id', $studentId)->update([
                            'agent_id' => $agentId,
                        ]);
                        $this->info('StudentAgentCommission updated successfully for student:'.$studentId);
                    }
                }
            }
        }
    }

    public function patch3_08()
    {
        Tenant::all()->each(function ($tenant) {
            $tenant->run(function ($tenant) {
                $this->info('Command run for '.$tenant->id.' only. ');

                $resArr = StudentAgentCommission::from('rto_student_agent_commission as rsac')
                    ->join('rto_student_initial_payment_transaction as rsipt', 'rsipt.transection_no', '=', 'rsac.transaction_no')
                    ->join('rto_student_initial_payment_details as rsipd', 'rsipd.id', '=', 'rsipt.initial_payment_detail_id')
                    // ->where('rsac.invoice_no', null)
                    ->get([
                        'rsac.id',
                        'rsac.transaction_no',
                        'rsac.commission_payable',
                        'rsipd.invoice_number',
                    ])
                    ->toArray();

                $this->updateAgentInvoiceNumber($resArr, $tenant->id);
            });
        });
    }

    private function updateAgentInvoiceNumber($resArr, $tenantName)
    {
        $success = $fail = 0;
        $failIds = [];
        foreach ($resArr as $res) {
            try {
                StudentAgentCommission::where('id', $res['id'])
                    ->update([
                        'invoice_no' => $res['invoice_number'],
                        'transaction_no' => 0,
                    ]);
                $success++;
            } catch (\Exception $e) {
                $fail++;
                $failIds[] = $res['id'];
                // throw new ApplicationException($e->getMessage());
            }
        }

        echo strtoupper($tenantName)." : $success items successfully updated & $fail items failed.";
        if (count($failIds) > 0) {
            echo 'Failed Agent Commission records Id: '.implode(', ', $failIds).'<br><hr/>';
        }
    }

    public function patch3_syncTrack()
    {
        $insertAr = [];
        for ($i = 1; $i <= 37; $i++) {
            $insertAr[] = [
                'version' => '0.0.'.$i,
                'status' => 1,
                'updated_at' => now(),
            ];
        }

        //    $insertAr = [];

        foreach (['01', '02', '03'] as $i) {
            $insertAr[] = [
                'version' => '1.'.$i,
                'status' => 1,
                'updated_at' => now(),
            ];
        }

        foreach (['01', '02', '03', '04', '05', '06', '07'] as $i) {
            $insertAr[] = [
                'version' => '3.'.$i,
                'status' => 1,
                'updated_at' => now(),
            ];
        }

        //    dd($insertAr);

        DB::table('patches')->insert($insertAr);
    }

    public function patch3_09()
    {
        Tenant::all()->each(function ($tenant) {

            $tenant->run(function ($tenant) {

                if ($tenant->id == self::TENANT_HILLSHIRE_ID) {

                    $this->info('Command run for...'.$tenant->id);

                    $collegeNo = Colleges::get(['id'])->first();
                    // 193 - Ronika Katila
                    // 199 - Yadap Prasad Ghimire
                    // 197 - Rima K C
                    $findStudents = Student::query()->whereIn('id', ['193', '199', '197'])->get()->toArray();

                    if (! empty($findStudents)) {
                        foreach ($findStudents as $findStudent) {

                            if ($findStudent['id'] == '193') {
                                $generatedStudId = $this->generateStudentID($collegeNo->id);
                                $this->info('New Student id '.$generatedStudId.' is set for :'.$findStudent['first_name']);
                                $objStudent = Student::find($findStudent['id']);
                                $objStudent->generated_stud_id = $generatedStudId;
                                $objStudent->save();
                                $this->updateGeneratedNumber($collegeNo->id);
                            }
                            if ($findStudent['id'] == '199') {

                                $this->info('New Student id is set for :'.$findStudent['first_name']);
                                $objStudent = Student::find($findStudent['id']);
                                $objStudent->generated_stud_id = 'HIC231121';
                                $objStudent->save();
                            }
                            if ($findStudent['id'] == '197') {

                                $this->info('New Student id is set for :'.$findStudent['first_name']);
                                $objStudent = Student::find($findStudent['id']);
                                $objStudent->generated_stud_id = 'HIC231119';
                                $objStudent->save();
                            }
                        }
                    }
                }
            });
        });
    }

    public function patch3_10()
    {
        Tenant::all()->each(function ($tenant) {
            if (in_array($tenant->id, [self::TENANT_KENTFORD_ID, self::TENANT_QUEENSFORD_ID, self::TENANT_OPAL_ID])) {
                $tenant->run(function () {
                    $findResNo = ResceiptNumber::get(['id'])->first();
                    $findInvNo = InvoiceNumber::get(['id'])->first();
                    $findTranNo = TransactionNumber::get(['id'])->first();
                    $collegeNo = Colleges::get(['id'])->first();

                    ResceiptNumber::where('id', $findResNo->id)->update([
                        'college_id' => $collegeNo->id,
                    ]);
                    InvoiceNumber::where('id', $findInvNo->id)->update([
                        'college_id' => $collegeNo->id,
                    ]);
                    TransactionNumber::where('id', $findTranNo->id)->update([
                        'college_id' => $collegeNo->id,
                    ]);
                });
            }
        });
    }

    public function patch3_11()
    {
        Tenant::all()->each(function ($tenant) {
            // Fox Axis only
            if ($tenant->id == self::TENANT_CHURCHILL_ID) {
                $tenant->run(function ($tenant) {
                    $this->info('Command run for '.$tenant->id.' only. ');

                    $getStudentDatas = [
                        ['generated_id' => 'CIHE23027', 'student_id' => 3091, 'agent_id' => 21, 'status' => 'done'],
                        ['generated_id' => 'CIHE22778', 'student_id' => 2842, 'agent_id' => 251, 'status' => 'done'],
                    ];

                    $this->updateOnlyAgentInPayment($getStudentDatas);
                });
            }
        });
    }

    public function patch3_12()
    {
        Tenant::all()->each(function ($tenant) {

            $tenant->run(function ($tenant) {
                $this->info('Command run for '.$tenant->id.' only. ');

                $testList = ['CAE', 'OET', 'PTE'];
                $collegeNo = Colleges::get(['id'])->first();

                for ($i = 0; $i < count($testList); $i++) {
                    $objSetupSection = new SetupSection;
                    $objSetupSection->college_id = $collegeNo->id;
                    $objSetupSection->section = '08';
                    $objSetupSection->type = '11';
                    $objSetupSection->value = $testList[$i];
                    $objSetupSection->is_default = '0';
                    $objSetupSection->permition = '0';
                    $objSetupSection->created_at = date('Y-m-d H:i:s');
                    $objSetupSection->updated_at = date('Y-m-d H:i:s');
                    $objSetupSection->created_by = '1';
                    $objSetupSection->updated_by = '1';
                    $objSetupSection->save();
                }
            });
        });
    }

    public function patch3_13()
    {
        $processCsvFile = function ($csvFilePath, $model, $modelFields, $collegeId) {
            $csvFile = fopen(base_path($csvFilePath), 'r');
            $firstline = true;
            while (($data = fgetcsv($csvFile, 2000, ',')) !== false) {
                if (! $firstline) {
                    $attributes = array_combine($modelFields, $data);
                    $attributes['college_id'] = $collegeId;
                    if ($attributes['abs_value'] < 10) {
                        $attributes['abs_value'] = '000'.$attributes['abs_value'];
                    }
                    unset($attributes['id']);
                    if ($attributes['name'] != '') {
                        $model::create($attributes);
                    }
                }
                $firstline = false;
            }
            fclose($csvFile);
        };
        Tenant::all()->each(function ($tenant) use ($processCsvFile) {

            $tenant->run(function ($tenant) use ($processCsvFile) {
                $this->info('Command run for '.$tenant->id.' only. ');

                $collegeNo = Colleges::get(['id'])->first();
                // Step 1: Retrieve the IDs to keep
                $idsToKeep = DB::table('rto_student_details')
                    ->join('rto_language', 'rto_student_details.main_lang', '=', 'rto_language.id')
                    ->groupBy('rto_student_details.main_lang')
                    ->pluck('rto_language.id');

                // Step 2: Delete all records except those with IDs in $idsToKeep
                DB::table('rto_language')
                    ->whereNotIn('id', $idsToKeep)
                    ->delete();

                $processCsvFile('database/seeds/csvfiles/rto_language.csv', Language::class, [
                    'id',
                    'college_id',
                    'name',
                    'abs_value',
                    'isdefault',
                    'created_at',
                    'updated_at',
                    'created_by',
                    'updated_by',
                ], $collegeNo->id);
            });
        });
    }

    public function patch3_14()
    {
        Tenant::all()->each(function ($tenant) {

            $tenant->run(function ($tenant) {
                $this->info('Command run for '.$tenant->id.' only. ');
                if ($tenant->id == self::TENANT_AXIS_ID) {

                    $collegeNo = Colleges::get(['id'])->first();

                    $oldToNewTestID = [
                        '43' => '153',
                        '44' => '154',
                        '45' => '155',
                        '46' => '156',
                        '98' => '153',
                        '99' => '154',
                        '100' => '155',
                        '101' => '156',
                    ];

                    foreach ($oldToNewTestID as $key => $value) {
                        StudentDetails::where('EPL_test_name', $key)->update([
                            'EPL_test_name' => $value,
                        ]);
                    }

                    SetupSection::where('college_id', '!=', $collegeNo->id)->delete();
                }

                $englishTests = [
                    'IELTS' => 'International English Language Testing System (IELTS)',
                    'TOEIC' => 'Test of English for International Communication (TOEIC)',
                    'TOEFL' => 'Test of English as a Foreign Language (TOEFL)',
                    'CAE' => 'Certificate in Advanced English (CAE)',
                    'OET' => 'Occupational English Test (OET)',
                    'PTE' => 'Pearson Test of English (PTE)',
                ];

                foreach ($englishTests as $key => $value) {
                    SetupSection::where('value', $key)->update([
                        'value' => $value,
                    ]);
                }
            });
        });
    }

    public function patch3_15()
    {
        Tenant::all()->each(function ($tenant) {

            $tenant->run(function ($tenant) {

                if ($tenant->id == self::TENANT_CHURCHILL_ID) {

                    $this->info('Command run for...'.$tenant->id);

                    $collegeNo = Colleges::get(['id'])->first();

                    $updateStudentId = [
                        'CIHE2023522' => 'CIHE23522',
                        'CIHE2023309' => 'CIHE23309',
                        'CIHE2023248' => 'CIHE23248',
                        'CIHE2023498' => 'CIHE23498',
                        'CIHE2023627' => 'CIHE23627',
                        'CIHE2023466' => 'CIHE23466',
                        'CIHE2023821' => 'CIHE23821',
                        'CIHE2023293' => 'CIHE23293',
                        'CIHE2023612' => 'CIHE23612',
                        'CIHE2023545' => 'CIHE23545',
                        'CIHE2023367' => 'CIHE23367',
                        'CIHE2023862' => 'CIHE23862',
                        'CIHE2023436' => 'CIHE23436',
                        'CIHE2023387' => 'CIHE23387',
                        'CIHE2023438' => 'CIHE23438',
                        'CIHE2023556' => 'CIHE23556',
                        'CIHE2023752' => 'CIHE23752',
                        'CIHE2023737' => 'CIHE23737',
                        'CIHE2023473' => 'CIHE23473',
                        'CIHE2023757' => 'CIHE23757',
                        'CIHE2023544' => 'CIHE23544',
                        'CIHE2023508' => 'CIHE23508',
                        'CIHE2023812' => 'CIHE23812',
                        'CIHE2023824' => 'CIHE23824',
                        'CIHE2023320' => 'CIHE23320',
                        'CIHE2023308' => 'CIHE23308',
                        'CIHE2023475' => 'CIHE23475',
                        'CIHE2023843' => 'CIHE23843',
                        'CIHE2023334' => 'CIHE23334',
                        'CIHE2023810' => 'CIHE23810',
                        'CIHE2023785' => 'CIHE23785',
                        'CIHE2023274' => 'CIHE23274',
                        'CIHE2023420' => 'CIHE23420',
                    ];

                    // CHANGE 1: Update the student id as per given in sheet.
                    foreach ($updateStudentId as $key => $value) {
                        Student::where('generated_stud_id', $key)->update([
                            'generated_stud_id' => $value,
                        ]);
                    }

                    $changedStudentIds = [
                        'CIHE2023116',
                        'CIHE2023108',
                        'CIHE2023138',
                        'CIHE2023177',
                        'CIHE2023127',
                        'CIHE2023200',
                        'CIHE2023169',
                        'CIHE2023240',
                        'CIHE2023122',
                    ];

                    // CHANGE 2: Generate New Student id for above student.
                    foreach ($changedStudentIds as $key => $value) {

                        $generatedStudId = $this->generateStudentID($collegeNo->id);

                        $this->info('New Student id '.$generatedStudId.' is set for :'.$value);
                        Student::where('generated_stud_id', $value)->update([
                            'generated_stud_id' => $generatedStudId,
                        ]);
                        $this->updateGeneratedNumber($collegeNo->id);
                    }

                    // CHANGE 3: Change auto increment number so it won't confic for future student.
                    $studIDFormate = StudentIdFormate::where('college_id', $collegeNo->id)->get();
                    $studIDFormate[0]->auto_increment = ($studIDFormate[0]->auto_increment + 1000);
                    $studIDFormate[0]->save();
                }
            });
        });
    }

    public function patch3_16()
    {
        Tenant::all()->each(function ($tenant) {

            $tenant->run(function ($tenant) {

                if ($tenant->id == self::TENANT_CHURCHILL_ID) {

                    $this->info('Command run for...'.$tenant->id);

                    $collegeNo = Colleges::get(['id'])->first();

                    $generatedStudId = $this->generateStudentID($collegeNo->id);

                    $objStudent = Student::find(178);
                    $objStudent->generated_stud_id = $generatedStudId;
                    $objStudent->save();

                    $this->info('New Student id '.$generatedStudId.' is set for :'.$objStudent->first_name);

                    $this->updateGeneratedNumber($collegeNo->id);
                }
            });
        });
    }

    public function patch3_17()
    {
        Tenant::all()->each(function ($tenant) {

            $tenant->run(function ($tenant) {

                $this->info('Command run for...'.$tenant->id);

                if ($tenant->id == self::TENANT_AXIS_ID) {
                    $completedStudentLists = StudentCourses::with(['student' => function ($query) {
                        $query->select('id', 'first_name');  // Select only the 'id' and 'firstname' fields
                    }])
                        // ->where('student_id', 399)
                        ->whereIn('status', ['Completed', 'Current Student'])
                        ->get(['student_id', 'course_id']);

                    foreach ($completedStudentLists as $completedStudentList) {

                        StudentSubjectEnrolment::where([
                            'student_id' => $completedStudentList->student_id,
                            'course_id' => $completedStudentList->course_id,
                        ])
                            ->whereIn('final_outcome', ['C', 'NYC'])
                            ->update([
                                'last_assessment_approved_date' => DB::raw('activity_finish_date'),
                            ]);

                        $this->info('Result is updated for student.'.$completedStudentList->student->first_name);
                    }
                }
            });
        });
    }

    /*
     Patch to udate course status for incomplete courses
     This patch takes all the inactive courses (whose activated_now is 0), then checks if the course is complete or not
     and update the activated_now to 2 if the course is not complete
     */
    public function patch3_18()
    {
        if ($this->option('rollback')) {
            /* Rollback Logic */
            Tenant::all()->each(function ($tenant) {
                $tenant->run(function () {
                    Courses::withoutEvents(function () {
                        return Courses::where('activated_now', 2)->update(['activated_now' => 0]);
                    });
                });
            });

            return;
        }

        Tenant::all()->each(function ($tenant) {
            $tenant->run(function () {
                $allCourses = Courses::where('activated_now', 0)->get();
                foreach ($allCourses as $course) {
                    $courseComplete = $course->isCourseComplete() ?? false;
                    if (! $courseComplete) {
                        $course->activated_now = 2;
                        $course->saveQuietly();
                    }
                }
            });
        });
    }

    public function patch3_19()
    {
        Tenant::all()->each(function ($tenant) {
            $tenant->run(function ($tenant) {

                $this->info('Command run for...'.$tenant->id);

                // if ($tenant->id == self::TENANT_AXIS_ID) {

                $allStudentEnrollMents = StudentSubjectEnrolment::query()
                    ->select(['id', 'student_id', 'course_id'])
                    ->groupBy('student_id', 'course_id')
                    ->get();

                $i = 0;
                foreach ($allStudentEnrollMents as $allStudentEnrollMent) {

                    $studentCourseCount = StudentCourses::where([
                        'student_id' => $allStudentEnrollMent->student_id,
                        'course_id' => $allStudentEnrollMent->course_id,
                    ])
                        ->join('rto_students', 'rto_students.id', '=', 'rto_student_courses.student_id')
                        ->join('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
                        ->select(['rto_courses.course_code', 'rto_students.first_name', 'rto_students.family_name', 'rto_student_courses.id', 'rto_student_courses.student_id', 'rto_student_courses.course_id', 'rto_student_courses.status'])
                        ->get();

                    if (count($studentCourseCount) > 1) {
                        $this->info($i.' - ID. '.$allStudentEnrollMent->student_id.' - Name: '.$studentCourseCount[0]->first_name.' '.$studentCourseCount[0]->family_name.' - CourseCode: '.$studentCourseCount[0]->course_code.' - Count:'.count($studentCourseCount));
                        $i++;
                        // $this->info('Course Info.'.$studentCourseCount);
                    }
                }

                // }
            });
        });

        if ($this->option('rollback')) {
            /* Rollback Logic */
        }
    }

    public function patch3_20()
    {
        Tenant::all()->each(function ($tenant) {
            // Fox Axis only
            if ($tenant->id == self::TENANT_CHURCHILL_ID) {
                $tenant->run(function ($tenant) {
                    $this->info('Command run for '.$tenant->id.' only. ');

                    $getStudentDatas = [
                        ['generated_id' => 'CIHE22679', 'student_id' => 2743, 'agent_id' => 12, 'status' => 'done'],
                    ];

                    $this->updateOnlyAgentInPayment($getStudentDatas);
                });
            }
        });
    }

    public function patch3_22()
    {
        Tenant::all()->each(function ($tenant) {
            // Fox Axis only
            if ($tenant->id == self::TENANT_CHURCHILL_ID) {
                $tenant->run(function ($tenant) {
                    $this->info('Command run for '.$tenant->id.' only. ');

                    $getStudentDatas = [
                        ['generated_id' => 'CIHE24355', 'student_id' => 3409, 'agent_id' => 280, 'status' => 'startprocess'],
                        ['generated_id' => 'CIHE22720', 'student_id' => 2784, 'agent_id' => 2, 'status' => 'startprocess'],
                    ];

                    $this->updateOnlyAgentInPayment($getStudentDatas);
                });
            }
        });
    }

    public function patch3_23()
    {
        Tenant::all()->each(function ($tenant) {
            $tenant->run(function ($tenant) {
                $this->info('Command run for '.$tenant->id.' only. ');

                StudentAgentCommission::whereRaw('commission_paid >= (gst_amount + commission_payable)')->update(['is_process' => 1]);

                /*$agentCommissions = StudentAgentCommission::all();
                foreach ($agentCommissions as $commission) {
                    if ($commission->commission_paid >= ($commission->gst_amount + $commission->commission_payable)) {
                        $commission->is_process = 1;
                        $commission->save();
                    }
                }*/
            });
        });
    }

    public function patch3_24()
    {
        Tenant::all()->each(function ($tenant) {

            $tenant->run(function ($tenant) {
                $this->info('Command run for '.$tenant->id.' only. ');

                SubjectMaterial::deleteDuplicateFoldersWithNoParent();

                $allTimeTables = Timetable::query()->groupBy('batch')->get();

                foreach ($allTimeTables as $allTimeTable) {

                    $detailArr = [
                        'batch' => $allTimeTable->batch,
                        'term' => $allTimeTable->term,
                        'subjectId' => $allTimeTable->subject_id,
                        'semesterId' => $allTimeTable->semester_id,
                    ];

                    $objSubjectMaterial = new SubjectMaterial;
                    $objSubjectMaterial->createSubFolderForBatch($allTimeTable->college_id, $allTimeTable->created_by, $detailArr);
                }
            });
        });
    }

    public function patch3_25()
    {
        $processCsvFile = function ($csvFilePath, $model, $modelFields, $collegeId) {
            $csvFile = fopen(base_path($csvFilePath), 'r');
            $firstline = true;
            while (($data = fgetcsv($csvFile, 2000, ',')) !== false) {
                if (! $firstline) {

                    $attributes = array_combine($modelFields, $data);
                    $attributes['college_id'] = $collegeId;
                    if ($attributes['absvalue'] < 10) {
                        $attributes['absvalue'] = '000'.$attributes['absvalue'];
                    }

                    if ($attributes['name'] != '') {
                        $exists = $model::where('absvalue', $attributes['absvalue'])->exists();
                        if (! $exists) {
                            // Remove id for auto increment number
                            unset($attributes['id']);
                            $model::create($attributes);
                        } else {
                            $this->info('Id found: '.$attributes['id']);
                        }
                    }
                }
                $firstline = false;
            }
            fclose($csvFile);
        };
        Tenant::all()->each(function ($tenant) use ($processCsvFile) {

            $tenant->run(function ($tenant) use ($processCsvFile) {
                $this->info('Command run for '.$tenant->id.' only. ');
                //    if ($tenant->id == '_queensford1_5FjQmX46ow0wYCdi') {

                $collegeNo = Colleges::get(['id'])->first();

                // Step 1: Retrieve unique IDs to keep from multiple tables using UNION
                $idsToKeep = DB::table('rto_students')->select('birth_country as country')
                    ->union(DB::table('rto_students')->select('nationality'))
                    ->union(DB::table('rto_students')->select('current_country'))
                    ->union(DB::table('rto_students')->select('postal_country'))
                    ->union(DB::table('rto_students')->select('permanent_country'))
                    ->union(DB::table('rto_staff_and_teacher')->select('country'))
                    ->union(DB::table('rto_agents')->select('office_country'))
                    ->union(DB::table('rto_agents')->select('postal_country'))
                    ->union(DB::table('rto_setup_provider')->select('country'))
                    ->union(DB::table('rto_venue')->select('country'))
                    ->pluck('country')
                    ->unique()
                    ->values()
                    ->toArray();

                if ($idsToKeep) {
                    // Step 2: Delete records from `rto_country` that are not in $idsToKeep
                    DB::table('rto_country')
                        ->whereNotIn('id', $idsToKeep)
                        ->delete();
                }

                $collegId = (! empty($collegeNo) && $collegeNo->id != '') ? $collegeNo->id : 0;
                $processCsvFile('database/seeds/csvfiles/rto_country.csv', Country::class, [
                    'id',
                    'college_id',
                    'name',
                    'countrycode',
                    'countrylevel',
                    'nationality',
                    'region',
                    'absvalue',
                    'status',
                    'created_at',
                    'updated_at',
                    'created_by',
                    'updated_by',
                ], $collegId);
                //    }

            });
        });
    }

    public function patch3_26()
    {
        $processCsvFile = function ($csvFilePath, $model, $modelFields, $collegeId) {
            $csvFile = fopen(base_path($csvFilePath), 'r');
            $firstline = true;
            $processedCodes = []; // Track unique provider codes

            while (($data = fgetcsv($csvFile, 2000, ',')) !== false) {
                if (! $firstline) {
                    $attributes = array_combine($modelFields, $data);
                    $attributes['college_id'] = $collegeId;
                    $providerCode = $attributes['provider_code'] ?? null;
                    // Only process if provider_code is unique
                    if ($providerCode && ! in_array($providerCode, $processedCodes)) {
                        $model::create($attributes);
                        $processedCodes[] = $providerCode; // Mark this code as processed
                    }
                }
                $firstline = false;
            }

            fclose($csvFile);
        };
        Tenant::all()->each(function ($tenant) use ($processCsvFile) {

            $tenant->run(function ($tenant) use ($processCsvFile) {
                $this->info('Command run for '.$tenant->id.' only. ');

                $collegeNo = Colleges::get(['id'])->first();
                $collegId = (! empty($collegeNo) && $collegeNo->id != '') ? $collegeNo->id : 0;
                $processCsvFile('database/seeds/csvfiles/rto_credit_providers_code.csv', CreditProvidersCode::class, [
                    'college_id',
                    'provider_code',
                    'abbr',
                    'provider_type',
                    'hep_type',
                    'trading_name',
                    'legal_name',
                    'effective_from_date',
                    'state',
                ], $collegId);
            });
        });
    }

    public function patch3_27()
    {
        Tenant::all()->each(function ($tenant) {
            $tenant->run(function ($tenant) {

                if ($tenant->id != self::TENANT_IIE_ID) {
                    return;
                }

                $this->info('Command run for...'.$tenant->id);

                $this->handleCourseIdCorrection(
                    StudentInitialPayment::from('rto_student_initial_payment'),
                    'rto_student_initial_payment',
                    'initial_payment',
                    $tenant->id,
                    [4120],
                );

                $this->handleCourseIdCorrection(
                    StudentInitialPaymentDetails::from('rto_student_initial_payment_details'),
                    'rto_student_initial_payment_details',
                    'initial_payment_details',
                    $tenant->id,
                    [4120],
                );
            });
        });
    }

    protected function handleCourseIdCorrection($query, $table, $label, $tenantId, $studentIds)
    {
        $records = $query
            ->join('rto_students', 'rto_students.id', '=', "$table.student_id")
            ->join('rto_student_courses', 'rto_student_courses.id', '=', "$table.student_course_id")
            ->whereColumn("$table.course_id", '!=', 'rto_student_courses.course_id')
            // ->whereIn("$table.student_id", $studentIds)
            ->when(! empty($studentIds), function ($query) use ($studentIds, $table) {
                return $query->whereIn("$table.student_id", $studentIds);
            })
            ->get([
                "$table.id",
                "$table.student_id",
                "$table.course_id",
                'rto_student_courses.course_id as original_course_id',
                'rto_students.first_name',
                'rto_students.generated_stud_id',
                DB::raw("CONCAT(rto_students.first_name,' ',rto_students.family_name) AS student_name"),
            ]);

        $total = $records->count();
        $success = 0;
        $failed = 0;

        foreach ($records as $row) {
            // $res = (bool) random_int(0, 1);
            $res = DB::table($table)->where('id', $row->id)->update([
                'course_id' => $row->original_course_id,
            ]);
            $res ? $success++ : $failed++;

            $this->info(sprintf(
                '%s changed course ID from %s to %s for Student ID: %s & Name: %s (%s)',
                $res ? '✅ Successfully' : '❌ Failed to',
                $row->course_id,
                $row->original_course_id,
                $row->student_id,
                $row->student_name,
                $row->generated_stud_id
            ));
        }

        $this->info(sprintf(
            '📊 Tenant: %s | %s Records => Total: %d | ✅ Success: %d | ❌ Failed: %d',
            $tenantId,
            ucfirst(str_replace('_', ' ', $label)),
            $total,
            $success,
            $failed
        ));
    }

    public function patch3_28()
    {
        Tenant::all()->each(function ($tenant) {
            $tenant->run(function ($tenant) {

                $this->info('Command run for...'.$tenant->id);

                $this->info('Command run for...'.$tenant->id);

                $this->handleCourseIdCorrection(
                    StudentInitialPayment::from('rto_student_initial_payment'),
                    'rto_student_initial_payment',
                    'initial_payment',
                    $tenant->id,
                    [],
                );

                $this->handleCourseIdCorrection(
                    StudentInitialPaymentDetails::from('rto_student_initial_payment_details'),
                    'rto_student_initial_payment_details',
                    'initial_payment_details',
                    $tenant->id,
                    [],
                );

                /*$dataArr = StudentInitialPayment::from('rto_student_initial_payment')
                    ->join('rto_students', 'rto_students.id', '=', 'rto_student_initial_payment.student_id')
                    ->join('rto_student_courses', 'rto_student_courses.id', '=', 'rto_student_initial_payment.student_course_id')
                    ->whereColumn('rto_student_initial_payment.course_id', '!=', 'rto_student_courses.course_id')
                    ->get([
                        'rto_student_initial_payment.id',
                        'rto_student_initial_payment.student_id',
                        'rto_student_initial_payment.course_id',
                        'rto_student_courses.course_id as original_course_id',
                        'rto_students.first_name',
                        'rto_students.generated_stud_id',
                        DB::raw("CONCAT(rto_students.first_name,' ',rto_students.family_name) AS student_name")
                    ]);

                $total = $dataArr->count();
                $success = 0;
                $failed = 0;

                foreach ($dataArr as $row){
                    if($row->course_id != $row->original_course_id){
                        $res = StudentInitialPayment::where('id', $row->id)->update([
                            'course_id' => $row->original_course_id
                        ]);
                        $res ? $success++ : $failed++;
                        $message = sprintf(
                            '%s change course ID from %s to %s for Student ID: %s & Student Name: %s (%s)',
                            $res ? '✅ Successfully' : '❌ Failed to',
                            $row->course_id,
                            $row->original_course_id,
                            $row->student_id,
                            $row->student_name,
                            $row->generated_stud_id
                        );
                        $this->info($message);
                    }
                }

                $this->info(sprintf(
                    "For Tenant : %s Total: %d | ✅ Success: %d | ❌ Failed: %d",
                    $tenant->id, $total, $success, $failed
                ));*/
            });
        });
    }

    public function patch3_27_02()
    {
        $processCsvFile = function ($csvFilePath, $model, $modelFields) {
            $csvFile = fopen(base_path($csvFilePath), 'r');
            $firstline = true;

            while (($data = fgetcsv($csvFile, 2000, ',')) !== false) {
                if (! $firstline) {
                    $attributes = array_combine($modelFields, $data);
                    $model::create($attributes);
                }
                $firstline = false;
            }

            fclose($csvFile);
        };
        Tenant::all()->each(function ($tenant) use ($processCsvFile) {

            $tenant->run(function ($tenant) use ($processCsvFile) {
                $this->info('Command run for '.$tenant->id.' only. ');
                CertificateAttribute::truncate();
                $processCsvFile('database/seeds/csvfiles/certificate_attribute.csv', CertificateAttribute::class, [
                    'name',
                    'parent',
                    'type',
                    'tag',
                    'created_by',
                    'updated_by',
                ]);
            });
        });
    }

    public function patch3_29()
    {
        Tenant::all()->each(function ($tenant) {

            $tenant->run(function ($tenant) {

                if ($tenant->id == self::TENANT_LMSC_ID) {

                    $this->info('Running patch for Tenant ID: '.$tenant->id);

                    $tableName = 'rto_student_initial_payment_details';
                    $columnName = 'bonus_gst_amount';

                    if (Schema::hasTable($tableName)) {
                        if (! Schema::hasColumn($tableName, $columnName)) {
                            Schema::table($tableName, function ($table) use ($columnName) {
                                $table->double($columnName)->nullable()->default(null)->after('agent_bonus');
                            });

                            $this->info("✅ Column '{$columnName}' added '{$tableName}'");
                        } else {
                            $this->info("ℹ️ Column '{$columnName}' already exists in '{$tableName}'");
                        }
                    } else {
                        $this->warn("❌ Table '{$tableName}' does not exist.");
                    }
                }
            });
        });
    }

    public function patch3_30()
    {
        $this->info('Temporarily on hold');

        return;

        Tenant::all()->each(function ($tenant) {

            $tenant->run(function ($tenant) {

                $this->info('Running patch for Tenant ID: '.$tenant->id);

                $resArr = StudentInitialPayment::withTrashed()
                    ->join('rto_student_courses', 'rto_student_courses.id', '=', 'rto_student_initial_payment.student_course_id')
                    ->join('rto_students', 'rto_students.id', '=', 'rto_student_courses.student_id')
                    ->where('rto_students.is_student', '=', null)
                    ->select('rto_student_initial_payment.college_id', 'rto_student_initial_payment.id', 'rto_student_initial_payment.student_course_id')
                    ->get();

                $count = $resArr->count();
                $this->info("🔍 Found {$count} initial payment record(s) for deletion.");

                $offerScheduleNullCount = 0;
                foreach ($resArr as $row) {
                    $count = OfferUpfrontFeeSchedule::where(['college_id' => $row->college_id, 'student_course_id' => $row->student_course_id])->count();
                    if ($count == 0) {
                        $offerScheduleNullCount++;
                        $this->info("🗑️  Deleting records for Initial Payment ID: {$row->id}, Student Course ID: {$row->student_course_id}");
                        StudentInitialPayment::where('id', $row->id)->forceDelete();
                        StudentInitialPaymentDetails::where('student_course_id', $row->student_course_id)->forceDelete();
                        StudentMiscellaneousPayment::where('student_course_id', $row->student_course_id)->forceDelete();
                    }
                }
                $this->info("✅  Total of {$offerScheduleNullCount} record(s) were found where students had no offer schedule data but invoices were generated.");
            });
        });
    }

    public function patch3_31()
    {
        Tenant::all()->each(function ($tenant) {
            $tenant->run(function ($tenant) {
                if ($tenant->id == self::TENANT_AXIS_ID) {

                    $this->info('Command run for '.$tenant->id.' only. ');

                    $resArr = StudentAgentCommission::from('rto_student_agent_commission as rsac')
                        ->join('rto_student_initial_payment_transaction as rsipt', 'rsipt.transection_no', '=', 'rsac.transaction_no')
                        ->join('rto_student_initial_payment_details as rsipd', 'rsipd.id', '=', 'rsipt.initial_payment_detail_id')
                        // ->where('rsac.student_course_id', 2847)
                        ->where('rsac.student_id', 767)
                        ->get([
                            'rsac.id',
                            'rsac.transaction_no',
                            'rsac.commission_payable',
                            'rsipd.invoice_number',
                        ])
                        ->toArray();

                    $this->updateAgentInvoiceNumber($resArr, $tenant->id);
                }
            });
        });
    }

    public function patch3_32()
    {
        // Define common CSV file processing function
        $processCsvFile = function ($csvFilePath, $model, $modelFields) {
            $csvFile = fopen(base_path($csvFilePath), 'r');
            $firstline = true;
            while (($data = fgetcsv($csvFile, 2000, ',')) !== false) {
                if (! $firstline) {
                    $attributes = array_combine($modelFields, $data);
                    $model::create($attributes);
                }
                $firstline = false;
            }
            fclose($csvFile);
        };
        Tenant::all()->each(function ($tenant) use ($processCsvFile) {
            $tenant->run(function ($tenant) use ($processCsvFile) {
                $this->info('Command run for '.$tenant->id);

                Report::truncate();
                $processCsvFile('database/seeds/csvfiles/rto_reports.csv', Report::class, [
                    'id',
                    'report_category',
                    'sub_category_id',
                    'report_name',
                    'created_at',
                    'updated_at',
                ]);
                $this->info('Report added successfully for...'.$tenant->id);
            });
        });
    }

    public function patch3_33()
    {
        Tenant::all()->each(function ($tenant) {
            $tenant->run(function ($tenant) {
                $this->info('Command run for...'.$tenant->id);

                // Add new email parameters
                $newEmailParameters = [
                    [
                        'parameter_name' => 'DeanName',
                        'parameter_value' => '{DeanName}',
                        'parameter_detail' => '{"type": "text","value": "Dean Name"}',
                        'created_at' => now(),
                        'updated_at' => now(),
                        'created_by' => '1',
                        'updated_by' => '1',
                    ],
                    [
                        'parameter_name' => 'DeanSignature',
                        'parameter_value' => '{DeanSignature}',
                        'parameter_detail' => '{"type": "text","value": "Dean Signature"}',
                        'created_at' => now(),
                        'updated_at' => now(),
                        'created_by' => '1',
                        'updated_by' => '1',
                    ],
                    [
                        'parameter_name' => 'CollegeSignature',
                        'parameter_value' => '{CollegeSignature}',
                        'parameter_detail' => '{"type": "text","value": "College Signature"}',
                        'created_at' => now(),
                        'updated_at' => now(),
                        'created_by' => '1',
                        'updated_by' => '1',
                    ],
                ];

                foreach ($newEmailParameters as $parameter) {
                    // Check if parameter already exists
                    $exists = EmailParameter::where('parameter_name', $parameter['parameter_name'])->exists();
                    if (! $exists) {
                        EmailParameter::create($parameter);
                        $this->info('Email parameter '.$parameter['parameter_name'].' added successfully for '.$tenant->id);
                    } else {
                        $this->info('Email parameter '.$parameter['parameter_name'].' already exists for '.$tenant->id);
                    }
                }

                // Add new letter parameters
                $newLetterParameters = [
                    [
                        'parameter_name' => 'DeanName',
                        'parameter_value' => '{DeanName}',
                        'parameter_detail' => '{"type": "text", "value": "Dean Name"}',
                        'created_at' => now(),
                        'updated_at' => now(),
                        'created_by' => '1',
                        'updated_by' => '1',
                    ],
                    [
                        'parameter_name' => 'DeanSignature',
                        'parameter_value' => '{DeanSignature}',
                        'parameter_detail' => '{"type": "text", "value": "Dean Signature"}',
                        'created_at' => now(),
                        'updated_at' => now(),
                        'created_by' => '1',
                        'updated_by' => '1',
                    ],
                    [
                        'parameter_name' => 'CollegeSignature',
                        'parameter_value' => '{CollegeSignature}',
                        'parameter_detail' => '{"type": "text", "value": "College Signature"}',
                        'created_at' => now(),
                        'updated_at' => now(),
                        'created_by' => '1',
                        'updated_by' => '1',
                    ],
                ];

                foreach ($newLetterParameters as $parameter) {
                    // Check if parameter already exists
                    $exists = LetterParameter::where('parameter_name', $parameter['parameter_name'])->exists();
                    if (! $exists) {
                        LetterParameter::create($parameter);
                        $this->info('Letter parameter '.$parameter['parameter_name'].' added successfully for '.$tenant->id);
                    } else {
                        $this->info('Letter parameter '.$parameter['parameter_name'].' already exists for '.$tenant->id);
                    }
                }
            });
        });
    }
}
