<?php

namespace App\Console\Commands;

use App\Model\v2\Tenant;
use Domains\Students\RiskAssessment\Jobs\ProcessRiskAssessmentsJob;
use Illuminate\Console\Command;
use Illuminate\Support\Facades\Log;

class ProcessRiskAssessments extends Command
{
    /**
     * The name and signature of the console command.
     *
     * @var string
     */
    protected $signature = 'risk-assessment:process {--tenant= : Process for specific tenant}';

    /**
     * The console command description.
     *
     * @var string
     */
    protected $description = 'Process risk assessments for all tenants or a specific tenant';

    /**
     * Execute the console command.
     */
    public function handle(): int
    {
        $this->info('Starting risk assessment processing...');

        try {
            $tenantId = $this->option('tenant');

            if ($tenantId) {
                $tenant = $this->getTenant($tenantId);
                // Process for specific tenant
                $this->processForTenant($tenant);
            } else {
                // Process for all tenants
                $this->processForAllTenants();
            }

            $this->info('Risk assessment processing completed successfully.');

            return 0;
        } catch (\Exception $e) {
            $this->error('Risk assessment processing failed: '.$e->getMessage());
            Log::error('Risk assessment command failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            return 1;
        }
    }

    /**
     * Process risk assessments for all tenants.
     */
    protected function processForAllTenants(): void
    {
        $this->info('Processing risk assessments for all tenants...');

        // Get all tenants (adjust based on your tenant model)
        $tenants = $this->getAllTenants();

        foreach ($tenants as $tenant) {
            $this->processForTenant($tenant);
        }
    }

    /**
     * Process risk assessments for a specific tenant.
     */
    protected function processForTenant(Tenant $tenant): void
    {
        $this->info("Processing risk assessments for tenant ID: {$tenant->id}");

        try {
            // Set tenant context
            $this->setTenantContext($tenant);

            // Dispatch the job
            ProcessRiskAssessmentsJob::dispatch();

            $this->info("Risk assessment job dispatched for tenant: {$tenant->name}");
        } catch (\Exception $e) {
            $this->error("Failed to process risk assessments for tenant {$tenant->name}: {$e->getMessage()}");
            Log::error('Failed to process risk assessments for tenant', [
                'tenant_id' => $tenant->id,
                'tenant_name' => $tenant->name,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Get all tenants.
     * Adjust this method based on your tenant model and structure.
     */
    protected function getAllTenants()
    {
        // TODO: Implement based on your tenant model
        // Example: return Tenant::all();

        // For now, return empty collection
        return Tenant::all();
    }

    /**
     * Get specific tenant.
     * Adjust this method based on your tenant model and structure.
     */
    protected function getTenant($tenantId)
    {
        // TODO: Implement based on your tenant model
        // Example: return Tenant::find($tenantId);

        // For now, return null
        return Tenant::find($tenantId);
    }

    /**
     * Set tenant context.
     * Adjust this method based on your multi-tenancy implementation.
     */
    protected function setTenantContext($tenant): void
    {
        // tenant($tenant);
        tenancy()->initialize($tenant);
    }
}
