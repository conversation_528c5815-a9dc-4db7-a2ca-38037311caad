<?php

namespace App\Console;

use App\Console\Commands\AvetMissImport;
use App\Console\Commands\ChangeStudentCourseStatus;
use App\Console\Commands\CreateStudentSubjectEnrolment;
use App\Console\Commands\ManageActivityLog;
use App\Console\Commands\PingLocal;
use App\Console\Commands\PlacementStatusCron;
use Domains\Xero\Commands\XeroKeepAliveCommand;
use Illuminate\Console\Scheduling\Schedule;
use Illuminate\Foundation\Console\Kernel as ConsoleKernel;

class Kernel extends ConsoleKernel
{
    /**
     * The Artisan commands provided by your application.
     *
     * @var array
     */
    protected $commands = [
        'App\Console\Commands\LastFiveDaysAttendanceMail',
        'App\Console\Commands\StudentPercentage',
        'App\Console\Commands\taskNewData',
        'App\Console\Commands\taskMigrate',
        Commands\SupersededstatusCron::class,
        Commands\TestMail::class,
        Commands\TimetableData::class,
        Commands\StudentTcsiData::class,
        Commands\StudentTcsiData::class,
        Commands\PersonalAccessTokenRemove::class,
        Commands\StudentCourseStatusChangeHistory::class,
        Commands\Patch::class,
        Commands\PatchVersion1::class,
        Commands\PatchVersion3::class,
        Commands\AssetVersioningCommand::class,
        Commands\RehashFiles::class,
        Commands\RunInsideTenant::class,
        Commands\RemoveTenant::class,
        Commands\SyncScout::class,
        Commands\GalaxyQueueCommand::class,
        Commands\GalaxyOldQueueDelete::class,
        // Commands\OptimizeSetup::class,
        XeroKeepAliveCommand::class,
        PingLocal::class,
        AvetMissImport::class,
        Commands\ChangeDbCollation::class,
        Commands\CreateProcedure::class,
        \App\Console\Commands\DeleteOldLogs::class,
        Commands\NewCollageDummyData::class,
        \Support\Commands\RefreshReleaseNotes::class,
        \Support\Commands\AddReleaseNote::class,
        ChangeStudentCourseStatus::class,
        PlacementStatusCron::class,
        ManageActivityLog::class,
        Commands\RenderTemplate::class,
        Commands\StudentSanction::class,
        Commands\NotifyPendingEnrollment::class,
        Commands\CheckTenantSMTP::class,
        Commands\CleanOldSessions::class,
        CreateStudentSubjectEnrolment::class,
    ];

    /**
     * Define the application's command schedule.
     *
     * @return void
     */
    protected function schedule(Schedule $schedule)
    {

        // $schedule->command('attendance:students')
        //     ->dailyAt('13:00');
        // $schedule->command('attendance:studentpercentage')
        //     ->dailyAt('13:00');
        // $schedule->command('inspire')
        //          ->hourly();
        // $schedule->command('tenants:run testmail:cron')
        //          ->everyMinute();

        $schedule->command('tenants:run deleteuserloginlog:cron')
            ->daily();
        $schedule->command('tenants:run placementstatus:cron')
            ->daily();
        $schedule->command('tenants:run changestudentcoursestatus:cron')
            ->daily();
        // $schedule->command('app:ping.local')->everySixHours();
        $schedule->command('xero:refresh-token')->everyFifteenMinutes();
        $schedule->command('app:galaxy-queue')->everyMinute();
        // $schedule->command('xero:refresh-token')->tuesdays()->at('00:00');
        // $schedule->command('xero:refresh-token')->thursdays()->at('00:00');
        $schedule->command('tenants:run personalaccesstokenremove:cron')->monthlyOn(1, '15:00'); // Run the task every month on the 4th at 15:00

        // Schedule the deletion of old logs to run daily at midnight
        $schedule->command('logs:delete-old')->daily();
        // $schedule->command('tenants:run logs:delete-old')->daily();
        $schedule->command('app:student-sanction')->daily(); // Student login access disable
        $schedule->command('app:check-tenant-smtp')->daily();
        $schedule->command('sessions:clean-old')->monthly();

    }

    /**
     * Register the Closure based commands for the application.
     *
     * @return void
     */
    protected function commands()
    {
        require base_path('routes/console.php');
    }
}
