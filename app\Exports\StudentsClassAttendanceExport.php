<?php

namespace App\Exports;

use Maatwebsite\Excel\Concerns\FromCollection;
use Maatwebsite\Excel\Concerns\WithHeadings;

class StudentsClassAttendanceExport implements FromCollection, WithHeadings
{
    /**
     * @return \Illuminate\Support\Collection
     */
    private $data;

    public function __construct($data1)
    {
        $this->data = $data1;

    }

    public function headings(): array
    {
        foreach ($this->data as $key => $value) {
            $arrData[$key] = $value;
        }

        $heading[] = ['Class Attendance Roll: '.$arrData['batch']];
        $heading[] = ['Subject Name :', $arrData['subjectCode'].' : '.$arrData['subjectName']];
        $heading[] = ['Semester :', $arrData['semester_name'], 'Batch Duration : ', parseDate($arrData['batchDate']['firstdate'], 'd-m-Y').' to '.parseDate($arrData['batchDate']['lastdate'], 'd-m-Y')];
        $heading[] = ['Location :', $arrData['campus_name'], 'Venue : ', $arrData['venue_name'].', room '.$arrData['roomId']];
        $heading[] = ['Teacher :', $arrData['teacherName'], 'Batch Time :', $arrData['startTime'].' to '.$arrData['endTime']];
        $heading[] = [' '];

        return $heading;
    }

    public function collection()
    {
        foreach ($this->data as $key => $value) {
            $arrData[$key] = $value;
        }

        $columns = [];

        foreach ($arrData['getAllDays'] as $dayRowKey => $dayRow) {

            $dateValue = [];
            $dateValue[] = 'ATTENDANCE DELIVERY DATES:';
            $dateValue[] = '';
            $dateValue[] = '';

            foreach ($dayRow as $rowDate) {
                $dateValue[] = parseDate($rowDate['timetable_date'], 'd-m-Y');
                $dateValue[] = ' ';
                $dateValue[] = ' ';
            }

            $columns[] = [' '];
            $columns[] = ['Week:', $dayRowKey.(in_array($dayRowKey, $arrData['holidayWeek']) ? ' (Holiday Week)' : '')];
            $columns[] = $dateValue;

            $tableHeader = [];

            $tableHeader[] = 'SN';
            $tableHeader[] = 'ID';
            $tableHeader[] = 'Name';

            foreach ($dayRow as $rowDate) {
                $tableHeader[] = 'STATUS';
                $tableHeader[] = 'MISSED MINUTES';
                $tableHeader[] = 'NOTES';
            }

            $columns[] = $tableHeader;
            if (! empty($arrData['studentList'])) {
                $i = 1;
                foreach ($arrData['studentList'] as $row) {
                    $markArr = [];
                    if (! empty($row['attendance_data'])) {
                        foreach ($row['attendance_data'] as $attKey => $attData) {
                            foreach ($dayRow as $key => $value) {
                                if ($value['timetable_date'] == $attData['timetable_date']) {
                                    $markArr[] = $attData['status'] ?? 'pending';
                                    $markArr[] = $attData['missed_minutes'];
                                    $markArr[] = $attData['notes'] ?? '';
                                }
                            }
                        }
                    }
                    $columns[] = [
                        $i,
                        $row['generated_stud_id'],
                        $row['full_name'],
                        ...$markArr,
                    ];
                    $i++;
                }
            }

        }

        return collect($columns);
    }
}
