<?php

namespace App\Http\Controllers\Frontend;

use App\Exceptions\ApplicationException;
use App\Exports\AgencyExport;
use App\Http\Controllers\Controller;
use App\Model\Agent;
use App\Model\AgentDocumentChecklist;
use App\Model\AgentImages;
use App\Model\AgentProfileStatus;
use App\Model\AgentStatus;
use App\Model\Country;
use App\Model\EmailTemplate;
use App\Model\Industries;
use App\Model\SecurityQuestion;
use App\Model\Staff;
use App\Model\StudentAgentCommission;
use App\Model\StudentCourse;
use App\Model\StudentInitialPayment;
use App\Model\StudentInitialPaymentDetails;
use App\Model\UserRoleType;
use App\Model\Users;
use App\Model\v2\AgentCommunication;
use App\Model\v2\AgentEmail;
use App\Repositories\AgentRepository;
use Domains\Xero\Jobs\SyncAgentTrackingOptions;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Bus;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Validator;
use Integrations\Zoho\Jobs\SyncAgentFromZoho;
use Integrations\Zoho\Traits\ZohoTrait;
use Maatwebsite\Excel\Facades\Excel;

class AgentController extends Controller
{
    use ZohoTrait;

    protected $agentRepository;

    public function __construct(
        AgentRepository $agentRepository

    ) {
        $this->agentRepository = $agentRepository;

        // $this->rememberToken
    }

    public function addAgent(Request $request)
    {
        $collegeId = Auth::user()->college_id;
        $userId = Auth::user()->id;

        $objRtoAgent = new Agent;
        $agentRecord = $objRtoAgent->getAgentList($collegeId);

        $objAgentStatus = new AgentStatus;
        $agentStatusArr = $objAgentStatus->getAgentStatusListArr($collegeId);

        $objRtoUser = new Staff;
        $userRecord = $objRtoUser->getStaffList($collegeId);

        $objAgentDocumentChecklist = new AgentDocumentChecklist;
        $agentChecklistData = $objAgentDocumentChecklist->getAgentCheckList();

        $objRtoIndustries = new Industries;
        $industryRecord = $objRtoIndustries->getIndustriesList();

        $objRtoCountry = new Country;
        $countryRecord = $objRtoCountry->getCountryList($collegeId);

        if ($request->isMethod('post')) {

            $validations = [
                'agency_name' => 'required',
                'contact_person' => 'required',
                'primary_email' => 'required|unique:rto_agents,primary_email|unique:rto_users,email',
                'telephone' => 'required',
                'office_address' => 'required',
                'office_postcode' => 'required',
            ];

            $failedValidations = [];
            if ($request->input('docs_count') > 0) {

                foreach ($request->input('is_compulsory') as $key => $value) {
                    $validations['images_'.$key] = 'mimes:jpeg,png,pdf';
                    if ($request->input('is_compulsory.'.$key) == '1') {
                        $validations['images_'.$key] = 'required|mimes:jpeg,png,pdf';
                        $failedValidations['images_'.$key.'.required'] = $request->input('document_name.'.$key).' is required.';
                    }
                    $failedValidations['images_'.$key.'.mimes'] = $request->input('document_name.'.$key).' is must be a file of type: jpeg, pdf, png.';
                }
            }

            $postalCheckboxValue = $request['postalCheckbox'];
            if ($request['postalCheckbox'] == 'on') {
                $validations['postal_city'] = 'required';
            }

            $validator = Validator::make($request->all(), $validations, $failedValidations);

            if ($validator->fails()) {
                return redirect(route('add-agent'))
                    ->withErrors($validator)
                    ->withInput();
            }

            DB::beginTransaction();
            try {

                $objRtoAgent = new Agent;
                $lastInsertedId = $objRtoAgent->saveAgentRecords($collegeId, $request);

                $objRtoAgentStatus = new AgentProfileStatus;
                $objRtoAgentStatus->saveAgentProfileStatus($collegeId, $lastInsertedId, $request);

                // multiple image upload
                $objRtoAgentImages = new AgentImages;
                $objRtoAgentImages->saveAgentImages($collegeId, $lastInsertedId, $request);

                DB::commit();
                dispatch(new SyncAgentTrackingOptions($lastInsertedId));
            } catch (\Exception $e) {
                DB::rollBack();

                throw new ApplicationException($e->getMessage());
            }

            $request->session()->flash('session_success', 'Agent Saved Successfully.');
            // multiple image upload end here

            return redirect(route('view-agent-list'));
        }

        $data['header'] = [
            'title' => 'Marketing Add Agents',
            'breadcrumb' => [
                'Home' => route('user_dashboard'),
                'Agents Manage' => route('view-agent-list'),
                'Add Agent' => '',
            ]];
        $data['pagetitle'] = 'Add Agent';
        $data['plugincss'] = [];
        $data['css'] = [];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js'];
        $data['js'] = ['agent.js'];
        $data['funinit'] = ['Agent.initAddAgent()'];
        $data['activateValue'] = 'Marketing';
        $data['isAddPage'] = true;
        $data['countryRecord'] = $countryRecord;
        $data['agentRecord'] = $agentRecord;
        $data['userRecord'] = $userRecord;
        $data['industryRecord'] = $industryRecord;
        $data['agentStatusArr'] = $agentStatusArr;
        $data['agentChecklistData'] = $agentChecklistData;
        $data['mainmenu'] = 'partner_agent';

        return view('frontend.agent.add-agent', $data);
    }

    public function editAgent(Request $request, $agentId)
    {

        $collegeId = Auth::user()->college_id;
        $userId = Auth::user()->id;

        $objRtoAgent = new Agent;
        $agentRecord = $objRtoAgent->getAgentList($collegeId);
        $agenEditData = $objRtoAgent->getAgentDetail($agentId);

        $objAgentStatus = new AgentStatus;
        $agentStatusArr = $objAgentStatus->getAgentStatusListArr($collegeId);

        $objRtoUser = new Staff;
        $userRecord = $objRtoUser->getStaffList($collegeId);

        $objRtoIndustries = new Industries;
        $industryRecord = $objRtoIndustries->getIndustriesList();

        $objRtoCountry = new Country;
        $countryRecord = $objRtoCountry->getCountryList($collegeId);

        $objRtoImages = new AgentImages;
        $agenEditImage = $objRtoImages->getImageList($agentId);

        $objAgentDocumentChecklist = new AgentDocumentChecklist;
        $agentChecklistData = $objAgentDocumentChecklist->getAgentCheckList();

        $objRtoAgentStatus = new AgentProfileStatus;
        $agenStatusEditData = $objRtoAgentStatus->getAgentProfileStatusId($agentId);

        $agenStatusEditDataFetch = [];
        if ($agenStatusEditData->isNotEmpty()) {
            $agenStatusEditDataFetch = $objRtoAgentStatus->getAgentProfileStatusDetail($agenStatusEditData[$agentId]);
        }

        // Set Data According to image List
        $agentCheckListData = [];
        $agentCommentData = [];
        $agentImageData = [];
        $approveImageData = [];
        for ($i = 0; $i < count($agenEditImage); $i++) {
            $checklistId = $agenEditImage[$i]['checklist_id'];
            $agentCheckListData[$checklistId] = 0;
            $agentCommentData[$checklistId] = $agenEditImage[$i]['comment'];
            $agentImageData[$checklistId] = $agenEditImage[$i]['original_name'];
            $approveImageData[$checklistId] = $agenEditImage[$i]['approved'];
        }

        $isComplsarySet = [];
        $commentData = [];
        $imageData = [];
        for ($i = 0; $i < count($agentChecklistData); $i++) {
            $checklistId = $agentChecklistData[$i]->id;
            if (isset($agentCheckListData[$checklistId])) {
                $isComplsarySet[$checklistId] = $agentCheckListData[$checklistId];
                $commentData[$checklistId] = $agentCommentData[$checklistId];
                $imageData[$checklistId] = $agentImageData[$checklistId];
            } else {
                $isComplsarySet[$checklistId] = $agentChecklistData[$i]->is_compulsory;
                $commentData[$checklistId] = '';
                $imageData[$checklistId] = '';
            }
        }
        if ($request->isMethod('post')) {

            $validations = [
                'agency_name' => 'required',
                'contact_person' => 'required',
                // 'primary_email' => 'required',
                'telephone' => 'required',
                'office_address' => 'required',
                'office_postcode' => 'required',
                // 'office_ABN' => 'required',
            ];

            $failedValidations = [];
            if ($request->input('docs_count') > 0) {

                foreach ($request->input('is_compulsory') as $key => $value) {
                    if ($request->input('is_compulsory.'.$key) == '1') {
                        $validations['images_'.$key] = 'required|mimes:jpeg,png,pdf';
                        $failedValidations['images_'.$key.'.required'] = $request->input('document_name.'.$key).' is required.';
                        $failedValidations['images_'.$key.'.mimes'] = $request->input('document_name.'.$key).' is must be a file of type: jpeg, pdf, png.';
                    }
                }
            }

            $postalCheckboxValue = $request['postalCheckbox'];
            if ($request['postalCheckbox'] == 'on') {
                $validations['postal_city'] = 'required';
            }

            $validator = Validator::make($request->all(), $validations, $failedValidations);

            if ($validator->fails()) {
                return redirect(route('edit-agent', ['id' => $agentId]))
                    ->withErrors($validator)
                    ->withInput();
            }
            $objRtoAgent = new Agent;
            $objRtoAgent->editAgentRecords($agentId, $collegeId, $request);

            $agentuserId = $objRtoAgent->getAgentUserId($agentId);
            if ($agentuserId[0]->user_id != null) {
                $objRtoUsers = new Users;
                if ($request->input('primary_email')) {
                    // $objRtoUsers->updateUserMail($agentuserId[0]->user_id, $collegeId, $request);
                }
            }

            // multiple image upload
            if (! empty($request->file())) {
                $objRtoAgentImages = new AgentImages;
                $lastInsertedId = $agentId;
                $objRtoAgentImages->editAgentImages($collegeId, $lastInsertedId, $request);
            }

            $request->session()->flash('session_success', 'Agent Updated Successfully.');

            return redirect(route('view-agent-list'));
        }
        $data['header'] = [
            'title' => 'Marketing Edit Agents',
            'breadcrumb' => [
                'Home' => route('user_dashboard'),
                'Agents Manage' => route('view-agent-list'),
                'Edit Agent' => '',
            ]];
        $data['pagetitle'] = 'Edit Agent';
        $data['plugincss'] = [];
        $data['css'] = [];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js'];
        $data['js'] = ['agent.js'];
        $data['funinit'] = ['Agent.initAddAgent()'];
        $data['activateValue'] = 'Marketing';
        $data['isAddPage'] = false;
        $data['countryRecord'] = $countryRecord;
        $data['agentRecord'] = $agentRecord;
        $data['userRecord'] = $userRecord;
        $data['industryRecord'] = $industryRecord;
        $data['agenEditData'] = $agenEditData;
        $data['agenEditImage'] = $agenEditImage;
        $data['approveImageData'] = $approveImageData;
        $data['agentChecklistData'] = $agentChecklistData;
        $data['isComplsarySet'] = $isComplsarySet;
        $data['agentStatusArr'] = $agentStatusArr;
        $data['agenStatusEditDataFetch'] = $agenStatusEditDataFetch;
        $data['commentData'] = $commentData;
        $data['imageData'] = $imageData;
        $data['agentId'] = $agentId;
        $data['mainmenu'] = 'partner_agent';

        // echo "<pre/>";print_r($data);exit;

        if ($agenEditData->college_id == $collegeId) {
            return view('frontend.agent.add-agent', $data);
        } else {
            return view('frontend.error_page.unauthorized', $data);
        }
    }

    public function viewAgentList(Request $request)
    {
        $collegeId = Auth::user()->college_id;
        $perPage = Config::get('constants.pagination.perPage');
        $searchFieldArr = ['allAgent' => 'All Agent',
            'id' => 'Agent ID',
            'agency_name' => 'Agent Name',
            'status' => 'Status',
            'primary_email' => 'Primary Email'];

        $objAgentStatus = new AgentStatus;
        $agentStatusArr = $objAgentStatus->getAgentStatusListArr($collegeId);
        $objSecurityQuestion = new SecurityQuestion;
        $objArrSecurityQuestion = $objSecurityQuestion->getSecurityQuestion();

        if ($request->isMethod('post')) {
            $validator = Validator::make($request->all(), ['agent_code' => 'required',
            ]);

            if ($validator->fails()) {
                return redirect(route('view-agent-list'))->withErrors($validator)->withInput();
            }
            $objAgentCodeEdit = new Agent;
            $objAgentCodeEdit->editAgentCode($request);

            $request->session()->flash('session_success', 'Agent Code Updated Successfully.');

            return redirect(route('view-agent-list'));
        }

        $data['header'] = [
            'title' => 'Marketing Agents view',
            'breadcrumb' => [
                'Home' => route('user_dashboard'),
                'Marketing' => '',
                'Agents view' => '',
            ]];
        $data['pagetitle'] = 'View Agent List';
        $data['plugincss'] = [];
        $data['css'] = [];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js'];
        $data['js'] = ['agent.js'];
        $data['funinit'] = ['Agent.initViewAgent()'];
        $data['activateValue'] = 'Marketing';
        $data['arrSecurityQuestion'] = $objArrSecurityQuestion;
        $data['searchFieldArr'] = $searchFieldArr;
        $data['agentStatusArr'] = $agentStatusArr;
        $data['mainmenu'] = 'partner_agent';
        $data['isZohoConnect'] = $this->isZohoConnected();

        return view('frontend.agent.view-agent-list', $data);
    }

    public function addAgentAgencyStatus(Request $request)
    {
        $collegeId = Auth::user()->college_id;

        if ($request->isMethod('post')) {
            $validator = Validator::make($request->all(), [
                'status' => 'required',
            ]);
            if ($validator->fails()) {
                return redirect(route('view-agent-list'))->withErrors($validator)->withInput();
            }

            $objRtoAgentStatus = new AgentProfileStatus;
            $arrObj = $objRtoAgentStatus->saveAgentAgencyStatus($collegeId, $request->input('agentId'), $request);
            if ($arrObj == 'Exist') {
                $request->session()->flash('session_error', 'Agency Status Already Exist.');
            } else {
                $request->session()->flash('session_success', 'Agent Agency Status Add Successfully.');
            }

            return redirect(route('view-agent-list'));
        }
    }

    public function _getAgentStatusList($agentId)
    {

        $collegeId = Auth::user()->college_id;
        $objAgentStatus = new AgentStatus;
        $agentStatusArr = $objAgentStatus->getAgentStatusListArr($collegeId);

        $objRtoAgentStatus = new AgentProfileStatus;
        $agentStatusList = $objRtoAgentStatus->getAgentAgencyListArr($collegeId, $agentId, $agentStatusArr);

        echo json_encode($agentStatusList);
        exit;
    }

    public function viewAgentEmailList(Request $request)
    {

        $userId = Auth::user()->id;
        $collegeId = Auth::user()->college_id;
        $perPage = Config::get('constants.pagination.perPage');
        // $agentStatus = Config::get('constants.arrAgentStatus');

        $ovjviewAgentRecords = new Agent;
        $users = $ovjviewAgentRecords->viewAgentRecordsEmail($perPage);

        $objRtoUser = new Agent;
        $arrAccountManager = $objRtoUser->getStaffFilterList($collegeId);

        $objRtoCountry = new Country;
        $countryRecord = $objRtoCountry->getCountryList($collegeId);
        // $superAgent = Users::paginate($perPage);

        $objEmailTemplate = new EmailTemplate;
        $arrEmailTemplate = $objEmailTemplate->getEmailTemplateByReceipt(3);

        $objAgentStatus = new AgentStatus;
        $agentStatus = $objAgentStatus->getAgentStatusListArr($collegeId);
        $defaultAgent[''] = ' - - Select Agent Status - -';

        if ($request->isMethod('post')) {

            if (empty($request->input('agent_select'))) {
                $validations['agent_select'] = 'required';
                $checkValidations = [
                    'agent_select.required' => 'Please select at least One Email Address from Agent List.',
                ];
                $validator = Validator::make($request->all(), $validations, $checkValidations);
                if ($validator->fails()) {
                    return redirect(route('view-agent-email-list'))->withErrors($validator)->withInput();
                }
            }

            // $arrStudentCourse = $this->commonRepository->getStudentEmailContent($studentId);
            // $dataArr = $this->dataBindForStudent($row, $enrolledCourseList, $offeredCourseList, $college_logo);
            // foreach ($dataArr as $key => $value) {
            //     $content = str_replace("$key", $value, $content);
            //     $subject = str_replace("$key", $value, $subject);
            // }

            $objRtoAgentEmail = new AgentEmail;
            $savedEmails = $objRtoAgentEmail->rtoAgentEmailSave($request, $collegeId, $userId);
            // get the attachment form the savedEmails

            // $objRtoAgentCommunication = new AgentCommunication();
            // $objRtoAgentCommunication->rtoAgentCommunication($request, $savedEmails);
            $objRtoAgentCommunication = $this->agentRepository->rtoAgentCommunication($request, $savedEmails);

            $request->session()->flash('session_success', 'Agent Email sent successfully');

            return redirect(route('view-agent-email-list'));
        }
        $data['header'] = [
            'title' => 'Agents Email view',
            'breadcrumb' => [
                'Home' => route('user_dashboard'),
                'Agents Email View' => '',
            ]];
        $data['pagetitle'] = 'View Agent List';
        $data['plugincss'] = [];
        $data['css'] = [];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js', 'ckeditor/ckeditor.js'];
        $data['js'] = ['agent.js'];
        $data['funinit'] = ['Agent.initEmailListView()'];

        $data['users'] = $users;
        $data['arrEmailTemplate'] = $arrEmailTemplate;
        $data['arrAccountManager'] = $arrAccountManager;
        $data['countryRecord'] = $countryRecord;
        $data['agentStatus'] = $defaultAgent + $agentStatus;
        $data['mainmenu'] = 'partner_agent';

        return view('frontend.agent.view-agent-email-list', $data);
    }

    public function deleteAgentList($agentId, Request $request)
    {

        $objRtoAgentStatus = new AgentProfileStatus;
        $agenStatusEditData = $objRtoAgentStatus->getAgentProfileStatusId($agentId);

        // check agent anther module used
        $agentStudentCourseCount = StudentCourse::where('agent_id', $agentId)->count();
        $agentStudentInitialPaymentCount = StudentInitialPayment::where('agent_id', $agentId)->count();
        $agentStudentInitialPaymentDetailsCount = StudentInitialPaymentDetails::where('agent_id', $agentId)->count();
        $agentStudentAgentCommissionCount = StudentAgentCommission::where('agent_id', $agentId)->count();

        if ($agentStudentCourseCount > 0 || $agentStudentInitialPaymentCount > 0 || $agentStudentInitialPaymentDetailsCount > 0 || $agentStudentAgentCommissionCount > 0) {
            $request->session()->flash('session_error', 'You cannot delete Agent because another module use it.');

            return redirect(route('view-agent-list'));
        }

        $objRtoAgentImage = new AgentImages;
        $arrAgentImages = AgentImages::where('agent_id', $agentId)->get();

        if (count($arrAgentImages) > 0) {
            foreach ($arrAgentImages as $arrAgentImage) {
                $imageId = $arrAgentImage->id;
                $agentImagesDelete = $objRtoAgentImage->getImageListDelete($agentId, $imageId);
            }
        }

        $category1 = AgentProfileStatus::where('agent_id', $agentId)->get();

        if (count($category1) > 0) {
            $agentStatusDelete = AgentProfileStatus::find($agenStatusEditData[$agentId])->delete();
        }

        $category2 = Agent::find($agentId)->delete();
        $request->session()->flash('session_success', 'User is successfully deleted.');

        return redirect(route('view-agent-list'));
    }

    public function agentStatusAdd(Request $request)
    {

        $college_id = Auth::user()->college_id;

        if ($request->isMethod('post')) {

            $regex = "/^(?=.+)(?:[1-9]\d*|0)?(?:\.\d+)?$/";

            $validator = Validator::make($request->all(), [
                'status_type' => 'required|unique:rto_agent_status,status_type,NULL,id,college_id,'.$college_id,
                'select_duration' => ['required', 'regex:'.$regex],
            ]);

            if ($validator->fails()) {
                return redirect(route('add-agent-status'))
                    ->withErrors($validator)
                    ->withInput();
            }

            $objAgentStatus = new AgentStatus;
            $objAgentStatus->agentStatusAdd($college_id, $request);
            $request->session()->flash('session_success', 'Agent Status Saved Successfully.');

            return redirect(route('agent-status'));
        }

        $data['pagetitle'] = 'Agent Status';
        $data['plugincss'] = [];
        $data['css'] = [];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js'];
        $data['js'] = ['agent.js'];
        $data['funinit'] = ['Agent.initAgentStatusAdd()'];
        $data['activateValue'] = 'Admin';
        $data['header'] = [
            'title' => 'Add Agent Status',
            'breadcrumb' => [
                'Home' => route('user_dashboard'),
                'Setup' => '',
                'Agent Status' => route('agent-status'),
                'Add Agent Status' => '',
            ]];
        $data['mainmenu'] = 'organisation_setup';

        return view('frontend.setup.agent-status-add', $data);
    }

    public function agentStatusEdit(Request $request)
    {

        $agentStatusId = $request->id;
        $college_id = Auth::user()->college_id;
        $dataAgentStatus = AgentStatus::find($agentStatusId);

        if ($request->isMethod('post')) {

            $regex = "/^(?=.+)(?:[1-9]\d*|0)?(?:\.\d+)?$/";

            $validator = Validator::make($request->all(), [
                'status_type' => 'required|unique:rto_agent_status,status_type,'.$agentStatusId.',id,college_id,'.$college_id,
                'select_duration' => ['required', 'regex:'.$regex],
            ]);

            if ($validator->fails()) {

                return redirect(route('edit-agent-status', ['id' => $agentStatusId]))
                    ->withErrors($validator)
                    ->withInput();
            }

            $objAgentStatus = new AgentStatus;
            $objAgentStatus->agentStatusEdit($college_id, $request, $agentStatusId);
            $request->session()->flash('session_success', 'Agent Status Updated Successfully.');

            return redirect(route('agent-status'));
        }

        $data['pagetitle'] = 'Agent Status';
        $data['plugincss'] = [];
        $data['css'] = [];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js'];
        $data['js'] = ['agent.js'];
        $data['funinit'] = ['Agent.initAgentStatusAdd()'];
        $data['activateValue'] = 'Admin';
        $data['header'] = [
            'title' => 'Edit Agent Status',
            'breadcrumb' => [
                'Home' => route('user_dashboard'),
                'Setup' => '',
                'Agent Status' => route('agent-status'),
                'Edit Agent Status' => '',
            ]];
        $data['id'] = $agentStatusId;
        $data['dataAgentStatus'] = $dataAgentStatus;
        $data['mainmenu'] = 'organisation_setup';

        if ($dataAgentStatus->college_id == $college_id) {
            return view('frontend.setup.agent-status-add', $data);
        } else {
            return view('frontend.error_page.unauthorized', $data);
        }
    }

    public function agentStatus(Request $request)
    {

        $perPage = Config::get('constants.pagination.perPage');
        $college_id = Auth::user()->college_id;

        $objAgentStatus = new AgentStatus;
        $agentStatsData = $objAgentStatus->getAgentStatusList($college_id, $perPage);

        $data['pagetitle'] = 'Agent Status';
        $data['plugincss'] = [];
        $data['css'] = [];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js'];
        $data['js'] = ['agent.js'];
        $data['funinit'] = ['Agent.initAgentStatus()'];
        $data['activateValue'] = 'Admin';
        $data['header'] = [
            'title' => 'Agent Status',
            'breadcrumb' => [
                'Home' => route('user_dashboard'),
                'Setup' => '',
                'Agent Status' => '',
            ]];
        $data['agentStatsData'] = $agentStatsData;
        $data['mainmenu'] = 'organisation_setup';

        return view('frontend.setup.agent-status', $data);
    }

    public function agentStatusDelete(Request $request, $agentStatusId)
    {

        $collegeId = Auth::user()->college_id;

        $objRtoAgent = new Agent;
        $count = $objRtoAgent->checkAgentInfoExist($collegeId, $agentStatusId);

        if ($count > 0) {
            $request->session()->flash('session_error', 'This Agent Status already Used.');
        } else {
            $objAgentStatus = new AgentStatus;
            $agentStatusDatas = $objAgentStatus->getAgentStatuslistCollegeIdWise($agentStatusId);

            $deleteCollegeId = $agentStatusDatas[0]->college_id;
            if ($deleteCollegeId == $collegeId) {
                $objAgentStatus->getAgentStatusDelete($agentStatusId);
                $request->session()->flash('session_success', 'This Agent Status is deleted.');

                return redirect(route('agent-status'));
            }
        }

        return redirect(route('agent-status'));
    }

    public function ajaxAction(Request $request)
    {
        $action = $request->input('action');
        $collegeId = Auth::user()->college_id;
        switch ($action) {
            case 'deleteAgent':
                $imageId = $request->input('data.id');
                $agentId = $request->input('data.agentId');
                $this->_deleteAgentImage($imageId, $agentId);
                break;
            case 'getFilterAgentEmail':
                $arrAccountManager = $request->input('data.arrAccountManager');
                $countryRecord = $request->input('data.countryRecord');
                $agentStatus = $request->input('data.agentStatus');
                $this->_filterAgentEmail($arrAccountManager, $countryRecord, $agentStatus);
                break;
            case 'searchAgentpaymentData':
                $filter_by = $request->input('data.filter_by');
                $search_str = $request->input('data.search_str');
                $agent_status = $request->input('data.agent_status');
                $this->_filterAgentPaymentList($filter_by, $search_str, $agent_status);
                break;
            case 'getExpiryDate':
                $statusId = $request->input('data.statusId');
                $startDate = $request->input('data.startDate');
                $this->_getExpiryDate($statusId, $startDate);
                break;
            case 'getFilterAgentList':
                $filterBy = $request->input('data.filterBy');
                $searchString = $request->input('data.searchString');
                $agentStatus = $request->input('data.agentStatus');
                $this->_getFilterAgentList($filterBy, $searchString, $agentStatus);
                break;
            case 'getAgentCode':
                $agentId = $request->input('data.id');
                $this->_getAgentCode($agentId);
                break;
            case 'agentStatusChange':
                $agentId = $request->input('data.id');
                $agentStatus = $request->input('data.status');
                $this->_changeAgentStatus($agentId, $agentStatus);
                break;
            case 'agentStatusGetData':
                $agentId = $request->input('data.agentId');
                $this->_getAgentStatusList($agentId);
                break;
            case 'deleteStatus':
                $agentId = $request->input('data.agentId');
                $this->_deleteAgentStatus($agentId);
                break;
            case 'checkUserCreate':
                $agentId = $request->input('data.agentId');
                $this->_checkUserCreate($agentId);
                break;
            case 'getAgentPaymentData':
                $objAgentStatus = new AgentStatus;
                $arrAgentStatus = $objAgentStatus->getAgentStatusListArr($collegeId);

                $objgetAgentRecords = new Agent;
                $users = $objgetAgentRecords->getAgentPaymentList($collegeId, $request, $arrAgentStatus);
                echo json_encode($users);
                exit;
                break;
            case 'getAgentList':
                $isZohoConnect = $this->isZohoConnected();
                $objAgent = new Agent;
                $agentList = $objAgent->getAgentDatatableList($collegeId, $request, $isZohoConnect);
                echo json_encode($agentList);
                exit;
                break;
            case 'agent-export':
                $agentIds = $request->input('data.agentIds');

                return $this->_agentDataExport($agentIds);
                break;
            case 'syncFromZoho':
                return $this->_syncAgentFromZoho();
                break;
            case 'syncToZoho':
                $agentIds = $request->input('data.agentIds');
                $this->_syncAgentToZoho($agentIds);
                break;
            case 'syncSingleAgentFromZoho':
                $agentId = $request->input('data.agentId');

                return $this->_syncSingleAgentFromZoho($agentId);
                break;
            case 'syncSingleAgentToZoho':
                $agentId = $request->input('data.agentId');

                return $this->_syncSingleAgentToZoho($agentId);
                break;
        }
        exit;
    }

    public function _getAgentCode($agentId)
    {
        $objAgentCode = new Agent;
        $arrEditData = $objAgentCode->getAgentCodeById($agentId);
        echo json_encode($arrEditData);
        exit;
    }

    public function _deleteAgentImage($imageId, $agentId)
    {
        $objRtoImages = new AgentImages;
        $agentGetImage = $objRtoImages->getImageListDelete($agentId, $imageId);
        echo json_encode($agentGetImage);
        exit;
    }

    public function _changeAgentStatus($agentId, $agentStatus)
    {
        $request = '';
        $objRtoAgent = new Agent;
        $agentStatus = $objRtoAgent->editAgentStatus($agentId, $agentStatus);
        if ($agentStatus == 'true') {
            $resultArr = ['type' => 'alert-success', 'message' => 'Agents status change  Successfully...'];
        } else {
            $resultArr = ['type' => 'alert-danger', 'message' => 'Something will be Wrong. Please Try again.'];
        }
        echo json_encode($resultArr);
        exit;
    }

    public function _filterAgentEmail($arrAccountManager, $countryRecord, $agentStatus)
    {
        $agentStatusConstant = Config::get('constants.arrAgentStatus');
        $objAgentEmail = new Agent;
        $arrAgentEmail = $objAgentEmail->filterAgentEmailData($arrAccountManager, $countryRecord, $agentStatus);
        echo json_encode($arrAgentEmail);
        exit;
    }

    /* Agent Payment */

    public function viewAgentPaymentList()
    {
        $perPage = Config::get('constants.pagination.perPage');
        $college_id = Auth::user()->college_id;

        $ovjviewAgentRecords = new Agent;
        $users = $ovjviewAgentRecords->viewAgentPaymentList($college_id, $perPage);
        $data['header'] = [
            'title' => 'Accounts Agent Payment',
            'breadcrumb' => [
                'Home' => route('user_dashboard'),
                'Accounts' => '',
                'Agent payment view' => '',
            ]];
        $objAgentStatus = new AgentStatus;
        $arrAgentStatus = $objAgentStatus->getAgentStatusListArr($college_id);
        $data['pagetitle'] = 'View Agent Payment List';
        $data['plugincss'] = [];
        $data['css'] = [];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js'];
        $data['js'] = ['agent.js'];
        $data['funinit'] = ['Agent.initSearchAgent()'];
        $data['activateValue'] = 'Accounts';
        $data['pagination'] = $users->links();
        $data['users'] = $users;
        $data['arrStatus'] = $arrAgentStatus;
        $data['mainmenu'] = 'partner_agent';

        return view('frontend.agent.view-agent-payment-list', $data);
    }

    public function _filterAgentPaymentList($filter_by, $search_str, $agent_status)
    {

        $collegeId = Auth::user()->college_id;
        $objAgent = new Agent;

        $objAgentStatus = new AgentStatus;

        $data['users'] = $objAgent->filterAgentPaymentList($collegeId, $filter_by, $search_str, $agent_status);
        $data['arrStatus'] = $objAgentStatus->getAgentStatusListArr($collegeId);

        $resultList = view('frontend.agent.search-agent-payment-list', $data)->render();

        $result = ['filterData' => $resultList, 'count' => count($data['users'])];
        echo json_encode($result);
        exit;
        // echo $resultList;exit;
    }

    public function _getExpiryDate($statusId, $startDate)
    {

        $collegeId = Auth::user()->college_id;

        $objAgentStatus = new AgentStatus;
        $expiryDate = $objAgentStatus->getExpiryDateCount($collegeId, $statusId, $startDate);
        echo $expiryDate;
        exit;
    }

    public function _getFilterAgentList($filterBy, $searchString, $agentStatus)
    {
        $collegeId = Auth::user()->college_id;

        $objAgent = new Agent;
        $agentList = $objAgent->getFilterAgentListArr($collegeId, $filterBy, $searchString, $agentStatus);

        echo json_encode($agentList);
        exit;
    }

    public function createAgentUser(Request $request)
    {
        $collegeId = Auth::user()->college_id;
        $searchFieldArr = [
            'allAgent' => 'All Agent',
            'id' => 'Agent ID',
            'agency_name' => 'Agent Name',
            'status' => 'Status',
            'primary_email' => 'Primary Email',
        ];
        $agentStatusArr = (new AgentStatus)->getAgentStatusListArr($collegeId);

        if ($request->isMethod('post')) {

            $validator = Validator::make($request->all(), [
                'password' => 'required|min:8|max:16',
                // 'agent_email' => 'required|unique:rto_users,email'
            ]);
            if ($validator->fails()) {
                if (empty($request->input('password'))) {
                    $request->session()->flash('session_error', 'Please enter password');
                } else {
                    $request->session()->flash('session_error', 'The new password must be between 8 and 16 characters');
                }

                return redirect(route('view-agent-list'))->withErrors($validator)->withInput();
            }

            $objRtoAgent = new Agent;
            $agenEditData = $objRtoAgent->getAgentDetail($request->input('agentId'));
            $objUsers = new Users;

            if ($agenEditData['user_id'] == '') {

                $arrUserData = $objUsers->duplicateUsersEmailCheck($request->input('agent_email'));

                if (count($arrUserData) == 0) {

                    $arrUsers = $objUsers->addUserFromAgent($request, $agenEditData);
                    $agenEditData = $objRtoAgent->editAgentUserId($request->input('agentId'), $arrUsers);

                    $objUserRoleType = new UserRoleType;
                    $arrUserRoleType = $objUserRoleType->saveStudentRoleType(14, $arrUsers);
                    $request->session()->flash('session_success', 'Agent User Create Successfully.');
                } else {
                    $request->session()->flash('session_error', 'Email already exist');
                }
            } else {
                $arrUsers = $objUsers->editUserFromAgent($request, $agenEditData);
                $request->session()->flash('session_success', 'Agent User Update Successfully.');
            }

            return redirect(route('view-agent-list'));
        }

        $data['pagetitle'] = 'View Agent List';
        $data['plugincss'] = [];
        $data['css'] = [];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js'];
        $data['js'] = ['agent.js'];
        $data['funinit'] = ['Agent.initViewAgent()'];
        $data['activateValue'] = 'Marketing';

        $data['searchFieldArr'] = $searchFieldArr;
        $data['agentStatusArr'] = $agentStatusArr;
        $data['mainmenu'] = 'partner_agent';

        return view('frontend.agent.view-agent-list', $data);
    }

    public function _deleteAgentStatus($agentId)
    {
        $objRtoStatus = new AgentProfileStatus;
        $agentGetImage = $objRtoStatus->deleteAgencyStatus($agentId);
        echo json_encode($agentGetImage);
        exit;
    }

    public function _checkUserCreate($agentId)
    {
        $arrUsers = [];
        $objRtoAgent = new Agent;
        $agenDataGet = $objRtoAgent->getAgentDetail($agentId);
        if ($agenDataGet['user_id'] != '') {
            $objUsers = new Users;
            $arrUsers = $objUsers->getUserDetails($agenDataGet['user_id']);
        }
        $arrayData[0]['primary_email'] = $agenDataGet['primary_email'];
        $arrayData[0]['user_id'] = $agenDataGet['user_id'];
        $arrayData[1] = $arrUsers;
        echo json_encode($arrayData);
        exit;
    }

    public function _agentDataExport($agentIds = '')
    {
        ob_end_clean();

        return Excel::download(new AgencyExport($agentIds), 'Agents.xlsx');
    }

    public function _syncAgentFromZoho()
    {
        if (! $this->isZohoConnected()) {
            echo json_encode(['status' => 'error', 'message' => 'Zoho not connected']);
            exit;
        }

        // dispatch_sync(new SyncAgentFromZoho());
        $jobs[] = new SyncAgentFromZoho;
        Bus::chain($jobs)->dispatch();
        $resArr = ['status' => 'success', 'message' => 'Agents syncing from Zoho'];

        echo json_encode($resArr);
        exit;
    }

    public function _syncAgentToZoho($agentIds)
    {
        if (! $this->isZohoConnected()) {
            echo json_encode(['status' => 'error', 'message' => 'Zoho not connected']);
            exit;
        }

        if (count($agentIds) == 0) {
            echo json_encode(['status' => 'error', 'message' => 'Please select at least one record']);
            exit;
        }

        $agents = \App\Model\v2\Agent::with('integrationItem')->whereIn('id', $agentIds)->get();

        $sync = $failSync = $alreadySync = $invalid = 0;
        $agentDetails = [];

        foreach ($agents as $agent) {
            $agentInfo = [
                'id' => $agent->id,
                'name' => $agent->agency_name,
                'sync_datetime' => now()->format('d M Y H:i:s'),
                'status' => '',
                'fail_reason' => '',
            ];

            if ($agent->integrationItem && ! empty($agent->integrationItem->sync_item_id)) {
                $alreadySync++;
                $agentInfo['status'] = 'Already Synced';
                $agentInfo['fail_reason'] = 'Agent already exists in Zoho';
                // Update sync datetime with actual sync time if available
                if ($agent->integrationItem->synced_at) {
                    $agentInfo['sync_datetime'] = $agent->integrationItem->synced_at->format('d M Y H:i:s');
                }
            } else {
                try {
                    $result = $agent->asZohoItem(true);

                    \Log::info('Zoho sync result for agent '.$agent->id, [
                        'result' => $result ? 'success' : 'null',
                        'sync_item_id' => $result ? $result->sync_item_id : 'null',
                    ]);

                    if ($result && $result->sync_item_id) {
                        $sync++;
                        $agentInfo['status'] = 'Success';
                        // Update sync datetime with actual sync time
                        if ($result->synced_at) {
                            $agentInfo['sync_datetime'] = $result->synced_at->format('d M Y H:i:s');
                        }
                    } else {
                        $failSync++;
                        $agentInfo['status'] = 'Failed';
                        $agentInfo['fail_reason'] = 'Sync completed but no sync ID returned';
                    }
                } catch (\Exception $e) {
                    $failSync++;
                    $agentInfo['status'] = 'Failed';
                    $agentInfo['fail_reason'] = $e->getMessage();
                    \Log::error('Zoho sync failed for agent '.$agent->id, [
                        'error' => $e->getMessage(),
                    ]);
                }
            }

            $agentDetails[] = $agentInfo;
        }

        $invalid = count(array_diff($agentIds, $agents->pluck('id')->toArray()));

        $error_msg = [];
        if ($alreadySync > 0) {
            $error_msg[] = "Total $alreadySync selected Agents already synced.";
        } elseif ($failSync > 0) {
            $error_msg[] = "Total $failSync selected Agents failed to sync.";
        } elseif ($invalid > 0) {
            $error_msg[] = "Total $invalid selected Agents could not be found.";
        }

        echo json_encode([
            'success_count' => $sync,
            'success_msg' => ($sync > 0) ? "Total $sync selected Agents synced successfully." : '',
            'error_count' => $alreadySync + $failSync + $invalid,
            'error_msg' => ($sync === count($agentIds)) ? '' : implode(' ', $error_msg),
            'show_popup' => true,
            'agent_details' => $agentDetails,
        ]);
        exit;

        // echo json_encode(['status' => 'error', 'action' => 'alert-danger', 'message' => 'Under process']);exit;

        /*$sync = $fail = $alreadySync = 0;
        $jobs = [];
        foreach ($agentIds as $id) {
            //TODO:: Check already sync or not
            if ($id > 0) {
                //$jobs[] = new SyncAgentToZoho($id);
                try {
                    $res = Agent::find($id)->asZohoItem(true);
                    if($res){
                        $sync++;
                    }
                } catch (\Exception $e) {
                    $fail++;
                }
            } else {
                $alreadySync++;
            }
        }

        if (count($jobs)) {
            //Bus::chain($jobs)->dispatch();
        }

        if ($sync > 0) {
            $resultArr = ['status' => 'success', 'message' => 'Selected agent synced successfully'];
        } elseif ($alreadySync > 0) {
            $resultArr = ['status' => 'error', 'message' => 'Selected agent already synced'];
        } else {
            $resultArr = ['status' => 'error', 'message' => 'Something will be Wrong. Please Try again.'];
        }

        echo json_encode($resultArr);
        exit;*/
    }

    public function _syncSingleAgentFromZoho($agentId)
    {
        if (! $this->isZohoConnected()) {
            echo json_encode(['status' => 'error', 'message' => 'Zoho not connected']);
            exit;
        }

        if (! $agentId) {
            echo json_encode(['status' => 'error', 'message' => 'Agent ID is required']);
            exit;
        }

        try {
            $agent = \App\Model\v2\Agent::find($agentId);
            if (! $agent) {
                echo json_encode(['status' => 'error', 'message' => 'Agent not found']);
                exit;
            }

            // Check if agent exists in Zoho by looking for integration record
            $integrationItem = $agent->integrationItem;
            if (! $integrationItem || empty($integrationItem->sync_item_id)) {
                echo json_encode(['status' => 'error', 'message' => 'Sync to Zoho first. Agent not found in Zoho.']);
                exit;
            }

            // Fetch agent data from Zoho using the sync_item_id
            $config = \Integrations\Zoho\Facades\Zoho::config();
            $mapped = @$config->metadata['mapped_fields']['agents'] ?? [];
            $moduleName = @$config->metadata['mapped_modules']['agents'];

            if (! $moduleName) {
                echo json_encode(['status' => 'error', 'message' => 'Zoho module not configured for agents']);
                exit;
            }

            // Fetch the agent record from Zoho
            $zohoAgentData = \Integrations\Zoho\Facades\Zoho::dynamicModules()
                ->setResource($moduleName)
                ->find($integrationItem->sync_item_id);

            if (! $zohoAgentData || empty($zohoAgentData)) {
                echo json_encode(['status' => 'error', 'message' => 'Agent not found in Zoho or has been deleted']);
                exit;
            }

            // The find method returns an array, so we need to get the first element if it's an array
            if (is_array($zohoAgentData) && isset($zohoAgentData[0])) {
                $zohoAgentData = $zohoAgentData[0];
            }

            // Convert Zoho data to payload format
            $payload = new \Integrations\Zoho\Entities\RecruitmentAgent;
            $payload = $payload->toPayloadArrayFromZoho($zohoAgentData);

            // Update local agent record with Zoho data
            $updatedAgent = \App\Model\v2\Agent::CreateFromZohoPayload($payload, $agent->college_id);

            Log::info('Agent details synced from Zoho', [
                'agent_id' => $agentId,
                'sync_item_id' => $integrationItem->sync_item_id,
                'updated_fields' => [
                    'name' => $payload->name,
                    'email' => $payload->email,
                    'phone' => $payload->phone,
                    'status' => $payload->status,
                    'agent_code' => $payload->agent_code,
                ],
            ]);

            echo json_encode(['status' => 'success', 'message' => 'Agent details synced from Zoho successfully']);
            exit;

        } catch (\Exception $e) {
            \Log::error('Failed to sync agent details from Zoho', [
                'agent_id' => $agentId,
                'error' => $e->getMessage(),
            ]);

            echo json_encode(['status' => 'error', 'message' => 'Failed to sync: '.$e->getMessage()]);
            exit;
        }
    }

    public function _syncSingleAgentToZoho($agentId)
    {
        if (! $this->isZohoConnected()) {
            echo json_encode(['status' => 'error', 'message' => 'Zoho not connected']);
            exit;
        }

        if (! $agentId) {
            echo json_encode(['status' => 'error', 'message' => 'Agent ID is required']);
            exit;
        }

        try {
            $agent = \App\Model\v2\Agent::with('integrationItem')->find($agentId);
            if (! $agent) {
                echo json_encode(['status' => 'error', 'message' => 'Agent not found']);
                exit;
            }

            // Check if already synced
            if ($agent->integrationItem && ! empty($agent->integrationItem->sync_item_id)) {
                echo json_encode([
                    'status' => 'error',
                    'message' => 'Agent already synced to Zoho on '.$agent->integrationItem->synced_at->format('d M Y'),
                ]);
                exit;
            }

            // Sync to Zoho
            $result = $agent->asZohoItem(true);

            if ($result && $result->sync_item_id) {
                \Log::info('Single agent synced to Zoho successfully', [
                    'agent_id' => $agentId,
                    'sync_item_id' => $result->sync_item_id,
                ]);

                echo json_encode(['status' => 'success', 'message' => 'Agent synced to Zoho successfully']);
            } else {
                echo json_encode(['status' => 'error', 'message' => 'Sync completed but no sync ID returned']);
            }
            exit;

        } catch (\Exception $e) {
            \Log::error('Failed to sync single agent to Zoho', [
                'agent_id' => $agentId,
                'error' => $e->getMessage(),
            ]);

            echo json_encode(['status' => 'error', 'message' => 'Sync failed: '.$e->getMessage()]);
            exit;
        }
    }
}
