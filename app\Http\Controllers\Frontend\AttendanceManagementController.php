<?php

namespace App\Http\Controllers\Frontend;

use App\Helpers\Helpers;
use App\Http\Controllers\Controller;
use App\Model\CollegeCampus;
use App\Model\CollegeDetails;
use App\Model\Courses;
use App\Model\CourseSubject;
use App\Model\CourseType;
use App\Model\EmailTemplate;
use App\Model\Holiday;
use App\Model\Semester;
use App\Model\SemesterDivision;
use App\Model\SendMail;
use App\Model\StudentAttendance;
use App\Model\StudentCourse;
use App\Model\Students;
use App\Model\StudentSubjectEnrolment;
use App\Model\Timetable;
use App\Model\TimetableBreaktime;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\App;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Maatwebsite\Excel\Facades\Excel;

class AttendanceManagementController extends Controller
{
    public function __construct()
    {
        // $this->rememberToken
    }

    public function classAttendance(request $request)
    {

        $collegeId = Auth::user()->college_id;
        $sessionPermission = $request->session()->get('arrPermissionList', 'default');
        $sessionPermissionData = $sessionPermission['deny_attendance_entry'];

        $objRtoCourseType = new CourseType;
        $arrCourseType = $objRtoCourseType->getCourseTypeV2($collegeId);
        reset($arrCourseType);
        $firstCourseTypeKey = key($arrCourseType);

        $objSemester = new Semester;
        $arrSemester = $objSemester->_getSemesterNamev2($collegeId, $firstCourseTypeKey);
        reset($arrSemester);
        $firstSemester = key($arrSemester);

        $arrSemesterDivision = ['' => 'No Term Found'];
        $arrClass = ['' => 'No Class Found'];
        $arrClassTimeTable = [];

        if (! empty($firstSemester)) {
            $arrSemesterDivision = $this->_getSemesterDivision($firstSemester);

            if (count($arrSemesterDivision) > 0) {
                $firstDay = date('l');
                $firstSemesterDivision = key($arrSemesterDivision);
                $arrSemData = explode(':', $firstSemesterDivision);
                $term = $arrSemData[0];
                $weekStart = $arrSemData[1];
                $weekFinish = $arrSemData[2];
                $weekSlot = $arrSemData[3];
                $arrClass = $this->_getClass($firstCourseTypeKey, $firstSemester, $term, $weekStart, $weekFinish, $firstDay, $weekSlot);
                $objTimetable = new Timetable;
                $arrClassTimeTable = $objTimetable->getClassListBySemesterWeekDay($collegeId, $firstCourseTypeKey, $firstSemester, $term, $weekStart, $weekFinish, $firstDay, $weekSlot);
            }
        }
        // echo $firstSemester;exit;

        $arrDays = Config::get('constants.arrDays');
        // $firstDay = key($arrDays);
        $arrAttendanceType = Config::get('constants.arrAttendanceType');
        $data['header'] = [
            'title' => 'Add Class Attendance (Daily/Weekly)',
            'breadcrumb' => [
                'Home' => route('user_dashboard'),
                'Compliance' => '',
                'Add Class Attendance' => '',
            ]];
        $data['arrCourseType'] = $arrCourseType;
        $data['arrSemester'] = $arrSemester;
        $data['arrSemesterDivision'] = $arrSemesterDivision;
        $data['arrDays'] = $arrDays;
        $data['arrClass'] = $arrClass;
        $data['arrClassTimeTable'] = $arrClassTimeTable;
        $data['arrAttendanceType'] = $arrAttendanceType;
        $data['sessionPermissionData'] = $sessionPermissionData;
        $data['pagetitle'] = 'Add Class Attendance';
        $data['plugincss'] = [];
        $data['css'] = [];
        $data['activateValue'] = 'Compliance';
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js'];
        $data['js'] = ['attendanceManagement.js'];
        $data['funinit'] = ['AttendanceManagement.initAddClassAttendance()'];
        $data['mainmenu'] = 'clients';

        // $data['css'] = array('frontendcomman.css');
        return view('frontend.attendance_management.add-class-attendance', $data);
    }

    public function attendanceSummary(Request $request)
    {
        $perPage = Config::get('constants.pagination.perPage');

        $collegeId = Auth::user()->college_id;
        $objRtoStudentsLists = new Students;
        $objStudentsLists = $objRtoStudentsLists->getStudentListForAttendance($perPage, $collegeId);
        // $objRtoCollegeCampus = new CollegeCampus();
        // $arrCampus = $objRtoCollegeCampus->getCollegeCampusList($collegeId);

        if ($request->isMethod('get')) {
            $campusID = $request->input('campus_name');
            $searchBy = $request->input('search_by');
            $searchString = $request->input('search_string');

            if ($campusID != '' && $searchBy != '' && $searchString != '') {
                $findRtoStudentId = new StudentCourse;
                $findStudentId = $findRtoStudentId->searchStudentsWithCampus($campusID);
                $studentId = [];
                for ($i = 0; $i < count($findStudentId); $i++) {
                    $studentId[] = $findStudentId[$i]['student_id'];
                }
                $objRtoStudentsLists = new Students;
                $objStudentsLists = $objRtoStudentsLists->searchStudentsForAttendance($searchBy, $searchString, $perPage, $studentId, $collegeId);
            } elseif ($campusID == '' && $searchBy != '' && $searchString != '') {
                $studentId = '';
                $objRtoStudentsLists = new Students;
                $objStudentsLists = $objRtoStudentsLists->searchStudentsForAttendance($searchBy, $searchString, $perPage, $studentId, $collegeId);
            } else {
                $objStudentsLists = $objRtoStudentsLists->getStudentListForAttendance($perPage, $collegeId);
                // echo "no record found";
            }
        }

        $data['header'] = [
            'title' => 'Attendance Management/Attendance Summary',
            'breadcrumb' => [
                'Home' => route('user_dashboard'),
                'Compliance' => '',
                'Attendance Summary' => '',
            ]];
        $data['objStudentsLists'] = $objStudentsLists;
        // $data['arrCampus'] = $arrCampus;
        $data['pagetitle'] = 'Attendance Summary';
        $data['activateValue'] = 'Compliance';
        $data['plugincss'] = [];
        $data['css'] = ['frontendcomman.css'];
        $data['mainmenu'] = 'clients';

        return view('frontend.attendance_management.search-students', $data);
    }

    public function studentAttendanceSummary($studentId, Request $request)
    {
        $studentId = safeDecryptIt($studentId);

        $sessionPermission = $request->session()->get('arrPermissionList', 'default');

        $sessionPermissionData = (isset($sessionPermission['activate/deactivate_staff/teacher_comm_log'])) ? $sessionPermission['activate/deactivate_staff/teacher_comm_log'] : 'yes';

        $collegeId = Auth::user()->college_id;
        $objStudent = new Students;
        $studentProfile = $objStudent->getStudentDetail($studentId)[0];
        $objRtoStudentCourse = new StudentCourse;
        $arrStudentCourse = $objRtoStudentCourse->getStudentAppliedCourseNameV2($studentId);

        reset($arrStudentCourse);
        $firstCourseId = key($arrStudentCourse);
        $objStudentCourse = new StudentCourse;
        $arrStudentCourseDetails = $objStudentCourse->getStudentCoursesInformation($studentId, $firstCourseId);
        $arrSemester = $this->_getSemester($firstCourseId, $arrStudentCourseDetails[0]['course_type_id'], $studentId);
        //        print_r($arrSemester);exit;
        reset($arrSemester);
        $firstSemesterId = key($arrSemester);
        $arrTerm = $this->_getTermBySemester($firstSemesterId);
        reset($arrTerm);
        $term = key($arrTerm);
        $arrAttendanceType = ['full' => 'View Full Attendance', 'subject' => 'View Attendance By Subject', 'timetable' => 'View Attendance By Class Timetable', 'total' => 'View Total Attendance Percentage'];
        $objStudentAttendance = new StudentAttendance;
        $arrStudentAttendance = $objStudentAttendance->getStudentAttendance($collegeId, $studentId, $firstSemesterId, $term);
        $data['header'] = [
            'title' => 'Student Attendance Summary',
            'breadcrumb' => [
                'Home' => route('user_dashboard'),
                'Student Attendance Summary' => '',
            ]];
        $data['pagetitle'] = 'Student Attendance History';
        $data['activateValue'] = 'Compliance';
        $data['arrAttendanceType'] = $arrAttendanceType;
        $data['arrStudentCourse'] = $arrStudentCourse;
        $data['studentProfile'] = $studentProfile;
        $data['arrStudentCourseDetails'] = $arrStudentCourseDetails[0];
        $data['arrStudentAttendance'] = $arrStudentAttendance;
        $data['arrSemester'] = $arrSemester;
        $data['arrTerm'] = $arrTerm;
        $data['plugincss'] = [];
        $data['css'] = ['frontendcomman.css'];
        $data['js'] = ['attendanceManagement.js'];
        $data['funinit'] = ['AttendanceManagement.initStudentAttendanceSummary()'];
        $data['mainmenu'] = 'clients';

        return view('frontend.attendance_management.student-attendance-summary', $data);
    }

    public function bulkAttendanceWeekly()
    {
        $data = [];
        $collegeId = Auth::user()->college_id;

        $objRtoCourseType = new CourseType;
        $arrCourseType = $objRtoCourseType->getCourseTypeV2($collegeId);
        reset($arrCourseType);
        $arrAscendingDescending = Config::get('constants.arrAscendingDescending');
        $orderBy['student_id'] = 'Student Id';
        $orderBy['lastName'] = 'Student Last Name';
        $firstCourseTypeKey = key($arrCourseType);
        $courseStatus = '1';
        $arrCourse = $this->_getCourseByCourseType($firstCourseTypeKey, $courseStatus);
        reset($arrCourse);
        $firstCourse = key($arrCourse);
        $arrYear = $this->_getCourseYear($firstCourse);
        reset($arrYear);
        $firstYear = key($arrYear);
        $arrSemester = $this->_getSemesterByCourseYear($firstCourse, $firstYear, $firstCourseTypeKey);
        reset($arrSemester);
        $firstSemesterId = key($arrSemester);
        $arrTerm = $this->_getTermBySemester($firstSemesterId);
        reset($arrTerm);
        $term = key($arrTerm);
        $arrWeek = $this->_getDivisionBySemesterTerm($firstSemesterId, $term);
        reset($arrWeek);
        $firstWeek = key($arrWeek);

        $objStudentSubjectEnrollment = new StudentSubjectEnrolment;
        $arrStudentList = $objStudentSubjectEnrollment->getStudentListByCourse($collegeId, $firstCourse, $firstSemesterId, $term, $firstWeek);

        $objStudentAttendance = new StudentAttendance;
        $arrStudentAttendance = $objStudentAttendance->getWeeklyAttendanceByCourse($collegeId, $firstSemesterId, $term, $firstCourse, $firstWeek);

        $data['header'] = [
            'title' => 'Bulk Add/Update Student Attendance Weekly',
            'breadcrumb' => [
                'Home' => route('user_dashboard'),
                'Compliance' => '',
                'Bulk Attendance' => '',
            ]];
        $data['arrCourseType'] = $arrCourseType;
        $data['arrCourse'] = $arrCourse;
        $data['arrYear'] = $arrYear;
        $data['arrSemester'] = $arrSemester;
        $data['arrTerm'] = $arrTerm;
        $data['arrWeek'] = $arrWeek;
        $data['arrStudentAttendance'] = $arrStudentAttendance;
        $data['arrStudentList'] = $arrStudentList;
        $data['arrAscendingDescending'] = $arrAscendingDescending;
        $data['orderBy'] = $orderBy;
        $data['pagetitle'] = 'Bulk Attendance';
        $data['plugincss'] = [];
        $data['css'] = [];
        $data['activateValue'] = 'Compliance';
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js'];
        $data['js'] = ['attendanceManagement.js'];
        $data['funinit'] = ['AttendanceManagement.initBulkAttendanceWeekly()'];
        $data['mainmenu'] = 'clients';

        return view('frontend.attendance_management.bulk-attendance-weekly', $data);
    }

    public function bulkAttendanceSubject()
    {
        $data = [];
        $collegeId = Auth::user()->college_id;
        $objRtoCourseType = new CourseType;
        $arrCourseType = $objRtoCourseType->getCourseTypeV2($collegeId);
        reset($arrCourseType);
        $firstCourseTypeKey = key($arrCourseType);
        $courseStatus = '1';
        $arrCourse = $this->_getCourseByCourseType($firstCourseTypeKey, $courseStatus);
        reset($arrCourse);
        $firstCourse = key($arrCourse);
        $arrSubject = $this->_getSubjectsByCourse($firstCourse);
        $arrYear = $this->_getCourseYear($firstCourse);
        reset($arrYear);
        $firstYear = key($arrYear);
        $arrSemester = $this->_getSemesterByCourseYear($firstCourse, $firstYear, $firstCourseTypeKey);
        reset($arrSemester);
        $firstSemesterId = key($arrSemester);
        $arrTerm = $this->_getTermBySemester($firstSemesterId);
        reset($arrTerm);
        $term = key($arrTerm);
        $data['header'] = [
            'title' => 'Bulk Add/Update Student Attendance Subject',
            'breadcrumb' => [
                'Home' => route('user_dashboard'),
                'Compliance' => '',
                'Bulk Attendance Subject' => '',
            ]];
        $data['arrCourseType'] = $arrCourseType;
        $data['arrCourse'] = $arrCourse;
        $data['arrYear'] = $arrYear;
        $data['arrSemester'] = $arrSemester;
        $data['arrTerm'] = $arrTerm;
        $data['arrSubject'] = $arrSubject;
        $data['pagetitle'] = 'Bulk Attendance';
        $data['plugincss'] = [];
        $data['css'] = [];
        $data['activateValue'] = 'Compliance';
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js'];
        $data['js'] = ['attendanceManagement.js'];
        $data['funinit'] = ['AttendanceManagement.initBulkAttendanceSubject()'];
        $data['mainmenu'] = 'clients';

        return view('frontend.attendance_management.bulk-attendance-subject', $data);
    }

    public function reportAndWarnings()
    {
        $data = [];
        $collegeId = Auth::user()->college_id;

        $objRtoCourseType = new CourseType;
        $arrCourseType = $objRtoCourseType->getCourseTypeV2($collegeId);
        reset($arrCourseType);
        $arrAscendingDescending = Config::get('constants.arrAscendingDescending');
        $orderBy['student_id'] = 'Student Id';
        $orderBy['lastName'] = 'Student Last Name';
        $firstCourseTypeKey = key($arrCourseType);
        $courseStatus = '1';
        $arrCourse = $this->_getCourseByCourseType($firstCourseTypeKey, $courseStatus);
        reset($arrCourse);
        $firstCourse = key($arrCourse);
        $arrYear = $this->_getCourseYear($firstCourse);
        reset($arrYear);
        $firstYear = key($arrYear);
        $arrSemester = $this->_getSemesterByCourseYear($firstCourse, $firstYear, $firstCourseTypeKey);
        reset($arrSemester);
        $firstSemesterId = key($arrSemester);
        $arrTerm = $this->_getTermBySemester($firstSemesterId);
        reset($arrTerm);
        $term = key($arrTerm);
        $arrWeek = $this->_getDivisionBySemesterTerm($firstSemesterId, $term);
        reset($arrWeek);
        $firstWeek = key($arrWeek);

        $objStudentSubjectEnrollment = new StudentSubjectEnrolment;
        $arrStudentList = $objStudentSubjectEnrollment->getStudentListByCourse($collegeId, $firstCourse, $firstSemesterId, $term, $firstWeek);

        $objStudentAttendance = new StudentAttendance;
        $arrStudentAttendance = $objStudentAttendance->getWeeklyAttendanceByCourse($collegeId, $firstSemesterId, $term, $firstCourse, $firstWeek);
        $arrReportType = ['' => 'Please Select Report type', '1' => 'Bellow X%', '2' => 'X consecutive day'];

        $arrRecipient = Config::get('constants.arrRecipient');
        unset($arrRecipient['3']);
        unset($arrRecipient['4']);
        unset($arrRecipient['5']);
        unset($arrRecipient['6']);

        $objRtoEmailTemplate = new EmailTemplate;
        $template_type_id = (isset($studentWarningPercentage->template_type) == 0 ? 0 : $studentWarningPercentage->template_type);

        $arrEmailTemplateList = ['----Choose Email Templete----'];
        $emailTemplateList = $objRtoEmailTemplate->getEmailTemplate($template_type_id, $collegeId);
        foreach ($emailTemplateList as $list) {
            $arrEmailTemplateList[$list->id] = $list->template_name;
        }

        $data['arrEmailTemplateListPercentage'] = $arrEmailTemplateList;
        $data['arrRecipientPercentage'] = $arrRecipient;

        $data['header'] = [
            'title' => 'Bulk Add/Update Student Attendance Weekly',
            'breadcrumb' => [
                'Home' => route('user_dashboard'),
                'Compliance' => '',
                'Bulk Attendance' => '',
            ]];
        $data['arrCourseType'] = $arrCourseType;
        $data['arrCourse'] = $arrCourse;
        $data['arrYear'] = $arrYear;
        $data['arrSemester'] = $arrSemester;
        $data['arrTerm'] = $arrTerm;
        $data['arrWeek'] = $arrWeek;
        $data['arrStudentAttendance'] = $arrStudentAttendance;
        $data['arrStudentList'] = $arrStudentList;
        $data['arrAscendingDescending'] = $arrAscendingDescending;
        $data['arrReportType'] = $arrReportType;
        $data['orderBy'] = $orderBy;
        $data['pagetitle'] = 'Reports and Warnings';
        $data['plugincss'] = [];
        $data['css'] = [];
        $data['activateValue'] = 'Compliance';
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js'];
        $data['js'] = ['reportsAndWarnings.js'];
        $data['funinit'] = ['ReportsAndWarnings.initReportsAndWarnings()'];
        $data['mainmenu'] = 'clients';

        return view('frontend.attendance_management.report-and-warnings', $data);
    }

    //     public function exportExcelReportAndWarnings(Request $request) {
    //        $type = 'subject';
    //        return $this->generateExcelSheet($type, $request);
    //    }

    public function ajaxAction(Request $request)
    {
        $action = $request->input('action');
        $userId = Auth::user()->id;
        $collegeId = Auth::user()->college_id;
        switch ($action) {
            case 'getSemesterName':
                $courseTypeId = $request->input('data.courseTypeId');
                $this->_getSemesterName($courseTypeId);
                break;
            case 'getSemesterDivisions':
                $semesterId = $request->input('data.semesterId');
                $arrSemesterDivision = $this->_getSemesterDivision($semesterId);
                echo json_encode($arrSemesterDivision);
                break;
            case 'getStudentCourseDetails':
                $courseId = $request->input('data.courseId');
                $studentId = $request->input('data.studentId');
                $objStudentCourse = new StudentCourse;
                $arrStudentCourseDetails = $objStudentCourse->getStudentCoursesInformation($studentId, $courseId);
                $arrReturn = [];
                $arrReturn['status'] = $arrStudentCourseDetails[0]->status;
                $arrReturn['course_length'] = 'Course Length: '.date('d-m-Y', strtotime($arrStudentCourseDetails[0]->start_date)).' - '.date('d-m-Y', strtotime($arrStudentCourseDetails[0]->finish_date));
                echo json_encode($arrReturn);
                break;
            case 'getSemesters':
                $courseId = $request->input('data.courseId');
                $studentId = $request->input('data.studentId');
                $objStudentCourse = new Courses;
                $courseData = $objStudentCourse->getCourseslistCollegeIdWise($courseId);
                $arrSemester = $this->_getSemester($courseId, $courseData[0]['course_type_id'], $studentId);
                echo json_encode($arrSemester);
                break;
            case 'getTerm':
                $semesterId = $request->input('data.semesterId');
                $arrTerm = $this->_getTermBySemester($semesterId);
                echo json_encode($arrTerm);
                break;
            case 'getClass':
                $courseTypeId = $request->input('data.courseTypeId');
                $semesterId = $request->input('data.semesterId');
                $semesterDivisionId = $request->input('data.term');
                $day = $request->input('data.day');
                if ($semesterDivisionId != '0') {
                    $arrSemData = explode(':', $semesterDivisionId);
                    $term = $arrSemData[0];
                    $weekStart = $arrSemData[1];
                    $weekFinish = $arrSemData[2];
                    $slot = $arrSemData[3];
                    $arrClass = $this->_getClass($courseTypeId, $semesterId, $term, $weekStart, $weekFinish, $day, $slot);
                } else {
                    $arrClass = ['' => 'No Class Found'];
                }
                echo json_encode($arrClass);
                break;
            case 'getClassList':
                $collegeId = Auth::user()->college_id;
                $courseTypeId = $request->input('data.courseTypeId');
                $semesterId = $request->input('data.semesterId');
                $semesterDivisionId = $request->input('data.term');
                $day = $request->input('data.day');
                $html = '';
                $arrAttendanceType = Config::get('constants.arrAttendanceType');
                if ($semesterDivisionId != '0') {
                    $arrSemData = explode(':', $semesterDivisionId);
                    $term = $arrSemData[0];
                    $weekStart = $arrSemData[1];
                    $weekFinish = $arrSemData[2];
                    $slot = $arrSemData[3];
                    $objTimetable = new Timetable;
                    $tableData = $objTimetable->getClassListBySemesterWeekDay($collegeId, $courseTypeId, $semesterId, $term, $weekStart, $weekFinish, $day, $slot);
                    if (count($tableData) != 0) {
                        foreach ($tableData as $value) {
                            $html .= '<tr>';
                            $html .= '<td>'.$value->subject_code.'</td>';
                            $html .= '<td>'.$value->subject_name.'</td>';
                            $html .= '<td>'.$value->batch.'</td>';
                            $html .= '<td>'.$value->semester_name.'</td>';
                            $html .= '<td>'.$value->day.'</td>';
                            $html .= '<td>'.date('h:i A', strtotime($value->start_time)).'</td>';
                            $html .= '<td>'.date('h:i A', strtotime($value->finish_time)).'</td>';
                            $html .= '<td>'.$value->start_week_id.'</td>';
                            $html .= '<td>'.$value->end_week_id.'</td>';
                            $html .= '<td>'.$arrAttendanceType[$value->attendance_type].'</td>';
                            $html .= '</tr>';
                        }
                    } else {
                        $html .= '<tr class="norecords">';
                        $html .= '<td colspan="10"  style="text-align:center;"><p style="color:red;"> No Record Found</p></td>';
                        $html .= '</tr>';
                    }
                } else {
                    $html .= '<tr class="norecords">';
                    $html .= '<td colspan="10"  style="text-align:center;"><p style="color:red;">No Record Found</p></td>';
                    $html .= '</tr>';
                }
                echo $html;
                break;
            case 'getStudentTimetable':
                $collegeId = Auth::user()->college_id;
                $timetableId = $request->input('data.timetableId');
                $semesterDivisionId = $request->input('data.term');
                $day = $request->input('data.day');
                $subjectClassDetails = $request->input('data.subjectClassDetails');
                $arrAttendanceType = Config::get('constants.arrAttendanceType');
                $teacher = $this->get_string_between($subjectClassDetails, 'by ', ' Time');
                $batch = $this->get_string_between($subjectClassDetails, '(Group: ', ')');

                $subjectCode = strtok($subjectClassDetails, ':');
                $arrSemData = explode(':', $semesterDivisionId);
                $term = $arrSemData[0];
                $weekStart = $arrSemData[1];
                $weekFinish = $arrSemData[2];
                $slot = $arrSemData[3];
                $objTimetable = new Timetable;
                $arrDays = $objTimetable->getDays($weekStart, $weekFinish);
                $attendanceDate = $arrDays[$day];

                $getMatchTimeTableRecord = Timetable::where('id', '=', $timetableId)->get(['batch', 'attendance_type', 'start_time', 'finish_time', 'break_from', 'break_to', 'semester_id'])->first();

                $objStudentAttendance = new StudentAttendance;
                $arrAttendance = $objStudentAttendance->getStudentAttendanceByTimetable($timetableId, $attendanceDate);

                $arrStudents = $objTimetable->getStudentClassListById($timetableId, $attendanceDate, $day, $batch);

                $attendanceType = '';
                $arrTimeSlot = [];
                $arrTimeIntervalSlot = [];
                $arrTimeIntervalSlotFrom = [];
                $arrTimeIntervalSlotTo = [];
                $arrTodayHoliday = [];
                $noTimeSlots = 0;
                $objHoliday = new Holiday;
                $arrHoliday = $objHoliday->getHolidayArrayList($collegeId);

                if (in_array($attendanceDate, $arrHoliday)) {
                    $arrTodayHoliday = ['Today is a Holiday'];
                }

                if (! empty($arrStudents)) {

                    $attendanceType = $arrAttendanceType[$getMatchTimeTableRecord->attendance_type];
                    $starttime = strtotime($getMatchTimeTableRecord->start_time);
                    $endtime = strtotime($getMatchTimeTableRecord->finish_time);
                    $difference = round(abs($starttime - $endtime) / 3600, 2);
                    $duration = 1;
                    $noTimeSlots = 1;
                    if ($attendanceType == 'Four-Hourly') {
                        $duration = 4;
                        $noTimeSlots = abs($difference / 4);
                    }
                    if ($attendanceType == 'Three-Hourly') {
                        $duration = 3;
                        $noTimeSlots = abs($difference / 4);
                    }
                    if ($attendanceType == 'Two-Hourly') {
                        $duration = 2;
                        $noTimeSlots = abs($difference / $duration);
                    }
                    if ($attendanceType == 'Hourly') {
                        $duration = 1;
                        $noTimeSlots = abs($difference / $duration);
                    }
                    if ($attendanceType != 'One Session') {
                        $arrTimeSlot = [];
                        $add_mins = $duration * 3600;
                        while ($starttime <= $endtime) {
                            $arrTimeSlot[] = date('h:i A', $starttime);
                            $starttime += $add_mins;
                        }
                        if (! in_array(date('h:i A', $endtime), $arrTimeSlot)) {
                            $arrTimeSlot[] = date('h:i A', $endtime);
                        }
                    } else {
                        $arrTimeSlot = [];
                        $arrTimeSlot[] = date('h:i A', $starttime);
                        $arrTimeSlot[] = date('h:i A', $endtime);
                        $noTimeSlots = 1;
                    }
                    $arrTimeIntervalSlot = [];
                    for ($i = 0; $i < $noTimeSlots; $i++) {
                        $interval = 5;
                        $starttime = strtotime($arrTimeSlot[$i]);
                        $endtime = strtotime($arrTimeSlot[$i + 1]);
                        $add_mins = $interval * 60;
                        while ($starttime <= $endtime) {
                            $arrTimeIntervalSlot[$i][date('h:i A', $starttime)] = date('h:i A', $starttime);
                            $starttime += $add_mins;
                        }
                        $arrTimeIntervalSlotFrom[$i] = $arrTimeIntervalSlot[$i];
                        $arrTimeIntervalSlotTo[$i] = $arrTimeIntervalSlot[$i];
                        array_shift($arrTimeIntervalSlotTo[$i]);
                        array_pop($arrTimeIntervalSlotFrom[$i]);
                    }
                }

                $arrBreak = [];
                $objTimetableBreaktime = new TimetableBreaktime;
                $arrBreakTime = $objTimetableBreaktime->getTimetableBreaktimeFromTo($timetableId);

                $isHoliday = SemesterDivision::where('college_id', '=', $collegeId)->where('semester_id', '=', $getMatchTimeTableRecord->semester_id)->where('is_holiday', '=', 1)->where('week_start', '<=', $attendanceDate)->where('week_finish', '>=', $attendanceDate)->first();

                $data['arrStudents'] = $arrStudents;
                $data['day'] = $day;
                $data['date'] = date('d-m-Y', strtotime($attendanceDate));
                $data['teacher'] = $teacher;
                $data['subjectCode'] = $subjectCode;
                $data['arrTimeSlot'] = $arrTimeSlot;
                $data['arrTimeIntervalSlot'] = $arrTimeIntervalSlot;
                $data['arrTimeIntervalSlotFrom'] = $arrTimeIntervalSlotFrom;
                $data['arrTimeIntervalSlotTo'] = $arrTimeIntervalSlotTo;
                $data['break_from'] = ! empty($getMatchTimeTableRecord->break_from) ? $getMatchTimeTableRecord->break_from : '';
                $data['break_to'] = ! empty($getMatchTimeTableRecord->break_to) ? $getMatchTimeTableRecord->break_to : '';
                $data['attendance_type'] = isset($getMatchTimeTableRecord->attendance_type) ? $getMatchTimeTableRecord->attendance_type : '';
                $data['noTimeSlots'] = $noTimeSlots;
                $data['timetableId'] = $timetableId;
                $data['arrAttendance'] = $arrAttendance;
                $data['attendanceType'] = $attendanceType;
                $data['breakTime'] = $arrBreakTime;
                $data['arrTodayHoliday'] = $arrTodayHoliday;
                $data['isHoliday'] = ! empty($isHoliday) ? '**This day is come under holiday week.' : '';

                return view('frontend.attendance_management.attendance-class-list', $data);
                break;
            case 'saveAttendance':
                $timetableId = $request->input('data.timetableId');
                $studentTimeSlots = $request->input('data.studentsTimeSlots');
                $attendanceDay = $request->input('data.attendance_day');
                $attendanceDate = $request->input('data.attendance_date');
                $attendanceType = $request->input('data.attendance_type');
                $breakFrom = $request->input('data.break_from');
                $breakTo = $request->input('data.break_to');
                $new_from = $request->input('data.new_from');
                $new_to = $request->input('data.new_to');
                $term = $request->input('data.term');
                $timeSlots = $request->input('data.time_slots');
                $strStudents = $request->input('data.students');

                $timeSlotsInterval = $request->input('data.time_slots_interval');
                $arrTerm = explode(':', $term);
                $week = end($arrTerm);
                reset($arrTerm);
                $arrFrom = [];
                $arrTo = [];
                parse_str($studentTimeSlots, $arrStudentTimeSlots);
                parse_str($new_from, $arrFrom);
                parse_str($new_to, $arrTo);
                parse_str($strStudents, $arrStudents);
                parse_str($timeSlotsInterval, $arrTimeSlotsInterval);
                $arrFrom = array_filter($arrFrom['new_from']);
                $arrTo = array_filter($arrTo['new_to']);
                $arrToNew = [];
                $arrFromoNew = [];
                $arrTimeslots = [];

                foreach ($arrFrom as $studentId => $fromString) {
                    $arrFromNew = explode(', ', $fromString[0]);
                    $arrToNew = explode(', ', $arrTo[$studentId][0]);
                    for ($i = 0; $i < $timeSlots; $i++) {
                        if (isset($arrFromNew[$i]) && $arrFromNew[$i] != '') {
                            $arrTimeslots[$studentId][$i] = $arrFromNew[$i].'-'.$arrToNew[$i];
                        }
                    }
                }

                $arrNewStudentTime = [];
                if (isset($arrStudentTimeSlots['time_slot'])) {
                    foreach ($arrStudentTimeSlots['time_slot'] as $studentId => $arr) {
                        for ($i = 0; $i < count($arr); $i++) {
                            if (in_array($arr[$i], $arrTimeSlotsInterval['time_slots_interval'])) {
                                $index = array_search($arr[$i], $arrTimeSlotsInterval['time_slots_interval']);
                                $arrNewStudentTime['time_slot'][$studentId][$index] = $arr[$i];
                            }
                        }
                    }
                }
                $getTimetableBreakTimeFromTo = [];
                $objTimetableBreaktime = new TimetableBreaktime;
                $getTimetableBreakTimeFromTo = $objTimetableBreaktime->getTimetableBreaktimeFromTo($timetableId);
                $arrMerged = [];
                foreach ($arrStudents['student'] as $studentId) {
                    for ($i = 0; $i < $timeSlots; $i++) {
                        if (isset($arrTimeslots[$studentId][$i])) {
                            $arrMerged[$studentId][$i]['time'] = $arrTimeslots[$studentId][$i];
                            $arrMerged[$studentId][$i]['break'] = $getTimetableBreakTimeFromTo;
                        } elseif (isset($arrNewStudentTime['time_slot'][$studentId][$i])) {
                            $arrMerged[$studentId][$i]['time'] = $arrNewStudentTime['time_slot'][$studentId][$i];
                            $arrMerged[$studentId][$i]['break'] = $getTimetableBreakTimeFromTo;
                        }
                    }
                }

                $data['timetableId'] = $timetableId;
                $data['weekPeriod'] = $week;
                $data['attendanceDay'] = $attendanceDay;
                $data['attendanceDate'] = $attendanceDate;
                $data['attendanceType'] = $attendanceType;
                $data['timeSlots'] = $arrMerged;

                $data['arrStudents'] = array_keys($arrFrom);

                $objStudentAttendance = new StudentAttendance;
                $result = $objStudentAttendance->saveStudentAttendance($data, $userId);

                if ($result) {
                    $resultData['status'] = 'alert-success';
                    $resultData['msg'] = 'Attendance recorded successfully.';
                } else {
                    $resultData['status'] = 'alert-error';
                    $resultData['msg'] = 'Something will be wrong. Please try again.';
                }
                echo json_encode($resultData);
                break;
            case 'updateStudentAttendance':
                $timetableId = $request->input('data.timetableId');
                $studentId = $request->input('data.studentId');
                $attendanceDay = $request->input('data.attendance_day');
                $attendanceDate = $request->input('data.attendance_date');
                $attendanceType = $request->input('data.attendance_type');
                $term = $request->input('data.term');
                $fromArray = $request->input('data.fromArray');
                $toArray = $request->input('data.toArray');
                $timeSlots = $request->input('data.time_slots');
                $arrTimeslots = [];
                for ($i = 0; $i < $timeSlots; $i++) {
                    $arrTimeslots[] = $fromArray[$i].'-'.$toArray[$i];
                }
                $arrTerm = explode(':', $term);
                $week = end($arrTerm);
                reset($arrTerm);
                $data['timetableId'] = $timetableId;
                $data['weekPeriod'] = $week;
                $data['attendanceDay'] = $attendanceDay;
                $data['attendanceDate'] = $attendanceDate;
                $data['attendanceType'] = $attendanceType;
                $data['timeSlots'][$studentId] = $arrTimeslots;
                $data['arrStudents'] = [$studentId];
                $objStudentAttendance = new StudentAttendance;
                $objStudentAttendance->saveStudentAttendance($data);
                break;
            case 'getStudentAttendanceDetails':
                $collegeId = Auth::user()->college_id;
                $semesterId = $request->input('data.semesterId');
                $term = $request->input('data.term');
                $studentId = $request->input('data.studentId');
                $objStudentAttendance = new StudentAttendance;
                $tableData = $objStudentAttendance->getStudentAttendance($collegeId, $studentId, $semesterId, $term);
                $objStudent = new Students;
                $studentProfile = $objStudent->getStudentDetail($studentId)[0];
                $data['arrStudentAttendance'] = $tableData;
                $data['studentProfile'] = $studentProfile;

                return view('frontend.attendance_management.student-daily-attendance', $data);
                break;
            case 'getStudentAttendanceDetailsBySubject':
                $collegeId = Auth::user()->college_id;
                $semesterId = $request->input('data.semesterId');
                $courseId = $request->input('data.courseId');
                $term = $request->input('data.term');
                $studentId = $request->input('data.studentId');
                $objStudentSubjectEnrolment = new StudentSubjectEnrolment;
                $listStudentSubjectEnrolment = $objStudentSubjectEnrolment->listStudentSubjectEnrolmentSemesterTerm($collegeId, $studentId, $courseId, '', $semesterId, $term);
                $arrSubjectList = [];
                $subjectBatch = [];
                for ($i = 0; $i < count($listStudentSubjectEnrolment); $i++) {
                    $arrSubjectList[$i] = $listStudentSubjectEnrolment[$i]['subject_code'];
                    $subjectBatch[$listStudentSubjectEnrolment[$i]['subject_code']] = $listStudentSubjectEnrolment[$i]['batch'];
                }
                $objStudent = new Students;
                $studentProfile = $objStudent->getStudentDetail($studentId)[0];
                $objStudentAttendance = new StudentAttendance;
                $arrWeeklyAttendance = $objStudentAttendance->getWeeklyAttendanceBySubject($collegeId, $semesterId, $term, $studentId);
                $objTimetable = new Timetable;
                $arrTimetable = $objTimetable->getWeeklyTimeTableBySubject($collegeId, $semesterId, $term, $subjectBatch);

                $total_projected_attandance_rows = 0;
                foreach ($arrTimetable as $key => $row) {
                    $subjectWeeklyHours = isset($row['subject']) ? array_sum($row['subject']) : 0;
                    $total_projected_attandance_rows += $subjectWeeklyHours;
                }

                $data['arrSubjectList'] = $arrSubjectList;
                $data['studentProfile'] = $studentProfile;
                $data['arrWeeklyAttendance'] = $arrWeeklyAttendance;
                $data['arrTimetable'] = $arrTimetable;
                $data['total_projected_attandance_rows'] = $total_projected_attandance_rows;

                //                echo "<pre>";
                //                print_r($total_projected_attandance_rows);
                //                print_r($arrTimetable);
                return view('frontend.attendance_management.student-weekly-attendance', $data);
                break;
            case 'getStudentAttendanceDetailsByTimetable':
                $collegeId = Auth::user()->college_id;
                $semesterId = $request->input('data.semesterId');
                $courseId = $request->input('data.courseId');
                $term = $request->input('data.term');
                $studentId = $request->input('data.studentId');

                $objStudentSubjectEnrolment = new StudentSubjectEnrolment;
                $listStudentSubjectEnrolment = $objStudentSubjectEnrolment->listStudentSubjectEnrolmentSemesterTerm($collegeId, $studentId, $courseId, '', $semesterId, $term);
                $arrSubjectList = [];
                $subjectBatch = [];
                for ($i = 0; $i < count($listStudentSubjectEnrolment); $i++) {
                    $arrSubjectList[$i] = $listStudentSubjectEnrolment[$i]['subject_code'];
                    $subjectBatch[$listStudentSubjectEnrolment[$i]['subject_code']] = $listStudentSubjectEnrolment[$i]['batch'];
                }

                $objStudent = new Students;
                $studentProfile = $objStudent->getStudentDetail($studentId)[0];
                $objStudentAttendance = new StudentAttendance;
                $arrWeeklyAttendance = $objStudentAttendance->getWeeklyAttendanceByDay($collegeId, $semesterId, $term, $studentId);
                $objTimetable = new Timetable;
                $arrTimetable = $objTimetable->getWeeklyTimeTableByDay($collegeId, $semesterId, $term, $studentId, $subjectBatch);

                $data['studentProfile'] = $studentProfile;
                $data['arrWeeklyAttendance'] = $arrWeeklyAttendance;
                $data['arrTimetable'] = $arrTimetable;

                return view('frontend.attendance_management.student-timetable-attendance', $data);
                break;
            case 'getTotalStudentAttendanceDetails':
                $collegeId = Auth::user()->college_id;
                $courseId = $request->input('data.courseId');
                $fromDate = $request->input('data.fromDate');
                $toDate = $request->input('data.toDate');
                $studentId = $request->input('data.studentId');
                $objStudentCourse = new StudentCourse;
                $arrStudentCourseDetails = $objStudentCourse->getStudentCoursesInformation($studentId, $courseId);
                $objStudent = new Students;
                $studentProfile = $objStudent->getStudentDetail($studentId)[0];
                //                $objTimetable = new Timetable();
                //                $totalHours = $objTimetable->getTotalHoursByCourse($courseId, $studentId, $collegeId, $fromDate, $toDate);
                //                $objStudentAttendance = new StudentAttendance();
                //                $totalAttendedHours = $objStudentAttendance->getTotalHoursByCourse($courseId, $studentId, $collegeId, $fromDate, $toDate);
                //                $studentAttd = 0;
                //                if ($totalHours != 0) {
                //                    $studentAttd = $totalAttendedHours / $totalHours;
                //                }
                //                $data['attdPer'] = number_format($studentAttd, 2);
                // echo "Total Hours: ".$totalHours." Total Attended Hours: ".$totalAttendedHours;
                $data['studentProfile'] = $studentProfile;
                $data['toDate'] = $toDate;
                $data['fromDate'] = $fromDate;
                $data['arrStudentCourseDetails'] = $arrStudentCourseDetails[0];
                // project course wise attendance Percentage
                $objStudentSubjectEnrolment = new StudentSubjectEnrolment;
                $arrSubjectUnitList = $objStudentSubjectEnrolment->listStudentSubjectEnrolmentV2($collegeId, $studentId, $courseId, $this->newLimit = 5);
                $semesterIds = [];
                foreach ($arrSubjectUnitList as $arrSubject) {
                    $semesterIds[] = $arrSubject['semester_id'];
                }
                $totalSemCurrAttd = $cnt = 0;
                $semesterIds = array_unique($semesterIds);
                foreach ($semesterIds as $semId) {
                    $semesterId = $semId; // $arrSubjectUnitList[0]['semester_id'];
                    $arrTerm = $this->_getTermBySemester($semesterId);
                    foreach ($arrTerm as $Term) {
                        $term = $Term; // $arrSubjectUnitList[0]['term'];
                        $objStudentSubjectEnrolment = new StudentSubjectEnrolment;
                        $listStudentSubjectEnrolment = $objStudentSubjectEnrolment->listStudentSubjectEnrolment($collegeId, $studentId, $courseId);
                        $arrSubjectList = [];
                        $subjectBatch = [];
                        for ($i = 0; $i < count($listStudentSubjectEnrolment); $i++) {
                            $arrSubjectList[$i] = $listStudentSubjectEnrolment[$i]->subject_code;
                            $subjectBatch[$listStudentSubjectEnrolment[$i]->subject_code] = $listStudentSubjectEnrolment[$i]->batch;
                        }
                        $objStudentAttendance = new StudentAttendance;
                        $arrWeeklyAttendance = $objStudentAttendance->getWeeklyAttendanceBySubject($collegeId, $semesterId, $term, $studentId);

                        $objTimetable = new Timetable;
                        $arrTimetable = $objTimetable->getWeeklyTimeTableBySubject($collegeId, $semesterId, $term, $subjectBatch);
                        $stdWeeklyTotal = $stdSubjectTotal = $semCurrAttd = $total_rows = $subjectWeeklyTotals = $projected_attandance = $subjectWeeklyTotal = $student_week_total = 0;

                        foreach ($arrTimetable as $week => $timetable) {
                            $studentWeeklyTotal = $subjectWeeklyTotal = 0;
                            foreach ($arrSubjectList as $subject) {
                                $studentWeeklyHours = isset($arrWeeklyAttendance[$timetable['semester_name']][$timetable['term']][$week]['subject'][$subject]) ? $arrWeeklyAttendance[$timetable['semester_name']][$timetable['term']][$week]['subject'][$subject] : 0;
                                $subjectWeeklyHours = isset($timetable['subject'][$subject]) ? $timetable['subject'][$subject] : 0;
                                $studentWeeklyTotal += $studentWeeklyHours;
                                $subjectWeeklyTotal += $subjectWeeklyHours;
                            }
                            $stdWeeklyTotal += $studentWeeklyTotal;
                            $stdSubjectTotal += $subjectWeeklyTotal;
                            if ($stdSubjectTotal != 0) {
                                $semCurrAttd = ($stdWeeklyTotal == 0) ? 0 : ($stdWeeklyTotal * 100) / $stdSubjectTotal;
                            }

                        }
                        $totalSemCurrAttd += $semCurrAttd;
                    }
                    $cnt++;
                }
                $data['attdPer'] = ($totalSemCurrAttd / $cnt);

                return view('frontend.attendance_management.student-total-attendance', $data);
                break;
            case 'getTotalStudentAttendanceDetailsNew':
                $collegeId = Auth::user()->college_id;
                $courseId = $request->input('data.courseId');
                $fromDate = $request->input('data.fromDate');
                $toDate = $request->input('data.toDate');
                $studentId = $request->input('data.studentId');
                $objStudentCourse = new StudentCourse;
                $arrStudentCourseDetails = $objStudentCourse->getStudentCoursesInformation($studentId, $courseId);
                $objStudent = new Students;
                $studentProfile = $objStudent->getStudentDetail($studentId)[0];
                //                $objTimetable = new Timetable();
                //                $totalHours = $objTimetable->getTotalHoursByCourse($courseId, $studentId, $collegeId, $fromDate, $toDate);
                //                $objStudentAttendance = new StudentAttendance();
                //                $totalAttendedHours = $objStudentAttendance->getTotalHoursByCourse($courseId, $studentId, $collegeId, $fromDate, $toDate);
                //                $studentAttd = 0;
                //                if ($totalHours != 0) {
                //                    $studentAttd = $totalAttendedHours / $totalHours;
                //                }
                //                $data['attdPer'] = number_format($studentAttd, 2);
                // echo "Total Hours: ".$totalHours." Total Attended Hours: ".$totalAttendedHours;
                $data['studentProfile'] = $studentProfile;
                $data['toDate'] = $toDate;
                $data['fromDate'] = $fromDate;
                $data['arrStudentCourseDetails'] = $arrStudentCourseDetails[0];

                // project course wise attendance Percentage
                $objStudentSubjectEnrolment = new StudentSubjectEnrolment;
                $arrSubjectUnitList = $objStudentSubjectEnrolment->listStudentSubjectEnrolmentV2($collegeId, $studentId, $courseId, $this->newLimit = 10);
                echo '<pre>';
                //                print_r($arrTimetable);
                //                print_r($arrSubjectUnitList);exit;
                $arrSubjectList = [];
                $arrWeeklyAttendance = [];
                $arrTimetable = [];
                $subjectBatch = [];
                $arrSemestor = [];
                $arrTerm = [];
                if (! empty($arrSubjectUnitList)) {
                    foreach ($arrSubjectUnitList as $arrSubjectUnit) {

                        $objStudentAttendance = new StudentAttendance;
                        $arrWeeklyAttendance[] = $objStudentAttendance->getWeeklyAttendanceBySubject($collegeId, $arrSubjectUnit['semester_id'], $arrSubjectUnit['term'], $studentId);
                        $subjectBatch[$arrSubjectUnit['subject_code']] = $arrSubjectUnit['batch'];
                        $arrSubjectList[$arrSubjectUnit['semester_id']][] = $arrSubjectUnit['subject_code'];
                        $arrSemestor[$arrSubjectUnit['semester_id']] = $arrSubjectUnit['semester_name'];

                        $objTimetable = new Timetable;
                        $arrTimetable[$arrSubjectUnit['semester_id']] = $objTimetable->getWeeklyTimeTableBySubjectOnDeskboard($collegeId, $arrSubjectUnit['semester_id'], $arrSubjectUnit['term'], $subjectBatch);

                    }

                }
                echo '<pre>';
                print_r($arrWeeklyAttendance);
                print_r($arrSubjectList);
                exit;
                $studentWeeklyTotal = $semCurrAttd = $stdSubjectTotal = $stdWeeklyTotal = $total_rows = $subjectWeeklyTotals = $projected_attandance = $subjectWeeklyTotal = $student_week_total = 0;
                $cnt = 1;
                if (count($arrSemestor) > 0) {
                    $total_rows_Semestor = count($arrSemestor);
                    foreach ($arrSemestor as $k => $v) {
                        if (count($arrTimetable[$k]) > 0) {
                            $total_rows = count($arrTimetable[$k]) * $total_rows_Semestor;
                            foreach ($arrTimetable[$k] as $week => $timetable) {
                                $studentWeeklyTotal = $subjectWeeklyTotal = 0;
                                //                                       echo "<pre>";
                                //                                       print_r($arrSubjectList);
                                // total weekly student attendance calulation
                                foreach ($arrSubjectList[$k] as $arrSubject => $subject) {
                                    echo $subject.'--'.$studentWeeklyHours = isset($arrWeeklyAttendance[$arrSubject][$timetable['semester_name']][$timetable['term']][$week]['subject'][$subject]) ? $arrWeeklyAttendance[$arrSubject][$timetable['semester_name']][$timetable['term']][$week]['subject'][$subject] : 0;
                                    echo '**'.$subjectWeeklyHours = isset($timetable['subject'][$subject]) ? $timetable['subject'][$subject] : 0;
                                    echo '<br>';
                                    $studentWeeklyTotal += $studentWeeklyHours;
                                    $subjectWeeklyTotal += $subjectWeeklyHours;
                                }

                                $stdWeeklyTotal += $studentWeeklyTotal;
                                $stdSubjectTotal += $subjectWeeklyTotal;
                                if ($stdSubjectTotal != 0) {
                                    $semCurrAttd = ($stdWeeklyTotal == 0) ? 0 : ($stdWeeklyTotal * 100) / $stdSubjectTotal;
                                }
                                $weeklyPercent = 0;
                                if ($subjectWeeklyTotal != 0) {
                                    $weeklyPercent = $studentWeeklyTotal / $subjectWeeklyTotal;
                                }
                                $student_week_total += $studentWeeklyTotal;
                                if ($subjectWeeklyTotal == 0) {
                                    $subjectWeeklyTotal = 1;
                                    $subjectWeeklyTotalZero = 0;
                                } else {
                                    $subjectWeeklyTotalZero = 1;
                                }
                                $subjectWeeklyTotals += $studentWeeklyTotal;

                                $projected_attandance = ((($subjectWeeklyTotal * ($total_rows - $cnt)) + $subjectWeeklyTotals) / ($subjectWeeklyTotal * $total_rows)) * 100;
                                $cnt++;

                                $projected_attandance = (($subjectWeeklyTotalZero == 0) ? $subjectWeeklyTotalZero : $projected_attandance);
                            }
                        } else {
                            echo $projected_attandance = 0;
                        }
                    }
                }

                exit;
                $data['attdPer'] = $projected_attandance;

                return view('frontend.attendance_management.student-total-attendance', $data);
                break;

            case 'getCourses':
                $collegeId = Auth::user()->college_id;
                $courseTypeId = $request->input('data.courseTypeId');
                $courseStatus = $request->input('data.courseStatus');
                $arrCourse = $this->_getCourseByCourseType($courseTypeId, $courseStatus);
                echo json_encode($arrCourse);
                break;
            case 'getCourseSubject':
                $courseId = $request->input('data.courseId');
                $arrSubject = $this->_getSubjectsByCourse($courseId);
                echo json_encode($arrSubject);
                break;
            case 'getCourseYear':
                $courseId = $request->input('data.courseId');
                $arrYear = $this->_getCourseYear($courseId);
                echo json_encode($arrYear);
                break;
            case 'getCourseSemester':
                $courseId = $request->input('data.courseId');
                $year = $request->input('data.year');
                $courseTypeId = $request->input('data.courseTypeId');
                $arrSemester = $this->_getSemesterByCourseYear($courseId, $year, $courseTypeId);
                echo json_encode($arrSemester);
                break;
            case 'getSemesterTerm':
                $semesterId = $request->input('data.semesterId');
                $arrTerm = $this->_getTermBySemester($semesterId);
                echo json_encode($arrTerm);
                break;
            case 'getSemesterWeek':
                $semesterId = $request->input('data.semesterId');
                $term = $request->input('data.term');
                $arrWeek = $this->_getDivisionBySemesterTerm($semesterId, $term);
                echo json_encode($arrWeek);
                break;
            case 'getBatch':
                $semesterId = $request->input('data.semesterId');
                $term = $request->input('data.term');
                $attendanceDate = $request->input('data.attendanceDate');
                $subjectId = $request->input('data.subjectId');
                $arrBatch = $this->_getBatch($semesterId, $term, $subjectId, $attendanceDate);
                echo json_encode($arrBatch);
                break;
            case 'getStudentAttendanceBySubject':
                $data = [];
                $collegeId = Auth::user()->college_id;
                $courseId = $request->input('data.courseId');
                $semesterId = $request->input('data.semesterId');
                $term = $request->input('data.term');
                $attendanceDate = $request->input('data.attendanceDate');
                $subjectId = $request->input('data.subjectId');
                $batch = $request->input('data.batch');
                $objStudentAttendance = new StudentAttendance;
                $arrStudentList = $objStudentAttendance->getAttendanceStudentList($collegeId, $courseId, $semesterId, $term, $batch, $attendanceDate);
                $data['arrStudentList'] = $arrStudentList;

                return view('frontend.attendance_management.student-list-bulk-attendance-subject', $data);
                break;
            case 'getStudentAttadanceWeeklyFilter':
                $arrResult = $request->input('data');
                $this->getStudentAttadanceWeeklyFilter($arrResult);
                break;
            case 'getStudentAttadanceReports':
                $arrResult = $request->input('data');
                $this->getStudentAttadanceReports($arrResult);
                break;
            case 'sentMailStudentAttadanceReport':
                $arrResult = $request->input('data');
                $this->sentMailStudentAttadanceReport($arrResult);
                break;

            case 'studentPercentageWarningMailSetting':
                $collegeId = Auth::user()->college_id;
                $arrRecipient = Config::get('constants.arrRecipient');
                unset($arrRecipient['3']);
                unset($arrRecipient['4']);
                unset($arrRecipient['5']);
                unset($arrRecipient['6']);

                $objRtoEmailTemplate = new EmailTemplate;
                $template_type_id = (isset($studentWarningPercentage->template_type) == 0 ? 0 : $studentWarningPercentage->template_type);

                $arrEmailTemplateList = ['----Choose Email Templete----'];
                $emailTemplateList = $objRtoEmailTemplate->getEmailTemplate($template_type_id, $collegeId);
                foreach ($emailTemplateList as $list) {
                    $arrEmailTemplateList[$list->id] = $list->template_name;
                }

                $data['arrEmailTemplateListPercentage'] = $arrEmailTemplateList;
                $data['arrRecipientPercentage'] = $arrRecipient;
                echo json_encode($data);
                break;

        }
    }

    private function _getSemester($course_id, $course_type_id, $studentId = '')
    {
        $collegeId = Auth::user()->college_id;
        $objSemesterData = new Semester;
        $arrSemesterData = $objSemesterData->getSemesterHasCourse($course_id, $course_type_id, $collegeId, $studentId);
        if (count($arrSemesterData) == 0) {
            $arrSemesterData[] = 'No Semester';
        }

        return $arrSemesterData;
    }

    private function _getTermBySemester($semesterId)
    {
        $collegeId = Auth::user()->college_id;
        $objRtoSemesterDivision = new SemesterDivision;
        $termList = $objRtoSemesterDivision->_getTermBySemester($semesterId, $collegeId);
        $results = [];
        if (count($termList) != 0) {
            foreach ($termList as $term) {
                $results[$term->term] = $term->term;
            }
        } else {
            $results[] = 'No Term';
        }

        return $results;
    }

    private function _getSemesterName($courseTypeId)
    {
        $collegeId = Auth::user()->college_id;
        $objRtoSemester = new Semester;
        $semesterName = $objRtoSemester->_getSemesterNamev2($courseTypeId, $collegeId);
        echo json_encode($semesterName);
        exit;
    }

    private function _getSemesterDivision($semesterId)
    {
        $collegeId = Auth::user()->college_id;
        $objRtoSemester = new SemesterDivision;
        $arrResult = $objRtoSemester->_getDivisionBySemester($semesterId, $collegeId);
        $arrSemester = [];
        if (count($arrResult)) {
            foreach ($arrResult as $id => $semesterDivision) {
                $arrSemester[$semesterDivision->term.':'.$semesterDivision->week_start.':'.$semesterDivision->week_finish.':'.$semesterDivision->week_period] = $semesterDivision->term.' : W - '.$semesterDivision->week_period.' : '.date('d-m-Y', strtotime($semesterDivision->week_start)).' - '.date('d-m-Y', strtotime($semesterDivision->week_finish)).'  '.(($semesterDivision->is_holiday == 1) ? ' - [Break Week]' : '');
            }
        } else {
            $arrSemester[] = 'No Term - Week Found';
        }

        return $arrSemester;
    }

    private function _getClass($courseTypeId, $semesterId, $term, $weekStart, $weekEnd, $day, $slot)
    {
        $collegeId = Auth::user()->college_id;
        $objTimetable = new Timetable;
        $arrResult = $objTimetable->getClassListBySemesterWeekDay($collegeId, $courseTypeId, $semesterId, $term, $weekStart, $weekEnd, $day, $slot);
        $arrClass = [];
        if (count($arrResult)) {
            foreach ($arrResult as $id => $timetable) {
                $arrClass[$timetable->id] = $timetable->subject_code.' : '.$timetable->subject_name.' (Batch: '.$timetable->batch.') by '.$timetable->name_title.' '.$timetable->first_name.' '.$timetable->last_name.' Time : '.date('h:i A', strtotime($timetable->start_time)).' - '.date('h:i A', strtotime($timetable->finish_time));
            }
        } else {
            $arrClass[] = 'No Class Found';
        }

        return $arrClass;
    }

    public function getDays($sStartDate, $sEndDate)
    {
        // Firstly, format the provided dates.
        // This function works best with YYYY-MM-DD
        // but other date formats will work thanks
        // to strtotime().
        $sStartDate = gmdate('Y-m-d', strtotime($sStartDate));
        $sEndDate = gmdate('Y-m-d', strtotime($sEndDate));

        // Start the variable off with the start date
        $aDays[date('l', strtotime($sStartDate))] = $sStartDate;

        // Set a 'temp' variable, sCurrentDate, with
        // the start date - before beginning the loop
        $sCurrentDate = $sStartDate;

        // While the current date is less than the end date
        while ($sCurrentDate < $sEndDate) {
            // Add a day to the current date
            $sCurrentDate = gmdate('Y-m-d', strtotime('+1 day', strtotime($sCurrentDate)));

            // Add this new day to the aDays array
            $aDays[date('l', strtotime($sCurrentDate))] = $sCurrentDate;
        }

        // Once the loop has finished, return the
        // array of days.
        return $aDays;
    }

    public function get_string_between($string, $start, $end)
    {
        $string = ' '.$string;
        $ini = strpos($string, $start);
        if ($ini == 0) {
            return '';
        }
        $ini += strlen($start);
        $len = strpos($string, $end, $ini) - $ini;

        return substr($string, $ini, $len);
    }

    private function _getCourseByCourseType($courseTypeId, $courseStatus)
    {
        $collegeId = Auth::user()->college_id;
        $objRtoCourses = new Courses;
        $courseList = $objRtoCourses->_getCourseByCourseTypev2($courseTypeId, $collegeId, $courseStatus);
        if (count($courseList)) {
            return $courseList;
        } else {
            return ['' => 'No Course Found'];
        }
    }

    public function _getCourseYear($courseId)
    {
        $collegeId = Auth::user()->college_id;
        $objStudentCourse = new StudentCourse;
        $arrYear = $objStudentCourse->getStudentCourseIntakeYear($courseId);
        $result = [];
        if (count($arrYear)) {
            foreach ($arrYear as $year) {
                $result[$year['intake_year']] = $year['intake_year'];
            }
        } else {
            $result[] = 'No Year Found';
        }

        return $result;
    }

    public function _getSemesterByCourseYear($courseId, $year, $courseTypeKey)
    {
        $collegeId = Auth::user()->college_id;
        $objSemester = new Semester;
        $arrSemester = $objSemester->getSemesterByCourseYear($courseId, $year, $collegeId, $courseTypeKey);
        if (count($arrSemester)) {
            return $arrSemester;
        } else {
            return ['' => 'No Semester Found'];
        }
    }

    public function _getDivisionBySemesterTerm($semesterId, $term)
    {
        $collegeId = Auth::user()->college_id;
        $objSemesterDivision = new SemesterDivision;
        $arrSemesterDivision = $objSemesterDivision->_getDivisionBySemesterTerm($semesterId, $term, $collegeId);
        $result = [];
        if (count($arrSemesterDivision)) {
            foreach ($arrSemesterDivision as $semDiv) {
                $result[$semDiv['week_period']] = $semDiv['week_period'].' : '.date('d-m-Y', strtotime($semDiv['week_start'])).' - '.date('d-m-Y', strtotime($semDiv['week_finish']));
            }
        } else {
            $result = ['' => 'No Week Found'];
        }

        return $result;
    }

    public function _getSubjectsByCourse($courseId)
    {
        $collegeId = Auth::user()->college_id;
        $objCourseSubject = new CourseSubject;
        $arrSubjectList = $objCourseSubject->getSubjectsByCourse($courseId, $collegeId);
        if (count($arrSubjectList)) {
            return $arrSubjectList;
        } else {
            return ['' => 'No Subject Found'];
        }
    }

    public function _getBatch($semesterId, $term, $subjectId, $attendanceDate)
    {
        $collegeId = Auth::user()->college_id;
        $objTimetable = new Timetable;
        $arrBatchList = $objTimetable->getBatchByDate($collegeId, $semesterId, $term, $subjectId, $attendanceDate);
        if (count($arrBatchList)) {
            return $arrBatchList;
        } else {
            return ['' => 'No Batch Found'];
        }
    }

    public function studentAttendanceCertificate($studentId, $courseId, $todate, $fromdate)
    {
        $collegeId = Auth::user()->college_id;
        $objStudentCourse = new StudentCourse;
        $arrStudentCourseDetails = $objStudentCourse->getStudentCoursesInformation($studentId, $courseId);

        $objRtoCollegeDetails = new CollegeDetails;
        $objCollegeDetails = $objRtoCollegeDetails->getCollegeDetails($collegeId);
        $filePath1 = Config::get('constants.uploadFilePath.CollegeLogo');
        $destinationPath1 = Helpers::changeRootPath($filePath1);
        $data['arrState'] = Config::get('constants.arrState');

        $objStudent = new Students;
        $studentProfile = $objStudent->getStudentDetail($studentId)[0];
        //        $objTimetable = new Timetable();
        //        $totalHours = $objTimetable->getTotalHoursByCourse($courseId, $studentId, $collegeId, $fromdate, $todate);
        //        $objStudentAttendance = new StudentAttendance();
        //        $totalAttendedHours = $objStudentAttendance->getTotalHoursByCourse($courseId, $studentId, $collegeId, $fromdate, $todate);
        //        $studentAttd = 0;
        //        if ($totalHours != 0) {
        //            $studentAttd = $totalAttendedHours / $totalHours;
        //        }
        //        $data['attdPer'] = number_format($studentAttd, 2);
        $data['logoPath'] = $destinationPath1['default'];
        $data['objCollegeDetails'] = $objCollegeDetails;
        $data['studentProfile'] = $studentProfile;
        $data['arrStudentCourseDetails'] = $arrStudentCourseDetails[0];

        $objStudentSubjectEnrolment = new StudentSubjectEnrolment;
        $arrSubjectUnitList = $objStudentSubjectEnrolment->listStudentSubjectEnrolmentV2($collegeId, $studentId, $courseId, $this->newLimit = 5);

        $semesterIds = [];
        foreach ($arrSubjectUnitList as $arrSubject) {
            $semesterIds[] = $arrSubject['semester_id'];
        }
        $totalSemCurrAttd = $cnt = 0;
        $semesterIds = array_unique($semesterIds);
        foreach ($semesterIds as $semId) {
            $semesterId = $semId; // $arrSubjectUnitList[0]['semester_id'];
            $term = $arrSubjectUnitList[0]['term'];
            $objStudentSubjectEnrolment = new StudentSubjectEnrolment;
            $listStudentSubjectEnrolment = $objStudentSubjectEnrolment->listStudentSubjectEnrolment($collegeId, $studentId, $courseId);
            $arrSubjectList = [];
            $subjectBatch = [];
            for ($i = 0; $i < count($listStudentSubjectEnrolment); $i++) {
                $arrSubjectList[$i] = $listStudentSubjectEnrolment[$i]->subject_code;
                $subjectBatch[$listStudentSubjectEnrolment[$i]->subject_code] = $listStudentSubjectEnrolment[$i]->batch;
            }
            $objStudentAttendance = new StudentAttendance;
            $arrWeeklyAttendance = $objStudentAttendance->getWeeklyAttendanceBySubject($collegeId, $semesterId, $term, $studentId);

            $objTimetable = new Timetable;
            $arrTimetable = $objTimetable->getWeeklyTimeTableBySubject($collegeId, $semesterId, $term, $subjectBatch);
            $stdWeeklyTotal = $stdSubjectTotal = $semCurrAttd = $total_rows = $subjectWeeklyTotals = $projected_attandance = $subjectWeeklyTotal = $student_week_total = 0;

            foreach ($arrTimetable as $week => $timetable) {
                $studentWeeklyTotal = $subjectWeeklyTotal = 0;
                foreach ($arrSubjectList as $subject) {
                    $studentWeeklyHours = isset($arrWeeklyAttendance[$timetable['semester_name']][$timetable['term']][$week]['subject'][$subject]) ? $arrWeeklyAttendance[$timetable['semester_name']][$timetable['term']][$week]['subject'][$subject] : 0;
                    $subjectWeeklyHours = isset($timetable['subject'][$subject]) ? $timetable['subject'][$subject] : 0;
                    $studentWeeklyTotal += $studentWeeklyHours;
                    $subjectWeeklyTotal += $subjectWeeklyHours;
                }
                $stdWeeklyTotal += $studentWeeklyTotal;
                $stdSubjectTotal += $subjectWeeklyTotal;
                if ($stdSubjectTotal != 0) {
                    $semCurrAttd = ($stdWeeklyTotal == 0) ? 0 : ($stdWeeklyTotal * 100) / $stdSubjectTotal;
                }
            }
            $totalSemCurrAttd += $semCurrAttd;
            $cnt++;
        }
        $data['attdPer'] = $totalSemCurrAttd / $cnt;
        $pdf = App::make('dompdf.wrapper');
        $pdf->loadView('frontend.attendance_management.certificate-pdf', $data);

        return $pdf->download("AttadanceCertificates_$studentProfile->generated_stud_id.pdf");
    }

    public function getStudentAttadanceWeeklyFilter($arrResult)
    {

        $collegeId = Auth::user()->college_id;
        $course_type_id = $arrResult['course_type_id'];
        $firstCourse = $arrResult['course_id'];
        $firstSemesterId = $arrResult['semester_id'];
        $term = $arrResult['term'];
        $firstWeek = $arrResult['week_period'];
        $order_type = $arrResult['order_type'];
        $orderBy = $arrResult['orderBy'];

        $objStudentSubjectEnrollment = new StudentSubjectEnrolment;
        $arrStudentList = $objStudentSubjectEnrollment->getStudentListByCourseWithFilter($collegeId, $firstCourse, $firstSemesterId, $term, $firstWeek, $order_type, $orderBy);
        $student_projected_attandance = 0;
        foreach ($arrStudentList as $studentId => $studentList) {
            //  $studentId=416;
            $objStudentSubjectEnrolment = new StudentSubjectEnrolment;
            $listStudentSubjectEnrolment = $objStudentSubjectEnrolment->listStudentSubjectEnrolment($collegeId, $studentId, $firstCourse);
            $arrSubjectList = [];
            $subjectBatch = [];
            for ($i = 0; $i < count($listStudentSubjectEnrolment); $i++) {
                $arrSubjectList[$i] = $listStudentSubjectEnrolment[$i]->subject_code;
                $subjectBatch[$listStudentSubjectEnrolment[$i]->subject_code] = $listStudentSubjectEnrolment[$i]->batch;
            }

            $objStudentAttendance = new StudentAttendance;
            $arrWeeklyAttendance = $objStudentAttendance->getWeeklyAttendanceBySubject($collegeId, $firstSemesterId, $term, $studentId);
            $objTimetable = new Timetable;
            $arrTimetable = $objTimetable->getWeeklyTimeTableBySubject($collegeId, $firstSemesterId, $term, $subjectBatch);

            $stdWeeklyTotal = $stdSubjectTotal = $semCurrAttd = $total_rows = $subjectWeeklyTotals = $projected_attandance = $subjectWeeklyTotal = $student_week_total = 0;
            $cnt = 1;
            $total_rows = count($arrTimetable);
            $total_projected_attandance_rows = 0;
            foreach ($arrTimetable as $key => $row) {
                $subjectWeeklyHours = isset($row['subject']) ? array_sum($row['subject']) : 0;
                $total_projected_attandance_rows += $subjectWeeklyHours;
            }

            foreach ($arrTimetable as $week => $timetable) {
                $studentWeeklyTotal = $subjectWeeklyTotal = 0;
                foreach ($arrSubjectList as $subject) {
                    $studentWeeklyHours = isset($arrWeeklyAttendance[$timetable['semester_name']][$timetable['term']][$week]['subject'][$subject]) ? $arrWeeklyAttendance[$timetable['semester_name']][$timetable['term']][$week]['subject'][$subject] : 0;
                    $subjectWeeklyHours = isset($timetable['subject'][$subject]) ? $timetable['subject'][$subject] : 0;
                    $studentWeeklyTotal += $studentWeeklyHours;
                    $subjectWeeklyTotal += $subjectWeeklyHours;
                }
                $stdWeeklyTotal += $studentWeeklyTotal;
                $stdSubjectTotal += $subjectWeeklyTotal;

                $student_week_total += $studentWeeklyTotal;
                $subjectWeeklyTotals += $studentWeeklyTotal;

                $projected_attandance = ($total_projected_attandance_rows > 0) ? ((($total_projected_attandance_rows - $stdSubjectTotal) + $subjectWeeklyTotals) / ($total_projected_attandance_rows)) * 100 : 0;

                if ($firstWeek == $week) {
                    $student_projected_attandance = $projected_attandance;
                }
            }
            $arrStudentList[$studentId]['projected_attandance'] = number_format((float) $student_projected_attandance, 2);
        }
        $objStudentAttendance = new StudentAttendance;
        $arrStudentAttendance = $objStudentAttendance->getWeeklyAttendanceByCourse($collegeId, $firstSemesterId, $term, $firstCourse, $firstWeek);

        $data['arrStudentAttendance'] = $arrStudentAttendance;
        $data['arrStudentList'] = $arrStudentList;

        $resultList = view('frontend.attendance_management.search-bulk-attendance-weekly', $data)->render();
        echo $resultList;
        exit;
    }

    public function getStudentAttadanceReports($arrResult)
    {

        $collegeId = Auth::user()->college_id;
        $course_type_id = $arrResult['course_type_id'];
        $firstCourse = $arrResult['course_id'];
        $firstSemesterId = $arrResult['semester_id'];
        $term = $arrResult['term'];
        $firstWeek = $arrResult['week_period'];
        $report_type = $arrResult['report_type'];
        $report_value = $arrResult['report_value'];

        $objStudentSubjectEnrollment = new StudentSubjectEnrolment;
        $arrStudentList = $objStudentSubjectEnrollment->getStudentListByCourseWithFilter($collegeId, $firstCourse, $firstSemesterId, $term, $firstWeek, $order_type = '', $orderBy = '');
        //        echo "<pre>";print_r($arrStudentList);exit;
        $courseName = '';
        $student_projected_attandance = 0;
        foreach ($arrStudentList as $studentId => $studentList) {
            $courseName = $arrStudentList[$studentId]['course_name'];
            $objStudentSubjectEnrolment = new StudentSubjectEnrolment;
            $listStudentSubjectEnrolment = $objStudentSubjectEnrolment->listStudentSubjectEnrolment($collegeId, $studentId, $firstCourse);
            $arrSubjectList = [];
            $subjectBatch = [];
            for ($i = 0; $i < count($listStudentSubjectEnrolment); $i++) {
                $arrSubjectList[$i] = $listStudentSubjectEnrolment[$i]->subject_code;
                $subjectBatch[$listStudentSubjectEnrolment[$i]->subject_code] = $listStudentSubjectEnrolment[$i]->batch;
            }

            $objStudentAttendance = new StudentAttendance;
            $arrWeeklyAttendance = $objStudentAttendance->getWeeklyAttendanceBySubject($collegeId, $firstSemesterId, $term, $studentId);
            $objTimetable = new Timetable;
            $arrTimetable = $objTimetable->getWeeklyTimeTableBySubject($collegeId, $firstSemesterId, $term, $subjectBatch);

            $stdWeeklyTotal = $stdSubjectTotal = $semCurrAttd = $total_rows = $subjectWeeklyTotals = $projected_attandance = $subjectWeeklyTotal = $student_week_total = 0;
            $cnt = 1;
            $total_rows = count($arrTimetable);
            $total_projected_attandance_rows = 0;
            foreach ($arrTimetable as $key => $row) {
                $subjectWeeklyHours = isset($row['subject']) ? array_sum($row['subject']) : 0;
                $total_projected_attandance_rows += $subjectWeeklyHours;
            }
            if ($total_projected_attandance_rows != 0) {
                foreach ($arrTimetable as $week => $timetable) {
                    $studentWeeklyTotal = $subjectWeeklyTotal = 0;
                    foreach ($arrSubjectList as $subject) {
                        $studentWeeklyHours = isset($arrWeeklyAttendance[$timetable['semester_name']][$timetable['term']][$week]['subject'][$subject]) ? $arrWeeklyAttendance[$timetable['semester_name']][$timetable['term']][$week]['subject'][$subject] : 0;
                        $subjectWeeklyHours = isset($timetable['subject'][$subject]) ? $timetable['subject'][$subject] : 0;
                        $studentWeeklyTotal += $studentWeeklyHours;
                        $subjectWeeklyTotal += $subjectWeeklyHours;
                    }
                    $stdWeeklyTotal += $studentWeeklyTotal;
                    $stdSubjectTotal += $subjectWeeklyTotal;

                    $student_week_total += $studentWeeklyTotal;
                    $subjectWeeklyTotals += $studentWeeklyTotal;

                    $projected_attandance = ((($total_projected_attandance_rows - $stdSubjectTotal) + $subjectWeeklyTotals) / ($total_projected_attandance_rows)) * 100;

                    if ($firstWeek == $week) {
                        $student_projected_attandance = $projected_attandance;
                    }
                }
            }
            $arrStudentList[$studentId]['projected_attandance'] = number_format((float) $student_projected_attandance, 2);
            if (isset($arrStudentList[$studentId]) && $student_projected_attandance >= $report_value && $report_type == 1) {
                unset($arrStudentList[$studentId]);
            } elseif (isset($arrStudentList[$studentId]) && $report_type == 2) {
                $arrConsecutiveStudentList = $this->getReportConsecutiveStudentList($collegeId, $report_value, $firstCourse, $firstSemesterId, $term);
                if (! in_array($studentId, $arrConsecutiveStudentList)) {
                    unset($arrStudentList[$studentId]);
                }
            }
        }

        $objStudentAttendance = new StudentAttendance;
        $arrStudentAttendance = $objStudentAttendance->getWeeklyAttendanceByCourse($collegeId, $firstSemesterId, $term, $firstCourse, $firstWeek);
        $data['selectCourse'] = $courseName;
        $data['countStudentList'] = count($arrStudentList);
        $data['arrStudentAttendance'] = $arrStudentAttendance;
        $data['arrStudentList'] = $arrStudentList;
        $resultList = view('frontend.attendance_management.student-list-report-and-warning', $data)->render();
        echo $resultList;
        exit;
    }

    public function getReportConsecutiveStudentList($collegeId, $report_value, $semester_id1, $semester_id, $term)
    {

        $where_date = date('Y-m-d');

        $timeTables = Timetable::where('college_id', '=', $collegeId)->where('semester_id', '=', $semester_id)
            ->where('term', '=', $term)->get();

        $batch = [];
        foreach ($timeTables as $timeTableData) {
            $batch[$timeTableData->batch]['days'][] = $timeTableData->day;
            // Specify the start date. This date can be any English textual format

            $date_from = strtotime($timeTableData->start_week); // Convert date to a UNIX timestamp
            // Specify the end date. This date can be any English textual format

            $date_to = strtotime($timeTableData->end_week); // Convert date to a UNIX timestamp
            // Loop from the start date to end date and output all dates inbetween

            for ($i = $date_from; $i <= $date_to; $i += 86400) {
                $date = date('Y-m-d', $i);
                if ($timeTableData->day == date('l', strtotime($date))) {
                    if ($where_date >= $date) {
                        $batch[$timeTableData->batch]['last_five'][] = $date;
                    }
                }
            }
        }

        foreach ($batch as $key => $btch) {
            if (isset($btch['last_five'])) {
                sort($btch['last_five']);
                $batch[$key] = array_slice(array_unique($btch['last_five']), -$report_value);
            }
        }
        $studentBatch = [];
        foreach ($batch as $batch_nm => $dates) {
            $studentSubjectEnrolls = StudentSubjectEnrolment::where('batch', $batch_nm)->groupBy('student_id')->get();
            foreach ($studentSubjectEnrolls as $studentSubjectEnroll) {
                $studentBatch[$batch_nm]['students'][] = $studentSubjectEnroll->student_id;
                $studentBatch[$batch_nm]['dates'] = $dates;
            }
        }
        $studentTimeTables = [];
        foreach ($studentBatch as $batch_nm => $stdBatchs) {
            foreach ($stdBatchs['students'] as $stdBatch) {
                $studentAttendances = StudentAttendance::where('student_id', $stdBatch)->get();
                foreach ($studentAttendances as $studentAttendance) {
                    $studentTimeTable = Timetable::where('id', $studentAttendance->timetable_id)->first();
                    if (isset($studentTimeTable->batch)) {
                        if ($studentTimeTable->batch == $batch_nm) {
                            $studentTimeTables[$batch_nm][$stdBatch][] = $studentAttendance->attendance_date;
                        }
                    }
                }
            }
        }

        foreach ($studentBatch as $std_batch_nm => $std_batch) {
            foreach ($std_batch['students'] as $std_id) {
                if (isset($studentTimeTables[$std_batch_nm])) {
                    $batch_last_dates = $studentBatch[$std_batch_nm]['dates'];
                    $studentTimeTableDate = isset($studentTimeTables[$std_batch_nm][$std_id]) ? $studentTimeTables[$std_batch_nm][$std_id] : [];
                    $result = array_intersect($batch_last_dates, $studentTimeTableDate);
                    if (count($result) == 0) {
                        $arrStudent[] = $std_id;
                    }
                } else {
                    $arrStudent[] = $std_id;
                }
            }
        }

        return $arrStudent;
    }

    public function exportReportForStudent(Request $request)
    {

        $collegeId = Auth::user()->college_id;
        $semesterId = $request->semester;
        $term = $request->term;
        $courseId = $request->course;
        $weekPeriod = $request->week_period;
        $report_type = $request->report_type;
        $report_value = $request->report_value;
        $arrStudentInput = [];
        if (isset($request->student_id) && $request->student_id != null) {
            $arrStudentInput = explode(',', $request->student_id);
        }

        $objStudentSubjectEnrollment = new StudentSubjectEnrolment;
        $arrStudentList = $objStudentSubjectEnrollment->getStudentListByCourseWithFilter($collegeId, $courseId, $semesterId, $term, $weekPeriod, $order_type = '', $orderBy = '');
        //        echo "<pre>";print_r($arrStudentList);exit;
        $student_projected_attandance = 0;

        foreach ($arrStudentList as $studentId => $studentList) {
            if (in_array($studentId, $arrStudentInput)) {

                $courseName = $arrStudentList[$studentId]['course_name'];

                $objStudentSubjectEnrolment = new StudentSubjectEnrolment;
                $listStudentSubjectEnrolment = $objStudentSubjectEnrolment->listStudentSubjectEnrolment($collegeId, $studentId, $courseId);
                $arrSubjectList = [];
                $subjectBatch = [];
                for ($i = 0; $i < count($listStudentSubjectEnrolment); $i++) {
                    $arrSubjectList[$i] = $listStudentSubjectEnrolment[$i]->subject_code;
                    $subjectBatch[$listStudentSubjectEnrolment[$i]->subject_code] = $listStudentSubjectEnrolment[$i]->batch;
                }

                $objStudentAttendance = new StudentAttendance;
                $arrWeeklyAttendance = $objStudentAttendance->getWeeklyAttendanceBySubject($collegeId, $semesterId, $term, $studentId);
                $objTimetable = new Timetable;
                $arrTimetable = $objTimetable->getWeeklyTimeTableBySubject($collegeId, $semesterId, $term, $subjectBatch);

                $stdWeeklyTotal = $stdSubjectTotal = $semCurrAttd = $total_rows = $subjectWeeklyTotals = $projected_attandance = $subjectWeeklyTotal = $student_week_total = 0;
                $cnt = 1;
                $total_rows = count($arrTimetable);
                $total_projected_attandance_rows = 0;
                foreach ($arrTimetable as $key => $row) {
                    $subjectWeeklyHours = isset($row['subject']) ? array_sum($row['subject']) : 0;
                    $total_projected_attandance_rows += $subjectWeeklyHours;
                }

                foreach ($arrTimetable as $week => $timetable) {
                    $studentWeeklyTotal = $subjectWeeklyTotal = 0;
                    foreach ($arrSubjectList as $subject) {
                        $studentWeeklyHours = isset($arrWeeklyAttendance[$timetable['semester_name']][$timetable['term']][$week]['subject'][$subject]) ? $arrWeeklyAttendance[$timetable['semester_name']][$timetable['term']][$week]['subject'][$subject] : 0;
                        $subjectWeeklyHours = isset($timetable['subject'][$subject]) ? $timetable['subject'][$subject] : 0;
                        $studentWeeklyTotal += $studentWeeklyHours;
                        $subjectWeeklyTotal += $subjectWeeklyHours;
                    }
                    $stdWeeklyTotal += $studentWeeklyTotal;
                    $stdSubjectTotal += $subjectWeeklyTotal;

                    $student_week_total += $studentWeeklyTotal;
                    $subjectWeeklyTotals += $studentWeeklyTotal;

                    $projected_attandance = ((($total_projected_attandance_rows - $stdSubjectTotal) + $subjectWeeklyTotals) / ($total_projected_attandance_rows)) * 100;

                    if ($weekPeriod == $week) {
                        $student_projected_attandance = $projected_attandance;
                    }
                }
                $arrStudentList[$studentId]['projected_attandance'] = number_format((float) $student_projected_attandance, 2);

            } else {
                unset($arrStudentList[$studentId]);
            }
        }
        $objStudentAttendance = new StudentAttendance;
        $arrStudentAttendance = $objStudentAttendance->getWeeklyAttendanceByCourse($collegeId, $semesterId, $term, $courseId, $weekPeriod);

        //        echo "<pre>";
        //        print_r($arrStudentAttendance);exit;
        if (count($arrStudentList) > 0) {

            $rowCount = count($arrStudentList);
            $tableTitle = ["Reports Student list for Course: $courseName; ($rowCount records)"];
            $arrFundingSource = Config::get('constants.arrFundingSource');
            Excel::create('Student_List_', function ($excel) use ($arrStudentList, $tableTitle, $arrStudentAttendance) {

                $headers = ['Student ID',
                    'Student Name',
                    'Course Name',
                    'Course Attempt',
                    'Status',
                    'Course Duration',
                    'Course Curr. Attd%',
                    'Course Proj. Attd%',
                    'Class Hour',
                    'Attended Hour'];

                $excel->sheet('Student_List_', function ($sheet) use ($headers, $arrStudentList, $tableTitle, $arrStudentAttendance) {

                    $sheet->row(1, $tableTitle);
                    $sheet->mergeCells('A1:K1');
                    $sheet->fromArray([$headers], null, 'A2', false, false);
                    $count = 3;
                    foreach ($arrStudentList as $student) {
                        $totalClassHours = isset($student['total_hours']) ? $student['total_hours'] : 0;
                        $totalAttdHours = isset($arrStudentAttendance[$student['student_id']]['total_hours']) ? $arrStudentAttendance[$student['student_id']]['total_hours'] : 0;
                        $currAttd = ($totalAttdHours != 0) ? ($totalClassHours > 0) ? ((($totalAttdHours > 0) ? $totalAttdHours * 100 : 100) / $totalClassHours) : 100 : 0;

                        $sheet->row($count, [
                            $student['generated_stud_id'],
                            $student['student_name'],
                            $student['course_name'],
                            $student['course_attempt'],
                            $student['status'],
                            date('d M Y', strtotime($student['start_date'])).'-'.date('d M Y', strtotime($student['finish_date'])),
                            number_format($currAttd, 2).' %',
                            $student['projected_attandance'].' %',
                            number_format($totalClassHours, 2),
                            number_format($totalAttdHours, 2),
                        ]);
                        $count++;
                    }
                });
            })->export('xls');
        } else {
            $request->session()->flash('session_error', 'Student record not found for selected data');

            return redirect(route('report-and-warnings'));
        }
    }

    public function sentMailStudentAttadanceReport($arrResult)
    {

        $collegeId = Auth::user()->college_id;
        //        print_r($request);
        //        exit;
        $courseId = $arrResult['course_id'];
        $semesterId = $arrResult['semester_id'];
        $term = $arrResult['term'];
        $weekPeriod = $arrResult['week_period'];
        $report_type = $arrResult['report_type'];
        $report_value = $arrResult['report_value'];
        $email_templete_id = $arrResult['email_templete_id'];

        $arrStudentInput = [];
        if (isset($arrResult['student_id']) && $arrResult['student_id'] != null) {
            $arrStudentInput = explode(',', $arrResult['student_id']);
        }

        $objStudentSubjectEnrollment = new StudentSubjectEnrolment;
        $arrStudentList = $objStudentSubjectEnrollment->getStudentListByCourseWithFilter($collegeId, $courseId, $semesterId, $term, $weekPeriod, $order_type = '', $orderBy = '');
        //        echo "<pre>";print_r($arrStudentList);exit;
        $student_projected_attandance = 0;

        foreach ($arrStudentList as $studentId => $studentList) {
            if (in_array($studentId, $arrStudentInput)) {

                $courseName = $arrStudentList[$studentId]['course_name'];

                $objStudentSubjectEnrolment = new StudentSubjectEnrolment;
                $listStudentSubjectEnrolment = $objStudentSubjectEnrolment->listStudentSubjectEnrolment($collegeId, $studentId, $courseId);
                $arrSubjectList = [];
                $subjectBatch = [];
                for ($i = 0; $i < count($listStudentSubjectEnrolment); $i++) {
                    $arrSubjectList[$i] = $listStudentSubjectEnrolment[$i]->subject_code;
                    $subjectBatch[$listStudentSubjectEnrolment[$i]->subject_code] = $listStudentSubjectEnrolment[$i]->batch;
                }

                $objStudentAttendance = new StudentAttendance;
                $arrWeeklyAttendance = $objStudentAttendance->getWeeklyAttendanceBySubject($collegeId, $semesterId, $term, $studentId);
                $objTimetable = new Timetable;
                $arrTimetable = $objTimetable->getWeeklyTimeTableBySubject($collegeId, $semesterId, $term, $subjectBatch);

                $stdWeeklyTotal = $stdSubjectTotal = $semCurrAttd = $total_rows = $subjectWeeklyTotals = $projected_attandance = $subjectWeeklyTotal = $student_week_total = 0;
                $cnt = 1;
                $total_rows = count($arrTimetable);
                $total_projected_attandance_rows = 0;
                foreach ($arrTimetable as $key => $row) {
                    $subjectWeeklyHours = isset($row['subject']) ? array_sum($row['subject']) : 0;
                    $total_projected_attandance_rows += $subjectWeeklyHours;
                }

                foreach ($arrTimetable as $week => $timetable) {
                    $studentWeeklyTotal = $subjectWeeklyTotal = 0;
                    foreach ($arrSubjectList as $subject) {
                        $studentWeeklyHours = isset($arrWeeklyAttendance[$timetable['semester_name']][$timetable['term']][$week]['subject'][$subject]) ? $arrWeeklyAttendance[$timetable['semester_name']][$timetable['term']][$week]['subject'][$subject] : 0;
                        $subjectWeeklyHours = isset($timetable['subject'][$subject]) ? $timetable['subject'][$subject] : 0;
                        $studentWeeklyTotal += $studentWeeklyHours;
                        $subjectWeeklyTotal += $subjectWeeklyHours;
                    }
                    $stdWeeklyTotal += $studentWeeklyTotal;
                    $stdSubjectTotal += $subjectWeeklyTotal;

                    $student_week_total += $studentWeeklyTotal;
                    $subjectWeeklyTotals += $studentWeeklyTotal;

                    $projected_attandance = ((($total_projected_attandance_rows - $stdSubjectTotal) + $subjectWeeklyTotals) / ($total_projected_attandance_rows)) * 100;

                    if ($weekPeriod == $week) {
                        $student_projected_attandance = $projected_attandance;
                    }
                }
                $arrStudentList[$studentId]['projected_attandance'] = number_format((float) $student_projected_attandance, 2);

            } else {
                unset($arrStudentList[$studentId]);
            }
        }
        $objStudentAttendance = new StudentAttendance;
        $arrStudentAttendance = $objStudentAttendance->getWeeklyAttendanceByCourse($collegeId, $semesterId, $term, $courseId, $weekPeriod);
        $sentCount = 0;
        foreach ($arrStudentList as $studentId => $studentList) {
            //           echo $studentId."--".$email_templete_id."*";
            $objRtoEmailTemplate = new EmailTemplate;
            $arrEmailTemplateInfo = $objRtoEmailTemplate->getEmailTemplateInfo($collegeId, $email_templete_id);
            $content = (count($arrEmailTemplateInfo) > 0) ? $arrEmailTemplateInfo[0]->content : '';
            //   File::put('mytextdocument.txt',$content);
            $objSendMail = new SendMail;
            $replacedContent = $objSendMail->setEmailBodyContent($studentId, '', $content);
            // this module is under process
            /*$mailData = array(
                'from' => env('MAIL_USERNAME'),
                'fromName' => 'noreply',
                'to' => '',
                'page' => 'mail.low-attendance',
                'subject' => 'Low Attendance Report',
                'data' => ['content'=>$replacedContent],
                'attachFile'=>[],
                'cc'=>''
            );
            $sendMail = new SendMail;
            $result = $sendMail->sendSmtpMail($mailData);*/
            $sentCount++;
        }
        if ($sentCount > 0) {
            $return = ['status' => 'success', 'message' => 'Student Assessment Reports Mail sent successfully.'];
        } else {
            $return = ['status' => 'error', 'message' => 'Student Assessment Reports Not Mail Sent.'];
        }

        echo json_encode($return);

        //
    }
}
