<?php

namespace App\Http\Controllers\Frontend;

use App;
use App\Classes\SiteConstants;
use App\Exports\ManageOfferExport;
use App\Helpers\Helpers;
use App\Http\Controllers\Controller;
use App\Model\Agent;
use App\Model\AgentCommission;
use App\Model\CollegeCampus;
use App\Model\CollegeDetails;
use App\Model\CollegeMaterials;
use App\Model\Colleges;
use App\Model\Country;
use App\Model\Courses;
use App\Model\CoursesIntakeDate;
use App\Model\CourseTemplate;
use App\Model\CourseType;
use App\Model\EmailTemplate;
use App\Model\EmailTemplateDocuments;
use App\Model\OfferDocumentChecklist;
use App\Model\OfferUpfrontFeeSchedule;
use App\Model\PaymentMode;
use App\Model\PdfTemplate;
use App\Model\SendMail;
use App\Model\SetupSection;
use App\Model\Staff;
use App\Model\StudentCommunicationLog;
use App\Model\StudentCourse;
use App\Model\StudentDetails;
use App\Model\StudentEducation;
use App\Model\StudentEmail;
use App\Model\StudentEmployment;
use App\Model\StudentIdFormate;
use App\Model\StudentInitialPayment;
use App\Model\StudentInitialPaymentDetails;
use App\Model\StudentInitialPaymentTransaction;
use App\Model\StudentMiscellaneousPayment;
use App\Model\StudentOfferDocuments;
use App\Model\StudentOffers;
use App\Model\Students;
use App\Model\StudentUploadFile;
use App\Model\StudyReason;
use App\Model\v2\InvoiceSetting;
use App\Model\v2\OfferLabel;
use App\Model\v2\Student;
use App\Services\StudentPaymentService;
use App\Services\StudentPaymentServiceV2;
use App\Traits\OfferLetterNameTrait;
use Domains\Xero\Entities\Invoice;
use Domains\Xero\Facades\Xero;
use File;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Redirect;
use Illuminate\Support\Facades\Validator;
use Integrations\Zoho\Facades\Zoho;
use Integrations\Zoho\Traits\ZohoTrait;
use Maatwebsite\Excel\Facades\Excel;
use Support\Services\UploadService;

class OfferManageController extends Controller
{
    use OfferLetterNameTrait;
    use ZohoTrait;

    protected $studentPaymentService;

    protected $studentPaymentServiceV2;

    public function __construct(
        StudentPaymentService $studentPaymentService,
        StudentPaymentServiceV2 $studentPaymentServiceV2,
    ) {
        parent::__construct();
        $this->studentPaymentService = $studentPaymentService;
        $this->studentPaymentServiceV2 = $studentPaymentServiceV2;
    }

    public function offerManage()
    {

        // listing available student in rto_students
        $perPage = Config::get('constants.pagination.perPage');
        $arrCourseStatusMail = Config::get('constants.arrCourseStatusMail');
        $collegeId = Auth::user()->college_id;

        $objRtoStudents = new Students;
        $objStudents = $objRtoStudents->getStudentList($perPage, $collegeId);

        $objRtoCollegeCampus = new CollegeCampus;
        $arrCollegeCampus = $objRtoCollegeCampus->getCollegeCampusList($collegeId);

        $objCourses = new Courses;
        $arrCourses = $objCourses->getCourseListV2($collegeId);
        $courseType = courseType::Where(['status' => 1])->whereIn('college_id', [Auth::user()->college_id, 0])->get()->toArray();
        $arrCourseType['All'] = 'All';
        for ($i = 0; $i < count($courseType); $i++) {
            $arrCourseType[$courseType[$i]['id']] = $courseType[$i]['title'];
        }
        $arrOfferLabel = OfferLabel::pluck('label_name', 'id')->toArray();
        // array for search by
        $arrSearchBy = ['offer_id' => 'Id',
            'first_name' => 'First Name',
            'agent_name' => 'Agent',
            'course_name' => 'Course',
            'created_at' => 'Date Applied',
            'generated_stud_id' => 'Reserved Student ID'];
        $data['header'] = [
            'title' => 'Offers View',
            'breadcrumb' => [
                'Home' => route('user_dashboard'),
                'Offers View' => '',
            ]];
        $data['pagetitle'] = 'Application List';
        $data['plugincss'] = [];
        $data['css'] = [];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js'];
        $data['js'] = ['offermanage.js'];
        $data['funinit'] = ['OfferManage.initOfferLetter()'];
        $data['activateValue'] = 'Offers';

        $data['objStudents'] = $objStudents;
        $data['arrCollegeCampus'] = $arrCollegeCampus;
        $data['arrSearchBy'] = $arrSearchBy;
        $data['arrCourses'] = $arrCourses;
        $data['arrOfferLabel'] = $arrOfferLabel;
        $data['arrCourseStatusMail'] = $arrCourseStatusMail;
        $data['arrCourseType'] = $arrCourseType;
        $data['isXeroConnect'] = (Xero::isConnected()) ? true : false;
        $data['isZohoConnect'] = $this->isZohoConnected();
        $data['mainmenu'] = 'clients';
        $data['SHORT_COURSE_TYPE_ID'] = SiteConstants::SHORT_COURSE_TYPE_ID;

        return view('frontend.student.offer-manage', $data);
    }

    public function deleteStudentsCourseOffer($primaryId, Request $request)
    {

        $objStudentCourse = new StudentCourse;
        $studentCourseResult = StudentCourse::find($primaryId);
        $studentCourseCount = StudentCourse::where('student_id', $studentCourseResult['student_id'])->count();

        if ($studentCourseCount == 1) {

            $objRtoDeleteStudent = new Students;
            $objDeleteStudents = $objRtoDeleteStudent->deleteStudents($studentCourseResult['student_id']);

            $objRtoDeleteStudentDetails = new StudentDetails;
            $objRtoDeleteStudentDetails->deleteStudentsDetails($studentCourseResult['student_id']);

            $objRtoStudentEducation = new StudentEducation;
            $objRtoStudentEducation->deleteStudentEducation($studentCourseResult['student_id']);

            $objRtoStudentEmployment = new StudentEmployment;
            $objStudentEmployment = $objRtoStudentEmployment->deleteStudentEmployment($studentCourseResult['student_id']);

            $objRtoStudentCourse = new StudentCourse;
            $objStudentCourse = $objRtoStudentCourse->deleteStudentCourse($studentCourseResult['student_id']);
            $objRtoOfferUpfrontFeeSchedule = new OfferUpfrontFeeSchedule;
            $deleteSameUpfrontFeeSchedule = $objRtoOfferUpfrontFeeSchedule->deleteFeeScheduleByStudent($studentCourseResult['student_id']);
        } else {
            $arrStudentCourse = $objStudentCourse->deleteStudentCourseById($primaryId);
            $arrScheduleData = [
                'student_id' => $studentCourseResult['student_id'],
                'course_id' => $studentCourseResult['course_id']];

            $objRtoOfferUpfrontFeeSchedule = new OfferUpfrontFeeSchedule;
            $deleteSameUpfrontFeeSchedule = $objRtoOfferUpfrontFeeSchedule->deleteSameUpfrontFeeSchedule($arrScheduleData);
        }

        $request->session()->flash('session_success', 'Successfully Deleted Record.');

        return redirect(route('offer-manage'));
    }

    public function studentCourseRejected($primaryId, Request $request)
    {
        $objStudentCourse = new StudentCourse;
        $arrStudentCourse = $objStudentCourse->studentCourseRejected($primaryId);
        $request->session()->flash('session_success', 'successfully Rejected.');

        return redirect(route('offer-manage'));
    }

    public function offerLetter($courseId, $studentId, $studentCourseID, Request $request)
    {
        $data = [];

        $collegeId = Auth::user()->college_id;

        if (isset($studentId)) {
            $objRtoPdfTemplate = new PdfTemplate;
            $arrPdfTemplateContent = $objRtoPdfTemplate->getPdfContent(1);
            $content = $arrPdfTemplateContent[0]->pdf_template;
            $offerLetterContent = $objRtoPdfTemplate->setPdfTemplateBodyContentNew($collegeId, $studentId, $content, $type = 'preview');

            if (isset($offerLetterContent['status']) && $offerLetterContent['status'] == 'error') {
                $request->session()->flash('session_error', $offerLetterContent['msg']);

                return redirect(route('offer-manage'));
            }
            $pdf = App::make('dompdf.wrapper');
            $pdf->loadHTML($offerLetterContent);

            $pdf->output();
            $dom_pdf = $pdf->getDomPDF();
            $canvas = $dom_pdf->get_canvas();
            $canvas->page_text(280, 800, 'Page {PAGE_NUM} of {PAGE_COUNT}', null, 6, [0, 0, 0]);

            return $pdf->stream();
            // return $offerLetterContent;
        }
    }

    public function domesticOfferLetter($courseId, $studentId, $studentCourseID, Request $request)
    {
        $data = [];

        $collegeId = Auth::user()->college_id;

        if (isset($studentId)) {
            $objRtoPdfTemplate = new PdfTemplate;
            $arrPdfTemplateContent = $objRtoPdfTemplate->getPdfContent(2);
            $content = $arrPdfTemplateContent[0]->pdf_template;
            $offerLetterContent = $objRtoPdfTemplate->setPdfTemplateBodyContentNew($collegeId, $studentId, $content, $type = 'preview');

            if (isset($offerLetterContent['status']) && $offerLetterContent['status'] == 'error') {
                $request->session()->flash('session_error', $offerLetterContent['msg']);

                return redirect(route('offer-manage'));
            }

            return $offerLetterContent;
        }
    }

    public function offerLetterPdfNew($courseId, $studentId, $studentCourseID, Request $request)
    {
        $collegeId = Auth::user()->college_id;
        if (! empty($studentId) && $studentId > 0) {

            ini_set('memory_limit', '1024M');
            ini_set('max_execution_time', 180); // 3 minutes

            $studentOfferLetterName = $this->studentOfferLetterName($studentId);

            $objRtoPdfTemplate = new PdfTemplate;
            $arrPdfTemplateContent = $objRtoPdfTemplate->getPdfContent(1);
            $content = $arrPdfTemplateContent[0]->pdf_template;
            $offerLetterContent = $objRtoPdfTemplate->setPdfTemplateBodyContentNew($collegeId, $studentId, $content);

            if (isset($offerLetterContent['status']) && $offerLetterContent['status'] == 'error') {
                $request->session()->flash('session_error', $offerLetterContent['msg']);

                return redirect(route('offer-manage'));
            }

            $pdf = App::make('dompdf.wrapper');
            $pdf->loadHTML($offerLetterContent);

            $pdf->output();
            $dom_pdf = $pdf->getDomPDF();
            $canvas = $dom_pdf->get_canvas();
            $canvas->page_text(280, 800, 'Page {PAGE_NUM} of {PAGE_COUNT}', null, 6, [0, 0, 0]);

            // $canvas->page_text(500, 800, "Page {PAGE_NUM} of {PAGE_COUNT}", null, 6, array(0, 0, 0));
            // return $pdf->stream();exit;
            return $pdf->download($studentOfferLetterName.'.pdf');
            /*if($pdf->stream()){
                return $pdf->download($studentOfferLetterName.'.pdf');
            }else{
                $request->session()->flash('session_error', 'Some thing will be wrong. Please try again.');
                return redirect(route('offer-manage'));
            }*/
        } else {
            $request->session()->flash('session_error', 'Not valid student ID');

            return redirect(route('offer-manage'));
        }
    }

    public function domesticOfferLetterPdf($courseId, $studentId, $studentCourseID, Request $request)
    {
        $collegeId = Auth::user()->college_id;
        if (isset($studentId)) {

            $studentOfferLetterName = $this->studentOfferLetterName($studentId);

            $objRtoPdfTemplate = new PdfTemplate;
            $arrPdfTemplateContent = $objRtoPdfTemplate->getPdfContent(2);
            $content = $arrPdfTemplateContent[0]->pdf_template;
            $offerLetterContent = $objRtoPdfTemplate->setPdfTemplateBodyContentNew($collegeId, $studentId, $content);

            if (isset($offerLetterContent['status']) && $offerLetterContent['status'] == 'error') {
                $request->session()->flash('session_error', $offerLetterContent['msg']);

                return redirect(route('offer-manage'));
            }

            $pdf = App::make('dompdf.wrapper');
            $pdf->loadHTML($offerLetterContent);

            $pdf->output();
            $dom_pdf = $pdf->getDomPDF();
            $canvas = $dom_pdf->get_canvas();
            $canvas->page_text(500, 800, 'Page {PAGE_NUM} of {PAGE_COUNT}', null, 6, [0, 0, 0]);
            // return $pdf->stream();exit;
            /*if (!empty($objStudentnameCoursecode[0]->course_code)){
                return $pdf->download($objStudentnameCoursecode[0]->course_code.'_'.$objStudentnameCoursecode[0]->first_name.'_'.$objStudentnameCoursecode[0]->family_name.'.pdf');
            } else {
                return $pdf->download('domestic-offer-letter.pdf');
            }*/

            return $pdf->download($studentOfferLetterName.'.pdf');
        }
    }

    public function addCourse($studentId, Request $request)
    {

        $collegeId = Auth::user()->college_id;
        $userId = Auth::user()->id;
        $arrModeQfDelivery = Config::get('constants.arrModeQfDelivery');

        $objRtoAgent = new Agent;
        $arrAgentData = $objRtoAgent->getAgentList($collegeId);

        $objRtoCollegeCampus = new CollegeCampus;
        $arrCampusData = $objRtoCollegeCampus->getCollegeCampusList($collegeId);

        $objRtoStudyReason = new StudyReason;
        $arrStudyreasonData = $objRtoStudyReason->getStudyReasonList();

        $objRtoapplicationRefID = new Students;
        $applicationRefID = $objRtoapplicationRefID->getExistsReferenceId($studentId, $collegeId);

        $objStaff = new Staff;
        $arrStaffData = $objStaff->getStaffNameList($collegeId);

        $arrCourseManagerData[''] = '- - Select Course Manager - -';
        $courseManagerList = $arrCourseManagerData + $arrStaffData;

        $arrPlacementData[''] = '- - Select Placement Manager - -';
        $placementManagerList = $arrPlacementData + $arrStaffData;

        $objCourseData = new CourseType;
        $arrCourseList = $objCourseData->getCourseType($collegeId);

        $objRtoEnrolledCources = new StudentCourse;
        $objEnrolledCources = $objRtoEnrolledCources->getStudentCoursesData($studentId);

        $checkEditCondition = Students::select('college_id')->where('id', $studentId)->get()->toArray();
        $editCollegeId = $checkEditCondition[0]['college_id'];

        $arrIntakeYearData = Config::get('constants.arrIntakeYear');

        if ($request->input('goBack') && $request->input('goBack') === 'Go Back') {
            return redirect(route('apply-online-step2', ['id' => $studentId]));
        }

        if ($request->isMethod('post')) {

            $validations = [
                'agent_id' => 'required',
                'campus_id' => 'required',
                'palacement_manager_id' => 'required',
                'course_manager_id' => 'required',
                'course_type_id' => 'required',
                'intake_year' => 'required',
                'course_id' => 'required',
                'study_reason_id' => 'required',
                'course_fee' => 'required',
                'course_upfront_fee' => 'required',
                'course_material_fee' => 'required',
            ];

            /* START */
            if ($request->input('intake_date') != '') {
                $date1 = $request->input('intake_date');
            }
            $total_weeks = ($request->input('total_weeks') != '') ? $request->input('total_weeks') : null;
            $date1 = strtotime($date1);
            $date2 = strtotime('+'.$total_weeks.' week', $date1);
            $finish_date = date('Y-m-d', $date2);

            $campusId = $request->input('campus_id');
            $courseId = $request->input('course_id');
            $offerId = $request->input('offer_id');
            $rowCount = $objRtoEnrolledCources->checkDuplicateRecord($studentId, $campusId, $courseId, $offerId);

            if ($rowCount->count() > 0) {
                $validations['id'] = 'required';
                $courseValidations = [
                    'id.required' => 'This Course Already Applied.',
                ];
                $validator = Validator::make($request->all(), $validations, $courseValidations);
            } else {
                if (! empty($finish_date)) {
                    if ($request->input('start_date') != '') {
                        if ($request->input('start_date') > $finish_date) {
                            $validations['finish_date'] = 'required';
                        }
                    }
                    if ($request->input('intake_date') != '') {
                        if ($request->input('intake_date') > $finish_date) {
                            $validations['finish_date'] = 'required';
                        }
                    }

                    $checkValidations = [
                        'finish_date.required' => 'Finish Date must be Greater From Start Date.',
                    ];
                    $validator = Validator::make($request->all(), $validations, $checkValidations);
                } else {
                    $validator = Validator::make($request->all(), $validations);
                }
            }
            /* END */

            if ($validator->fails()) {
                return redirect(route('offer-manage-add-course', ['id' => $studentId]))
                    ->withErrors($validator)
                    ->withInput();
            }

            $objStudentOffers = new StudentOffers;
            $studentOfferId = $objStudentOffers->getStudentOffersId($studentId, $request);

            $studentCourse = new StudentCourse;
            $studentCourse->saveStudentCourse($studentId, $studentOfferId, $request, $userId);

            $request->session()->flash('session_success', 'New course add successfully.');

            return redirect(route('offer-manage-add-course', ['id' => $studentId]));
        }

        $data['header'] = [
            'title' => 'Add New Course',
            'breadcrumb' => [
                'Home' => route('user_dashboard'),
                'Offer manage' => route('offer-manage'),
                'Apply Online' => '',
            ]];
        $studentType = $applicationRefID[0]->student_type;
        if ($studentType == 'Offshore') {
            $data['arrModeQfDelivery'] = ['2' => 'oncampus'];
        } elseif ($studentType == 'Onshore') {
            $data['arrModeQfDelivery'] = ['2' => 'oncampus'];
        } elseif ($studentType == 'Domestic') {
            $data['arrModeQfDelivery'] = $arrModeQfDelivery;
        }

        $data['pagetitle'] = 'Apply Online';
        $data['plugincss'] = [];
        $data['css'] = ['frontendcomman.css'];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js'];
        $data['js'] = ['applyonline.js'];
        $data['funinit'] = ['Applyonline.initstep3()'];
        $data['activateValue'] = 'Offers';

        $data['arrAgentData'] = $arrAgentData;
        $data['arrCampusData'] = $arrCampusData;
        $data['arrIntakeYearData'] = $arrIntakeYearData;
        $data['arrStudyreasonData'] = $arrStudyreasonData;
        $data['objEnrolledCources'] = $objEnrolledCources;
        $data['applicationRefID'] = $applicationRefID;

        $data['placementManagerList'] = $placementManagerList;
        $data['courseManagerList'] = $courseManagerList;
        $data['arrCourseData'] = $arrCourseList;
        $data['studentId'] = $studentId;
        $data['arrModeQfDelivery'] = $arrModeQfDelivery;
        $data['flag'] = 'offer';
        $data['mainmenu'] = 'clients';

        if ($editCollegeId == $collegeId) {
            return view('frontend.offer_manage.offer-add-course', $data);
        } else {
            return view('frontend.error_page.unauthorized', $data);
        }
    }

    public function offerManageDocument($studentId)
    {
        $collegeId = Auth::user()->college_id;
        $objRtoapplicationRefID = new Students;
        $applicationRefID = $objRtoapplicationRefID->getExistsReferenceId($studentId, $collegeId);

        $checkEditCondition = Students::select('college_id')->where('id', $studentId)->get()->toArray();
        $editCollegeId = $checkEditCondition[0]['college_id'];

        $data['pagetitle'] = 'Application List';
        $data['plugincss'] = [];
        $data['css'] = [];
        $data['pluginjs'] = [];
        $data['js'] = ['offermanage.js'];
        $data['funinit'] = ['OfferManage.initOfferLetter()'];
        $data['activateValue'] = 'Offers';
        $data['applicationRefID'] = $applicationRefID;
        $data['studentId'] = $studentId;
        $data['mainmenu'] = 'clients';

        if ($editCollegeId == $collegeId) {
            return view('frontend.student.offer-manage-document', $data);
        } else {
            return view('frontend.error_page.unauthorized', $data);
        }
    }

    public function offerStudentDocumentOLD(Request $request)
    {

        $perPage = Config::get('constants.pagination.perPage');
        $collegeId = Auth::user()->college_id;
        $userId = Auth::user()->id;

        $folderName = $request->folder;
        $id = $request->id;
        $primaryStudentId = $request->id;
        $flag = $request->flag;
        // check folder is exist or not in college Martial Table if not then create it.
        $objRtoCollegeMaterials = new CollegeMaterials;

        $checkFolder = $objRtoCollegeMaterials->checkModuleFolder($folderName, $collegeId);

        $checkFolderId = $objRtoCollegeMaterials->checkModuleFolderId($id, $collegeId);

        if (empty($checkFolder)) {

            // create folder and save record in college martial table
            $arrCreateFolder = $objRtoCollegeMaterials->saveModuleFolderCollegeMaterials($folderName, $collegeId, $request);
            //  }

            $checkModuleFolder = $objRtoCollegeMaterials->checkModuleIdFolder($id, $folderName, $collegeId);

            $arrCreateFolder = $objRtoCollegeMaterials->saveModuleIdFolderCollegeMaterials($id, $folderName, $collegeId, $request);
            $id = $arrCreateFolder;
        } else {

            if ($flag == 1) {
                // $checkModuleFolder = $objRtoCollegeMaterials->getModuleIdWiseParentIdList($id, $folderName, $collegeId, $perPage);

                if (! empty($checkFolderId) && $checkFolderId[0]['upperParentId'] != 0) {
                    $arrCreateFolder = $objRtoCollegeMaterials->saveModuleIdFolderCollegeMaterials($id, $folderName, $collegeId, $request);
                    $id = $arrCreateFolder;
                }
                if (empty($checkFolderId)) {
                    $arrCreateFolder = $objRtoCollegeMaterials->saveModuleIdFolderCollegeMaterials($id, $folderName, $collegeId, $request);
                    $id = $arrCreateFolder;
                }
            }
        }
        if (! empty($checkFolderId)) {
            $id = $checkFolderId[0]['id'];
        }

        return redirect(route('offer-manage-document', ['id' => $id, 'primaryStudentId' => $primaryStudentId]));
    }

    public function listStudentDocument(Request $request)
    {

        // echo $request->student_id;exit;

        $perPage = Config::get('constants.pagination.perPage');
        $collegeId = Auth::user()->college_id;
        $userId = Auth::user()->id;
        $parent_id = 0;

        // ***********************Start*********************************//
        $studentId = $request->student_id;
        $parentId = $request->parent_id;

        $objStudentUploadFile = new StudentUploadFile;
        $arrStudentUploadedFile = $objStudentUploadFile->getStudentUploadFileList($collegeId, $studentId, $parentId);

        $backMenuId = $objStudentUploadFile->getParentId($parentId, $collegeId);

        if ($request->isMethod('post')) {

            if (empty($request->input('folder_or_file')) && empty($request->file())) {
                $validations['folder_or_file'] = 'required';
                $checkValidations = [
                    'folder_or_file.required' => 'Please Create any folder OR Upload Document(s).',
                ];
                $validator = Validator::make($request->all(), $validations, $checkValidations);

                if ($validator->fails()) {
                    return redirect(route('list-student-document', ['student_id' => $studentId, 'parent_id' => $parentId]))->withErrors($validator)->withInput();
                }
            }

            $fileForValidate = $request->file();

            if ($request->file()) {
                $isValidSize = true;
                $isValidType = true;
                foreach ($fileForValidate['images'] as $tmpFile) {
                    $tmpFile = $fileForValidate['images'][0];
                    if ($isValidSize) {
                        $tmpSize = $tmpFile->getSize();
                        $tmpSize = number_format($tmpSize / 51048576, 2);
                        if ($tmpSize > 5) {
                            $isValidSize = false;
                        }
                    }

                    if ($isValidType) {
                        $arr = ['jpg', 'jpeg', 'png', 'pdf', 'doc', 'gif', 'msg', 'bin'];
                        $tmpType = $tmpFile->guessClientExtension();

                        if (! in_array(strtolower($tmpType), $arr)) {
                            $isValidType = false;
                        }
                    }
                }

                if (! $isValidType || ! $isValidSize) {
                    $errorArr = [];
                    (! $isValidType) ? $errorArr[] = 'Please Select Valid File ( .pdf , .jpg , .jpeg , .gif, .msg)' : '';
                    (! $isValidSize) ? $errorArr[] = 'The maximum file upload size limit is 50MB.' : '';

                    return redirect(route('list-student-document', ['student_id' => $studentId, 'parent_id' => $parentId]))->withErrors($errorArr)->withInput();
                }
            }

            $objStudentUploadFile = new StudentUploadFile;
            $result = $objStudentUploadFile->studentFileUpload($request, $collegeId, $studentId, $userId, $parentId);

            if ($result == 'file') {
                $request->session()->flash('session_success', 'Student Documents Upload Successfully.');
            } elseif ($result == 'folder') {
                $request->session()->flash('session_success', 'Root directory Add Successfully.');
            } else {
                $request->session()->flash('session_error', 'Something will be wrong. Please try again.');
            }

            return redirect(route('list-student-document', ['student_id' => $studentId, 'parent_id' => $parentId]));
        }

        $data['header'] = [
            'title' => "Application Document Upload Page for Offer ID:( $studentId )",
            'breadcrumb' => [
                'Home' => route('user_dashboard'),
                'Offer manage' => route('offer-manage'),
                'Apply Online' => '',
            ]];
        $data['pagetitle'] = 'Student Document';
        $data['plugincss'] = [];
        $data['css'] = ['frontendcomman.css'];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js'];
        $data['js'] = ['student.js'];
        $data['funinit'] = ['Student.initOfferStudentDoc()'];

        $data['collageMaterialsData'] = $arrStudentUploadedFile;
        $data['userId'] = $userId;
        $data['studentId'] = $studentId;
        $data['mainmenu'] = 'clients';

        // echo "hi";exit;
        return view('frontend.student.view-student-document-list', $data);
    }

    public function offerManageEdit($courseID, $studentId, Request $request)
    {

        $collegeId = Auth::user()->college_id;

        $objStudentCources = new StudentCourse;
        $studentId = $objStudentCources->getStudentID($courseID);
        $courseData = $objStudentCources->getStudentCourseData($courseID);
        $arrOfferIdData = $objStudentCources->getOfferIdListArr($studentId);

        $arrCourseStatus = Config::get('constants.arrCourseStatus');
        $arrModeQfDelivery = Config::get('constants.arrModeQfDelivery');
        $arrIntakeYearData = Config::get('constants.arrIntakeYear');

        $objRtoAgent = new Agent;
        $arrAgentData = $objRtoAgent->getAgentList($collegeId);

        $objCourseData = new Courses;
        $arrCourseListData = $objCourseData->getFullCoursesArr($collegeId);
        $arrResultsCalculationMethod = Config::get('constants.arrResultsCalculationMethod');

        if (count($courseData)) {
            $courseId = $courseData[0]['course_id'];
            $objCourseTemplate = new CourseTemplate;
            $arrCourseTemplateData = $objCourseTemplate->getCourseWiseTemplateList($collegeId, $courseId);
        } else {
            $arrCourseTemplateData = ['' => 'No Template Found'];
        }

        $objRtoCollegeCampus = new CollegeCampus;
        $arrCampusData = $objRtoCollegeCampus->getCollegeCampusList($collegeId);

        $objRtoStudyReason = new StudyReason;
        $arrStudyreasonData = $objRtoStudyReason->getStudyReasonList();

        $objCourseIntakeDate = new CoursesIntakeDate;
        $arrIntakeDateList = $objCourseIntakeDate->getIntakeDateList($courseData[0]['course_id'], $courseData[0]['intake_year']);

        $objStaff = new Staff;
        $arrStaffData = $objStaff->getStaffNameList($collegeId);

        $arrCourseManagerData[''] = '- - Select Course Manager - -';
        $courseManagerList = $arrCourseManagerData + $arrStaffData;

        $arrPlacementData[''] = '- - Select Placement Manager - -';
        $placementManagerList = $arrPlacementData + $arrStaffData;

        $checkEditCondition = Students::select('college_id', 'student_type')->where('id', $studentId)->get()->toArray();
        $editCollegeId = $checkEditCondition[0]['college_id'];
        $studentType = $checkEditCondition[0]['student_type'];
        $data['studentType'] = $studentType;

        if ($studentType == 'Offshore') {
            $data['arrModeQfDelivery'] = ['2' => 'oncampus'];
        } elseif ($studentType == 'Onshore') {
            $data['arrModeQfDelivery'] = ['2' => 'oncampus'];
        } elseif ($studentType == 'Domestic') {
            $data['arrModeQfDelivery'] = $arrModeQfDelivery;
        }

        if ($request->isMethod('post')) {
            $validations = [
                'campus_id' => 'required',
                'agent_id' => 'required',
            ];

            $validator = Validator::make($request->all(), $validations);
            if ($validator->fails()) {
                return redirect(route('student-course-edit', ['id' => $courseID]))
                    ->withErrors($validator)
                    ->withInput();
            }
            $studentCourse = new StudentCourse;
            $studentCourse->updateStudentCourseV2($courseID, $request);

            $request->session()->flash('session_success', 'Student Course Update successfully.');

            return redirect(route('offer-manage-confirmation', $studentId));
        }

        $data['pagetitle'] = 'Apply Online';
        $data['plugincss'] = [];
        $data['css'] = ['frontendcomman.css'];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js', 'ckeditor/ckeditor.js'];
        $data['js'] = ['studentCourse.js'];
        $data['funinit'] = ['studentCourse.edit_init()'];
        $data['activateValue'] = 'Offers';

        $data['arrAgentData'] = $arrAgentData;
        $data['arrOfferIdData'] = $arrOfferIdData;
        $data['arrCourseListData'] = $arrCourseListData;
        $data['arrIntakeYearData'] = $arrIntakeYearData;
        $data['arrIntakeDateList'] = $arrIntakeDateList;
        $data['arrCampusData'] = $arrCampusData;
        $data['courseManagerList'] = $courseManagerList;
        $data['placementManagerList'] = $placementManagerList;

        $data['arrStudyreasonData'] = $arrStudyreasonData;
        $data['arrCourseStatus'] = $arrCourseStatus;
        $data['arrResultCalMethod'] = $arrResultsCalculationMethod;
        $data['arrCourseTemplate'] = $arrCourseTemplateData;
        $data['courseData'] = $courseData[0];
        $data['mainmenu'] = 'clients';

        if ($editCollegeId == $collegeId) {
            return view('frontend.offer_manage.offer-manage-edit', $data);
        } else {
            return view('frontend.error_page.unauthorized', $data);
        }
    }

    public function offerManageChecklist($studentId, Request $request)
    {

        $collegeId = Auth::user()->college_id;
        $userId = Auth::user()->id;

        $objRtoapplicationRefID = new Students;
        $applicationRefID = $objRtoapplicationRefID->getExistsReferenceId($studentId, $collegeId);

        $arrDocumentType = Config::get('constants.arrDocumentType');

        $objStudentType = $objRtoapplicationRefID->getStudentType($studentId);

        $studentType = $objStudentType[0]->student_type;

        $objRtoOfferDocumentChecklist = new OfferDocumentChecklist;
        $objDocumentNames = $objRtoOfferDocumentChecklist->getOfferDocumentName($collegeId, $studentType);

        $objRtoStudentOfferDocument = new StudentOfferDocuments;
        $objStudentDocument = $objRtoStudentOfferDocument->getStudentDocument($studentId);
        // echo "<pre/>";print_r($objStudentDocument);exit;
        $checkEditCondition = Students::select('college_id')->where('id', $studentId)->get()->toArray();
        $editCollegeId = $checkEditCondition[0]['college_id'];

        if ($request->input('goBack') && $request->input('goBack') === 'Go Back') {
            return redirect(route('offer-manage'));
        }

        if ($request->isMethod('post') || $request->file()) {
            $errorArr = [];
            $fileForValidate = $request->file();
            if ($request->file()) {
                $isValidSize = true;
                $isValidType = true;
                foreach ($fileForValidate as $tmpFile) {

                    if ($isValidSize) {
                        $tmpSize = $tmpFile->getSize();
                        $tmpSize = number_format($tmpSize / 2048576, 2);
                        if ($tmpSize > 5) {
                            $isValidSize = false;
                        }
                    }
                    if ($isValidType) {
                        $arr = ['png', 'pdf', 'jpg', 'jpeg', 'gif'];
                        $tmpType = $tmpFile->guessClientExtension();

                        if (! in_array(strtolower($tmpType), $arr)) {
                            $isValidType = false;
                        }
                    }
                }

                if (! $isValidType || ! $isValidSize) {

                    (! $isValidType) ? $errorArr[] = 'Please Select Valid File ( .png , .pdf , .jpg ,.jpeg ,.gif)' : '';
                    (! $isValidSize) ? $errorArr[] = 'The maximum file upload size limit is 20MB.' : '';

                    return redirect(route('offer-manage-checklist', ['id' => $studentId]))
                        ->withErrors($errorArr)
                        ->withInput();
                }
            }

            if ($request->input('total_count') == 0) {
                $request->session()->flash('session_error', 'Document Not Selected ');

                return redirect(route('offer-manage-checklist', ['id' => $studentId]));
            }
            $objRtoStudentOfferDocument = new StudentOfferDocuments;
            $studentOfferDocument = $objRtoStudentOfferDocument->updateStudentDocumentsField($collegeId, $userId, $studentId, $request);
            // $studentOfferDocument = $objRtoStudentOfferDocument->updateStudentDocumentsField($studentId, $request, $userId);

            if ($request->input('submitApplication') && $request->input('submitApplication') === 'Submit Application') {
                $request->session()->flash('session_success', 'Offer Manage Checklist Saved Successfully');

                return redirect(route('offer-manage'));
            }
        }
        $arrStudentDocumentId = [];
        $arrStudentImage = [];
        $arrStudentMaterial = [];
        $arrStudentComment = [];
        $arrStudentLastChecked = [];
        $arrStudentLastUpdated = [];
        for ($i = 0; $i < count($objStudentDocument); $i++) {
            $documentChecklistId = $objStudentDocument[$i]->rto_offer_document_checklist_id;
            $arrStudentDocumentId[$documentChecklistId] = $objStudentDocument[$i]->id;
            $arrStudentImage[$documentChecklistId] = $objStudentDocument[$i]->file_name;
            $arrStudentMaterial[$documentChecklistId] = $objStudentDocument[$i]->document_material_id;
            $arrStudentApproved[$documentChecklistId] = $objStudentDocument[$i]->approved;
            $arrStudentComment[$documentChecklistId] = $objStudentDocument[$i]->comment;
            $arrStudentLastChecked[$documentChecklistId] = $objStudentDocument[$i]->name;
            $arrStudentLastUpdated[$documentChecklistId] = date('d/m/Y h:i:s A', strtotime($objStudentDocument[$i]->updated_at));
        }

        $arrStudentDocument = [];
        for ($i = 0; $i < count($objDocumentNames); $i++) {

            $arrStudentDocument[$i]['id'] = $objDocumentNames[$i]->id;
            $arrStudentDocument[$i]['document_name'] = $objDocumentNames[$i]->document_name;
            $arrStudentDocument[$i]['document_type'] = $objDocumentNames[$i]->document_type;
            $arrStudentDocument[$i]['is_compulsory'] = $objDocumentNames[$i]->is_compulsory;
            $arrStudentDocument[$i]['is_active'] = $objDocumentNames[$i]->is_active;
            $arrStudentDocument[$i]['student_origin'] = $objDocumentNames[$i]->student_origin;

            $arrStudentDocument[$i]['student_document_id'] = isset($arrStudentDocumentId[$objDocumentNames[$i]->id]) ? $arrStudentDocumentId[$objDocumentNames[$i]->id] : '';
            $arrStudentDocument[$i]['file_name'] = isset($arrStudentImage[$objDocumentNames[$i]->id]) ? $arrStudentImage[$objDocumentNames[$i]->id] : '';
            $arrStudentDocument[$i]['document_material_id'] = isset($arrStudentMaterial[$objDocumentNames[$i]->id]) ? $arrStudentMaterial[$objDocumentNames[$i]->id] : '';
            $arrStudentDocument[$i]['comment'] = isset($arrStudentComment[$objDocumentNames[$i]->id]) ? $arrStudentComment[$objDocumentNames[$i]->id] : '';
            $arrStudentDocument[$i]['approved'] = isset($arrStudentApproved[$objDocumentNames[$i]->id]) ? $arrStudentApproved[$objDocumentNames[$i]->id] : '';
            $arrStudentDocument[$i]['last_checked_by'] = isset($arrStudentLastChecked[$objDocumentNames[$i]->id]) ? $arrStudentLastChecked[$objDocumentNames[$i]->id] : '';
            $arrStudentDocument[$i]['last_updated'] = isset($arrStudentLastUpdated[$objDocumentNames[$i]->id]) ? $arrStudentLastUpdated[$objDocumentNames[$i]->id] : '';
        }

        $trueFlaseArray = [];
        for ($i = 0; $i < count($objDocumentNames); $i++) {
            if (isset($arrStudentImage[$objDocumentNames[$i]->id])) {
                $trueFlaseArray[$objDocumentNames[$i]->id] = 0;
            } else {
                if ($objDocumentNames[$i]->is_compulsory == 1) {
                    $trueFlaseArray[$objDocumentNames[$i]->id] = 1;
                } else {
                    $trueFlaseArray[$objDocumentNames[$i]->id] = 0;
                }
            }
        }
        $data['header'] = [
            'title' => 'Offer manage Checklist',
            'breadcrumb' => [
                'Home' => route('user_dashboard'),
                'Offer manage' => route('offer-manage'),
                'Apply Online' => '',
            ]];
        $data['pagetitle'] = 'Application List';
        $data['plugincss'] = [];
        $data['css'] = [];
        $data['pluginjs'] = [];
        $data['js'] = ['offermanage.js'];
        $data['funinit'] = ['OfferManage.initOfferManageChecklist()'];
        $data['activateValue'] = 'Offers';
        $data['applicationRefID'] = $applicationRefID;
        $data['objDocumentNames'] = $objDocumentNames;
        $data['arrDocumentType'] = $arrDocumentType;
        $data['trueFlaseArray'] = $trueFlaseArray;
        $data['arrStudentDocument'] = $arrStudentDocument;
        $data['studentId'] = $studentId;
        $data['mainmenu'] = 'clients';

        if ($editCollegeId == $collegeId) {
            return view('frontend.student.offer-manage-checklist', $data);
        } else {
            return view('frontend.error_page.unauthorized', $data);
        }
    }

    public function offerManageSendMail($studentId, Request $request)
    {
        // DD($request->all());
        $collegeId = Auth::user()->college_id;
        $objCollegesMail = new Colleges;
        $arrFromMail = $objCollegesMail->getCollegeMail($collegeId);

        $fromMail = [Auth::user()->email => Auth::user()->email];

        $objRtoEmailTemplate = new EmailTemplate;
        $arrEmailTemplate = $objRtoEmailTemplate->getEmailTemplateType($collegeId);

        $objRtoStudentCourse = new StudentCourse;
        $arrStudentCourse = $objRtoStudentCourse->getStudentAppliedOfferCourseName($studentId);

        $objStdentAgentName = $objRtoStudentCourse->getStudentAgentName($studentId);

        $objRtoapplicationRefID = new Students;
        $applicationRefID = $objRtoapplicationRefID->getExistsReferenceId($studentId, $collegeId);

        if ($objStdentAgentName->count() > 0) {
            $objStdentAgentName = $objStdentAgentName;
        } else {
            $objStdentAgentName = [];
        }

        $checkEditCondition = Students::select('college_id')->where('id', $studentId)->get()->toArray();
        $editCollegeId = $checkEditCondition[0]['college_id'];

        $arrRecipient = Config::get('constants.arrRecipient');

        if ($request->isMethod('post')) {
            $getStudentCOEDetail = $objRtoStudentCourse->getStudentCoursesData($request->input('student_id'));

            if ($request->input('offer_letter')) {
                $validator = Validator::make($request->all(), [
                    'course_id' => 'required',
                    'email_from' => 'required|email',
                    'email_to' => 'required',
                    'email_cc' => 'nullable',
                    'email_bcc' => 'nullable|sometimes|email',
                    'email_subject' => 'required',
                    'email_content' => 'required',
                ]);
            } else {
                $validator = Validator::make($request->all(), [
                    'email_from' => 'required|email',
                    'email_to' => 'required',
                    'email_cc' => 'nullable',
                    'email_bcc' => 'nullable|sometimes|email',
                    'email_subject' => 'required',
                    'email_content' => 'required']);
            }

            if ($validator->fails()) {
                return redirect(route('offer-manage-send-mail', ['id' => $studentId]))->withErrors($validator)->withInput();
            }

            $fileForValidate = $request->file();

            if ($fileForValidate) {
                $isValidSize = true;
                $isValidType = true;
                $arr = ['pdf', 'jpg', 'jpeg', 'png', 'gif'];

                foreach ($fileForValidate as $key => $tmpFile) {
                    $tmpSize = $tmpFile->getSize();
                    $tmpSize = number_format($tmpSize / 1048576, 2);
                    $tmpType = $tmpFile->guessClientExtension();
                    if ($tmpSize > 5) {
                        $isValidSize = false;
                    }

                    if (! in_array(strtolower($tmpType), $arr)) {
                        $isValidType = false;
                    }
                }

                if (! $isValidType || ! $isValidSize) {
                    $errorArr = [];
                    (! $isValidType) ? $errorArr[] = 'Please Select Valid File ( .pdf, .jpg, .jpeg, .png, .gif)' : '';
                    (! $isValidSize) ? $errorArr[] = 'The maximum file upload size limit is 5MB.' : '';

                    return redirect(route('offer-manage-send-mail', ['id' => $studentId]))->withErrors($errorArr)->withInput();
                }
            }

            $email_to = $request->email_to;
            $email_cc_array = ! empty($request->input('email_cc')) ? explode(',', $request->input('email_cc')) : '';
            $email_cc = is_array($email_cc_array) ? array_map('trim', $email_cc_array) : trim($email_cc_array);
            $email_bcc = ! empty($request->input('email_bcc')) ? $request->input('email_bcc') : '';
            $status = true;
            $returnFiles = true;

            $is_payment_invoice = ($request->input('student_invoice')) ? $request->input('student_invoice') : '0';
            $is_offer_letter = ($request->input('offer_letter')) ? $request->input('offer_letter') : '0';
            $is_attach_coe = ($request->input('attach_coe')) ? $request->input('attach_coe') : '0';

            $savedFileName = [];
            $logAttachmentFiles = [];
            $emailContentId = $request->input('email_template');

            $tempFilePath = Config::get('constants.uploadFilePath.Templates');
            $templateFilePath = Helpers::changeRootPath($tempFilePath, $emailContentId);
            $filePath = Config::get('constants.uploadFilePath.StudentMailAttach');
            $destinationPath = Helpers::changeRootPath($filePath, $studentId);

            $invoicePdf = $offerLetterPdf = '';
            $download = 'save';
            if ($is_payment_invoice > 0) {
                $courseId = $request->input('course_id');
                $fileName = $this->studentInvoicePdf($courseId, $studentId, $download, $request);

                $invoicePdf = $destinationPath['view'].$fileName;
                $savedFileName[] = $invoicePdf;
                $logAttachmentFiles[] = '<a href="'.url($destinationPath['view'].$fileName).'" title="'.$fileName.'">invoice.pdf </a>';

            }

            //  offer letter pdf
            if ($is_offer_letter > 0) {
                $courseId = $request->input('course_id');

                $arrStudentCourseDetails = $objRtoStudentCourse->getStudentCourseDetails($courseId, $studentId);

                $studentCourseID = $arrStudentCourseDetails->id;

                if (isset($studentCourseID)) {
                    $this->offerLetterPdf($courseId, $studentId, $studentCourseID, $download, $request);

                    $studentOfferLetterName = $this->studentOfferLetterName($studentId);
                    $offerLetterPdf = $destinationPath['view'].$studentOfferLetterName.'.pdf';
                    $savedFileName[] = $offerLetterPdf;

                    $logAttachmentFiles[] = '<a href="'.url($destinationPath['view'].$studentOfferLetterName.'.pdf').'" title="'.$studentOfferLetterName.'.pdf'.'">'.$studentOfferLetterName.'.pdf'.'</a>';

                }
            }

            if ($is_attach_coe > 0) {
                $filePath = Config::get('constants.uploadFilePath.StudentCOE');
                $destinationPath = Helpers::changeRootPath($filePath, $studentId);
                foreach ($getStudentCOEDetail as $key => $value) {
                    if (isset($value->coe_image) && ! empty($value->coe_image)) {
                        // if (file_exists($destinationPath['default'] . $value->coe_image)) {
                        $COEImage = $destinationPath['view'].$value->coe_image;
                        $savedFileName[] = $COEImage;
                        $logAttachmentFiles[] = '<a href="'.url($destinationPath['view'].$value->coe_image).'" title="'.$value->coe_image.'">'.$value->coe_image.'</a>';
                        // }
                    }
                }
            }

            $objRtoStudentEmail = new StudentEmail;

            $resultArr = $objRtoStudentEmail->saveStudentMail($studentId, $request, $email_to, $status, $returnFiles, $logAttachmentFiles);

            if ($returnFiles == 'true' && ! empty($resultArr)) {
                foreach ($resultArr as $file) {
                    $savedFileName[] = $file;
                    // $savedFileName[] = $destinationPath . $file;
                }
            }

            $email_attachment = ($request->input('email_attachment')) ? $request->input('email_attachment') : [];

            for ($i = 0; $i < count($email_attachment); $i++) {
                $savedFileName[] = $templateFilePath['view'].$email_attachment[$i];
            }
            // dd($savedFileName);
            // $agentEmail = $objStdentAgentName[0]['primary_email'];

            if ($request->input('send_to') == 'student') {
                $mailSendAddress = trim($applicationRefID[0]['email']);
            } elseif ($request->input('send_to') == 'agent') {
                $mailSendAddress = trim($objStdentAgentName[0]['primary_email']);
            } else {
                $mailSendAddress = [];
                $mailSendAddress = [trim($objStdentAgentName[0]['primary_email']), trim($applicationRefID[0]['email'])];
            }

            $dataArray = $request->input('email_content');
            $emailContentId = $request->input('email_template');
            $courseId = $request->input('course_id');

            $replacedContent = $request->input('email_content');
            $mailData = [
                'from' => env('MAIL_USERNAME'),
                'fromName' => env('MAIL_NAME'),
                'to' => $mailSendAddress,
                'cc' => $email_cc,
                'bcc' => trim($email_bcc),
                'page' => 'mail.student-offer-email',
                'subject' => $request->input('email_subject'),
                'attachFile' => $savedFileName,
                'data' => ['content' => $replacedContent],
            ];
            // dd($mailData);
            $sendMail = new SendMail;
            $result = $sendMail->sendSmtpMail($mailData);
            if ($result == 'success') {
                $request->session()->flash('session_success', 'Student Offer Manage Email sent successfully');
                // if ($is_payment_invoice > 0 && file_exists($invoicePdf)) {
                //     unlink($invoicePdf);
                // }
                // if ($is_offer_letter > 0 && file_exists($offerLetterPdf)) {
                //     unlink($offerLetterPdf);
                // }
            } else {
                $request->session()->flash('session_error', 'Something will be wrong. Please try again');
            }

            return redirect(route('offer-manage-send-mail', ['id' => $studentId]));
        }
        $data['header'] = [
            'title' => 'Student Offer Manage Send Email',
            'breadcrumb' => [
                'Home' => route('user_dashboard'),
                'Offer manage' => route('offer-manage'),
                'Send Mail' => '',
            ]];
        $data['pagetitle'] = 'Student Offer manage Send Email';
        $data['plugincss'] = [];
        $data['css'] = [];
        $data['pluginjs'] = ['ckeditor/ckeditor.js', 'jQuery/jquery.validate.min.js'];
        $data['js'] = ['offermanage.js'];
        $data['funinit'] = ['OfferManage.initSendEmail()'];
        $data['activateValue'] = 'Offers';

        $data['fromMail'] = $fromMail;
        $data['arrFromMail'] = $arrFromMail;
        $data['arrRecipient'] = $arrRecipient;
        $data['arrEmailTemplate'] = $arrEmailTemplate;
        $data['arrStudentCourse'] = $arrStudentCourse;
        $data['objStdentAgentName'] = $objStdentAgentName;
        $data['applicationRefID'] = $applicationRefID;
        $data['studentId'] = $studentId;
        $data['mainmenu'] = 'clients';

        if ($editCollegeId == $collegeId) {
            return view('frontend.student.offer-manage-send-mail', $data);
        } else {
            return view('frontend.error_page.unauthorized', $data);
        }
    }

    public function offerManageCommunicationLog(Request $request)
    {

        $studentId = $request->id;
        $college_id = Auth::user()->college_id;
        $userId = $login_id = Auth::user()->id;

        $perPage = Config::get('constants.pagination.perPage');

        $typeId = '4'; // for Diary-Offer Type List
        $statusId = '5'; // for Diary-Offer Status List
        $objSetupSection = new SetupSection;
        $arrType = $objSetupSection->getSectionTypeList($college_id, $typeId);
        $arrStatus = $objSetupSection->getSectionTypeList($college_id, $statusId);

        $objStudentCommunication = new StudentCommunicationLog;
        $studentName = $objStudentCommunication->getStudentData($studentId);
        $userName = $objStudentCommunication->getUsersData($login_id);

        $arrStudCommList = $objStudentCommunication->getStudentCommunicationData($perPage, $studentId, $college_id, $userId);

        if ($request->isMethod('post')) {

            $validator = Validator::make($request->all(), [
                'log' => 'required',
                'type' => 'required',
                'status' => 'required',
            ]);

            if ($validator->fails()) {
                return redirect(route('offer-manage-communication-log', ['id' => $studentId]))
                    ->withErrors($validator)
                    ->withInput();
            }
            $objStudentCommunication->addStudentCommunicationLog($request, $college_id, $studentId, $login_id, $userId);
            $request->session()->flash('session_success', 'Student Communication Log is Saved Successfully.');

            return redirect(route('offer-manage-communication-log', ['id' => $studentId]));
        }

        $data['header'] = [
            'title' => 'Student Communication Log',
            'breadcrumb' => [
                'Home' => route('user_dashboard'),
                'Offer manage' => route('offer-manage'),
                'Apply Online' => '',
            ]];
        $data['pagetitle'] = 'Student Communication log';
        $data['plugincss'] = ['bootstrap-wysihtml5/bootstrap3-wysihtml5.css'];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js', 'ckeditor/ckeditor.js', 'bootstrap-wysihtml5/bootstrap3-wysihtml5.all.min.js'];
        $data['css'] = [];
        $data['js'] = ['studentprofile.js'];
        $data['funinit'] = ['studentProfile.initCommunicationLog()'];
        $data['activateValue'] = 'Offers';
        $data['userName'] = $userName;
        $data['studentName'] = $studentName;
        $data['arrStatus'] = $arrStatus;
        $data['arrType'] = $arrType;
        $data['arrStudCommList'] = $arrStudCommList;
        // $data['arrStudEmail'] = $arrStudCommList;
        $data['studentId'] = $studentId;
        $data['pagination'] = $arrStudCommList->links();
        $data['mainmenu'] = 'clients';

        return view('frontend.student.student-commnunication-view', $data);
    }

    public function offerManageCommunicationLogEdit($studentCommunicationId, $visiblity, $studentId, Request $request)
    {

        $objStudentCommunicationLog = new StudentCommunicationLog;
        $objStudentCommunicationLog->editStudentCommunicationLog($studentCommunicationId, $visiblity);

        $request->session()->flash('session_success', 'offer Manage Communication Log Edit SuccessFully.');

        return redirect(route('offer-manage-communication-log', ['id' => $studentId]));
    }

    public function offerManageUpfrontFeeScheduleNew($studentCourseId, Request $request)
    {

        $collegeId = Auth::user()->college_id;

        $arrInstallmentDuration = Config::get('constants.arrInstallmentDuration');

        // get upfront fee schedule for student
        $objRtoStudentCourses = new StudentCourse;
        $studCourseData = $objRtoStudentCourses->getStudentUpfrontFeeScheduleNew($studentCourseId);

        $studentId = $studCourseData->student_id;
        $courseId = $studCourseData->course_id;
        $agentId = $studCourseData->agent_id;

        $getStudentDetail = Students::where('id', $studentId)->get(['name_title', 'first_name', 'middel_name', 'family_name', 'id', 'is_student'])->first()->toArray();

        $getPaidAmount = StudentInitialPaymentDetails::where('college_id', '=', $collegeId)->where('student_id', '=', $studentId)->where('student_course_id', '=', $studentCourseId)->where('course_id', '=', $courseId)->where('payment_type', 'Schedual')->get([DB::raw('SUM(upfront_fee_pay) as total_fee_paid')])->toArray();

        $getInitialPaidAmount = StudentInitialPaymentDetails::where('college_id', '=', $collegeId)->where('student_id', '=', $studentId)->where('student_course_id', '=', $studentCourseId)->where('course_id', '=', $courseId)->where('payment_type', 'Initial')->get([DB::raw('SUM(upfront_fee_pay) as total_fee_paid')])->toArray();

        $totalFeePaid = (! empty($getPaidAmount) && isset($getPaidAmount[0]['total_fee_paid'])) ? $getPaidAmount[0]['total_fee_paid'] : 0;
        $totalInitialFeePaid = (! empty($getInitialPaidAmount) && isset($getInitialPaidAmount[0]['total_fee_paid'])) ? $getInitialPaidAmount[0]['total_fee_paid'] : 0;

        // agent commision
        $objAgentCommissionList = new AgentCommission;
        $arrAgentCommissionListData = $objAgentCommissionList->getAgentCommisionData($collegeId, $agentId, $courseId);

        $commission = isset($arrAgentCommissionListData[0]) ? $arrAgentCommissionListData[0]['commission'] : 0;
        $gst = isset($arrAgentCommissionListData[0]) ? $arrAgentCommissionListData[0]['gst'] : 'NO GST';

        $installmentStartDateCount = $objRtoStudentCourses->startDateCalculation($studCourseData->total_weeks, $studCourseData->course_fee, $studCourseData->course_upfront_fee, $studCourseData->start_date);
        // get saved or scheduled upfront fee
        $objRtoOfferUpfrontFeeSchedule = new OfferUpfrontFeeSchedule;
        $objGetUpfrontFeeSchedule = $objRtoOfferUpfrontFeeSchedule->getUpfrontFeeScheduleNew($studentCourseId);

        $invoiceSettingData = InvoiceSetting::getValueFromKey(InvoiceSetting::DAYS_AFTER_START_DATE) ?? 0;

        if ($request->isMethod('post')) {
            $validator = Validator::make($request->all(), [
                'installment_start_date' => 'required',
                'no_of_installment' => 'required|integer',
                'frequency' => 'required|integer',
                'duration' => 'required',
            ]);

            if ($validator->fails()) {
                return redirect(route('offer-manage-upfront-fee-schedule-new', ['studentCourseId' => $studentCourseId, 'redirect' => (($request->input('redirect')) ? $request->input('redirect') : '')]))
                    ->withErrors($validator)
                    ->withInput();
            }

            // if ($request->copyScheduleInvoice == 1) {
            $isExistSchedule = StudentInitialPaymentDetails::whereIn('payment_status', ['paid', 'partially paid'])
                ->where(['payment_type' => 'Schedual', 'student_course_id' => $studentCourseId])
                ->count();
            if ($isExistSchedule > 0) {
                $validator = 'Schedule Already Exists and Paid';

                return redirect(route('offer-manage-upfront-fee-schedule-new', ['studentCourseId' => $studentCourseId, 'redirect' => (($request->input('redirect')) ? $request->input('redirect') : '')]))
                    ->withErrors($validator)
                    ->withInput();
            }
            // }

            $startDate = date('Y-m-d', strtotime($studCourseData->start_date));
            $endDate = date('Y-m-d', strtotime($studCourseData->finish_date));

            $dataArr = [
                'college_id' => $collegeId,
                'student_id' => $studentId,
                'course_id' => $courseId,
                'student_course_id' => $studentCourseId,
            ];

            $noOfInstallment = $request->input('no_of_installment');
            $frequency = $request->input('frequency');
            $duration = $request->input('duration');
            $installmentDate = date('Y-m-d', strtotime($request->input('installment_start_date')));
            $dueDateDays = $request->input('due_date_day');

            /*$dueDateStep = "+" . ($frequency * $noOfInstallment) . " " . $duration;
            $installmentDueDate = date("Y-m-d", strtotime($dueDateStep, strtotime($installmentDate)));*/

            $lastInstallmentStep = '+'.($frequency * ($noOfInstallment - 1)).' '.$duration;
            $lastInstallmentStartDate = date('Y-m-d', strtotime($lastInstallmentStep, strtotime($installmentDate)));
            $lastInstallmentDueDate = date('Y-m-d', strtotime("+ $dueDateDays Days", strtotime($lastInstallmentStartDate)));

            // check installment_start_date should be between start and finish date
            // if ($installmentDate >= $startDate && $lastInstallmentDueDate >= $startDate && $installmentDate <= $endDate && $lastInstallmentDueDate <= $endDate) {
            if ($installmentDate <= $endDate && $lastInstallmentDueDate <= $endDate) {

                $remainFee = $studCourseData->course_fee - $totalFeePaid - ($studCourseData->course_upfront_fee);

                // check and redirect if remaining fee is less than 1
                if ($remainFee < 1) {
                    $validator = 'Cannot generate! Payment Schedule. Remaining amount is 0';

                    return redirect(route('offer-manage-upfront-fee-schedule-new', ['studentCourseId' => $studentCourseId, 'redirect' => (($request->input('redirect')) ? $request->input('redirect') : '')]))
                        ->withErrors($validator)
                        ->withInput();
                }

                // TODO::GNG-2457 (Ensure that the invoice is synchronized; if it is synced, verify that the status is "draft"; otherwise, refrain from removing the invoice.)
                $canOverrideInvoice = $this->validateExistingInvoice($dataArr);
                if (! $canOverrideInvoice) {
                    $validator = 'Cannot generate! Schedule invoice already paid/awaiting.';

                    return redirect(route('offer-manage-upfront-fee-schedule-new', ['studentCourseId' => $studentCourseId, 'redirect' => (($request->input('redirect')) ? $request->input('redirect') : '')]))
                        ->withErrors($validator)
                        ->withInput();
                }

                // dividation
                $installmentFee = round($remainFee / $noOfInstallment, 2);

                $installmentStartDate = strtotime($request->input('installment_start_date'));
                $courseFinishDate = strtotime($studCourseData->finish_date);

                $i = 1;
                // $newInstallmentDate = Array();
                $newInstallmentData = [];

                // create step for next duration
                $step = '+'.$frequency.' '.$duration;
                $remainingBalance = $remainFee;
                $installmentAmount = $installmentFee;

                // generate next duration(date) for installment
                while ($installmentStartDate <= $courseFinishDate) {
                    if ($i <= $noOfInstallment) {
                        // $newInstallmentDate[] = date("Y-m-d", $installmentStartDate);
                        $newInstallmentData[] = [
                            'start_date' => date('Y-m-d', $installmentStartDate),
                            // 'due_date'      => date("Y-m-d", strtotime($step, $installmentStartDate)),
                            'amount' => ($i == $noOfInstallment) ? $remainingBalance : $installmentAmount,
                        ];
                        $installmentStartDate = strtotime($step, $installmentStartDate);
                        $remainingBalance -= $installmentAmount;
                    } else {
                        break;
                    }
                    $i++;
                }

                // check and redirect if generated installment and no_of_installment not equal
                if (count($newInstallmentData) != $noOfInstallment) {
                    $validator = 'Cannot generate! Schedule date will be out of course finish date.';

                    return redirect(route('offer-manage-upfront-fee-schedule-new', ['studentCourseId' => $studentCourseId, 'redirect' => (($request->input('redirect')) ? $request->input('redirect') : '')]))
                        ->withErrors($validator)
                        ->withInput();
                }
            } else {
                // redirect if installment_start_date not between start and finish date
                $validator = 'Cannot generate! Schedule due date will be out of course finish date.';

                return redirect(route('offer-manage-upfront-fee-schedule-new', ['studentCourseId' => $studentCourseId, 'redirect' => (($request->input('redirect')) ? $request->input('redirect') : '')]))
                    ->withErrors($validator)
                    ->withInput();
            }
            // exit;
            $arrScheduleData = [
                'college_id' => $collegeId,
                'student_id' => $studentId,
                'course_id' => $courseId,
                'student_course_id' => $studentCourseId,
                'remain_fee' => $remainFee,
                'installment_fee' => $installmentFee,
                'commission' => $commission,
                'gst' => $gst,
            ];

            // TODO::GNG-2457 (Stop duplicate invoice for initial amount from Offer manage module)
            $objCheckIsInitialPayment = new StudentInitialPaymentDetails;
            $checkIsInitialPayment = $objCheckIsInitialPayment->checkIsStudentPayment($studentId, $studentCourseId, $collegeId);
            $isScheduleOnly = (count($checkIsInitialPayment) > 0) ? true : false;

            DB::beginTransaction();
            try {
                $objRtoOfferUpfrontFeeSchedule = new OfferUpfrontFeeSchedule;

                // TODO::GNG-2457 (Delete existing schedule invoice)
                $this->removeExistingInvoice($dataArr);

                // Delete same records before insert
                $deleteSameUpfrontFeeSchedule = $objRtoOfferUpfrontFeeSchedule->deleteSameUpfrontFeeScheduleNew($arrScheduleData);

                // Insert
                $objOfferUpfrontFeeSchedule = $objRtoOfferUpfrontFeeSchedule->saveUpfrontFeeScheduleNew($arrScheduleData, $newInstallmentData, $request);

                // if(empty($checkIsInitialPayment)){
                if ($request->copyScheduleInvoice == 1) {
                    $getInitialPaymentData = $objRtoOfferUpfrontFeeSchedule->getStudentUpfrontFeeData($studentId, $studentCourseId);
                    if (isset($getInitialPaymentData[0]) && ! empty($getInitialPaymentData[0])) {

                        $objRtoStudentCourses = new StudentCourse;
                        $getStudentDetails = $objRtoStudentCourses->getStudentUpfrontFeeScheduleNew($studentCourseId);

                        // Correct Record for initial payment and initial payment detail.
                        $this->correctInitialPaymentRecord($studentCourseId);

                        // Insert in initial payment
                        (new StudentInitialPayment)->saveStudentInitialPaymentOfferManage($getInitialPaymentData, $getStudentDetails, $request);

                        // Insert in initial payment details
                        (new StudentInitialPaymentDetails)->saveStudentInitialPaymentDetailsOfferManage($getInitialPaymentData, $getStudentDetails, $isScheduleOnly, $request);

                        // Insert miscellaneous data like material fee, enrollment fee and OSHC fee
                        $this->importMiscellaneousInvoiceData($studCourseData);

                        // TODO::GNG-2457 (Create invoice for generated schedule)
                        // $this->createNewXeroInvoice($studentId, $getStudentDetails[0]->course_id);
                    }
                }
                // }
                DB::commit();
                $request->session()->flash('session_success', 'Upfront Fee Schedule Setup is Saved Successfully.');
            } catch (\Exception $e) {
                DB::rollBack();
                $request->session()->flash('session_error', $e->getMessage());
                // throw new ApplicationException($e->getMessage());
            }

            // $request->session()->flash('session_success', 'Upfront Fee Schedule Setup is Saved Successfully.');
            return redirect(route('offer-manage-upfront-fee-schedule-new', ['studentCourseId' => $studentCourseId, 'redirect' => (($request->input('redirect')) ? $request->input('redirect') : '')]));
        }

        $data['header'] = [
            'title' => 'Offers Upfront Fee Schedule',
            'breadcrumb' => [
                'Home' => route('user_dashboard'),
                'Offer manage' => route('offer-manage'),
                'Upfront Fee Schedule' => '',
            ]];
        $data['pagetitle'] = 'Upfront Fee Schedule';
        $data['plugincss'] = [];
        $data['css'] = [];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js'];
        $data['js'] = ['offermanage.js'];
        $data['funinit'] = ['OfferManage.initUpfrontFeeSchedule()'];
        $data['activateValue'] = 'Offers';
        $data['redirect'] = (($request->input('redirect')) ? $request->input('redirect') : '');
        $data['getStudentDetail'] = $getStudentDetail;
        $data['getPaidAmount'] = $totalFeePaid;
        $data['arrAgentCommissionListData'] = $arrAgentCommissionListData;
        $data['studentCourseData'] = $studCourseData;
        $data['daysAfterInvoiceStartDate'] = (isset($invoiceSettingData->value)) ? $invoiceSettingData->value : 0;
        $data['arrInstallmentDuration'] = $arrInstallmentDuration;
        $data['objGetUpfrontFeeSchedule'] = $objGetUpfrontFeeSchedule;
        $data['installmentStartDateCount'] = $installmentStartDateCount;
        $data['mainmenu'] = 'clients';
        $data['isHigherEd'] = App\Model\v2\StudentCourses::checkCourseIsHigherEd($studentCourseId);

        return view('frontend.student.offer-manage-upfront-fee-schedule', $data);
    }

    private function importMiscellaneousInvoiceData($studCourseData)
    {
        // for material fee
        if ($studCourseData->enroll_fee > 0) {
            $this->studentPaymentServiceV2->addMiscellaneousPaymentSchedule($studCourseData, \App\Model\v2\StudentMiscellaneousPayment::PAYMENT_TYPE_ENROLLMENT);
        }

        // for enrollment fee
        if ($studCourseData->course_material_fee > 0) {
            $this->studentPaymentServiceV2->addMiscellaneousPaymentSchedule($studCourseData, \App\Model\v2\StudentMiscellaneousPayment::PAYMMENT_TYPE_MATERIALFEE);
        }

        // for OSHC fee
        $studentDetails = \App\Model\v2\StudentDetails::where([
            'student_id' => $studCourseData->student_id,
            'OSHC_course_id' => $studCourseData->course_id,
        ])->first();

        if ($studentDetails && $studentDetails->OSHC_fee > 0) {
            $studCourseData->oshc_fee = $studentDetails->OSHC_fee;
            $studCourseData->studentDetails = $studentDetails;
            $this->studentPaymentServiceV2->addMiscellaneousPaymentSchedule($studCourseData, \App\Model\v2\StudentMiscellaneousPayment::PAYMENT_TYPE_OSHC);
        }
    }

    private function createNewXeroInvoice($studentId, $courseId)
    {
        if (Xero::isConnected()) {
            event(new \Domains\Xero\Events\XeroCreateStudentInvoiceEvent($studentId, $courseId));
        }
    }

    private function getInitialPaymentData($dataArr)
    {
        return App\Model\v2\StudentInitialPaymentDetails::where([
            'college_id' => $dataArr['college_id'],
            'student_id' => $dataArr['student_id'],
            'student_course_id' => $dataArr['student_course_id'],
        ])
            ->with(['xeroInvoice'])
            ->get();
    }

    private function validateExistingInvoice($dataArr)
    {
        $flag = true;
        if (Xero::isConnected()) {
            $initialPaymentDataArr = $this->getInitialPaymentData($dataArr);
            foreach ($initialPaymentDataArr as $initialPayment) {
                if ($initialPayment->payment_type != 'Initial') {
                    if ($initialPayment->xeroInvoice && $initialPayment->xeroInvoice->xero_invoice_status != Invoice::STATUS_DRAFT) {
                        $flag = false;
                        break;
                    }
                }
            }
        }

        return $flag;
    }

    private function removeExistingInvoice($dataArr)
    {
        if (Xero::isConnected()) {
            $initialPaymentDataArr = $this->getInitialPaymentData($dataArr);
            if (count($initialPaymentDataArr) > 0) {
                return $this->studentPaymentService->deletePaymentScheduleForOldVersion($dataArr, $initialPaymentDataArr);
            }
        }

        return true;
    }

    private function manageExistingInvoice()
    {
        if (Xero::isConnected()) {
            // change invoice amount
        }
    }

    public function correctInitialPaymentRecord($studentCourseId)
    {
        StudentInitialPaymentDetails::where([
            'student_course_id' => $studentCourseId,
            'upfront_fee_pay' => 0,
            'payment_type' => 'Schedual',
        ])->where('upfront_fee_pay', 0)->delete();
    }

    public function offerManageUpfrontFeeSchedule($studentId, $studentCourseId, Request $request)
    {

        $collegeId = Auth::user()->college_id;

        $arrInstallmentDuration = Config::get('constants.arrInstallmentDuration');

        // get upfront fee schedule for student
        $objRtoStudentCourses = new StudentCourse;
        $objStudentUpfrontFeeSchedule = $objRtoStudentCourses->getStudentUpfrontFeeSchedule($studentId, $studentCourseId);

        //  exit;
        $installmentStartDateCount = $objRtoStudentCourses->startDateCalculation($objStudentUpfrontFeeSchedule[0]['total_weeks'], $objStudentUpfrontFeeSchedule[0]['course_fee'], $objStudentUpfrontFeeSchedule[0]['course_upfront_fee'], $objStudentUpfrontFeeSchedule[0]['start_date']);
        // get saved or scheduled upfront fee
        $objRtoOfferUpfrontFeeSchedule = new OfferUpfrontFeeSchedule;
        $objGetUpfrontFeeSchedule = $objRtoOfferUpfrontFeeSchedule->getUpfrontFeeSchedule($studentId, $studentCourseId);

        if ($request->isMethod('post')) {
            $validator = Validator::make($request->all(), [
                'installment_start_date' => 'required',
                'no_of_installment' => 'required|integer',
                'frequency' => 'required|integer',
                'duration' => 'required',
            ]);

            if ($validator->fails()) {
                return redirect(route('offer-manage-upfront-fee-schedule', ['id' => $studentId, 'studentCourseId' => $studentCourseId]))
                    ->withErrors($validator)
                    ->withInput();
            }

            $startDate = date('Y-m-d', strtotime($objStudentUpfrontFeeSchedule[0]->start_date));
            $endDate = date('Y-m-d', strtotime($objStudentUpfrontFeeSchedule[0]->finish_date));

            $noOfInstallment = $request->input('no_of_installment');
            $frequency = $request->input('frequency');
            $duration = $request->input('duration');
            $installmentDate = date('Y-m-d', strtotime($request->input('installment_start_date')));

            $dueDateStep = '+'.($frequency * $noOfInstallment).' '.$duration;
            $installmentDueDate = date('Y-m-d', strtotime($dueDateStep, strtotime($installmentDate)));
            $upfrontDueDate = date('Y-m-d', strtotime('-1 day', strtotime($installmentDate)));

            // check installment_start_date should be between start and finish date
            if ($installmentDate >= $startDate && $installmentDueDate >= $startDate && $installmentDate <= $endDate && $installmentDueDate <= $endDate) {
                $remainFee = $objStudentUpfrontFeeSchedule[0]->course_fee - $objStudentUpfrontFeeSchedule[0]->course_upfront_fee;

                // check and redirect if remaining fee is less than 1
                if ($remainFee < 1) {
                    $validator = 'Cannot generate! Payment Schedule. Remaining amount is 0';

                    return redirect(route('offer-manage-upfront-fee-schedule', ['id' => $studentId, 'studentCourseId' => $studentCourseId]))
                        ->withErrors($validator)
                        ->withInput();
                }

                // dividation
                $installmentFee = $remainFee / $noOfInstallment;

                $installmentStartDate = strtotime($request->input('installment_start_date'));
                $courseFinishDate = strtotime($objStudentUpfrontFeeSchedule[0]->finish_date);

                $i = 1;
                $newInstallmentDate = [];

                // create step for next duration
                $step = '+'.$frequency.' '.$duration;

                // generate next duration(date) for installment
                while ($installmentStartDate <= $courseFinishDate) {
                    if ($i <= $noOfInstallment) {
                        $newInstallmentDate[] = date('Y-m-d', $installmentStartDate);
                        $installmentStartDate = strtotime($step, $installmentStartDate);
                    } else {
                        break;
                    }
                    $i++;
                }

                // check and redirect if generated installment and no_of_installment not equal
                if (count($newInstallmentDate) != $noOfInstallment) {
                    $validator = 'Cannot generate! Schedule duedate will be out of course startdate and finishdate.';

                    return redirect(route('offer-manage-upfront-fee-schedule', ['id' => $studentId, 'studentCourseId' => $studentCourseId]))
                        ->withErrors($validator)
                        ->withInput();
                }
            } else {
                // redirect if installment_start_date not between start and finish date
                $validator = 'Cannot generate! Schedule date will be out of course date range.';

                return redirect(route('offer-manage-upfront-fee-schedule', ['id' => $studentId, 'studentCourseId' => $studentCourseId]))
                    ->withErrors($validator)
                    ->withInput();
            }
            // exit;
            $arrScheduleData = [
                'college_id' => $collegeId,
                'student_id' => $studentId,
                'course_id' => $studentCourseId,
                'remain_fee' => $remainFee,
                'installment_fee' => $installmentFee,
            ];

            $objRtoOfferUpfrontFeeSchedule = new OfferUpfrontFeeSchedule;

            // delete same records before insert
            $deleteSameUpfrontFeeSchedule = $objRtoOfferUpfrontFeeSchedule->deleteSameUpfrontFeeSchedule($arrScheduleData);

            // insert
            $objOfferUpfrontFeeSchedule = $objRtoOfferUpfrontFeeSchedule->saveUpfrontFeeSchedule($arrScheduleData, $newInstallmentDate, $request);

            $request->session()->flash('session_success', 'Upfront Fee Schedule Setup is Saved Successfully.');

            return redirect(route('offer-manage-upfront-fee-schedule', ['id' => $studentId, 'studentCourseId' => $studentCourseId]));
        }
        $data['header'] = [
            'title' => 'Offers Upfront Fee Schedule',
            'breadcrumb' => [
                'Home' => route('user_dashboard'),
                'Offer manage' => route('offer-manage'),
                'Upfront Fee Schedule' => '',
            ]];
        $data['pagetitle'] = 'Upfront Fee Schedule';
        $data['plugincss'] = [];
        $data['css'] = [];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js'];
        $data['js'] = ['offermanage.js'];
        $data['funinit'] = ['OfferManage.initUpfrontFeeSchedule()'];
        $data['activateValue'] = 'Offers';

        $data['objStudentUpfrontFeeSchedule'] = $objStudentUpfrontFeeSchedule;
        $data['arrInstallmentDuration'] = $arrInstallmentDuration;
        $data['objGetUpfrontFeeSchedule'] = $objGetUpfrontFeeSchedule;
        $data['installmentStartDateCount'] = $installmentStartDateCount;
        $data['mainmenu'] = 'clients';

        return view('frontend.student.offer-manage-upfront-fee-schedule', $data);
    }

    public function deleteUpfrontFeeSchedule_OLD($scheduleId, Request $request)
    {
        $objRtoOfferUpfrontFeeSchedule = new OfferUpfrontFeeSchedule;

        // TODO::GNG-2457 (Remove existing invoice OR validate before delete)
        if (Xero::isConnected()) {
            $res = $objRtoOfferUpfrontFeeSchedule->getUpfrontFeeData($scheduleId);
            $whereArr = [
                'college_id' => $res->college_id,
                'student_id' => $res->student_id,
                'course_id' => $res->course_id,
                'student_course_id' => $res->student_course_id,
                'invoiced_start_date' => $res->installment_start_date,
            ];

            $hasTransactions = \App\Model\v2\StudentInitialPaymentDetails::where($whereArr)->whereHas('transactions')->exists();
            if ($hasTransactions) {
                $request->session()->flash('session_error', 'Cannot delete this invoice. It is already paid.');

                return Redirect::back();
            }

            /*$initialPaymentDataArr = StudentInitialPaymentDetails::where($whereArr)->get()->first();
            if($initialPaymentDataArr){
                $resData = StudentInitialPaymentDetails::find($initialPaymentDataArr->id);
                if(!$resData->canDelete()){
                    $request->session()->flash('session_error', 'Cannot delete this invoice. It is already paid.');
                    return Redirect::back();
                }
                $this->studentPaymentService->deletePaymentScheduleForOldVersion($whereArr, [$resData]);
            }*/
        }

        $deleteUpfrontFeeSchedule = $objRtoOfferUpfrontFeeSchedule->deleteUpfrontFeeSchedule($scheduleId);
        if (isset($deleteUpfrontFeeSchedule)) {
            $request->session()->flash('session_success', 'Upfront Fee Schedule Delete Successfully.');

            return Redirect::back();
        }
    }

    public function deleteUpfrontFeeSchedule($scheduleId, Request $request)
    {
        $objRtoOfferUpfrontFeeSchedule = new OfferUpfrontFeeSchedule;
        $res = $objRtoOfferUpfrontFeeSchedule->getUpfrontFeeData($scheduleId);

        if ($res && Xero::isConnected()) {
            $whereArr = [
                'college_id' => $res->college_id,
                'student_id' => $res->student_id,
                'course_id' => $res->course_id,
                'student_course_id' => $res->student_course_id,
                'invoiced_start_date' => $res->installment_start_date,
            ];

            $hasTransactions = \App\Model\v2\StudentInitialPaymentDetails::where($whereArr)->whereHas('transactions')->exists();
            if ($hasTransactions) {
                $request->session()->flash('session_error', 'Cannot delete this invoice. It is already paid.');

                return Redirect::back();
            }
            /*$initialPaymentDataArr = StudentInitialPaymentDetails::where($whereArr)->get()->first();
            if($initialPaymentDataArr){
                $resData = StudentInitialPaymentDetails::find($initialPaymentDataArr->id);
                if(!$resData->canDelete()){
                    $request->session()->flash('session_error', 'Cannot delete this invoice. It is already paid.');
                    return Redirect::back();
                }
                $this->studentPaymentService->deletePaymentScheduleForOldVersion($whereArr, [$resData]);
            }*/
        }

        DB::beginTransaction();
        try {
            $deleteUpfrontFeeSchedule = $objRtoOfferUpfrontFeeSchedule->deleteUpfrontFeeSchedule($scheduleId);

            if ($deleteUpfrontFeeSchedule) {
                $isStudent = Student::find($res->student_id)?->is_student == '1';

                if (! $isStudent) {
                    $offerScheduleCount = $objRtoOfferUpfrontFeeSchedule->checkOfferScheduleCount($res->college_id, $res->student_course_id);
                    if ($offerScheduleCount == 0) {
                        \App\Model\v2\StudentInitialPayment::where('student_course_id', $res->student_course_id)->forceDelete();
                        \App\Model\v2\StudentInitialPaymentDetails::where('student_course_id', $res->student_course_id)->forceDelete();
                        \App\Model\v2\StudentMiscellaneousPayment::where('student_course_id', $res->student_course_id)->forceDelete();
                    }
                }
                DB::commit();
                $request->session()->flash('session_success', 'Upfront Fee Schedule deleted successfully.');
            }
        } catch (\Exception $e) {
            DB::rollBack();
            report($e);
            $request->session()->flash('session_error', 'An error occurred while deleting the Upfront Fee Schedule.');
        }

        return Redirect::back();
    }

    public function ajaxAction(Request $request)
    {
        $action = $request->input('action');
        $collegeId = Auth::user()->college_id;
        switch ($action) {
            case 'getEmailTemplate':
                $emailRecipient = $request->input('data.EmailTemplateType');
                $this->_getEmailTemplate($emailRecipient, $collegeId);
                break;
            case 'getEmailContent':
                $emailContentId = $request->input('data.EmailContentId');
                $this->_getEmailContent($emailContentId);
                break;
            case 'getEmailContentOffer':
                $emailContentId = $request->input('data.EmailContentId');
                $studentId = $request->input('data.studentId');
                $courseId = $request->input('data.courseId');
                $this->_getEmailContentOffer($emailContentId, $studentId, $courseId);
                break;
            case 'generateStudentID':
                $studID = $request->input('data.studID');
                $generateID = $request->input('data.generateID');
                $this->_generateStudentID($studID, $generateID);
                break;
            case 'checkStudentIDAvailability':
                $generateID = $request->input('data.generateID');
                $IDtype = $request->input('data.IDtype');
                $studID = $request->input('data.studID');
                $this->_checkStudentIDAvailability($generateID, $IDtype, $studID);
                break;
            case 'studentCOEdetail':
                $courseID = $request->input('data.courseID');
                $this->_getStudentCOE($courseID);
                break;
            case 'StudentCOEImageDelete':
                $matrialID = base64_decode($request->input('data.id'));
                $studentId = $request->input('data.studentId');
                $oldFileName = $request->input('data.filename');
                $collegeId = Auth::user()->college_id;
                $this->_deleteStudentCOEImage($collegeId, $matrialID, $studentId, $oldFileName);
                break;
            case 'offerManageFindStudent':
                $idValue = $request->input('data.idValue');
                $this->_offerManageFindStudent($idValue);
                break;
            case 'offerManageStudentCourse':
                $studenIdPrimary = $request->input('data.studenIdPrimary');
                $courseId = $request->input('data.course_id');
                $this->_offerManageStudentCourse($studenIdPrimary, $courseId);
                break;
            case 'searchOfferManageStudent':
                $searchBy = $request->input('data.searchBy');
                $collegeCampus = $request->input('data.searchCollegeCampus');
                $searchString = $request->input('data.searchString');
                $createdAt = $request->input('data.searchCreatedAt');
                $courseFilter = $request->input('data.courseFilter');
                $applicationStautus = $request->input('data.applicationStautus');
                $this->_searchOfferManageStudent($searchBy, $collegeCampus, $searchString, $createdAt, $courseFilter, $applicationStautus);
                break;
            case 'getOfferManageDatatabledata':
            case 'offerManageFilterData':
                $objRtoStudents = new Students;
                $resultArr = $objRtoStudents->getOfferManagaDatatableData($request);
                echo json_encode($resultArr);
                break;
            case 'studentPayment':
                $student_id = $request->input('data.student_id');
                $course_id = $request->input('data.course_id');
                $this->_getStudentPayment($course_id, $student_id);
                break;
            case 'editDataStudentPayment':
                $editId = $request->input('data.editId');
                $this->_editDataStudentPayment($editId);
                break;
            case 'editStudentPayment':
                $editId = $request->input('data.editId');
                $dueDateEdit = $request->input('data.dueDateEdit');
                $this->_editStudentPayment($editId, $dueDateEdit);
                break;
            case 'deleteStudentPayment':
                $deleteId = $request->input('data.deleteId');
                $this->_deleteStudentPayment($deleteId);
                break;
            case 'deleteStudentPaymentMis':
                $deleteId = $request->input('data.deleteId');
                $this->_deleteStudentPaymentMis($deleteId);
                break;
            case 'getStudentInvoicePdf':
                $courseId = $request->input('data.courseId');
                $studentId = $request->input('data.studentId');
                $resultArr = $this->attachStudentInvoicePdf($courseId, $studentId);
                echo json_encode($resultArr);
                break;
            case 'getStudentOfferLetterPdf':
                $courseId = $request->input('data.courseId');
                $studentId = $request->input('data.studentId');
                $resultArr = $this->attachOfferLetterPdf($courseId, $studentId);
                echo json_encode($resultArr);
                break;
            case 'saveStudentsOfferLabel':
                $studentId = $request->input('data.studentId');
                $offerLabel = $request->input('data.offerLabel');
                $resultArr = $this->_saveStudentsOfferLabel($offerLabel, $studentId);
                echo json_encode($resultArr);
                break;
            case 'resetOfferLabel':
                $studentCourseId = $request->input('data.studentCourseId');
                $resultArr = $this->_resetOfferLabel($studentCourseId);
                echo json_encode($resultArr);
                break;
            case 'syncStudent':
                $studentId = $request->input('data.studentId');
                $resultArr = $this->_syncToXero($studentId);
                echo json_encode($resultArr);
                break;
            case 'reSyncStudent':
                $studentId = $request->input('data.studentId');
                $resultArr = $this->_reSyncToXero($studentId);
                echo json_encode($resultArr);
                break;
            case 'getSyncData':
                $studentId = $request->input('data.studentID');
                $this->_getSyncData($studentId);
                break;
            case 'offer-export':
                // ob_clean();
                ob_end_clean();  // TODO:: Sometimes "ob_clean" might not fully clear the output buffers. Try using "ob_end_clean" to ensure all buffers are completely flushed.

                return Excel::download(new ManageOfferExport, 'Offer_List.xlsx');
                break;
            case 'bulkSyncStudent':
                $service = strtolower($request->input('data.service'));
                $studentIds = array_unique($request->input('data.studentIds'));
                $resultArr = $this->handleBulkSync($service, $studentIds);
                echo json_encode($resultArr);
                break;
        }
        exit;
    }

    public function _syncToXero($studentId)
    {
        try {
            if (Xero::isConnected()) {
                $student = Student::findOrFail($studentId);
                $student->asXeroContact();
                $xeroContact = $student->fresh()->xeroContact;
                if ($xeroContact) {
                    if ($xeroContact->xero_failed_at) {
                        // Fail message
                        $message = ! empty($xeroContact->xero_failed_message) ? $xeroContact->xero_failed_message : 'Something want wrong';

                        return ['status' => 'error', 'message' => $message];
                    }

                    return ['status' => 'success', 'message' => 'Synced Successfully'];
                }
            } else {
                return ['status' => 'success', 'message' => 'Xero not connected'];
            }
        } catch (\Exception $e) {
            return ['status' => 'error', 'message' => $e->getMessage()];
        }
    }

    public function handleBulkSync($service, $studentIds)
    {
        if (count($studentIds) == 0) {
            return ['status' => 'error', 'message' => 'Please select at least one record'];
        }

        if ($service == 'xero') {
            if (! Xero::isConnected()) {
                return ['status' => 'error', 'message' => 'Xero not connected'];
            }

            return $this->_bulkSyncToXero($studentIds);
        }

        if ($service == 'zoho') {
            if (! $this->isZohoConnected()) {
                return ['status' => 'error', 'message' => 'Zoho not connected'];
            }

            return $this->_bulkSyncToZoho($studentIds);
        }
    }

    public function _bulkSyncToXero($studentIds)
    {
        $sync = $alreadySync = $failSync = $invalid = 0;
        $studentDetails = [];

        foreach ($studentIds as $studentId) {
            $student = Student::findOrFail($studentId);

            $studentInfo = [
                'id' => $student->generated_stud_id ?: 'ID: '.$studentId,
                'name' => $student->first_name.' '.$student->family_name, // $student->getFullNameAttribute(),
                'sync_datetime' => now()->format('d-m-Y H:i:s'),
                'status' => '',
                'fail_reason' => '',
            ];

            if (empty($student->generated_stud_id)) {
                $invalid++;
                $studentInfo['status'] = 'Invalid';
                $studentInfo['fail_reason'] = 'Student ID not generated';
            } elseif ($student->isXeroContactCreatated()) {
                $alreadySync++;
                $studentInfo['status'] = 'Already Synced';
                $xeroContact = $student->xeroContact;
                if ($xeroContact && $xeroContact->created_at) {
                    $studentInfo['sync_datetime'] = $xeroContact->created_at->format('d-m-Y H:i:s');
                }
            } else {
                try {
                    $student->asXeroContact();
                    $xeroContact = $student->fresh()->xeroContact;
                    if ($xeroContact) {
                        if ($xeroContact->xero_failed_at) {
                            $failSync++;
                            $studentInfo['status'] = 'Failed';
                            $studentInfo['fail_reason'] = $xeroContact->xero_failed_reason ?: 'Sync failed';
                        } else {
                            $sync++;
                            $studentInfo['status'] = 'Success';
                        }
                    } else {
                        $failSync++;
                        $studentInfo['status'] = 'Failed';
                        $studentInfo['fail_reason'] = 'Unable to create Xero contact';
                    }
                } catch (\Exception $e) {
                    $failSync++;
                    $studentInfo['status'] = 'Failed';
                    $studentInfo['fail_reason'] = $e->getMessage();
                }
            }

            $studentDetails[] = $studentInfo;
        }

        $error_msg = [];
        if ($alreadySync > 0) {
            $error_msg[] = "Total $alreadySync selected students already synced.";
        } elseif ($failSync > 0) {
            $error_msg[] = "Total $failSync selected students failed to sync.";
        } elseif ($invalid > 0) {
            $error_msg[] = "Total $invalid selected students have not generated their ID";
        }

        return [
            'success_count' => $sync,
            'success_msg' => ($sync > 0) ? "Total $sync selected students synced successfully." : '',
            'error_count' => $alreadySync + $failSync + $invalid,
            'error_msg' => ($sync === count($studentIds)) ? '' : implode(' ', $error_msg),
            'student_details' => $studentDetails,
            'show_popup' => true,
        ];
    }

    public function _reSyncToXero($studentId)
    {
        try {
            if (Xero::isConnected()) {
                $student = Student::findOrFail($studentId);
                if ($student->isXeroContactCreatated()) {
                    dispatch_sync(new \Domains\Xero\Jobs\SyncContactFromXero($student->xeroContact));
                } else {
                    $student->fresh()->asXeroContact();
                }

                return ['status' => 'success', 'message' => 'Synced Successfully'];
            } else {
                return ['status' => 'success', 'message' => 'Xero not connected'];
            }
        } catch (\Exception $e) {
            return ['status' => 'error', 'message' => $e->getMessage()];
        }
    }

    public function _getSyncData($studentId)
    {
        if (! empty($studentId)) {
            $student = Student::findOrFail($studentId);
            $res = $student->fresh()->xeroContact;
            echo json_encode($res->toArray());
            exit;
        }
    }

    public function _bulkSyncToZoho(array $studentIds)
    {
        $students = Student::with('integrationItem')->whereIn('id', $studentIds)->get();

        $sync = $alreadySync = $failSync = $invalid = 0;
        $studentDetails = [];

        foreach ($students as $student) {
            $studentInfo = [
                'id' => $student->generated_stud_id,
                'name' => $student->first_name.' '.$student->family_name, // $student->getFullNameAttribute(),
                'sync_datetime' => now()->format('d M Y H:i:s'),
                'status' => '',
                'fail_reason' => '',
            ];

            if ($student->integrationItem && ! empty($student->integrationItem->sync_item_id)) {
                $alreadySync++;
                $studentInfo['status'] = 'Already Synced';
                $studentInfo['sync_datetime'] = $student->integrationItem->synced_at ? $student->integrationItem->synced_at->format('d M Y H:i:s') : 'N/A';
            } else {

                // Try to get the payload first to see if there are mapping issues
                /*try {
                    $payload = $student->getZohoPayload();
                    Log::info('Zoho payload created for student ' . $student->id, [
                        'payload_key' => $payload->key(),
                        'module_name' => $payload->moduleName()
                    ]);
                } catch (\Exception $payloadException) {
                    Log::error('Failed to create Zoho payload for student ' . $student->id, [
                        'error' => $payloadException->getMessage(),
                        'trace' => $payloadException->getTraceAsString()
                    ]);
                    throw new \Exception('Payload creation failed: ' . $payloadException->getMessage());
                }*/

                try {
                    $result = $student->asZohoItem();

                    Log::info('Zoho sync result for student '.$student->id, [
                        'result' => $result ? 'success' : 'null',
                        'sync_item_id' => $result ? $result->sync_item_id : 'null',
                    ]);

                    if ($result && $result->sync_item_id) {
                        $sync++;
                        $studentInfo['status'] = 'Success';
                        // Update sync datetime with actual sync time
                        if ($result->synced_at) {
                            $studentInfo['sync_datetime'] = $result->synced_at ? $result->synced_at->format('d- M Y H:i:s') : 'N/A';
                        }
                    } else {
                        $failSync++;
                        $studentInfo['status'] = 'Failed';
                        $studentInfo['fail_reason'] = 'Sync completed but no sync ID returned';
                    }
                } catch (\Exception $e) {
                    $failSync++;
                    $studentInfo['status'] = 'Failed';
                    $errorMessage = $e->getMessage();
                    $studentInfo['fail_reason'] = $errorMessage ?: 'Exception with empty message';
                    Log::error('Zoho sync Exception for student '.$student->id, [
                        'error' => $errorMessage,
                        'exception_class' => get_class($e),
                        'trace' => $e->getTraceAsString(),
                    ]);
                }
            }
            // dd($studentInfo);
            $studentDetails[] = $studentInfo;
        }

        // Handle invalid student IDs
        $invalidIds = array_diff($studentIds, $students->pluck('id')->toArray());
        foreach ($invalidIds as $invalidId) {
            $invalid++;
            $studentDetails[] = [
                'id' => 'ID: '.$invalidId,
                'name' => 'Student not found',
                'sync_datetime' => now()->format('d M Y H:i:s'),
                'status' => 'Invalid',
                'fail_reason' => 'Student record not found',
            ];
        }

        $error_msg = [];
        if ($alreadySync > 0) {
            $error_msg[] = "Total $alreadySync selected students already synced.";
        } elseif ($failSync > 0) {
            $error_msg[] = "Total $failSync selected students failed to sync.";
        } elseif ($invalid > 0) {
            $error_msg[] = "Total $invalid selected students could not be found.";
        }

        return [
            'success_count' => $sync,
            'success_msg' => ($sync > 0) ? "Total $sync selected students synced successfully." : '',
            'error_count' => $alreadySync + $failSync + $invalid,
            'error_msg' => ($sync === count($studentIds)) ? '' : implode(' ', $error_msg),
            'student_details' => $studentDetails,
            'show_popup' => true,
        ];
    }

    public function _resetOfferLabel($studentCourseId)
    {

        if (empty($studentCourseId)) {
            return ['status' => 'error', 'message' => 'Student or offer lable not selected.'];
        }
        StudentCourse::where('id', $studentCourseId)->update(['offer_label_id' => 0]);

        return ['status' => 'success', 'message' => 'Offer Label updated successfully.'];
    }

    public function _saveStudentsOfferLabel($offerLabel, $studentId)
    {

        if (empty($studentId) && $offerLabel == '') {
            return ['status' => 'error', 'message' => 'Student or offer lable not selected.'];
        }
        StudentCourse::whereIn('id', $studentId)->update(['offer_label_id' => $offerLabel]);

        return ['status' => 'success', 'message' => 'Offer Label updated successfully.'];
    }

    public function _deleteStudentCOEImage($college_id, $id, $studenId, $oldFileName)
    {
        $objRtoStudentCourse = new StudentCourse;
        $deleteStudentCOEImage = $objRtoStudentCourse->deleteCoeOfferManageImage($college_id, $id, $studenId, $oldFileName);
        if ($deleteStudentCOEImage) {
            $success = 1;
        } else {
            $success = 0;
        }
        echo $success;
        exit;
    }

    public function _deleteStudentPaymentMis($deleteId)
    {
        $success = 1;
        $collegeId = Auth::user()->college_id;

        $objStudentMiscellaneousPayment = new StudentMiscellaneousPayment;
        $arrStudentMiscellaneousPayment = $objStudentMiscellaneousPayment->deleteMiscellaneousPayment($deleteId);
        echo $success = 1;
        exit;
    }

    public function _deleteStudentPayment($deleteId)
    {
        $success = 0;
        $collegeId = Auth::user()->college_id;
        $objStudentInitialPaymentTransaction = new StudentInitialPaymentTransaction;
        $arrStudentInitialPaymentTransaction = $objStudentInitialPaymentTransaction->_getPaymentScheduleInfo($collegeId, $deleteId);
        if (count($arrStudentInitialPaymentTransaction) == 0) {
            $objStudentInitialPaymentDetails = new StudentInitialPaymentDetails;
            $arrStudentInitialPaymentDetails = $objStudentInitialPaymentDetails->deleteStudentPaymentDetail($deleteId);
            $success = 1;
        }
        echo $success;
        exit;
    }

    public function _editStudentPayment($editId, $dueDateEdit)
    {
        $objRtoStudentMiscellaneousPayment = new StudentInitialPaymentDetails;
        $arrMiscellaneousFeeDue = $objRtoStudentMiscellaneousPayment->studentPaymentDueDateEdit($editId, $dueDateEdit);

        echo json_encode($arrMiscellaneousFeeDue);
    }

    public function _editDataStudentPayment($editId)
    {
        $objRtoStudentMiscellaneousPayment = new StudentInitialPaymentDetails;
        $arrMiscellaneousFeeDue = $objRtoStudentMiscellaneousPayment->getStudentPaymentDueDate($editId);
        echo json_encode($arrMiscellaneousFeeDue);
    }

    public function _getStudentPayment($course_id, $student_id)
    {

        $collegeId = Auth::user()->college_id;

        $objPaymentMode = new PaymentMode;
        $arrPaidMode = $objPaymentMode->getPaymentModeData($collegeId);
        $receiptNoMod = [];

        $objStudentDetailsGet = new StudentInitialPaymentDetails;
        $arrStudentDetailsGet = $objStudentDetailsGet->studentPaymentDetailsGet($collegeId, $course_id, $student_id);

        $objRtoStudentMiscellaneousPayment = new StudentMiscellaneousPayment;
        $arrMiscellaneousFeeDue = $objRtoStudentMiscellaneousPayment->getUnpaidMiscellaneousPayment($student_id, $course_id);

        $InvoicedDueAmount = 0;
        $totalCoursePaidAmmount = 0;
        $arrPaidDuration = Config::get('constants.arrPaidDuration');
        for ($j = 0; $j < count($arrStudentDetailsGet); $j++) {
            $arrStudentDetailsGet[$j]['duration_type'] = $arrPaidDuration[$arrStudentDetailsGet[$j]['paid_duration_day']];
            $arrStudentDetailsGet[$j]['payment_mode_value'] = $arrPaidMode[$arrStudentDetailsGet[$j]['payment_mode']];
            $InvoicedDueAmount = $InvoicedDueAmount + $arrStudentDetailsGet[$j]['upfront_fee_to_pay'];
            $totalCoursePaidAmmount += $arrStudentDetailsGet[$j]['upfront_fee_pay'];
        }

        $dataArray = [];

        $dataArray['paymentDetail'] = $arrStudentDetailsGet;
        $dataArray['miscellaneousPayment'] = $arrMiscellaneousFeeDue;

        echo json_encode($dataArray);
    }

    private function _offerManageStudentCourse($studenIdPrimary, $courseId)
    {

        $objStudentCourse = new StudentCourse;
        $arrStudentCourse = $objStudentCourse->_getAccountPayment($courseId, $studenIdPrimary);
        echo json_encode($arrStudentCourse);
        exit;
    }

    private function _offerManageFindStudent($idValue)
    {
        $collegeId = Auth::user()->college_id;
        $objStudents = new Students;
        $arrStudents = $objStudents->getStudentId($collegeId, $idValue);
        echo json_encode($arrStudents);
        exit;
    }

    private function _getEmailTemplate($emailRecipient, $collegeId)
    {
        $objRtoEmailTemplate = new EmailTemplate;
        $arrEmailTemplate = $objRtoEmailTemplate->getEmailTemplate($emailRecipient, $collegeId);

        echo json_encode($arrEmailTemplate);
        exit;
    }

    private function _getEmailContent($emailContentId)
    {

        $objRtoEmailTemplate = new EmailTemplate;
        $arrEmailContent = $objRtoEmailTemplate->getEmailContent($emailContentId);

        $objEmailTemplateDocuments = new EmailTemplateDocuments;
        $arrEmailTemplateDocuments = $objEmailTemplateDocuments->getDocumentsList($emailContentId);

        $arrEmailContent[0]['document'] = $arrEmailTemplateDocuments;
        echo json_encode($arrEmailContent);
        exit;
    }

    private function _getEmailContentOffer($emailContentId, $studentId, $courseId)
    {
        $objRtoEmailTemplate = new EmailTemplate;
        $arrEmailContent = $objRtoEmailTemplate->getEmailContent($emailContentId);

        $objSendMail = new SendMail;
        $contentData = $objSendMail->setEmailBodyContent($studentId, $courseId, $arrEmailContent[0]['content']);
        $arrEmailContent[0]['content'] = $contentData;
        $objEmailTemplateDocuments = new EmailTemplateDocuments;
        $arrEmailTemplateDocuments = $objEmailTemplateDocuments->getDocumentsList($emailContentId);
        $arrEmailContent[0]['document'] = $arrEmailTemplateDocuments;

        echo json_encode($arrEmailContent);
        exit;
    }

    private function getEmailContentData($content, $studentId, $courseId)
    {

        $objStudentCourse = new StudentCourse;
        $arrStudentCourse = $objStudentCourse->getStudentCoursesEmailContent($studentId, $courseId);

        $content = str_replace('{alter_email}', $arrStudentCourse[0]['emergency_email'], $content);
        $content = str_replace('{alter_email2}', $arrStudentCourse[0]['emergency_email'], $content);
        $content = str_replace('{college_email}', $arrStudentCourse[0]['college_email'], $content);
        $content = str_replace('{country_birth}', $arrStudentCourse[0]['birth_country'], $content);

        $content = str_replace('{current_date}', $arrStudentCourse[0]['course_attempt'], $content);
        $content = str_replace('{dob}', $arrStudentCourse[0]['birth_date'], $content);
        $content = str_replace('{dob_without_stroke}', $arrStudentCourse[0]['birth_date'], $content);
        $content = str_replace('{email}', $arrStudentCourse[0]['student_email'], $content);
        $content = str_replace('{expiry_date}', $arrStudentCourse[0]['visa_expiry_date'], $content);
        $content = str_replace('{fax}', $arrStudentCourse[0]['current_mobile_phone'], $content);
        $content = str_replace('{first_name}', $arrStudentCourse[0]['first_name'], $content);
        $content = str_replace('{last_name}', $arrStudentCourse[0]['family_name'], $content);
        $content = str_replace('{gender}', $arrStudentCourse[0]['gender'], $content);
        $content = str_replace('{mobile}', $arrStudentCourse[0]['current_mobile_phone'], $content);
        $content = str_replace('{nationality}', $arrStudentCourse[0]['nationality'], $content);
        $content = str_replace('{nick_name}', $arrStudentCourse[0]['nickname'], $content);
        $content = str_replace('{passport_no}', $arrStudentCourse[0]['passport_no'], $content);
        $content = str_replace('{phone}', $arrStudentCourse[0]['current_mobile_phone'], $content);
        $content = str_replace('{postcode}', $arrStudentCourse[0]['current_postcode'], $content);
        $content = str_replace('{state}', $arrStudentCourse[0]['current_state'], $content);
        $content = str_replace('{street_address}', $arrStudentCourse[0]['current_street_name'], $content);
        $content = str_replace('{Student_id}', $arrStudentCourse[0]['generated_stud_id'], $content);
        $content = str_replace('{suburb}', $arrStudentCourse[0]['current_city'], $content);
        $content = str_replace('{title}', $arrStudentCourse[0]['name_title'], $content);
        $content = str_replace('{user_name}', $arrStudentCourse[0]['generated_stud_id'], $content);
        $content = str_replace('{visa_type}', $arrStudentCourse[0]['visa_status'], $content);

        return $content;
    }

    private function _checkStudentIDAvailability($generateID, $IDtype, $studID)
    {
        if (! empty($generateID)) {
            $objStudent = new Students;
            $result = $objStudent->checkAvailabeStudentID($generateID, $IDtype, $studID);

            echo $result;
            exit;
        } else {
            echo 'NULL_ID';
            exit;
        }
    }

    private function _generateStudentID($studID, $generateID)
    {

        if (! $generateID != 'false') {
            $collegeID = Auth::user()->college_id;

            $studIDFormate = new StudentIdFormate;
            $studentIdFormate = $studIDFormate->getStudentIdFormate($collegeID);

            $p1 = $studentIdFormate[0]->position1;
            $p2 = $studentIdFormate[0]->position2;
            $p3 = $studentIdFormate[0]->position3;

            $alphabeat = $studentIdFormate[0]->alphabeat;
            $yeardigit = $studentIdFormate[0]->yeardigit;
            $auto_no = $studentIdFormate[0]->auto_increment;

            $str = '';
            $positions = [
                'position1' => $studentIdFormate[0]->position1,
                'position2' => $studentIdFormate[0]->position2,
                'position3' => $studentIdFormate[0]->position3,
            ];

            // Prepare variables
            $str = '';

            // Loop through the positions
            foreach ($positions as $position => $value) {
                switch ($value) {
                    case 'countrycode':
                        $str .= '061';
                        break;
                    case 'alphabeat':
                        if (! empty($alphabeat)) {
                            $str .= $alphabeat;
                        }
                        break;
                    case 'yeardigit':
                        if (! empty($yeardigit)) {
                            $str .= $yeardigit;
                        }
                        break;
                }
            }

            // $str .= $auto_no;
            $generateID = $str.$auto_no;
            $generateIDFormate = $str.'0000';

            $dataArr = ['student_id' => $studID,
                'college_id' => $collegeID,
                'generated_id' => $generateID,
                'generateIDFormate' => $generateIDFormate,
                'auto_no' => $auto_no];

        }
        echo json_encode($dataArr);
        exit;
    }

    private function _generateStudentIDOld($studID, $generateID)
    {

        if (! $generateID != 'false') {
            $collegeID = Auth::user()->college_id;

            $studIDFormate = new StudentIdFormate;
            $studentIdFormate = $studIDFormate->getStudentIdFormate($collegeID);

            $p1 = $studentIdFormate[0]->position1;
            $p2 = $studentIdFormate[0]->position2;
            $p3 = $studentIdFormate[0]->position3;

            $alphabeat = $studentIdFormate[0]->alphabeat;
            $yeardigit = $studentIdFormate[0]->yeardigit;
            $auto_no = $studentIdFormate[0]->auto_increment;

            $str = '';
            if ($p1 == 'countrycode') {
                $str .= '061';
            } elseif ($p2 == 'countrycode') {
                $str .= '061';
            } elseif ($p3 == 'countrycode') {
                $str .= '061';
            }

            if ($p1 == 'alphabeat' && $alphabeat != '') {
                $str .= $alphabeat;
            } elseif ($p2 == 'alphabeat' && $alphabeat != '') {
                $str .= $alphabeat;
            } elseif ($p3 == 'alphabeat' && $alphabeat != '') {
                $str .= $alphabeat;
            }

            if ($p1 == 'yeardigit' && $yeardigit != '') {
                $str .= $yeardigit;
            } elseif ($p2 == 'yeardigit' && $yeardigit != '') {
                $str .= $yeardigit;
            } elseif ($p3 == 'yeardigit' && $yeardigit != '') {
                $str .= $yeardigit;
            }

            // $str .= $auto_no;
            $generateID = $str.$auto_no;
            $generateIDFormate = $str.'0000';

            $dataArr = ['student_id' => $studID,
                'college_id' => $collegeID,
                'generated_id' => $generateID,
                'generateIDFormate' => $generateIDFormate,
                'auto_no' => $auto_no];

        }
        echo json_encode($dataArr);
        exit;
    }

    private function _searchOfferManageStudent($searchBy, $collegeCampus, $searchString, $createdAt, $courseFilter, $applicationStautus)
    {
        $perPage = Config::get('constants.pagination.perPage');

        $objRtoStudents = new Students;
        $objStudents = $objRtoStudents->_searchOfferManageStudent($searchBy, $collegeCampus, $searchString, $createdAt, $perPage, $courseFilter, $applicationStautus);

        $data['pagetitle'] = 'Application List';
        $data['plugincss'] = [];
        $data['css'] = [];
        $data['pluginjs'] = [];
        $data['js'] = ['offermanage.js'];
        $data['funinit'] = ['OfferManage.initOfferLetter()'];
        $data['activateValue'] = 'Offers';

        $data['objStudents'] = $objStudents;

        $resultTable = view('frontend.student.search-offer-manage-student', $data)->render();
        echo $resultTable;
        exit;
    }

    public function _getStudentCOE($courseID)
    {
        if (! empty($courseID)) {
            $objStudCourse = new StudentCourse;
            $dataResult = $objStudCourse->getStudentCOEDetail($courseID);
            $dataResult['coe_material_id'] = base64_encode($dataResult['coe_material_id']);
            echo json_encode($dataResult);
            exit;
        }
    }

    public function generateNewStudentID(Request $request)
    {

        $stud_id = $request->stud_id;
        $generated_id = $request->generated_id;
        $primary_id = $request->primary_id;
        $studIDType = 'generate';

        $objStudent = new Students;
        $objStudent->updateStudentDetail($stud_id, $generated_id, $studIDType, $primary_id, $request);

        $request->session()->flash('session_success', 'Student ID Generate Successfully...');

        return redirect(route('offer-manage'));
    }

    public function reserveNewStudentID(Request $request)
    {

        $stud_id = $request->stud_id;
        $reserve_id = $request->reserve_id;
        $studIDType = 'reserve';

        $objStudent = new Students;
        $objStudent->updateStudentDetail($stud_id, $reserve_id, $studIDType, $primaryID = null, $request);

        $request->session()->flash('session_success', 'Student ID Reserve Successfully...');

        return redirect(route('offer-manage'));
    }

    public function offerLetterPdf($courseId, $studentId, $studentCourseID, $download, Request $request)
    {
        $collegeId = Auth::user()->college_id;
        $objRtoPdfTemplate = new PdfTemplate;
        $objRtoStudents = new Students;
        $getRtoStudents = $objRtoStudents->getStudents($studentId);
        if ($getRtoStudents->student_type == 'Domestic') {
            $arrPdfTemplateContent = $objRtoPdfTemplate->getPdfContent(2);
        } else {
            $arrPdfTemplateContent = $objRtoPdfTemplate->getPdfContent(1);
        }

        $content = $arrPdfTemplateContent[0]->pdf_template;
        $offerLetterContent = $objRtoPdfTemplate->setPdfTemplateBodyContentNew($collegeId, $studentId, $content);

        if (isset($offerLetterContent['status']) && $offerLetterContent['status'] == 'error') {
            $request->session()->flash('session_error', $offerLetterContent['msg']);

            return redirect(route('offer-manage'));
        }

        $pdf = App::make('dompdf.wrapper');
        $pdf->loadHTML($offerLetterContent);

        $studentOfferLetterName = $this->studentOfferLetterName($studentId);

        if ($download == 'save') {

            $filePath = Config::get('constants.uploadFilePath.StudentMailAttach');
            $destinationPath = Helpers::changeRootPath($filePath, $studentId);
            if (! is_dir($destinationPath['default'])) {
                File::makeDirectory($destinationPath['default'], 0777, true, true);
            }
            $studentOfferLetterName = str_replace("\0", '', $studentOfferLetterName); // Remove null byte
            // $filename = $studentOfferLetterName . '.pdf';
            $offerLetterPdf = $destinationPath['default'].$studentOfferLetterName.'.pdf';
            $pdfContent = $pdf->output();

            $tmpPath = tempnam(sys_get_temp_dir(), 'pdf_');
            file_put_contents($tmpPath, $pdfContent);
            $upload_success = UploadService::uploadAs($destinationPath['view'], new \Illuminate\Http\File($tmpPath), $studentOfferLetterName.'.pdf');

            @unlink($tmpPath);
            info('file uploaded form offer letter ', [$upload_success]);

        } else {

            $pdf->output();
            $dom_pdf = $pdf->getDomPDF();
            $canvas = $dom_pdf->get_canvas();
            $canvas->page_text(500, 750, 'Page {PAGE_NUM} of {PAGE_COUNT}', null, 6, [0, 0, 0]);
            // return $pdf->stream();exit;

            return $pdf->download($studentOfferLetterName.'.pdf');
        }

    }

    public function studentInvoicePdf($courseId, $studentId, $download, Request $request)
    {
        $data = [];
        $data['pagetitle'] = '';
        if (isset($studentId)) {

            $objRtoStudents = new Students;
            $objStudentDetails = $objRtoStudents->getStudentDetailsV2($studentId, $courseId);

            $collegeId = Auth::user()->college_id;
            if ($objStudentDetails[0]->college_id == $collegeId) {

                $objRtoCountry = new Country;
                $objCurrentCountry = $objRtoCountry->getSelectedCountry($objStudentDetails[0]->current_country);
                $objNationality = $objRtoCountry->getSelectedCountry($objStudentDetails[0]->nationality);

                $objRtoCollegeDetails = new CollegeDetails;
                $objCollegeDetails = $objRtoCollegeDetails->getCollegeDetails($collegeId);

                $objRtoStudentCourse = new StudentCourse;
                $objStudentCourse = $objRtoStudentCourse->getAppliedStudentCourse($courseId, $studentId);
            } else {
                return view('frontend.error_page.unauthorized', $data);
            }

            if ($objStudentCourse->count() > 0) {
                $objStudentCourse = $objStudentCourse;
            } else {
                $objStudentCourse = [];
            }

            $studentDetails = new StudentDetails;
            $getOHSCCourseId = $studentDetails->getStudentDetailsOfOHSCCourseId($studentId);
            if (empty($getOHSCCourseId[0])) {
                $firstStudentCourseId = StudentCourse::where('rto_student_courses.student_id', '=', $studentId)
                    ->select('rto_student_courses.course_id')
                    ->get();
                $firstStudentCourseId = $firstStudentCourseId[0]->course_id;
            } else {
                $firstStudentCourseId = $getOHSCCourseId[0];
            }
        }

        // $data['logoPath'] = Config::get('constants.displayCollegeLogoPDF');
        $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
        $data['arrState'] = Config::get('constants.arrState');
        $destinationPath = Helpers::changeRootPath($filePath);
        $data['logoPath'] = $destinationPath['default'];

        $college_logo = '';
        if ($objCollegeDetails[0]['college_logo'] != '') {
            // $college_logo_url = str_replace('\\', "/", $destinationPath['view']).$objCollegeDetails[0]['college_logo'];
            $college_logo_url = UploadService::imageEmbed($destinationPath['view'].$objCollegeDetails[0]['college_logo']);
            // $college_logo = '<img src="data:image/png;base64,'. base64_encode(file_get_contents(public_path($college_logo_url))) .'" alt="rto_college_logo.jpg" style="height: auto; width: 85px;padding-left: 35px; padding-top: 20px;"  />';
            $college_logo = '<img src="'.$college_logo_url.'" alt="rto_college_logo.jpg" style="height: auto; width: 85px;padding-left: 35px; padding-top: 20px;"  />';
        }
        $data['objCollegeDetails'] = $objCollegeDetails;
        $data['objStudentCourse'] = $objStudentCourse;
        $data['objCurrentCountry'] = $objCurrentCountry;
        $data['objNationality'] = $objNationality;
        $data['objStudentDetails'] = $objStudentDetails;
        $data['firstStudentCourseId'] = $firstStudentCourseId;
        $data['college_logo'] = $college_logo;

        if ($download == 'save') {

            $html = view('frontend.offer_manage.student-invoice-pdf', $data);
            $pdf = App::make('dompdf.wrapper');

            $pdf->loadHTML($html);
            $filePath = Config::get('constants.uploadFilePath.StudentMailAttach');
            $destinationPath = Helpers::changeRootPath($filePath, $studentId);
            if (! is_dir($destinationPath['default'])) {
                // mkdir($destinationPath['default'], 0777);
                File::makeDirectory($destinationPath['default'], 0777, true, true);
            }
            $fileName = time().'_invoice.pdf';

            $pdfContent = $pdf->output();

            $tmpPath = tempnam(sys_get_temp_dir(), 'pdf_');
            file_put_contents($tmpPath, $pdfContent);
            $upload_success = UploadService::uploadAs($destinationPath['view'], new \Illuminate\Http\File($tmpPath), $fileName);

            @unlink($tmpPath);
            info('file uploaded form offer student invoice '.$fileName, [$upload_success]);

            return $fileName;
        } else {

            $pdf = App::make('dompdf.wrapper');

            // load from other pages use object or array by comma like (pdf-view,$user)
            $pdf->loadView('frontend.offer_manage.student-invoice-pdf', $data);

            // return $pdf->stream();exit;
            return $pdf->download('student-invoice.pdf', $data);

        }
    }

    public function downloadStudentInvoicePdf($courseId, $studentId, $download, Request $request)
    {

        $data = [];
        $data['pagetitle'] = '';
        if (isset($studentId)) {

            $objRtoStudents = new Students;

            $objStudentDetails = $objRtoStudents->getStudentDetailsV3($studentId, $courseId);

            $collegeId = Auth::user()->college_id;
            if ($objStudentDetails[0]->college_id == $collegeId) {

                $objRtoCountry = new Country;
                $objCurrentCountry = $objRtoCountry->getSelectedCountry($objStudentDetails[0]->current_country);
                $objNationality = $objRtoCountry->getSelectedCountry($objStudentDetails[0]->nationality);

                $objRtoCollegeDetails = new CollegeDetails;
                $objCollegeDetails = $objRtoCollegeDetails->getCollegeDetails($collegeId);

                $objRtoStudentCourse = new StudentCourse;
                $objStudentCourse = $objRtoStudentCourse->getAppliedStudentCourse($courseId, $studentId);
            } else {
                return view('frontend.error_page.unauthorized', $data);
            }

            if ($objStudentCourse->count() > 0) {
                $objStudentCourse = $objStudentCourse;
            } else {
                $objStudentCourse = [];
            }

            $studentDetails = new StudentDetails;
            $getOHSCCourseId = $studentDetails->getStudentDetailsOfOHSCCourseId($studentId);
            if (empty($getOHSCCourseId[0])) {
                $firstStudentCourseId = StudentCourse::where('rto_student_courses.student_id', '=', $studentId)
                    ->select('rto_student_courses.course_id')
                    ->get();
                $firstStudentCourseId = $firstStudentCourseId[0]->course_id;
            } else {
                $firstStudentCourseId = $getOHSCCourseId[0];
            }
        }

        // $data['logoPath'] = Config::get('constants.displayCollegeLogoPDF');
        $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
        $data['arrState'] = Config::get('constants.arrState');
        $destinationPath = Helpers::changeRootPath($filePath);
        $data['logoPath'] = $destinationPath['default'];

        $college_logo = '';
        if ($objCollegeDetails[0]['college_logo'] != '') {
            // $college_logo_url = str_replace('\\', "/", $destinationPath['view']).$objCollegeDetails[0]['college_logo'];
            // $college_logo = '<img src="data:image/png;base64,'. base64_encode(file_get_contents(public_path($college_logo_url))) .'" alt="rto_college_logo.jpg" style="height: auto; width: 85px;padding-left: 35px; padding-top: 20px;"  />';
            $college_logo_url = UploadService::imageEmbed($destinationPath['view'].$objCollegeDetails[0]['college_logo']);
            $college_logo = '<img src="'.$college_logo_url.'" alt="rto_college_logo.jpg" style="height: auto; width: 85px;padding-left: 35px; padding-top: 20px;"  />';
        }
        $data['objCollegeDetails'] = $objCollegeDetails;
        $data['objStudentCourse'] = $objStudentCourse;
        $data['objCurrentCountry'] = $objCurrentCountry;
        $data['objNationality'] = $objNationality;
        $data['objStudentDetails'] = $objStudentDetails;
        $data['firstStudentCourseId'] = $firstStudentCourseId;
        $data['college_logo'] = $college_logo;

        if ($download == 'save') {
            $html = view('frontend.offer_manage.student-invoice-pdf-v2', $data);
            $pdf = App::make('dompdf.wrapper');

            $pdf->loadHTML($html);
            $filePath = Config::get('constants.uploadFilePath.StudentMailAttach');
            $destinationPath = Helpers::changeRootPath($filePath, $studentId);
            if (! is_dir($destinationPath['default'])) {
                // mkdir($destinationPath['default'], 0777);
                File::makeDirectory($destinationPath['default'], 0777, true, true);
            }
            $pdf->save($destinationPath['default'].'invoice.pdf');
        } else {
            $pdf = App::make('dompdf.wrapper');

            // load from other pages use object or array by comma like (pdf-view,$user)
            $pdf->loadView('frontend.offer_manage.student-invoice-pdf-v2', $data);

            //            return $pdf->stream();exit;
            return $pdf->download('student-invoice.pdf', $data);
        }
    }

    public function agentPaymentInvoicePdf($courseId, $studentId, Request $request)
    {

        $data = [];
        $data['pagetitle'] = '';
        if (isset($studentId)) {

            $objRtoStudents = new Students;

            $objStudentDetails = $objRtoStudents->getStudentDetails($studentId);

            $collegeId = Auth::user()->college_id;
            if ($objStudentDetails[0]->college_id == $collegeId) {

                $objRtoCountry = new Country;
                $objCurrentCountry = $objRtoCountry->getSelectedCountry($objStudentDetails[0]->current_country);
                $objNationality = $objRtoCountry->getSelectedCountry($objStudentDetails[0]->nationality);

                $objRtoCollegeDetails = new CollegeDetails;
                $objCollegeDetails = $objRtoCollegeDetails->getCollegeDetails($collegeId);

                $objRtoStudentCourse = new StudentCourse;
                $objStudentCourse = $objRtoStudentCourse->getAppliedStudentCourse($courseId, $studentId);
            } else {
                return view('frontend.error_page.unauthorized', $data);
            }

            if ($objStudentCourse->count() > 0) {
                $objStudentCourse = $objStudentCourse;
            } else {
                $objStudentCourse = [];
            }
        }

        $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
        $destinationPath = Helpers::changeRootPath($filePath);
        $data['logoPath'] = $destinationPath['default'];

        $college_logo = '';
        if ($objCollegeDetails[0]['college_logo'] != '') {
            // $college_logo_url = str_replace('\\', "/", $destinationPath['view']).$objCollegeDetails[0]['college_logo'];
            $college_logo_url = UploadService::imageEmbed($destinationPath['view'].$objCollegeDetails[0]['college_logo']);
            // $college_logo = '<img src="data:image/png;base64,'. base64_encode(file_get_contents(public_path($college_logo_url))) .'" alt="rto_college_logo.jpg" style="height: auto; width: 85px;padding-left: 35px; padding-top: 20px;"  />';
            $college_logo = '<img src="'.$college_logo_url.'" alt="rto_college_logo.jpg" style="height: auto; width: 85px;padding-left: 35px; padding-top: 20px;"  />';
        }

        $data['objCollegeDetails'] = $objCollegeDetails;
        $data['objStudentCourse'] = $objStudentCourse;
        $data['objCurrentCountry'] = $objCurrentCountry;
        $data['objNationality'] = $objNationality;
        $data['objStudentDetails'] = $objStudentDetails;
        $data['college_logo'] = $college_logo;

        $pdf = App::make('dompdf.wrapper');

        // load from other pages use object or array by comma like (pdf-view,$user)
        $pdf->loadView('frontend.offer_manage.agent-payment-invoice-pdf', $data);

        // open in browser
        // return $pdf->stream();exit;
        return $pdf->download('agent-'.$data['objStudentCourse'][0]['agent_id'].'-invoice.pdf');
    }

    public function offerGenerateInvoiceView(Request $request, $studenId)
    {
        $collegeId = Auth::user()->college_id;
        $objStudents = new Students;
        $arrStudents = $objStudents->getExistsReferenceId($studenId, $collegeId);
        $objStudents = new StudentCourse;
        $arrStudentsCourseData = $objStudents->getOfferStudentCourseData($studenId);

        $data['pagetitle'] = 'Apply Online';
        $data['header'] = [
            'title' => 'Offers Generate Invoice View',
            'breadcrumb' => [
                'Home' => route('user_dashboard'),
                'Offers View' => '',
            ]];
        $data['plugincss'] = [];
        $data['css'] = ['frontendcomman.css'];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js', 'ckeditor/ckeditor.js'];
        $data['js'] = ['offermanage.js'];
        $data['funinit'] = ['OfferManage.initOfferManageSendMail()'];
        $data['activateValue'] = 'Offers';
        $data['arrStudents'] = $arrStudents;
        $data['studenIdPrimary'] = $studenId;
        $data['arrStudentsCourse'] = $arrStudentsCourseData[1];
        $data['arrStudentsCourseData'] = $arrStudentsCourseData[0];
        $data['mainmenu'] = 'clients';

        return view('frontend.offer_manage.offer-generate-invoice-view', $data);
    }

    public function offerGenerateInvoice(Request $request)
    {
        $data['header'] = [
            'title' => 'Offers Generate Invoice',
            'breadcrumb' => [
                'Home' => route('user_dashboard'),
                'Offers' => '',
            ]];
        $data['pagetitle'] = 'Apply Online';
        $data['plugincss'] = [];
        $data['css'] = ['frontendcomman.css'];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js', 'ckeditor/ckeditor.js'];
        $data['js'] = ['offermanage.js'];
        $data['funinit'] = ['OfferManage.initOfferManageSendMail()'];
        $data['activateValue'] = 'Offers';
        $data['mainmenu'] = 'clients';

        return view('frontend.offer_manage.offer-generate-invoice', $data);
    }

    public function deleteStudentCourse(Request $request)
    {
        $id = $request->id;
        $studentID = $request->student_id;
        $courseID = $request->course_id;
        $flag = $request->flag;

        $objStudentCourse = new StudentCourse;
        $result = $objStudentCourse->deleteStudentCourseRecordWithId($id, $studentID, $courseID);

        if ($result) {
            $request->session()->flash('session_success', 'This Course record is deleted.');
        } else {
            $request->session()->flash('session_error', "Can't delete this course as it is the last course applied for this offer.");
        }

        if ($flag == 'offer') {
            return redirect(route('offer-manage-add-course', ['id' => $studentID]));
        } elseif ($flag == 'step3') {
            return redirect(route('apply-online-step3', ['id' => $studentID]));
        }
    }

    public function attachStudentInvoicePdf($courseId, $studentId)
    {

        $msg = '';
        $data = [];
        $data['pagetitle'] = '';
        $collegeId = Auth::user()->college_id;

        if ($courseId > 0) {
            if (isset($studentId)) {

                $objRtoStudents = new Students;
                $objStudentDetails = $objRtoStudents->getStudentDetails($studentId);

                $offerStatus = StudentCourse::where('student_id', $studentId)->where('course_id', $courseId)->value('offer_status');

                if ($objStudentDetails[0]->college_id == $collegeId) {

                    if ($offerStatus == 'Offered') {

                        $objRtoCountry = new Country;
                        $objCurrentCountry = $objRtoCountry->getSelectedCountry($objStudentDetails[0]->current_country);
                        $objNationality = $objRtoCountry->getSelectedCountry($objStudentDetails[0]->nationality);

                        $objRtoCollegeDetails = new CollegeDetails;
                        $objCollegeDetails = $objRtoCollegeDetails->getCollegeDetails($collegeId);

                        $objRtoStudentCourse = new StudentCourse;
                        $objStudentCourse = $objRtoStudentCourse->getAppliedStudentCourse($courseId, $studentId);

                        $filePath1 = Config::get('constants.uploadFilePath.CollegeLogo');
                        $destinationPath1 = Helpers::changeRootPath($filePath1);
                        $data['logoPath'] = $destinationPath1['default'];

                        $college_logo = '';
                        if (isset($objCollegeDetails[0]) && ! empty($objCollegeDetails[0]['college_logo'])) {
                            $college_logo_url = str_replace('\\', '/', $destinationPath1['view']).$objCollegeDetails[0]['college_logo'];
                            $college_logo = '<img src="data:image/png;base64,'.base64_encode(file_get_contents(public_path($college_logo_url))).'" alt="rto_college_logo.jpg" style="height: auto; width: 85px;padding-left: 35px; padding-top: 20px;"  />';
                        }

                        $data['objCollegeDetails'] = $objCollegeDetails;
                        $data['objStudentCourse'] = ($objStudentCourse->count() > 0) ? $objStudentCourse : [];
                        $data['objCurrentCountry'] = $objCurrentCountry;
                        $data['objNationality'] = $objNationality;
                        $data['objStudentDetails'] = $objStudentDetails;
                        $data['college_logo'] = $college_logo;

                        $pdf = App::make('dompdf.wrapper');

                        // load from other pages use object or array by comma like (pdf-view,$user)
                        $pdf->loadView('frontend.offer_manage.student-invoice-pdf', $data);

                        $filePath = Config::get('constants.uploadFilePath.StudentMailAttach');
                        $destinationPath = Helpers::changeRootPath($filePath, $studentId);
                        $file = $destinationPath['default'].'invoice.pdf';
                        file_put_contents($file, $pdf->output());

                        $status = 'true';
                    } else {
                        $status = 'false';
                        $msg = 'Your application is not Offered';
                    }
                }
            }
        } else {
            $msg = 'Please select any course';
            $status = 'false';
        }

        return ['status' => $status, 'msg' => $msg];
    }

    public function attachOfferLetterPdf($courseId, $studentId)
    {

        $msg = '';
        $data = [];
        $data['pagetitle'] = '';
        $collegeId = Auth::user()->college_id;

        if ($courseId > 0) {
            if (isset($studentId)) {

                $objRtoStudents = new Students;
                $objStudentDetails = $objRtoStudents->getStudentDetails($studentId);

                $offerStatus = StudentCourse::where('student_id', $studentId)->where('course_id', $courseId)->value('offer_status');

                if ($objStudentDetails[0]->college_id == $collegeId) {

                    if ($offerStatus == 'Offered') {

                        $objRtoCountry = new Country;
                        $objCurrentCountry = $objRtoCountry->getSelectedCountry($objStudentDetails[0]->current_country);
                        $objNationality = $objRtoCountry->getSelectedCountry($objStudentDetails[0]->nationality);

                        $objRtoCollegeDetails = new CollegeDetails;
                        $objCollegeDetails = $objRtoCollegeDetails->getCollegeDetails($collegeId);

                        $objRtoStudentCourse = new StudentCourse;
                        $objStudentCourse = $objRtoStudentCourse->getAppliedStudentCourse($courseId, $studentId);

                        $objCourse = new Courses;
                        $courseDetail = $objCourse->getCourseCode($courseId);
                        $courseName = $courseDetail[0]['course_code'].' : '.$courseDetail[0]['course_name'];

                        $objRtoOfferUpfrontFeeSchedule = new OfferUpfrontFeeSchedule;
                        $arrUpfrontFeeList = $objRtoOfferUpfrontFeeSchedule->getUpfrontFeeSchedule($studentId, $courseId);

                        $data['logoPath'] = Config::get('constants.displayCollegeLogoPDF');
                        $filePath1 = Config::get('constants.uploadFilePath.CollegeLogo');
                        $destinationPath1 = Helpers::changeRootPath($filePath1);
                        $data['logoPath'] = $destinationPath1['default'];

                        $data['objCollegeDetails'] = $objCollegeDetails;
                        $data['objStudentCourse'] = (count($objStudentCourse) > 0) ? $objStudentCourse : [];
                        $data['courseName'] = $courseName;
                        $data['arrUpfrontFeeList'] = (count($arrUpfrontFeeList) > 0) ? $arrUpfrontFeeList : [];
                        $data['objCurrentCountry'] = $objCurrentCountry;
                        $data['objNationality'] = $objNationality;
                        $data['objStudentDetails'] = $objStudentDetails;
                        $data['arrState'] = Config::get('constants.arrState');
                        $pdf = App::make('dompdf.wrapper');

                        // load from other pages use object or array by comma like (pdf-view,$user)
                        $pdf->loadView('frontend.student_course.student-offer-letter-pdf', $data);

                        $filePath = Config::get('constants.uploadFilePath.StudentMailAttach');
                        $destinationPath = Helpers::changeRootPath($filePath, $studentId);

                        $studentOfferLetterName = $this->studentOfferLetterName($studentId);
                        $file = $destinationPath['default'].$studentOfferLetterName.'.pdf';
                        file_put_contents($file, $pdf->output());

                        $status = 'true';
                    } else {
                        $status = 'false';
                        $msg = 'Your application is not Offered';
                    }
                }
            }
        } else {
            $msg = 'Please select any course';
            $status = 'false';
        }

        return ['status' => $status, 'msg' => $msg];
    }

    public function deleteOfferManageDoc(Request $request)
    {
        $collegeMaterialId = $request->id;
        $collegeId = Auth::user()->college_id;
        $objRtoCollegeMaterials = new CollegeMaterials;
        $arrParentId = $objRtoCollegeMaterials->getParentId($collegeMaterialId, $collegeId);
        $parentId = $arrParentId[0]['parent_id'];
        $deleteData = $objRtoCollegeMaterials->deleteCollegeMaterials($collegeMaterialId);

        if ($deleteData) {
            $request->session()->flash('session_success', 'College Materials Successfully Deleted.');
        } else {
            $request->session()->flash('session_error', 'Something will be wrong. Please try again.');
        }

        return redirect(route('offer-manage-document', ['id' => $parentId, 'primaryStudentId' => $parentId]));
    }

    public function deleteOfferManagecheckListDocument(Request $request)
    {

        $studentOfferDocumentId = $request->id;
        $collegeId = Auth::user()->college_id;

        $arrStudentOfferDocumentDetails = StudentOfferDocuments::find($studentOfferDocumentId);

        $objRtoStudentOfferDocuments = new StudentOfferDocuments;
        $deleteData = $objRtoStudentOfferDocuments->deleteStudentOfferDocumentsId($studentOfferDocumentId);
        if ($deleteData) {
            $request->session()->flash('session_success', 'College Materials Successfully Deleted.');
        } else {
            $request->session()->flash('session_error', 'Something will be wrong. Please try again.');
        }

        return redirect(route('offer-manage-checklist', ['id' => $arrStudentOfferDocumentDetails['rto_student_id']]));
    }

    public function deleteStudentCourcecheckListDocument(Request $request)
    {

        $studentOfferDocumentId = $request->id;
        $collegeId = Auth::user()->college_id;

        $arrStudentOfferDocumentDetails = StudentOfferDocuments::find($studentOfferDocumentId);

        $objRtoStudentOfferDocuments = new StudentOfferDocuments;
        $deleteData = $objRtoStudentOfferDocuments->deleteStudentOfferDocumentsId($studentOfferDocumentId);
        if ($deleteData) {
            $request->session()->flash('session_success', 'College Materials Successfully Deleted.');
        } else {
            $request->session()->flash('session_error', 'Something will be wrong. Please try again.');
        }

        return redirect(route('student-course-checklist', ['id' => $arrStudentOfferDocumentDetails['rto_student_id']]));
    }

    public function editStudentDocumentDirecory(Request $request)
    {

        $collegeId = $this->loginUser->college_id;
        $userId = $this->loginUser->id;

        if ($request->isMethod('post')) {
            $studentId = $request->input('student_id');
            $studentDocId = $request->input('primary_id');

            $objStudentUploadFile = new StudentUploadFile;
            $result = $objStudentUploadFile->editStudentFileDirecory($collegeId, $userId, $request);
            $parentId = $objStudentUploadFile->getParentId($studentDocId, $collegeId);

            if ($result) {
                $request->session()->flash('session_success', 'Renamed successfully.');
            } else {
                $request->session()->flash('session_error', 'Something will be wrong. Please try again.');
            }

            return redirect(route('list-student-document', ['student_id' => $studentId, 'parent_id' => $parentId]));
        }
    }

    public function deleteUploadedFile(Request $request)
    {

        $studentId = $request->student_id;
        $primaryId = $request->primary_id;
        $collegeId = $this->loginUser->college_id;

        $objStudentUploadFile = new StudentUploadFile;
        $parentId = $objStudentUploadFile->getParentId($primaryId, $collegeId);
        $result = $objStudentUploadFile->deleteStudentUploadedFile($collegeId, $studentId, $primaryId);

        if ($result) {
            $request->session()->flash('session_success', 'Student File Delete Successfully.');
        } else {
            $request->session()->flash('session_error', 'Something will be wrong. Please try again.');
        }

        return redirect(route('list-student-document', ['student_id' => $studentId, 'parent_id' => $parentId]));
    }

    public function exportOfferManage(Request $request)
    {
        /* HOW ARE DIFFERENT CHARACTERS SET IN OUR REQUEST ? */
        ob_clean();

        return Excel::download(new ManageOfferExport, 'Offer_List.xlsx', \Maatwebsite\Excel\Excel::XLSX);
    }
}
