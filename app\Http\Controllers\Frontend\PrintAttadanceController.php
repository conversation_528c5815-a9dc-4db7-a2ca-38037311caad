<?php

namespace App\Http\Controllers\Frontend;

use App;
use App\Exports\StudentsClassAttendanceExport;
use App\Exports\StudentsClassWithBatchExport;
use App\Helpers\Helpers;
use App\Http\Controllers\Controller;
use App\Model\CampusVenue;
use App\Model\CollegeCampus;
use App\Model\Colleges;
use App\Model\CourseType;
use App\Model\SemesterDivision;
use App\Model\StudentSubjectEnrolment;
use App\Model\Timetable;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Validator;
use Maatwebsite\Excel\Facades\Excel;
use Support\Services\UploadService;

class PrintAttadanceController extends Controller
{
    public function __construct()
    {
        // $this->rememberToken
    }

    public function timetableReplacementTeacher(Request $request)
    {

        $collegeId = Auth::user()->college_id;

        $objRtoTimetable = new Timetable;
        $arrTeacherList = $objRtoTimetable->teacherListForReplacement($collegeId);

        $arrYearList = $objRtoTimetable->yearListForReplacement($collegeId);
        // $defaultYear[''] = '- - Select Year - -';
        if ($request->isMethod('post')) {
            $validations = [
                'teacher_id' => 'required|max:255',
                'year_list' => 'required',
                'semester_id' => 'required',
                'term' => 'required',
                'subject_id' => 'required',
                'new_teacher_id' => 'required',
                'batch' => 'required',
                'class_id' => 'required',
            ];

            if ($request['change_type'] == 'relief') {
                $validations['relief_from_week'] = 'required';
                $validations['relief_to_week'] = 'required';
            }

            $validator = Validator::make($request->all(), $validations);

            if ($validator->fails()) {
                return redirect(route('timetable-replacement-teacher'))
                    ->withErrors($validator)
                    ->withInput();
            }

            // save replacement Details
            $saveReplacementDetails = $objRtoTimetable->saveTeacherReplacement($collegeId, $request);
            $request->session()->flash('session_success', 'Teacher Replacement Successfully Saved.');

            return redirect(route('timetable-replacement-teacher'));
        }

        $data['pagetitle'] = 'Timetable Manage';
        $data['plugincss'] = [];
        $data['css'] = [];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js'];
        $data['js'] = ['viewTimetable.js'];
        $data['funinit'] = ['viewTimetable.initReplacementTeacher()'];
        $data['activateValue'] = 'Timetable';

        $data['arrEmpty'] = [];
        $data['arrTeacherList'] = $arrTeacherList;
        $data['arrYearList'] = $arrYearList;

        return view('frontend.timetable_assessment_group.timetable-replacement-teacher', $data);
    }

    public function editManageAssessmentGroup(Request $request)
    {
        $collegeId = Auth::user()->college_id;
        $objRtoTimetable = new Timetable;
        $arrVenueList = $objRtoTimetable->editAssessmentGroup($collegeId, $request);
        $request->session()->flash('session_success', 'Assessment Group Update successfully');

        return redirect(route('manage-assessment-group'));
    }

    public function timetableReport()
    {

        $arrSelectReportType = Config::get('constants.arrSelectReportType');
        $arrTimeTableReportOrderBy = Config::get('constants.arrTimeTableReportOrderBy');
        $arrTimeTableReportSelectedBy = Config::get('constants.arrTimeTableReportSelectedBy');

        $collegeId = Auth::user()->college_id;
        $objRtoCourseType = new CourseType;
        $arrCourseType = $objRtoCourseType->getCourseType($collegeId);

        // get venue list
        $objRtoTimetable = new Timetable;
        $arrVenueList = $objRtoTimetable->getVenueList($collegeId);

        $data['pagetitle'] = 'Timetable Manage';
        $data['plugincss'] = [];
        $data['css'] = [];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js'];
        $data['js'] = ['timetablereport.js'];
        $data['funinit'] = ['TimetableReport.initManageTimetableReport()'];
        $data['activateValue'] = 'Timetable';

        $data['arrSelectReportType'] = $arrSelectReportType;
        $data['arrEmpty'] = ['' => '-- Select Value --'];
        $data['arrTimeTableReportOrderBy'] = $arrTimeTableReportOrderBy;
        $data['arrTimeTableReportSelectedBy'] = $arrTimeTableReportSelectedBy;

        $data['arrCourseType'] = $arrCourseType;
        $data['arrVenueList'] = $arrVenueList;

        return view('frontend.timetable_assessment_group.timetable_report', $data);
    }

    public function weeklyClass(Request $request)
    {

        $collegeId = Auth::user()->college_id;
        $arrAttendanceListStatus = Config::get('constants.arrAttendanceListStudentStatus');

        $objRtoCourseType = new CourseType;
        $arrCourseType = $objRtoCourseType->getCourseType($collegeId);

        $objRtoCollegeCampus = new CollegeCampus;
        $arrCollegeCampus = $objRtoCollegeCampus->getCollegeCampusList($collegeId);

        if ($request->isMethod('post')) {

            $validations = [
                'class_id' => 'required',
                'subject_id' => 'required',
                'semester_id' => 'required',
                'term' => 'required',
            ];
            $validator = Validator::make($request->all(), $validations);

            if ($validator->fails()) {
                return redirect(route('attendance-list-by-class'))->withErrors($validator)->withInput();
            }

            $attdType = $request->input('attedance_type');
            $subjectId = $request->input('subject_id');
            $semesterId = $request->input('semester_id');
            $termId = $request->input('term');
            $batch = $request->input('class_id');
            $orderByField = $request->input('orderByField');
            $orderByType = $request->input('orderByType');
            $studentStatus = $request->input('student_status');

            $objTimetable = new Timetable;
            $arrClassAttdList = $objTimetable->getClassAttendanceData($collegeId, $batch, $subjectId, $semesterId, $termId);

            if (empty($arrClassAttdList[0])) {
                $request->session()->flash('session_error', 'No Timetable record found');

                return redirect(route('attendance-list-by-class'));
            }

            $getAllDays = $this->setupWeeklyLogic($arrClassAttdList);

            $objSubjectEnrolment = new StudentSubjectEnrolment;
            $arrStudSubjectEnrollList = $objSubjectEnrolment->getSemsterEnrollStudentAttendance($semesterId, $termId, $subjectId, $batch, $collegeId, $orderByField, $orderByType, $studentStatus);

            $batchDate = $this->findBatchStartDateEndDate($arrClassAttdList);

            $holidayWeek = SemesterDivision::where('college_id', '=', $collegeId)->where('semester_id', '=', $semesterId)->where('term', '=', $termId)->where('is_holiday', '=', 1)->pluck('week_period')->toArray();

            $data['batch'] = $arrClassAttdList[0]['batch'];
            $data['roomId'] = $arrClassAttdList[0]['room_id'];
            $data['startTime'] = $arrClassAttdList[0]['start_time'];
            $data['endTime'] = $arrClassAttdList[0]['finish_time'];
            $data['batchDate'] = $batchDate;
            $data['subjectCode'] = $arrClassAttdList[0]['subject_code'];
            $data['subjectName'] = $arrClassAttdList[0]['subject_name'];
            $data['teacherName'] = $arrClassAttdList[0]['name_title'].' '.$arrClassAttdList[0]['first_name'].' '.$arrClassAttdList[0]['last_name'];
            $data['arrClassAttdList'] = $arrClassAttdList;
            $data['studentList'] = $arrStudSubjectEnrollList;
            $data['getAllDays'] = $getAllDays;
            $data['campus_name'] = $arrClassAttdList[0]['campus_name'];
            $data['venue_name'] = $arrClassAttdList[0]['venue_name'];
            $data['holidayWeek'] = $holidayWeek;

            if (count($arrStudSubjectEnrollList) > 0) {

                if ($attdType == 'Excel') {

                    $data['room_name'] = $arrClassAttdList[0]['room_name'];
                    $data['semester_name'] = $arrClassAttdList[0]['semester_name'];

                    return Excel::download(new StudentsClassAttendanceExport($data), $arrClassAttdList[0]['batch'].'-Attendance.xlsx');
                } elseif ($attdType == 'PDF') {

                    $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
                    $arrState = Config::get('constants.arrState');

                    $destinationPath = Helpers::changeRootPath($filePath);

                    $collegeDetail = Colleges::join('rto_college_details', 'rto_college_details.college_id', '=', 'rto_colleges.id')->where('id', $collegeId)->get(['rto_colleges.college_logo', 'rto_colleges.college_name', 'rto_college_details.street_address', 'rto_college_details.street_suburb', 'rto_college_details.street_state', 'rto_college_details.street_postcode', 'rto_colleges.contact_phone', 'rto_college_details.fax', 'rto_colleges.contact_email'])->first();

                    $collegeLogoImg = '';
                    if ($collegeDetail->college_logo != '') {
                        $college_logo_url = UploadService::imageEmbed($destinationPath['view'].$collegeDetail->college_logo);
                        $collegeLogoImg = '<img src="'.$college_logo_url.'" alt="rto_college_logo.jpg" style="height: auto; width: 85px;padding-left: 35px; padding-top: 20px;"  />';
                    }
                    $venueDetail = CampusVenue::where('college_id', '=', $collegeId)->where('id', '=', $arrClassAttdList[0]['venue_id'])->get(['id', 'venue_name', 'unit_detail', 'street_no', 'street_name', 'sub_urb', 'state', 'contact_no', 'postcode'])->first();
                    $data['collegeLogoImg'] = $collegeLogoImg;
                    $data['collegeDetail'] = $collegeDetail;
                    $data['venueDetail'] = $venueDetail;
                    $data['arrState'] = $arrState;

                    $pdfName = $arrClassAttdList[0]['batch'].'-Attendance';

                    $pdf = App::make('dompdf.wrapper');
                    $pdf->loadView('frontend.print_attadance.class-attendance-pdf', $data);

                    return $pdf->download("$pdfName.pdf");
                }
            } else {
                $request->session()->flash('session_error', 'No Student record found');

                return redirect(route('attendance-list-by-class'))->withErrors($validator)->withInput();
            }
        }
        $data['header'] = [
            'title' => 'Generate Attendance List By Class',
            'breadcrumb' => [
                'Home' => route('user_dashboard'),
                'Print Attendance List' => '',
                'Attendance List By Class' => '',
            ]];
        $data['pagetitle'] = 'Generate Attendance List By Class';
        $data['plugincss'] = [];
        $data['css'] = [];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js'];
        $data['js'] = ['printAttendanceList.js'];
        $data['funinit'] = ['PrintAttendanceList.initWeeklyClass()'];

        $data['arrCollegeCampus'] = $arrCollegeCampus;
        $data['arrCourseType'] = $arrCourseType;
        $data['arrAttendanceListStatus'] = $arrAttendanceListStatus;
        $data['orderByField'] = ['generated_stud_id' => 'Student ID', 'first_name' => 'Student Name'];
        $data['orderByType'] = ['ASC' => 'Ascending', 'DESC' => 'Descending'];
        $data['mainmenu'] = 'clients';

        return view('frontend.print_attadance.weekly-class-list', $data);
    }

    public function bulkPdf(Request $request)
    {

        $collegeId = Auth::user()->college_id;
        $arrStudentStatus = Config::get('constants.arrAttendanceListStudentStatus');
        $objRtoCourseType = new CourseType;
        $arrCourseType = $objRtoCourseType->getCourseType($collegeId);

        $objRtoCollegeCampus = new CollegeCampus;
        $arrCollegeCampus = $objRtoCollegeCampus->getCollegeCampusList($collegeId);

        if ($request->isMethod('post')) {

            $validations['timetableIdArr'] = 'required';
            $checkValidations = [
                'timetableIdArr.required' => 'Please select at least One Record from Class list.',
            ];
            $validator = Validator::make($request->all(), $validations, $checkValidations);

            if ($validator->fails()) {
                return redirect(route('attendance-list-for-bulk-pdf'))->withErrors($validator)->withInput();
            }

            $timetableId = $request->input('timetableId');
            for ($i = 0; $i < count($timetableId); $i++) {

                $timeTableDetail = Timetable::where('id', $timetableId[$i])->get()->first();

                $classId = $timeTableDetail->batch;
                $subjectId = $timeTableDetail->subject_id;
                $semesterId = $timeTableDetail->semester_id;
                $termId = $timeTableDetail->term;
                $batch = $timeTableDetail->batch;
                $orderByField = 'generated_stud_id';
                $orderByType = 'ASC';
                $studentStatus = $request->input('status');

                $objTimetable = new Timetable;
                $arrClassAttdList = $objTimetable->getClassAttendanceData($collegeId, $batch, $subjectId, $semesterId, $termId);

                if (empty($arrClassAttdList[0])) {
                    $request->session()->flash('session_error', 'No Timetable record found');

                    return redirect(route('attendance-list-by-class'))->withErrors($validator)->withInput();
                }

                $getAllDays = $this->setupWeeklyLogic($arrClassAttdList);

                $objSubjectEnrolment = new StudentSubjectEnrolment;
                $arrStudSubjectEnrollList = $objSubjectEnrolment->getSemsterEnrollStudentAttendance($semesterId, $termId, $subjectId, $classId, $collegeId, $orderByField, $orderByType, $studentStatus);

                $batchDate = $this->findBatchStartDateEndDate($arrClassAttdList);

                $collegeId = Auth::user()->college_id;

                $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
                $arrState = Config::get('constants.arrState');

                $destinationPath = Helpers::changeRootPath($filePath);

                $collegeDetail = Colleges::join('rto_college_details', 'rto_college_details.college_id', '=', 'rto_colleges.id')->where('id', $collegeId)->get(['rto_colleges.college_logo', 'rto_colleges.college_name', 'rto_college_details.street_address', 'rto_college_details.street_suburb', 'rto_college_details.street_state', 'rto_college_details.street_postcode', 'rto_colleges.contact_phone', 'rto_college_details.fax', 'rto_colleges.contact_email'])->first();

                $collegeLogoImg = '';
                if ($collegeDetail->college_logo != '') {
                    $college_logo_url = UploadService::imageEmbed($destinationPath['view'].$collegeDetail->college_logo);
                    $collegeLogoImg = '<img src="'.$college_logo_url.'" alt="rto_college_logo.jpg" style="height: auto; width: 85px;padding-left: 35px; padding-top: 20px;"  />';
                }

                $venueDetail = CampusVenue::where('college_id', '=', $collegeId)->where('id', '=', $arrClassAttdList[0]['venue_id'])->get(['id', 'venue_name', 'unit_detail', 'street_no', 'street_name', 'sub_urb', 'state', 'contact_no', 'postcode'])->first();

                $data['batchDate'][] = $batchDate;
                $data['batch'][] = $arrClassAttdList[0]['batch'];
                $data['roomId'][] = $arrClassAttdList[0]['room_id'];
                $data['startTime'][] = $arrClassAttdList[0]['start_time'];
                $data['endTime'][] = $arrClassAttdList[0]['finish_time'];
                $data['subjectCode'][] = $arrClassAttdList[0]['subject_code'];
                $data['subjectName'][] = $arrClassAttdList[0]['subject_name'];
                $data['teacherName'][] = $arrClassAttdList[0]['name_title'].' '.$arrClassAttdList[0]['first_name'].' '.$arrClassAttdList[0]['last_name'];
                $data['collegeLogoImg'] = $collegeLogoImg;
                $data['studentList'][] = $arrStudSubjectEnrollList;
                $data['collegeDetail'] = $collegeDetail;
                $data['venueDetail'] = $venueDetail;
                $data['arrState'] = $arrState;
                $data['arrClassAttdList'][] = $arrClassAttdList;
                $data['getAllDays'][] = $getAllDays;
                $data['timetableId'][] = $timetableId;
            }

            $pdfName = 'Bulk-Attendance';

            $pdf = App::make('dompdf.wrapper');
            $pdf->loadView('frontend.print_attadance.weekly-attendance-pdf', $data);

            return $pdf->download("$pdfName.pdf");
        }
        $data['header'] = [
            'title' => 'Generate Attendance List By Class',
            'breadcrumb' => [
                'Home' => route('user_dashboard'),
                'Print Attendance List' => route('attendance-list-by-class'),
                'Weekly Attendance List' => '',
            ]];
        $data['pagetitle'] = 'Generate Weekly Attendance List';
        $data['plugincss'] = [];
        $data['css'] = [];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js'];
        $data['js'] = ['printAttendanceList.js'];
        $data['funinit'] = ['PrintAttendanceList.initBulkPdf()'];

        $data['arrCollegeCampus'] = $arrCollegeCampus;
        $data['arrCourseType'] = $arrCourseType;
        $data['arrStudentStatus'] = $arrStudentStatus;
        $data['mainmenu'] = 'clients';

        return view('frontend.print_attadance.bulk-pdf', $data);
    }

    public function individualClass(Request $request)
    {

        $collegeId = Auth::user()->college_id;
        $arrDayList = Config::get('constants.arrDays');

        $objRtoCourseType = new CourseType;
        $arrCourseType = $objRtoCourseType->getCourseType($collegeId);

        $objRtoCollegeCampus = new CollegeCampus;
        $arrCollegeCampus = $objRtoCollegeCampus->getCollegeCampusList($collegeId);

        if ($request->isMethod('post')) {

            $timeTableDetail = Timetable::where('id', $request->input('specific_class'))->get()->first();

            $specific_class = explode('@', $request->input('specific_class'));
            $attdType = $request->input('attedance_type');
            $courseTypeId = $request->input('course_type_id');
            $semesterId = $request->input('semester_id');
            $termId = $request->input('term_id');
            $subjectId = $timeTableDetail->subject_id;
            $batch = $timeTableDetail->batch;
            $orderByField = 'generated_stud_id';
            $orderByType = 'ASC';

            $objTimetable = new Timetable;
            $arrClassAttdList = $objTimetable->getClassAttendanceData($collegeId, $batch, $subjectId, $semesterId, $termId);

            if (empty($arrClassAttdList[0])) {
                $request->session()->flash('session_error', 'No Timetable record found');

                return redirect(route('attendance-list-by-class'))->withErrors($validator)->withInput();
            }

            $getAllDays = $this->setupWeeklyLogic($arrClassAttdList);

            $objSubjectEnrolment = new StudentSubjectEnrolment;
            $arrStudSubjectEnrollList = $objSubjectEnrolment->getSemsterEnrollStudentAttendance($semesterId, $termId, $subjectId, $batch, $collegeId, $orderByField, $orderByType);

            $batchDate = $this->findBatchStartDateEndDate($arrClassAttdList);

            $data['batch'] = $arrClassAttdList[0]['batch'];
            $data['roomId'] = $arrClassAttdList[0]['room_id'];
            $data['startTime'] = $arrClassAttdList[0]['start_time'];
            $data['endTime'] = $arrClassAttdList[0]['finish_time'];
            $data['batchDate'] = $batchDate;
            $data['subjectCode'] = $arrClassAttdList[0]['subject_code'];
            $data['subjectName'] = $arrClassAttdList[0]['subject_name'];
            $data['teacherName'] = $arrClassAttdList[0]['name_title'].' '.$arrClassAttdList[0]['first_name'].' '.$arrClassAttdList[0]['last_name'];
            $data['arrClassAttdList'] = $arrClassAttdList;
            $data['studentList'] = $arrStudSubjectEnrollList;
            $data['getAllDays'] = $getAllDays;

            if (count($arrStudSubjectEnrollList) > 0) {

                if ($attdType == 'Excel') {

                    $data['room_name'] = $arrClassAttdList[0]['room_name'];
                    $data['semester_name'] = $arrClassAttdList[0]['semester_name'];

                    return Excel::download(new StudentsClassAttendanceExport($data), $arrClassAttdList[0]['batch'].'-Attendance.xlsx');
                } elseif ($attdType == 'PDF') {

                    $collegeId = Auth::user()->college_id;

                    $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
                    $arrState = Config::get('constants.arrState');

                    $destinationPath = Helpers::changeRootPath($filePath);

                    $collegeDetail = Colleges::join('rto_college_details', 'rto_college_details.college_id', '=', 'rto_colleges.id')->where('id', $collegeId)->get(['rto_colleges.college_logo', 'rto_colleges.college_name', 'rto_college_details.street_address', 'rto_college_details.street_suburb', 'rto_college_details.street_state', 'rto_college_details.street_postcode', 'rto_colleges.contact_phone', 'rto_college_details.fax', 'rto_colleges.contact_email'])->first();
                    $venueDetail = CampusVenue::where('college_id', '=', $collegeId)->where('id', '=', $arrClassAttdList[0]['venue_id'])->get(['id', 'venue_name', 'unit_detail', 'street_no', 'street_name', 'sub_urb', 'state', 'contact_no', 'postcode'])->first();

                    $collegeLogoImg = '';
                    if ($collegeDetail->college_logo != '') {
                        $college_logo_url = UploadService::imageEmbed($destinationPath['view'].$collegeDetail->college_logo);
                        $collegeLogoImg = '<img src="'.$college_logo_url.'" alt="rto_college_logo.jpg" style="height: auto; width: 85px;padding-left: 35px; padding-top: 20px;"  />';
                    }

                    $data['collegeLogoImg'] = $collegeLogoImg;
                    $data['collegeDetail'] = $collegeDetail;
                    $data['venueDetail'] = $venueDetail;
                    $data['arrState'] = $arrState;

                    $pdfName = $arrClassAttdList[0]['batch'].'-Attendance';

                    $pdf = App::make('dompdf.wrapper');
                    $pdf->loadView('frontend.print_attadance.class-attendance-pdf', $data);

                    return $pdf->download("$pdfName.pdf");
                }
            } else {
                $request->session()->flash('session_error', 'No Student record found');

                return redirect(route('attendance-list-by-individual-class'));
            }
        }

        $data['header'] = [
            'title' => 'Generate Class Attendance List By Individual Class',
            'breadcrumb' => [
                'Home' => route('user_dashboard'),
                'Attendance List By Individual Class' => '',
            ]];

        $data['pagetitle'] = 'Generate Class Attendance List By Individual Class';
        $data['plugincss'] = [];
        $data['css'] = [];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js'];
        $data['js'] = ['printAttendanceList.js'];
        $data['funinit'] = ['PrintAttendanceList.initIndividualClass()'];

        $data['arrCollegeCampus'] = $arrCollegeCampus;
        $data['arrCourseType'] = $arrCourseType;
        $data['arrDayList'] = $arrDayList;
        $data['mainmenu'] = 'clients';

        return view('frontend.print_attadance.individual-class', $data);
    }

    public function ajaxAction(Request $request)
    {

        $action = $request->input('action');
        $collegeId = Auth::user()->college_id;

        switch ($action) {
            case 'getTimetableSemester':
                $courseTypeId = $request->input('data.courseTypeId');
                $objTimeTable = new Timetable;
                $arrSemesterList = $objTimeTable->getTimetableSemesterList($collegeId, $courseTypeId);
                echo json_encode($arrSemesterList);
                break;
            case 'getTermListBySemester':
                $semesterId = $request->input('data.semesterId');
                $objTimeTable = new Timetable;
                $arrTermList = $objTimeTable->_getTermForReplacement($collegeId, $semesterId);
                echo json_encode($arrTermList);
                break;
            case 'getWeekList':
                $courseTypeId = $request->input('data.courseTypeId');
                $semesterId = $request->input('data.semesterId');
                $termId = $request->input('data.termId');
                $objSemesterDivision = new SemesterDivision;
                $arrWeekList = $objSemesterDivision->getWeekList($collegeId, $courseTypeId, $semesterId, $termId);
                echo json_encode($arrWeekList);
                break;
            case 'getWeeklyAttendanceList':
                $dataArr = $request->input('data');
                $arrWeeklyAttendanceList = $this->generateWeeklyAttendanceList($collegeId, $dataArr);
                echo json_encode($arrWeeklyAttendanceList);
                break;
            case 'getSpecificClassList':
                $courseTypeId = $request->input('data.courseTypeId');
                $semesterId = $request->input('data.semesterId');
                $termId = $request->input('data.termId');
                $day = $request->input('data.day');
                $objTimeTable = new Timetable;
                $arrClassList = $objTimeTable->getSpecificClassList($collegeId, $courseTypeId, $semesterId, $termId, $day);
                echo json_encode($arrClassList);
                break;
            case 'getFilterStudentList':
                $timeTableDetail = Timetable::where('id', $request->input('data.timetableId'))->get()->first();
                $courseTypeId = $request->input('data.courseTypeId');
                $semesterId = $request->input('data.semesterId');
                $termId = $request->input('data.termId');
                $subjectId = $timeTableDetail->subject_id;
                $batch = $timeTableDetail->batch;

                $objSubjectEnrolment = new StudentSubjectEnrolment;
                $arrClassList = $objSubjectEnrolment->getStudentListByIndividualClass($collegeId, $courseTypeId, $semesterId, $termId, $subjectId, $batch);
                echo json_encode($arrClassList);
                break;
        }
        exit;
    }

    public function studentAttendanceList($classId, $subjectId, $semesterId, $termId, Request $request)
    {

        $collegeId = Auth::user()->college_id;

        if ($classId == 'NA' || $subjectId == 'NA' || $semesterId == 'NA' || $termId == 'NA') {
            $request->session()->flash('session_error', 'Please fill require input field');

            return redirect(route('attendance-list-by-class'));
        } else {
            $objSubjectEnrolment = new StudentSubjectEnrolment;
            $arrStudSubjectEnrollList = $data['arrStudSubjectEnrollList'] = $objSubjectEnrolment->getSemsterEnrollStudentAttendance($semesterId, $termId, $subjectId, $classId, $collegeId);

            if (count($arrStudSubjectEnrollList) > 0) {
                return Excel::download(new StudentsClassWithBatchExport(json_encode($data)), 'StudentsClassWithBatchExport.xlsx');
            } else {
                $request->session()->flash('session_error', 'No Student record found');

                return redirect(route('attendance-list-by-class'));
                // return redirect(route('attendance-list-by-class'))->withInput();
            }
        }
    }

    public function findBatchStartDateEndDate($arrClassAttdList)
    {
        $weeksDate = array_merge(array_column($arrClassAttdList, 'start_week'), array_column($arrClassAttdList, 'end_week'));

        // Convert dates to timestamps for sorting
        $timestamps = array_map('strtotime', $weeksDate);

        // Sort the timestamps and maintain the association with original dates
        array_multisort($timestamps, $weeksDate);

        $firstValue = reset($weeksDate);
        $lastValue = end($weeksDate);

        return ['firstdate' => $firstValue, 'lastdate' => $lastValue];
        // dd($weeksDate,$firstValue,$lastValue);
    }

    public function setupWeeklyLogic($arrClassAttdList)
    {

        $finalArray = [];
        for ($i = 0; $i < count($arrClassAttdList); $i++) {
            $finalArray[$arrClassAttdList[$i]['start_week_id']][] = $arrClassAttdList[$i];
        }
        // sort array so first date came first
        ksort($finalArray);

        $findDateRangeAndDay = [];

        // Combain day and startdate and enddate
        foreach ($finalArray as $startWeekIdKey => $startWeekIdValue) {
            for ($j = 0; $j < count($startWeekIdValue); $j++) {
                $findDateRangeAndDay[$startWeekIdKey]['day'][$j] = $finalArray[$startWeekIdKey][$j]['day'];
                $findDateRangeAndDay[$startWeekIdKey]['startdate'] = $finalArray[$startWeekIdKey][$j]['start_week'];
                $findDateRangeAndDay[$startWeekIdKey]['enddate'] = $finalArray[$startWeekIdKey][$j]['end_week'];
                $findDateRangeAndDay[$startWeekIdKey]['start_week_id'] = $finalArray[$startWeekIdKey][$j]['start_week_id'];
                $findDateRangeAndDay[$startWeekIdKey]['end_week_id'] = $finalArray[$startWeekIdKey][$j]['end_week_id'];
            }
        }

        // Calculate date weekwise
        $timeTableDetail = [];
        foreach ($findDateRangeAndDay as $findDateRangeAndDayKey => $findDateRangeAndDayValue) {
            for ($i = $findDateRangeAndDayValue['start_week_id']; $i <= $findDateRangeAndDayValue['end_week_id']; $i++) {

                if ($i == $findDateRangeAndDayValue['start_week_id']) {
                    $startTime = $findDateRangeAndDayValue['startdate']; // Replace this with your given date
                }

                $givenDateTime = new \DateTime($startTime); // Create a DateTime object from the given date
                $givenDateTime->modify('+1 week'); // Add 1 week to the given date
                $endTime = $givenDateTime->format('Y-m-d'); // Print the modified date in the desired format

                $startWeekDate = strtotime($startTime);
                $endWeekDate = strtotime($endTime);
                for ($currentDate = $startWeekDate; $currentDate < $endWeekDate; $currentDate += (86400)) {
                    $dayWiseDate = date('Y-m-d', $currentDate);
                    $day = date('l', $currentDate);
                    if (in_array($day, $findDateRangeAndDayValue['day'])) {
                        $timeTableDetail[$i][] = [
                            'timetable_date' => $dayWiseDate,
                            'day' => $day,
                        ];

                    }
                }
                $startTime = $endTime;
            }
        }

        return $timeTableDetail;
    }

    public function generateWeeklyAttendanceList($collegeId, $dataArr)
    {

        $courseTypeId = $dataArr['courseTypeId'];
        $semesterId = $dataArr['semesterId'];
        $termId = $dataArr['termId'];
        $status = $dataArr['status'];

        $objTimeTable = new Timetable;
        $arrAttendanceList = $objTimeTable->getWeeklyAttendanceList($collegeId, $courseTypeId, $semesterId, $termId, $status);

        return $arrAttendanceList;
    }
}
