<?php

namespace App\Http\Controllers\Spa\Student;

use App;
use App\Http\Controllers\Controller;
use App\Model\Agent;
use App\Model\Colleges;
use App\Model\EmailTemplate;
use App\Model\InvoiceNumber;
use App\Model\PaymentMode;
use App\Model\ResceiptNumber;
use App\Model\StudentCourse;
use App\Model\StudentInitialPayment;
use App\Model\StudentInitialPaymentDetails;
use App\Model\StudentInitialPaymentTransaction;
use App\Model\StudentInvoiceCredit;
use App\Model\StudentMiscellaneousPayment;
use App\Model\Students;
use App\Model\StudentServicePayment;
use App\Model\v2\Student;
use App\Model\v2\StudentCourses;
use App\Model\v2\StudentInitialPaymentDetails as StudentInitialPaymentDetailsV2;
use App\Model\v2\StudentMiscellaneousPayment as StudentMiscellaneousPaymentV2;
use App\Repositories\CommonRepository;
use App\Repositories\StudentPaymentRepository;
use App\Repositories\StudentPaymentRepositoryV2;
use App\Repositories\StudentRepository;
use App\Services\StudentCourseService;
use App\Traits\CommonTrait;
use App\Traits\ResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Inertia\Inertia;

class StudentPaymentInformationController extends Controller
{
    use CommonTrait;
    use ResponseTrait;

    private $studentPaymentRepositoryV2;

    protected $studentRepository;

    protected $studentPaymentRepository;

    protected $commonRepository;

    protected $studentMiscellaneousPayment;

    protected $studentInvoiceCredit;

    protected $studentCourse;

    protected $emailTemplate;

    protected $studentProfileCommonRepository;

    protected $studentInitialPaymentDetails;

    protected $invoiceNumber;

    protected $studentCourseService;

    protected $paymentMode;

    protected $resceiptNumber;

    protected $studentServicePayment;

    public function __construct(

        CommonRepository $commonRepository,
        StudentCourses $studentCourses,
        PaymentMode $paymentMode,
        InvoiceNumber $invoiceNumber,
        ResceiptNumber $resceiptNumber,
        StudentInitialPaymentDetailsV2 $studentInitialPaymentDetails,
        StudentMiscellaneousPayment $studentMiscellaneousPayment,
        StudentInvoiceCredit $studentInvoiceCredit,
        EmailTemplate $emailTemplate,
        StudentServicePayment $studentServicePayment,
        StudentCourseService $studentCourseService,
        StudentPaymentRepositoryV2 $studentPaymentRepositoryV2,

    ) {
        parent::__construct();
        $this->commonRepository = $commonRepository;
        $this->studentRepository = new StudentRepository($studentCourses);
        $this->studentPaymentRepository = new StudentPaymentRepository($studentCourses);
        $this->studentCourse = new StudentPaymentRepository($studentCourses);
        $this->paymentMode = new StudentPaymentRepository($paymentMode);
        $this->invoiceNumber = new StudentPaymentRepository($invoiceNumber);
        $this->resceiptNumber = new StudentPaymentRepository($resceiptNumber);
        $this->studentInitialPaymentDetails = new StudentPaymentRepository($studentInitialPaymentDetails);
        $this->studentMiscellaneousPayment = new StudentPaymentRepository($studentMiscellaneousPayment);
        $this->studentInvoiceCredit = new StudentPaymentRepository($studentInvoiceCredit);
        $this->emailTemplate = new StudentPaymentRepository($emailTemplate);
        $this->studentServicePayment = new StudentPaymentRepository($studentServicePayment);
        $this->studentCourseService = $studentCourseService;
        $this->studentPaymentRepositoryV2 = $studentPaymentRepositoryV2;

    }

    public function StudentPaymentInformation(Request $request)
    {

        $id = $this->loginUser->id;
        $collegeId = $this->loginUser->college_id;
        $personalEmail = $this->loginUser->email;
        // $arrPaidMode = Config::get('constants.arrPaidMode');
        $objPaymentMode = new PaymentMode;
        $arrPaidMode = $objPaymentMode->getPaymentModeData($collegeId);
        $genStudentId = $this->loginUser->username;
        $objRtoapplicationRefID = new Students;
        $primaryId = $objRtoapplicationRefID->getStudentUserId($genStudentId);
        $id = $primaryId[0]['id'];
        $objCollegesMail = new Colleges;
        $arrFromMail = $objCollegesMail->getCollegeMail($collegeId);

        $objRtoEmailTemplate = new EmailTemplate;
        $arrEmailTemplate = $objRtoEmailTemplate->getEmailTemplateType($collegeId);

        $objRtoStudentCourse = new StudentCourse;
        $objStdentAgentName = $objRtoStudentCourse->getStudentAgentName($id);

        $applicationRefID = $objRtoapplicationRefID->getExistsReferenceId($id, $collegeId);
        $objRtoStudentMiscellaneousPayment = new StudentMiscellaneousPayment;
        $courseMiscellaneousFeePaid = $objRtoStudentMiscellaneousPayment->getMiscellaneousPaymentInformationPaid($id, $collegeId);
        $courseMiscellaneousFeeDueUnPaid = $objRtoStudentMiscellaneousPayment->getMiscellaneousPaymentInformationUnPaid($id, $collegeId);

        for ($i = 0; $i < count($courseMiscellaneousFeePaid); $i++) {
            $courseMiscellaneousFeePaid[$i]['paymentModePaid'] = isset($arrPaidMode[$courseMiscellaneousFeePaid[$i]['payment_mode']]) ? $arrPaidMode[$courseMiscellaneousFeePaid[$i]['payment_mode']] : '';
            //            $courseMiscellaneousFeePaid[$i]['paymentModePaid'] = $arrPaidMode[$courseMiscellaneousFeePaid[$i]['payment_mode']];
        }

        $objStdentAgentEmail = [];
        $agentId = '';
        if ($objStdentAgentName->count() > 0) {
            if (! empty($objStdentAgentName[0]->primary_email)) {
                $objStdentAgentEmail[$objStdentAgentName[0]->primary_email] = $objStdentAgentName[0]->primary_email;
            }
            if (! empty($objStdentAgentName[0]->alertnet_email)) {
                $objStdentAgentEmail[$objStdentAgentName[0]->alertnet_email] = $objStdentAgentName[0]->alertnet_email;
            }
            $agentId = $objStdentAgentName[0]->id;
        }

        $data['agent_id'] = $agentId;
        $data['personalEmail'] = $personalEmail;
        $data['arrFromMail'] = $arrFromMail;
        $data['arrEmailTemplate'] = $arrEmailTemplate;
        $data['objStdentAgentEmail'] = $objStdentAgentEmail;
        $data['applicationRefID'] = $applicationRefID[0];

        $objRtoStudentsLists = new Students;
        $objStudentsLists = $objRtoStudentsLists->viewStudentPayment($id, $collegeId);

        $objStudentCourse = new StudentCourse;
        $arrStudentCourse = $objStudentCourse->getStudentAppliedCourseName($id);

        $currStudentCourse = $objRtoStudentCourse->getStudentCourseListWithStatus($id, true);

        $objgetAgentList = new Agent;
        $arrgetAgentList = $objgetAgentList->getAgentList($collegeId);

        $objInvoiceNumber = new InvoiceNumber;
        $arrInvoiceNumber = $objInvoiceNumber->invoceNumberGet($collegeId);

        $arrPaidDuration = Config::get('constants.arrPaidDuration');
        // $arrPaidMode = Config::get('constants.arrPaidMode');
        $objPaymentMode = new PaymentMode;
        $arrPaidMode = $objPaymentMode->getPaymentModeData($collegeId);

        if ($request->isMethod('post')) {
            $postData = $request->input();

            if (isset($postData['editTransaction'])) {
                $objRtoStudentInitialPaymentTransaction = new StudentInitialPaymentTransaction;
                $objRtoStudentInitialPaymentTransaction->updateStudentInitialPaymentTransaction($request->input());

                $request->session()->flash('session_success', 'Transaction Modified Successfully.');
            } else {
                $objStudentDetails = new StudentInitialPaymentDetails;
                $dataValidation = $objStudentDetails->checkSheduleUpdateValidation($postData);
                if ($dataValidation['type'] == 'session_success') {
                    $result = $objStudentDetails->editStudentInitialPaymentDetails($request);
                }

                $request->session()->flash($dataValidation['type'], $dataValidation['message']);
            }

            return redirect(route('student-payment-summary', ['id' => $id]))->withInput();
        }

        $data['pagetitle'] = 'Student Account';
        $data['plugincss'] = [];
        $data['css'] = [];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js', 'ckeditor/ckeditor.js'];
        $data['js'] = ['studentInformation.js'];
        $data['funinit'] = ['studentInformation.initStudentPayment();'];
        $data['activateValue'] = 'Accounts';
        $data['header'] = [
            'title' => 'Students Payment Summary',
            'breadcrumb' => [
                'Home' => route('student_dashboard'),
                'student' => '',
                'Payment Summary' => '',
            ],
        ];
        // $data['objStudentsLists'] = $objStudentsLists;
        // $data['arrStudentCourse'] = $arrStudentCourse;
        $data['student_id'] = $id;
        $data['arrgetAgentList'] = $arrgetAgentList;
        $data['arrPaidDuration'] = $arrPaidDuration;
        $data['arrInvoiceNumber'] = $arrInvoiceNumber;
        $data['arrPaidMode'] = $arrPaidMode;
        $data['courseMiscellaneousFeePaid'] = $courseMiscellaneousFeePaid;
        $data['courseMiscellaneousFeeDueUnPaid'] = $courseMiscellaneousFeeDueUnPaid;
        $data['mainmenu'] = 'payment';
        $data['actionBtns'] = $this->getActionButtons();
        // return view('student.student_payment_information.student-payment-information', $data);

        return Inertia::render('studentportal/payments/ViewPayment', [
            'data' => $data,
            'studentId' => $id,
            'collegeId' => $collegeId,
            'courseId' => array_key_first($currStudentCourse), // TODO: Not getting the correct course id
        ]);
    }

    private function getActionButtons()
    {
        $actionBtns = [];
        $btn = null;
        $btn['action'] = route('spa.agent.student.profile', ['id' => 1]);
        $actionBtns[] = $btn;

        return $actionBtns;
    }

    public function ajaxAction(Request $request)
    {
        $action = $request->input('action');
        switch ($action) {

            case 'studentPayment':
                $student_id = $request->input('data.student_id');
                $course_id = $request->input('data.course_id');
                $this->_getStudentPayment($course_id, $student_id);
                break;
        }
    }

    public function _getStudentPayment($course_id, $student_id)
    {

        $collegeId = $this->loginUser->college_id;
        // $arrPaidMode = Config::get('constants.arrPaidMode');
        $objPaymentMode = new PaymentMode;
        $arrPaidMode = $objPaymentMode->getPaymentModeData($collegeId);

        $receiptNoMod = [];
        $objRtoStudentsLists = new StudentCourse;
        $objStudentsLists = $objRtoStudentsLists->_getAccountPayment($course_id, $student_id);

        $objStudentDetailsGet = new StudentInitialPaymentDetails;
        $arrStudentDetailsGet = $objStudentDetailsGet->studentPaymentDetailsGet($collegeId, $course_id, $student_id);
        //        print_r($arrStudentDetailsGet);
        // print_r($arrPaidMode);exit;
        $objGetLastInvoiceDueDate = $objStudentDetailsGet->_getLastInvoiceDate($collegeId, $course_id, $student_id);

        $objStudentInitialPayment = new StudentInitialPayment;
        $arrStudentInitialPayment = $objStudentInitialPayment->getStudentInitialPaymentCourse($student_id, $course_id, $collegeId);

        // get payment transaction details from (rto_student_initial_payment_transaction)
        $objRtoStudentInitialPaymentTransaction = new StudentInitialPaymentTransaction;
        $objInitialPaymentTransaction = $objRtoStudentInitialPaymentTransaction->_getInitialPaymentTransaction($collegeId, $student_id, $course_id);

        for ($i = 0; $i < count($objInitialPaymentTransaction); $i++) {
            $receiptNoMod[$objInitialPaymentTransaction[$i]['receipt_no']] = $objInitialPaymentTransaction[$i]['receipt_no'];
        }
        // get Course Miscellaneous Fee Due
        $objRtoStudentMiscellaneousPayment = new StudentMiscellaneousPayment;
        $courseMiscellaneousFeeDue = $objRtoStudentMiscellaneousPayment->_getCourseMiscellaneousFeeDue($collegeId, $student_id, $course_id);

        // get Total Miscellaneous Fee Due
        $totalMiscellaneousFeeDue = $objRtoStudentMiscellaneousPayment->_getTotalMiscellaneousFeeDue($collegeId, $student_id);

        // get Course Service Fee Due
        $objRtoStudentServicePayment = new StudentServicePayment;
        $courseServiceFeePaid = $objRtoStudentMiscellaneousPayment->_getCourseServiceFeePaid($collegeId, $student_id, $course_id, 'Oshc');

        // get Total Service Fee Due
        $totalServiceFeeDue = $objRtoStudentServicePayment->_getTotalServiceFeeDue($collegeId, $student_id);

        // echo '<pre>';print_r($totalMiscellaneousFeeDue);exit;
        // print_r($arrStudentDetailsGet);
        //  print_r($arrStudentInitialPayment);
        //        exit;
        $InvoicedDueAmount = 0;
        $totalCoursePaidAmmount = 0;
        $arrPaidDuration = Config::get('constants.arrPaidDuration');
        for ($j = 0; $j < count($arrStudentDetailsGet); $j++) {
            //            $arrStudentDetailsGet[$j]['duration_type'] = $arrPaidDuration[$arrStudentDetailsGet[$j]['paid_duration_day']];
            //            $arrStudentDetailsGet[$j]['payment_mode_value'] = $arrPaidMode[$arrStudentDetailsGet[$j]['payment_mode']];
            $arrStudentDetailsGet[$j]['duration_type'] = isset($arrPaidDuration[$arrStudentDetailsGet[$j]['paid_duration_day']]) ? $arrPaidDuration[$arrStudentDetailsGet[$j]['paid_duration_day']] : '';
            $arrStudentDetailsGet[$j]['payment_mode_value'] = isset($arrPaidMode[$arrStudentDetailsGet[$j]['payment_mode']]) ? $arrPaidMode[$arrStudentDetailsGet[$j]['payment_mode']] : '';
            $InvoicedDueAmount = $InvoicedDueAmount + $arrStudentDetailsGet[$j]['upfront_fee_to_pay'];
            $totalCoursePaidAmmount += $arrStudentDetailsGet[$j]['upfront_fee_pay'];
        }

        for ($i = 0; $i < count($objInitialPaymentTransaction); $i++) {
            $objInitialPaymentTransaction[$i]['payment_mode_value'] = isset($arrPaidMode[$objInitialPaymentTransaction[$i]['payment_mode']]) ? $arrPaidMode[$objInitialPaymentTransaction[$i]['payment_mode']] : '';
            $objInitialPaymentTransaction[$i]['bank_deposit_date'] = ($objInitialPaymentTransaction[$i]['bank_deposit_date'] == null) ? '-' : $objInitialPaymentTransaction[$i]['bank_deposit_date'];
            $objInitialPaymentTransaction[$i]['reference_no'] = ($objInitialPaymentTransaction[$i]['reference_no'] == null) ? '-' : $objInitialPaymentTransaction[$i]['reference_no'];
        }

        $dataArray = [];
        $dataArray['studentDetail'] = $objStudentsLists[0];
        $dataArray['studentPaymentDetail']['totalCourseFeeBalanceDue'] = $objStudentsLists[0]['course_fee'] - $totalCoursePaidAmmount;
        $dataArray['studentPaymentDetail']['invoicedDueAmount'] = $InvoicedDueAmount - $totalCoursePaidAmmount;
        $dataArray['studentPaymentDetail']['courseMiscellaneousFeeDue'] = $courseMiscellaneousFeeDue;
        $dataArray['studentPaymentDetail']['totalMiscellaneousFeeDue'] = $totalMiscellaneousFeeDue;
        $dataArray['studentPaymentDetail']['courseServiceFeePaid'] = $courseServiceFeePaid;
        $dataArray['studentPaymentDetail']['totalServiceFeeDue'] = $totalServiceFeeDue;
        $dataArray['studentPaymentDetail']['totalFeePaid'] = $totalCoursePaidAmmount;
        $dataArray['paymentDetail'] = $arrStudentDetailsGet;

        $dataArray['lastInvoiceDueDate'] = $objGetLastInvoiceDueDate;

        $dataArray['initialPaymentTransaction'] = $objInitialPaymentTransaction;
        $dataArray['receiptNoMod'] = $receiptNoMod;

        if ($arrStudentInitialPayment != '' && ! empty($arrStudentInitialPayment)) {
            $dataArray['arrStudentInitialPayment'] = $arrStudentInitialPayment[0];
        }
        if (count($arrStudentInitialPayment) == 0) {
            $dataArray['arrStudentInitialPayment'] = '';
        }

        return $dataArray;
        // echo json_encode($dataArray);
    }

    public function generateStudentScheduleInvoicePdf(Request $request, $isSave = false)
    {
        $collegeId = $request->college_id;
        $studentId = $request->student_id;
        $studentCourseId = $request->student_course_id;
        $arrStudentInfo = $this->studentRepository->getStudentDetail($studentId);
        $courseId = $this->studentCourse->find($studentCourseId)->course_id;
        $invoiceNo = (isset($request->invoice_number)) ? $request->invoice_number : $this->studentInitialPaymentDetails->findWithTrashed($request->id)->invoice_number;
        $arrStudentCourse = $this->studentPaymentRepository->getStudentPdfCourseDataV2($courseId, $studentId, $studentCourseId);

        $data = [
            'arrStudentInfo' => $arrStudentInfo,
            'collegeLogo' => $this->commonRepository->getCollegeLogo($arrStudentInfo->college_logo),
            'collegeData' => $this->commonRepository->getCollegeDetails($collegeId),
            'arrStudentPaymentDetail' => $this->studentPaymentRepository->studentPaymentDetailsGetV2($collegeId, $studentId, $studentCourseId, $invoiceNo),
            'arrMiscellaneousPayment' => $this->studentPaymentRepository->getMiscellaneousPayment($collegeId, $invoiceNo),
            'arrStudentServicePayment' => $this->studentServicePayment->getWhere(['college_id' => $collegeId, 'invoice_number' => $invoiceNo]),
            'arrStudentCourse' => ($arrStudentCourse->count() > 0) ? $arrStudentCourse[0] : [],
            'fileName' => "Schedule_Invoice_$invoiceNo.pdf",
            'bladeFilePath' => 'v2.sadmin.student.pages.student-schedule-invoice-pdf',
        ];
        if ($isSave) {
            return $this->generateAndSavePdf($data, $studentId, $invoiceNo, $collegeId);
        }

        return $this->generateAndDownloadPdf($data);
    }

    public function generateStudentTaxReceiptPdf(Request $request, $isSave = false)
    {
        $collegeId = Auth::user()->college_id;

        $primaryId = $request->id;
        if ($primaryId) {
            $resData = $this->studentPaymentRepository->getStudentAllPaymentTransactionDetails($primaryId);
            if (count($resData) > 0) {
                $studentId = $resData[0]['student_id'];
                $courseId = $resData[0]['course_id'];
                $studentCourseId = $resData[0]['student_course_id'];
                $receiptNo = $resData[0]['receipt_no'];
                $invoiceNo = $resData[0]['invoice_number'];
                $arrStudentInfo = $this->studentRepository->getStudentDetail($studentId);
                $collegeDetail = $this->commonRepository->getCollegeDetails($collegeId);
                $arrStudentCourse = $this->studentPaymentRepository->getStudentPdfCourseDataV2($courseId, $studentId, $studentCourseId);
                $data = [
                    'arrStudentInfo' => $arrStudentInfo,
                    'transactionData' => $resData,
                    'collegeLogo' => $this->commonRepository->getCollegeLogo($arrStudentInfo->college_logo),
                    'college_signature' => $this->commonRepository->getCollegeLogo($collegeDetail->college_signature),
                    'objCollegeDetails' => $collegeDetail,
                    'arrState' => Config::get('constants.arrState'),
                    'arrMiscellaneousPayment' => StudentMiscellaneousPayment::where('resceipt_number', $receiptNo)->get(),
                    'arrStudentCourse' => ($arrStudentCourse->count() > 0) ? $arrStudentCourse[0] : [],
                    'fileName' => 'student-receipt-'.$arrStudentInfo->generated_stud_id.'-'.$receiptNo.'.pdf',
                    'bladeFilePath' => 'v2.sadmin.student.pages.student-all-tax-receipt-pdf',
                ];

                return $this->generateAndDownloadPdf($data);
            }
        }
    }

    public function miscellaneousPaymentInvoicePdf(Request $request)
    {
        $primaryId = $request->id;
        if ($primaryId) {
            $miscData = StudentMiscellaneousPaymentV2::withTrashed()->find($primaryId);
            if ($miscData) {
                $collegeId = $miscData->college_id;
                $invoiceNumber = $miscData->invoice_number;
                $arrStudentInfo = $this->studentRepository->getStudentDetail($miscData->student_id);

                $data = [
                    'collegeLogo' => $this->commonRepository->getCollegeLogo($arrStudentInfo->college_logo),
                    'arrState' => Config::get('constants.arrState'),
                    'arrStudentInfo' => $arrStudentInfo,
                    'objCollegeDetails' => $this->commonRepository->getCollegeDetails($collegeId),
                    'arrMiscellaneousPayment' => $miscData,
                    // 'arrStudentServicePayment'  => $this->studentPaymentRepositoryV2->getStudentServicePayment($invoiceNumber),                   //TODO::GNG-2852
                    // 'arrStudentInitialPayment'  => $this->studentPaymentRepositoryV2->getStudentInitialPaymentDetail($collegeId, $invoiceNumber), //TODO::GNG-2852
                    'fileName' => 'student-miscellaneous-payment-invoice.pdf',
                    'bladeFilePath' => 'v2.sadmin.student.pages.student-miscellaneous-payment-invoice-pdf',
                ];

                return $this->generateAndDownloadPdf($data);
            }
        }
    }

    public function miscellaneousPaymentReceiptPdf(Request $request)
    {
        $primaryId = $request->id;
        if ($primaryId) {
            $miscData = StudentMiscellaneousPaymentV2::find($primaryId);
            if ($miscData) {
                $collegeId = $miscData->college_id;
                $invoiceNumber = $miscData->invoice_number;
                $arrStudentInfo = $this->studentRepository->getStudentDetail($miscData->student_id);

                $data = [
                    'collegeLogo' => $this->commonRepository->getCollegeLogo($arrStudentInfo->college_logo),
                    'arrState' => Config::get('constants.arrState'),
                    'arrPaidMode' => $this->studentPaymentRepositoryV2->getPaymentModeData($collegeId),
                    'arrStudentInfo' => $arrStudentInfo,
                    'objCollegeDetails' => $this->commonRepository->getCollegeDetails($collegeId),
                    'arrMiscellaneousPayment' => $miscData,
                    // 'arrStudentServicePayment'      => $this->studentPaymentRepositoryV2->getStudentServicePayment($invoiceNumber),                       //TODO::GNG-2852
                    // 'arrStudentInitialPayment'      => $this->studentPaymentRepositoryV2->getStudentInitialPaymentDetail($collegeId, $invoiceNumber),     //TODO::GNG-2852
                    'fileName' => 'student-miscellaneous-payment-receipt.pdf',
                    'bladeFilePath' => 'v2.sadmin.student.pages.student-miscellaneous-payment-receipt-pdf',
                ];

                return $this->generateAndDownloadPdf($data);
            }
        }
    }

    public function generateReceiptAdditionalPaymentServicePdf(Request $request, $isSave = false)
    {
        $primaryId = $request->id;
        if ($primaryId) {
            $resData = $this->studentPaymentRepository->getStudentServicePaymentData($primaryId);
            if (count($resData) > 0 && isset($resData[0]['student_course_id'])) {
                $collegeId = $resData[0]['college_id'];
                $studentId = $resData[0]['student_id'];
                $courseId = $resData[0]['course_id'];
                $invoiceNo = $resData[0]['formatted_invoice_number'];
                $studentCourseId = $resData[0]['student_course_id'];

                $arrStudentInfo = $this->studentRepository->getStudentDetail($studentId);
                $arrStudentCourse = $this->studentPaymentRepository->getStudentPdfCourseDataV2($courseId, $studentId, $studentCourseId);
                $string = $arrStudentInfo->generated_stud_id.'_'.$arrStudentInfo->application_reference_id;
                $data = [
                    'invoiceNo' => $invoiceNo,
                    'arrStudentInfo' => $arrStudentInfo,
                    'objCollegeDetails' => $this->commonRepository->getCollegeDetails($collegeId),
                    'collegeLogo' => $this->commonRepository->getCollegeLogo($arrStudentInfo['college_logo']),
                    'arrState' => Config::get('constants.arrState'),
                    'arrStudentCourse' => $arrStudentCourse[0],
                    'arrPaidMode' => $this->paymentMode->getAll(['id as Id', 'name as Name'], 'name'),
                    'arrStudentDetailsGet' => $resData,
                    'fileName' => "serviceReceipt_$string.pdf",
                    'bladeFilePath' => 'v2.sadmin.student.pages.student-service-receipt-pdf',
                ];
                if ($isSave) {
                    return $this->generateAndSavePdf($data, $studentId, $invoiceNo, $collegeId);
                }

                return $this->generateAndDownloadPdf($data);
            }
        }
    }

    public function generateInvoicesAdditionalPaymentServicePdf(Request $request)
    {
        $primaryId = $request->id;
        if ($primaryId) {
            $resData = $this->studentPaymentRepository->getStudentServicePaymentData($primaryId);
            if (count($resData) > 0 && isset($resData[0]['student_course_id'])) {
                $collegeId = $resData[0]['college_id'];
                $studentId = $resData[0]['student_id'];
                $courseId = $resData[0]['course_id'];
                $invoiceNo = $resData[0]['formatted_invoice_number'];
                $studentCourseId = $resData[0]['student_course_id'];

                $arrStudentInfo = $this->studentRepository->getStudentDetail($studentId);
                $arrStudentCourse = $this->studentPaymentRepository->getStudentPdfCourseDataV2($courseId, $studentId, $studentCourseId);
                $string = $arrStudentInfo->generated_stud_id.'_'.$arrStudentInfo->application_reference_id;
                $data = [
                    'invoiceNo' => $invoiceNo,
                    'arrStudentInfo' => $arrStudentInfo,
                    'objCollegeDetails' => $this->commonRepository->getCollegeDetails($collegeId),
                    'collegeLogo' => $this->commonRepository->getCollegeLogo($arrStudentInfo->college_logo),
                    'arrState' => Config::get('constants.arrState'),
                    'arrStudentCourse' => $arrStudentCourse[0],
                    'arrPaidMode' => $this->paymentMode->getAll(['id as Id', 'name as Name'], 'name'),
                    'arrStudentDetailsGet' => $this->studentPaymentRepository->studentPaymentDetailsForStudentServicePaymentGet($collegeId, $courseId, $studentId, ''),
                    'arrStudentServicePayment' => $resData,
                    'fileName' => "ServiceInvoice_$string.pdf",
                    'bladeFilePath' => 'v2.sadmin.student.pages.student-service-invoice-pdf',
                ];

                return $this->generateAndDownloadPdf($data);
            }
        }
    }

    private function generateAndDownloadPdf($data)
    {
        $pdf = App::make('dompdf.wrapper');
        $pdf->loadView($data['bladeFilePath'], $data);

        return $pdf->download($data['fileName']);
    }
}
