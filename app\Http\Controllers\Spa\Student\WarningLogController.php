<?php

namespace App\Http\Controllers\Spa\Student;

use App\Helpers\Helpers;
use App\Http\Controllers\Controller;
use App\Model\v2\StudentLetter;
use App\Repositories\StudentProfileCommonRepository;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Config;
use Inertia\Inertia;

class WarningLogController extends Controller
{
    protected $studentProfileCommonRepository;

    public function __construct(
        StudentProfileCommonRepository $studentProfileCommonRepository
    ) {
        $this->studentProfileCommonRepository = $studentProfileCommonRepository;
        parent::__construct();
    }

    public function warningLog(Request $request)
    {

        $collegeId = $this->loginUser->college_id;
        $userId = $this->loginUser->id;
        $username = $this->loginUser->username;

        $search = trim($request->input('search'));
        $take = (int) $request->input('take');
        $take = empty($take) ? 10 : $take;

        $objStudentLetter = new StudentLetter;
        $arrStudentLetter = $objStudentLetter->getStudentLetterV2($collegeId, $userId, $username, $search, $take);
        foreach ($arrStudentLetter as $key => $res) {
            $arrStudentLetter[$key]['detail'] = $this->getStudentMailContent($res['student_id'], $res['course_id'], $res['detail'], $res['category']);
        }

        $data['mainmenu'] = 'log';
        $warningLogData = null;
        if ($request->input('dataId')) {
            $warningLogData = $this->_getWarningLogData($request->input('dataId'));
        }

        return Inertia::render('studentportal/warning-logs/WarningLogs', [
            'data' => $data,
            'warningLogData' => $arrStudentLetter,
        ]);
    }

    public function _getWarningLogData($dataId)
    {
        $objStudentLetter = new StudentLetter;
        $arrStudentLetter = $objStudentLetter->getStudentLetter($dataId);
        echo json_encode($arrStudentLetter);
    }

    private function getStudentMailContent($studentId, $courseId, $content, $subject = '', $emailType = false)
    {
        $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
        $destinationPath = Helpers::changeRootPath($filePath);

        $arrStudentCourse = $this->studentProfileCommonRepository->getStudentEmailContent($studentId);

        $arrStudentEnrolledCourse = $this->studentProfileCommonRepository->getArrayStudentEnrolledCourseName($studentId);
        $arrStudentOfferedCourse = $this->studentProfileCommonRepository->getArrayStudentOfferedCourseName($studentId);
        if (! empty($arrStudentCourse)) {
            $row = $arrStudentCourse[0];
            $domain = url('/');
            $basePath = $destinationPath['view'];

            $college_logo_url = $domain.str_replace('\\', '/', $basePath).$row['college_logo'];
            $college_signature_url = $domain.str_replace('\\', '/', $basePath).$row['college_signature'];
            $dean_signature_url = $domain.str_replace('\\', '/', $basePath).$row['dean_signature'];

            $college_logo = '<img src="'.$college_logo_url.'" alt="College Logo" style="height: auto; width: 150px; padding-left: 49px; padding-top: 20px;"  />';
            $college_signature = '<img src="'.$college_signature_url.'" alt="College Signature" style="height: auto; width: 150px; padding-left: 49px; padding-top: 20px;"  />';
            $dean_signature = '<img src="'.$dean_signature_url.'" alt="Dean/CEO Signature" style="height: auto; width: 150px; padding-left: 49px; padding-top: 20px;"  />';

            $enrolledCourseList = '';
            if (! empty($arrStudentEnrolledCourse)) {
                $enrolledCourseList = '<ul>';
                foreach ($arrStudentEnrolledCourse as $value) {
                    $enrolledCourseList .= '<li>'.$value['course_code'].' : '.$value['course_name'].' ('.date('d-m-Y', strtotime($value['start_date'])).' - '.date('d-m-Y', strtotime($value['finish_date'])).')</li>';
                }
                $enrolledCourseList .= '</ul>';
            }
            $offeredCourseList = '';
            if (! empty($arrStudentOfferedCourse)) {
                $offeredCourseList = '<ul>';
                foreach ($arrStudentOfferedCourse as $value) {
                    $offeredCourseList .= '<li>'.$value['course_code'].' : '.$value['course_name'].' ('.date('d-m-Y', strtotime($value['start_date'])).' - '.date('d-m-Y', strtotime($value['finish_date'])).')</li>';
                }
                $offeredCourseList .= '</ul>';
            }

            $imgArr = [
                'college_logo' => $college_logo,
                'college_signature' => $college_signature,
                'dean_signature' => $dean_signature,
            ];

            $dataArr = $this->dataBindForStudent($row, $enrolledCourseList, $offeredCourseList, $imgArr);
            foreach ($dataArr as $key => $value) {
                $content = str_replace("$key", $value, $content);
                $subject = str_replace("$key", $value, $subject);
            }

            return [$subject, $content];
        } else {
            return false;
        }
        // return $student;
    }

    private function dataBindForStudent($row, $enrolledCourseList, $offeredCourseList, $imgArr)
    {
        return [

            '{AlterEmail1}' => $row['emergency_email'],
            '{AlterEmail2}' => $row['emergency_email'],
            '{CollegeLogo}' => $imgArr['college_logo'],
            '{CollegeEmail}' => $row['college_email'],
            '{Country}' => $row['country_name'],
            '{CountryBirth}' => $row['birth_country'],
            // "{CurrentDate}"         => date('d-m-Y'),
            '{CurrentDate}' => $this->getCurrentDateTimeWithTimeZone($row['college_timezone'], 'd-m-Y'), // TODO::GN-2333
            '{DoB}' => date('d-m-Y', strtotime($row['birth_date'])),
            '{DOB}' => date('d-m-Y', strtotime($row['birth_date'])),
            '{DoB Without Stroke}' => '******************',
            '{Email}' => $row['student_email'],
            '{ExpDate}' => date('d-m-Y', strtotime($row['visa_expiry_date'])),
            '{Fax}' => $row['fax'],
            '{Student ID}' => $row['generated_stud_id'],
            '{Student Name}' => $row['first_name'].' '.$row['family_name'],
            '{StudentId}' => $row['generated_stud_id'],
            '{FirstName}' => $row['first_name'],
            '{MiddleName}' => $row['middel_name'],
            '{LastName}' => $row['family_name'],
            '{Gender}' => $row['gender'],
            '{Mobile}' => $row['current_mobile_phone'],
            '{Nationality}' => $row['nationality'],
            '{NickName}' => $row['nickname'],
            '{PassportNo}' => $row['passport_no'],
            '{Phone}' => $row['current_mobile_phone'],
            '{Postcode}' => $row['current_postcode'],
            '{State}' => $row['current_state'],
            '{StreetAddress}' => $row['current_street_name'],
            '{StreetNumber}' => $row['current_street_no'],
            '{UnitDetail}' => $row['current_unit_detail'],
            '{BuildingName}' => $row['current_building_name'],
            '{Suburb}' => $row['current_city'],
            '{Title}' => $row['name_title'],
            '{UserName}' => $row['generated_stud_id'],
            '{VisaType}' => $row['visa_type'],
            '{CourseCode}' => $row['course_code'] ?? '',
            '{CourseName}' => $row['course_name'] ?? '',
            '{CollegeRtoCode}' => $row['RTO_code'],
            '{CollegeCircosCode}' => $row['CRICOS_code'],
            '{CollegeLegalName}' => $row['legal_name'],
            '{CollegeName}' => $row['entity_name'],
            '{CollegeSignature}' => $imgArr['college_signature'],
            '{DeanName}' => $row['dean_name'],
            '{DeanSignature}' => $imgArr['dean_signature'],
            '{CollegeContactPerson}' => $row['contact_person'],
            '{CollegeContactPhone}' => $row['contact_phone'],
            '{CollegeURL}' => $row['college_url'],
            '{CollegeABN}' => $row['college_ABN'],
            '{CollegeFax}' => $row['fax'],
            '{CourseType}' => $row['course_type'] ?? '',
            '{Campus}' => $row['campus_name'] ?? '',
            '{StudentType}' => $row['student_type'],
            '{TeacherFirstName}' => $row['teacher_first_name'],
            '{TeacherLastName}' => $row['teacher_last_name'],
            '{TeacherEmail}' => $row['teacher_email'],
            '{TeacherMobile}' => $row['teacher_mobile'],
            '{AgencyName}' => $row['agency_name'] ?? '',
            '{AgentName}' => $row['agent_name'] ?? '',
            '{AgentEmail}' => $row['agent_email'] ?? '',
            '{AgentTelephone}' => $row['agent_telephone'] ?? '',
            '{EnrolledCourseList}' => $enrolledCourseList,
            '{OfferedCourseList}' => $offeredCourseList,
            '{CourseStartDate}' => (isset($row['start_date'])) ? date('d-m-Y', strtotime($row['start_date'])) : '',
            '{CourseEndDate}' => (isset($row['finish_date'])) ? date('d-m-Y', strtotime($row['finish_date'])) : '',
            '{CourseDuration}' => (isset($row['total_weeks'])) ? $row['total_weeks'].' Weeks' : '',
            '{StudentContactEmail}' => $row['personalEmail'],
            '{StudentAlternateEmail}' => $row['AlternateEmail'],
            '{StudentEmergencyEmail}' => $row['emergency_email'],
        ];
    }

    public function getCurrentDateTimeWithTimeZone($timezone = 'UTC', $format = 'Y-m-d H:i:s')
    {
        $currentDateTime = Carbon::now($timezone);

        return $currentDateTime->format($format);
    }
}
