<?php

namespace App\Http\Controllers\Student;

use App\Http\Controllers\Controller;
use App\Model\StudentLetter;
use Config;
use Illuminate\Http\Request;

class WarningLogController extends Controller
{
    public function __construct()
    {
        // $this->rememberToken
        parent::__construct();
    }

    public function warningLog(Request $request)
    {

        $collegeId = $this->loginUser->college_id;
        $userId = $this->loginUser->id;
        $username = $this->loginUser->username;

        $objStudentLetter = new StudentLetter;
        $arrStudentLetter = $objStudentLetter->getStudentLetterV2($collegeId, $userId, $username);

        $data['pagetitle'] = 'Email Feedback';
        $data['plugincss'] = [];
        $data['css'] = [];
        $data['pluginjs'] = ['jQuery/jquery.validate.min.js', 'ckeditor/ckeditor.js'];
        $data['js'] = ['warningLog.js'];
        $data['funinit'] = ['warningLog.initWarningLog()'];
        $data['header'] = [
            'title' => 'Student Warning Letter Log',
            'breadcrumb' => [
                'Home' => route('student_dashboard'),
                'Warning Letter Log' => '',
            ]];
        $data['arrStudentLetter'] = $arrStudentLetter;
        $data['mainmenu'] = 'log';

        return view('student.warning_log.warning-log', $data);
    }

    public function ajaxAction(Request $request)
    {
        $action = $request->input('action');
        switch ($action) {

            case 'getWarningLogData':
                $dataId = $request->input('data.dataId');
                $this->_getWarningLogData($dataId);
                break;
        }
    }

    public function _getWarningLogData($dataId)
    {
        $objStudentLetter = new StudentLetter;
        $arrStudentLetter = $objStudentLetter->getStudentLetterAjax($dataId);
        echo json_encode($arrStudentLetter);
    }

    private function getStudentMailContent($studentId, $courseId, $content, $subject, $emailType)
    {
        $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
        $destinationPath = Helpers::changeRootPath($filePath);
        if ($emailType == $this->getCourseType()) {
            $arrStudentCourse = $this->studentProfileCommonRepository->getStudentCoursesEmailContent($studentId, $courseId);
        } else {
            $arrStudentCourse = $this->studentProfileCommonRepository->getStudentEmailContent($studentId);
        }

        $arrStudentEnrolledCourse = $this->studentProfileCommonRepository->getArrayStudentEnrolledCourseName($studentId);
        $arrStudentOfferedCourse = $this->studentProfileCommonRepository->getArrayStudentOfferedCourseName($studentId);
        if (! empty($arrStudentCourse)) {
            $row = $arrStudentCourse[0];
            $domain = url('/');
            $basePath = $destinationPath['view'];
            $college_logo_url = $domain.str_replace('\\', '/', $basePath).$row['college_logo'];
            $college_logo = '<img src="'.$college_logo_url.'" alt="College Logo" style="height: auto; width: 150px;padding-left: 49px; padding-top: 20px;"  />';
            $enrolledCourseList = '';
            if (! empty($arrStudentEnrolledCourse)) {
                $enrolledCourseList = '<ul>';
                foreach ($arrStudentEnrolledCourse as $value) {
                    $enrolledCourseList .= '<li>'.$value['course_code'].' : '.$value['course_name'].' ('.date('d-m-Y', strtotime($value['start_date'])).' - '.date('d-m-Y', strtotime($value['finish_date'])).')</li>';
                }
                $enrolledCourseList .= '</ul>';
            }
            $offeredCourseList = '';
            if (! empty($arrStudentOfferedCourse)) {
                $offeredCourseList = '<ul>';
                foreach ($arrStudentOfferedCourse as $value) {
                    $offeredCourseList .= '<li>'.$value['course_code'].' : '.$value['course_name'].' ('.date('d-m-Y', strtotime($value['start_date'])).' - '.date('d-m-Y', strtotime($value['finish_date'])).')</li>';
                }
                $offeredCourseList .= '</ul>';
            }
            $dataArr = $this->dataBindForStudent($row, $enrolledCourseList, $offeredCourseList, $college_logo);
            foreach ($dataArr as $key => $value) {
                $content = str_replace("$key", $value, $content);
                $subject = str_replace("$key", $value, $subject);
            }

            return [$subject, $content];
        } else {
            return false;
        }
        // return $student;
    }
}
