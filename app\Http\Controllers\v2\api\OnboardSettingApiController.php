<?php

namespace App\Http\Controllers\v2\api;

use App\Http\Controllers\Controller;
use App\Http\Requests\FormValidation\SaveCoursesIntakeDates;
use App\Model\v2\ContractCode;
use App\Model\v2\ContractFundingSource;
use App\Model\v2\CourseSite;
use App\Model\v2\Report;
use App\Services\OnboardSettingService;
use App\Traits\ResponseTrait;
use Config;
use Domains\Customers\Settings\Models\SettingTracker;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Hash;

class OnboardSettingApiController extends Controller
{
    use ResponseTrait;

    protected $onboardSettingService;

    public function __construct(OnboardSettingService $onboardSettingService)
    {
        $this->onboardSettingService = $onboardSettingService;
    }

    public function getContractCodeData(Request $request)
    {
        $data = $this->onboardSettingService->getContractCodeData($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getContractFundingSourceData(Request $request)
    {
        $data = $this->onboardSettingService->getContractFundingSourceData($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getCourseSiteData(Request $request)
    {
        $data = $this->onboardSettingService->getCourseSiteData($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function failedEmailData(Request $request)
    {
        $data = $this->onboardSettingService->failedEmailData($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function failedEmailDetail(Request $request)
    {
        $data = $this->onboardSettingService->failedEmailDetail($request->input());

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function manageReportsData(Request $request)
    {
        $data = $this->onboardSettingService->manageReportsData($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function bankInfoData(Request $request)
    {
        $data = $this->onboardSettingService->bankInfoData($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function studentReportLetterData(Request $request)
    {
        $data = $this->onboardSettingService->studentReportLetterData($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getElearningLinkData(Request $request)
    {
        $data = $this->onboardSettingService->getElearningLinkData($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function publicHolidayData(Request $request)
    {
        $data = $this->onboardSettingService->publicHolidayData($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function courseTemplateData(Request $request)
    {
        $data = $this->onboardSettingService->courseTemplateData($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function uploadedEmailTemplateDocumentsData(Request $request)
    {
        $data = $this->onboardSettingService->uploadedEmailTemplateDocumentsData($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function uploadedLetterTemplateDocumentsData(Request $request)
    {
        $data = $this->onboardSettingService->uploadedLetterTemplateDocumentsData($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function uploadEmailTemplateDocument(Request $request)
    {
        $data = $this->onboardSettingService->uploadEmailTemplateDocument($request);
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function deleteEmailTemplateDocument(Request $request)
    {
        $data = $this->onboardSettingService->deleteEmailTemplateDocumentData($request->input());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function deleteLetterTemplateDocument(Request $request)
    {
        $data = $this->onboardSettingService->deleteLetterTemplateDocumentData($request->input());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function uploadLetterTemplateDocument(Request $request)
    {
        $data = $this->onboardSettingService->uploadLetterTemplateDocument($request);
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function uploadUserProfilePicture(Request $request)
    {
        $metaDataArr = json_decode($request->input('metadata'), true);
        $originalFileName = $metaDataArr['fileName'];
        $profilePicture = $request->file('file');
        $userId = $request->user_id;
        $data = $this->onboardSettingService->uploadUserProfilePicture($userId, $profilePicture, $originalFileName);
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function saveContractCode(Request $request)
    {
        $validatedData = $request->validate([
            'contract_code' => 'required',
            'description' => 'required',
        ]);
        $data = $this->onboardSettingService->saveContractCode($request->input());
        SettingTracker::Completed($request['tracking_form']);
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function getContractCodeDetails(Request $request)
    {
        $data = ContractCode::find($request->id);

        return $this->successResponse('Data found', 'data', $data);

    }

    public function updateContractCode(Request $request)
    {

        $data = $this->onboardSettingService->updateContractCodeData($request->input());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function saveContractFundingSource(Request $request)
    {
        $validatedData = $request->validate([
            'contract_code_id' => 'required',
            'state' => 'required',
            'funding_source' => 'required',
            'schedual_code' => 'required',
            'description' => 'required',
        ]);
        $data = $this->onboardSettingService->saveContractFundingSource($request->input());
        SettingTracker::Completed($request['tracking_form']);
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function getContractFundingSourceDetails(Request $request)
    {
        $data = ContractFundingSource::find($request->id);

        return $this->successResponse('data found', 'data', $data);

    }

    public function updateContractFundingSource(Request $request)
    {
        $data = $this->onboardSettingService->updateContractFundingSourceData($request->input());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }

    }

    public function saveCourseSite(Request $request)
    {
        $validatedData = $request->validate([
            'course_site_id' => 'required',
            'course_site_name' => 'required',
        ]);
        $data = $this->onboardSettingService->saveCourseSite($request->input());
        SettingTracker::Completed($request['tracking_form']);
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function getCourseSiteDetails(Request $request)
    {
        $data = CourseSite::find($request->id);

        return $this->successResponse($data['message'], 'data', $data);
    }

    public function updateCourseSite(Request $request)
    {
        $data = $this->onboardSettingService->updateCourseSiteData($request->input());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }

    }

    public function saveElearningLink(Request $request)
    {
        $validatedData = $request->validate([
            'title' => 'required',
            'link' => 'required',
        ]);
        $data = $this->onboardSettingService->saveElearningLink($request->input());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function saveBankInfo(Request $request)
    {
        $validatedData = $request->validate([
            'campus_id' => 'required',
            'bank_name' => 'required',
            'branch_name' => 'required',
            'acnt_name' => 'required',
            'acnt_no' => 'required',
        ]);
        $data = $this->onboardSettingService->saveBankInfo($request->input());
        SettingTracker::Completed($request['tracking_form']);
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function saveReportLetter(Request $request)
    {
        $validatedData = $request->validate([
            'letter_id' => 'required',
            'student_id' => 'required',
        ]);
        $data = $this->onboardSettingService->saveReportLetter($request->input());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function saveHolidayDetails(Request $request)
    {
        $validatedData = $request->validate([
            'holiday_date' => 'required | date',
            'description' => 'required',
        ]);
        $data = $this->onboardSettingService->saveHolidayDetails($request->input());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function updateReportData(Request $request)
    {
        $validatedData = $request->validate([
            'report_id' => 'required',
            'role_id' => 'required',
        ]);
        $data = $this->onboardSettingService->updateReportData($request->input());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function saveCourseTemplate(Request $request)
    {
        $validatedData = $request->validate([
            'template_name' => 'required',
            'course_id' => 'required',
            'no_of_core_subject' => 'required | integer',
            'no_of_elective_subject' => 'required | integer',
        ]);
        $data = $this->onboardSettingService->saveCourseTemplate($request->input());
        SettingTracker::Completed($request['tracking_form']);
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function updateProfileData(Request $request)
    {
        $data = $this->onboardSettingService->updateProfileData($request->input());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function updateUserPassword(Request $request)
    {
        $validatedData = $request->validate([
            'old_password' => 'required',
            'new_password' => 'required|min:8|max:16',
            'password' => 'required|same:new_password',
        ],
            [
                'password.same' => 'The new password and confirm password must match.',
            ]);
        if (Hash::check($request->old_password, Auth::user()->password)) {
            $data = $this->onboardSettingService->updateProfileData($request->input());
            if ($data['status'] == 'success') {
                return $this->successResponse($data['message'], 'data', $data);
            } else {
                return $this->errorResponse($data['message'], 'data', $data);
            }
        } else {
            return $this->errorResponse('Old Password does not match.', 'data', [], 200);
        }

    }

    public function getSmsTemplateData(Request $request)
    {
        $data = $this->onboardSettingService->getSmsTemplateData($request->input());

        return $this->successResponse('Data Found Successfully Saved', 'data', $data);
    }

    public function getEmailTemplateTitleData(Request $request)
    {
        $data = $this->onboardSettingService->getEmailTemplateTitleData($request->input());

        return $this->successResponse('Data Found Successfully Saved', 'data', $data);
    }

    public function getEmailTemplateData(Request $request)
    {
        $data = $this->onboardSettingService->getEmailTemplateData($request->input());

        return $this->successResponse('Data Found Successfully Saved', 'data', $data);
    }

    public function getLetterTemplateData(Request $request)
    {
        $data = $this->onboardSettingService->getLetterTemplateData($request->input());

        return $this->successResponse('Data Found Successfully Saved', 'data', $data);
    }

    public function saveSmsTemplate(Request $request)
    {
        $data = $this->onboardSettingService->saveSmsTemplate($request->input());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function saveEmailTemplate(Request $request)
    {
        $data = $this->onboardSettingService->saveEmailTemplate($request->input());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function saveLetterTemplate(Request $request)
    {
        $data = $this->onboardSettingService->saveLetterTemplate($request->input());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function deleteContractCode(Request $request)
    {
        $data = $this->onboardSettingService->deleteContractCode($request->input());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function deleteContractFundingSource(Request $request)
    {
        $data = $this->onboardSettingService->deleteContractFundingSource($request->input());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function deleteCourseSite(Request $request)
    {
        $data = $this->onboardSettingService->deleteCourseSite($request->input());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function deleteElearningLink(Request $request)
    {
        $data = $this->onboardSettingService->deleteElearningLink($request->input());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function deleteBankInfo(Request $request)
    {
        $data = $this->onboardSettingService->deleteBankInfo($request->input());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function getBankInfo(Request $request)
    {
        $data = $this->onboardSettingService->getBankInfo($request->input());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function deleteHoliday(Request $request)
    {
        $data = $this->onboardSettingService->deleteHoliday($request->input());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function deleteCourseTemplate(Request $request)
    {
        $data = $this->onboardSettingService->deleteCourseTemplate($request->input());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function getReportName(Request $request)
    {
        $data = [];
        if ($request->input('category_id')) {
            $data = $this->onboardSettingService->getReportName($request->input());
        }

        return $this->successResponse('Data found successfully', 'data', $data);

    }

    public function getCourseByType(Request $request)
    {
        $allOption[0] = ['Id' => 'All', 'Name' => 'All'];
        $data = $this->onboardSettingService->getCourseByType($request->input());

        return $this->successResponse('Data found successfully', 'data', array_merge($allOption, $data));
    }

    public function getCourseYear(Request $request)
    {
        $data = $this->onboardSettingService->getCourseYear($request->input());

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getCourseIntakeDate(Request $request)
    {
        $data = $this->onboardSettingService->getCourseIntakeDate($request->input());

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getCourseSemester(Request $request)
    {
        $data = $this->onboardSettingService->getCourseSemester($request->input());

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getCourseTerm(Request $request)
    {
        $data = $this->onboardSettingService->getCourseTerm($request->input());

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getCourseSubject(Request $request)
    {
        $data = $this->onboardSettingService->getCourseSubject($request->input());

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getCourseBatch(Request $request)
    {
        $data = $this->onboardSettingService->getCourseBatch($request->input());

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getCourseClass(Request $request)
    {
        $data = $this->onboardSettingService->getCourseClass($request->input());

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getReportLetterList(Request $request)
    {
        $data = $this->onboardSettingService->getReportLetterList($request->input());

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getReportLetterDetails(Request $request)
    {
        $data = $this->onboardSettingService->getReportLetterDetails($request->input());

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function ajaxAction(Request $request)
    {

        $action = $request->input('action');
        $data = $request->input('data');
        switch ($action) {
            case 'generateReport':
                $reportId = $data['report_id'];
                $data['reportName'] = $this->onboardSettingService->reportName($reportId);
                $data['from_date'] = date('Y-m-d', strtotime($data['from_date']));
                $data['to_date'] = date('Y-m-d', strtotime($data['to_date']));
                $data['date'] = date('Y-m-d', strtotime($data['date']));
                $data['intake_date'] = date('Y-m-d', strtotime($data['intake_date']));
                $data['college_id'] = Auth::user()->college_id;
                $arrPaidDuration = Config::get('constants.arrPaidDuration');

                switch ($reportId) {
                    case Report::REPORT_AGENT_COMMISSION: // Agent: Agent Commission ready to process between two dates
                        $data['arrReportData'] = $this->onboardSettingService->generateAgentCommissionReport($data);
                        $returnHTML = view('v2.sadmin.onboardsetting.reports.agent-commission-report', $data)->render();

                        return response()->json(['success' => true, 'html' => $returnHTML]);
                        break;
                    case Report::REPORT_FEE_OVERDUE: // Agent: Agent Commission ready to process between two dates
                        $data['arrReportData'] = $this->onboardSettingService->generateStudentInitialPaymentReport($data);
                        $data['arrCourseInvoiceDue'] = $this->onboardSettingService->getCourseInvoiceDue($data);
                        $data['arrPaidDuration'] = $arrPaidDuration;
                        $returnHTML = view('v2.sadmin.onboardsetting.reports.fee-overdue-report', $data)->render();

                        return response()->json(['success' => true, 'html' => $returnHTML]);
                        break;
                    case Report::REPORT_FINANCE_ALL_TUITION: // Finance: All tuition fee invoice list between two dates
                        $data['arrReportData'] = $this->onboardSettingService->generateTuitionFeeInvoiceReport($data);
                        $data['arrAgentComissionPaid'] = $this->onboardSettingService->getAgentComissionPaid($data);
                        $data['arrPaidDuration'] = $arrPaidDuration;
                        $returnHTML = view('v2.sadmin.onboardsetting.reports.tuition-fee-invoice-report', $data)->render();

                        return response()->json(['success' => true, 'html' => $returnHTML]);
                        break;
                    case Report::REPORT_PAYMENT_REFUND: // Refund: Payment refund list between two dates
                        $data['arrReportData'] = $this->onboardSettingService->generatePaymentRefundReport($data);
                        $data['arrPaidMode'] = $this->onboardSettingService->getPaymentModeData($data);
                        $returnHTML = view('v2.sadmin.onboardsetting.reports.payment-refund-list-report', $data)->render();

                        return response()->json(['success' => true, 'html' => $returnHTML]);
                        break;
                    case Report::REPORT_VIEW_APPLICATION: // Application : View application by (id/firstname/lastname/passport-no/dob)
                        $data['arrReportData'] = $this->onboardSettingService->generateApplicationReport($data);
                        $returnHTML = view('v2.sadmin.onboardsetting.reports.application-report', $data)->render();

                        return response()->json(['success' => true, 'html' => $returnHTML]);
                        break;
                    case Report::REPORT_STUDENT_ENROLMENT: // Enrollment: Student enrolment between two dates
                        $data['arrReportData'] = $this->onboardSettingService->generateEnrollmentReport($data);
                        $returnHTML = view('v2.sadmin.onboardsetting.reports.student-enrollment-report', $data)->render();

                        return response()->json(['success' => true, 'html' => $returnHTML]);
                        break;
                    case Report::REPORT_VIEW_AGENT_COMMISSION_OWING: // Finance: View agent commission owing details at a given date
                        $data['arrReportData'] = $this->onboardSettingService->getAgentCommissionOwingDetails($data);
                        $returnHTML = view('v2.sadmin.onboardsetting.reports.agent-commission-owing-report', $data)->render();

                        return response()->json(['success' => true, 'html' => $returnHTML]);
                        break;
                    case Report::REPORT_WARNING_LETTER_SENT: // Student : Warning letter sent list
                        $data['arrReportData'] = []; // no data in old one
                        $returnHTML = view('v2.sadmin.onboardsetting.reports.student-warning-letter-sent-reportt', $data)->render();

                        return response()->json(['success' => true, 'html' => $returnHTML]);
                        break;
                    case Report::REPORT_AGENT_CONVERSION: // Agent: Agent conversion report between two dates
                        $data['arrReportData'] = $this->onboardSettingService->getAgentConversionReport($data);
                        $returnHTML = view('v2.sadmin.onboardsetting.reports.agent-conversion-report', $data)->render();

                        return response()->json(['success' => true, 'html' => $returnHTML]);
                        break;
                    case Report::REPORT_AGENT_LIST_BY_STATUS_AND_TARGET: // Agent: Agent list by status and target country
                        $data['arrCountry'] = $this->onboardSettingService->getCountryList($data);
                        $data['arrReportData'] = $this->onboardSettingService->getAgentListByStatusAndCountry($data);
                        $returnHTML = view('v2.sadmin.onboardsetting.reports.agentlist-status-country-report', $data)->render();

                        return response()->json(['success' => true, 'html' => $returnHTML]);
                        break;
                    case Report::REPORT_NO_OF_STUDENT_BY_AGENT_BY_STATUS: // Student: No of student by agent by status between two dates
                        $data['arrReportData'] = $this->onboardSettingService->getStudentCountByStatus($data);
                        $returnHTML = view('v2.sadmin.onboardsetting.reports.students-count-by-agent-status-report', $data)->render();

                        return response()->json(['success' => true, 'html' => $returnHTML]);
                        break;
                    case Report::REPORT_NO_OF_STUDENT_CURRENTLY_STUDYING: // Student: No of students currently studying in college
                        $data['arrReportData'] = $this->onboardSettingService->getStudentCountByCourse($data);
                        $returnHTML = view('v2.sadmin.onboardsetting.reports.students-currently-studying-college-report', $data)->render();

                        return response()->json(['success' => true, 'html' => $returnHTML]);
                        break;
                    case Report::REPORT_LIST_OF_OFFERS: // Offer: list of offers generated between two dates
                        $data['arrReportData'] = $this->onboardSettingService->getStudentOfferList($data);
                        $returnHTML = view('v2.sadmin.onboardsetting.reports.offer-list-generated-report', $data)->render();

                        return response()->json(['success' => true, 'html' => $returnHTML]);
                        break;
                    case Report::REPORT_DOCUMENT_CHECKLIST: // document: Document checklist report (missing documents only)
                        $data['arrReportData'] = $this->onboardSettingService->getMissingDocuments($data);
                        $returnHTML = view('v2.sadmin.onboardsetting.reports.document-checklist-missing-documents-report', $data)->render();

                        return response()->json(['success' => true, 'html' => $returnHTML]);
                        break;
                    case Report::REPORT_STUDENT_OFFER_LIST: // Offer list: Student offer list between two dates
                        $data['arrReportData'] = $this->onboardSettingService->getStudentOfferListNotEnrolled($data);
                        $returnHTML = view('v2.sadmin.onboardsetting.reports.student-offerlist-report', $data)->render();

                        return response()->json(['success' => true, 'html' => $returnHTML]);
                        break;
                    case Report::REPORT_STUDENT_LIST_BY_INTAKE_DATE: // Student list: Student list by intake date and course
                        $data['arrReportData'] = $this->onboardSettingService->getStudentListByIntakeDate($data);
                        $returnHTML = view('v2.sadmin.onboardsetting.reports.student-list-by-intake-date-report', $data)->render();

                        return response()->json(['success' => true, 'html' => $returnHTML]);
                        break;
                    case Report::REPORT_STUDENT_LIST_COMPLETED_COURSE: // Academic: Student list completed course & graduating
                        $data['arrReportData'] = $this->onboardSettingService->getStudentListCompletedCourse($data);
                        $returnHTML = view('v2.sadmin.onboardsetting.reports.student-list-completed-course-report', $data)->render();

                        return response()->json(['success' => true, 'html' => $returnHTML]);
                        break;
                    case Report::REPORT_STUDENT_LIST_FAILING_MORE_SUBJECT_IN_CONSECUTIVE_SEMESTER: // Academic: Student list failing 50% or more subject in consecutive semesterss
                        $data['arrReportData'] = $this->onboardSettingService->getStudentsFailingConsecutiveSemester($data);
                        $returnHTML = view('v2.sadmin.onboardsetting.reports.student-list-failing-consecutive-semesters', $data)->render();

                        return response()->json(['success' => true, 'html' => $returnHTML]);
                        break;
                    case Report::REPORT_STUDENT_LIST_FAILING_MORE_SUBJECT_IN_ONE_SEMESTER: // Academic: Student list failing 50% or more subjects in one semester
                        $data['arrReportData'] = $this->onboardSettingService->getStudentsFailingOneSemester($data);
                        $returnHTML = view('v2.sadmin.onboardsetting.reports.student-list-failing-one-semester', $data)->render();

                        return response()->json(['success' => true, 'html' => $returnHTML]);
                        break;
                    case Report::REPORT_FIVE_CONSECUTIVE_ATTENDANCE_MISSING: // Attendance: Five consecutive attenandance missing list
                        $data['arrReportData'] = $this->onboardSettingService->getAttendanceMissingList($data);
                        $returnHTML = view('v2.sadmin.onboardsetting.reports.five-consecutive-attendance-missing-report', $data)->render();

                        return response()->json(['success' => true, 'html' => $returnHTML]);
                        break;
                    case Report::REPORT_STUDENT_ATTENDANCE_BY_CLASS: // Attendacne: Student attendance by class
                        $class = $data['class_id'];
                        $arrClass = explode(':', $class);
                        if (! empty($class)) {
                            $classStart = $arrClass[0];
                            $classFinish = $arrClass[1];
                        }
                        $data['arrReportData'] = $this->onboardSettingService->getStudentAttendanceByCourseClass($data);
                        $returnHTML = view('v2.sadmin.onboardsetting.reports.student-attendance-report', $data)->render();

                        return response()->json(['success' => true, 'html' => $returnHTML]);
                        break;
                    case Report::REPORT_STUDENT_CONTACT_UPLOAD: // PRISMS: student contact upload
                        $data['arrReportData'] = $this->onboardSettingService->getStudentContactUploadReport($data);
                        $returnHTML = view('v2.sadmin.onboardsetting.reports.student-contact-upload-report', $data)->render();

                        return response()->json(['success' => true, 'html' => $returnHTML]);
                        break;
                    case Report::REPORT_STUDENT_LIST_BY_COURSE_AND_CLASS: // Student: student list by course and class
                        $class = $data['class_id'];
                        $arrClass = explode(':', $class);
                        if ($class) {
                            $classStart = $arrClass[0];
                            $classFinish = $arrClass[1];
                        } else {
                            $classStart = '';
                            $classFinish = '';
                        }
                        $data['arrReportData'] = $this->onboardSettingService->getStudentListByCourseClass($data, $classStart, $classFinish);
                        $returnHTML = view('v2.sadmin.onboardsetting.reports.student-list-by-course-class-report', $data)->render();

                        return response()->json(['success' => true, 'html' => $returnHTML]);
                        break;
                    case Report::REPORT_STUDENT_LIST_WITH_MISSING_USI_NUMBERS: // USI: Student list with missing USI numbers studying between two dates
                        $data['arrReportData'] = $this->onboardSettingService->getStudentListMissingUSINumbers($data);
                        $returnHTML = view('v2.sadmin.onboardsetting.reports.student-list-missing-usi-number-report', $data)->render();

                        return response()->json(['success' => true, 'html' => $returnHTML]);
                        break;
                    case Report::REPORT_STUDENT_LIST_WITH_UNIT_GRADE: // Student: Unit Assessment Summary for Students
                        $data['arrReportData'] = $this->onboardSettingService->getStudentListForUnitAssessmentGrade($data);
                        $returnHTML = view('v2.sadmin.onboardsetting.reports.student-unit-grade-report', $data)->render();

                        return response()->json(['success' => true, 'html' => $returnHTML]);
                        break;
                }
        }

    }

    public function getCoursesUpfrontFeeList(Request $request)
    {
        $data = $this->onboardSettingService->getCoursesUpfrontFeeList($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getFullCoursesList()
    {
        $data = $this->onboardSettingService->getFullCoursesArrList();

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getCourseFee(Request $request)
    {
        $data = $this->onboardSettingService->getCourseFeeData($request->input());

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function saveCourseUpfrontFee(Request $request)
    {
        $validatedData = $request->validate([
            'course_id' => 'required',
            'orgin' => 'required',
            'agent_id' => 'required',
            'upfront_fee' => 'required',
            'no_of_installments' => 'required',
            'frequency' => 'required',
            'material_fee' => 'required',
        ]);
        $data = $this->onboardSettingService->saveCourseUpfrontFeeData($request->input());
        SettingTracker::Completed(request('tracking_form'));
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data, 200);
        }
    }

    public function getUpFrontFeeDetailForEdit(Request $request)
    {

        $data = $this->onboardSettingService->getUpFrontFeeDetailForEditData($request->input());

        return $this->successResponse('Course Upfront Fee is Saved Successfully.', 'data', $data);
    }

    public function editCourseUpfrontFee(Request $request)
    {

        $validatedData = $request->validate([
            'upfront_fee' => 'required',
            'no_of_installments' => 'required',
            'frequency' => 'required',
            'material_fee' => 'required',
        ]);
        $data = $this->onboardSettingService->editCourseUpfrontFeeData($request->input());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function deleteUpfrontFee(Request $request)
    {

        $data = $this->onboardSettingService->deleteUpfrontFeeData($request->input());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function getElicosDiscountWeekList(Request $request)
    {
        $data = $this->onboardSettingService->getElicosDiscountWeekList($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function saveElicosDiscountWeek(Request $request)
    {
        $validatedData = $request->validate([
            'course_id' => 'required',
            'applied_week' => 'required',
            'discount_week' => 'required',
            'from_date' => 'required|date',
            'to_date' => 'required|date|after:from_date',
        ]);
        $data = $this->onboardSettingService->saveElicosDiscountWeekData($request->input());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function getElicosDiscountWeek(Request $request)
    {

        $data = $this->onboardSettingService->getElicosDiscountWeekData($request->input());

        return $this->successResponse('Data Found Successfully.', 'data', $data);
    }

    public function editElicosDiscountWeek(Request $request)
    {

        $validatedData = $request->validate([
            'course_id' => 'required',
            'applied_week' => 'required',
            'discount_week' => 'required',
            'from_date' => 'required|date',
            'to_date' => 'required|date|after:from_date',
        ]);
        $data = $this->onboardSettingService->editElicosDiscountWeek($request->input());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function deleteElicosDiscountWeek(Request $request)
    {

        $data = $this->onboardSettingService->deleteElicosDiscountWeekData($request->input());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function getCoursePromotionPriceList(Request $request)
    {
        $data = $this->onboardSettingService->getCoursePromotionPriceList($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function saveCoursePromotionPrice(Request $request)
    {
        $validatedData = $request->validate([
            'course_id' => 'required',
            'country_level' => 'required',
            'price' => 'required',
            'from_date' => 'required|date',
            'to_date' => 'required|date|after:from_date',
        ]);

        $data = $this->onboardSettingService->saveCoursePromotionPriceData($request->input());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function getCoursePromotionPrice(Request $request)
    {

        $data = $this->onboardSettingService->getCoursePromotionPriceData($request->input());

        return $this->successResponse('Data Found Successfully.', 'data', $data);
    }

    public function editCoursePromotionPrice(Request $request)
    {
        $validatedData = $request->validate([
            'course_id' => 'required',
            'country_level' => 'required',
            'price' => 'required',
            'from_date' => 'required|date',
            'to_date' => 'required|date|after:from_date',
        ]);
        $data = $this->onboardSettingService->editCoursePromotionPrice($request->input());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function deleteCoursePromotionPrice(Request $request)
    {

        $data = $this->onboardSettingService->deleteCoursePromotionPriceData($request->input());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', $data);
        }
    }

    public function getCampusNameList(Request $request)
    {
        $data = $this->onboardSettingService->getCampusNameListData($request->input());

        return $this->successResponse('Data Found', 'data', $data);
    }

    public function getCourseTypeList(Request $request)
    {
        $data = $this->onboardSettingService->getCourseTypeListData($request->input());

        return $this->successResponse('Data Found', 'data', $data);
    }

    public function getCourseIntakeYear(Request $request)
    {
        $data = $this->onboardSettingService->getCourseIntakeYearData($request->input());

        return $this->successResponse('Data Found', 'data', $data);
    }

    public function getCourseListByType(Request $request)
    {
        $data = $this->onboardSettingService->getCourseListByTypeData($request->input());
        $result = [];
        foreach ($data as $row) {
            $result[] = [
                'value' => $row['value'],
                'label' => $row['label'],
            ];
        }

        return $this->successResponse('Data Found', 'data', $result);
    }

    public function getAllDateFromYear(Request $request)
    {
        $year = $request->input('year');

        $startDate = $year.'-01-01';
        $endDate = $year.'-12-31';

        for ($i = strtotime($startDate); $i <= strtotime($endDate); $i = strtotime('+1 day', $i)) {
            if (date('N', $i) == 1) { // Monday == 1
                $date_array[] = date('d-m-Y', $i); // prints the date only if it's a Monday
            }
        }

        return $this->successResponse('Data Found', 'data', $date_array);
    }

    public function saveCoursesIntakeDateAdd(SaveCoursesIntakeDates $request)
    {
        $data = $this->onboardSettingService->saveCoursesIntakeDateAddData($request->input());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', null, 200);
        }

    }

    public function getCoursesIntakeList(Request $request)
    {

        $data = $this->onboardSettingService->getCoursesIntakeListData($request->input());

        return $this->successResponse('Data found ', 'data', $data);

    }

    public function deleteCoursesInkaeDate(Request $request)
    {
        $data = $this->onboardSettingService->deleteCoursesIntakeDateData($request->input());
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', null, 200);

        }

    }

    public function deleteLetterTemplateData(Request $request, $id)
    {
        $data = $this->onboardSettingService->deleteLetterTemplateData($request->input(), $id);
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', null, 200);

        }

    }

    public function deleteEmailTemplateData(Request $request, $id)
    {
        $data = $this->onboardSettingService->deleteEmailTemplateData($request->input(), $id);
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', null, 200);

        }

    }

    public function deleteSmsTemplateData(Request $request, $id)
    {
        $data = $this->onboardSettingService->deleteSmsTemplateData($request->input(), $id);
        if ($data['status'] == 'success') {
            return $this->successResponse($data['message'], 'data', $data);
        } else {
            return $this->errorResponse($data['message'], 'data', null, 200);

        }
    }
}
