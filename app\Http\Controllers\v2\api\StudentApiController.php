<?php

namespace App\Http\Controllers\v2\api;

use App;
use App\Exceptions\ApplicationException;
use App\Exports\AgentStudentExport;
use App\Helpers\Helpers;
use App\Http\Controllers\Controller;
use App\Mail\sendStudentInviteMail;
use App\Model\CoursesUpfrontFee;
use App\Model\PromotionPrice;
use App\Model\SetupServicesName;
use App\Model\v2\Agent;
use App\Model\v2\CampusVenue;
use App\Model\v2\CollegeDetails;
use App\Model\v2\CollegeEnrollmentFees;
use App\Model\v2\ContractCode;
use App\Model\v2\Country;
use App\Model\v2\CourseBatch;
use App\Model\v2\CourseCalendar;
use App\Model\v2\Courses;
use App\Model\v2\CoursesIntakeDate;
use App\Model\v2\CourseSite;
use App\Model\v2\CourseSubject;
use App\Model\v2\CourseTemplate;
use App\Model\v2\CourseTraining;
use App\Model\v2\CourseType;
use App\Model\v2\EmailTemplateDocuments;
use App\Model\v2\Employer;
use App\Model\v2\LetterSetting;
use App\Model\v2\PaymentMode;
// TODO::not-used
use App\Model\v2\ReportLetterSetup;
use App\Model\v2\ResultGrade;
use App\Model\v2\Semester;
use App\Model\v2\SemesterDivision;
use App\Model\v2\SmtpSetup;
use App\Model\v2\Student;
use App\Model\v2\StudentAdditionalServiceRequest;
use App\Model\v2\StudentCertificateRegister;
use App\Model\v2\StudentCommunicationLog;
use App\Model\v2\StudentCourses;
use App\Model\v2\StudentDetails;
// TODO::not-used
use App\Model\v2\StudentInitialPaymentDetails;
use App\Model\v2\StudentInitialPaymentTransaction;
use App\Model\v2\StudentMiscellaneousPayment;
use App\Model\v2\StudentSubjectEnrolment;
use App\Model\v2\StudentSubjectEnrolmentHistory;
use App\Model\v2\StudentUnitEnrollment;
use App\Model\v2\Users as userModal;
// TODO::not-used
use App\Repositories\StudentProfileCommonRepository;
use App\Repositories\StudentProfileRepository;
use App\Repositories\StudentRepository;
use App\Services\MoodleService;
use App\Services\StudentProfileCommonService;
use App\Services\TemplateService;
use App\Services\USIService;
// TODO::not-used
use App\Traits\CommonTrait;
use App\Traits\LetterGenerateAndSendTrait;
use App\Traits\ResponseTrait;
use App\Traits\SendEmailTrait;
// TODO::not-used
use App\Users;    // TODO::not-used
// TODO::not-used
// TODO::not-used
use Domains\Moodle\Facades\Moodle;
use Domains\Moodle\Jobs\UnEnrollUserToMoodleCourse;
use Domains\Moodle\Moodle as MoodleMoodle;
use Domains\Moodle\Traits\MoodleStatusTrait;
use Domains\Moodle\Traits\MoodleSyncableItem;
use Domains\Xero\Facades\Xero;
use File;
use Illuminate\Http\Request;
use Illuminate\Support\Arr;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
// TODO::not-used
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Facades\Password;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Str;
use Integrations\Zoho\Traits\ZohoTrait;
use Maatwebsite\Excel\Facades\Excel;
use Mail;
use SSO\Events\IdentityCreated;
use SSO\Jobs\SyncUserToKeycloak;
use SSO\Traits\SSOStatusTrait;
use Support\Services\UploadService;
use ZipArchive;

class StudentApiController extends Controller
{
    use CommonTrait;
    use LetterGenerateAndSendTrait;
    use MoodleStatusTrait;
    use MoodleSyncableItem;
    use ResponseTrait;
    use SendEmailTrait;
    use SSOStatusTrait;
    use ZohoTrait;

    private $studentModel;

    protected $studentProfileCommonRepository;

    protected $usiService;

    protected $studentProfileCommonService;

    protected $studentCourse;

    protected $communicationLog;

    protected $studentSubjectEnrolment;

    protected $courses;

    protected $country;

    protected $student;

    protected $studentDetails;

    protected $studentInitialPaymentTransaction;

    protected $studentInitialPaymentDetails;

    protected $studentMiscellaneousPayment;

    protected $studentAdditionalServiceRequest;

    protected $collegeEnrollmentFees;

    protected $coursesUpfrontFee;

    protected $coursesIntakeDate;

    protected $promotionPrice;

    protected $employer;

    protected $contractCode;

    protected $courseSite;

    protected $studentCertificateRegister;

    protected $paymentMode;

    protected $courseTraining;

    protected $letterSetting;

    protected $resultGrade;

    private $templateService;

    protected $moodleService;

    public function __construct(
        Student $studentModel,
        StudentDetails $studentDetails,
        StudentCommunicationLog $communicationLog,
        Courses $courses,
        Country $country,
        StudentSubjectEnrolment $studentSubjectEnrolment,
        StudentCourses $studentCourseModel,
        StudentInitialPaymentTransaction $initialPaymentTransaction,
        StudentInitialPaymentDetails $studentInitialPaymentDetails,
        StudentMiscellaneousPayment $studentMiscellaneousPayment,
        StudentAdditionalServiceRequest $studentAdditionalServiceRequest,
        CollegeEnrollmentFees $collegeEnrollmentFees,
        CoursesUpfrontFee $coursesUpfrontFee,
        CoursesIntakeDate $coursesIntakeDate,
        PromotionPrice $promotionPrice,
        Employer $employer,
        ContractCode $contractCode,
        CourseSite $courseSite,
        StudentCertificateRegister $studentCertificateRegister,
        PaymentMode $paymentMode,
        CourseTraining $courseTraining,
        LetterSetting $letterSetting,
        ResultGrade $resultGrade,
        USIService $usiService,
        StudentProfileCommonRepository $studentProfileCommonRepository,
        StudentProfileCommonService $studentProfileCommonService,
        TemplateService $templateService,
        MoodleService $moodleService
    ) {
        $this->studentModel = new StudentRepository($studentModel);
        $this->communicationLog = new StudentRepository($communicationLog);
        $this->studentSubjectEnrolment = new StudentProfileRepository($studentSubjectEnrolment);
        $this->courses = new StudentProfileRepository($courses);
        $this->country = new StudentProfileRepository($country);
        $this->studentCourse = new StudentProfileRepository($studentCourseModel);
        $this->student = new StudentProfileRepository($studentModel);
        $this->studentDetails = new StudentProfileRepository($studentDetails);
        $this->studentInitialPaymentTransaction = new StudentProfileRepository($initialPaymentTransaction);
        $this->studentInitialPaymentDetails = new StudentProfileRepository($studentInitialPaymentDetails);
        $this->studentMiscellaneousPayment = new StudentProfileRepository($studentMiscellaneousPayment);
        $this->studentAdditionalServiceRequest = new StudentProfileRepository($studentAdditionalServiceRequest);
        $this->collegeEnrollmentFees = new StudentProfileRepository($collegeEnrollmentFees);
        $this->coursesUpfrontFee = new StudentProfileRepository($coursesUpfrontFee);
        $this->coursesIntakeDate = new StudentProfileRepository($coursesIntakeDate);
        $this->promotionPrice = new StudentProfileRepository($promotionPrice);
        $this->employer = new StudentProfileRepository($employer);
        $this->contractCode = new StudentProfileRepository($contractCode);
        $this->courseSite = new StudentProfileRepository($courseSite);
        $this->studentCertificateRegister = new StudentProfileRepository($studentCertificateRegister);
        $this->paymentMode = new StudentProfileRepository($paymentMode);
        $this->courseTraining = new StudentProfileRepository($courseTraining);
        $this->letterSetting = new StudentProfileRepository($letterSetting);
        $this->resultGrade = new StudentProfileRepository($resultGrade);
        $this->studentProfileCommonService = $studentProfileCommonService;
        $this->usiService = $usiService;
        $this->studentProfileCommonRepository = $studentProfileCommonRepository;
        $this->templateService = $templateService;
        $this->moodleService = $moodleService;

        ini_set('memory_limit', '-1');
    }

    public function getStudentDataScout(Request $request)
    {

        $results = $this->studentModel->getStudentSearchData($request);

        $data['total'] = $results['total'];
        $data['data'] = utf8ize($results['records']->toArray());

        // $data['data'] =  $this->studentModel->getStudentSearchData($request);
        // $data['total'] =  $this->studentModel->getStudentSearchData($request,true);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getStudentData(Request $request)
    {
        $post = ($request->input()) ? $request->input() : [];
        $customFilterParts = (isset($post['filter']) && isset($post['filter']['filters'])) ? $post['filter']['filters'] : [];
        $isFilter = false;
        // $isLeftFilter = false;

        $arrData = $this->getStudentCacheData();
        if (! $arrData) {
            $this->setStudentCacheData();
            $dataArr = $this->studentModel->getStudentDataV2($request);

            $data['data'] = $this->convertToUTF_1($dataArr);
            $data['total'] = $this->studentModel->getStudentDataV2($request, true);

            return $this->successResponse('Data found successfully', 'data', $data);
        }

        if (isset($post['sort']) && count($post['sort']) > 0) {
            $sortField = $post['sort'][0]['field'];
            $sortType = ($post['sort'][0]['dir'] == 'asc') ? SORT_ASC : SORT_DESC;
            array_multisort(array_column($arrData['data'], $sortField), $sortType, $arrData['data']);
        }

        foreach ($arrData['data'] as $k => $row) {
            $arrData['data'][$k]['secure_id'] = encryptIt($row['id']);
            $arrData['data'][$k]['profile_pic'] = $this->getStudentProfilePicPath($row['id'], $row['profile_picture']);
            $arrData['data'][$k]['mini_profile_pic'] = $this->getStudentProfilePicPath($row['id'], $row['profile_picture'], 'small');
        }

        if (! empty($customFilterParts)) {
            foreach ($customFilterParts as $filters) {
                if (Arr::accessible($filters)) {
                    $isFilter = true;
                    /*if (isset($filters['field']) && $filters['field'] == 'extra') {
                        $isLeftFilter = true;
                        break;
                    }*/
                }
            }

            if ($isFilter) {
                $arrData['data'] = $this->applyFilter($post, $arrData);
            }
        }

        $data['data'] = array_slice($arrData['data'], $post['skip'], $post['take']);
        $data['total'] = count($arrData['data']);

        return $this->successResponse('Data found successfully', 'data', $data);
        // try{

        // } catch(\Exception $e){
        //     dd($e);
        // }
    }

    // private function staticFilterData(){
    //     return [
    //         [
    //             "field"     => "searchKey",
    //             "operator"  => "contains",
    //             "value"     => "a"
    //         ],
    //         [
    //             "field"     => "extra",
    //             "operator"  => "contains",
    //             "value"     => [
    //                 "course_id" => ["18", "23", "34"],
    //                 "teacher"   => ["21", "25"],
    //                 "nationality"  => ["9", "8"]
    //             ]
    //         ],
    //         [
    //             "logic" => "or",
    //             "filters" => [
    //                 ["field" => "student_name", "operator" => "eq", "value" => "Bikee RIJAL"],
    //                 ["field" => "student_name", "operator" => "eq", "value" => "Prashant PAUDEL"],
    //                 ["field" => "student_name", "operator" => "eq", "value" => "Dolma Sherpa"]
    //             ]
    //         ],
    //         [
    //             "logic" => "or",
    //             "filters" => [
    //                 ["field" => "student_type", "operator" => "eq", "value" => "Onshore"],
    //                 ["field" => "student_type", "operator" => "eq", "value" => "Offshore"]
    //             ]
    //         ]
    //     ];
    // }

    private function applyFilter($post, $arrStudentCache)
    {
        // $customFilterParts = $this->staticFilterData();
        $customFilterParts = (isset($post['filter']) && isset($post['filter']['filters'])) ? $post['filter']['filters'] : [];
        $arrayFilter = $arrData = $arrStudentCache['data'];
        // dd($customFilterParts);
        $columnsArr = ['student_name', 'course_list', 'student_type', 'DOB', 'student_id', 'campus', 'formatted_dob'];

        foreach ($customFilterParts as $filter) {
            if (isset($filter['field']) && $filter['field'] != 'searchKey') {
                $arrData = $this->applySidebarFilter($arrData, $filter['field']);
            }
            // elseif (isset($filter['filters']) && Arr::accessible($filter['filters'])) {
            //     $arrData = $this->filterAndCollapseData($arrData, $filter['filters']);
            // }
            elseif (isset($filter['field']) && $filter['field'] == 'searchKey') {
                $arrData = $this->applySearchFilter($arrData, $filter['value'], $columnsArr);
            }
            // elseif (isset($filter['field']) && in_array($filter['field'], $columnsArr)) {
            //     $dataArr5 = [];
            //     foreach ($columnsArr as $column) {
            //         $dataArr5[] = $this->findWhere($arrData, array($column => $filter['value']), 'like');
            //     }
            //     $arrData = Arr::collapse($dataArr5);
            // }
        }

        $dataArr = array_unique($arrData, SORT_REGULAR);

        foreach ($dataArr as $key => $student) {
            $dataArr[$key]['profile_pic'] = $this->getStudentProfilePicPath($student['id'], $student['profile_picture']);
            $dataArr[$key]['mini_profile_pic'] = $this->getStudentProfilePicPath($student['id'], $student['profile_picture'], 'small');
        }

        return $dataArr;

        /*$data['data'] = array_slice($dataArr, $post['skip'], $post['take']);
        $data['total'] = count($dataArr);
        unset($dataArr, $arrStudentCache);
        return $data;*/
    }

    private function applySidebarFilter($arrData, $filterDataArr)
    {
        foreach ($filterDataArr as $fieldName => $fieldValue) {
            if (count($fieldValue) > 0) {
                if (in_array($fieldName, ['status', 'student_type', 'nationality'])) {
                    if ($fieldName == 'status') {
                        $arrData = $this->filterAndCollapseData($arrData, $fieldValue, $fieldName, 'like');
                    } else {
                        $arrData = $this->filterAndCollapseData($arrData, $fieldValue, $fieldName);
                    }

                } elseif (in_array($fieldName, ['teacher', 'batch', 'student_intake'])) {
                    if ($fieldName == 'teacher') {
                        $filteredStudIds = $this->studentModel->getStudentIdBasedOnTeachers($fieldValue);
                    } elseif ($fieldName == 'batch') {
                        $filteredStudIds = $this->studentModel->getStudentIdBasedOnBatch($fieldValue);
                    } else {
                        $filteredStudIds = $this->studentModel->getStudentIdBasedOnIntakeDate($fieldValue);
                    }
                    $arrData = $this->filterAndCollapseData($arrData, $filteredStudIds, 'id');
                } elseif ($fieldName == 'course_id') {
                    $dataArr = [];
                    $dataArr[] = $this->findWhere($arrData, [$fieldName => $fieldValue], 'whereIn');
                    $arrData = Arr::collapse($dataArr);
                }
            }
        }

        return $arrData;
    }

    private function applySearchFilter($arrData, $searchVal, $columnsArr)
    {
        $searchText = trim($searchVal);
        if (! empty($searchText)) {
            $dataArr = [];
            foreach ($columnsArr as $column) {
                $dataArr[] = $this->findWhere($arrData, [$column => $searchText], 'like');
            }
            $arrData = Arr::collapse($dataArr);
        }

        return $arrData;
    }

    private function filterAndCollapseData($arrData, $fieldValue, $fieldName = '', $operator = '')
    {
        if (isset($fieldValue) && Arr::accessible($fieldValue)) {
            $dataArr = [];
            foreach ($fieldValue as $value) {
                if (! empty($fieldName)) {
                    $dataArr[] = $this->findWhere($arrData, [$fieldName => trim($value)], $operator);
                } else {
                    $dataArr[] = $this->findWhere($arrData, [$value['field'] => trim($value['value'])], $operator);
                }
            }
            $arrData = Arr::collapse($dataArr);
        }

        return $arrData;
    }

    private function findWhere($array, $matching, $operator = 'where')
    {
        $dataReturn = [];
        foreach ($array as $item) {
            $is_match = true;
            foreach ($matching as $key => $value) {
                /* $pattern = (is_object($item)) ? $item->$key : $item[$key]; */
                $pattern = (is_object($item)) ? (isset($item->$key) ? $item->$key : '') : (isset($item[$key]) ? $item[$key] : '');
                if (! isset($pattern)) {
                    $is_match = false;
                    break;
                }

                if ($operator == 'like') {
                    if (! $this->likeMatch("%$value%", $pattern)) {
                        $is_match = false;
                        break;
                    }
                } elseif ($operator == 'whereIn') {
                    $commonValues = array_intersect($pattern, $value);
                    if (empty($commonValues)) {
                        $is_match = false;
                        break;
                    }
                } else {
                    if ($pattern != $value) {
                        $is_match = false;
                        break;
                    }
                }
            }

            if ($is_match) {
                $dataReturn[] = $item;
            }
        }

        return $dataReturn;
    }

    private function likeMatch($pattern, $subject)
    {
        $pattern = str_replace('%', '.*', preg_quote($pattern, '/'));

        return (bool) preg_match("/^{$pattern}$/i", $subject);
    }

    public function getAllStudentListForDropdown(Request $request)
    {
        $data = $this->studentModel->getAllStudentListForDropdownWithSearch($request);
        foreach ($data as $key => $student) {
            $data[$key]['name'] = trim($student['name']);
            // $data[$key]['profile_pic'] = $this->getStudentProfilePicPath($student['id'], $student['profile_pic'], 'small');
            $data[$key]['profile_pic'] = '';
        }
        $data = $this->convertToUTF_1($data);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getStudentCourseList(Request $request)
    {
        if ($request->studIds) {
            $studIds = $request->studIds;
            $data = StudentCourses::from('rto_student_courses as t1')
                ->Join('rto_courses as t2', 't2.id', '=', 't1.course_id')
                ->whereIn('t1.student_id', $studIds)
                ->select('t2.id as value', DB::raw("CONCAT(t2.course_code,' - ',t2.course_name) as text"))
                ->groupBy('t2.id')
                ->get()
                ->toArray();
        } else {
            $whereArr = [
                'college_id' => $request->user()->college_id,
            ];
            $data = $this->courses->Where($whereArr)->select(DB::raw("CONCAT(course_code,' - ',course_name) as course_list"))->orderBy('course_list', 'ASC')->get()->toarray();
        }

        return $this->successResponse('data found successfully', 'data', $data);
    }

    public function findStudentIdWithCourse(Request $request)
    {
        if ($request->has('studIds')) {
            $studIds = array_map(function ($item) {
                return $item['id'];
            }, $request->studIds);
            // $studIds = $request->studIds;

            $data = StudentCourses::from('rto_student_courses as t1')->join('rto_courses as t2', 't2.id', '=', 't1.course_id')->whereIn('t1.student_id', $studIds)
                ->select('t2.id as value', DB::raw("CONCAT(t2.course_code,' - ',t2.course_name) as text"))
                ->groupBy('t2.id')
                ->first()
                ->toArray();

            $courseId = isset($request->course_id) ? $request->course_id : $data['value'];
            $totalStudentsCollection = collect($request->studIds);
            $withoutCourseStudentIds = [];
            $withCourseStudentIds = [];
            $withCourseStudentIds = StudentCourses::whereIn('student_id', $studIds)->where('course_id', $courseId)->get(['student_id', 'course_id'])->map(function ($studentCourse) {
                return [
                    'id' => $studentCourse->student_id,
                ];
            });
            $withoutCourseStudentIds = $totalStudentsCollection->reject(function ($student) use ($withCourseStudentIds) {
                return $withCourseStudentIds->contains('id', $student['id']);
            })->values();

            return $this->successResponse('Data found successfully', 'data', ['withoutCourseStudentIds' => $withoutCourseStudentIds->toArray()]);

        } else {
            return $this->successResponse('Data found successfully', 'data', ['withoutCourseStudentIds' => []]);
        }
    }

    public function findStudentIdWithCourseV2(Request $request)
    {
        $withoutCourseStudentIds = [];

        if ($request->has('studIds')) {
            // $studIds = array_map(function($item) { return $item['id']; }, $request->studIds);
            $studIds = collect($request->studIds)->pluck('id');

            $data = StudentCourses::from('rto_student_courses as t1')
                ->join('rto_courses as t2', 't2.id', '=', 't1.course_id')
                ->whereIn('t1.student_id', $studIds)
                ->select('t2.id as value', DB::raw("CONCAT(t2.course_code,' - ',t2.course_name) as text"))
                ->groupBy('t2.id')
                ->first();

            $courseId = $request->course_id ?? $data->value ?? null;

            if (! $courseId) {
                return $this->successResponse('No course found', 'data', ['withoutCourseStudentIds' => []]);
            }

            $withCourseStudentIds = StudentCourses::where('course_id', $courseId)->whereIn('student_id', $studIds)->pluck('student_id');
            $withCourseStudentIds[] = 0;
            $withoutCourseStudentIds = collect($request->studIds)->whereNotIn('id', $withCourseStudentIds)->values()->toArray();
        }

        return $this->successResponse('Data found successfully', 'data', ['withoutCourseStudentIds' => $withoutCourseStudentIds]);
    }

    public function getStudentFullName(Request $request)
    {
        $whereArr = [
            'is_student' => 1,
            'college_id' => $request->user()->college_id,
        ];
        $columnArr = [
            DB::raw("TRIM(CONCAT(first_name,' ',family_name)) as student_name"),
        ];
        $res = $this->studentModel->Where($whereArr)->select($columnArr)->orderBy('student_name', 'ASC')->get()->toarray();
        $data = array_column($res, 'student_name');

        return $this->successResponse('data found successfully', 'data', $data);
    }

    public function getAgentStudentData(Request $request)
    {
        $dataArr = $this->studentModel->getAgentStudentData($request);
        foreach ($dataArr as $key => $student) {
            $dataArr[$key]['profile_pic'] = $this->getStudentProfilePicPath($student['id'], $student['profile_picture'], 'small');
        }

        $data['data'] = $this->convertToISO8859_1($dataArr);
        $data['total'] = $this->studentModel->getAgentStudentData($request, true);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function exportAgentStudentData(Request $request)
    {
        $collegeId = Auth::user()->college_id;
        $agentId = $request->agent_id;

        if (empty($agentId)) {
            return $this->errorResponse('Agent data not found.', 'data', [], 200);
        } else {
            $agent = Agent::find($agentId);
            if (! $agent) {
                return $this->errorResponse('Agent data not found.', 'data', [], 200);
            }
            $studentData = $this->studentModel->getAgentStudents($collegeId, $agentId);
            if ($studentData->count() == 0) {
                return $this->errorResponse('Student data not found for this Agent', 'data', [], 200);
            }
        }

        $dataArr = ['college_id' => $collegeId, 'agent_id' => $agentId];

        if (isset($request->stud_course_id) && count($request->stud_course_id) > 0) {
            $dataArr['stud_course_ids'] = $request->stud_course_id;
        }

        $fileName = 'Student List.xlsx';
        $filePath = Config::get('constants.uploadFilePath.TempFiles');
        $destinationPath = Helpers::changeRootPath($filePath, null, $collegeId);
        File::makeDirectory($destinationPath['default'], 0777, true, true);

        // return Excel::download(new AgentStudentExport($dataArr), $fileName);
        Excel::store(new AgentStudentExport($dataArr), $destinationPath['view'].'/'.$fileName, 'uploads', \Maatwebsite\Excel\Excel::XLSX);

        $data = [
            'file_name' => $fileName,
            'export_url' => base64_encode(url($destinationPath['view'].$fileName)),
        ];

        return $this->successResponse('File Export successfully', 'data', $data);
    }

    public function getSidebarFilter(Request $request)
    {
        /* Single Old filter */
        if ($request->input('id')) {
            $data = $this->studentModel->getSidebarResultNew($request);

            return $this->successResponse('Data found successfully', 'data', $data);
        }

        /* Multiple ids filter */
        if ($request->input('ids') && is_array($request->input('ids'))) {
            foreach ($request->input('ids') as $id) {

                $data[$id] = $this->studentModel->getSidebarResultNew($request->merge(['id' => $id]));
            }

            return $this->successResponse('Data found successfully', 'data', $data);
        }
        $data = $this->getStudentFilterCategory();

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getCoursesListForFilter(Request $request)
    {
        $collegeId = Auth::user()->college_id;
        $query = Courses::where('college_id', $collegeId);

        if ($request->activeCourse == 1) {
            $query->where('activated_now', Courses::ACTIVITED_YES);
        } else {
            $query->whereIn('activated_now', [Courses::ACTIVITED_YES, Courses::ACTIVITED_NO]);
        }

        $data = $query->select(['activated_now', 'id as value', DB::raw("CONCAT(course_code,' - ',course_name) as label")])
            ->orderBy('activated_now', 'ASC')
            ->orderBy('course_code', 'ASC')
            // ->orderBy('value', 'ASC')
            ->get()
            ->toArray();

        $resultData = array_map(function ($item) {
            $item['label'] = ($item['activated_now'] == 0) ? "<span class='bg-gray-200 text-gray-800 py-1 px-2 rounded text-xs mr-1'> Inactive</span>".$item['label'] : $item['label'];
            $item['encoded'] = false;

            return $item;
        }, $data);

        return $this->successResponse('Data found successfully', 'data', $resultData);

    }

    public function filterAgentStudents(Request $request)
    {
        if ($request->input('id')) {
            $data = $this->studentModel->getSidebarResult($request);

            return $this->successResponse('Data found successfully', 'data', $data);
        }
        $data = $this->getAgentStudentFilterCategory();

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getIntakesByFilter(Request $request)
    {
        $data = $this->studentModel->getIntakesByFilter($request);
        if ($data && count($data) > 0) {
            return $this->successResponse('Data found successfully', 'data', $data);
        } else {
            return $this->errorResponse('Course not found.', 'data', [], 200);
        }
    }

    private function getStudentFilterCategory()
    {
        return [
            ['id' => 0, 'hasChild' => false, 'text' => 'Select Campus'],
            ['id' => 1, 'hasChild' => true, 'text' => 'Student Type', 'expanded' => true],
            ['id' => 2, 'hasChild' => true, 'text' => 'Course by Intake', 'expanded' => false],
            ['id' => 3, 'hasChild' => true, 'text' => 'Nationality', 'expanded' => false],
            ['id' => 4, 'hasChild' => true, 'text' => 'Status', 'expanded' => false],
            ['id' => 5, 'hasChild' => true, 'text' => 'Batch', 'expanded' => false],
            ['id' => 6, 'hasChild' => true, 'text' => 'Teacher', 'expanded' => false],
        ];
    }

    private function getAgentStudentFilterCategory()
    {
        return [
            ['id' => 4, 'hasChild' => true, 'text' => 'Status', 'expanded' => false],
        ];
    }

    /* student-profile */

    public function uploadStudentProfilePic(Request $request)
    {
        $collegeId = $request->post('college_id');
        $studentId = $request->post('student_id');
        $req = $request->input('metadata');
        $uploadData = json_decode($req, true);
        $originalFileName = $uploadData['fileName'];

        $studentImage = $request->file();

        if (isset($studentImage['files'])) {
            $file = $studentImage['files'];
            $filePath = Config::get('constants.uploadFilePath.StudentPics');
            $destinationPath = $this->changeRootPath($filePath, $collegeId, $studentId);
            // $college_name = str_replace(" ", "_", $request->input('college_name'));
            $stud_pic_name = str_replace(' ', '_', $originalFileName);
            $filename = 'student'.'-'.$studentId.'-'.$stud_pic_name;

            $res = $file->move($destinationPath['default'], $filename);
            if ($res && ! empty($studentId)) {
                $this->student->update(['profile_picture' => $filename], $studentId);
                echo json_encode(['uploaded' => true, 'fileUid' => $filename, 'status' => 'success', 'message' => 'Student image upload successfully.']);
                exit;
            }
        }
        echo json_encode(['status' => 'error', 'message' => 'Something will be wrong. Please try again.']);
        exit;
    }

    public function removeStudentProfilePic(Request $request)
    {
        $collegeId = $request->post('college_id');
        $studentId = $request->post('student_id');
        if (! empty($studentId)) {
            $filePath = Config::get('constants.uploadFilePath.StudentPics');
            $destinationPath = $this->changeRootPath($filePath, $collegeId, $studentId);
            UploadService::delete($destinationPath['default']);
            // $dirPath = public_path() . $destinationPath['view'];
            // if (is_dir($dirPath)) {
            //     $this->delete_directory($dirPath);
            // }
        }
        $result = ['status' => 'success', 'message' => 'Student image remove successfully.'];
        echo json_encode($result);
        exit;
    }

    public function delete_directory($dirname)
    {
        if (is_dir($dirname)) {
            $dir_handle = opendir($dirname);
        }
        if (! $dir_handle) {
            return false;
        }
        while ($file = readdir($dir_handle)) {
            if ($file != '.' && $file != '..') {
                if (! is_dir($dirname.'/'.$file)) {
                    unlink($dirname.'/'.$file);
                } else {
                    $this->delete_directory($dirname.'/'.$file);
                }
            }
        }
        closedir($dir_handle);
        rmdir($dirname);

        return true;
    }

    public function getVisaStatusData(Request $request)
    {

        $data = $this->studentModel->getVisaStatusData($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getCurrentCourseSummary(Request $request)
    {
        $data = $this->studentSubjectEnrolment->getCurrentCourseSummary($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getStudentDetail(Request $request)
    {
        $studentId = $request->student_id;
        $data = $this->student->studentDetails($studentId);
        $data['profile_pic'] = $this->getStudentProfilePicPath($data['id'], $data['profile_picture']);
        $data['country'] = $this->studentModel->getCountryData($request->college_id);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function updateStudentDetails(Request $request)
    {
        $rules = [
            'email' => 'email|unique:rto_students,email,'.$request->input('student_id'),
            'optional_email' => 'nullable|email|unique:rto_students,optional_email,'.$request->input('student_id'),
            'USI' => [
                'sometimes', // Only validate if present in the request
                'required',
                'unique:rto_students,USI,'.$request->input('student_id'),
            ],
            'mobile' => ['regex:/^\d{1,10}$/'],
        ];
        // Custom error messages
        $messages = [
            'email.email' => 'Please provide a valid email address.',
            'email.unique' => 'The email has already been taken.',
            'USI.required' => 'The USI field is required.',
            'USI.unique' => 'The USI has already been taken.',
            'optional_email.email' => 'Please provide a valid optional email address.',
            'optional_email.unique' => 'The optional email has already been taken.',
            'mobile.required' => 'Mobile number is required.',
            'mobile.regex' => 'Please provide a valid phone number.',
        ];
        // Perform validation
        $validator = Validator::make($request->all(), $rules, $messages);

        if ($validator->fails()) {
            return ['status' => 'error', 'message' => $validator->errors()->first()];
        }
        // Check if validation fails
        $formData = $request->input();
        $returnData = $request->input();
        if (isset($formData['passport_expiry']) && ($formData['passport_expiry'])) {
            $formData['passport_expiry'] = date('Y-m-d', strtotime($formData['passport_expiry']));
        }
        if (isset($formData['visa_expiry_date']) && ($formData['visa_expiry_date'])) {
            $formData['visa_expiry_date'] = date('Y-m-d', strtotime($formData['visa_expiry_date']));
        }
        if (isset($request->mobile)) {
            $formData['current_mobile_phone'] = $request->mobile;
        }
        if (isset($request->email)) {
            $formData['current_email_phone'] = $request->mobile;
        }
        $objUser = Users::firstWhere('username', $request->generated_stud_id);
        if ($objUser && ($objUser->name !== ($request->first_name.' '.$request->family_name))) {
            $objUser->name = "{$request->first_name} {$request->family_name}";
            $objUser->save();
        }
        $formData['isSingleName'] = isset($formData['isSingleName']) ? $formData['isSingleName'] : '';
        $formData['last_address_updated_date'] = date('Y-m-d');
        if (isset($formData['DOB'])) {
            $formData['DOB'] = date('Y-m-d', strtotime($formData['DOB']));
        }
        if (isset($formData['USI'])) {
            $data = [
                'recordId' => 1,
                'USI' => $formData['USI'],
                'firstName' => ($formData['isSingleName'] == 'yes') ? '' : $formData['first_name'],
                'lastName' => ($formData['isSingleName'] == 'yes') ? '' : $formData['family_name'],
                'singleName' => ($formData['isSingleName'] == 'yes') ? $formData['first_name'] : '',
                'isSingleName' => ($formData['isSingleName'] == 'yes') ? true : false,
                'dateOfBirth' => date('Y-m-d', strtotime($formData['dateOfBirth'])),
            ];
            $result = $this->usiService->checkStudentUSI($data);
            if (isset($result->isVerified)) {
                $IsValid = $result->isVerified ? 1 : 0;
                $formData['usi_invalid_reason'] = $returnData['usi_invalid_reason'] = $result->getInvalidReason() ?? null;
                $formData['is_usi_verified'] = $returnData['is_usi_verified'] = $IsValid;
                if (! $result->isVerified) {
                    return $this->errorResponse('The USI format is not  correct.', 'data', $returnData, 200);
                }
                unset($formData['first_name']);
                unset($formData['family_name']);
                unset($formData['dateOfBirth']);
            } else {
                $result = (object) [
                    'FirstName' => '-',
                    'FamilyName' => '-',
                    'DateOfBirth' => '-',
                    'Result' => 'The USI format is Invalid.',
                    'usi_invalid_reason' => 'USI is Invalid',
                    'is_usi_verified' => 0,
                ];

                return $this->errorResponse('USI is Invalid', 'data', $result, 200);
            }
        }
        DB::beginTransaction();
        try {
            $this->student->update($formData, $formData['student_id']);
            DB::commit();

            return $this->successResponse('Data saved successfully', 'data', $returnData);
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApplicationException($e->getMessage());
        }
    }

    public function updateAdditionalDetails(Request $request)
    { // TODO::NOT USED
        $studentId = $request->student_id;
        $formData = $request->input();
        DB::beginTransaction();
        try {
            $this->studentDetails->whereUpdate($formData, ['student_id' => $studentId]);
            DB::commit();
            $arrSurveyContactStatusDiv = Config::get('constants.arrSurveyContactStatusDiv');
            $formData['scs_value'] = '';
            if (isset($formData['scs'])) {
                $formData['scs_value'] = $arrSurveyContactStatusDiv[$formData['scs']];
            }
            $formData['surveyContactData']['data'] = $this->convertConstantsFormat(Config::get('constants.arrSurveyContactStatusDiv'));

            return $this->successResponse('Data saved successfully', 'data', $formData);
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApplicationException($e->getMessage());
        }

    }

    public function getStudentProfileCourses(Request $request)
    {
        $studentCourseId = $request->student_course_id;
        $data = $this->studentCourse->profileCourseDetails($studentCourseId);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getStudentCourseDropdown(Request $request)
    {
        $studentId = 17;
        $collegeId = Auth::user()->college_id;
        $studentCourseList = $this->studentCourse->getCourseList($studentId, $collegeId);

        return $this->successResponse('data found successfully', 'data', $studentCourseList);
    }

    public function getStudentCourseDetail(Request $request)
    {
        $studentId = $request->student_id;
        $selectedStudCourseId = $request->student_course_id;
        $collegeId = Auth::user()->college_id;

        $studentCourseList = Student::from('rto_students as rs')
            ->leftjoin('rto_student_courses as rsc', 'rsc.student_id', '=', 'rs.id')
            ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->where(['rs.college_id' => $collegeId, 'rs.id' => $studentId, 'rsc.id' => $selectedStudCourseId])
            ->select('rs.id as studentId', 'rsc.id as selectedStudCourseID', DB::raw("CONCAT(rs.first_name,' ',rs.family_name) as student_name"), 'generated_stud_id', 'profile_picture', DB::raw("CONCAT(rc.course_code,' - ',rc.course_name) as course_name"), 'rsc.status', 'rsc.start_date', 'rsc.finish_date', 'rsc.total_weeks', 'rsc.course_duration_type')
            ->get()
            ->toArray();

        foreach ($studentCourseList as $key => $value) {
            $bgWithTextColor = $this->getCourseStatusColor($value['status']);

            $studentCourseList[$key]['profile_picture'] = $this->getStudentProfilePicPath($value['studentId'], $value['profile_picture']);
            $studentCourseList[$key]['bgcolor'] = $bgWithTextColor;
            $studentCourseList[$key]['textcolor'] = $bgWithTextColor;
            $studentCourseList[$key]['statusHistoryData'] = $this->studentProfileCommonService->getStatusHistoryData($collegeId, $selectedStudCourseId);
            $studentCourseList[$key]['isValidForExtendStartDate'] = $this->studentProfileCommonService->canExtendCourseStartDate($value['status']);
        }

        $studentCourseList[0]['isXeroConnect'] = (Xero::isConnected()) ? 1 : 0;

        return $this->successResponse('data found successfully', 'data', $studentCourseList);
    }

    public function getStudentDocumentData(Request $request)
    {
        $data = $this->student->getStudentDocumentData($request);

        return $data;
        // return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getStudentBreadcrumb(Request $request)
    {
        $data = $this->student->getStudentBreadcrumbData($request);

        return $data;
        // return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function uploadStudentDocuments($uid, $cid, $sid, Request $request)
    {
        $data = $this->student->uploadStudentDocuments($uid, $cid, $sid, $request);

        return response()->json($data);
    }

    public function addStudentDocumentDirectory(Request $request)
    {
        $data = $this->student->addStudentDocumentDirectory($request);

        if ($data) {
            return $this->successResponse('Directory create successfully.', 'data', $data);
        } else {
            return $this->errorResponse('Something will be wrong. Please try again.', 'data');
        }
    }

    public function updateStudentDocumentDirectory(Request $request)
    {
        return $this->student->updateStudentDocumentDirectory($request);

    }

    public function removeStudentDocumentDirectory(Request $request)
    {
        return $this->student->removeStudentDocumentDirectory($request);
    }

    public function studentResultData(Request $request)
    {
        $isMoodleConnect = $this->isMoodleConnected();
        $result = $this->studentSubjectEnrolment->studentResultData($request, $isMoodleConnect);
        $data = [
            'data' => $result['data'],
            'total' => $result['total'],
            'isMoodleConnect' => $isMoodleConnect,
        ];

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function studentResultDataDelete(Request $request)
    {
        $updatedData = $request->input('models');
        $decodedData = json_decode($updatedData, true);
        // $data = $this->studentSubjectEnrolment->delete($decodedData[0]['id']);
        // return $this->successResponse('Data Deleted successfully', 'data', $data);
        $studentSubjectEnrollId = $decodedData[0]['id'];
        try {
            $data = $this->isMoodleConnected()
                ? $this->unEnrollStudentFromMoodle($studentSubjectEnrollId)
                : $this->deleteUnitEnrollData($studentSubjectEnrollId);

            return $this->successResponse('Data Deleted successfully', 'data', $data);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 'data', [], 200);
        }
    }

    public function getSubjectsForEnroll(Request $request)
    {
        $data = $this->studentCourse->getSubjectsForEnroll($request->input());
        echo json_encode($data);
        exit;
    }

    public function getSubjectUnitForEnroll(Request $request)
    {
        $data['enrolledUnit'] = $this->studentCourse->getSubjectUnitForEnroll($request->input());
        if (empty($data)) {
            $data['enrolledUnit'] = [
                'value' => '',
                'groupName' => '',
                'unit_type' => 'No Data Found',
                'unit_code' => 'No Data Found',
                'unit_name' => 'No Data Found',
            ];

            return $this->successResponse('Data found successfully', 'data', $data);
        }

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function studentUnitData(Request $request)
    {
        $isMoodleConnect = $this->isMoodleConnected();
        $result = $this->studentSubjectEnrolment->studentUnitData($request, $isMoodleConnect);
        $data = [
            'data' => $result['data'],
            'total' => $result['total'],
            'isMoodleConnect' => $isMoodleConnect,
        ];

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function studentTimetableData(Request $request)
    {
        $request->course_id = $this->studentCourse->find($request->student_course_id)->course_id;
        $data = $this->studentSubjectEnrolment->studentTimetableData($request);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getStudentPaymentDetails(Request $request)
    {
        $studentId = $request->student_id;
        $studentCourseId = $request->student_course_id;
        $data = $this->studentCourse->getStudentPaymentDetails($studentId, $studentCourseId);

        return $this->successResponse('Data found successfully', 'data', $data);
    }
    // Not able to find any reference
    // public function getPaymentHistoryData(Request $request)
    // {
    //     $data['data'] = $this->studentInitialPaymentTransaction->getPaymentHistoryData($request);
    //     $data['total'] = $this->studentInitialPaymentTransaction->getPaymentHistoryData($request, true);
    //     return $this->successResponse('Data found successfully', 'data', $data);
    // }
    // Not able to find any reference
    // public function getPaymentScheduleData(Request $request)
    // {
    //     $data['data'] = $this->studentInitialPaymentDetails->getPaymentScheduleData($request);
    //     $data['total'] = $this->studentInitialPaymentDetails->getPaymentScheduleData($request, true);
    //     return $this->successResponse('Data found successfully', 'data', $data);
    // }

    public function getStudentActivityLog(Request $request)
    {
        $data = [];
        $dataArr = $this->communicationLog->getActivityLog($request);

        foreach ($dataArr as $row) {
            $data[] = [
                'type' => $row['type'],
                'status' => $row['status'],
                'date' => date('Y-m-dTH:i:s.000Z', strtotime($row['created_at'])),
                'date_time' => date('h:i A', strtotime($row['created_at'])),
                'description' => $row['log'],
                'user_name' => $row['user_name'],
            ];
        }
        echo json_encode($data);

        exit;
        // return $this->successResponse('Data found successfully','data', $data);
    }

    public function removeSpecialCharacter($fileName)
    {
        $fileName = str_replace(' ', '-', $fileName); // Replaces all spaces with hyphens.

        return preg_replace('/[^A-Za-z0-9.\-]/', '', $fileName); // Removes special chars.
    }

    public function verifyAndStoreImage(Request $request, $filename, $directory = 'upload')
    {

        if ($request->hasFile($filename)) {

            if (! $request->file($filename)->isValid()) {
                flash('Invalid image')->error()->important();

                return redirect()->back()->withInput();
            }

            return $request->file($filename)->store('image/'.$directory, 'public');
        }

        return null;
    }

    public function editActivityNote(Request $request)
    {
        $file = $request->file('note_attachment_edit');
        if (! empty($file)) {
            $filePath = Config::get('constants.uploadFilePath.StudentNotes');
            $destinationPath = Helpers::changeRootPath($filePath);
            $file = $request->file('note_attachment_edit');
            $time = time();
            $originalName = $file->getClientOriginalName();
            $filenm = hashFileName($originalName);
            $fileName = $this->removeSpecialCharacter($filenm);
            $upload_success = $file->move($destinationPath['default'], $fileName);
        }
        $note_attachment_path = '';
        $note_attachment = '';
        if (! empty($upload_success)) {
            $note_attachment_path = $destinationPath['view'];
            $note_attachment = $fileName;
        }
        $saveData = $this->communicationLog->update(['log' => $request->log, 'note_attachment_path' => $note_attachment_path, 'note_attachment' => $note_attachment, 'updated_by' => $request->user()->id], $request->id);

        return $this->successResponse('Note Updated Successfully', 'data', $saveData);
    }

    public function deleteActivityNoteFromTimeLine(Request $request)
    {
        $data = $this->communicationLog->delete($request->id);

        return $this->successResponse('Note Deleted Successfully', 'data', $data);
    }

    public function attendanceChartData(Request $request)
    {
        $collegeId = $request->college_id;
        $studentId = $request->student_id;
        $studentCourseId = $request->student_course_id;
        $courseId = $this->studentCourse->find($studentCourseId)->course_id;
        $res = $this->student->getStudentCourseAttendanceData($collegeId, $studentId, $courseId);
        echo json_encode($res);
        exit;
    }

    public function studentIssueLetterGenerate(Request $request)
    {
        return $this->generateLetterPdfTraitV2($request);
        // return $this->generateLetterPdfTrait($request);
    }

    public function verifyLetterWatermark(Request $request)
    {
        $collegeId = (! empty($request->college_id)) ? $request->college_id : Auth::user()->college_id;
        $watermarkFileName = $this->letterSetting->getWhereVal(['college_id' => $collegeId], 'watermark');
        if (! empty($watermarkFileName)) {
            $filePath = Config::get('constants.uploadFilePath.LetterSetting');
            $destinationPath = Helpers::changeRootPath($filePath, null, $collegeId);
            // $existingFilePath = $destinationPath['default'] . $watermarkFileName;
            // dd($destinationPath['view'] . $watermarkFileName);
            $existingFilePath = UploadService::exists($destinationPath['view'].$watermarkFileName);
            if ($existingFilePath) {
                return $this->successResponse('file exist', 'data', '');
            }
        }

        return $this->errorResponse('Watermark not found.', 'data', [], 200);
    }

    public function studentIssueLetterGenerateWithWatermark(Request $request)
    {
        return $this->generateLetterPdfTraitV2($request, true);
        // return $this->generateLetterWithWatermarkPdfTrait($request);
    }

    public function studentIssueLetterZip($subFolder)
    {
        $zip = new ZipArchive;
        $file_name = "$subFolder.zip";

        $filePath = Config::get('constants.uploadFilePath.StudentLetterSetting');
        $fileDestinationPath = Helpers::changeRootPath($filePath, $subFolder)['default'];
        $zipPath = Config::get('constants.uploadFilePath.StudentLetterZip');
        $zipDestinationPath = Helpers::changeRootPath($zipPath)['default'];
        File::makeDirectory($zipDestinationPath, 0777, true, true);

        if ($zip->open($zipDestinationPath.$file_name, ZipArchive::CREATE) == true) {
            $files = File::files($fileDestinationPath);
            if (count($files) == 1) {
                foreach ($files as $key => $value) {
                    $relativeName = basename($value);
                    $data = [
                        'file' => $file_name,
                        'download_path' => Helpers::changeRootPath($filePath)['view'].$relativeName,
                        'url' => 'api/download-file-and-delete',
                    ];

                    return $this->successResponse('Create zip file successfully', 'data', $data);
                }
            }
            if (count($files) > 0) {
                foreach ($files as $key => $value) {
                    $relativeName = basename($value);
                    $zip->addFile($value, $relativeName);
                    // unlink($value);
                }
                $zip->close();

                /*$fullPath = Helpers::changeRootPath($zipPath)['default'] . $file_name;
                // Set appropriate headers for download
                header('Content-Type: application/zip');
                header('Content-disposition: attachment; filename=example.zip');
                header('Content-Length: ' . filesize($fullPath));

                // Read the zip file and output it to the browser
                readfile($fullPath);

                // Optionally, you can delete the zip file after download
                unlink($fullPath);*/

                // rmdir($fileDestinationPath);

                // Delete files after the zip has been created
                foreach ($files as $file) {
                    unlink($file);
                }

                // Try to remove the directory if it is empty
                if (File::isDirectory($fileDestinationPath)) {
                    File::deleteDirectory($fileDestinationPath);
                }
            }
            $data = [
                'file' => $file_name,
                'download_path' => Helpers::changeRootPath($zipPath)['view'].$file_name,
                'url' => '',
            ];

            return $this->successResponse('Create zip file successfully', 'data', $data);
        } else {
            return $this->errorResponse('Failed to create zip file', 'data', '');
        }
        echo $file_name;
        // echo route('search-student');
    }

    public function downloadFileAndDelete(Request $request)
    {
        return response()->download(public_path($_GET['filePath']))->deleteFileAfterSend(true);

    }

    /* Anup 26-05-2023 */
    public function getCollageEnrolmentFees(Request $request)
    {

        $collegeId = Auth::user()->college_id;
        $courseId = $request->courseId;
        $enrolmentFee = $materialFee = $upfrontFee = $promotionPrice = 0;
        $intakeDateList = [];

        $whereArr = ['college_id' => $collegeId, 'course_id' => $courseId];

        $fees = $this->collegeEnrollmentFees->getWhere($whereArr, ['fee_amount']);
        $upFront = $this->coursesUpfrontFee->getWhere($whereArr, ['material_fee', 'upfront_fee']);

        if (! empty($fees)) {
            $enrolmentFee = $fees[0]['fee_amount'];
        }

        if (! empty($upFront)) {
            $upfrontFee = $upFront[0]['upfront_fee'];
            $materialFee = $upFront[0]['material_fee'];
        }

        $findCourse = $this->courses->where(['id' => $courseId])->get();

        if ($request->intakeYear) {
            $intakeDateList = $this->coursesIntakeDate->getWhere(['course_id' => $courseId, 'intake_year' => $request->intakeYear], ['intake_start as Id', 'intake_start as Name']);
            foreach ($intakeDateList as $key => $value) {
                $intakeDateList[$key]['Name'] = date('d-m-Y', strtotime($value['Name']));
            }
        }

        if ($request->startDate) {
            $start = date('Y-m-d', strtotime($request->startDate));
            /*$promotionWhereArr = [
                'college_id' => $collegeId,
                'course_id' => $courseId,
                'from_date >=' => $start,
                'to_date <=' => $start
            ];
            $promotion = $this->promotionPrice->getWhere($promotionWhereArr, ['price as special_price']);*/
            $promotion = $this->promotionPrice->where($whereArr)
                ->where('from_date', '>=', $start)
                ->where('to_date', '<=', $start)
                ->select(['price as special_price'])
                ->get()->toArray();

            if (! empty($promotion)) {
                $promotionPrice = $promotion[0]['special_price'];
            }
        }

        $collegeData = [
            'enrolmentFee' => $enrolmentFee,
            'upfrontFee' => $upfrontFee,
            'materialFee' => $materialFee,
            'intakeDateList' => $intakeDateList,
            'promotionPrice' => $promotionPrice,
            'course' => $findCourse,
        ];
        $findCourse[0]->isHigherEd = (new CourseType)->checkHigherEdGradingType($findCourse[0]->course_type_id);

        return $this->successResponse('Data found successfully', 'data', $collegeData);
    }

    public function getStudentServicesName(Request $request)
    {
        $arrServicesName = SetupServicesName::select('services_name as Name', 'id as Id')->where('college_id', $request->college_id)->get()->toArray();

        return $this->successResponse('Data found successfully', 'data', $arrServicesName);
    }

    public function getStudentSubjectEnrolmentHistory(Request $request)
    {
        $student_subject_enrolment_id = $request->student_subject_enrolment_id;
        $data = StudentSubjectEnrolmentHistory::from('rto_student_subject_enrolment_history as rsseh')
            ->join('rto_users as ru', 'ru.id', '=', 'rsseh.created_by')
            ->where('student_subject_enrolment_id', $student_subject_enrolment_id)
            ->select('ru.name as created_by_name', 'rsseh.*')
            ->get()
            ->toArray();

        if ($data) {
            foreach ($data as $key => $value) {

                $data[$key]['start_original_date'] = date('M d Y', strtotime($value['start_original_date']));
                $data[$key]['start_updated_date'] = date('M d Y', strtotime($value['start_updated_date']));
                $data[$key]['end_original_date'] = date('M d Y', strtotime($value['end_original_date']));
                $data[$key]['end_updated_date'] = date('M d Y', strtotime($value['end_updated_date']));
                $data[$key]['final_outcome_original'] = $value['final_outcome_original'];
                $data[$key]['final_outcome_updated'] = $value['final_outcome_updated'];
                $data[$key]['created_at_date'] = date('d M, Y', strtotime($value['created_at']));
                $data[$key]['created_at_time'] = date('H:i A', strtotime($value['created_at']));
            }

            return $this->successResponse('History Found', 'data', $data);
        } else {
            return $this->errorResponse('History not found.', 'data', [], 200);
        }
    }

    public function getTermList($collegeId, $semesterId)
    {

        $objRtoSemesterDivision = new SemesterDivision;
        $termList = $objRtoSemesterDivision->_getTermBySemester($semesterId, $collegeId);

        $result = [];
        if ($termList->count() > 0) {
            foreach ($termList as $term) {
                $result[$term->term] = $term->term;
            }
        } else {
            $result[''] = '- - No Trem Found - -';
        }

        return $result;
    }

    private function getSemesterList($courseId, $startDate)
    {

        $startYear = date('Y', strtotime($startDate));

        $objCourseCalender = new CourseCalendar;
        $calendarType = $objCourseCalender->_getCalenderData($courseId, $startYear);
        $courseTypeId = $objCourseCalender->getCourseTypeId($courseId);
        $objSemester = new Semester;
        $arrSemesterList = $objSemester->_getSemesterData($courseTypeId, $calendarType, $startYear);

        $semester = [];
        if ($arrSemesterList->count() > 0) {
            foreach ($arrSemesterList as $sem) {
                $semester[$sem->id] = $sem->semester_name;
            }
        } else {
            $semester[''] = '- - No Semester Found - -';
        }

        return $semester;
    }

    public function getEmployerNameList(Request $request)
    {
        $employerNameData = $this->employer->selectData(['id as Id', 'employer_name as Name']);

        return $this->successResponse('Data found successfully', 'data', $employerNameData);
    }

    public function getContractCode(Request $request)
    {

        $contractCodeData = $this->contractCode->selectData(['id as Id', 'contract_code as Name']);

        return $this->successResponse('Data found successfully', 'data', $contractCodeData);
    }

    public function getVenueList(Request $request)
    {
        $campusVenue = CampusVenue::select(['id as Id', DB::raw('CONCAT(rto_venue.venue_code,":",rto_venue.venue_name) AS Name')])->get()->toArray();

        return $this->successResponse('Data found successfully', 'data', $campusVenue);
    }

    public function getCourseSiteList(Request $request)
    {
        $data = $this->courseSite->getWhere(['college_id' => $request->college_id], ['id as Id', 'course_site_name as Name']);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getStudentAdditionalService(Request $request)
    {
        $resultArray = $this->studentAdditionalServiceRequest->where(['id' => $request->input('id')])->get()->toArray();

        return $this->successResponse('Data found successfully', 'data', $resultArray);
    }

    public function generateVatStudentEnrollSubjectPdf(Request $request)
    {
        if (class_exists(\Barryvdh\Debugbar\Facades\Debugbar::class) && config('app.debug')) {
            \Barryvdh\Debugbar\Facades\Debugbar::disable();
        }
        $d = $request->all();

        $dataValue = json_decode($d['data'], true)[0];

        $collegeId = Auth::user()->college_id;
        $data['collegeId'] = $collegeId;
        $studCourseId = $dataValue['studCourseId'];
        $isDownload = $dataValue['isDownload'];
        $data['type'] = $type = $dataValue['type'];
        $postData['issueDate'] = date('Y-m-d', strtotime($dataValue['issued_date']));
        $postData['completionDate'] = date('Y-m-d', strtotime($dataValue['completion_date']));
        $postData['startDate'] = date('Y-m-d', strtotime($dataValue['issued_date']));
        $postData['start_date'] = date('Y-m-d', strtotime($dataValue['issued_date']));
        $postData['finish_date'] = date('Y-m-d', strtotime($dataValue['completion_date']));
        $postData['transcriptType'] = $dataValue['type'];
        $postData['isCompleted'] = $dataValue['isCompleted'];
        $postData['convert_course_status'] = $dataValue['convert_course_status'];
        $postData['include_failed_subject'] = $dataValue['include_failed_subject'];
        $postData['subject_enrollment_id'] = $dataValue['subject_enrollment_id'] ?? '';
        $postData['downloadType'] = 'S';
        $postData['student_course_id'] = $studCourseId ?? '';

        $objStudentCourse = new StudentCourses;
        $studentCourseInfo = $objStudentCourse->getStudentCourseInfo($collegeId, $studCourseId);

        $postData['courseId'] = $studentCourseInfo[0]['courseId'];

        if ($type == 'C' && $postData['convert_course_status']) {
            $objStudentCourse->completeStudentCourseInfo($studCourseId);
        }

        $dataArr = [];
        if ($studentCourseInfo->count() > 0) {
            $studentCourseInfo = $studentCourseInfo->toArray();

            $studentId = $studentCourseInfo[0]['studentId'];
            $courseId = $studentCourseInfo[0]['courseId'];
            $objStudentSubjectEnrolment = new StudentSubjectEnrolment;
            $studentCourseInfo[0]['enrollList'] = $objStudentSubjectEnrolment->getStudentCourseListForPdf($studentId, $courseId, $postData);
            $dataArr = $studentCourseInfo[0];
        }
        $objStudentCertificateRegister = new StudentCertificateRegister;

        $data['studentCourseInfo'][0] = $dataArr;
        $data['studentCourseInfo'][0]['certificateType'] = $objStudentCertificateRegister->previewCertificateRegisterFromPdf($collegeId, $postData, $studentId);

        $data['studentCourseInfo'][0]['issueDate'] = $postData['issueDate'];
        $data['studentCourseInfo'][0]['completionDate'] = $postData['completionDate'];

        $objRtoCollegeDetails = new CollegeDetails;
        $objCollegeDetails = $objRtoCollegeDetails->getCollegeDetails($collegeId)[0];
        $data['objCollegeDetails'] = $objCollegeDetails;
        $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
        $destinationPath = Helpers::changeRootPath($filePath);

        if ($objCollegeDetails->college_signature) {
            // $data['college_signature'] = str_replace("\\", "/", $destinationPath['view']) . $objCollegeDetails->college_signature;
            $data['college_signature'] = UploadService::url($destinationPath['view'].$objCollegeDetails->college_signature);
        } else {
            $data['college_signature'] = '';
        }
        if ($objCollegeDetails->college_logo) {
            // $data['college_logo'] = str_replace("\\", "/", $destinationPath['view']) . $objCollegeDetails->college_logo;
            $data['college_logo'] = UploadService::url($destinationPath['view'].$objCollegeDetails->college_logo);

        } else {
            $data['college_logo'] = '';
        }

        if ($objCollegeDetails->dean_signature) {
            // $data['dean_signature'] = str_replace("\\", "/", $destinationPath['view']) . $objCollegeDetails->dean_signature;
            $data['dean_signature'] = UploadService::url($destinationPath['view'].$objCollegeDetails->dean_signature);

        } else {
            $data['dean_signature'] = '';
        }

        $data['semWiseChunkDatas'] = collect($studentCourseInfo[0]['enrollList'])->groupBy('semester_name')->map(function ($items, $semester) {
            return [
                'semester_name' => $semester,
                'data' => $items,
            ];
        })->values()->chunk(3)
            ->map(function ($chunk) {
                return [
                    'total_count' => $chunk->sum(fn ($item) => count($item['data'])), // Get total count for this chunk
                    'data' => $chunk->toArray(),
                ];
            })
            ->toArray();

        $whereArr = [
            'college_id' => $request->user()->college_id,
        ];
        $data['gradeDatas'] = $this->resultGrade->Where($whereArr)->select('*')->get()->toArray();

        $data['gradePointData'] = collect($data['gradeDatas'])->pluck('grade_point', 'id')->toArray();
        $data['gradeData'] = collect($data['gradeDatas'])->pluck('grade', 'id')->toArray();

        $data['letterSettingData'] = LetterSetting::find(1);

        $filePath1 = Config::get('constants.uploadFilePath.LetterSetting');
        $destinationLetterPath = Helpers::changeRootPath($filePath1);
        // $data['watermarkPath'] = $destinationLetterPath['view'];
        $data['watermarkPath'] = UploadService::url($destinationLetterPath['view'].$data['letterSettingData']->watermark);
        $data['isDownload'] = $isDownload;
        // dd($data['college_signature']);
        $viewMap = [
            'C' => 'v2.sadmin.student.bulk_certificate.certificate-pdf-multiple',
            'SOA' => 'v2.sadmin.student.bulk_certificate.statement-attainment-pdf-multilpe',
            'TOCA' => 'v2.sadmin.student.bulk_certificate.cometencies-achieved-pdf-multiple',
            'TOGA' => 'v2.sadmin.student.bulk_certificate.grades-achieved-pdf-multiple',
            'UTOGA' => 'v2.sadmin.student.bulk_certificate.grades-achieved-pdf-multiple',
            'CL' => 'v2.sadmin.student.bulk_certificate.completion-letter-pdf-multiple',
        ];

        if ($type === 'CL') {
            $data['user_name'] = Auth::user()->name;
        }
        $view = $viewMap[$type] ?? null;
        if (! $view) {
            throw new \Exception('Invalid certificate type.');
        }

        $pdf = App::make('dompdf.wrapper');

        if (in_array($isDownload, [1, 2, 3])) {
            $typeArr = Config::get('constants.generateCertificateList');
            $filePath = Config::get('constants.uploadFilePath.StudentCertificate');
            $pdf->loadView($view, $data);

            // $destinationPath = Helpers::changeRootPath($filePath);
            $destinationPath = $this->changeRootPath($filePath, $collegeId);
            $fileName = str_replace(' ', '_', $typeArr[$type]).'_'.time();

            $pdfContent = $pdf->output();
            $uploadPath = rtrim($destinationPath['view'], '/').'/'.$fileName.'.pdf';
            // $upload_success = UploadService::uploadAs($uploadPath, $pdfContent,$fileName);
            // info('file uploaded form download', [$upload_success]);
            //            $pdf->save($destinationPath['default'].$fileName.'.pdf');
            // Save PDF S3
            $tmpPath = tempnam(sys_get_temp_dir(), 'pdf_');
            file_put_contents($tmpPath, $pdfContent);
            $upload_success = UploadService::uploadAs($destinationPath['view'], new \Illuminate\Http\File($tmpPath), $fileName.'.pdf');

            @unlink($tmpPath);
            info('file uploaded form generateVatStudentEnrollSubjectPdf', [$upload_success]);

            $objStudentCertificateRegister->saveCertificateRegisterFromPdf($collegeId, $postData, $studentId, $fileName);

            // $pdf->output();

            // $canvas = $pdf->getDomPDF()->get_canvas();
            // $pageTextX = $type === 'SOA' ? 530 : 280;
            // $canvas->page_text($pageTextX, 815, 'Page {PAGE_NUM} of {PAGE_COUNT}', null, 6, [0, 0, 0]);

            return $pdf->download($fileName.'.pdf');
        } else {
            return $pdf->loadView($view, $data)->stream();
            // return view($view, $data);
        }
    }

    public function checkCertificateIsDownload(Request $request)
    {
        $typeArr = Config::get('constants.generateCertificateListType');
        $collegeId = $request->user()->college_id;
        $studCourseId = $request->studCourseId;
        $isDownload = $request->isDownload;
        $studentCourseInfo = (new StudentCourses)->getStudentCourseInfo($collegeId, $studCourseId);
        $studentId = $studentCourseInfo[0]['studentId'];
        $courseId = $studentCourseInfo[0]['courseId'];
        $typeArrV2 = ['C' => '1', 'SOA' => '3', 'TOCA' => '5', 'TOGA' => '4', 'UTOGA' => '4', 'CL' => '2'];
        $isCertificateGenerated = StudentCertificateRegister::where(['college_id' => $collegeId, 'student_id' => $studentId, 'course_id' => $courseId, 'certificate_type' => $typeArrV2[$request->type]])->count();
        if ($isCertificateGenerated > 0) {
            return $this->errorResponse('Already Downloaded', 'data', '', 200);
        } else {
            StudentCourses::where(['student_id' => $studentId, 'course_id' => $courseId])->update(['is_certificate' => '1']);

            return $this->successResponse('Download successfully', 'data', '');

        }
    }

    public function checkCertificateRequirements(Request $request)
    {
        $isHigherEd = StudentCourses::checkCourseIsHigherEd($request->studCourseId);
        if ($isHigherEd) {
            return $this->successResponse('Student meets requirements for certificate generation', 'data', '');
        }
        $courseDetails = StudentCourses::with('course')->find($request->studCourseId);
        $totalCourseTemplate = $courseDetails->course_template ? CourseTemplate::find($courseDetails->course_template) : null;

        // Get core and elective unit counts from course template or course
        $data = [
            'core_unit' => $totalCourseTemplate->no_of_core_subject ?? $courseDetails->course->core_units_number ?? 0,
            'elective_unit' => $totalCourseTemplate->no_of_elective_subject ?? $courseDetails->course->elective_units_number ?? 0,
        ];

        // If both core and elective units are 0, get the count from CourseSubject table
        if ($data['core_unit'] == 0 && $data['elective_unit'] == 0) {
            $courseSubjectCount = CourseSubject::where('course_id', $courseDetails->course_id)
                ->where('is_active', 1)
                ->count();

            if ($courseSubjectCount > 0) {
                $data['core_unit'] = $courseSubjectCount;
                $data['elective_unit'] = 0;
            }
        }

        $totalCourseUnit = $data['core_unit'] + $data['elective_unit'];
        $totalCourseEnrollmentCount = StudentSubjectEnrolment::withStudentCourseId($request->studCourseId)->where('course_id', $courseDetails->course_id)->where('student_id', $courseDetails->student_id)->whereIn('final_outcome', ['C', 'CT', 'RPL'])->count();

        if ($totalCourseEnrollmentCount < $totalCourseUnit) {
            return $this->errorResponse('The student does not meet the requirements to generate the certificate.', 'data', $data, 200);
        } else {
            return $this->successResponse('Student meets requirements for certificate generation', 'data', '');
        }
    }

    public function checkMultipleStudentCertificateRequirements(Request $request)
    {
        $studentCourseIds = $request->studCourseId;
        $failedStudentCourseIds = [];
        foreach ($studentCourseIds as $studentCourseId) {
            $isHigherEd = StudentCourses::checkCourseIsHigherEd($studentCourseId);
            if ($isHigherEd) {
                continue;
            }
            $courseDetails = StudentCourses::with('course')->find($studentCourseId);
            $totalCourseTemplate = $courseDetails->course_template ? CourseTemplate::find($courseDetails->course_template) : null;
            $data = [
                'core_unit' => $totalCourseTemplate->no_of_core_subject ?? $courseDetails->course->core_units_number ?? 0,
                'elective_unit' => $totalCourseTemplate->no_of_elective_subject ?? $courseDetails->course->elective_units_number ?? 0,
            ];
            $totalCourseUnit = $data['core_unit'] + $data['elective_unit'];
            $totalCourseEnrollmentCount = StudentSubjectEnrolment::withStudentCourseId($studentCourseId)->where('course_id', $courseDetails->course_id)->where('student_id', $courseDetails->student_id)->whereIn('final_outcome', ['C', 'CT', 'RPL'])->count();

            if ($totalCourseEnrollmentCount < $totalCourseUnit) {
                $failedStudentCourseIds[] = $courseDetails->student->generated_stud_id;
            }
        }
        if (count($failedStudentCourseIds) > 0) {
            return $this->errorResponse('The following students do not meet the requirements to generate the certificate: '.implode(', ', $failedStudentCourseIds), 'data', $data, 200);
        }

        return $this->successResponse('Students meets requirements for certificate generation', 'data', '');

    }

    public function checkCourseUnitResult(Request $request)
    {
        $courseDetails = StudentCourses::with('course')->find($request->studCourseId);
        $totalCourseTemplate = $courseDetails->course_template ? CourseTemplate::find($courseDetails->course_template) : null;
        $data = [
            'core_unit' => $totalCourseTemplate->no_of_core_subject ?? $courseDetails->course->core_units_number ?? 0,
            'elective_unit' => $totalCourseTemplate->no_of_elective_subject ?? $courseDetails->course->elective_units_number ?? 0,
        ];
        $totalCourseUnit = $data['core_unit'] + $data['elective_unit'];
        $totalCourseEnrollmentCount = StudentSubjectEnrolment::where('student_course_id', $request->studCourseId)->whereIn('final_outcome', ['C', 'CT', 'RPL'])->count();

        if ($totalCourseEnrollmentCount < $totalCourseUnit) {
            return $this->errorResponse('Please enroll all units to generate certificate', 'data', $data, 200);
        } else {
            return $this->successResponse('Data found successfully', 'data', '');
        }
    }

    public function getViewSchedulePaymentDetails(Request $request)
    {
        $data['data'] = $this->studentCourse->getViewSchedulePaymentDetails($request);
        $data['total'] = $this->studentCourse->getViewSchedulePaymentDetails($request, true);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getPaymentModeName(Request $request)
    {
        $data = $this->paymentMode->selectData(['id as Id', 'name as Name']);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    /* Start fail job manage related to attendance module */
    public function failedAttendanceJobsData(Request $request)
    {
        $data['data'] = $this->studentModel->getFailAttendanceData($request);
        $data['total'] = $this->studentModel->getFailAttendanceData($request, true);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function failedAttendanceJobsRun(Request $request)
    {
        $studentId = $request->studentId;
        $batch = $request->batch;

        if ($studentId == 'all') {
            $result = DB::table('attendance_log_manage')->where('status', 'fail')->get(['student_id', 'batch'])->toArray();
            $dataArr = json_decode(json_encode($result), true);

            $count = 0;
            foreach ($dataArr as $row) {
                // event(new \App\Events\EnrollStudentAttendanceEvent($row['student_id'], [$row['batch']]));
                $res = $this->studentModel->manageEnrollStudentAttendance($row['student_id'], [$row['batch']]);
                if ($res) {
                    $count++;
                }
            }
            if ($count > 0) {
                return $this->successResponse("Total $count failed job manage successfully", 'data', '');
            } else {
                return $this->errorResponse('Nothing to pending event.', 'data', '', 200);
            }

        } elseif (! empty($studentId) && ! empty($batch)) {
            // event(new \App\Events\EnrollStudentAttendanceEvent($studentId, [$batch]));
            // return $this->successResponse('Event run successfully', 'data', '');
            $res = $this->studentModel->manageEnrollStudentAttendance($studentId, [$batch]);
            if ($res) {
                return $this->successResponse('Failed job manage successfully', 'data', '');
            } else {
                return $this->errorResponse('Some thing will be wrong. Please check log details.', 'data', '', 200);
            }
        } else {
            return $this->errorResponse('Some thing will be wrong. Please try again.', 'data', '', 200);
        }
    }
    /* End fail job manage related to attendance module */

    // TODO::GNG-2481
    public function verifyCourseTypeHigherEd(Request $request)
    {
        $studentCourseId = $request->student_course_id;
        $data = $this->studentProfileCommonService->verifyCourseTypeAndStatus($studentCourseId);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function studentInviteMailSend(Request $request)
    {
        if (is_array($request->student_id)) {
            // DB::beginTransaction();
            $existFilePath = Config::get('constants.uploadFilePath.Templates');

            $existDestinationPath = Helpers::changeRootPath($existFilePath, $request->template_id);

            $docList = EmailTemplateDocuments::where('email_template_id', $request->template_id)->get()->toArray();
            $existAttachment = [];

            foreach ($docList as $doc) {
                $existAttachment[] = $existDestinationPath['default'].$doc['file'];
            }
            $sendCount = 0;
            $notSendCount = 0;
            foreach ($request->student_id as $sid) {
                $userData = $this->createStudentToUser($sid);
                $mailData['student_name'] = $userData['name'];
                $mailData['contentData'] = $this->studentModel->setLetterBodyContent($request->student_id, '', $request->email_content);
                $student = Student::find($sid);
                $user = Users::whereEmail($userData['email'])->first();
                $token = Password::getRepository()->create($user);
                $mailData['subject'] = $request->subject;
                $mailData['actionUrl'] = route('password.reset', $token).'?email='.$userData['email'];
                $mailData['attachments'] = $existAttachment;
                $student->setMeta('invitation_code', strtoupper(Str::random(8)));
                $mailData['inviteCode'] = $student->getMeta('invitation_code');
                try {
                    Mail::to($userData['email'])->send(new sendStudentInviteMail($mailData));
                    DB::commit();
                    $sendCount++;
                    $sendStatus[] = $this->getSendDataArr($student, 'Success', 'Send Successfully');
                } catch (\Exception  $e) {
                    $notSendCount++;
                    $sendStatus[] = $this->getSendDataArr($student, 'Fail', $e->getMessage());
                }

            }
            $emailStatusData['success_msg'] = 'Email sent successfully for '.$sendCount.(($sendCount > 1) ? ' Student(s)' : ' Student');
            $emailStatusData['fail_msg'] = ($notSendCount == 0) ? '' : 'Email sent fail for '.$notSendCount.(($notSendCount > 1) ? ' Student(s)' : ' Student');
            $emailStatusData['statusData'] = $sendStatus;

            return $emailStatusData;
            // return $this->successResponse('Email sent successfully','data',[]);
        } else {
            dd('ss');
            DB::beginTransaction();

            $userData = $this->createStudentToUser($request->student_id);
            $mailData['student_name'] = $userData['name'];
            $mailData['contentData'] = $this->studentModel->setLetterBodyContent($request->student_id, '', $request->email_content);
            $mailData['subject'] = $request->subject;
            $user = Users::whereEmail($userData['email'])->first();
            $token = Password::getRepository()->create($user);
            $mailData['actionUrl'] = route('password.reset', $token).'?email='.$userData['email'];
            try {
                Mail::to($userData['email'])->send(new sendStudentInviteMail($mailData));
                DB::commit();

                return $this->successResponse('Email sent successfully', 'data', []);
            } catch (\Exception $e) {
                DB::rollBack();

                return $this->errorResponse('Failed to send email: '.$e->getMessage(), 'data', []);
            }

        }

    }

    private function getSendDataArr($studRow, $status, $description)
    {
        return [
            'id' => $studRow['id'],
            'name' => $studRow['first_name'].' '.$studRow['family_name'],
            'profile_pic' => $this->getStudentProfilePicPath($studRow['id'], $studRow['profile_picture'], 'small'),
            'status' => ($status) ? 'Success' : 'Fail',
            'description' => $description,
        ];
    }

    public function createStudentToUser($student_id)
    {
        $studentData = Student::find($student_id);
        $userData['college_id'] = Auth::user()->college_id;
        $userData['name'] = $studentData['first_name'].' '.$studentData['family_name'];
        $userData['username'] = $studentData['generated_stud_id'];
        $userData['role_id'] = 13;
        $userData['email'] = ($studentData['email'] != '') ? $studentData['email'] : null;
        $userData['phone'] = ($studentData['current_home_phone'] != '') ? $studentData['current_home_phone'] : null;
        $userData['mobile'] = ($studentData['current_mobile_phone'] != '') ? $studentData['current_mobile_phone'] : null;
        userModal::create($userData);

        return $userData;
    }

    public function verifyStudentUsiNumber(Request $request)
    {
        $validator = Validator::make($request->all(), [
            'USI' => 'required|string|min:10|max:10',
            'first_name' => 'required|string',
            'dateOfBirth' => 'required',
            'family_name' => $request->input('isSingleName') == 'no' ? 'required|string' : 'nullable|string',
        ], [
            'USI.min' => 'The USI must be at least :min characters.',
            'USI.max' => 'The USI must not be more than :max characters.',
        ]);

        // Check if validation fails
        if ($validator->fails()) {
            return $this->errorResponse($validator->errors()->first(), 'data', $request->all(), 200);
        }

        // Sample Data
        // $data = [
        //     "recordId" => 1,
        //     "USI" => "A93EVM49PV",
        //     "firstName" => "",
        //     "lastName" => "",
        //     "singleName" => "Harsh",
        //     "isSingleName" => true,
        //     "dateOfBirth" => "2001-08-04"
        // ];
        // $data = [
        //     "recordId" => 1,
        //     "USI" => "BP6LKB3C7X",
        //     "firstName" => "Maryam",
        //     "lastName" => "Fredrick",
        //     "singleName" => "",
        //     "isSingleName" => false,
        //     "dateOfBirth" => "1966-05-25"
        // ];

        $data = [
            'recordId' => 1,
            'USI' => $request->USI,
            'firstName' => ($request->isSingleName == 'yes') ? '' : $request->first_name,
            'lastName' => ($request->isSingleName == 'yes') ? '' : $request->family_name,
            'singleName' => ($request->isSingleName == 'yes') ? $request->first_name : '',
            'isSingleName' => ($request->isSingleName == 'yes') ? true : false,
            'dateOfBirth' => date('Y-m-d', strtotime($request->dateOfBirth)),
        ];
        $result = $this->usiService->checkStudentUSI($data);
        if (isset($result->isVerified)) {
            $IsValid = ($result->isVerified) ? 1 : 0;
            // converting to old response
            $oldResultResponse = [
                'Result' => $result->isVerified ? 'Valid' : 'Invalid',
                'usi_invalid_reason' => $result->isVerified ? null : $result->getInvalidReason(),
                'is_usi_verified' => $IsValid,
            ];
            if ($result->hasSingleName()) {
                $oldResultResponse['SingleName'] = $result->singleName ? 'Match' : 'No Match';
            } else {
                $oldResultResponse['FirstName'] = $result->firstName ? 'Match' : 'No Match';
                $oldResultResponse['FamilyName'] = $result->familyName ? 'Match' : 'No Match';
            }
            $oldResultResponse['DateOfBirth'] = $result->dob ? 'Match' : 'No Match';
            if ($result->isVerified) {
                return $this->successResponse('USI is valid', 'data', $oldResultResponse);
            } else {
                return $this->errorResponse('USI is Invalid', 'data', $oldResultResponse, 200);
            }
        } else {
            $result = (object) [
                'FirstName' => '-',
                'FamilyName' => '-',
                'DateOfBirth' => '-',
                'Result' => 'The USI format is Invalid.',
                'usi_invalid_reason' => 'USI is Invalid',
                'is_usi_verified' => 0,
            ];

            return $this->errorResponse('USI is Invalid', 'data', $result, 200);
        }

    }

    public function updateUSIResponse($IsValid, $result)
    {
        if (! $IsValid) {
            $result->Result = str_replace('Valid', 'Invalid', $result->Result);
        }

        // dd($result);
        return $result;
    }

    public function checkUISVerified($request, $result): array
    {
        $result = (array) $result;
        $fieldsToCheck = $request->isSingleName === 'yes' ? ['SingleName'] : ['FirstName', 'FamilyName', 'DateOfBirth', 'Result'];
        $invalidFields = [];
        $matching = [];
        foreach ($fieldsToCheck as $field => $fieldLabel) {
            if ($this->fieldHasNoMatch($result[$fieldLabel], $fieldLabel)) {
                $invalidFields[] = $fieldLabel;
            }
        }
        $isVerified = empty($invalidFields);
        $invalidReason = ! $isVerified
            ? 'USI is not matching with '.implode(', ', $invalidFields).'.'
            : null;

        return [
            'isVerified' => $isVerified,
            'invalidReason' => $invalidReason,
        ];
    }

    private function fieldHasNoMatch($fieldValue, $fieldLabel): bool
    {
        if ($fieldLabel == 'Result') {
            return strpos($fieldValue, 'Invalid');
        } else {
            return strpos($fieldValue, 'NoMatch');
        }
    }

    public function getUserAuditLog(Request $request)
    {
        $data = $this->studentProfileCommonService->getUserAuditLogData($request);

        return $this->successResponse('Data found successfully', 'data', $data);

    }

    public function getDataForBulkEmail(Request $request)
    {
        $collegeId = Auth::user()->college_id;

        $emailData = SmtpSetup::where('college_id', $collegeId)->first(['email', 'status']);
        $email = $emailData && $emailData->status ? $emailData->email : config('mail.from.address');

        $filterCourseData = [];
        if (isset($request->course_id) && ! empty($request->course_id) && count($request->course_id) == 1) {
            $courseData = Courses::find($request->course_id[0]);
            if ($courseData) {
                $filterCourseData = [
                    'id' => $courseData->id,
                    'text' => "{$courseData->course_code} - {$courseData->course_name}",
                ];
            }
        }

        $data = [
            'email' => $email,
            'emailTemplate' => $this->templateService->getEmailTemplateList(),
            'filterCourseData' => $filterCourseData,
        ];

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getDataForBulkLetter(Request $request)
    {
        $collegeId = Auth::user()->college_id;
        $filterCourseData = [];

        if (isset($request->course_id) && ! empty($request->course_id) && count($request->course_id) == 1) {
            $courseData = Courses::find($request->course_id[0]);
            if ($courseData) {
                $filterCourseData = [
                    'id' => $courseData->id,
                    'text' => "{$courseData->course_code} - {$courseData->course_name}",
                ];
            }
        }

        $data = [
            'letterTemplate' => $this->templateService->getletterTemplateList(),
            'filterCourseData' => $filterCourseData,
        ];

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function getStudentAddressHistory(Request $request)
    {

        $results = $this->student->getStudentAddressHistoryData($request);
        $data['total'] = $results['total'];
        $data['data'] = utf8ize($results['data']);

        return $this->successResponse('Data found successfully', 'data', $data);
    }

    public function generateContentToPdf(Request $request)
    {
        if (class_exists(\Barryvdh\Debugbar\Facades\Debugbar::class) && config('app.debug')) {
            \Barryvdh\Debugbar\Facades\Debugbar::disable();
        }
        $dataItem = $request->all();
        $data = json_decode($dataItem['data']);
        $pdf = App::make('dompdf.wrapper');
        $content = '
                <style>
                    table {
                        border-collapse: collapse;
                        width: 100%;
                    }
                    table, th, td {
                        border: 1px solid black;
                    }
                    th, td {
                        padding: 5px;
                    }
                </style>'.$data[0]->contentText;

        return $pdf->loadHTML($content)->stream();
    }

    public function getLetterParameterList(Request $request)
    {
        $collegeId = Auth::user()->college_id;

        $content = ReportLetterSetup::where(['college_id' => $collegeId, 'id' => $request->letter_template_id])->value('letter_editer');
        $request_from = $request->request_from ?? 'profile';
        $replacedContent = $this->studentModel->setLetterBodyContent($request->student_id, $request->course_id, $content, $request_from);
        if ($request_from == 'student-list' && $request->student_count > 1) {
            $arrParameterList = $this->studentModel->getParameterList($content);
        } else {
            $arrParameterList = $this->studentModel->getParameterList($replacedContent);
        }
        $isCourseParameterFound = false;
        if ($request->course_id == '') {
            $isCourseParameterFound = $this->studentModel->isCourseParameter($content);
        }
        $result = ['content' => $replacedContent, 'parameter' => $arrParameterList, 'isCourseParameterFound' => $isCourseParameterFound];

        return $this->successResponse('Data found successfully', 'data', $result);

    }

    public function getReplaceParameterContent(Request $request)
    {
        $collegeId = Auth::user()->college_id;

        $paramVal = $request->input('arr');
        $letterId = $request->input('letter_template_id');
        $courseId = $request->input('course_id');
        $studentId = $request->input('student_id');
        $request_from = $request->request_from ?? 'profile';
        // $objReportLetter = new ReportLetterSetup;
        $content = ReportLetterSetup::where('college_id', $collegeId)->where('id', $letterId)->value('letter_editer');

        $replacedContent = $this->studentModel->setLetterBodyContent($studentId, $courseId, $content, $request_from);
        if ($request_from == 'student-list' && $request->student_count > 1) {
            $finalContent = $this->studentModel->assignParameterValue($content, $paramVal);
        } else {
            $finalContent = $this->studentModel->assignParameterValue($replacedContent, $paramVal);
        }
        // $finalContent = $this->studentModel->assignParameterValue($replacedContent, $paramVal);
        $result = ['content' => $finalContent];

        return $this->successResponse('Data found successfully', 'data', $result);

    }

    public function unitSyncWithMoodle(Request $request)
    {
        return $this->syncWithMoodle('unit', $request);
    }

    public function assessmentSyncWithMoodle(Request $request)
    {
        return $this->syncWithMoodle('assessment', $request);
    }

    public function enrollStudentSubjectSyncWithMoodle(Request $request)
    {
        return $this->syncWithMoodle('enrollment', $request);
    }

    public function unitGradeSyncFromMoodle(Request $request)
    {
        return $this->syncFromMoodle('unit', $request);
    }

    private function syncWithMoodle($type, Request $request)
    {
        if (! $this->isMoodleConnected()) {
            return $this->errorResponse('Moodle not connected', 'data', '', 200);
        }

        $validationMap = [
            'unit' => 'subject_unit_id',
            'assessment' => 'assign_assessment_task_id',
            'enrollment' => 'student_subject_enroll_id',
        ];

        if (empty($request->{$validationMap[$type]})) {
            return $this->errorResponse(ucfirst($type).' ID missing', 'data', '', 200);
        }

        try {
            $studentId = $request->student_id ?? null;
            $courseId = isset($request->student_course_id)
                ? optional($this->studentCourse->find($request->student_course_id))->course_id
                : null;

            switch ($type) {
                case 'unit':
                    return $this->moodleService->syncUnitWithMoodle($courseId, $request->subject_unit_id);
                case 'assessment':
                    return $this->moodleService->syncAssessmentWithMoodle($courseId, $request);
                case 'enrollment':
                    return $this->moodleService->syncEnrollStudSubjectWithMoodle($studentId, $courseId, $request->student_subject_enroll_id);
                default:
                    return $this->errorResponse('Invalid sync type', 'data', '', 200);
            }
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 'data', '', 200);
        }
    }

    private function syncFromMoodle($type, Request $request)
    {
        if (! $this->isMoodleConnected()) {
            return $this->errorResponse('Moodle not connected', 'data', '', 200);
        }

        if (empty($request->unit_id)) {
            return $this->errorResponse(ucfirst($type).' ID missing', 'data', '', 200);
        }

        try {
            $studentId = $request->student_id ?? null;
            $courseId = isset($request->student_course_id)
                ? optional($this->studentCourse->find($request->student_course_id))->course_id
                : null;

            switch ($type) {
                case 'unit':
                    return $this->moodleService->syncUnitAssessmentGradeFromMoodle($studentId, $courseId, $request->unit_id);
                default:
                    return $this->errorResponse('Invalid sync type', 'data', '', 200);
            }
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 'data', '', 200);
        }
    }

    private function unEnrollStudentFromMoodle($studentSubjectEnrollId)
    {

        $studentSubjectEnroll = StudentSubjectEnrolment::findOrFail($studentSubjectEnrollId);
        if (! $studentSubjectEnroll) {
            return $this->errorResponse('Enrollment data not found', 'data', '', 200);
        }

        $syncEnroll = $studentSubjectEnroll->getMeta(StudentSubjectEnrolment::MOODLE_META_KEY);
        if (isset($syncEnroll['sync_status']) && $syncEnroll['sync_status'] == 1) {
            // TODO:: un-enroll student from moodle course
            // return $this->jobCode($studentSubjectEnrollId);
            dispatch(new UnEnrollUserToMoodleCourse($studentSubjectEnrollId));
        } else {
            return $this->deleteUnitEnrollData($studentSubjectEnrollId);
        }

        return true;
    }

    private function deleteUnitEnrollData($studentSubjectEnrollId)
    {
        StudentUnitEnrollment::where(['student_subject_enrollment_id' => $studentSubjectEnrollId])->delete();

        return StudentSubjectEnrolment::where('id', $studentSubjectEnrollId)->delete();
        // return StudentSubjectEnrolment::destroy($studentSubjectEnrollId);
    }

    /*private function jobCode($studentSubjectEnrollId)
    {
        $enrollment = StudentSubjectEnrolment::findOrFail($studentSubjectEnrollId);

        $meta = $enrollment->getMeta(StudentSubjectEnrolment::MOODLE_META_KEY);
        if (!isset($meta['sync_status']) || $meta['sync_status'] != 1) {
            return;
        }

        $student = @$enrollment->student->associatedUserAccount;
        $unit = $enrollment->unit;
        $course = $enrollment->course;
        $courseBatch = CourseBatch::firstOrCreate([
            'unit_id' =>  $enrollment->unit_id,
            'batch' =>  $enrollment->batch
        ]);
        $subjectUnit = $enrollment->subjectUnitRevised()->where('course_id', $enrollment->course_id)->first();

        // dd('enroll user', [
        //     'course_id'     => $course->id,
        //     'subjectUnitId' => $subjectUnit->id,
        //     'unit_id'       => $enrollment->unit_id,
        //     'batch'         => $enrollment->batch,
        //     'course-batch'  => $courseBatch->id
        // ]);
        $moodleUser = $student->asMoodleItem();
        if(!$moodleUser){
            return;
        }

        $moodleCourse = $subjectUnit->asMoodleItem();
        if(!$moodleCourse){
            return;
        }

        //$response = Moodle::enrolments()->unenrolUser($moodleUser->getSyncId(), $moodleCourse->getSyncId(), MoodleMoodle::ROLE_STUDENT);
        $response = Moodle::request('enrol_manual_unenrol_users', [
            'enrolments' => [
                [
                    'roleid'   => MoodleMoodle::ROLE_STUDENT,
                    'userid'   => $moodleUser->getSyncId(),
                    'courseid' => $moodleCourse->getSyncId()
                ],
            ],
        ]);
    }*/

    public function ssoConnectForStudent(Request $request)
    {
        if (! $this->isSSOConnected()) {
            return $this->errorResponse('SSO Allowed', 'data', '', 200);
        }

        if (! $request->student_id) {
            return $this->errorResponse('Student ID missing', 'data', '', 200);
        }

        $student = Student::findOrFail($request->student_id);
        if (! $student) {
            return $this->errorResponse('Student data not found', 'data', '', 200);
        }

        if (! $student->associatedUserAccount) {
            return $this->errorResponse('Student does not have associated login account', 'data', '', 200);
        }

        $user = Users::findOrFail($student->associatedUserAccount->id);
        if (! $user) {
            return $this->errorResponse('User data not found', 'data', '', 200);
        }

        $name = $user->first_last_name;
        $userAr = [
            'firstName' => @$name[0],
            'lastName' => @$name[1],
            'email' => $user->email,
            'password' => Str::random(8),
            'temporaryPassword' => true,
            'notify' => true,
            'user_id' => $user->id,
        ];

        try {
            Log::info('identity creating', $userAr);
            dispatch_sync(new SyncUserToKeycloak(IdentityCreated::FromArray($userAr)));
            sleep(3);

            $res = ['sso_id' => 'NA', 'synced_at' => 'NA'];
            $ssoData = Users::findOrFail($user->id)->sso_data;
            if ($ssoData) {
                $res['sso_id'] = $ssoData['sso_id'] ?? 'NA';
                $res['synced_at'] = $ssoData['synced_at'] ? \Carbon\Carbon::parse($ssoData['synced_at'])->diffForHumans() : 'NA';
            }

            return $this->successResponse('SSO ID created successfully', 'data', $res, 200);

        } catch (\Exception $e) {
            Log::info('Failed to create identity', $userAr);

            return $this->errorResponse($e->getMessage(), 'data', '', 200);
        }

    }
}
