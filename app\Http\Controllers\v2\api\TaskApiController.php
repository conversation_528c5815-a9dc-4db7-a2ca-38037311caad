<?php

namespace App\Http\Controllers\v2\api;

use App\Http\Controllers\Controller;
use App\Model\TaskDepartmentNew;
use App\Model\TaskPriority;
use App\Model\TaskStatus;
use App\Model\Users;
use App\Model\v2\Student;
use App\Traits\CommonTrait;
use App\Traits\ResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Auth;
use SSO\Factories\TaskPayloadFactory;
use SSO\Models\Integration;
use SSO\Services\TaskService;

class TaskApiController extends Controller
{
    use CommonTrait;
    use ResponseTrait;

    public function getTaskFormData(Request $request)
    {
        $collegeId = Auth::user()->college_id;
        $data['departmentData'] = TaskDepartmentNew::where('college_id', $collegeId)->select('department_name as Name', 'id as Id')->get();
        $data['priorityData'] = TaskPriority::where('college_id', $collegeId)->select('priority_name as Name', 'id as Id')->get();

        return $this->successResponse('Data found', 'data', $data);
    }

    public function createTask(Request $request)
    {
        try {
            // Validate request data
            $validated = $request->validate([
                'title' => 'required|string|max:255',
                'department_id' => 'required|integer',
                'priority_id' => 'required|integer',
            ]);

            // Get authenticated user
            $currentUser = Auth::user();
            $collegeId = $currentUser->college_id;
            $checkList = [];
            $subtask = [];
            $status = new TaskStatus(['status_name' => 'Active']);
            $department = TaskDepartmentNew::find($request->department_id);
            $priority = TaskPriority::find($request->priority_id);
            $students = Student::find($request->student_id);
            $users = Users::find(Auth::user()->id);
            $params = TaskPayloadFactory::CreateForTaskPayload(
                $request->title,
                '',
                'Qtask',
                'QLD',
                date('Y-m-d'),
                null,
                $checkList,
                $subtask,
                $department,
                $status,
                $priority,
                $students,
                $users,
            );
            $qtaskIntegration = Integration::QtaskConnection();
            if (! $qtaskIntegration) {
                return $this->errorResponse('QTask integration not configured', 'error', 500);
            }

            $taskService = new TaskService($qtaskIntegration->repo());
            $createTasks = $taskService->createTask($params);

            // Get QTask SSO URL
            $integration = Integration::where('integration', Integration::INTEGRATION_QTASK)->first();
            if (! $integration) {
                return $this->errorResponse('QTask integration not found', 'error', 500);
            }

            $redirectUrl = $integration->myTaskListWithTaskUuidUrl($createTasks['data']['uuid']);

            return $this->successResponse('Task created successfully', 'data', [
                'task' => $createTasks,
                'redirect_url' => $redirectUrl,
            ]);
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage(), 'error', 500);
        }
    }
}
