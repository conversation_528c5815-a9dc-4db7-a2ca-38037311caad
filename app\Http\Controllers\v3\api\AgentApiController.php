<?php

namespace App\Http\Controllers\v3\api;

use App\Helpers\Helpers;
use App\Http\Controllers\Controller;
use App\Http\Requests\api\v3\AgentStoreRequest;
use App\Http\Resources\api\v3\AgentInformationDetailResource;
use App\Http\Resources\api\v3\AgentInformationResource;
use App\Http\Resources\api\v3\AgentStatsResource;
use App\Model\v2\Agent;
use App\Model\v2\AgentDocumentChecklist;
use App\Model\v2\AgentImages;
use App\Traits\CommonRequestMethodsTrait;
use App\Traits\ResponseTrait;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Support\Services\UploadService;

class AgentApiController extends Controller
{
    use CommonRequestMethodsTrait;
    use ResponseTrait;

    public function __construct() {}

    /**
     * Get List of Agents.
     *
     * @queryParam page The page number.
     * @queryParam limit The number of items per page.
     * @queryParam search The search query.
     * @queryParam status The status of the agent. Possible values: 1 (Active), 2 (Inactive), 3 (New Application Request), 4 (Preliminary), 5 (Principal Agent), 6 (Terminated).
     * @queryParam sort The sort order.
     * @queryParam order The sort order.
     *
     * @response array{
     *   success: "0/1",
     *   status: "Success/Error",
     *   message: "String",
     *   data: array{
     *     agents: array{
     *       id: "string (encrypted)",
     *       agent_code: "string|null",
     *       agency_name: "string|null",
     *       contact_person: "string|null",
     *       primary_email: "string|null",
     *       status: "integer|null (Agent status: 1=Active, 2=Inactive, 3=New Application Request, 4=Preliminary, 5=Principal Agent, 6=Terminated)",
     *       locked: "integer|null (Lock status: 0=Unlocked, 1=Locked)",
     *       login_data: array{
     *         active: "boolean (User account active status)"
     *       }
     *     }[]
     *   }
     * }
     */
    public function list(Request $request)
    {
        try {
            $request->validate([
                /* page number to dislplay the list of agents */
                'page' => 'nullable|integer',
                /* limit of agents to be displayed in a page */
                'limit' => 'nullable|integer|min:1|max:100',
                /* search the list */
                'search' => 'nullable|string',
                /* sort the list by the field */
                'sort' => 'nullable|in:name,person,email,telephone,mobile,country,recent,locked,status',
                /* order of the sort Ascending or Descending */
                'order' => 'nullable|in:asc,desc',
            ]);
            $dataParams = [
                'page' => $request->input('page') ?? 1,
                'limit' => $request->input('limit') ?? 25,
                'search' => $request->input('search') ?? '',
                'sort' => $request->input('sort') ?? 'name',
                'order' => $request->input('order') ?? 'asc',
            ];
            $agents = Agent::getAgentsListForApi($dataParams);

            return response()->json([
                'success' => 1,
                'status' => 'success',
                'message' => 'Agents list fetched successfully',
                'data' => AgentInformationResource::collection($agents),
                'meta' => [
                    'current_page' => $agents->currentPage(),
                    'from' => $agents->firstItem(),
                    'last_page' => $agents->lastPage(),
                    'per_page' => $agents->perPage(),
                    'to' => $agents->lastItem(),
                    'total_records' => $agents->total(),
                ],
            ], 200);

        } catch (\Exception $e) {
            DB::rollBack();

            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Get Agent Detail.
     *
     * @queryParam id The id of the agent (encrypted).
     *
     * @response array{
     *   success: "0/1",
     *   status: "Success/Error",
     *   message: "String",
     *   data: array{
     *     agents: array{
     *       id: "string (encrypted)",
     *       agent_code: "string|null",
     *       agency_name: "string|null",
     *       contact_person: "string|null",
     *       primary_email: "string|null",
     *       status: "integer|null (Agent status: 1=Active, 2=Inactive, 3=New Application Request, 4=Preliminary, 5=Principal Agent, 6=Terminated)",
     *       locked: "integer|null (Lock status: 0=Unlocked, 1=Locked)",
     *       login_data: array{
     *         id: "string (encrypted)",
     *         email: "string|null",
     *         username: "string|null",
     *         name: "string|null",
     *         email_verified: "boolean (Email verification status)",
     *         active: "boolean (User account active status: true=Active, false=Inactive)"
     *       }|null,
     *       sub_agents: "array (Collection of sub-agents with same structure as parent)",
     *       super_agent: "object|null (Parent agent information if this is a sub-agent)"
     *     }
     *   }
     * }
     */
    public function getAgentDetail(Request $request, $id)
    {
        try {
            $id = decryptIt($id);

            $agent = Agent::getAgentDetailForApi($id);

            return $this->successResponse('Agent Detail Found', 'data', new AgentInformationDetailResource($agent));

        } catch (\Exception $e) {
            DB::rollBack();

            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Add New Agent
     *
     * Create new agent in the application.
     *
     * @bodyParam agent_code string optional Agent code (must be unique within college).
     * @bodyParam agency_name string required Agency name.
     * @bodyParam contact_person string optional Contact person name.
     * @bodyParam primary_email string required Primary email (must be unique within college).
     * @bodyParam status integer optional Agent status. Default: 1. Possible values: 1 (Active), 2 (Inactive), 3 (New Application Request), 4 (Preliminary), 5 (Principal Agent), 6 (Terminated).
     * @bodyParam is_lock integer optional Lock status. Default: 1. Values: 0 (Unlocked), 1 (Locked).
     *
     * @response array{
     *   success: "0/1",
     *   status: "Success/Error",
     *   message: "String",
     *   data: array{
     *     agents: array{
     *       id: "string (encrypted)",
     *       agent_code: "string|null",
     *       agency_name: "string|null",
     *       contact_person: "string|null",
     *       primary_email: "string|null",
     *       status: "integer|null (Agent status: 1=Active, 2=Inactive, 3=New Application Request, 4=Preliminary, 5=Principal Agent, 6=Terminated)",
     *       locked: "integer|null (Lock status: 0=Unlocked, 1=Locked)"
     *     }
     *   }
     * }
     */
    public function store(AgentStoreRequest $request)
    {
        try {
            $validated = $request->validated();
            $validated['college_id'] = auth()->user()->college_id ?? null;
            $agent = Agent::create($validated);

            return ajaxSuccess([
                'data' => new AgentInformationResource($agent),
            ], 'Agent created successfully');
        } catch (\Exception $e) {
            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Update Agent Information
     *
     * Update an existing agent's information.
     *
     * @param  Request  $request
     * @param  string  $id  Encrypted agent ID
     *
     * @bodyParam agent_code string optional Agent code (must be unique within college, excluding current agent).
     * @bodyParam agency_name string optional Agency name.
     * @bodyParam contact_person string optional Contact person name.
     * @bodyParam primary_email string optional Primary email (must be unique within college, excluding current agent).
     * @bodyParam status integer optional Agent status. Possible values: 1 (Active), 2 (Inactive), 3 (New Application Request), 4 (Preliminary), 5 (Principal Agent), 6 (Terminated).
     * @bodyParam is_lock integer optional Lock status. Values: 0 (Unlocked), 1 (Locked).
     *
     * @response array{
     *   success: "0/1",
     *   status: "Success/Error",
     *   message: "String",
     *   data: array{
     *     agents: array{
     *       id: "string (encrypted)",
     *       agent_code: "string|null",
     *       agency_name: "string|null",
     *       contact_person: "string|null",
     *       primary_email: "string|null",
     *       status: "integer|null (Agent status: 1=Active, 2=Inactive, 3=New Application Request, 4=Preliminary, 5=Principal Agent, 6=Terminated)",
     *       locked: "integer|null (Lock status: 0=Unlocked, 1=Locked)",
     *       login_data: array{
     *         id: "string (encrypted)",
     *         email: "string|null",
     *         username: "string|null",
     *         name: "string|null",
     *         email_verified: "boolean (Email verification status)",
     *         active: "boolean (User account active status: true=Active, false=Inactive)"
     *       }|null,
     *       sub_agents: "array (Collection of sub-agents with same structure as parent)",
     *       super_agent: "object|null (Parent agent information if this is a sub-agent)"
     *     }
     *   }
     * }
     */
    public function update(AgentStoreRequest $request, $id)
    {
        try {
            $agentId = decryptIt($id);
            $agent = Agent::find($agentId);
            if (! $agent) {
                return $this->errorResponse('Agent not found');
            }
            $validated = $request->validated();
            $agent->update($validated);

            return $this->successResponse('Agent updated successfully', 'data', new AgentInformationDetailResource($agent));
        } catch (\Exception $e) {
            DB::rollBack();

            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Get agent statistics
     *
     * @queryParam from date optional Start date for statistics filtering (format: Y-m-d).
     * @queryParam to date optional End date for statistics filtering (format: Y-m-d).
     *
     * @response array{
     *   success: "0/1",
     *   status: "Success/Error",
     *   message: "String",
     *   data: "object (Agent statistics data including performance metrics and status-related information)"
     * }
     */
    public function stats(Request $request, $id)
    {
        try {
            $request->validate([
                'from' => 'nullable|date',
                'to' => 'nullable|date',
            ]);

            $dateFilters = [
                'startDate' => $request->input('from') ?? '',
                'endDate' => $request->input('to') ?? '',
            ];

            $id = decryptIt($id);

            $stats = Agent::getAgentStatsForApi($id, $dateFilters);

            return $this->successResponse('Agent Statistics', 'data', new AgentStatsResource($stats));

        } catch (\Exception $e) {
            DB::rollBack();

            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Get Agent Document Checklist
     *
     * Retrieves the list of document checklist items for agents.
     *
     * @param  string  $id  Encrypted agent ID
     *
     * @response array{
     *   success: "0/1",
     *   status: "Success/Error",
     *   message: "String",
     *   data: array{
     *     id: "integer",
     *     document_name: "string",
     *     is_compulsory: "boolean (Document requirement status)",
     *     is_active: "boolean (Document checklist item active status)"
     *   }[]
     * }
     */
    public function getAgentDocumentChecklist(Request $request, $id)
    {
        try {

            $documentChecklist = AgentDocumentChecklist::where('college_id', auth()->user()->college_id)->where('is_active', 1)->get();
            $documentChecklist = $documentChecklist->map(function ($item) {
                return [
                    'id' => $item->id,
                    'document_name' => $item->document_name,
                    'is_compulsory' => (bool) $item->is_compulsory,
                    'is_active' => (bool) $item->is_active,
                ];
            });

            return $this->successResponse('Agent Document Checklist', 'data', $documentChecklist);

        } catch (\Exception $e) {
            DB::rollBack();

            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Upload Agent Document
     *
     * Upload a new document for a specific agent.
     *
     * @param  string  $id  Encrypted agent ID
     *
     * @bodyParam file file required Document file (jpeg, png, jpg, pdf, doc, docx, max: 1GB).
     * @bodyParam document_name string required Document name/title.
     * @bodyParam checklist_id integer required Document checklist item ID.
     * @bodyParam is_compulsory boolean required Whether the document is compulsory.
     *
     * @response array{
     *   success: "0/1",
     *   status: "Success/Error",
     *   message: "String",
     *   data: array{
     *     document_id: "integer",
     *     file_name: "string (Original filename)",
     *     file_path: "string (Full file path)"
     *   }
     * }
     */
    public function uploadDocument(Request $request, $id)
    {
        try {
            $agentId = decryptIt($id);

            $agent = Agent::find($agentId);
            if (! $agent) {
                return $this->errorResponse('Agent not found');
            }
            $request->validate([
                'file' => 'required|file|mimes:jpeg,png,jpg,pdf,doc,docx|max:1024000',
                'document_name' => 'required|string|max:255',
                'checklist_id' => 'required|integer',
                'is_compulsory' => 'required|boolean',
            ]);

            $user = auth()->user();
            $collegeId = $user->college_id ?? null;

            $rootFolder = Config::get('constants.arrCollegeRootFolder');
            $filePath = Config::get('constants.uploadFilePath.AgentFiles');
            $destinationPath = Helpers::changeRootPath($filePath, $agentId);

            $file = $request->file('file');
            $originalName = $file->getClientOriginalName();
            $filename = hashFileName($originalName);
            $fileSize = ceil($file->getSize() / 1024);

            $upload_success = UploadService::uploadAs($destinationPath['view'], $file, $filename);
            info('file uploaded form Agent Document upload', [$upload_success]);

            $document = AgentImages::create([
                'agent_id' => $agentId,
                'original_name' => $originalName,
                'images' => $filename,
                'document_name' => $request->input('document_name'),
                'checklist_id' => $request->input('checklist_id'),
                'comment' => ' ',
                'is_compulsory' => $request->input('is_compulsory'),
                'approved' => 1,
            ]);

            $objCommonModel = new \App\Model\CommonModel;
            $dataArr = [
                'college_id' => $collegeId,
                'folder_name' => $rootFolder['AgentFiles'],
                'sub_folder_name' => $agentId,
                'user_id' => $user->id,
            ];

            $clgMaterialParentId = $objCommonModel->getSubParentId($dataArr);

            $agentDataArr = [
                'college_id' => $collegeId,
                'original_name' => $originalName,
                'file_name' => $filename,
                'size' => $fileSize,
                'type' => 'File',
                'parent_id' => $clgMaterialParentId,
                'file_path' => $destinationPath['view'],
                'user_id' => $user->id,
            ];

            $objCommonModel->addCollegeMaterialInfo($agentDataArr);

            return $this->successResponse('Document uploaded successfully', 'data', [
                'document_id' => $document->id,
                'file_name' => $originalName,
                'file_path' => $destinationPath['view'].$filename,
            ]);

        } catch (\Exception $e) {
            DB::rollBack();

            return $this->errorResponse($e->getMessage());
        }
    }

    /**
     * Delete Agent Document
     *
     * Delete a specific document from an agent.
     *
     * @param  string  $id  Encrypted agent ID
     * @param  int  $documentId  Document ID
     *
     * @response array{
     *   success: "0/1",
     *   status: "Success/Error",
     *   message: "String"
     * }
     */
    public function deleteDocument(Request $request, $id, $documentId)
    {
        try {
            $agentId = decryptIt($id);
            $agent = Agent::find($agentId);
            if (! $agent) {
                return $this->errorResponse('Agent not found');
            }
            $document = AgentImages::where('id', $documentId)->where('agent_id', $agentId)->first();
            if (! $document) {
                return $this->errorResponse('Document not found or does not belong to this agent');
            }
            $document->delete();

            $filePath = Config::get('constants.uploadFilePath.AgentFiles');
            $destinationPath = Helpers::changeRootPath($filePath, $agentId);
            UploadService::delete($destinationPath['view'].$document->images);

            return $this->successResponse('Document deleted successfully');

        } catch (\Exception $e) {
            DB::rollBack();

            return $this->errorResponse($e->getMessage());
        }
    }
}
