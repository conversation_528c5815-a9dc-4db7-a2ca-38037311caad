<?php

namespace App\Http\Livewire;

use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Str;
use Livewire\Attributes\Computed;
use Livewire\Component;

class Docs extends Component
{
    public string $path;

    public ?string $title = null;

    public ?string $tooltip = null;

    public array $data = [];

    public function mount($path, $title = null, $tooltip = null, $data = [])
    {
        $this->path = $path;
        $this->title = $title;
        $this->tooltip = $tooltip;
        $this->data = $data;
    }

    /**
     * Get the tooltip text based on the file path if not explicitly provided
     */
    public function getTooltipText()
    {
        if ($this->tooltip) {
            return $this->tooltip;
        }

        // Extract the filename without extension
        $filename = pathinfo($this->path, PATHINFO_FILENAME);

        // Capitalize the first letter and format the tooltip
        $serviceName = ucfirst($filename);

        return "Click to view {$serviceName} setup documentation";
    }

    #[Computed()]
    public function content()
    {
        // Make cache key tenant-specific and include data hash to avoid cross-tenant URL caching issues
        $dataHash = md5(serialize($this->data));
        $cacheKey = $this->path.'_'.(tenant('id') ?? 'default').'_'.$dataHash;

        return Cache::driver('file')->rememberForever($cacheKey, function () {
            $content = $this->getFormattedContent();

            // $content = file_get_contents(base_path($this->path));
            return Str::markdown($content, [
                'enable_em' => true,
                'enable_strong' => true,
                'use_asterisk' => true,
                'use_underscore' => true,
                'unordered_list_markers' => ['-', '*', '+'],
            ]);
        });
    }

    private function getFormattedContent()
    {
        $content = file_get_contents(base_path($this->path));

        // Process custom data placeholders first
        if (! empty($this->data)) {
            foreach ($this->data as $key => $value) {
                $content = str_replace('{{ $'.$key.' }}', $value, $content);
            }
        }

        // Process Laravel route helpers
        $content = preg_replace_callback('/\{\{\s*route\([\'"](.+?)[\'"](.*?)\)\s*\}\}/', function ($matches) {
            $routeName = $matches[1];
            $routeParams = isset($matches[2]) && ! empty(trim($matches[2])) ? trim($matches[2]) : null;

            try {
                if ($routeParams) {
                    // Parse route parameters
                    $params = [];
                    preg_match_all('/[\'"](.*?)[\'"]\s*=>\s*[\'"](.*?)[\'"]/', $routeParams, $paramMatches, PREG_SET_ORDER);
                    foreach ($paramMatches as $match) {
                        $params[$match[1]] = $match[2];
                    }

                    return route($routeName, $params);
                } else {
                    return route($routeName);
                }
            } catch (\Exception) {
                // If route doesn't exist, return the original text
                return $matches[0];
            }
        }, $content);

        // Process image paths in markdown
        $content = preg_replace_callback('/!\[(.*?)\]\((.*?)\)/', function ($matches) {
            $altText = $matches[1];
            $imagePath = $matches[2];

            // If the image path is relative, make it absolute
            if (! str_starts_with($imagePath, 'http') && ! str_starts_with($imagePath, '/')) {
                // Get the MD file name without extension to use as folder name
                $mdFileName = pathinfo($this->path, PATHINFO_FILENAME);

                // Check priority paths in order:
                // 1. First check if image exists in public/v2/img/{mdFileName}/ directory
                $mdSpecificPath = 'v2/img/'.$mdFileName.'/'.basename($imagePath);

                // 2. Then check if image exists directly in public/v2/img/ directory
                $v2ImgPath = 'v2/img/'.basename($imagePath);

                if (file_exists(public_path($mdSpecificPath))) {
                    // Use the MD-specific folder path
                    $imagePath = asset($mdSpecificPath);
                } elseif (file_exists(public_path($v2ImgPath))) {
                    // Use the general v2/img path
                    $imagePath = asset($v2ImgPath);
                } else {
                    // Fall back to the original directory-relative path
                    $docDir = dirname(base_path($this->path));
                    $fullImagePath = $docDir.'/'.$imagePath;

                    // Convert to a URL path relative to public directory
                    $publicPath = str_replace(base_path(), '', $fullImagePath);
                    $publicPath = str_replace('\\', '/', $publicPath);

                    // Create a proper URL
                    $imagePath = asset($publicPath);
                }
            }

            // Return the image tag with proper styling
            return '<img src="'.$imagePath.'" alt="'.$altText.'" class="max-w-full rounded-md shadow-sm border border-gray-200" style="max-height: 300px; object-fit: contain;">';
        }, $content);

        // Process links to open in new tab
        $content = preg_replace_callback('/\[(.*?)\]\((.*?)\)/', function ($matches) {
            // Skip if it's an image link (already processed above)
            if (substr($matches[0], 0, 1) === '!') {
                return $matches[0];
            }

            $linkText = $matches[1];
            $linkUrl = $matches[2];

            // Return the link with target="_blank" and rel attributes for security
            return '<a href="'.$linkUrl.'" target="_blank" class="text-blue-500 hover:text-blue-900" rel="noopener noreferrer">'.$linkText.'</a>';
        }, $content);

        return $content;
    }

    public function reloadDocs()
    {
        // Clear cache for current tenant and data combination
        $dataHash = md5(serialize($this->data));
        $cacheKey = $this->path.'_'.(tenant('id') ?? 'default').'_'.$dataHash;
        Cache::driver('file')->forget($cacheKey);
    }

    public function render()
    {
        return view('livewire.docs');
    }
}
