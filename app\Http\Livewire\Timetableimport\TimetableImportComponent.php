<?php

namespace App\Http\Livewire\Timetableimport;

use App\Jobs\ProcessTimetableImport;
use App\Model\v2\TimetableImport;
use App\Traits\CommonTrait;
use Carbon\Carbon;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Storage;
use Livewire\Component;
use Livewire\WithFileUploads;
use Livewire\WithPagination;
use Support\Services\UploadService;

class TimetableImportComponent extends Component
{
    use CommonTrait;
    use WithFileUploads;
    use WithPagination;

    public $csv_file;

    public $import_batch_id;

    public $headers = [];

    public $isPolling = false;

    public $sortDirection = 'desc'; // Default sort direction (newest first)

    protected $paginationTheme = 'tailwind';

    public $searchTerm = '';

    protected $listeners = [
        'recordUpdated' => 'onRecordUpdated',
        'togglePolling' => 'togglePolling',
        'startImport' => 'startImportFromFile',
        'deleteRecord' => 'delete',
        'viewDetails' => 'getRecordDetails',
        'toggleSortDirection' => 'toggleSortDirection',
        'edit' => 'edit',
    ];

    public function onRecordUpdated()
    {
        $this->refreshGrid();
    }

    // Set polling interval to 10 seconds
    public function getPollingIntervalProperty()
    {
        return 10000; // 10 seconds in milliseconds
    }

    public function mount()
    {
        $this->import_batch_id = session('timetable_import_batch_id', uniqid('Import_'));
        session(['timetable_import_batch_id' => $this->import_batch_id]);
    }

    public function updatedCsvFile()
    {
        $this->validate([
            'csv_file' => 'required|file|mimes:csv,txt',
        ]);
        // Do NOT call importCsv() here!
    }

    public function loadRows()
    {
        $searchableColumns = [
            'campus',
            'course_type',
            'calendar_type',
            'calendar_year',
            'semester',
            'term',
            'start_week',
            'end_week',
            'assessor',
            'census_date',
            'subject',
            'batch',
            'teacher',
            'attendance_type',
            'days',
            'class_type',
            'start_time',
            'finish_time',
            'venue_code',
            'room_code',
        ];

        $result = TimetableImport::with('creator:id,name')
            ->when($this->searchTerm, function ($query) use ($searchableColumns) {
                $query->where(function ($q) use ($searchableColumns) {
                    foreach ($searchableColumns as $column) {
                        $q->orWhere($column, 'like', '%'.$this->searchTerm.'%');
                    }
                });
                $query->orWhereHas('creator', function ($q) {
                    $q->where('name', 'like', '%'.$this->searchTerm.'%');
                });
            })
            ->orderBy('created_at', $this->sortDirection)
            ->paginate(50);

        // Append the new column to each result
        $result->getCollection()->transform(function ($item) {
            $item->import_batch_id = $item->import_batch_id.'_'.$item->id; // Add the ID to the import_batch_id

            return $item;
        });

        return $result;
    }

    /**
     * Toggle the sort direction between 'asc' and 'desc'
     */
    public function toggleSortDirection(...$args)
    {
        $dir = $args[0] ?? 'asc';
        $this->sortDirection = $dir;
    }

    public function edit($id)
    {
        $record = TimetableImport::find($id);
        if ($record) {
            // Use dispatch instead of emit
            $this->dispatch('openEditModal', record: $record->toArray());
        }
    }

    public function delete($id)
    {
        // Handle both Livewire 2.x and 3.x parameter styles
        if (is_array($id) && isset($id['id'])) {
            $id = $id['id'];
        }

        $record = TimetableImport::find($id);
        if ($record) {
            $record->delete();
            session()->flash('message', 'Record deleted successfully!');
        }
        $this->refreshGrid();
    }

    public function resync($id)
    {
        $record = TimetableImport::find($id);
        if ($record) {
            // Reset status to pending
            $record->update([
                'status' => 'pending',
                'error_message' => null,
            ]);

            // Dispatch job for reprocessing
            ProcessTimetableImport::dispatch($record);

            // Enable auto-refresh to show progress
            $this->isPolling = true;

            session()->flash('message', 'Record queued for reprocessing! Auto-refresh enabled to show progress.');
        }
    }

    public function downloadSampleCsv()
    {
        $headers = [
            'CampusName',
            'CourseType',
            'CalendarType',
            'Year',
            'SemesterName',
            'Term',
            'StartDate',
            'EndDate',
            'AssessorCode',
            'CensusDate',
            'SubjectCode',
            'TeacherCode',
            'AttendanceType',
            'Days',
            'ClassType',
            'StartTime',
            'FinishTime',
            'VenueCode',
            'RoomCode',
        ];

        $sampleData = [
            [
                'Campus A',
                'VET',
                'Calendar Set1',
                '2023',
                'Semester 1',
                '1',
                '2023-02-01',
                '2023-05-30',
                'ASR001',
                '2023-03-15',
                'MATH101',
                'TCH001',
                'Hourly',
                'Monday|Wednesday',
                'Lecture',
                '09:00',
                '11:00',
                'VENUE101',
                'RM101',
            ],
            [
                'Campus B',
                'HigherEd',
                'Calendar Set2',
                '2023',
                'Semester 2',
                '2',
                '2023-07-01',
                '2023-10-30',
                'ASR002',
                '2023-07-15',
                'ENG101',
                'TCH002',
                'Hourly',
                'Wednesday|Friday|Saturday',
                'Tutorial',
                '13:00',
                '15:00',
                'VENUE202',
                'RM202',
            ],
        ];

        $csvContent = implode(',', $headers)."\n";
        foreach ($sampleData as $row) {
            $csvContent .= implode(',', $row)."\n";
        }

        $filename = 'timetable_import_sample.csv';

        return response()->streamDownload(function () use ($csvContent) {
            echo $csvContent;
        }, $filename, [
            'Content-Type' => 'text/csv',
        ]);
    }

    /**
     * Toggle the polling state
     */
    public function togglePolling()
    {
        $this->isPolling = ! $this->isPolling;
        $this->refreshGrid();
    }

    /**
     * Start importing data from an uploaded file
     *
     * @param  string  $filename  The name of the uploaded file
     * @return void
     */
    public function startImportFromFile($filename)
    {
        try {
            $collegeId = auth()->user()->college_id;
            $filePath = Config::get('constants.uploadFilePath.TimetableImport');
            $destinationPath = $this->changeRootPath($filePath, $collegeId);

            // Check if we're using S3 storage
            $tempFilePath = null;

            // Get the file path or URL
            if (isset($destinationPath['view'])) {
                $fullPath = UploadService::url($destinationPath['view'].$filename);
            } else {
                $fullPath = $destinationPath['default'].$filename;
            }

            $s3FilePath = $destinationPath['view'].$filename;
            $stream = Storage::disk(config('filesystems.upload_disk'))->readStream($s3FilePath);
            // Process the CSV file
            $header = fgetcsv($stream);
            $this->headers = $header;

            while ($row = fgetcsv($stream)) {
                $data = array_combine($header, $row);
                $importRecord = TimetableImport::create([
                    'campus' => trim($data['CampusName'] ?? null),
                    'course_type' => trim($data['CourseType'] ?? null),
                    'calendar_type' => trim($data['CalendarType'] ?? null),
                    'calendar_year' => trim($data['Year'] ?? null),
                    'semester' => trim($data['SemesterName'] ?? null),
                    'term' => trim($data['Term'] ?? null),
                    'start_week' => $this->parseDate($data['StartDate'] ?? null),
                    'end_week' => $this->parseDate($data['EndDate'] ?? null),
                    'assessor' => trim($data['AssessorCode'] ?? null),
                    'census_date' => $this->parseDate($data['CensusDate'] ?? null),
                    'subject' => trim($data['SubjectCode'] ?? null),
                    'batch' => trim($data['BatchName'] ?? null),
                    'teacher' => trim($data['TeacherCode'] ?? null),
                    'attendance_type' => trim($data['AttendanceType'] ?? null),
                    'days' => trim($data['Days'] ?? null),
                    'class_type' => trim($data['ClassType'] ?? null),
                    'start_time' => $this->parseTime($data['StartTime'] ?? null),
                    'finish_time' => $this->parseTime($data['FinishTime'] ?? null),
                    'venue_code' => trim($data['VenueCode'] ?? null),
                    'room_code' => trim($data['RoomCode'] ?? null),
                    'status' => 'pending',
                    'import_batch_id' => $this->import_batch_id,
                ]);

                // Dispatch job for processing
                ProcessTimetableImport::dispatch($importRecord);
            }

            // Close the file
            fclose($stream);

            // Clean up temporary file if used
            if ($tempFilePath && file_exists($tempFilePath)) {
                unlink($tempFilePath);
            }

            $this->refreshGrid();

            // Enable polling to show progress
            $this->isPolling = true;

            session()->flash('message', 'CSV imported successfully! Records are being processed in the background.');
        } catch (\Exception $e) {
            // Clean up temporary file if it exists
            if (isset($tempFilePath) && $tempFilePath && file_exists($tempFilePath)) {
                unlink($tempFilePath);
            }

            session()->flash('error', 'Error importing CSV: '.$e->getMessage());
        }
    }

    /**
     * Poll function that will be called automatically
     */
    public function poll()
    {
        // This method will be called automatically every 10 seconds
        // when polling is enabled
        return $this->loadRows();
    }

    /**
     * Get the details of a specific record and emit them to the frontend
     *
     * @param  int  $id  The ID of the record to get details for
     * @return void
     */
    public function getRecordDetails($id)
    {
        if (is_array($id) && isset($id['id'])) {
            $id = $id['id'];
        }

        $record = TimetableImport::find($id);

        if ($record) {
            // Create a copy of the record data as an array
            $recordData = $record->toArray();

            // Format dates for display
            $recordData['created_at'] = $record->created_at ? $record->created_at->format('d-m-Y g:i A') : null;
            $recordData['updated_at'] = $record->updated_at ? $record->updated_at->format('d-m-Y g:i A') : null;

            // Dispatch method with browser event
            $this->dispatch('record-details-loaded', $recordData);
        }
    }

    /**
     * Parse a date string to Y-m-d format using Carbon
     * Handles multiple date formats including DD-MM-YYYY and MM-DD-YYYY
     *
     * @param  string|null  $dateString
     * @return string|null
     */
    protected function parseDate($dateString)
    {
        if (empty($dateString)) {
            return null;
        }

        try {
            // Try to parse the date using multiple formats
            if (preg_match('/^\d{2}-\d{2}-\d{4}$/', $dateString)) {
                // For dates in XX-XX-XXXX format, we need to determine if it's DD-MM or MM-DD

                // Extract the parts
                [$part1, $part2, $year] = explode('-', $dateString);

                // Check if it's a valid date in DD-MM-YYYY format
                if (checkdate((int) $part2, (int) $part1, (int) $year)) {
                    // It's a valid DD-MM-YYYY date
                    return Carbon::createFromFormat('d-m-Y', $dateString)->format('Y-m-d');
                }

                // Check if it's a valid date in MM-DD-YYYY format
                if (checkdate((int) $part1, (int) $part2, (int) $year)) {
                    // It's a valid MM-DD-YYYY date
                    return Carbon::createFromFormat('m-d-Y', $dateString)->format('Y-m-d');
                }

                // If we get here, neither format is valid
                return null;
            }

            // Try YYYY-MM-DD format
            if (preg_match('/^\d{4}-\d{2}-\d{2}$/', $dateString)) {
                return $dateString; // Already in the correct format
            }

            // As a fallback, use Carbon's flexible parsing
            return Carbon::parse($dateString)->format('Y-m-d');
        } catch (\Exception) {
            // Log the error if needed
            // \Log::error("Failed to parse date: {$dateString}");

            // If all parsing attempts fail, return null
            return null;
        }
    }

    /**
     * Parse a time string to H:i format using Carbon
     *
     * @param  string|null  $timeString
     * @return string|null
     */
    protected function parseTime($timeString)
    {
        if (empty($timeString)) {
            return null;
        }

        try {
            return Carbon::parse($timeString)->format('H:i');
        } catch (\Exception) {
            // Log the error if needed
            // \Log::error("Failed to parse time: {$timeString}");

            return null;
        }
    }

    public function updatedSearchTerm()
    {
        $this->resetPage();
        $this->refreshGrid();
    }

    private function refreshGrid()
    {
        $this->dispatch('updateKendoGrid', [
            'data' => $this->loadRows(),
        ]);
    }

    public function render()
    {
        return view('livewire.timetableimport.timetable-import', [
            'rows' => $this->loadRows(),
            'headers' => $this->headers,
        ]);
    }
}
