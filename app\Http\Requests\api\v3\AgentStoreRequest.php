<?php

namespace App\Http\Requests\api\v3;

use App\Traits\CommonRequestMethodsTrait;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Validation\Rule;

class AgentStoreRequest extends FormRequest
{
    use CommonRequestMethodsTrait;

    private $methodName;

    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     *
     * @return array<string, \Illuminate\Contracts\Validation\ValidationRule|array<mixed>|string>
     */
    public function rules()
    {
        $collegeId = auth()->user()->college_id ?? null;
        $agentId = $this->route('id') ? decryptIt($this->route('id')) : null;

        return [

            /**
             * @param agency_name The name of the agency.
             */
            'agency_name' => 'required|string|max:255',
            /**
             * @param contact_person The contact person of the agency.
             */
            'contact_person' => 'required|string|max:255',
            /**
             * @param agent_code The unique agent code.
             */
            'agent_code' => [
                'nullable',
                'string',
                'max:50',
                Rule::unique('rto_agents', 'agent_code')
                    ->where('college_id', $collegeId)
                    ->ignore($agentId),
            ],
            /**
             * @param primary_email The primary email of the agency.
             */
            'primary_email' => [
                'required',
                'email',
                'max:255',
                Rule::unique('rto_agents', 'primary_email')
                    ->where('college_id', $collegeId)
                    ->ignore($agentId),
            ],
            /**
             * @param telephone The telephone of the agency.
             */
            'telephone' => 'required|string|max:255',
            /**
             * @param super_agent_id The super agent id of the agency.
             */
            'super_agent_id' => 'required|integer',
            /**
             * @param office_country The office country of the agency.
             */
            'office_country' => 'required|string|max:255',
            /**
             * @param office_postcode The office postcode of the agency.
             */
            'office_postcode' => 'required|string|max:255',
            /**
             * @param postal_country The postal country of the agency.
             */
            'postal_country' => 'required|string|max:255',
            /**
             * @param postal_postcode The postal postcode of the agency.
             */
            'postal_postcode' => 'required|string|max:255',
            /**
             * @param target_primary_country The target primary country of the agency.
             */
            'target_primary_country' => 'required|string|max:255',
            /**
             * @param target_secondry_country The target secondry country of the agency.
             */
            'target_secondry_country' => 'required|string|max:255',
            /**
             * @param status The status of the agency. Valid values: 1 (Active), 2 (Inactive), 3 (New Application Request), 4 (Preliminary), 5 (Principal Agent), 6 (Terminated).
             */
            'status' => 'required|integer|in:1,2,3,4,5,6',
            /**
             * @param is_lock The lock status of the agency. Valid values: 0 (Unlocked), 1 (Locked).
             */
            'is_lock' => 'nullable|integer|in:0,1',

        ];
    }

    public function messages()
    {
        return [
            'agency_name.required' => 'Agency name is required',
            'contact_person.required' => 'Contact person is required',
            'agent_code.unique' => 'This agent code is already taken by another agent in your college.',
            'primary_email.required' => 'Primary email is required',
            'primary_email.unique' => 'This email address is already registered with another agent in your college.',
            'telephone.required' => 'Telephone is required',
            'super_agent_id.required' => 'Super agent id is required',
            'office_country.required' => 'Office country is required',
            'office_postcode.required' => 'Office postcode is required',
            'postal_country.required' => 'Postal country is required',
            'postal_postcode.required' => 'Postal postcode is required',
            'target_primary_country.required' => 'Target primary country is required',
            'target_secondry_country.required' => 'Target secondry country is required',
            'status.required' => 'Status is required',
            'status.in' => 'Invalid status value. Supported status values are: 1 (Active), 2 (Inactive), 3 (New Application Request), 4 (Preliminary), 5 (Principal Agent), 6 (Terminated).',
            'is_lock.in' => 'Invalid lock status value. Supported lock status values are: 0 (Unlocked), 1 (Locked).',
        ];
    }
}
