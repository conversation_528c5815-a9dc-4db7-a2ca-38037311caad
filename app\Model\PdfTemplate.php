<?php

namespace App\Model;

use App\Model\v2\StudentCourses;
use App\Traits\OfferLetterNameTrait;
use Auth;
use Config;
use Helpers;
use Illuminate\Database\Eloquent\Model;
use Support\Services\UploadService;

class PdfTemplate extends Model
{
    use OfferLetterNameTrait;

    protected $table = 'rto_pdf_template';

    protected $fillable = [
        'id',
        'college_id',
        'type',
        'default_template',
        'pdf_template',
        'created_at',
        'updated_at',
        'created_by',
        'updated_by',
    ];

    public function savePdfTemplateRecords($collegeId, $request)
    {
        $objPdfTemplate = new PdfTemplate;

        $userId = Auth::user()->id;
        $objPdfTemplate->college_id = $collegeId;

        $type = ($request->input('type') != '') ? $request->input('type') : null;
        $content = ($request->input('editor1') != '') ? $request->input('editor1') : null;

        $objPdfTemplate->type = $type;
        $objPdfTemplate->default_template = $content;
        $objPdfTemplate->pdf_template = $content;
        $objPdfTemplate->created_by = $userId;
        $objPdfTemplate->updated_by = $userId;

        if ($objPdfTemplate->save()) {
            return true;
        }
    }

    public function editPdfTemplateRecords($collegeId, $request, $templateId)
    {

        $userId = Auth::user()->id;
        $type = ($request->input('type') != '') ? $request->input('type') : null;
        $content = ($request->input('editor1') != '') ? $request->input('editor1') : null;

        $objPdfTemplate = PdfTemplate::find($templateId);

        $objPdfTemplate->college_id = $collegeId;
        $objPdfTemplate->type = $type;
        // $objPdfTemplate->default_template = $content;
        $objPdfTemplate->pdf_template = $content;
        $objPdfTemplate->created_by = $userId;
        $objPdfTemplate->updated_by = $userId;
        $objPdfTemplate->created_by = $userId;
        $objPdfTemplate->updated_by = $userId;
        $objPdfTemplate->save();

        return true;
    }

    public function getPdfTemplateListList($college_id, $perPage = 5)
    {

        $arrPdfTemplate = PdfTemplate::select('rto_pdf_template.*')
            ->where('rto_pdf_template.college_id', $college_id)
            ->orderBy('rto_pdf_template.id', 'desc')
            ->paginate(5);

        return $arrPdfTemplate;
    }

    public function editPdfTemplateInfo($userId, $id, $request)
    {
        $objEditPdfTemplate = PdfTemplate::find($id);
        $objEditPdfTemplate->pdf_template = ($request->input('pdf_template') != '') ?
            $request->input('pdf_template') : null;
        $objEditPdfTemplate->updated_by = $userId;
        $objEditPdfTemplate->save();

        return $objEditPdfTemplate;
    }

    public function checkDuplicatePdfTemplateInfo($college_id, $templateId, $request)
    {

        $type = ($request->input('type') != '') ? $request->input('type') : null;
        $countTypePdfTemplate = PdfTemplate::where('id', '<>', $templateId)
            ->where('college_id', $college_id)
            ->where('type', $type)
            ->count();

        return $countTypePdfTemplate;
    }

    public function getPdfTemplateList($collegeId, $data)
    {

        $arrPdfType = Config::get('constants.arrPdfTemplateType');
        $sql = PdfTemplate::where('college_id', '=', $collegeId);

        $result = $sql->get([
            'id',
            'type',
            'updated_at',
        ]);

        foreach ($result as $row) {
            $row->updated_date = date('d-m-Y', strtotime($row->updated_at));
            $row->type = (array_key_exists($row->type, $arrPdfType)) ? $arrPdfType[$row->type] : '';
        }

        return $result;
    }

    public function getPdfTemplateRecords($pdfTemplateId)
    {
        $collegeId = Auth::user()->college_id;
        $data = PdfTemplate::where('college_id', '=', $collegeId)->where('id', $pdfTemplateId)->get();

        return $data;
    }

    public function getPdfTemplateOfDefaultTemplate($college_id, $id)
    {
        $getdefaultTemplateContent = PdfTemplate::where('id', $id)
            ->where('college_id', $college_id)
            ->get(['default_template']);

        return $getdefaultTemplateContent;
    }

    public function editDefaultPdfTemplateRecords($collegeId, $request)
    {
        $id = $request->input('data.id');
        $content = $request->input('data.default_template');
        $userId = Auth::user()->id;

        $objPdfTemplate = PdfTemplate::find($id);
        $objPdfTemplate->default_template = $content;
        $objPdfTemplate->save();

        return true;
    }

    public function deletePdfTemplateRecords($pdfTemplateId)
    {
        $returnPdfTemplate = PdfTemplate::where('id', $pdfTemplateId)
            ->delete();

        return $returnPdfTemplate;
    }

    public function getPdfContent($pdfTemplateId)
    {
        // $collegeId = (!empty(Auth::user())) ? Auth::user()->college_id : Auth::guard('agent')->user()->college_id;
        $collegeId = $this->checkCollegeID();

        return PdfTemplate::where('college_id', '=', $collegeId)
            ->where('type', '=', $pdfTemplateId)
            ->get(['id', 'pdf_template']);
    }

    public function setPdfTemplateBodyContentNew($collegeId, $studentId, $content, $type = 'pdf', $courseId = '', $studentCourseID = '')
    {
        $isHigherEd = false;
        if (! empty($studentCourseID)) {
            $isHigherEd = StudentCourses::checkCourseIsHigherEd($studentCourseID);
        }

        $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
        $arrState = Config::get('constants.arrState');
        $destinationPath = Helpers::changeRootPath($filePath);

        $objRtoCollegeDetails = new CollegeDetails;
        $objCollegeDetails = $objRtoCollegeDetails->getCollegeDetails($collegeId)[0];
        // echo "<pre/>"; print_r($objCollegeDetails); exit();
        $objRtoStudentCourse = new StudentCourse;
        if ($courseId != '' && $studentCourseID != '') {
            $objStudentCourse = $objRtoStudentCourse->getStudentInfoFromStudentCourseId($courseId, $studentId, $studentCourseID);
        } else {
            $objStudentCourse = $objRtoStudentCourse->getStudentOfferLetterInfoFromStudentId($studentId);
        }
        if (count($objStudentCourse) == 0) {
            return ['status' => 'error', 'msg' => 'Student course detail not found'];
        }

        $studentDetails = new StudentDetails;
        $getOHSCCourseId = $studentDetails->getStudentDetailsOfOHSCCourseId($studentId);

        $objRtoStudents = new Students;
        if (isset($getOHSCCourseId) && ! empty($getOHSCCourseId[0])) {
            $objStudentDetails = $objRtoStudents->getStudentDetailsV2($studentId, $getOHSCCourseId[0]);
        } else {
            $objStudentDetails = $objRtoStudents->getStudentDetails($studentId);
        }
        if (count($objStudentDetails) == 0) {
            return ['status' => 'error', 'msg' => 'Student detail not found. OR OHSC have not added course.'];
        }

        $studentCourseIdArr = [];
        $studentCourseTableIdArr = [];
        foreach ($objStudentCourse as $value) {
            $studentCourseIdArr[] = $value['course_id'];
            $studentCourseTableIdArr[] = $value['id'];
        }

        $objRtoOfferUpfrontFeeSchedule = new OfferUpfrontFeeSchedule;
        $arrUpfrontFeeListArr = $objRtoOfferUpfrontFeeSchedule->getUpfrontFeeSchedulewithCourceIn($studentId, $collegeId, $studentCourseTableIdArr);

        // $arrUpfrontFeeListArr = $objRtoOfferUpfrontFeeSchedule->getUpfrontFeeSchedulewithOutCource($studentId, $collegeId); //15-12-2020

        // 09-12-2020 only course id
        // $objRtoOfferUpfrontFeeSchedule = new OfferUpfrontFeeSchedule();
        // $arrUpfrontFeeListArr = $objRtoOfferUpfrontFeeSchedule->getUpfrontFeeScheduleNew($studentCourseID);

        $arrUpfrontFeeLists = [];
        foreach ($arrUpfrontFeeListArr as $key => $value) {
            if (in_array($value['course_id'], $studentCourseIdArr)) {
                $arrUpfrontFeeLists[$value['course_id']][] = $value;
            }
        }

        if ($type == 'preview') {
            $basePath = $destinationPath['view'];
        } else {
            $basePath = $destinationPath['default'];
        }

        $college_logo = '';
        if ($objCollegeDetails['college_logo'] != '') {

            $college_logo_url = UploadService::imageEmbed($destinationPath['view'].$objCollegeDetails['college_logo']);
            $college_logo = '<img src="'.$college_logo_url.'" alt="rto_college_logo.jpg" style="height: auto; width: 85px;padding-left: 35px; padding-top: 20px;"  />';
            // $college_logo_url = str_replace('\\', '/', $destinationPath['view']).$objCollegeDetails['college_logo'];
            // if (file_exists(public_path($college_logo_url))) {
            //     $college_logo = '<img src="data:image/png;base64,'.base64_encode(file_get_contents(public_path($college_logo_url))).'" alt="rto_college_logo.jpg" style="height: auto; width: 85px;padding-left: 35px; padding-top: 20px;"  />';
            // }
        }

        $college_signature = '';

        if ($objCollegeDetails['college_signature'] != '') {

            $college_signature_url = UploadService::imageEmbed($destinationPath['view'].$objCollegeDetails['college_signature']);
            $college_signature = '<img src="'.$college_signature_url.'" alt="rto_college_logo.jpg" style="height: auto; width: 85px;padding-left: 0px; padding-top: 0px;"  />';
            // $college_signature_url = str_replace('\\', '/', $destinationPath['view']).$objCollegeDetails['college_signature'];
            // if (file_exists(public_path($college_signature_url))) {
            //     $college_signature = '<img style="width:70px;" src="data:image/png;base64,'.base64_encode(file_get_contents(public_path($college_signature_url))).'">';
            // }
        }

        // $college_signature     = '<img style="width:70px;" src="'.$college_signature_url.'"></img>';
        $c_url = empty($objCollegeDetails['college_url']) ? '' : trim($objCollegeDetails['college_url']);
        $college_url = '<a href="'.$c_url.'" target="_blank">'.$c_url.'</a>';

        $EPL_istening_score = empty($objStudentDetails[0]->EPL_istening_score) ? '0.00' : $objStudentDetails[0]->EPL_istening_score;
        $EPL_reading_score = empty($objStudentDetails[0]->EPL_reading_score) ? '0.00' : $objStudentDetails[0]->EPL_reading_score;
        $EPL_writing_score = empty($objStudentDetails[0]->EPL_writing_score) ? '0.00' : $objStudentDetails[0]->EPL_writing_score;
        $EPL_speaking_score = empty($objStudentDetails[0]->EPL_speaking_score) ? '0.00' : $objStudentDetails[0]->EPL_speaking_score;
        $total_score = $EPL_istening_score + $EPL_reading_score + $EPL_writing_score + $EPL_speaking_score;
        $ielts_score = empty($objStudentDetails[0]->EPL_overall_score) ? $total_score : $objStudentDetails[0]->EPL_overall_score;

        $student_course_detail_table = '<table class="table-block" cellspacing="0"
        rules="all" border="1" id="GridView1" style="border-collapse:collapse;">
        <thead>
            <tr>
                <th scope="col" style="width: 20%;"> Course </th>
                <th scope="col"> CRICOS Code </th>
                <th scope="col"> Campus Location</th>
                <th scope="col"> Course Period </th>
                <th scope="col"> Course Length </th>
                <th scope="col"> Delivery Medium </th>
                <th scope="col"> Vocational Placement Hours/Work-based Training </th>
                <th scope="col" style="width:15%"> Material Fee</th>
                <th scope="col" style="width:12%"> Tuition Fees</th>
            </tr>
        </thead>
        <tbody>';

        $total_enroll_fee = 0;
        $total_tuition_fee = 0;
        $total_material_fee = 0;
        $oshc_fee = empty($objStudentDetails[0]->OSHC_fee) ? 0 : $objStudentDetails[0]->OSHC_fee;
        $pick_up_fee = empty($objStudentDetails[0]->picup_fee) ? 0 :
            $objStudentDetails[0]->picup_fee;
        $guardian_fee = empty($objStudentDetails[0]->service_fee) ? 0 :
            $objStudentDetails[0]->service_fee;

        $state = (array_key_exists($objCollegeDetails->street_state, $arrState)) ?
            $arrState[$objCollegeDetails->street_state] : '';

        if (count($objStudentCourse) > 0) {
            foreach ($objStudentCourse as $row) {
                $total_enroll_fee = ($total_enroll_fee + $row->enroll_fee);
                $total_tuition_fee = ($total_tuition_fee + $row->course_fee);
                $total_material_fee = ($total_material_fee + $row->course_material_fee);

                $deliveryArr = [];
                $deliveryStr = '';
                if ($row->internal == 'Y') {
                    $deliveryArr[] = 'Blended delivery (Face to Face & Online)';
                }
                if ($row->external == 'Y') {
                    $deliveryArr[] = 'External';
                }
                if ($row->workplace_based_delivery == 'Y') {
                    $deliveryArr[] = 'Workplace-based delivery';
                }

                $camDetail = new CampusVenue;
                $campushDetail = $camDetail->getCampusVenueForOfferLetter($collegeId, $row->campus_id);
                $compushDetailStr = 'No Venue Found';
                if (! empty($campushDetail)) {
                    $compushDetailStr = $campushDetail[0]['vanueDetail'];
                }

                $deliveryStr = implode(' ,', $deliveryArr);

                $student_course_detail_table .= '<tr>
                    <td style="width: 20%;"><span  id="GridView1_ctl02_lblCourseName">'.
                    (empty($row->course_code) ? null : $row->course_code).' '.(empty($row->course_name) ? null :
                        $row->course_name).'</span></td>
                    <td><span>'.(empty($row->cricos_code) ? 'NA' : $row->cricos_code).'</span></td>
                    <td><span>'.$compushDetailStr.'</span></td>
                    <td><span>'.(empty($row->start_date) ? '-' : date('d/m/Y', strtotime($row->start_date))).
                    (empty($row->finish_date) ? '' : (empty($row->start_date) ? '-' : ' - '.
                        date('d/m/Y', strtotime($row->finish_date)))).'</span></td>
                    <td> <span >'.(empty($row->total_weeks) ? 'NA' : $row->total_weeks).' week(s)</span></td>
                    <td> <span> '.(($deliveryStr == '') ? '-' : $deliveryStr).'</span></td>
                    <td> <span>'.(empty($row->work_placement_hour) ? '0' : $row->work_placement_hour).'</span> <span>'.(empty($row->work_placement_component_type) ? '' : $row->work_placement_component_type).'</span></td>
                    <td style="width: 15%;"> <span > '.(empty($row->course_material_fee) ? '-' : '$ '.
                        number_format($row->course_material_fee, 2)).'</span></td>
                    <td style="width: 12%;"> <span >'.(empty($row->course_fee) ? '-' : '$ '.
                        number_format($row->course_fee, 2)).'</span></td>
                </tr>';
            }
        }
        $student_course_detail_table .= '</tbody></table>';

        $student_work_placement_detail_table = '';
        foreach ($objStudentCourse as $row) {
            if ($row->work_placement == 1) {
                $student_work_placement_detail_table .= '<table>
                            <tr>
                                <td><span>The course <span> '.
                    (empty($row->course_code) ? null : $row->course_code).' : '.
                    (empty($row->course_name) ? null : $row->course_name).' '.
                    (empty($row->campus_name) ? null : ' in '.$row->campus_name).'</span>  requires <span>'.
                    (empty($row->work_placement_hour) ? 0 : $row->work_placement_hour).
                    '</span> hours of <span class="yellow">work-based training</span>.</span></td>
                            </tr>
                        </table>';
            }
        }

        $student_initial_payment_required_table = '<table class="infotable" cellspacing="0"
        rules="all" border="1" id="GridView1" style="border-collapse:collapse;">
            <tr style="background-color: #f48024; font-size: 20px;color: white;" >
                <th><span class="font14 white">Initial Payment Required</span></th>
                <th><span class="font14 white">Fee Type</span></th>
                <th><span class="font14 white">Amount</span></th>
            </tr>';
        $initial_payment = 0;
        $later_student_initial_payment_required_table = [];
        foreach ($objStudentCourse as $row) {
            $course_upfront_fee = $row->course_upfront_fee;
            $student_initial_payment_required_table .= '<tr>
                <td>'.(empty($row->course_code) ? null : $row->course_code).' '.
                (empty($row->course_name) ? null : $row->course_name).' - Deposit Fee</td>
                <td>Tuition Tuition  Fees</td>
                <td>'.(empty($row->course_upfront_fee) ? null : '$'.
                    number_format($row->course_upfront_fee, 2)).'</td>
            </tr>';
            if (! empty($row->enroll_fee)) {
                $student_initial_payment_required_table .= '<tr>
                    <td>Enrollment Fees</td>
                    <td>Non-Tuition Fees</td>
                    <td>'.(empty($row->enroll_fee) ? null : '$'.number_format($row->enroll_fee, 2)).'</td>
                </tr>';
            }

            if ($row->is_material_fee_inc_initail_payment) {
                $student_initial_payment_required_table .= '<tr>
                <td>'.(empty($row->course_name) ? null : $row->course_name).' - Material Fee</td>
                <td>Non-Tuition Fees</td>
                <td>'.(empty($row->course_material_fee) ? null : '$'.number_format($row->course_material_fee, 2)).'</td>
                </tr>';

                $initial_payment += $row->enroll_fee + $row->course_upfront_fee + $row->course_material_fee;
            } else {

                $materialFeeStartDate = $row->intake_date;
                if (! empty($arrUpfrontFeeLists[$row->course_id]) && isset($arrUpfrontFeeLists[$row->course_id][0]->installment_start_date)) {
                    $materialFeeStartDate = $arrUpfrontFeeLists[$row->course_id][0]->installment_start_date;
                }
                $later_student_initial_payment_required_table[$row->course_id] = '<tr>
                            <td>'.(empty($materialFeeStartDate) ? null : (date('d/m/Y', strtotime($materialFeeStartDate)))).'</td>
                            <td>'.(empty($row->course_material_fee) ? null : '$'.number_format($row->course_material_fee, 2)).'</td>
                            <td>Non-Tuition Fees - Material Fee</td>
                            </tr>';

                $initial_payment += $row->enroll_fee + $row->course_upfront_fee;
            }

            if ($objStudentDetails[0]->arrange_OSHC === 1) {
                if (! empty($getOHSCCourseId[0])) {
                    if ($row->course_id == $getOHSCCourseId[0]) {
                        $student_initial_payment_required_table .= '<tr>
                                <td> Overseas Student Health Cover (OSHC) - '.
                            (date('d-m-Y', strtotime($objStudentDetails[0]->OSHC_start_date))).' To '.
                            (date('d-m-Y', strtotime($objStudentDetails[0]->OSHC_end_date))).'</td>
                                <td>OSHC</td>
                                <td>'.(empty($objStudentDetails[0]->OSHC_fee) ? null : '$'.
                                number_format($objStudentDetails[0]->OSHC_fee, 2)).'</td>
                            </tr>';
                        $initial_payment += $objStudentDetails[0]->OSHC_fee;
                    }
                }
            }
        }

        $student_initial_payment_required_table .= '<tr>
            <td colspan="2"><b class="red" >TOTAL FEE DUE NOW</b></td>
            <td><b class="red" > $'.(number_format($initial_payment, 2)).'</b></td>
        </tr>
        </table>';

        $student_course_special_condition_table = '<table class="table-block" cellspacing="0"
        rules="all" border="1" id="GridView1" style="border-collapse:collapse;width: 100%">
            <tr>
                <th class="font16">Special Condition/s:</th>
            </tr>';
        foreach ($objStudentCourse as $row) {
            if ($row->application_request == '') {
                $application_request = 'No Special Condition';
            } else {
                $application_str = mb_convert_encoding($row->application_request, 'ISO-8859-1', 'utf-8');
                $application_request = nl2br($application_str);
            }

            $student_course_special_condition_table .= '<tr>
                    <td><b>'.(empty($row->course_code) ? null : $row->course_code).' : '.
                (empty($row->course_name) ? null : $row->course_name).'</b></td>
                </tr>
                <tr>
                    <td>'.$application_request.'</td>
                </tr>';
        }
        $student_course_special_condition_table .= '</table>';

        $student_payment_schedule_table = '';
        if (count($arrUpfrontFeeLists) > 0) {
            foreach ($arrUpfrontFeeLists as $arrUpfrontFeeList) {
                $student_payment_schedule_table .= '<br/><strong>'.(empty($arrUpfrontFeeList[0]->course_code) ? '-' : $arrUpfrontFeeList[0]->course_code).' : '.(empty($arrUpfrontFeeList[0]->course_name) ? null : $arrUpfrontFeeList[0]->course_name).'</strong><table id="Table2" class="table-block" border="0">
                        <thead><tr><td colspan="3" class="font14 bgred">Payment Schedule* Details:</td></tr>';

                $student_payment_schedule_table .= '<tr>
                        <th>DUE DATE</th>
                        <th>AMOUNT</th>
                        <th>FEE TYPE</th>
                    </tr>
                </thead>
                <tbody>';

                if (isset($later_student_initial_payment_required_table[$arrUpfrontFeeList[0]->course_id])) {
                    $student_payment_schedule_table .= $later_student_initial_payment_required_table[$arrUpfrontFeeList[0]->course_id];
                }

                if (count($arrUpfrontFeeList) > 0) {
                    foreach ($arrUpfrontFeeList as $row) {
                        $student_payment_schedule_table .= '<tr>
                            <td>'.(date('d/m/Y', strtotime($row->installment_due_date))).'</td>
                            <td>$'.(number_format($row->installment_amount, 2)).'</td>
                            <td> Tuition Fees </td>
                        </tr>';
                    }
                }
                $student_payment_schedule_table .= '</tbody></table>';
            }
        } else {
            $student_payment_schedule_table .= '<table id="Table2" class="table-block" border="0">
                        <thead><tr><td colspan="3" class="font14 bgred">Payment Schedule* Details:</td></tr><tr>
                <td colspan="3" class="font14">No Schedule Found</td>
            </tr></tbody></table>';
        }

        $data['studentAge'] = '';
        if (! empty($objStudentDetails[0]->DOB)) {
            $studentAge = date_diff(date_create($objStudentDetails[0]->DOB), date_create('today'))->y;
        }

        $student_parent_guardian_table = '';
        if ($studentAge <= 18) {
            $student_parent_guardian_table .= 'If student is below 18 years of age, one of the parent/legal guardian
            needs to sign.<br/>
            <table id="Table1" class="table-block" border="0">
                <thead>
                <tr>
                    <td style="border-width:0px !important;border-bottom: 1px solid !important;
                    border-top: 0px !important;" class="font14"><b>Parent Name:</b>  </td>
                    <td style="border-width:0px !important;border-bottom: 1px solid !important;
                    border-top: 0px !important;" class="font14"><b>Relation With Student:</b> </td>
                </tr>
                <tr>
                    <td style="border-width:0px !important;border-left: none !important;
                    border-right: none !important;" class="font14"> <b>Signed:</b> </td>
                    <td style="border-width:0px !important;border-left: none !important;
                    border-right: none !important;" class="font14"> <b>Dated:</b></td>
                </tr>
                </thead>
            </table>';
        }

        $student_course_detail_with_study_design_2_table = '';
        $student_course_detail_with_study_design_2_table .= '<table class="table-block" cellspacing="0" rules="all" border="1"  style="border-collapse:collapse;width: 100%">
                                <thead>
                                    <tr>
                                        <th scope="col" style="width: 30%;"> Course  </th>
                                        <th scope="col" style="width: 15%;"> CRICOS Code </th>
                                        <th scope="col" style="width: 13%;"> Start Date</th>
                                        <th scope="col" style="width: 12%;"> End Date </th>
                                        <th scope="col" style="width: 20%;" colspan="2" > Course Duration </th>
                                    </tr>
                                </thead>
                                <tbody>';
        if (count($objStudentCourse) > 0) {
            foreach ($objStudentCourse as $row) {
                $camDetail = new CampusVenue;
                $compushDetailStr = 'No Venue Found';
                $campushDetail = $camDetail->getCampusVenueForOfferLetter($collegeId, $row->campus_id);

                if (! empty($campushDetail)) {
                    $compushDetailStr = $campushDetail[0]['vanueDetail'];
                }

                $student_course_detail_with_study_design_2_table .= '<tr>
                                                <td>'.(empty($row->course_code) ? null : $row->course_code).' '.(empty($row->course_name) ? null : $row->course_name).'</td>
                                                <td>'.(empty($row->cricos_code) ? 'NA' : $row->cricos_code).'</td>
                                                <td>'.(empty($row->start_date) ? '-' : date('d/m/Y', strtotime($row->start_date))).'</td>
                                                <td>'.(empty($row->finish_date) ? ' - ' : date('d/m/Y', strtotime($row->finish_date))).'</td>
                                                <td colspan="2">'.(empty($row->total_weeks) ? ' - ' : $row->total_weeks).' Weeks including holidays</td>
                                                </tr>';
                $student_course_detail_with_study_design_2_table .= '<tr>
                                                    <th colspan="2" style="text-align:left;" >Mode of Study</th>
                                                    <td colspan="2" > '.(empty($row->face_to_face_hours) ? '0' : $row->face_to_face_hours).' Hours Face to Face  </td>
                                                    <td colspan="2" style="text-align:left;" > '.(empty($row->online_hours) ? '0' : $row->online_hours).' Hours Online </td>
                                                </tr>
                                                <tr>
                                                    <th colspan="2" style="text-align:left;"> Location of Study </th>
                                                    <td colspan="4"> '.$compushDetailStr.' </td>
                                                </tr>
                                                <tr>
                                                    <th colspan="2" style="text-align:left;"> Prerequisite Information </th>
                                                    <td colspan="4"> Some  courses  may  have  pre-requisite  requirement, please  be  aware  that  student  will  not  be  able  to commence on the course or unit of competency until the pre-requisite is met by the student. </td>
                                                </tr>';
            }
        } else {
            $student_course_detail_with_study_design_2_table .= '<tr><td colspan="6">No record Found</td></tr>';
        }

        $student_course_detail_with_study_design_2_table .= '</tbody></table>';

        $student_course_details_design_2_table = '<table class="table-block" cellspacing="0" rules="all" border="1"  style="border-collapse:collapse;width: 100%">
                                <thead>
                                    <tr>
                                        <th scope="col"> Course  </th>
                                        <th scope="col"> CRICOS Code </th>
                                        <th scope="col"> Start Date</th>
                                        <th scope="col"> End Date </th>
                                        <th scope="col" colspan="2"> Course Duration </th>
                                    </tr>
                                </thead>
                                <tbody>';
        if (count($objStudentCourse) > 0) {
            foreach ($objStudentCourse as $row) {
                $student_course_details_design_2_table .= '<tr>
                                                <td>'.(empty($row->course_code) ? null : $row->course_code).' '.(empty($row->course_name) ? null : $row->course_name).'</td>
                                                <td>'.(empty($row->cricos_code) ? 'NA' : $row->cricos_code).'</td>
                                                <td>'.(empty($row->start_date) ? '-' : date('d/m/Y', strtotime($row->start_date))).'</td>
                                                <td>'.(empty($row->finish_date) ? ' - ' : date('d/m/Y', strtotime($row->finish_date))).'</td>
                                                <td colspan="2">'.(empty($row->total_weeks) ? ' - ' : $row->total_weeks).' Weeks including holidays</td>
                                            </tr>';
            }
        } else {
            $student_course_details_design_2_table .= '<tr><td colspan="8">No record Found</td></tr>';
        }
        $student_course_details_design_2_table .= '</tbody>
                            </table>';
        $later_student_initial_payment_required_table_2 = [];
        $material_fee_in_schedule = [];
        $student_course_fees_and_charges_design_2_table = '<table class="table-block" cellspacing="0" rules="all" border="1" style="border-collapse:collapse;width: 100%">
                                <thead>
                                    <tr>
                                        <th scope="col" style="width: 80%;text-align:left;"> Initial Deposit Fees and Charges </th>
                                        <th scope="col"> Amounts </th>
                                    </tr>
                                </thead>
                                <tbody>';
        $initial_payment = 0;
        $material_fee = $material_fee_course = 0;
        $remaining_payment = 0;
        foreach ($objStudentCourse as $row) {
            $student_course_fees_and_charges_design_2_table .= '<tr>
                                            <th colspan="2" style="text-align:left" >'.(empty($row->course_code) ? null : $row->course_code).' '.(empty($row->course_name) ? null : $row->course_name).'</th></tr>';
            $student_course_fees_and_charges_design_2_table .= '<tr>
                                            <td>Initial Deposit Amount  </td>
                                            <td>'.(empty($row->course_upfront_fee) ? '-' : '$'.number_format($row->course_upfront_fee, 2)).'</td>
                                        </tr>';
            if (! empty($row->enroll_fee)) {
                $student_course_fees_and_charges_design_2_table .= '<tr>
                                                <td>Enrollment Fees</td>
                                                <td>'.(empty($row->enroll_fee) ? '-' : '$'.number_format($row->enroll_fee, 2)).'</td>
                                            </tr>';
            }

            if ($row->is_material_fee_inc_initail_payment) {
                $student_course_fees_and_charges_design_2_table .= '<tr>
                                                    <td>Material Fee</td>
                                                    <td>'.(empty($row->course_material_fee) ? '-' : '$'.number_format($row->course_material_fee, 2)).'</td>
                                                </tr>';
                $material_fee_course = empty($row->course_material_fee) ? '0' : $row->course_material_fee;
                $material_fee += $material_fee_course;
                $intinal_payment = (empty($row->course_upfront_fee)) ? '0' : $row->course_upfront_fee;
                $initial_payment += $row->enroll_fee + $row->course_upfront_fee + $row->course_material_fee;
            } else {
                $intinal_payment = (empty($row->course_upfront_fee)) ? '0' : $row->course_upfront_fee;
                $initial_payment += $row->enroll_fee + $row->course_upfront_fee;

                $materialFeeStartDate = $row->intake_date;
                if (! empty($arrUpfrontFeeLists[$row->course_id]) && isset($arrUpfrontFeeLists[$row->course_id][0]->installment_start_date)) {
                    $materialFeeStartDate = $arrUpfrontFeeLists[$row->course_id][0]->installment_start_date;
                }
                $material_fee_in_schedule[$row->course_id] = $row->course_material_fee;
                $later_student_initial_payment_required_table_2[$row->course_id] = '<tr>
                                                        <td>Non-Tuition Fees - Material Fee</td>
                                                        <td>'.(empty($materialFeeStartDate) ? null : (date('d/m/Y', strtotime($materialFeeStartDate)))).'</td>
                                                        <td>'.(empty($row->course_material_fee) ? '$'.number_format(0, 2) : '$'.number_format($row->course_material_fee, 2)).'</td>
                                                        </tr>';
                $material_fee_course = empty($row->course_material_fee) ? '0' : $row->course_material_fee;
                $material_fee += $material_fee_course;
            }

            $remaining_payment = $row->course_fee - $initial_payment;
        }
        foreach ($objStudentCourse as $row) {
            if ($objStudentDetails[0]->arrange_OSHC === 1) {
                if (! empty($getOHSCCourseId[0])) {
                    if ($row->course_id == $getOHSCCourseId[0]) {
                        $student_course_fees_and_charges_design_2_table .= '<tr>
                                                    <th colspan="2" style="text-align:left" >OSHC</th></tr><tr>
                                                        <td> Overseas Student Health Cover (OSHC) - '.(date('d-m-Y', strtotime($objStudentDetails[0]->OSHC_start_date))).' To '.(date('d-m-Y', strtotime($objStudentDetails[0]->OSHC_end_date))).'</td>
                                                        <td>'.(empty($objStudentDetails[0]->OSHC_fee) ? null : '$'.number_format($objStudentDetails[0]->OSHC_fee, 2)).'</td>
                                                    </tr>';
                        $initial_payment += $objStudentDetails[0]->OSHC_fee;
                    }
                }
            }
        }

        $student_course_fees_and_charges_design_2_table .= '<tr>
                                        <td> <strong> Total Amount Intial Deposit <strong></td>
                                        <td  style="width: 20%;">$'.(number_format($initial_payment, 2)).'</td>
                                    </tr>
                                </tbody>
                            </table>';

        $student_payment_schedule_design_2_table = '';
        $student_course_payment_schedule_fee = $student_course_payment_schedule_fee_course = 0;
        $total_fee_payble_by_student = $total_fee_payble_by_student_course = 0;

        if (count($arrUpfrontFeeLists) > 0) {

            $student_payment_schedule_design_2_table .= '<table class="table-block" cellspacing="0" rules="all" border="1"  style="border-collapse:collapse;width: 100%">
                                        <thead>
                                            <tr>
                                                <th scope="col" style="text-align:left"> Payment Schedule  </th>
                                                <th scope="col"  style="width: 15%;"> Due Date </th>
                                                <th scope="col" style="width: 15%;"> Amount </th>
                                            </tr>
                                        </thead>
                                        <tbody>';

            foreach ($arrUpfrontFeeLists as $arrUpfrontFeeList) {
                $student_payment_schedule_design_2_table .= '<tr>
                                                        <th colspan="3" style="text-align:left" ><strong>'.(empty($arrUpfrontFeeList[0]->course_code) ? '-' : $arrUpfrontFeeList[0]->course_code).' : '.(empty($arrUpfrontFeeList[0]->course_name) ? '-' : $arrUpfrontFeeList[0]->course_name).'</strong></th></tr>';

                $student_course_payment_schedule_fee_course = 0;

                if (isset($later_student_initial_payment_required_table_2[$arrUpfrontFeeList[0]->course_id])) {
                    $student_payment_schedule_design_2_table .= $later_student_initial_payment_required_table_2[$arrUpfrontFeeList[0]->course_id];
                    if (isset($material_fee_in_schedule[$arrUpfrontFeeList[0]->course_id])) {
                        $student_course_payment_schedule_fee_course = $material_fee_in_schedule[$arrUpfrontFeeList[0]->course_id];
                    }
                }

                if (count($arrUpfrontFeeList) > 0) {
                    $semCount = 1;
                    foreach ($arrUpfrontFeeList as $row) {
                        $student_payment_schedule_design_2_table .= '<tr>
                                                        <td> Tuition Fees '.(($isHigherEd) ? "(Semester - $semCount)" : '').'</td>
                                                        <td style="width: 15%;" >'.(date('d/m/Y', strtotime($row->installment_due_date))).'</td>
                                                        <td style="width: 15%;">$'.(number_format($row->installment_amount, 2)).'</td>
                                                    </tr>';
                        $student_course_payment_schedule_fee_course = $student_course_payment_schedule_fee_course + $row->installment_amount;
                        $semCount++;
                    }
                    $student_course_payment_schedule_fee += $student_course_payment_schedule_fee_course;
                    $student_payment_schedule_design_2_table .= '<tr>
                                                    <td colspan="2"> <strong> Total Remaining  Amount <strong>  </td>
                                                    <td  style="width: 15%;"> <strong>$'.(number_format($student_course_payment_schedule_fee_course, 2)).'</strong> </td>
                                                </tr>';

                    $total_fee_payble_by_student_course = $student_course_payment_schedule_fee_course + $initial_payment;
                    $total_fee_payble_by_student += $total_fee_payble_by_student_course;
                }
            }
        } else {
            $student_payment_schedule_design_2_table .= '<table class="table-block" cellspacing="0" rules="all" border="1"  style="border-collapse:collapse;width: 100%">
                                        <thead>
                                            <tr>
                                                <th scope="col"> Payment Schedule  </th>
                                                <th scope="col"  style="width: 15%;"> Due Date </th>
                                                <th scope="col" style="width: 15%;"> Amount </th>
                                            </tr>
                                        </thead>
                                        <tbody><tr>
                                            <td colspan="3" class="font14">No Schedule Found</td>
                                        </tr>';
        }
        $student_payment_schedule_design_2_table .= '</tbody></table>';

        $total_fee_payble_by_student = $student_course_payment_schedule_fee + $initial_payment;
        $student_course_total_tuition_fees_domestic_table = '<table class="table-block" cellspacing="0" rules="all" border="1" style="border-collapse:collapse;width: 100%">
                                <thead>
                                    <tr>
                                        <th scope="col" style="width: 80%;"> Total Tuition Fees  </th>
                                        <th scope="col"> Amounts </th>
                                    </tr>
                                </thead>
                                <tbody>';

        $initial_payment_domestic1 = $remaining_payment = $remaining_payment_course = 0;
        foreach ($objStudentCourse as $row) {
            $initial_payment_domestic1 = 0;
            $student_course_total_tuition_fees_domestic_table .= '<tr>
                                        <td>'.(empty($row->course_code) ? null : $row->course_code).' '.(empty($row->course_name) ? null : $row->course_name).' - Deposit Fee1</td>
                                        <td>'.(empty($row->course_upfront_fee) ? '-' : '$'.number_format($row->course_upfront_fee, 2)).'</td>
                                    </tr>';

            $initial_payment_domestic1 += (empty($row->course_upfront_fee)) ? '0' : $row->course_upfront_fee;
            $remaining_payment_course = $row->course_fee - $initial_payment_domestic1;
            $remaining_payment += $remaining_payment_course;
        }

        $student_course_total_tuition_fees_domestic_table .= '<tr>
                                        <td> <strong> Total Amount (Incl. Material Fees) <strong></td>
                                        <td  style="width: 20%;">$'.(number_format($initial_payment_domestic1, 2)).'</td>
                                    </tr>
                                </tbody>
                            </table>';

        $student_course_total_tuition_fees_domestic_table = '<table class="table-block" cellspacing="0" rules="all" border="1" style="border-collapse:collapse;width: 100%">
                                <thead>
                                    <tr>
                                        <th scope="col" style="width: 80%;"> Total Tuition Fees  </th>
                                        <th scope="col"> Amounts </th>
                                    </tr>
                                </thead>
                                <tbody>';
        $total_tuition_fees = 0;

        foreach ($objStudentCourse as $row) {
            $student_course_total_tuition_fees_domestic_table .= '<tr>
                                        <td>'.(empty($row->course_code) ? null : $row->course_code).' '.(empty($row->course_name) ? null : $row->course_name).'</td>
                                        <td>'.(empty($row->course_fee) ? '-' : '$'.number_format($row->course_fee, 2)).'</td>
                                    </tr>';
            $total_tuition_fees += (empty($row->course_fee)) ? '0' : $row->course_fee;
        }

        $student_course_total_tuition_fees_domestic_table .= '<tr>
                                        <td> <strong> Total Amount Due AUS$ : <strong></td>
                                        <td  style="width: 20%;">$'.(number_format($total_tuition_fees, 2)).'</td>
                                    </tr>
                                </tbody>
                            </table>';

        $student_course_initial_payment_required_domestic_table = '<table class="table-block" cellspacing="0" rules="all" border="1" style="border-collapse:collapse;width: 100%">
                                <thead>
                                    <tr>
                                        <th scope="col" style="width: 80%;"> Initial Payment Required   </th>
                                        <th scope="col"> Amounts </th>
                                    </tr>
                                </thead>
                                <tbody>';
        $initial_payment_domestic = 0;
        foreach ($objStudentCourse as $row) {
            $student_course_initial_payment_required_domestic_table .= '<tr>
                                        <td>'.(empty($row->course_code) ? null : $row->course_code).' '.(empty($row->course_name) ? null : $row->course_name).' - Deposit Fee1</td>
                                        <td>'.(empty($row->course_upfront_fee) ? '-' : '$'.number_format($row->course_upfront_fee, 2)).'</td>
                                    </tr>';
            $initial_payment_domestic += (empty($row->course_upfront_fee)) ? '0' : $row->course_upfront_fee;
        }

        $student_course_initial_payment_required_domestic_table .= '<tr>
                                        <td> <strong> TOTAL FEE DUE NOW <strong></td>
                                        <td  style="width: 20%;">$'.(number_format($initial_payment_domestic, 2)).'</td>
                                    </tr>
                                </tbody>
                            </table>';

        if (! empty($objCollegeDetails)) {

            $dataArr = [
                '{college_name}' => empty($objCollegeDetails['college_name']) ? null : $this->convertStringToISO($objCollegeDetails['college_name']),
                '{college_legal_name}' => empty($objCollegeDetails['legal_name']) ? null : $this->convertStringToISO($objCollegeDetails['legal_name']),
                '{college_ABN}' => empty($objCollegeDetails['ABN']) ? null : $this->convertStringToISO($objCollegeDetails['ABN']),
                '{college_CRICOS_code}' => $this->convertStringToISO($objCollegeDetails['CRICOS_code']),
                '{college_RTO_code}' => $this->convertStringToISO($objCollegeDetails['RTO_code']),
                '{college_street_address}' => $this->convertStringToISO($objCollegeDetails['street_address']),
                '{college_street_suburb}' => $this->convertStringToISO($objCollegeDetails['street_suburb']),
                '{college_street_state}' => (array_key_exists($objCollegeDetails['street_state'], $arrState)) ? $arrState[$objCollegeDetails['street_state']] : '',
                '{college_street_postcode}' => $this->convertStringToISO($objCollegeDetails['street_postcode']),
                '{college_contact_phone}' => $this->convertStringToISO($objCollegeDetails['contact_phone']),
                '{college_contact_email}' => $this->convertStringToISO($objCollegeDetails['contact_email']),
                '{college_account_email}' => $this->convertStringToISO($objCollegeDetails['account_email']),
                '{college_logo}' => $college_logo,
                '{college_signature}' => $college_signature,
                '{college_url}' => $college_url,
                '{application_reference_id}' => ($objStudentDetails[0]->generated_stud_id) ? $objStudentDetails[0]->generated_stud_id : $this->convertStringToISO($objStudentDetails[0]->application_reference_id),
                '{current_date}' => date('d F Y'),
                '{issued_date}' => date('d-m-Y', strtotime($objStudentDetails[0]->issued_date)),
                '{student_name_title}' => $this->convertStringToISO($objStudentDetails[0]->name_title),
                '{student_first_name}' => $this->convertStringToUTF($objStudentDetails[0]->first_name),
                '{student_family_name}' => $this->convertStringToUTF($objStudentDetails[0]->family_name),
                '{student_gender}' => $this->convertStringToISO($objStudentDetails[0]->gender),
                '{student_phone}' => $this->convertStringToISO($objStudentDetails[0]->current_mobile_phone),
                '{student_date_of_birth}' => date('d/m/Y', strtotime($objStudentDetails[0]->DOB)),
                '{nationality_of_student}' => $this->convertStringToISO($objStudentDetails[0]->nationality_of_student),
                '{student_current_street_no}' => $this->convertStringToUTF($objStudentDetails[0]->current_street_no),
                '{student_current_street_name}' => $this->convertStringToUTF($objStudentDetails[0]->current_street_name),
                '{student_current_city}' => $this->convertStringToUTF($objStudentDetails[0]->current_city),
                '{student_current_state}' => $this->convertStringToUTF($objStudentDetails[0]->current_state),
                '{student_current_postcode}' => $this->convertStringToISO($objStudentDetails[0]->current_postcode),
                '{student_country_name}' => $this->convertStringToISO($objStudentDetails[0]->country_name),
                '{student_country_of_birth}' => $this->convertStringToISO($objStudentDetails[0]->country_of_birth),
                '{student_emergency_contact_person}' => $this->convertStringToISO($objStudentDetails[0]->emergency_contact_person),
                '{student_emergency_relationship}' => $this->convertStringToISO($objStudentDetails[0]->emergency_relationship),
                '{student_emergency_address}' => $this->convertStringToISO($objStudentDetails[0]->emergency_address),
                '{student_emergency_phone}' => $this->convertStringToISO($objStudentDetails[0]->emergency_phone),
                '{student_emergency_email}' => $this->convertStringToISO($objStudentDetails[0]->emergency_email),
                '{student_subject_calming_for_credit}' => ($objStudentCourse[0]->subject_clming_for_credit) ? $this->convertStringToISO($objStudentCourse[0]->subject_clming_for_credit) : 'NA',
                '{student_scholarship_percentage}' => ($objStudentCourse[0]->scholarship_percentage) ? $this->convertStringToISO($objStudentCourse[0]->scholarship_percentage) : '0',
                '{student_wil_requirements}' => ($objStudentCourse[0]->wil_requirements) ? $this->convertStringToISO($objStudentCourse[0]->wil_requirements) : 'NA',
                '{student_third_party_providers}' => ($objStudentCourse[0]->third_party_providers) ? $this->convertStringToISO($objStudentCourse[0]->third_party_providers) : 'NA',
                '{student_course_fee_after_scholarship}' => '$'.(number_format($this->findCourseFeeAfterDiscount($objStudentCourse), 2)),
                '{student_course_fee_after_scholarship_and_initial_payment}' => '$'.(number_format($this->findCourseFeeAfterDiscountMinusInit($objStudentCourse), 2)),
                '{course_duration_after_advance_standing}' => $this->findCourseDurationAfterAdvanceStanding($objStudentCourse).' Weeks',
                '{student_passport_no}' => $this->convertStringToISO($objStudentDetails[0]->passport_no),
                '{student_full_name}' => $this->convertStringToISO($objStudentDetails[0]->first_name.' '.$objStudentDetails[0]->family_name),
                '{student_address}' => $this->convertStringToISO($objStudentDetails[0]->current_street_no.' '.$objStudentDetails[0]->current_street_name.', '.$objStudentDetails[0]->current_city.', '.$objStudentDetails[0]->current_state.' '.$objStudentDetails[0]->current_postcode.', '.$objStudentDetails[0]->country_name),
                '{student_course_code_and_name}' => (count($objStudentCourse) > 0) ? ($objStudentCourse[0]->course_code.' : '.$objStudentCourse[0]->course_name) : '',
                '{student_course_start_date}' => (count($objStudentCourse) > 0) ? date('d/m/Y', strtotime($objStudentCourse[0]->start_date)) : '',
                '{student_course_finish_date}' => (count($objStudentCourse) > 0) ? date('d/m/Y', strtotime($objStudentCourse[0]->finish_date)) : '',
                '{student_course_total_fee}' => (count($objStudentCourse) > 0) ? '$'.(number_format($objStudentCourse[0]->course_fee, 2)) : '',
                '{student_course_enroll_fee}' => (count($objStudentCourse) > 0) ? '$'.(number_format($objStudentCourse[0]->enroll_fee, 2)) : '',
                '{student_course_material_fee}' => (count($objStudentCourse) > 0) ? '$'.(number_format($objStudentCourse[0]->course_material_fee, 2)) : '',
                '{student_course_upfront_fee}' => (count($objStudentCourse) > 0) ? '$'.(number_format($objStudentCourse[0]->course_upfront_fee, 2)) : '',
                '{offer_issue_date_after_X_days}' => (count($objStudentCourse) > 0) ? date('d/m/Y', strtotime($objStudentCourse[0]->issued_date.' +28 days')) : '',
                '{offer_issue_date_of_first_course}' => (count($objStudentCourse) > 0) ? date('d/m/Y', strtotime($objStudentCourse[0]->issued_date)) : '',
                '{student_course_duration}' => (count($objStudentCourse) > 0) ? ((empty($objStudentCourse[0]->total_weeks) ? ' - ' : $objStudentCourse[0]->total_weeks)) : '',
                '{student_course_period}' => (count($objStudentCourse) > 0) ? ((empty($objStudentCourse[0]->total_weeks) ? ' - ' : $objStudentCourse[0]->total_weeks).'Weeks including holidays') : '',
                '{total_tuition_fee}' => '$'.(number_format($total_tuition_fee, 2)),
                '{ielts_score}' => (number_format($ielts_score, 2)),
                '{student_course_detail_table}' => $student_course_detail_table,
                '{initial_payment}' => '$'.(number_format($initial_payment, 2)),
                '{remaining_payment}' => '$'.(number_format($remaining_payment, 2)),
                '{college_bank_name}' => $this->convertStringToISO($objCollegeDetails['bank_name']),
                '{college_bank_account_name}' => $this->convertStringToISO($objCollegeDetails['bank_account_name']),
                '{college_bank_BSB}' => $this->convertStringToISO($objCollegeDetails['bank_BSB']),
                '{college_bank_account_number}' => $this->convertStringToISO($objCollegeDetails['bank_account_number']),
                '{student_offer_id}' => $objStudentDetails[0]->offer_id,
                '{college_bank_swift_code}' => $this->convertStringToISO($objCollegeDetails['bank_swift_code']),
                '{college_bank_branch}' => $this->convertStringToISO($objCollegeDetails['bank_branch']),
                '{student_payment_schedule_table}' => $student_payment_schedule_table,
                '{student_parent_guardian_table}' => $student_parent_guardian_table,
                '{student_email}' => $objStudentDetails[0]->email,
                '{total_fee_payble_by_student}' => '$'.(number_format($total_fee_payble_by_student, 2)),
                '{student_work_placement_detail_table}' => $student_work_placement_detail_table,
                '{student_initial_payment_required_table}' => $student_initial_payment_required_table,
                '{student_course_special_condition_table}' => $student_course_special_condition_table,
                '{student_course_detail_with_study_design_2_table}' => $student_course_detail_with_study_design_2_table,
                '{student_course_detail_with_study_design_3_table}' => $this->getStudentCourseDetailWithStudyDesign3Table($objStudentCourse, $collegeId),
                '{student_course_details_design_2_table}' => $student_course_details_design_2_table,
                '{student_course_fees_and_charges_design_2_table}' => $student_course_fees_and_charges_design_2_table,
                '{student_payment_schedule_design_2_table}' => $student_payment_schedule_design_2_table,
                '{material_fee}' => '$'.(number_format($material_fee, 2)),
                '{student_course_payment_schedule_fee}' => '$'.(number_format($student_course_payment_schedule_fee, 2)),
                '{student_course_total_tuition_fees_domestic_table}' => $student_course_total_tuition_fees_domestic_table,
                '{student_course_initial_payment_required_domestic_table}' => $student_course_initial_payment_required_domestic_table,

            ];

            foreach ($dataArr as $key => $value) {
                $content = str_replace("$key", $value, $content);
            }

            return $content;
        } else {
            return ['status' => 'error', 'msg' => 'Some thing will be wrong. Please try again.'];
        }
    }

    public function getStudentCourseDetailWithStudyDesign3Table($objStudentCourse, $collegeId)
    {
        $student_course_detail_with_study_design_3_table = '';
        $compushDetailStr = 'No Venue Found';
        $student_work_placement_detail_table = '';
        $application_request = '';
        $student_course_detail_with_study_design_3_table .= '<table class="table-block" cellspacing="0" rules="all" border="1"  style="border-collapse:collapse;width: 100%">
                                <thead>
                                    <tr>
                                        <th scope="col" style="width: 30%;"> Course Code & Title </th>
                                        <th scope="col" style="width: 15%;"> CRICOS Code </th>
                                        <th scope="col" style="width: 13%;"> Start Date</th>
                                        <th scope="col" style="width: 12%;"> End Date </th>
                                        <th scope="col" style="width: 20%;" > Course Duration </th>
                                        <th scope="col" style="width: 20%;" > Tuition Fees </th>
                                        <th scope="col" style="width: 20%;" > Material Fee </th>
                                    </tr>
                                </thead>
                                <tbody>';
        if (count($objStudentCourse) > 0) {
            foreach ($objStudentCourse as $row) {
                $camDetail = new CampusVenue;

                $campushDetail = $camDetail->getCampusVenueForOfferLetter($collegeId, $row->campus_id);

                if (! empty($campushDetail)) {
                    $compushDetailStr = $campushDetail[0]['vanueDetail'];
                }

                $student_course_detail_with_study_design_3_table .= '<tr>
                                                <td>'.(empty($row->course_code) ? null : $row->course_code).' '.(empty($row->course_name) ? null : $row->course_name).'</td>
                                                <td>'.(empty($row->cricos_code) ? 'NA' : $row->cricos_code).'</td>
                                                <td>'.(empty($row->start_date) ? '-' : date('d/m/Y', strtotime($row->start_date))).'</td>
                                                <td>'.(empty($row->finish_date) ? ' - ' : date('d/m/Y', strtotime($row->finish_date))).'</td>
                                                <td>'.(empty($row->total_weeks) ? ' - ' : $row->total_weeks).' Weeks</td>
                                                <td>'.(empty($row->course_fee) ? ' - ' : '$'.number_format($row->course_fee, 2)).'</td>
                                                <td>'.(empty($row->course_material_fee) ? ' - ' : '$'.number_format($row->course_material_fee, 2)).'</td>
                                                </tr>';
                if ($row->work_placement == 1) {
                    $student_work_placement_detail_table .= '
                                                                <span> '.
                        (empty($row->course_code) ? null : $row->course_code).' : '.
                        (empty($row->course_name) ? null : $row->course_name).' '
                        .'This course includes work placement hours of '.
                        (empty($row->work_placement_hour) ? 0 : $row->work_placement_hour).
                        '('.
                        (empty($row->maximum_weekly_study) ? 0 : $row->maximum_weekly_study).' hours per week over '.
                        (empty($row->vocational_duration) ? 0 : $row->vocational_duration).' weeks'.
                        ').</span><br>';
                }
                if ($row->application_request == '') {
                    $application_request = 'No Special Condition';
                } else {
                    $application_str = mb_convert_encoding($row->application_request, 'ISO-8859-1', 'utf-8');
                    $application_request .= nl2br($application_str).'<br>';
                }
            }
            $student_course_detail_with_study_design_3_table .= '<tr>
                                                    <th colspan="2" style="text-align:left;">Course delivery location: </th>
                                                    <td colspan="5"> '.$compushDetailStr.' </td>
                                                </tr>';

            $student_course_detail_with_study_design_3_table .= '<tr>
                                                    <th colspan="2" style="text-align:left;" >Mode of Study:</th>
                                                    <td colspan="5">
                                                            <ul>
                                                                <li>Face to Face mode in classroom</li>
                                                                <li>Structured distance learning mode</li>
                                                                <li>Mandatory work placement in an approved work placement venue</li>
                                                            </ul>
                                                     </td>
                                                </tr>';

            $student_course_detail_with_study_design_3_table .= '<tr>
                                                    <th colspan="2" style="text-align:left;"> Work Placement: </th>
                                                    <td colspan="5"> '.$student_work_placement_detail_table.' </td>
                                                </tr>';

            $student_course_detail_with_study_design_3_table .= '<tr>
                                                    <th colspan="2" style="text-align:left;"> Conditions of offer: </th>
                                                    <td colspan="5"> '.$application_request.' </td>
                                                </tr>';
        } else {
            $student_course_detail_with_study_design_3_table .= '<tr><td colspan="6">No record Found</td></tr>';
        }

        $student_course_detail_with_study_design_3_table .= '</tbody></table>';

        return $student_course_detail_with_study_design_3_table;
    }

    private function findCourseFeeAfterDiscount($objStudentCourse)
    {
        $totalFee = 0;
        if (! empty($objStudentCourse)) {
            foreach ($objStudentCourse as $course) {
                $courseFee = empty($course->course_fee) ? 0 : $course->course_fee;
                $scholarshipPercentage = empty($course->scholarship_percentage) ? 0 : $course->scholarship_percentage;

                if ($scholarshipPercentage > 0) {
                    $discountAmount = ($courseFee * $scholarshipPercentage) / 100;
                    $courseFee = $courseFee - $discountAmount;
                }

                $totalFee += $courseFee;
            }
        }

        return $totalFee;
    }

    private function findCourseFeeAfterDiscountMinusInit($objStudentCourse)
    {
        $totalFee = 0;
        if (! empty($objStudentCourse)) {
            foreach ($objStudentCourse as $course) {
                $courseFee = empty($course->course_fee) ? 0 : $course->course_fee;
                $scholarshipPercentage = empty($course->scholarship_percentage) ? 0 : $course->scholarship_percentage;
                $initialPayment = empty($course->course_upfront_fee) ? 0 : $course->course_upfront_fee;

                // Apply scholarship discount
                if ($scholarshipPercentage > 0 && $course->is_receiving_any_scholarship == 'yes') {
                    $discountAmount = ($courseFee * $scholarshipPercentage) / 100;
                    $courseFee = $courseFee - $discountAmount;
                }

                // Subtract initial payment
                $courseFee = $courseFee - $initialPayment;

                $totalFee += $courseFee;
            }
        }

        return $totalFee;
    }

    private function findCourseDurationAfterAdvanceStanding($objStudentCourse)
    {
        if (empty($objStudentCourse)) {
            return 0;
        }

        $totalWeeks = 0;
        foreach ($objStudentCourse as $course) {
            $subjectCredits = empty($course->subject_clming_for_credit) ? 0 : $course->subject_clming_for_credit;
            $courseWeeks = empty($course->total_weeks) ? 0 : $course->total_weeks;

            // Calculate reduced weeks based on subject credits
            if ($subjectCredits < 4) {
                // 4 subjects = reduce by 6 months (26 weeks)
                $courseWeeks = max(0, $courseWeeks - 0);
            } elseif ($subjectCredits >= 4 && $subjectCredits < 8) {
                // 8 subjects = reduce by 1 year (52 weeks)
                $courseWeeks = max(0, $courseWeeks - 26);
            } elseif ($subjectCredits == 8) {
                // 8 subjects = reduce by 1 year (52 weeks)
                $courseWeeks = max(0, $courseWeeks - 52);
            }

            $totalWeeks += $courseWeeks;
        }

        return $totalWeeks;
    }

    private function checkCollegeID()
    {
        $college_id = '';
        if (! empty(Auth()->guard('agent')->user())) {
            $college_id = Auth()->guard('agent')->user()->college_id;
        }
        if (! empty(Auth()->guard('agentstaff')->user())) {
            $college_id = Auth()->guard('agentstaff')->user()->college_id;
        }
        if (! empty(Auth::user())) {
            $college_id = Auth::user()->college_id;
        }

        return $college_id;
    }

    public function scopeFilterQuery(
        $query,
        $value
    ) {
        $arrPdfType = Config::get('constants.arrPdfTemplateType');

        // Find all type keys where the label matches partially
        $matchingTypes = collect($arrPdfType)
            ->filter(function ($label) use ($value) {
                return stripos($label, $value) !== false;
            })
            ->keys()
            ->toArray();

        if (empty($matchingTypes)) {
            return $query->whereRaw('0 = 1'); // No match
        }

        return $query->whereIn('type', $matchingTypes);
    }

    public function scopeCollegeId(
        $query,
        $value
    ) {
        if (empty($value)) {
            return $query;
        }

        return $query->where('college_id', $value);
    }
}
