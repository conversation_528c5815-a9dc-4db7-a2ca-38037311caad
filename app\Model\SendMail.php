<?php

namespace App\Model;

use App\Model\v2\FailedEmails;
use App\Model\v2\SmtpSetup;
use App\Traits\CommonTrait;
use Helpers;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Mail;
use Illuminate\Support\Facades\Validator;
use Illuminate\Support\Facades\View;
use Support\Services\UploadService;

class SendMail extends Model
{
    use CommonTrait;

    private function getLoginUserCollegeId()
    {
        if (! empty(Auth()->guard('student')->user())) {
            $this->loginUser = Auth()->guard('student')->user();
        }
        if (! empty(Auth()->guard('teacher')->user())) {
            $this->loginUser = Auth()->guard('teacher')->user();
        }
        if (! empty(Auth()->guard('agent')->user())) {
            $this->loginUser = Auth()->guard('agent')->user();
        }
        if (! empty(Auth::user())) {
            $this->loginUser = Auth::user();
        }
        $collegeId = (isset($this->loginUser->college_id) ? $this->loginUser->college_id : 0);

        return $collegeId;
    }

    public function sendSmtpMail($mailData)
    {

        $collegeId = $this->getLoginUserCollegeId();
        // $mail = SmtpSetup::get()->first();
        // $mailData['from'] = $mail->username;
        // $validator = Validator::make(['email' => $mailData['from']],[
        //     'email' => 'required|email'
        // ]);

        // if(!$validator->passes()){
        // $mailData['from'] = $mail->email;
        // }
        // $mailData['fromName'] = $mail->name;

        $mail = SmtpSetup::getSMTPDetail();
        if ($mail && $mail->status) {
            $mailData['from'] = $mail->username;
            $validator = Validator::make(['email' => $mailData['from']], [
                'email' => 'required|email',
            ]);

            if (! $validator->passes()) {
                $mailData['from'] = $mail->email;
            }
            $mailData['fromName'] = $mail->name;

            // Set SMTP configuration
            $this->setSMTP();
        } else {
            // Fallback to .env mail settings
            $mailData['from'] = config('mail.from.address');
            $mailData['fromName'] = config('mail.from.name');
        }

        $page = (isset($mailData['page']) && ! empty($mailData['page'])) ? $mailData['page'] : [];
        $data = (isset($mailData['data']) && ! empty($mailData['data'])) ? $mailData['data'] : [];
        try {
            Mail::send($page, $data, function ($message) use ($mailData) {

                $message->from(($mailData['from'] = ! '' ? $mailData['from'] : '<EMAIL>'), $mailData['fromName']);
                $message->to($mailData['to']);

                if (isset($mailData['replyTo']) && ! empty($mailData['replyTo'])) {
                    $message->replyTo($mailData['replyTo']);
                }

                if (isset($mailData['cc']) && ! empty($mailData['cc'])) {
                    $message->cc($mailData['cc']);
                }

                if (isset($mailData['bcc']) && ! empty($mailData['bcc'])) {
                    $message->bcc($mailData['bcc']);
                }

                $message->subject($mailData['subject']);

                // If blade template set as page then not using body parameter in mailData
                if (isset($mailData['body']) && ! empty($mailData['body'])) {
                    // $message->setBody($mailData['body'], 'text/html');
                    $message->html($mailData['body']);
                }

                if (isset($mailData['attachFile']) && ! empty($mailData['attachFile'])) {

                    for ($i = 0; $i < count($mailData['attachFile']); $i++) {
                        $attachFile = UploadService::inlineAttachment($mailData['attachFile'][$i]);

                        $message->attachData($attachFile->data, $attachFile->name, [
                            'mime' => $attachFile->mime,
                            'contentId' => @$attachFile->contentId,
                        ]);
                    }
                }
            });

            return 'success';

        } catch (\Symfony\Component\Mime\Exception\RfcComplianceException $exception) {

            $errorMessage = $exception->getMessage();
            $errorCode = $exception->getCode();
            if (isset($mailData['page'])) {
                $contentText = View::make($mailData['page'], $mailData['data'])->render();
            } else {
                $contentText = (isset($mailData['data']['content'])) ? $mailData['data']['content'] : (isset($mailData['body']) ? $mailData['body'] : '');

                if ($contentText == '') {
                    $contentText = $mailData['body'];
                }
            }
            // Generate proper error message for invalid email addresses
            $invalidEmails = [];

            // Check 'to' field
            if (! filter_var($mailData['to'], FILTER_VALIDATE_EMAIL)) {
                $invalidEmails[] = $mailData['to'];
            }

            // Check 'cc' field if it exists and is not empty
            if (isset($mailData['cc']) && ! empty($mailData['cc'])) {
                if (is_array($mailData['cc'])) {
                    foreach ($mailData['cc'] as $ccEmail) {
                        if (! filter_var($ccEmail, FILTER_VALIDATE_EMAIL)) {
                            $invalidEmails[] = $ccEmail;
                        }
                    }
                } else {
                    // If cc is a string, check it as well
                    if (! filter_var($mailData['cc'], FILTER_VALIDATE_EMAIL)) {
                        $invalidEmails[] = $mailData['cc'];
                    }
                }
            }

            // Create error message
            $errorMessage = 'Invalid email address(es): '.implode(', ', $invalidEmails).'.';

            $arrFailMail = [
                'sender' => ($mailData['from'] != '') ? $mailData['from'] : '<EMAIL>',
                'receiver' => $mailData['to'],
                'subject' => $mailData['subject'],
                'content' => $contentText,
                'error_message' => $errorMessage,
                'error_code' => $errorCode,
            ];
            (new FailedEmails)->saveFailedEmails($collegeId, $arrFailMail);

            return false;
        } catch (\Exception  $exception) {

            $errorMessage = $exception->getMessage();
            $errorCode = $exception->getCode();
            if (isset($mailData['page'])) {
                $contentText = View::make($mailData['page'], $mailData['data'])->render();
            } else {
                $contentText = (isset($mailData['data']['content'])) ? $mailData['data']['content'] : (isset($mailData['body']) ? $mailData['body'] : '');
            }
            $arrFailMail = [
                'sender' => ($mailData['from'] != '') ? $mailData['from'] : '<EMAIL>',
                'receiver' => $mailData['to'],
                'subject' => $mailData['subject'],
                'content' => $contentText,
                'error_message' => $errorMessage,
                'error_code' => $errorCode,
            ];
            (new FailedEmails)->saveFailedEmails($collegeId, $arrFailMail);

            return false;
        }

        return ($result == '') ? 'fail' : 'success';
    }

    public function sendSmtpMailQueue($mailData)
    {

        $collegeId = $mailData['college_id'];
        // dd($collegeId);
        // $mail = SmtpSetup::get()->first();
        // $mailData['from'] = $mail->username;
        $mail = SmtpSetup::getSMTPDetail();

        if (($mail) && ($mail->status)) {
            $this->setSMTP();
        }

        $fromEmail = $mail && $mail->status ? $mail->email : config('mail.from.address');

        $fromEmailName = $mail && $mail->status ? $mail->name : config('mail.from.name');
        $mailData['from'] = $fromEmail;
        $mailData['fromName'] = $fromEmailName;
        $validator = Validator::make(['email' => $mailData['from']], [
            'email' => 'required|email',
        ]);

        // $mailData['to'] = "iie2.com";l
        if (! $validator->passes()) {
            $mailData['from'] = $mail->email;
        }
        // $mailData['fromName'] = $mail->name;
        $page = (isset($mailData['page']) && ! empty($mailData['page'])) ? $mailData['page'] : [];
        $data = (isset($mailData['data']) && ! empty($mailData['data'])) ? $mailData['data'] : [];
        try {

            Mail::send($page, $data, function ($message) use ($mailData) {

                $message->from(($mailData['from'] = ! '' ? $mailData['from'] : '<EMAIL>'), $mailData['fromName']);
                $message->to($mailData['to']);

                if (isset($mailData['replyTo']) && ! empty($mailData['replyTo'])) {
                    // Log::info('ReplyTo:', ['reply_to_email' => $mailData['replyTo']]);
                    $message->replyTo($mailData['replyTo']);
                }

                if (isset($mailData['cc']) && ! empty($mailData['cc'])) {
                    $message->cc($mailData['cc']);
                }

                if (isset($mailData['bcc']) && ! empty($mailData['bcc'])) {
                    $message->bcc($mailData['bcc']);
                }

                $message->subject($mailData['subject']);

                // If blade template set as page then not using body parameter in mailData
                if (isset($mailData['body']) && ! empty($mailData['body'])) {
                    // $message->setBody($mailData['body'], 'text/html');
                    $message->html($mailData['body']);
                }

                if (isset($mailData['attachFile']) && ! empty($mailData['attachFile'])) {
                    for ($i = 0; $i < count($mailData['attachFile']); $i++) {
                        $attachFile = UploadService::inlineAttachment($mailData['attachFile'][$i]);
                        $message->attachData($attachFile->data, $attachFile->name, [
                            'mime' => $attachFile->mime,
                            'contentId' => @$attachFile->contentId,
                        ]);
                    }
                }
            });

            return ['status' => 'success', 'message' => 'Email send successfully'];

        } catch (\Exception  $exception) {

            $errorMessage = $exception->getMessage();
            $errorCode = $exception->getCode();
            if (isset($mailData['page'])) {
                $contentText = View::make($mailData['page'], $mailData['data'])->render();
            } else {
                $contentText = (isset($mailData['data']['content'])) ? $mailData['data']['content'] : (isset($mailData['body']) ? $mailData['body'] : '');
            }
            $arrFailMail = [
                'sender' => ($mailData['from'] != '') ? $mailData['from'] : '<EMAIL>',
                'receiver' => $mailData['to'],
                'subject' => $mailData['subject'],
                'content' => $contentText,
                'error_message' => $errorMessage,
                'error_code' => $errorCode,
            ];
            (new FailedEmails)->saveFailedEmails($collegeId, $arrFailMail);

            return ['status' => 'fail', 'message' => $errorMessage];

        }
        // return ($result == "") ? 'fail' : 'success';
    }

    public function sendSmtpTestMail($mailData)
    {

        $collegeId = $this->getLoginUserCollegeId();
        $mail = SmtpSetup::active()->first();
        if ($mail) {
            $mailData['from'] = $mail->username;
            $validator = Validator::make(['email' => $mailData['from']], [
                'email' => 'required|email',
            ]);

            if (! $validator->passes()) {
                $mailData['from'] = $mail->email;
            }
            $mailData['fromName'] = $mail->name;
        } else {
            $mailData['from'] = config('mail.from.address');
            $mailData['fromName'] = config('mail.from.name');
        }
        // dd(config('mail'));
        $page = (isset($mailData['page']) && ! empty($mailData['page'])) ? $mailData['page'] : [];
        $data = (isset($mailData['data']) && ! empty($mailData['data'])) ? $mailData['data'] : [];

        try {

            Mail::send($page, $data, function ($message) use ($mailData) {
                $message->from(($mailData['from'] = ! '' ? $mailData['from'] : '<EMAIL>'), $mailData['fromName']);
                $message->to($mailData['to']);
                $message->subject($mailData['subject']);
                $message->html($mailData['body']);
            });

            return ['status' => true, 'message' => 'Send Successfully'];

        } catch (\Exception  $exception) {

            $errorMessage = $exception->getMessage();
            $errorCode = $exception->getCode();
            $contentText = (isset($mailData['data']['content'])) ? $mailData['data']['content'] : (isset($mailData['body']) ? $mailData['body'] : '');

            $arrFailMail = [
                'sender' => ($mailData['from'] != '') ? $mailData['from'] : '<EMAIL>',
                'receiver' => $mailData['to'],
                'subject' => $mailData['subject'],
                'content' => $contentText,
                'error_message' => $errorMessage,
                'error_code' => $errorCode,
            ];
            (new FailedEmails)->saveFailedEmails($collegeId, $arrFailMail);

            return ['status' => false, 'message' => $errorMessage];
        }
    }

    public function sendSmtpMailRegister($mailData)
    {

        $collegeId = $this->getLoginUserCollegeId();
        try {
            $result = Mail::send($mailData['page'], $mailData['data'], function ($message) use ($mailData) {
                $message->from(($mailData['from'] = ! '' ? $mailData['from'] : '<EMAIL>'), $mailData['fromName']);
                $message->to($mailData['to']);

                if (isset($mailData['replyTo']) && ! empty($mailData['replyTo'])) {
                    $message->replyTo($mailData['replyTo']);
                }

                if (isset($mailData['cc']) && ! empty($mailData['cc'])) {
                    $message->cc($mailData['cc']);

                }
                if (isset($mailData['bcc']) && ! empty($mailData['bcc'])) {
                    $message->bcc($mailData['bcc']);

                }
                $message->subject($mailData['subject']);

                if (isset($mailData['attachFile']) && ! empty($mailData['attachFile'])) {
                    for ($i = 0; $i < count($mailData['attachFile']); $i++) {
                        $message->attach($mailData['attachFile'][$i]);
                    }
                }

            });
        } catch (\Exception  $exception) {
            $errorMessage = $exception->getMessage();
            $errorCode = $exception->getCode();
            $arrFailMail = [
                'sender' => ($mailData['from'] != '') ? $mailData['from'] : '<EMAIL>',
                'receiver' => $mailData['to'],
                'subject' => $mailData['subject'],
                'content' => $mailData['data']['content'],
                'error_message' => $errorMessage,
                'error_code' => $errorCode,
            ];
            (new FailedEmails)->saveFailedEmails($collegeId, $arrFailMail);

            return false;
        }
        if ($result == '') {
            return 'success';
        } else {
            return 'fail';
        }
    }

    public function sendSmtpMailForAdmin($mailData)
    {

        $result = Mail::send($mailData['page'], $mailData['data'], function ($message) use ($mailData) {

            $message->from(($mailData['from'] = ! '' ? $mailData['from'] : '<EMAIL>'), $mailData['fromName']);
            $message->to($mailData['to']);

            if (isset($mailData['replyTo']) && ! empty($mailData['replyTo'])) {
                $message->replyTo($mailData['replyTo']);
            }

            if (isset($mailData['cc']) && ! empty($mailData['cc'])) {
                $message->cc($mailData['cc']);
            }
            if (isset($mailData['bcc']) && ! empty($mailData['bcc'])) {
                $message->bcc($mailData['bcc']);
            }
            $message->subject($mailData['subject']);

            for ($i = 0; $i < count($mailData['attachFile']); $i++) {
                $message->attach($mailData['attachFile'][$i]);
            }
        });

        return ($result == '') ? 'success' : 'fail';
    }

    public function setLetterBodyContent($studentId, $courseId, $content)
    {

        $collegeId = Auth::user()->college_id;

        $objStudentCourse = new StudentCourse;
        $arrStudentCourse = $objStudentCourse->getStudentCoursesEmailContent($studentId, $courseId);

        $objStudentSubjectEnrollment = new StudentSubjectEnrolment;
        $arrStudentSubjectEnrollment = $objStudentSubjectEnrollment->getCourseProgressSummaryByUnitV2($collegeId, $studentId, $courseId);

        $arrStudentEnrolledCourse = $objStudentCourse->getArrayStudentEnrolledCourseName($studentId);
        $arrStudentOfferedCourse = $objStudentCourse->getArrayStudentOfferedCourseName($studentId);

        $row = $arrStudentCourse[0];

        $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
        $destinationPath = Helpers::changeRootPath($filePath);
        // $domain = env('APP_URL');
        $domain = url('/');
        $basePath = $destinationPath['view'];

        $college_logo_url = $domain.str_replace('\\', '/', $basePath).$row['college_logo'];
        $college_signature_url = $domain.str_replace('\\', '/', $basePath).$row['college_signature'];
        $dean_signature_url = $domain.str_replace('\\', '/', $basePath).$row['dean_signature'];

        $college_logo = '<img src="'.$college_logo_url.'" alt="College Logo" style="height: auto; width: 150px; padding-left: 49px; padding-top: 20px;"  />';
        $college_signature = '<img src="'.$college_signature_url.'" alt="College Signature" style="height: auto; width: 150px; padding-left: 49px; padding-top: 20px;"  />';
        $dean_signature = '<img src="'.$dean_signature_url.'" alt="Dean/CEO Signature" style="height: auto; width: 150px; padding-left: 49px; padding-top: 20px;"  />';

        $enrolledCourseList = '';
        if (! empty($arrStudentEnrolledCourse)) {
            $enrolledCourseList = '<ul>';
            foreach ($arrStudentEnrolledCourse as $value) {
                $enrolledCourseList .= '<li>'.$value['course_code'].' : '.$value['course_name'].' ('.date('d-m-Y', strtotime($value['start_date'])).' - '.date('d-m-Y', strtotime($value['finish_date'])).')</li>';
            }
            $enrolledCourseList .= '</ul>';
        }

        $offeredCourseList = '';
        if (! empty($arrStudentOfferedCourse)) {
            $offeredCourseList = '<ul>';
            foreach ($arrStudentOfferedCourse as $value) {
                $offeredCourseList .= '<li>'.$value['course_code'].' : '.$value['course_name'].' ('.date('d-m-Y', strtotime($value['start_date'])).' - '.date('d-m-Y', strtotime($value['finish_date'])).')</li>';
            }
            $offeredCourseList .= '</ul>';
        }

        $enrolledUnitList = '';
        if (! empty($arrStudentSubjectEnrollment)) {
            $enrolledUnitList = '<ul>';
            foreach ($arrStudentSubjectEnrollment as $value) {
                $enrolledUnitList .= '<li>'.$value['unit_code'].' : '.$value['unit_name'].'</li>';
            }
            $enrolledUnitList .= '</ul>';
        }

        $dataArr = [
            '{AccountManager}' => $row['account_manager'],
            '{agentid}' => $row['agent_id'],
            '{AgentName}' => $row['agent_name'],
            '{CampusName}' => $row['campus_name'],
            '{CoENo}' => $row['coe_name'],
            '{CollegeEmail}' => $row['college_email'],
            '{Country}' => $row['country_name'],
            '{Course Attd}' => '*********',
            '{CourseAttempt}' => $row['course_attempt'],
            '{CourseCode}' => $row['course_code'],
            '{CourseID}' => $row['course_code'],
            '{CourseName}' => $row['course_name'],
            '{CourseType}' => $row['course_type'],
            '{CricosCode}' => $row['cricos_code'],
            // "{Current Date}"      => date('d/m/Y'),
            '{Current Date}' => $this->getCurrentDateTimeWithTimeZone($row['college_timezone'], 'd/m/Y'), // TODO::GNG-2333
            '{Dob}' => date('d/m/Y', strtotime($row['birth_date'])),
            '{Duration}' => $row['total_weeks'],
            '{DurationType}' => 'Week',
            '{Email}' => $row['student_email'],
            '{EntityName}' => $row['entity_name'],
            '{Fax}' => $row['fax'],
            '{Finishdate}' => date('d/m/Y', strtotime($row['finish_date'])),
            '{FirstName}' => $row['first_name'],
            '{Gender}' => $row['gender'],
            '{LastLetterName}' => '*********',
            '{LastLetterSentDate}' => '*********',
            '{Lastname}' => $row['family_name'],
            '{Mobile}' => $row['current_mobile_phone'],
            '{Name}' => $row['student_name'],
            '{Nationality}' => $row['nationality'],
            '{OfferId}' => $row['offer_id'],
            '{OfferNo}' => $row['offer_id'],
            '{Page Break}' => '<hr/>',
            '{Passportno}' => $row['passport_no'],
            '{Phone}' => $row['current_mobile_phone'],
            '{PostCode}' => $row['current_postcode'],
            '{Proj Attd}' => '*********',
            '{Startdate}' => date('d/m/Y', strtotime($row['start_date'])),
            '{State}' => $row['current_state'],
            '{Status}' => $row['status'],
            '{StreetAddress}' => $row['current_street_name'],
            '{StudentId}' => $row['generated_stud_id'],
            '{Suburb}' => $row['current_city'],
            '{Title}' => $row['name_title'],
            '{StreetNumber}' => $row['current_street_no'],
            '{UnitDetail}' => $enrolledUnitList,
            '{BuildingName}' => $row['current_building_name'],
            '{CollegeRtoCode}' => $row['RTO_code'],
            '{CollegeCircosCode}' => $row['CRICOS_code'],
            '{CollegeLegalName}' => $row['legal_name'],
            '{CollegeName}' => $row['entity_name'],
            '{CollegeSignature}' => $college_signature,
            '{DeanName}' => $row['dean_name'],
            '{DeanSignature}' => $dean_signature,
            '{CollegeContactPerson}' => $row['contact_person'],
            '{CollegeContactPhone}' => $row['contact_phone'],
            '{CollegeURL}' => $row['college_url'],
            '{CollegeABN}' => $row['college_ABN'],
            '{CollegeFax}' => $row['fax'],
            '{StudentType}' => $row['student_type'],
            '{TeacherFirstName}' => $row['teacher_first_name'],
            '{TeacherLastName}' => $row['teacher_last_name'],
            '{TeacherEmail}' => $row['teacher_email'],
            '{TeacherMobile}' => $row['teacher_mobile'],
            '{AgencyName}' => $row['agency_name'],
            '{AgentEmail}' => $row['agent_email'],
            '{AgentTelephone}' => $row['agent_telephone'],
            '{CollegeLogo}' => $college_logo,
            '{EnrolledCourseList}' => $enrolledCourseList,
            '{OfferedCourseList}' => $offeredCourseList,
        ];

        foreach ($dataArr as $key => $value) {
            $content = str_replace("$key", $value, $content);
        }

        return $content;
    }

    public function getParameterList($content)
    {

        $start = '{';
        $end = '}';
        $split_string = explode($end, $content);
        foreach ($split_string as $data) {
            $str_pos = strpos($data, $start);
            $last_pos = strlen($data);
            $capture_len = $last_pos - $str_pos;
            $value = substr($data, $str_pos + 1, $capture_len);
            $result[] = str_replace('#', '', $value);
        }

        if ((count($result) > 1)) {
            array_pop($result);

            return $result;
        } else {
            return [];
        }
    }

    public function assignParameterValue($content, $paramVal)
    {

        foreach ($paramVal as $row) {
            $key = '{#'.$row['key'].'#}';
            $value = $row['value'];
            $content = str_replace($key, $value, $content);
        }

        return $content;
    }

    public function setEmailBodyContent($studentId, $courseId, $content)
    {

        $collegeId = Auth::user()->college_id;

        $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
        $destinationPath = Helpers::changeRootPath($filePath);

        $objStudentCourse = new StudentCourse;
        $arrStudentCourse = $objStudentCourse->getStudentCoursesEmailContent($studentId, $courseId);

        $arrStudentEnrolledCourse = $objStudentCourse->getArrayStudentEnrolledCourseName($studentId);

        $arrStudentOfferedCourse = $objStudentCourse->getArrayStudentOfferedCourseName($studentId);

        $objStudentSubjectEnrollment = new StudentSubjectEnrolment;
        $arrStudentSubjectEnrollment = $objStudentSubjectEnrollment->getCourseProgressSummaryByUnitV2($collegeId, $studentId, $courseId);

        if (! empty($arrStudentCourse)) {
            $row = $arrStudentCourse[0];
            // $domain = env('APP_URL');
            $domain = url('/');

            $basePath = $destinationPath['view'];
            $college_logo_url = $domain.str_replace('\\', '/', $basePath).$row['college_logo'];
            $college_signature_url = $domain.str_replace('\\', '/', $basePath).$row['college_signature'];
            $dean_signature_url = $domain.str_replace('\\', '/', $basePath).$row['dean_signature'];

            $college_logo = '<img src="'.$college_logo_url.'" alt="College Logo" style="height: auto; width: 150px; padding-left: 49px; padding-top: 20px;"  />';
            $college_signature = '<img src="'.$college_signature_url.'" alt="College Signature" style="height: auto; width: 150px; padding-left: 49px; padding-top: 20px;"  />';
            $dean_signature = '<img src="'.$dean_signature_url.'" alt="Dean/CEO Signature" style="height: auto; width: 150px; padding-left: 49px; padding-top: 20px;"  />';

            $enrolledCourseList = '';
            if (! empty($arrStudentEnrolledCourse)) {
                $enrolledCourseList = '<ul>';
                foreach ($arrStudentEnrolledCourse as $value) {
                    $enrolledCourseList .= '<li>'.$value['course_code'].' : '.$value['course_name'].' ('.date('d-m-Y', strtotime($value['start_date'])).' - '.date('d-m-Y', strtotime($value['finish_date'])).')</li>';
                }
                $enrolledCourseList .= '</ul>';

            }

            $offeredCourseList = '';
            if (! empty($arrStudentOfferedCourse)) {
                $offeredCourseList = '<ul>';
                foreach ($arrStudentOfferedCourse as $value) {
                    $offeredCourseList .= '<li>'.$value['course_code'].' : '.$value['course_name'].' ('.date('d-m-Y', strtotime($value['start_date'])).' - '.date('d-m-Y', strtotime($value['finish_date'])).')</li>';
                }
                $offeredCourseList .= '</ul>';
            }

            $enrolledUnitList = '';
            if (! empty($arrStudentSubjectEnrollment)) {
                $enrolledUnitList = '<ul>';
                foreach ($arrStudentSubjectEnrollment as $value) {
                    $enrolledUnitList .= '<li>'.$value['unit_code'].' : '.$value['unit_name'].'</li>';
                }
                $enrolledUnitList .= '</ul>';
            }

            $deliveryMode = '';
            if (isset($row['internal']) && isset($row['external']) && isset($row['workplace_based_delivery'])) {
                $deliveryMode = '<ul>';

                if ($row['internal']) {
                    $internal = ($row['internal'] == 'N') ? 'NO' : 'YES';
                    $deliveryMode .= '<li> Internal : '.$internal.'</li>';
                }
                if ($row['external']) {
                    $external = ($row['external'] == 'N') ? 'NO' : 'YES';
                    $deliveryMode .= '<li> External : '.$external.'</li>';
                }
                if ($row['workplace_based_delivery']) {
                    $workplace_based_delivery = ($row['workplace_based_delivery'] == 'N') ? 'NO' : 'YES';
                    $deliveryMode .= '<li> Workplace Based Delivery : '.$workplace_based_delivery.'</li>';
                }

                $deliveryMode .= '</ul>';
            }

            $dataArr = [
                '{AlterEmail1}' => $row['emergency_email'],
                '{AlterEmail2}' => $row['emergency_email'],
                // "{CollegeEmail}"              => $row['coe_name'],
                '{CollegeLogo}' => $college_logo,
                '{CollegeEmail}' => $row['college_email'],
                '{Country}' => $row['country_name'],
                '{CountryBirth}' => $row['birth_country'],
                // "{CurrentDate}"               => date('d-m-Y'),
                '{CurrentDate}' => $this->getCurrentDateTimeWithTimeZone($row['college_timezone'], 'd-m-Y'), // TODO::GN-2333
                '{DoB}' => date('d-m-Y', strtotime($row['birth_date'])),
                '{DOB}' => date('d-m-Y', strtotime($row['birth_date'])),
                '{DoB Without Stroke}' => '******************',
                '{Email}' => $row['student_email'],
                '{ExpDate}' => date('d-m-Y', strtotime($row['visa_expiry_date'])),
                '{Fax}' => $row['fax'],
                '{StudentId}' => $row['generated_stud_id'],
                '{FirstName}' => $row['first_name'],
                '{MiddleName}' => $row['middel_name'],
                '{LastName}' => $row['family_name'],
                '{Gender}' => $row['gender'],
                '{Mobile}' => $row['current_mobile_phone'],
                '{Nationality}' => $row['nationality'],
                '{NickName}' => $row['nickname'],
                '{PassportNo}' => $row['passport_no'],
                '{Phone}' => $row['current_mobile_phone'],
                '{Postcode}' => $row['current_postcode'],
                '{State}' => $row['current_state'],
                '{StreetAddress}' => $row['current_street_name'],
                '{StreetNumber}' => $row['current_street_no'],
                '{UnitDetail}' => $enrolledUnitList,
                '{BuildingName}' => $row['current_building_name'],
                '{Suburb}' => $row['current_city'],
                '{Title}' => $row['name_title'],
                '{UserName}' => $row['generated_stud_id'],
                '{VisaType}' => $row['visa_type'],
                '{CourseCode}' => $row['course_code'],
                '{CourseName}' => $row['course_name'],
                '{CollegeRtoCode}' => $row['RTO_code'],
                '{CollegeCircosCode}' => $row['CRICOS_code'],
                '{CollegeLegalName}' => $row['legal_name'],
                '{CollegeName}' => $row['entity_name'],
                '{CollegeSignature}' => $college_signature,
                '{DeanName}' => $row['dean_name'],
                '{DeanSignature}' => $dean_signature,
                '{CollegeContactPerson}' => $row['contact_person'],
                '{CollegeContactPhone}' => $row['contact_phone'],
                '{CollegeURL}' => $row['college_url'],
                '{CollegeABN}' => $row['college_ABN'],
                '{CollegeFax}' => $row['fax'],
                '{CourseType}' => $row['course_type'],
                '{Campus}' => $row['campus_name'],
                '{StudentType}' => $row['student_type'],
                '{TeacherFirstName}' => $row['teacher_first_name'],
                '{TeacherLastName}' => $row['teacher_last_name'],
                '{TeacherEmail}' => $row['teacher_email'],
                '{TeacherMobile}' => $row['teacher_mobile'],
                '{AgencyName}' => $row['agency_name'],
                '{AgentName}' => $row['agent_name'],
                '{AgentEmail}' => $row['agent_email'],
                '{AgentTelephone}' => $row['agent_telephone'],
                '{EnrolledCourseList}' => $enrolledCourseList,
                '{OfferedCourseList}' => $offeredCourseList,
                '{IntakeDate}' => date('d-m-Y', strtotime($row['intake_date'])),
                '{CourseCRICOSCode}' => $row['cricos_code'],
                '{DeliveryMode}' => $deliveryMode,
            ];

            foreach ($dataArr as $key => $value) {
                $content = str_replace("$key", $value, $content);
            }

            return $content;
        } else {
            return false;
        }

    }

    public function setStaffEmailBodyContent($teacherId, $content)
    {

        $objStaff = new Staff;
        $arrStaff = $objStaff->getStaffCoursesEmailContent($teacherId);
        if (! empty($arrStaff)) {
            $row = $arrStaff[0];

            $dataArr = [
                '{CollegeEmail}' => $row['college_email'],
                '{Email}' => $row['email'],
                '{FirstName}' => $row['first_name'],
                '{LastName}' => $row['last_name'],
                '{Mobile}' => $row['mobile'],
                '{Nationality}' => $row['country_name'],
                '{Phone}' => $row['phone'],
                '{Postcode}' => $row['postcode'],
                '{State}' => $row['state'],
                '{StreetAddress}' => $row['address'],
                '{Suburb}' => $row['city_town'],
                '{Title}' => $row['name_title'],
            ];

            foreach ($dataArr as $key => $value) {
                $content = str_replace("$key", $value, $content);
            }

            return $content;
        } else {
            return false;
        }

    }

    private function setSMTP()
    {
        $config = SmtpSetup::where('status', 1)->first(); // Adjust the query to suit your needs
        if ($config) {
            // Set mail configuration
            Config::set('mail.mailers.smtp.transport', 'smtp');
            Config::set('mail.mailers.smtp.host', $config->host);
            Config::set('mail.mailers.smtp.port', $config->port);
            Config::set('mail.mailers.smtp.username', $config->username);
            Config::set('mail.mailers.smtp.password', $config->password);
            Config::set('mail.mailers.smtp.encryption', $config->encryption);
            Config::set('mail.from.address', $config->email);
            Config::set('mail.from.name', $config->name);
        }
    }
}
