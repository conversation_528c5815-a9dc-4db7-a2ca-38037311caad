<?php

namespace App\Model;

use Domains\Moodle\Jobs\SyncStudentCourseUnitGradeReportFromMoodle;
use Illuminate\Database\Eloquent\Model;
use App\Exceptions\ApplicationException;
use App\Model\StudentCourse;
use App\Model\AssessmentDueDate;
use App\Model\Holiday;
use App\Model\v2\TimetableDetail;
use App\Model\v2\StudentAttendance;
use App\Model\StudentUnitEnrollment;
use App\Model\StudentAttendanceHours;
use App\Model\ResultGrade;
use App\Model\v2\SubjectUnits;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use App\Traits\AvetMissTrait;
use Spatie\Activitylog\Contracts\Activity;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;
use App\Traits\ActivityLog\StudentEnrollmentActivityLog;

class StudentSubjectEnrolment extends Model {

    use LogsActivity;
    use StudentEnrollmentActivityLog;

    protected $table = 'rto_student_subject_enrolment';
    protected $fillable = [
        'college_id',
        'student_id',
        'course_id',
        'semester_id',
        'term',
        'enroll_type',
        'course_stage',
        'subject_id',
        'unit_id',
        'activity_start_date',
        'activity_finish_date',
        'study_reason',
        'vanue_location',
        'funding_source_state',
        'funding_source_nat',
        'final_outcome',
        'outcome_identifier',
        'comment',
        'batch',
        'funding_identifier',
        'course_commencing_id',
        'training_contract_id',
        'apprenticeship_client_id',
        'vet_in_school',
        'fee_exemption_id',
        'booking_id',
        'course_site_id',
        'delivery_mode',
        'predominant_delivery_mode',
        'purchase_contract_id',
        'con_schedule_id',
        'full_time_learning_option',
        'census_date',
        'mode_of_attendance',
        'subject_tution_fee',
        'fee_paid_upfront',
        'eftsl_study_load',
        'subject_attempt',
        'marks',
        'grade',
        'mark_outcome',
        'created_at',
        'updated_at',
        'created_by',
        'is_result_lock',
        'updated_by',
    ];
    protected $logAttributes = [
        'college_id',
        'student_id',
        'course_id',
        'semester_id',
        'term',
        'enroll_type',
        'course_stage',
        'subject_id',
        'unit_id',
        'activity_start_date',
        'activity_finish_date',
        'study_reason',
        'vanue_location',
        'funding_source_state',
        'funding_source_nat',
        'final_outcome',
        'outcome_identifier',
        'comment',
        'batch',
        'funding_identifier',
        'course_commencing_id',
        'training_contract_id',
        'apprenticeship_client_id',
        'vet_in_school',
        'fee_exemption_id',
        'booking_id',
        'course_site_id',
        'delivery_mode',
        'predominant_delivery_mode',
        'purchase_contract_id',
        'con_schedule_id',
        'full_time_learning_option',
        'census_date',
        'mode_of_attendance',
        'subject_tution_fee',
        'fee_paid_upfront',
        'eftsl_study_load',
        'subject_attempt',
        'marks',
        'grade',
        'mark_outcome',
        'is_result_lock'
    ];
    use AvetMissTrait;

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logFillable()
            ->logOnlyDirty()
            ->setDescriptionForEvent(fn(string $eventName) => "Task Result has been {$eventName}");
    }

    public function tapActivity(Activity $activity, string $eventName)
    {


    //     $properties = $activity->properties->toArray();
    //     if (isset($properties['attributes']['is_result_lock'])) {
    //         // Debugging step: Log or inspect the value before casting
    //         // Check if the value is "1" as a string or 1 as an integer to handle both cases
    //         $attributesLock = $properties['attributes']['is_result_lock'];
    //         $properties['attributes']['is_result_lock'] = ($attributesLock === "1" || $attributesLock === 1) ? 'Lock' : 'Unlock';

    //         // Debugging step: Log or inspect the old value before casting
    //         if (isset($properties['old']['is_result_lock'])) {
    //             $oldLock = $properties['old']['is_result_lock'];
    //             $properties['old']['is_result_lock'] = ($oldLock === "1" || $oldLock === 1) ? 'Lock' : 'Unlock';
    //         }
    //     }
    //     $activity->properties = collect($properties);
    // //    d $activity->properties

        // dd($properties['attributes']['is_result_lock'], $properties['old']['is_result_lock']);
        $activity->log_name = (new self)->getMorphClass() . '_' . $this->student_id .'_'. $this->course_id;
        $activity->subject_type = self::class;
        $activity->studentName = $this->student->first_name .' '.$this->student->family_name;
        $activity->batch = $this->batch;
        $activity->subject = $this->subject->subject_code.' '.$this->subject->subject_name;
        $activity->studentId = $this->student->generated_stud_id;
        $activity->description = $this->getCustomDescriptionForEvent($eventName,$activity);

    }

    public function student(){
        return $this->hasOne(Students::class, 'id', 'student_id');
    }
    public function subject(){
        return $this->hasOne(Subject::class, 'id', 'subject_id');
    }
    public function unit(){
        return $this->hasOne(UnitModule::class, 'id', 'unit_id');
    }

    public function saveStudentSubjectEnrolment($request, $studentId) {

        $subjectData = $request->input('subject_id');
        $unit_id = $request->input('unit_id');

        $collegeId = Auth::user()->college_id;
        $userId = Auth::user()->id;
        $data = $request->input();

        if (!empty($subjectData)) {
            for ($i = 0; $i < count($subjectData); $i++) {
                $objRtoUnitModule = new UnitModule();
                $unitId = $objRtoUnitModule->getUnitId($collegeId, $subjectData[$i]);

                for ($j = 0; $j < count($unitId); $j++) {
                    $lastInsertId = $this->saveStudentEnrollData($data, $subjectData[$i], $unitId[$j]['id'], $studentId);
                    $resultArr = $this->saveStudentUnitEnrollData($data, $subjectData[$i], $unitId[$j], $studentId, $lastInsertId);
                }
            }
        } elseif (!empty($unit_id)) {
            $subjectId = $data['subject_list'];
            for ($i = 0; $i < count($unit_id); $i++) {
                $unitId = $unit_id[$i];
                $objRtoUnitModule = new UnitModule();
                $unitData = $objRtoUnitModule->getUnitDetailsData($collegeId, $unitId);

                $lastInsertId = $this->saveStudentEnrollData($data, $subjectId, $unitData[0]['id'], $studentId);
                $resultArr = $this->saveStudentUnitEnrollData($data, $subjectId, $unitData[0], $studentId, $lastInsertId);
            }
        }
    }

    public function studentSubjectEnrollmentUpdate($collegeId, $request) {
        $objSubjectEnrolment = StudentSubjectEnrolment::where('activity_finish_date', '>', date('Y-m-d', strtotime($request->input('finish_date'))))
                ->where('student_id', '=', $request->input('student_id_hidden'))
                ->where('course_id', '=', $request->input('course_id_hidden'))
                ->where('college_id', '=', $collegeId)
                ->update(['activity_finish_date' => date('Y-m-d', strtotime($request->input('finish_date'))),
        ]);
        return StudentSubjectEnrolment::where('college_id', '=', $collegeId)
                        ->where('student_id', '=', $request->input('student_id_hidden'))
                        ->where('course_id', '=', $request->input('course_id_hidden'))
                        ->where('college_id', '=', $collegeId)
                        ->where('activity_finish_date', '=', date('Y-m-d', strtotime($request->input('finish_date'))))
                        ->pluck('id')
                        ->toarray();
    }

    public function getStudentName($semesterId, $term, $subjectId, $batch, $arrStudentIds = []) {
        $qry = StudentSubjectEnrolment::join('rto_students', 'rto_students.id', '=', 'rto_student_subject_enrolment.student_id')
                ->where('batch', '=', $batch)
                ->where('subject_id', '=', $subjectId)
                ->where('semester_id', '=', $semesterId)
                ->where('term', '=', $term)
                ->where('rto_students.generated_stud_id', '!=', null);
        if (!empty($arrStudentIds)) {
            $qry = $qry->whereIn('rto_students.id', $arrStudentIds);
        }
        return $qry->groupBy('rto_student_subject_enrolment.student_id')
                        ->get(['student_id', 'rto_students.generated_stud_id', 'rto_students.first_name', 'rto_students.family_name']);
    }

    public function getStudentUnitEnrollConpentency($semesterId, $term, $subjectId, $batch) {

        $studentSubjectEnrolmentUnit = StudentVocationalPlacementResult::where('batch', '=', $batch)
                ->where('subject_id', '=', $subjectId)
                ->where('semester_id', '=', $semesterId)
                ->where('term_id', '=', $term)
                ->get(['vocational_result', 'unit_id', 'student_id', 'is_locked', 'is_approved']);

        $student = array();
        $studentStatus = array();

        foreach ($studentSubjectEnrolmentUnit as $row) {
            $student[$row['student_id']][$row['unit_id']] = $row['vocational_result'];
            $studentStatus[$row['student_id']]['lock'] = $row['is_locked'];
            $studentStatus[$row['student_id']]['approve'] = $row['is_approved'];
        }

        return [$student, $studentStatus];
    }

    public function deleteStudentSubjectEnrolment($enrId)
    {
        //TODO:: GNG-1711
        $this->deleteAllDataForAttendance($enrId);
        $this->deleteStudentSubjectEnrollmentData($enrId);
        $this->deleteStudentUnitEnrollmentData($enrId);
        return true;
    }

    public function deleteStudentEnrollment($collegeId, $enrId)
    {
        $resData = StudentSubjectEnrolment::where(['college_id' => $collegeId, 'id' => $enrId])->get()->first();

        if(!isset($resData)){
            return false;
        }

        //TODO:: GNG-1711
        $this->deleteAllDataForAttendance($enrId);
        $this->deleteStudentUnitEnrollmentData($enrId);

        //delete all matching records
        return StudentSubjectEnrolment::where([
                'college_id' => $collegeId,
                'student_id' => $resData->student_id,
                'course_id'  => $resData->course_id,
                'subject_id' => $resData->subject_id,
                'semester_id'=> $resData->semester_id,
                'subject_attempt'=> $resData->subject_attempt
            ])->delete();
    }

    public function deleteStudentEnrollById($collegeId, $enrId)
    {
        $resData = StudentSubjectEnrolment::where(['college_id' => $collegeId, 'id' => $enrId])->get()->first();

        if(isset($resData)){
            $res = $this->deleteStudentSubjectEnrollmentData($enrId);
            if($res){
                $this->deleteStudentUnitEnrollmentData($enrId);
                return $resData->student_id;
            }else{
                return $res;
            }
        }
        return false;
    }

    //TODO:: GNG-1711
    //delete all entry related to attendance & attendance hours data
    private function deleteAllDataForAttendance($enrId)
    {

        //$resData = StudentSubjectEnrolment::find($enrId);
        $resData = StudentSubjectEnrolment::where('id', $enrId)->get()->first();

        if(isset($resData) && !empty($resData->student_id) && !empty($resData->batch)){
            $attendanceData = StudentAttendance::from('rto_student_attendance as rsa')
                ->join('rto_timetable as rt', 'rt.id', '=', 'rsa.timetable_id')
                ->where([
                    'rsa.student_id' => $resData->student_id,
                    'rt.batch' => $resData->batch
                ])
                ->get(['rsa.id'])
                ->toArray();

            $attendanceIdArr = array_column($attendanceData, 'id');

            DB::beginTransaction();
            try {

                $res = StudentAttendance::destroy($attendanceIdArr);
                if ($res) {
                    $attendanceHours = StudentAttendanceHours::whereIn('student_attendance_id', $attendanceIdArr)->get();
                    $attendanceHoursIds = $attendanceHours->pluck('id')->toArray();
                    StudentAttendanceHours::destroy($attendanceHoursIds);
                }
                DB::commit();
                //return $res;
            } catch (\Exception $e) {
                DB::rollBack();
                safeDD($e);
                throw new ApplicationException($e->getMessage());
            }
        }
    }

    private function deleteStudentSubjectEnrollmentData($enrId)
    {
        $count = StudentSubjectEnrolment::where('id', $enrId)->get()->count();
        if($count > 0){
            DB::beginTransaction();
            try {
//                $res = StudentSubjectEnrolment::where('id', $enrId)->delete();
                $res = StudentSubjectEnrolment::destroy($enrId);
                DB::commit();
                return $res;
            } catch (\Exception $e) {
                DB::rollBack();
                safeDD($e);
                throw new ApplicationException($e->getMessage());
            }
        }
        return false;
    }

    private function deleteStudentUnitEnrollmentData($enrId)
    {
        $count = StudentUnitEnrollment::where('student_subject_enrollment_id', $enrId)->get()->count();
        if($count > 0){
            DB::beginTransaction();
            try {
                $studentUnitEnrollment = StudentUnitEnrollment::where('student_subject_enrollment_id', $enrId)->get();
                $studentUnitEnrollmentIds = $studentUnitEnrollment->pluck('id')->toArray();
                $res =  StudentUnitEnrollment::destroy($studentUnitEnrollmentIds);
//                 StudentUnitEnrollment::where('student_subject_enrollment_id', $enrId)->delete();
                DB::commit();
                return $res;
            } catch (\Exception $e) {
                DB::rollBack();
                safeDD($e);
                throw new ApplicationException($e->getMessage());
            }
        }
        return false;
    }

    public function saveStudentEnrollData($data, $subjectId, $unitId, $studentId) {

        $collegeId = Auth::user()->college_id;
        $userId = Auth::user()->id;

        $grade = '';
        if($data['isHigherEd'] == '1')
        {
            $gradeId = $data['select_mark_outcome'];

            if(isset($gradeId) && !empty($gradeId))
            {
                $gradeData = (new ResultGrade)->getResultGradelistCollegeIdWise($gradeId);
                $grade = $gradeData[0]['grade'];
            }
        }
        // Check if it's first attempt or second
        $attempt = StudentSubjectEnrolment::where([
            'student_id' => $studentId,
            'course_id' => $data['course_id'],
            'subject_id' => $subjectId,
            'unit_id' => $unitId
        ])->count();

        $objSubjectEnrolment = new StudentSubjectEnrolment();
        $objSubjectEnrolment->college_id = $collegeId;
        $objSubjectEnrolment->student_id = $studentId;
        $objSubjectEnrolment->course_id = $data['course_id'];
        $objSubjectEnrolment->semester_id = $data['semester'];
        $objSubjectEnrolment->term = $data['term'];
        $objSubjectEnrolment->enroll_type = $data['type_of_enrollment'];
        $objSubjectEnrolment->course_stage = $data['course_stage'];
        $objSubjectEnrolment->subject_id = $subjectId;
        $objSubjectEnrolment->unit_id = $unitId;
        $objSubjectEnrolment->subject_attempt = ++$attempt;
        $objSubjectEnrolment->batch = (!empty($data['batchTxt'])) ? $data['batchTxt'] : null;
        $objSubjectEnrolment->activity_start_date = date('Y-m-d', strtotime($data['activity_start_date']));
        $objSubjectEnrolment->activity_finish_date = date('Y-m-d', strtotime($data['activity_finish_date']));
        $objSubjectEnrolment->study_reason = $data['study_reason'];
        $objSubjectEnrolment->vanue_location = $data['vanue_location'];
        $objSubjectEnrolment->funding_source_state = $data['funding_source_state'];
        $objSubjectEnrolment->funding_source_nat = $data['funding_source_nat'];
        $objSubjectEnrolment->final_outcome = (isset($data['select_final_outcome'])) ? $data['select_final_outcome'] : NULL;
        $objSubjectEnrolment->mark_outcome = (isset($data['select_mark_outcome']) && $data['isHigherEd'] == '1') ? $data['select_mark_outcome'] : NULL;
        $objSubjectEnrolment->grade = $grade;
        $objSubjectEnrolment->delivery_mode = $data['delivery_mode'];
        $objSubjectEnrolment->vet_in_school = ($data['vet_in_school'] == "YES") ? "Y" : "N";
        $objSubjectEnrolment->outcome_identifier = ($data['outcome_identifier'])?$data['outcome_identifier']:'';
        $objSubjectEnrolment->created_by = $userId;
        $objSubjectEnrolment->updated_by = $userId;

        $objSubjectEnrolment->save();

        return $lastInsertedId = $objSubjectEnrolment->id;
    }

    public function saveStudentEnrollDataCompentancy($collegeId, $subjectId, $competency, $request) {
//dd($competency);
        $userId = Auth::user()->id;
        $objSubjectEnrolment = StudentSubjectEnrolment::where('subject_id', '=', $subjectId)
                ->update([
                    'final_outcome'     => $competency,
                    'census_date'       => ($request->input('census_date') != '') ? date('Y-m-d', strtotime($request->input('census_date'))) : null,
                    'mode_of_attendance'=> ($request->input('mode_of_attendance') != '') ? $request->input('mode_of_attendance') : null,
                    'subject_tution_fee'=> ($request->input('subject_tution_fee') != '') ? $request->input('subject_tution_fee') : null,
                    'fee_paid_upfront'  => ($request->input('fee_paid_upfront') != '') ? $request->input('fee_paid_upfront') : null,
                    'eftsl_study_load'  => ($request->input('eftsl_study_load') != '') ? $request->input('eftsl_study_load') : null,
                    'updated_by'        => $userId,
                ]);
    }

    public function saveStudentUnitEnrollData($data, $subjectId, $unitId, $studentId, $lastInsertId) {

        $collegeId = Auth::user()->college_id;
        $userId = Auth::user()->id;

        $compentency = '';
        if($data['type_of_enrollment'] == 'unit'){
            if($data['isHigherEd'] == '1'){
                $gradeId = $data['select_mark_outcome'];
                if(isset($gradeId) && !empty($gradeId))
                    $gradeData = (new ResultGrade)->getResultGradelistCollegeIdWise($gradeId);
                $compentency = $gradeData[0]['grade'];
            }else{
                $compentency = $data['select_final_outcome'];
            }
        }else{
            $compentency = $data['select_final_outcome'];
        }

        $objSubjectEnrolment = new StudentUnitEnrollment();
        $objSubjectEnrolment->college_id = $collegeId;
        $objSubjectEnrolment->student_subject_enrollment_id = $lastInsertId;
        $objSubjectEnrolment->compentency = $compentency;
        $objSubjectEnrolment->unit_id = $unitId['id'];

        $objSubjectEnrolment->schedule_hours = $unitId['nominal_hours'];
        $objSubjectEnrolment->delivery_mode = $data['delivery_mode'];
        $objSubjectEnrolment->tution_fee = $unitId['tution_fees'];
        $objSubjectEnrolment->attended_hour = 0;
        $objSubjectEnrolment->study_from = date('Y-m-d', strtotime($data['activity_start_date']));
        $objSubjectEnrolment->study_to = date('Y-m-d', strtotime($data['activity_finish_date']));
        $objSubjectEnrolment->created_by = $userId;
        $objSubjectEnrolment->updated_by = $userId;

        $objSubjectEnrolment->save();
        return $lastInsertedId = $objSubjectEnrolment->id;
    }

    public function listStudentSubjectEnrolment($collegeId, $studentId, $courseId = '') {

        $qry = StudentSubjectEnrolment::leftjoin('rto_semester', 'rto_semester.id', '=', 'rto_student_subject_enrolment.semester_id')
                ->leftjoin('rto_subject', 'rto_subject.id', '=', 'rto_student_subject_enrolment.subject_id')
                ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_subject_enrolment.course_id')
                //->leftjoin('rto_subject_unit', 'rto_subject_unit.id', '=', 'rto_student_subject_enrolment.unit_id')
                ->leftjoin('rto_student_courses', 'rto_student_courses.course_id', '=', 'rto_student_subject_enrolment.course_id')
                ->where('rto_student_subject_enrolment.college_id', '=', $collegeId)
                ->where('rto_student_subject_enrolment.student_id', '=', $studentId)
                ->where('rto_student_courses.student_id', '=', $studentId);
        if ($courseId != '') {
            $qry->where('rto_student_subject_enrolment.course_id', '=', $courseId);
        }

        $qry->groupBy('rto_student_subject_enrolment.subject_id')
                ->groupBy('rto_student_subject_enrolment.semester_id')
                ->orderBy('rto_student_subject_enrolment.id');
        $resultArr = $qry->get(['rto_student_subject_enrolment.*', 'rto_semester.semester_name', 'rto_subject.subject_code', 'rto_subject.subject_name', 'rto_student_courses.campus_id', 'rto_courses.flexible_attendance']);
        return $resultArr;
    }

    public function listStudentSubjectEnrolmentData($collegeId, $studentId) {
        return StudentSubjectEnrolment::leftjoin('rto_students', 'rto_students.id', '=', 'rto_student_subject_enrolment.student_id')
                        ->leftjoin('rto_semester', 'rto_semester.id', '=', 'rto_student_subject_enrolment.semester_id')
                        ->where('rto_student_subject_enrolment.college_id', '=', Auth::user()->college_id)
                        ->where('rto_students.generated_stud_id', '=', $studentId)
                        ->groupBy('rto_student_subject_enrolment.semester_id')
                        ->get(['rto_semester.semester_name', 'rto_semester.id'])->toarray();
    }

    public function currentCourseAcademicSemester($collegeId, $studentId) {
        return StudentSubjectEnrolment::leftjoin('rto_semester', 'rto_semester.id', '=', 'rto_student_subject_enrolment.semester_id')
                        ->where('rto_student_subject_enrolment.college_id', '=', $collegeId)
                        ->where('rto_student_subject_enrolment.student_id', '=', $studentId)
                        ->groupBy('rto_student_subject_enrolment.semester_id')
                        ->get(['rto_semester.semester_name', 'rto_semester.id'])->toarray();
    }

    public function currentCourseAcademicTerm($collegeId, $semesterId) {
        return StudentSubjectEnrolment::where('college_id', '=', $collegeId)
                        ->where('semester_id', '=', $semesterId)
                        ->groupBy('term')
                        ->get(['term'])->toarray();
    }

    public function currentCourseAcademicData($collegeId, $semesterId, $termId, $studentID) {
        return StudentSubjectEnrolment::leftjoin('rto_semester', 'rto_semester.id', '=', 'rto_student_subject_enrolment.semester_id')
                        ->leftjoin('rto_subject', 'rto_subject.id', '=', 'rto_student_subject_enrolment.subject_id')
                        ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_subject_enrolment.course_id')
                        ->leftjoin('rto_student_courses', 'rto_student_courses.course_id', '=', 'rto_student_subject_enrolment.course_id')
                        ->where('rto_student_subject_enrolment.college_id', '=', $collegeId)
                        ->where('rto_student_subject_enrolment.semester_id', '=', $semesterId)
                        ->where('rto_student_subject_enrolment.term', '=', $termId)
                        ->where('rto_student_subject_enrolment.student_id', '=', $studentID)
                        ->groupBy('rto_student_subject_enrolment.subject_id')
                        ->orderBy('rto_student_subject_enrolment.id')
                        ->get(['rto_student_subject_enrolment.*',
                            'rto_semester.semester_name',
                            'rto_subject.subject_code',
                            'rto_subject.subject_name',
                            'rto_student_courses.campus_id',
                            'rto_courses.course_code'])
                        ->toarray();
    }

    public function currentCourseAcademicDataWithSort($collegeId, $studentId, $semesterId, $termId, $sortField, $sortBy) {

        $sortFieldName = '';
        if ($sortField == 'subject_name') {
            $sortFieldName = 'rto_subject.subject_name';
        }
        if ($sortField == 'start_date') {
            $sortFieldName = 'rto_student_subject_enrolment.activity_start_date';
        }

        $sql = StudentSubjectEnrolment::leftjoin('rto_semester', 'rto_semester.id', '=', 'rto_student_subject_enrolment.semester_id')
                ->leftjoin('rto_subject', 'rto_subject.id', '=', 'rto_student_subject_enrolment.subject_id')
                ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_subject_enrolment.course_id')
                ->leftjoin('rto_student_courses', 'rto_student_courses.course_id', '=', 'rto_student_subject_enrolment.course_id')
                ->where('rto_student_subject_enrolment.college_id', '=', $collegeId)
                ->where('rto_student_subject_enrolment.semester_id', '=', $semesterId)
                ->where('rto_student_subject_enrolment.term', '=', $termId)
                ->where('rto_student_subject_enrolment.student_id', '=', $studentId)
                ->groupBy('rto_student_subject_enrolment.subject_id');

        if (empty($sortFieldName)) {
            $sql->orderBy('rto_student_subject_enrolment.id');
        } else {
            $sql->orderBy($sortFieldName, $sortBy);
        }

        $resultArr = $sql->get(['rto_student_subject_enrolment.*',
                    'rto_semester.semester_name',
                    'rto_subject.subject_code',
                    'rto_subject.subject_name',
                    'rto_student_courses.campus_id',
                    'rto_courses.course_code'])
                ->toarray();

        return $resultArr;
    }

    public function nvrStudentSubjectEnrolment($collegeId, $yearValue) {
        $result = StudentSubjectEnrolment::leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_subject_enrolment.course_id')
                        ->where('rto_student_subject_enrolment.college_id', '=', $collegeId)
                        ->whereYear('rto_student_subject_enrolment.activity_start_date', '=', $yearValue)
                        ->groupBy('rto_student_subject_enrolment.course_id')
                        ->get(['rto_student_subject_enrolment.*', 'rto_courses.national_code', 'rto_courses.course_name'])->toarray();

        for ($i = 0; $i < count($result); $i++) {

            $countStudent = StudentSubjectEnrolment::where('college_id', '=', $collegeId)
                            ->whereYear('activity_start_date', '=', $yearValue)
                            ->where('college_id', '=', $collegeId)
                            ->where('course_id', '=', $result[$i]['course_id'])
                            ->groupBy('student_id')
                            ->get(['student_id'])->toarray();

            $countStudentCourse = StudentCourse::whereIn('student_id', $countStudent)
                            ->where('status', '=', "Completed")
                            ->where('course_id', '=', $result[$i]['course_id'])
                            ->get(['student_id'])->toarray();

            $result[$i]['studentCount'] = count($countStudent);
            $result[$i]['studentCourseCount'] = count($countStudentCourse);
        }
        return $result;
    }

    public function nvrStudentUnitEnrolment($collegeId, $yearValue) {
        $result = StudentSubjectEnrolment::leftjoin('rto_subject_unit', 'rto_subject_unit.id', '=', 'rto_student_subject_enrolment.unit_id')
                        ->where('rto_student_subject_enrolment.college_id', '=', $collegeId)
                        ->whereYear('rto_student_subject_enrolment.activity_start_date', '=', $yearValue)
                        ->groupBy('rto_student_subject_enrolment.unit_id')
                        ->get(['rto_student_subject_enrolment.*', 'rto_subject_unit.unit_code', 'rto_subject_unit.unit_name'])->toarray();

        for ($i = 0; $i < count($result); $i++) {

            $countStudent = StudentSubjectEnrolment::where('college_id', '=', $collegeId)
                            ->whereYear('activity_start_date', '=', $yearValue)
                            ->where('college_id', '=', $collegeId)
                            ->where('unit_id', '=', $result[$i]['unit_id'])
                            ->groupBy('student_id')
                            ->get(['student_id'])->toarray();

            $countStudentCourse = StudentCourse::whereIn('student_id', $countStudent)
                            ->where('status', '=', "Completed")
                            ->where('course_id', '=', $result[$i]['course_id'])
                            ->get(['student_id'])->toarray();

            $result[$i]['studentCount'] = count($countStudent);
            $result[$i]['studentCourseCount'] = count($countStudentCourse);
        }

        return $result;
    }

    public function listStudentSubjectEnrolmentV2($collegeId, $studentId, $firstArrStudentCourseKey = '', $limit = '') {
        $graddingType = array();
        $sql = StudentSubjectEnrolment::from('rto_student_subject_enrolment as rsse')
                ->leftjoin('rto_semester', 'rto_semester.id', '=', 'rsse.semester_id')
                ->leftjoin('rto_subject', 'rto_subject.id', '=', 'rsse.subject_id')
                ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rsse.course_id')
                ->leftjoin('rto_student_courses', 'rto_student_courses.course_id', '=', 'rsse.course_id')
                ->leftjoin('rto_campus', 'rto_campus.id', '=', 'rto_student_courses.campus_id')
                ->leftjoin('rto_timetable', function($join)
                {
                  $join->on('rto_timetable.subject_id', '=', 'rsse.subject_id');
                  $join->on('rto_timetable.batch','=', 'rsse.batch');
                })
                ->where('rsse.college_id', '=', $collegeId)
                ->where('rsse.student_id', '=', $studentId)
                ->where('rto_student_courses.student_id', '=', $studentId);

        if (!empty($firstArrStudentCourseKey)) {
            $sql->where('rsse.course_id', '=', $firstArrStudentCourseKey);
        }
        if (!empty($limit)) {
            $sql->take($limit);
        }

        $listStudentSubjectEnrolment = $sql->groupBy('rsse.id')
                ->groupBy('rsse.semester_id')
                ->orderBy('rsse.activity_start_date')
                ->get(['rsse.*',
            'rto_semester.semester_name',
            'rto_subject.subject_code',
            'rto_subject.course_type',
            'rto_subject.subject_name',
            'rto_subject.grading_type',
            'rto_student_courses.campus_id',
            'rto_campus.name as campus_name',
            'rto_courses.flexible_attendance',
            'rto_courses.course_type_id','rto_timetable.start_week','rto_timetable.end_week']);

        $arrSelectFinalOutcome = (new ResultGrade)->getGradeResultList($collegeId);

        $arrSubjectList = array();
        for ($i = 0; $i < count($listStudentSubjectEnrolment); $i++) {
            $arrSubjectList[$i] = $listStudentSubjectEnrolment[$i]->subject_id;
        }
        $arrSubjectUnitList = array();
        if (!empty($arrSubjectList)) {
            $objStudentUnitList = $this->getStudentUnitList($collegeId, $arrSubjectList, $studentId);

            for ($i = 0; $i < count($listStudentSubjectEnrolment); $i++) {

                $course_type_id = $listStudentSubjectEnrolment[$i]->course_type;
                $isHigherEd = (new CourseType)->checkHigherEdGradingType($course_type_id);

                $arrSubjectUnitList[$i]['id'] = $listStudentSubjectEnrolment[$i]->id;
                $arrSubjectUnitList[$i]['semester_id'] = $listStudentSubjectEnrolment[$i]->semester_id;
                $arrSubjectUnitList[$i]['subject_id'] = $listStudentSubjectEnrolment[$i]->subject_id;
                $arrSubjectUnitList[$i]['grading_type'] = $listStudentSubjectEnrolment[$i]->grading_type;
                $arrSubjectUnitList[$i]['gradingMarks'] = $listStudentSubjectEnrolment[$i]->marks;
                $arrSubjectUnitList[$i]['gradingType'] = $listStudentSubjectEnrolment[$i]->grade;
                $arrSubjectUnitList[$i]['course_id'] = $listStudentSubjectEnrolment[$i]->course_id;
                $arrSubjectUnitList[$i]['flexible_attendance'] = $listStudentSubjectEnrolment[$i]->flexible_attendance;
                $arrSubjectUnitList[$i]['course_type_id'] = $listStudentSubjectEnrolment[$i]->course_type_id;
                $arrSubjectUnitList[$i]['semester_name'] = $listStudentSubjectEnrolment[$i]->semester_name;
                $arrSubjectUnitList[$i]['term'] = $listStudentSubjectEnrolment[$i]->term;
                $arrSubjectUnitList[$i]['course_stage'] = $listStudentSubjectEnrolment[$i]->course_stage;
                $arrSubjectUnitList[$i]['subject_code'] = $listStudentSubjectEnrolment[$i]->subject_code;
                $arrSubjectUnitList[$i]['subject_name'] = $listStudentSubjectEnrolment[$i]->subject_name;
                $arrSubjectUnitList[$i]['subject_attempt'] = $listStudentSubjectEnrolment[$i]->subject_attempt;
                $arrSubjectUnitList[$i]['batch'] = $listStudentSubjectEnrolment[$i]->batch;
                $arrSubjectUnitList[$i]['activity_start_date'] = $listStudentSubjectEnrolment[$i]->activity_start_date;
                $arrSubjectUnitList[$i]['activity_finish_date'] = $listStudentSubjectEnrolment[$i]->activity_finish_date;
                $arrSubjectUnitList[$i]['start_week'] = $listStudentSubjectEnrolment[$i]->start_week;
                $arrSubjectUnitList[$i]['end_week'] = $listStudentSubjectEnrolment[$i]->end_week;
                $arrSubjectUnitList[$i]['final_outcome'] = $listStudentSubjectEnrolment[$i]->final_outcome;
                $arrSubjectUnitList[$i]['mark_outcome'] = $listStudentSubjectEnrolment[$i]->mark_outcome;
                $arrSubjectUnitList[$i]['isHigherEd'] = $isHigherEd;
                $arrSubjectUnitList[$i]['campus_name'] = $listStudentSubjectEnrolment[$i]->campus_name;

                for ($j = 0; $j < count($objStudentUnitList); $j++) {
                    if (($objStudentUnitList[$j]->unit_subject_id == $arrSubjectUnitList[$i]['subject_id']) && ($objStudentUnitList[$j]->subject_enrolment_semester_id == $listStudentSubjectEnrolment[$i]->semester_id)) {
                        $arrSubjectUnitList[$i]['unit'][] = $objStudentUnitList[$j]->id . ': ' . $objStudentUnitList[$j]->vet_unit_code;
                        $arrSubjectUnitList[$i]['unit_code'][] = $objStudentUnitList[$j]->vet_unit_code;
                        if($isHigherEd){
                            $arrSubjectUnitList[$i]['compentency'][] = (isset($arrSelectFinalOutcome[$objStudentUnitList[$j]->mark_outcome]))?$arrSelectFinalOutcome[$objStudentUnitList[$j]->mark_outcome]:'--';
                        }else{
                            $arrSubjectUnitList[$i]['compentency'][] = $objStudentUnitList[$j]->compentency;
                        }

                        $arrSubjectUnitList[$i]['unit_study_from_to'][] = date("d-m-Y", strtotime($objStudentUnitList[$j]->study_from)) . " - " . date("d-m-Y", strtotime($objStudentUnitList[$j]->study_to));
                    }
                }
            }
        }
        // echo "<pre>";print_r($arrSubjectUnitList);exit;
        // dd($arrSubjectUnitList);
        return $arrSubjectUnitList;
    }

    public function listStudentEnrolmentData($collegeId, $studentId) {
        $graddingType = array();
        $sql = StudentSubjectEnrolment::from('rto_student_subject_enrolment as rsse')
                ->leftjoin('rto_semester', 'rto_semester.id', '=', 'rsse.semester_id')
                ->leftjoin('rto_subject', 'rto_subject.id', '=', 'rsse.subject_id')
                ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rsse.course_id')
                ->leftjoin('rto_student_courses', 'rto_student_courses.course_id', '=', 'rsse.course_id')
                ->leftjoin('rto_campus', 'rto_campus.id', '=', 'rto_student_courses.campus_id')
                // ->leftjoin('rto_timetable', function($join)
                // {
                //   $join->on('rto_timetable.subject_id', '=', 'rsse.subject_id');
                //   $join->on('rto_timetable.batch','=', 'rsse.batch');
                // })
                ->where('rsse.college_id', '=', $collegeId)
                ->where('rsse.student_id', '=', $studentId)
                ->where('rto_student_courses.student_id', '=', $studentId);

        $listStudentSubjectEnrolment = $sql->groupBy('rsse.subject_id')
                ->groupBy('rsse.subject_attempt')
                ->orderBy('rsse.activity_start_date')
                ->get(['rsse.*',
            'rto_semester.semester_name',
            'rto_subject.subject_code',
            'rto_subject.course_type',
            'rto_subject.subject_name',
            'rto_subject.grading_type',
            'rto_student_courses.campus_id',
            'rto_campus.name as campus_name',
            'rto_courses.flexible_attendance',
            'rto_courses.course_type_id'
            // 'rto_timetable.start_week','rto_timetable.end_week'
        ]);

        $arrSelectFinalOutcome = (new ResultGrade)->getGradeResultList($collegeId);

        $arrSubjectList = array();
        for ($i = 0; $i < count($listStudentSubjectEnrolment); $i++) {
            $arrSubjectList[$i] = $listStudentSubjectEnrolment[$i]->subject_id;
        }
        $arrSubjectUnitList = array();
        if (!empty($arrSubjectList)) {
            $objStudentUnitList = $this->getStudentUnitList($collegeId, $arrSubjectList, $studentId);

            for ($i = 0; $i < count($listStudentSubjectEnrolment); $i++) {

                $course_type_id = $listStudentSubjectEnrolment[$i]->course_type;
                $isHigherEd = (new CourseType)->checkHigherEdGradingType($course_type_id);

                $arrSubjectUnitList[$i]['id'] = $listStudentSubjectEnrolment[$i]->id;
                $arrSubjectUnitList[$i]['semester_id'] = $listStudentSubjectEnrolment[$i]->semester_id;
                $arrSubjectUnitList[$i]['subject_id'] = $listStudentSubjectEnrolment[$i]->subject_id;
                $arrSubjectUnitList[$i]['grading_type'] = $listStudentSubjectEnrolment[$i]->grading_type;
                $arrSubjectUnitList[$i]['gradingMarks'] = $listStudentSubjectEnrolment[$i]->marks;
                $arrSubjectUnitList[$i]['gradingType'] = $listStudentSubjectEnrolment[$i]->grade;
                $arrSubjectUnitList[$i]['course_id'] = $listStudentSubjectEnrolment[$i]->course_id;
                $arrSubjectUnitList[$i]['flexible_attendance'] = $listStudentSubjectEnrolment[$i]->flexible_attendance;
                $arrSubjectUnitList[$i]['course_type_id'] = $listStudentSubjectEnrolment[$i]->course_type_id;
                $arrSubjectUnitList[$i]['semester_name'] = $listStudentSubjectEnrolment[$i]->semester_name;
                $arrSubjectUnitList[$i]['term'] = $listStudentSubjectEnrolment[$i]->term;
                $arrSubjectUnitList[$i]['course_stage'] = $listStudentSubjectEnrolment[$i]->course_stage;
                $arrSubjectUnitList[$i]['subject_code'] = $listStudentSubjectEnrolment[$i]->subject_code;
                $arrSubjectUnitList[$i]['subject_name'] = $listStudentSubjectEnrolment[$i]->subject_name;
                $arrSubjectUnitList[$i]['subject_attempt'] = $listStudentSubjectEnrolment[$i]->subject_attempt;
                $arrSubjectUnitList[$i]['batch'] = $listStudentSubjectEnrolment[$i]->batch;
                $arrSubjectUnitList[$i]['activity_start_date'] = $listStudentSubjectEnrolment[$i]->activity_start_date;
                $arrSubjectUnitList[$i]['activity_finish_date'] = $listStudentSubjectEnrolment[$i]->activity_finish_date;
                $arrSubjectUnitList[$i]['start_week'] = $listStudentSubjectEnrolment[$i]->start_week;
                $arrSubjectUnitList[$i]['end_week'] = $listStudentSubjectEnrolment[$i]->end_week;
                $arrSubjectUnitList[$i]['final_outcome'] = $listStudentSubjectEnrolment[$i]->final_outcome;
                $arrSubjectUnitList[$i]['mark_outcome'] = $listStudentSubjectEnrolment[$i]->mark_outcome;
                $arrSubjectUnitList[$i]['isHigherEd'] = $isHigherEd;
                $arrSubjectUnitList[$i]['campus_name'] = $listStudentSubjectEnrolment[$i]->campus_name;

                for ($j = 0; $j < count($objStudentUnitList); $j++) {
                    if (($objStudentUnitList[$j]->unit_subject_id == $arrSubjectUnitList[$i]['subject_id'])
                    && ($objStudentUnitList[$j]->subject_enrolment_semester_id == $listStudentSubjectEnrolment[$i]->semester_id)
                    && ($objStudentUnitList[$j]->subject_attempt == $listStudentSubjectEnrolment[$i]->subject_attempt)
                    )
                    {
                        $arrSubjectUnitList[$i]['unit'][] = $objStudentUnitList[$j]->id . ': ' . $objStudentUnitList[$j]->vet_unit_code;
                        $arrSubjectUnitList[$i]['unit_code'][] = $objStudentUnitList[$j]->vet_unit_code;
                        if($isHigherEd){
                            $arrSubjectUnitList[$i]['compentency'][] = (isset($arrSelectFinalOutcome[$objStudentUnitList[$j]->mark_outcome]))?$arrSelectFinalOutcome[$objStudentUnitList[$j]->mark_outcome]:'--';
                        }else{
                            $arrSubjectUnitList[$i]['compentency'][] = $objStudentUnitList[$j]->compentency;
                        }

                        $arrSubjectUnitList[$i]['unit_study_from_to'][] = date("d-m-Y", strtotime($objStudentUnitList[$j]->study_from)) . " - " . date("d-m-Y", strtotime($objStudentUnitList[$j]->study_to));
                    }
                }
            }
        }

        return $arrSubjectUnitList;
    }

    public function listStudentSubjectEnrolmentSemesterTerm($collegeId, $studentId, $firstArrStudentCourseKey = '', $limit = '', $semesterId, $term) {
        $graddingType = array();
        $sql = StudentSubjectEnrolment::from('rto_student_subject_enrolment as rsse')
                ->leftjoin('rto_semester', 'rto_semester.id', '=', 'rsse.semester_id')
                ->leftjoin('rto_subject', 'rto_subject.id', '=', 'rsse.subject_id')
                ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rsse.course_id')
                ->leftjoin('rto_student_courses', 'rto_student_courses.course_id', '=', 'rsse.course_id')
                ->leftjoin('rto_campus', 'rto_campus.id', '=', 'rto_student_courses.campus_id')
                ->where('rsse.college_id', '=', $collegeId)
                ->where('rsse.student_id', '=', $studentId)
                ->where('rsse.semester_id', '=', $semesterId)
                ->where('rsse.term', '=', $term)
                ->where('rto_student_courses.student_id', '=', $studentId);

        if (!empty($firstArrStudentCourseKey)) {
            $sql->where('rsse.course_id', '=', $firstArrStudentCourseKey);
        }
        if (!empty($limit)) {
            $sql->take($limit);
        }

        $listStudentSubjectEnrolment = $sql->groupBy('rsse.subject_id')
                ->groupBy('rsse.semester_id')
                ->orderBy('rsse.activity_start_date')
                ->get(['rsse.*',
            'rto_semester.semester_name',
            'rto_subject.subject_code',
            'rto_subject.course_type',
            'rto_subject.subject_name',
            'rto_subject.grading_type',
            'rto_student_courses.campus_id',
            'rto_campus.name as campus_name',
            'rto_courses.flexible_attendance',
            'rto_courses.course_type_id']);

        $arrSubjectUnitList = array();
        if (count($listStudentSubjectEnrolment) > 0) {
            for ($i = 0; $i < count($listStudentSubjectEnrolment); $i++) {
                $arrSubjectUnitList[$i]['id']                   = $listStudentSubjectEnrolment[$i]->id;
                $arrSubjectUnitList[$i]['semester_id']          = $listStudentSubjectEnrolment[$i]->semester_id;
                $arrSubjectUnitList[$i]['subject_id']           = $listStudentSubjectEnrolment[$i]->subject_id;
                $arrSubjectUnitList[$i]['grading_type']         = $listStudentSubjectEnrolment[$i]->grading_type;
                $arrSubjectUnitList[$i]['gradingMarks']         = $listStudentSubjectEnrolment[$i]->marks;
                $arrSubjectUnitList[$i]['gradingType']          = $listStudentSubjectEnrolment[$i]->grade;
                $arrSubjectUnitList[$i]['course_id']            = $listStudentSubjectEnrolment[$i]->course_id;
                $arrSubjectUnitList[$i]['flexible_attendance']  = $listStudentSubjectEnrolment[$i]->flexible_attendance;
                $arrSubjectUnitList[$i]['course_type_id']       = $listStudentSubjectEnrolment[$i]->course_type_id;
                $arrSubjectUnitList[$i]['semester_name']        = $listStudentSubjectEnrolment[$i]->semester_name;
                $arrSubjectUnitList[$i]['term']                 = $listStudentSubjectEnrolment[$i]->term;
                $arrSubjectUnitList[$i]['course_stage']         = $listStudentSubjectEnrolment[$i]->course_stage;
                $arrSubjectUnitList[$i]['subject_code']         = $listStudentSubjectEnrolment[$i]->subject_code;
                $arrSubjectUnitList[$i]['subject_name']         = $listStudentSubjectEnrolment[$i]->subject_name;
                $arrSubjectUnitList[$i]['subject_attempt']      = $listStudentSubjectEnrolment[$i]->subject_attempt;
                $arrSubjectUnitList[$i]['batch']                = $listStudentSubjectEnrolment[$i]->batch;
                $arrSubjectUnitList[$i]['activity_start_date']  = $listStudentSubjectEnrolment[$i]->activity_start_date;
                $arrSubjectUnitList[$i]['activity_finish_date'] = $listStudentSubjectEnrolment[$i]->activity_finish_date;
                $arrSubjectUnitList[$i]['final_outcome']        = $listStudentSubjectEnrolment[$i]->final_outcome;
                $arrSubjectUnitList[$i]['campus_name']          = $listStudentSubjectEnrolment[$i]->campus_name;
            }
        }
        return $arrSubjectUnitList;
    }

    public function getStudentUnitList($collegeId, $arrSubjectList, $studentId) {
        return StudentSubjectEnrolment::leftjoin('rto_subject_unit as rsu', 'rsu.id', '=', 'rto_student_subject_enrolment.unit_id')
                        ->leftjoin('rto_student_unit_enrollment as rsue', 'rsue.student_subject_enrollment_id', '=', 'rto_student_subject_enrolment.id')
                        ->where('rto_student_subject_enrolment.college_id', '=', $collegeId)
                        ->where('rto_student_subject_enrolment.student_id', '=', $studentId)
                        ->whereIn('rsu.subject_id', $arrSubjectList)
                        ->get(['rto_student_subject_enrolment.subject_attempt','rsu.unit_name', 'rsu.vet_unit_code', 'rto_student_subject_enrolment.id as subject_enrolment_id', 'rto_student_subject_enrolment.semester_id as subject_enrolment_semester_id', 'rto_student_subject_enrolment.mark_outcome', 'rsu.subject_id as unit_subject_id', 'rsu.id', 'rsue.compentency', 'rsue.study_from', 'rsue.study_to']);
    }

    public function checkDuplicateAssessment($studentId, $collegeId, $request) {

        $returnValue = "Valid Record";

        $course_id = $request->input('course_id');
        $calendarId = $request->input('calendar_id');
        $semester = $request->input('semester');

        $term = $request->input('term');
        $typeOfEnrollment = $request->input('type_of_enrollment');

        $validRecord = StudentSubjectEnrolment::where('college_id', '=', $collegeId)
                ->where('student_id', '=', $studentId)
                ->where('course_id', '=', $course_id);
                // ->where('semester_id', '=', $semester);

        $isHigherEd = $request->input('isHigherEd');
        if ($typeOfEnrollment == 'subject') {
            $subject_id = $request->input('subject_id');
            $validRecord->whereIn('subject_id', $subject_id);

            // For HigherEd if failed then give second attempt
            if($isHigherEd){
                $arrSelectMarksOutcome = ResultGrade::getFailedGradeResult($collegeId);
                $filedResultArray = $arrSelectMarksOutcome->pluck('id')->toArray();
                $validRecord->whereNotIn('mark_outcome', $filedResultArray);
            }else{
                $validRecord->where('final_outcome', '!=','NYC');
            }

        } else {
            $subject_id = $request->input('subject_list');
            $unit_id = $request->input('unit_id');
            $validRecord->where('subject_id', $subject_id);
            $validRecord->whereIn('unit_id', $unit_id);
            if($isHigherEd){
                $arrSelectMarksOutcome = ResultGrade::getFailedGradeResult($collegeId);
                $filedResultArray = $arrSelectMarksOutcome->pluck('id')->toArray();
                $validRecord->whereNotIn('mark_outcome', $filedResultArray);
            }else{
                $validRecord->where('final_outcome', '!=','NYC');
            }
        }
        $arrValid = $validRecord->get();

        if ($arrValid->count() > 0) {
            $returnValue = "Duplicate-Record";
        }

        return $returnValue;
    }

    public function checkClassCapecityAssessment($collegeId, $enrollId, $request) {
        $getMatchRecord = StudentSubjectEnrolment::where('college_id', '=', $collegeId)->where('id', '=', $enrollId)->get(['term', 'semester_id']);

        $semesterId = $getMatchRecord[0]->semester_id;
        $term = $getMatchRecord[0]->term;
        $batch = $request->input('batch');

        $validRecord = StudentSubjectEnrolment::where('college_id', '=', $collegeId)
                ->where('id', '!=', $enrollId)
                ->where('batch', '=', $batch)
                ->where('term', '=', $term)
                ->where('semester_id', '=', $semesterId);
        $arrValid = $validRecord->get();

        return $arrValid->count();
    }

    public function checkClassCapecityAssessmentBulkEnrollmentByGroup($collegeId, $semesterId, $batch, $term) {

        $validRecord = StudentSubjectEnrolment::where('college_id', '=', $collegeId)
                ->where('batch', '=', $batch)
                ->where('term', '=', $term)
                ->where('semester_id', '=', $semesterId)
                ->groupby(['rto_student_subject_enrolment.student_id', 'rto_student_subject_enrolment.course_id', 'rto_student_subject_enrolment.student_id']);
        $arrValid = $validRecord->get();

        return $arrValid->count();
    }

    public function getStudentEnrollData($collegeId, $enrollId) {

        return StudentSubjectEnrolment::from('rto_student_subject_enrolment as rsse')
                        ->leftjoin('rto_venue as rv', 'rv.id', '=', 'rsse.vanue_location')
                        ->leftjoin('rto_subject', 'rto_subject.id', '=', 'rsse.subject_id')
                        ->where('rsse.college_id', '=', $collegeId)
                        ->where('rsse.id', '=', $enrollId)
                        ->select('rsse.*', 'rv.venue_code', 'rv.venue_name', 'rto_subject.grading_type')
                        ->get();
    }

    public function getUnitFeeTotal($collegeId, $enrollId) {
        return $result = StudentSubjectEnrolment::leftjoin('rto_subject_unit as rsu', 'rsu.subject_id', '=', 'rto_student_subject_enrolment.subject_id')
                ->where('rto_student_subject_enrolment.id', '=', $enrollId)
                ->sum('rsu.tution_fees');
    }

    public function updateStudentEnrollData($collegeId, $enrollId, $request) {
        //get match records nased enroll id
        $getMatchRecord = StudentSubjectEnrolment::where('college_id', '=', $collegeId)->where('id', '=', $enrollId)->get(['student_id', 'course_id', 'subject_id', 'semester_id','subject_attempt','mark_outcome','grade']);

        $studentId = $getMatchRecord[0]->student_id;
        $courseId = $getMatchRecord[0]->course_id;
        $subjectId = $getMatchRecord[0]->subject_id;
        $semesterId = $getMatchRecord[0]->semester_id;
        $graddingType = $getMatchRecord[0]->grade;
        $selectMarkOutcome = $getMatchRecord[0]->mark_outcome;
        $subjectAttempt = $getMatchRecord[0]->subject_attempt;

        if ($request->input('graddingIsMarks') == 1 && $request->input('marks') != '') {
            $objSubject = new Subject();
            $arrSubject = $objSubject->getSubjectlistCollegeIdWise($subjectId);

            $objResultGrade = new ResultGrade();
            $arrResultGrade = $objResultGrade->getResultGradeInfoSubject($collegeId, $arrSubject[0]['grading_type']);

            for ($j = 0; $j < count($arrResultGrade); $j++) {
                if ($arrResultGrade[$j]["maximum_marks"] >= $request->input('marks')) {
                    $graddingType = $arrResultGrade[$j]["grade"];
                    $selectMarkOutcome = $arrResultGrade[$j]["id"];
                    break;
                }
            }
        }

        //update all matching records
        $whereArr = [
            'college_id'    => $collegeId,
            'student_id'    => $studentId,
            'course_id'     => $courseId,
            'subject_id'    => $subjectId,
            'semester_id'   => $semesterId,
            'subject_attempt'   => $subjectAttempt,
        ];

        $objUpdateEnrollData = StudentSubjectEnrolment::find($enrollId);
        
        $objUpdateEnrollData->update([
            'census_date'           => ($request->input('census_date') != '') ? date('Y-m-d', strtotime($request->input('census_date'))) : null,
            'marks'                 => ($request->input('marks') != '') ? $request->input('marks') : null,
            'grade'                 => $graddingType,
            'mark_outcome'          => $selectMarkOutcome,
            'mode_of_attendance'    => ($request->input('mode_of_attendance') != '') ? $request->input('mode_of_attendance') : null,
            'subject_tution_fee'    => ($request->input('subject_tution_fee') != '') ? $request->input('subject_tution_fee') : null,
            'fee_paid_upfront'      => ($request->input('fee_paid_upfront') != '') ? $request->input('fee_paid_upfront') : null,
            'eftsl_study_load'      => ($request->input('eftsl_study_load') != '') ? $request->input('eftsl_study_load') : null,
            'activity_start_date'   => ($request->input('activity_start_date') != '') ? date('Y-m-d', strtotime($request->input('activity_start_date'))) : null,
            'activity_finish_date'  => ($request->input('activity_finish_date') != '') ? date('Y-m-d', strtotime($request->input('activity_finish_date'))) : null,
            'study_reason'          => ($request->input('study_reason') != '') ? $request->input('study_reason') : null,
            'funding_source_state'  => ($request->input('funding_source_state') != '') ? $request->input('funding_source_state') : null,
            'funding_source_nat'    => ($request->input('funding_source_nat') != '') ? $request->input('funding_source_nat') : null,
            'final_outcome'         => ($request->input('final_outcome') != '') ? $request->input('final_outcome') : null,
            'outcome_identifier'    => ($request->input('outcome_identifier') != '') ? $request->input('outcome_identifier') : null,
            'comment'               => ($request->input('comment') != '') ? $request->input('comment') : null,
            'batch'                 => ($request->input('batch') != '') ? $request->input('batch') : null,
            'updated_by'            => Auth::user()->id
        ]);

        //TODO:: GNG-1711

        $objSubjectEnrollArr = StudentSubjectEnrolment::where($whereArr);

        $objSubjectEnrollArrClone = clone $objSubjectEnrollArr;

        $subjectEnrollArr = $objSubjectEnrollArr->groupBy('batch')->get(['batch'])->toArray();
        $batchArr = array_column($subjectEnrollArr, 'batch');

        $getEnrolledId = $objSubjectEnrollArrClone->pluck('id')->toArray();

        StudentUnitEnrollment::whereIn('student_subject_enrollment_id', $getEnrolledId)->update([
            'compentency'           => ($request->input('final_outcome') != '') ? $request->input('final_outcome') : null,
            'study_from'           => ($request->input('activity_start_date') != '') ? date('Y-m-d', strtotime($request->input('activity_start_date'))) : null,
            'study_to'             => ($request->input('activity_finish_date') != '') ? date('Y-m-d', strtotime($request->input('activity_finish_date'))) : null,
        ]);

        event(new \App\Events\EnrollStudentAttendanceEvent($studentId, $batchArr));
        //$this->manageDefaultAttendance($studentId, $batchArr);

        return true;
    }

    public function getUnitCodeList($collegeId, $studentId, $courseId) {
        return StudentSubjectEnrolment::from('rto_student_subject_enrolment as rsse')
                        ->leftjoin('rto_subject as rs', 'rs.id', '=', 'rsse.subject_id')
                        ->leftjoin('rto_subject_unit as rsu', 'rsu.id', '=', 'rsse.unit_id')
                        ->where('rsse.college_id', '=', $collegeId)
                        ->where('rsse.student_id', '=', $studentId)
                        ->where('rsse.course_id', '=', $courseId)
                        ->select(DB::raw("group_concat(DISTINCT rsu.vet_unit_code SEPARATOR ', ') as unit_code"))
                        ->get();
    }

    public function getSubjectAndUnitDetail($collegeId, $studentId, $courseId) {
        return StudentSubjectEnrolment::from('rto_student_subject_enrolment as rsse')
                        ->leftjoin('rto_subject as rs', 'rs.id', '=', 'rsse.subject_id')
                        ->leftjoin('rto_subject_unit as rsu', 'rsu.id', '=', 'rsse.unit_id')
                        ->where('rsse.college_id', '=', $collegeId)
                        ->where('rsse.student_id', '=', $studentId)
                        ->where('rsse.course_id', '=', $courseId)
                        ->get(['rs.subject_code', 'rsu.vet_unit_code', 'rsse.subject_id', 'rsse.unit_id', 'rsse.id']);

    }

    public function getSubjectUnitDetail($primaryId) {
        return StudentSubjectEnrolment::leftjoin('rto_subject_unit as rsu', 'rsu.id', '=', 'rto_student_subject_enrolment.unit_id')
                        ->leftjoin('rto_student_unit_enrollment as sue', 'sue.student_subject_enrollment_id', '=', 'rto_student_subject_enrolment.id')
                        ->where('rto_student_subject_enrolment.college_id', '=', Auth::user()->college_id)
                        ->whereIn('rto_student_subject_enrolment.id', $primaryId)
                        ->get(['rsu.vet_unit_code',
                            'rto_student_subject_enrolment.subject_id',
                            'rto_student_subject_enrolment.unit_id',
                            'rto_student_subject_enrolment.id',
                            'sue.compentency as unitCompentency'])
                        ->toarray();
    }

    public function saveStudentUnitActivity($collegeId, $studentId, $courseId, $request) {

        $subjectId = $request->input('subject_id');
        $unitId = $request->input('unit_id');

        for ($i = 0; $i < count($subjectId); $i++) {
            $objSaveUnitActivity = StudentSubjectEnrolment::where('college_id', '=', $collegeId)
                    ->where('student_id', '=', $studentId)
                    ->where('course_id', '=', $courseId)
                    ->where('subject_id', '=', $subjectId[$i])
                    ->where('unit_id', '=', $unitId[$i])
                    ->update([
                'funding_source_state' => ($request->input('funding_source_state') != '') ? $request->input('funding_source_state') : null,
                'funding_source_nat' => ($request->input('funding_source_nat') != '') ? $request->input('funding_source_nat') : null,
                'funding_identifier' => ($request->input('funding_identifier') != '') ? $request->input('funding_identifier') : null,
                'study_reason' => ($request->input('study_reason') != '') ? $request->input('study_reason') : null,
                'course_commencing_id' => ($request->input('course_commencing_id') != '') ? $request->input('course_commencing_id') : null,
                'training_contract_id' => ($request->input('training_contract_id') != '') ? $request->input('training_contract_id') : null,
                'apprenticeship_client_id' => ($request->input('apprenticeship_client_id') != '') ? $request->input('apprenticeship_client_id') : null,
                'vet_in_school' => ($request->input('vet_in_school') != '') ? $request->input('vet_in_school') : null,
                'fee_exemption_id' => ($request->input('fee_exemption_id') != '') ? $request->input('fee_exemption_id') : null,
                'purchase_contract_id' => ($request->input('purchase_contract_id') != '') ? $request->input('purchase_contract_id') : null,
                'con_schedule_id' => ($request->input('con_schedule_id') != '') ? $request->input('con_schedule_id') : null,
                'booking_id' => ($request->input('booking_id') != '') ? $request->input('booking_id') : null,
                'course_site_id' => ($request->input('course_site_id') != '') ? $request->input('course_site_id') : null,
                'delivery_mode' => ($request->input('delivery_mode') != '') ? $request->input('delivery_mode') : null,
                'predominant_delivery_mode' => ($request->input('predominant_delivery_mode') != '') ? $request->input('predominant_delivery_mode') : null,
            ]);
        }
    }

    public function getStudentDetail($collegeId, $enrollId) {
        return StudentSubjectEnrolment::leftjoin('rto_semester as rs', 'rs.id', '=', 'rto_student_subject_enrolment.semester_id')
                        ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rto_student_subject_enrolment.course_id')
                        ->leftjoin('rto_students as rst', 'rst.id', '=', 'rto_student_subject_enrolment.student_id')
                        ->leftjoin('rto_subject as rsub', 'rsub.id', '=', 'rto_student_subject_enrolment.subject_id')
                        ->leftjoin('rto_student_courses as rsc', 'rsc.course_id', '=', 'rto_student_subject_enrolment.course_id')
                        ->leftjoin('rto_campus as rcm', 'rcm.id', '=', 'rsc.campus_id')
                        ->where('rto_student_subject_enrolment.college_id', '=', $collegeId)
                        ->where('rto_student_subject_enrolment.id', '=', $enrollId)
                        ->get(['rsc.course_attempt', 'rsc.start_date', 'rsc.finish_date', 'rcm.name as campus_name', 'rs.course_type_id', 'rs.semester_name', 'rs.year', 'rc.course_code', 'rc.course_name', 'rst.first_name', 'rst.middel_name', 'rst.generated_stud_id', 'rsub.subject_name', 'rsub.subject_code', 'rto_student_subject_enrolment.*']);
    }

    //update final_outcome after assessment confirmation
    public function _assessmentConfirmation($collegeId, $assessmentCompetency, $enrollId) {
        $objStudentEnrolment = StudentSubjectEnrolment::where('college_id', '=', $collegeId)
                ->where('id', '=', $enrollId)
                ->update([
            'final_outcome' => ($assessmentCompetency != "") ? $assessmentCompetency : null,
            'updated_by' => Auth::user()->id,
        ]);
    }

    //update batch and active and finish dates
    public function _updateBatchDates($collegeId, $newStartDate, $newFinishDate, $enrollId, $batch) {
        //get match records nased enroll id
        $getMatchRecord = StudentSubjectEnrolment::where('college_id', '=', $collegeId)->where('id', '=', $enrollId)->get(['student_id', 'course_id', 'subject_id', 'semester_id']);

        $studentId = $getMatchRecord[0]->student_id;
        $courseId = $getMatchRecord[0]->course_id;
        $subjectId = $getMatchRecord[0]->subject_id;
        $semesterId = $getMatchRecord[0]->semester_id;

        //update all matching records
        $objUpdateBatchDates = StudentSubjectEnrolment::where('college_id', '=', $collegeId)
                ->where('student_id', '=', $studentId)
                ->where('course_id', '=', $courseId)
                ->where('subject_id', '=', $subjectId)
                ->where('semester_id', '=', $semesterId)
                ->update([
            'activity_start_date' => ($newStartDate != '') ? date('Y-m-d', strtotime($newStartDate)) : null,
            'activity_finish_date' => ($newFinishDate != '') ? date('Y-m-d', strtotime($newFinishDate)) : null,
            'batch' => ($batch != '') ? $batch : null,
        ]);
    }

    //get all unit_id from student_unit_enrollment
    public function getUnitIdListing($collegeId, $studentId, $subjectId, $courseId, $semesterId) {
        return StudentSubjectEnrolment::where('college_id', '=', $collegeId)
                        ->where('student_id', '=', $studentId)
                        ->where('subject_id', '=', $subjectId)
                        ->where('course_id', '=', $courseId)
                        ->where('semester_id', '=', $semesterId)
                        ->get(['unit_id']);
    }

    public function getUnitIdListingV2($enrollId, $collegeId, $studentId, $subjectId, $courseId, $semesterId) {
        return StudentSubjectEnrolment::where('college_id', '=', $collegeId)
                        ->where('student_id', '=', $studentId)
                        ->where('subject_id', '=', $subjectId)
                        ->where('course_id', '=', $courseId)
                        ->where('semester_id', '=', $semesterId)
                        ->where('id', '=', $enrollId)
                        ->get(['unit_id']);
    }

    //get all id from student_unit_enrollment
    public function getEnrollIdListing($collegeId, $studentId, $subjectId, $courseId, $semesterId) {
        return StudentSubjectEnrolment::where('college_id', '=', $collegeId)
                        ->where('student_id', '=', $studentId)
                        ->where('subject_id', '=', $subjectId)
                        ->where('course_id', '=', $courseId)
                        ->where('semester_id', '=', $semesterId)
                        ->get(['id']);
    }

    //get all id from student_unit_enrollment without course is
    public function getWithoutCourseEnrollIdListing($collegeId, $studentId, $subjectId, $unitId, $semesterId, $term, $batch) {
        return StudentSubjectEnrolment::where('college_id', '=', $collegeId)
                        ->where('student_id', '=', $studentId)
                        ->where('subject_id', '=', $subjectId)
                        ->where('unit_id', '=', $unitId)
                        ->where('semester_id', '=', $semesterId)
                        ->where('term', '=', $term)
                        ->where('batch', '=', $batch)
                        ->get(['id']);
    }

    //view acedemic summary
    public function getCourseSubjectSummary($collegeId, $studentId, $courseId) {
        return StudentSubjectEnrolment::where('college_id', '=', $collegeId)
                        ->where('student_id', '=', $studentId)
                        ->where('course_id', '=', $courseId)
                        ->groupBy('subject_id')
                        ->get(['subject_id', 'activity_start_date', 'activity_finish_date', 'batch', 'final_outcome']);
    }

    // get subjects based on the semester id and term
    public function getStudentUnitData($sub_student_id, $sub_course_id, $sub_semester_id, $sub_subject_id) {

        return StudentSubjectEnrolment::leftjoin('rto_subject_unit', 'rto_subject_unit.id', '=', 'rto_student_subject_enrolment.unit_id')
                        ->leftjoin('rto_venue', 'rto_venue.id', '=', 'rto_student_subject_enrolment.vanue_location')
                        ->where('rto_student_subject_enrolment.college_id', '=', Auth::user()->college_id)
                        ->where('rto_student_subject_enrolment.student_id', '=', $sub_student_id)
                        ->where('rto_student_subject_enrolment.course_id', '=', $sub_course_id)
                        ->where('rto_student_subject_enrolment.semester_id', '=', $sub_semester_id)
                        ->where('rto_student_subject_enrolment.subject_id', '=', $sub_subject_id)
                        ->get(['rto_subject_unit.unit_code',
                            'rto_subject_unit.unit_name',
                            'rto_venue.venue_code',
                            'rto_venue.state'])
                        ->toArray();
    }

    public function getSubjectsBySemestersTerm($semesterId, $term, $collegeId) {

        return StudentSubjectEnrolment::join('rto_subject', 'rto_subject.id', '=', 'rto_student_subject_enrolment.subject_id')
                        ->where('rto_student_subject_enrolment.college_id', '=', $collegeId)
                        ->where('rto_student_subject_enrolment.semester_id', '=', $semesterId)
                        ->where('rto_student_subject_enrolment.term', '=', $term)
                        ->whereNotNull('rto_student_subject_enrolment.batch')
                        ->groupBy('rto_student_subject_enrolment.subject_id')
                        ->get(['rto_subject.subject_code', 'rto_subject.subject_name', 'rto_subject.id'])
                        ->toArray();
    }

    public function getStudentEnrolmentDetails($studentId) {
        if (!empty(Auth()->guard('student')->user())) {
            $collegeId = Auth()->guard('student')->user()->college_id;
        }else {
            $collegeId = Auth::user()->college_id;
        }
        $monday = strtotime("last monday");
        $monday = date('w', $monday) == date('w') ? $monday + 7 * 86400 : $monday;
        $sunday = strtotime(date("Y-m-d", $monday) . " +6 days");
        $this_week_sd = date("Y-m-d", $monday);
        $this_week_ed = date("Y-m-d", $sunday);

        return $arrEnrollment = StudentSubjectEnrolment::where('college_id', '=', $collegeId)
                ->where('student_id', '=', $studentId)
                ->where(function($query) use ($this_week_sd, $this_week_ed) {
                    $query->where(function($query) use ($this_week_sd, $this_week_ed) {
                        $query->where('activity_start_date', '>=', $this_week_sd);
                        $query->where('activity_start_date', '<=', $this_week_ed);
                    });
                    $query->orWhere(function($query) use ($this_week_sd, $this_week_ed) {
                        $query->where('activity_finish_date', '>=', $this_week_sd);
                        $query->where('activity_finish_date', '<=', $this_week_ed);
                    });
                    $query->orWhere(function($query) use ($this_week_sd, $this_week_ed) {
                        $query->where('activity_start_date', '<=', $this_week_sd);
                        $query->where('activity_finish_date', '>=', $this_week_ed);
                    });
                })
                ->groupby(['batch'])
                ->get(['subject_id', 'term', 'batch'])
                ->toarray();
    }

    public function getStudentListV2($collegeId, $subjectId, $semesterId, $term, $batch, $assessmentTaskId, $arrStudentIds = [], $isHigherEd=false) {

        $totalMarks = $passMarks = $weightMarks = 0;
        $whereArr = [
            'college_id'        => $collegeId,
            'subject_id'        => $subjectId,
            'semester_id'       => $semesterId,
            'term'              => $term,
            'batch'             => $batch,
            'assessment_task_id'=> $assessmentTaskId
        ];

        $marksRes = AssignedAssessmentTask::Where($whereArr)->get()->first();

        $unitList = AssignedAssessmentTaskUnit::where('assign_assessment_task_id',$marksRes->id)->pluck('unit_id')->toArray();

        if($isHigherEd){
            $totalMarks = (isset($marksRes->total_marks) && !empty($marksRes->total_marks)) ? $marksRes->total_marks : 0;
            $passMarks = (isset($marksRes->pass_marks) && !empty($marksRes->pass_marks)) ? $marksRes->pass_marks : 0;
            $weightMarks = (isset($marksRes->assessment_weight) && !empty($marksRes->assessment_weight)) ? $marksRes->assessment_weight : 0;
        }

        $qry = StudentSubjectEnrolment:: leftJoin('rto_student_assigned_assessment_tasks', function($join) use ($assessmentTaskId) {
                    $join->on('rto_student_assigned_assessment_tasks.batch', '=', 'rto_student_subject_enrolment.batch');
                    $join->on('rto_student_assigned_assessment_tasks.student_id', '=', 'rto_student_subject_enrolment.student_id');
                    $join->on('rto_student_assigned_assessment_tasks.assessment_task_id', '=', DB::raw($assessmentTaskId));
                })
                ->leftjoin('rto_students', 'rto_students.id', '=', 'rto_student_subject_enrolment.student_id')
                ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_subject_enrolment.course_id')
                ->leftJoin('rto_student_courses', function($join) {
                    $join->on('rto_student_courses.course_id', '=', 'rto_student_subject_enrolment.course_id');
                    $join->on('rto_student_courses.student_id', '=', 'rto_student_subject_enrolment.student_id');
                })
                ->where('rto_student_subject_enrolment.college_id', '=', $collegeId)
                ->where('rto_student_subject_enrolment.subject_id', '=', $subjectId)
                ->whereIn('rto_student_subject_enrolment.unit_id',  $unitList)
                ->where('rto_student_subject_enrolment.semester_id', '=', $semesterId)
                ->where('rto_student_subject_enrolment.term', '=', $term)
                ->where('rto_student_subject_enrolment.batch', '=', $batch);
        if (!empty($arrStudentIds)) {
            $qry = $qry->whereIn('rto_students.id', $arrStudentIds);
        }
        $resArr = $qry->groupby(['rto_student_subject_enrolment.student_id'])
                        ->orderBy('rto_students.first_name', 'ASC')
                        ->orderBy('rto_students.family_name', 'ASC')
                        ->get(['rto_student_subject_enrolment.student_id',
                            'rto_students.generated_stud_id',
                            'rto_students.first_name',
                            'rto_students.family_name',
                            'rto_student_subject_enrolment.course_id',
                            'rto_courses.course_code',
                            'rto_courses.course_name',
                            'rto_student_courses.status',
                            'rto_student_assigned_assessment_tasks.is_locked',
                            'rto_student_assigned_assessment_tasks.is_approved',
                            'rto_student_assigned_assessment_tasks.competency',
                            'rto_student_assigned_assessment_tasks.comments',
                            'rto_student_assigned_assessment_tasks.id',
                            'rto_student_assigned_assessment_tasks.subject_attempts',
                            'rto_student_assigned_assessment_tasks.created_at',
                            'rto_student_assigned_assessment_tasks.id',
                            'rto_student_assigned_assessment_tasks.extended_due_date',
                            'rto_student_assigned_assessment_tasks.assessment_type',
                            'rto_student_assigned_assessment_tasks.result_comment',
                            'rto_student_subject_enrolment.marks',
                            'rto_student_assigned_assessment_tasks.marks as assign_marks',
                            DB::raw("$totalMarks as total_marks"),
                            DB::raw("$passMarks as pass_marks"),
                            DB::raw("$weightMarks as weight_marks")
                        ])
                        ->toarray();

        return $resArr;
    }
    public function getStudentList($collegeId, $subjectId, $semesterId, $term, $batch, $assessmentTaskId, $arrStudentIds = [], $isHigherEd=false) {

        $totalMarks = $passMarks = $weightMarks = 0;
        if($isHigherEd){
            $whereArr = [
                'college_id'        => $collegeId,
                'subject_id'        => $subjectId,
                'semester_id'       => $semesterId,
                'term'              => $term,
                'batch'             => $batch,
                'assessment_task_id'=> $assessmentTaskId
            ];
            $marksRes = AssignedAssessmentTask::Where($whereArr)->get()->first();
            $totalMarks = (isset($marksRes->total_marks) && !empty($marksRes->total_marks)) ? $marksRes->total_marks : 0;
            $passMarks = (isset($marksRes->pass_marks) && !empty($marksRes->pass_marks)) ? $marksRes->pass_marks : 0;
            $weightMarks = (isset($marksRes->assessment_weight) && !empty($marksRes->assessment_weight)) ? $marksRes->assessment_weight : 0;
        }

        $qry = StudentSubjectEnrolment:: leftJoin('rto_student_assigned_assessment_tasks', function($join) use ($assessmentTaskId) {
                    $join->on('rto_student_assigned_assessment_tasks.student_id', '=', 'rto_student_subject_enrolment.student_id');
                    $join->on('rto_student_assigned_assessment_tasks.assessment_task_id', '=', DB::raw($assessmentTaskId));
                })
                ->leftjoin('rto_students', 'rto_students.id', '=', 'rto_student_subject_enrolment.student_id')
                ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_subject_enrolment.course_id')
                ->leftJoin('rto_student_courses', function($join) {
                    $join->on('rto_student_courses.course_id', '=', 'rto_student_subject_enrolment.course_id');
                    $join->on('rto_student_courses.student_id', '=', 'rto_student_subject_enrolment.student_id');
                })
                ->where('rto_student_subject_enrolment.college_id', '=', $collegeId)
                ->where('rto_student_subject_enrolment.subject_id', '=', $subjectId)
                ->where('rto_student_subject_enrolment.semester_id', '=', $semesterId)
                ->where('rto_student_subject_enrolment.term', '=', $term)
                ->where('rto_student_subject_enrolment.batch', '=', $batch);
        if (!empty($arrStudentIds)) {
            $qry = $qry->whereIn('rto_students.id', $arrStudentIds);
        }
        $resArr = $qry->groupby(['rto_student_subject_enrolment.student_id'])
                        ->orderBy('rto_students.first_name', 'ASC')
                        ->orderBy('rto_students.family_name', 'ASC')
                        ->get(['rto_student_subject_enrolment.student_id',
                            'rto_students.generated_stud_id',
                            'rto_students.first_name',
                            'rto_students.family_name',
                            'rto_student_subject_enrolment.course_id',
                            'rto_courses.course_code',
                            'rto_courses.course_name',
                            'rto_student_courses.status',
                            'rto_student_assigned_assessment_tasks.is_locked',
                            'rto_student_assigned_assessment_tasks.is_approved',
                            'rto_student_assigned_assessment_tasks.competency',
                            'rto_student_assigned_assessment_tasks.comments',
                            'rto_student_assigned_assessment_tasks.id',
                            'rto_student_assigned_assessment_tasks.subject_attempts',
                            'rto_student_assigned_assessment_tasks.created_at',
                            'rto_student_assigned_assessment_tasks.id',
                            'rto_student_assigned_assessment_tasks.extended_due_date',
                            'rto_student_assigned_assessment_tasks.assessment_type',
                            'rto_student_assigned_assessment_tasks.result_comment',
                            'rto_student_subject_enrolment.marks',
                            'rto_student_assigned_assessment_tasks.marks as assign_marks',
                            DB::raw("$totalMarks as total_marks"),
                            DB::raw("$passMarks as pass_marks"),
                            DB::raw("$weightMarks as weight_marks")
                        ])
                        ->toarray();

        return $resArr;
    }

    public function getStudentListTransferV2($collegeId, $subjectId, $semesterId, $term, $batch, $arrStudentIds = []) {

    $resArr = StudentSubjectEnrolment::leftjoin('rto_subject_unit', 'rto_subject_unit.id', '=', 'rto_student_subject_enrolment.unit_id')
        ->leftjoin('rto_students', 'rto_students.id', '=', 'rto_student_subject_enrolment.student_id')
        ->leftJoin('rto_student_courses', function($join) {
            $join->on('rto_student_courses.course_id', '=', 'rto_student_subject_enrolment.course_id');
            $join->on('rto_student_courses.student_id', '=', 'rto_student_subject_enrolment.student_id');
        })
        ->where('rto_student_subject_enrolment.college_id', '=', $collegeId)
        ->where('rto_student_subject_enrolment.subject_id', '=', $subjectId)
        ->where('rto_student_subject_enrolment.semester_id', '=', $semesterId)
        ->where('rto_student_subject_enrolment.term', '=', $term)
        ->where('rto_student_subject_enrolment.batch', '=', $batch)
        ->groupBy(['rto_student_subject_enrolment.student_id'])
        ->get([
            'rto_student_subject_enrolment.final_outcome',
            'rto_student_subject_enrolment.last_assessment_approved_date',
            'rto_student_subject_enrolment.is_result_lock',
            'rto_student_subject_enrolment.student_id',
            'rto_student_subject_enrolment.grade',
            'rto_student_subject_enrolment.course_id',
            'rto_student_courses.status',
            DB::raw("GROUP_CONCAT(DISTINCT CONCAT(rto_subject_unit.unit_code, ' - ', rto_student_subject_enrolment.final_outcome) SEPARATOR ', ') AS unit_code_final_outcome"),
            'rto_student_courses.finish_date',
            'rto_subject_unit.unit_code',
            'rto_subject_unit.unit_name',
            'rto_students.generated_stud_id',
            'rto_students.first_name',
            'rto_students.family_name',
            ])->toarray();

        // $qry = StudentSubjectEnrolment::leftjoin('rto_student_assigned_assessment_tasks', 'rto_student_assigned_assessment_tasks.student_id', '=', 'rto_student_subject_enrolment.student_id')
        //         ->leftjoin('rto_students', 'rto_students.id', '=', 'rto_student_subject_enrolment.student_id')
        //         ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_subject_enrolment.course_id')
        //         ->leftjoin('rto_student_courses', 'rto_student_courses.student_id', '=', 'rto_student_subject_enrolment.student_id')
        //         ->where('rto_student_subject_enrolment.college_id', '=', $collegeId)
        //         ->where('rto_student_subject_enrolment.subject_id', '=', $subjectId)
        //         ->where('rto_student_subject_enrolment.semester_id', '=', $semesterId)
        //         ->where('rto_student_subject_enrolment.term', '=', $term)
        //         ->where('rto_student_subject_enrolment.batch', '=', $batch)
        //         ->where('rto_student_assigned_assessment_tasks.is_locked', '=', '1')
        //         ->where('rto_student_assigned_assessment_tasks.is_approved', '=', '1');
        // if (!empty($arrStudentIds)) {
        //     $qry = $qry->whereIn('rto_students.id', $arrStudentIds);
        // }
        // $resArr = $qry->groupby(['rto_student_subject_enrolment.student_id'])
        //                 ->orderBy('rto_students.first_name', 'ASC')
        //                 ->orderBy('rto_students.family_name', 'ASC')
        //                 ->get(['rto_student_subject_enrolment.student_id',
        //                     'rto_students.generated_stud_id',
        //                     'rto_students.first_name',
        //                     'rto_students.family_name',
        //                     'rto_student_subject_enrolment.course_id',
        //                     'rto_student_subject_enrolment.activity_finish_date',
        //                     'rto_student_subject_enrolment.last_assessment_approved_date',
        //                     'rto_student_subject_enrolment.is_result_lock',
        //                     'rto_courses.course_code',
        //                     'rto_courses.course_name',
        //                     'rto_student_courses.status',
        //                     'rto_student_courses.finish_date',
        //                     'rto_student_subject_enrolment.final_outcome',
        //                     'rto_student_subject_enrolment.grade',
        //                     'rto_student_assigned_assessment_tasks.is_locked',
        //                     'rto_student_assigned_assessment_tasks.is_approved',
        //                     'rto_student_assigned_assessment_tasks.competency',
        //                     'rto_student_assigned_assessment_tasks.comments',
        //                     'rto_student_assigned_assessment_tasks.id',
        //                     'rto_student_assigned_assessment_tasks.subject_attempts',
        //                     'rto_student_assigned_assessment_tasks.created_at',
        //                     'rto_student_assigned_assessment_tasks.id',
        //                     'rto_student_assigned_assessment_tasks.extended_due_date',
        //                     'rto_student_assigned_assessment_tasks.assessment_type',
        //                     'rto_student_assigned_assessment_tasks.result_comment'
        //                 ])
        //                 ->toarray();
            return $resArr;
    }
    public function getStudentListTransfer($collegeId, $subjectId, $semesterId, $term, $batch, $arrStudentIds = []) {
        $qry = StudentSubjectEnrolment::leftjoin('rto_student_assigned_assessment_tasks', 'rto_student_assigned_assessment_tasks.student_id', '=', 'rto_student_subject_enrolment.student_id')
                ->leftjoin('rto_students', 'rto_students.id', '=', 'rto_student_subject_enrolment.student_id')
                ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_subject_enrolment.course_id')
                ->leftjoin('rto_student_courses', 'rto_student_courses.student_id', '=', 'rto_student_subject_enrolment.student_id')
                ->where('rto_student_subject_enrolment.college_id', '=', $collegeId)
                ->where('rto_student_subject_enrolment.subject_id', '=', $subjectId)
                ->where('rto_student_subject_enrolment.semester_id', '=', $semesterId)
                ->where('rto_student_subject_enrolment.term', '=', $term)
                ->where('rto_student_subject_enrolment.batch', '=', $batch)
                ->where('rto_student_assigned_assessment_tasks.is_locked', '=', '1')
                ->where('rto_student_assigned_assessment_tasks.is_approved', '=', '1');
        if (!empty($arrStudentIds)) {
            $qry = $qry->whereIn('rto_students.id', $arrStudentIds);
        }
        $resArr = $qry->groupby(['rto_student_subject_enrolment.student_id'])
                        ->orderBy('rto_students.first_name', 'ASC')
                        ->orderBy('rto_students.family_name', 'ASC')
                        ->get(['rto_student_subject_enrolment.student_id',
                            'rto_students.generated_stud_id',
                            'rto_students.first_name',
                            'rto_students.family_name',
                            'rto_student_subject_enrolment.course_id',
                            'rto_student_subject_enrolment.activity_finish_date',
                            'rto_student_subject_enrolment.last_assessment_approved_date',
                            'rto_student_subject_enrolment.is_result_lock',
                            'rto_courses.course_code',
                            'rto_courses.course_name',
                            'rto_student_courses.status',
                            'rto_student_courses.finish_date',
                            'rto_student_subject_enrolment.final_outcome',
                            'rto_student_subject_enrolment.grade',
                            'rto_student_assigned_assessment_tasks.is_locked',
                            'rto_student_assigned_assessment_tasks.is_approved',
                            'rto_student_assigned_assessment_tasks.competency',
                            'rto_student_assigned_assessment_tasks.comments',
                            'rto_student_assigned_assessment_tasks.id',
                            'rto_student_assigned_assessment_tasks.subject_attempts',
                            'rto_student_assigned_assessment_tasks.created_at',
                            'rto_student_assigned_assessment_tasks.id',
                            'rto_student_assigned_assessment_tasks.extended_due_date',
                            'rto_student_assigned_assessment_tasks.assessment_type',
                            'rto_student_assigned_assessment_tasks.result_comment'
                        ])
                        ->toarray();
            return $resArr;
    }


    // Get Student Data for Bulk Enroll By group
    public function getSemsterEnrollStudent($semesterId, $term, $subjectId, $batch, $collegeId) {

        $result = StudentSubjectEnrolment::leftjoin('rto_students', 'rto_students.id', '=', 'rto_student_subject_enrolment.student_id')
                ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_subject_enrolment.course_id')
                ->leftjoin('rto_student_courses', 'rto_student_courses.student_id', '=', 'rto_student_subject_enrolment.student_id')
                ->leftjoin('rto_semester', 'rto_semester.id', '=', 'rto_student_subject_enrolment.semester_id')
                ->leftjoin('rto_subject', 'rto_subject.id', '=', 'rto_student_subject_enrolment.subject_id')
                ->leftjoin('rto_subject_unit', 'rto_subject_unit.id', '=', 'rto_student_subject_enrolment.unit_id')
                ->leftjoin('rto_study_reasons', 'rto_study_reasons.avaitmiss_id', '=', 'rto_student_subject_enrolment.study_reason')
                ->where('rto_student_subject_enrolment.college_id', '=', $collegeId)
                ->where('rto_student_subject_enrolment.subject_id', '=', $subjectId)
                ->where('rto_student_subject_enrolment.semester_id', '=', $semesterId)
                ->where('rto_student_subject_enrolment.term', '=', $term)
                ->where('rto_student_subject_enrolment.batch', '=', $batch)
                ->groupby(['rto_student_subject_enrolment.student_id', 'rto_student_subject_enrolment.course_id', 'rto_student_subject_enrolment.student_id', 'rto_student_subject_enrolment.unit_id'])
                ->get([
                    'rto_students.generated_stud_id',
                    DB::raw('concat(rto_students.first_name, " ", rto_students.family_name) as student_name'),
                    'rto_students.first_name',
                    'rto_student_subject_enrolment.student_id',
                    'rto_semester.semester_name',
                    'rto_subject_unit.unit_name',
                    'rto_subject_unit.unit_code',
                    'rto_student_subject_enrolment.term',
                    'rto_courses.course_code',
                    'rto_subject.course_type',
                    'rto_subject.subject_code',
                    'rto_subject.subject_name',
                    'rto_student_subject_enrolment.batch',
                    'rto_student_subject_enrolment.activity_start_date',
                    'rto_student_subject_enrolment.activity_finish_date',
                    'rto_student_subject_enrolment.delivery_mode',
                    'rto_student_subject_enrolment.predominant_delivery_mode',
                    'rto_study_reasons.title',
                    'rto_student_subject_enrolment.study_reason',
                    'rto_student_subject_enrolment.funding_source_state',
                    'rto_student_subject_enrolment.final_outcome',
                    'rto_student_subject_enrolment.mark_outcome',
                    'rto_student_subject_enrolment.id as primaryId',
                ])
                ->toarray();

        foreach ($result as $r=>$row){
            $result[$r]['outcome_value'] = $row['final_outcome'];
            if($row['final_outcome'] != 'Enrolled'){
                $isHigherEd = (new CourseType)->checkHigherEdGradingType($row['course_type']);
                if($isHigherEd){
                    $gradeData = (new ResultGrade)->getResultGradelistCollegeIdWise($row['mark_outcome']);
                    $result[$r]['outcome_value'] = ">=". $gradeData[0]['marks'] . " (". $gradeData[0]['grade'] .")";
                }
            }
        }
        /* echo "<pre>";
          print_r($result);exit; */
        return $result;
    }

    public function getSemsterEnrollStudentWithoutBatchTerm($semesterId, $term, $subjectId, $batch, $collegeId) {
        $result = StudentSubjectEnrolment::leftjoin('rto_students', 'rto_students.id', '=', 'rto_student_subject_enrolment.student_id')
                ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_subject_enrolment.course_id')
                ->leftjoin('rto_student_courses', 'rto_student_courses.student_id', '=', 'rto_student_subject_enrolment.student_id')
                ->leftjoin('rto_semester', 'rto_semester.id', '=', 'rto_student_subject_enrolment.semester_id')
                ->leftjoin('rto_subject_unit', 'rto_subject_unit.id', '=', 'rto_student_subject_enrolment.unit_id')
                ->leftjoin('rto_subject', 'rto_subject.id', '=', 'rto_student_subject_enrolment.subject_id')
                ->leftjoin('rto_study_reasons', 'rto_study_reasons.avaitmiss_id', '=', 'rto_student_subject_enrolment.study_reason')
                ->where('rto_student_subject_enrolment.college_id', '=', $collegeId)
                ->where('rto_student_subject_enrolment.subject_id', '=', $subjectId)
                ->where('rto_student_subject_enrolment.semester_id', '=', $semesterId)
                //     ->where('rto_student_subject_enrolment.term', '=', $term)
                ->where(function($query) use ($batch) {
                    $query->where('rto_student_subject_enrolment.batch', '!=', $batch);
                    $query->orWhere('rto_student_subject_enrolment.batch', '=', NULL);
                })
                ->groupby(['rto_student_subject_enrolment.student_id', 'rto_student_subject_enrolment.course_id', 'rto_student_subject_enrolment.student_id', 'rto_student_subject_enrolment.unit_id'])
                ->get([
                    'rto_students.generated_stud_id',
                    DB::raw('concat(rto_students.first_name, " ", rto_students.family_name) as student_name'),
                    'rto_students.first_name',
                    'rto_student_subject_enrolment.student_id',
                    'rto_semester.semester_name',
                    'rto_student_subject_enrolment.term',
                    'rto_courses.course_code',
                    'rto_subject_unit.unit_name',
                    'rto_subject_unit.unit_code',
                    'rto_subject.subject_code',
                    'rto_subject.subject_name',
                    'rto_student_subject_enrolment.batch',
                    'rto_student_subject_enrolment.activity_start_date',
                    'rto_student_subject_enrolment.activity_finish_date',
                    'rto_student_subject_enrolment.delivery_mode',
                    'rto_student_subject_enrolment.predominant_delivery_mode',
                    'rto_study_reasons.title',
                    'rto_student_subject_enrolment.study_reason',
                    'rto_student_subject_enrolment.funding_source_state',
                    'rto_student_subject_enrolment.final_outcome',
                    'rto_student_subject_enrolment.mark_outcome',
                    'rto_student_subject_enrolment.id as primaryId',
                ])
                ->toarray();

        return $result;
    }

    public function getSemsterEnrollStudentAttendance($semesterId, $term, $subjectId, $batch, $collegeId, $orderByField = '', $orderByType = '', $studentStatus = '') {

        $sql = StudentSubjectEnrolment::from('rto_student_subject_enrolment as studentSubject')
                ->leftjoin('rto_students', 'rto_students.id', '=', 'studentSubject.student_id')
                ->leftjoin('rto_courses', 'rto_courses.id', '=', 'studentSubject.course_id')
                ->leftJoin('rto_student_courses as studentCourse', function($join) {
                    $join->on('studentCourse.course_id', '=', 'studentSubject.course_id');
                    $join->on('studentCourse.student_id', '=', 'studentSubject.student_id');
                })
                ->leftjoin('rto_semester', 'rto_semester.id', '=', 'studentSubject.semester_id')
                ->leftjoin('rto_subject', 'rto_subject.id', '=', 'studentSubject.subject_id')
                ->where('studentSubject.college_id', '=', $collegeId)
                ->where('studentSubject.subject_id', '=', $subjectId)
                ->where('studentSubject.semester_id', '=', $semesterId)
                ->where('studentSubject.term', '=', $term)
                ->where('studentSubject.batch', '=', $batch);

        if($studentStatus != ''){
            $sql->where('studentCourse.status', '=',$studentStatus);
        }
        $sql->groupby(['studentSubject.student_id']);

        if (!empty($orderByField)) {
            $sql->orderBy('rto_students.' . $orderByField, $orderByType);
        }

        $result = $sql->select(
                        'rto_students.generated_stud_id', 'rto_students.first_name', 'rto_students.family_name', 'studentSubject.student_id', 'rto_semester.semester_name', 'studentSubject.term', 'studentCourse.start_date as courseStartDate', 'studentCourse.finish_date as courseFinishDate',
                        'studentCourse.course_id as coursecourse_id', DB::raw('concat(rto_students.name_title," ",rto_students.first_name," ",rto_students.family_name) as full_name'),'studentCourse.status as studentCourseStatus', 'rto_courses.course_code', 'rto_subject.subject_code', 'rto_subject.subject_name', 'studentSubject.batch', DB::raw("DATE_FORMAT(studentSubject.activity_start_date, '%d/%m/%Y') AS activity_start_date"), DB::raw("DATE_FORMAT(studentSubject.activity_finish_date, '%d/%m/%Y') AS activity_finish_date"), 'studentSubject.study_reason', 'studentSubject.funding_source_state', 'studentSubject.final_outcome')
                ->get()
                ->toarray();

        return $result;
    }

    public function saveStudentSubjectBulkEnrollInfo($collegeId, $data) {

        $userId = Auth::user()->id;
        $studArr = $data['studentId'];

        $returnMessage = array();
        if (!empty($studArr)) {
            $insertData = array();
            $j = 0;
            for ($i = 0; $i < count($studArr); $i++) {
                $explodId = explode('-', $studArr[$i]);
                $studentId = $explodId[0];
                $generatedId = $explodId[1];
                $studentCourseId = $explodId[2];

                $duplicateCount = $this->_checkDuplicateData($studentId, $data);
                $dateRange = $this->_checkDateRange($studentId, $data, $studentCourseId);
                if ($dateRange) {
                    $returnMessage[$i]['status'] = 'error';
                    $returnMessage[$i]['message'] = $generatedId . ' student Enrollment Start date greater then Course Start Date.';
                    $returnMessage[$i]['generatedId'] = $generatedId;
                } else {

                    $objRtoStudentCourse = new StudentCourse();
                    $arrStudentCourse = $objRtoStudentCourse->getStudentCourseStudyReasonIdData($studentId, $data['course']);

                    $isHigherEd = (new CourseType)->checkHigherEdGradingType($data['course_type']);

                    $grade = '';
                    $selectMarkOutcome = '';
                    if($isHigherEd)
                    {
                        $gradeId = $data['select_mark_outcome'];

                        if(isset($gradeId) && !empty($gradeId))
                        {
                            $gradeData = (new ResultGrade)->getResultGradelistCollegeIdWise($gradeId);
                            $grade = $gradeData[0]['grade'];
                            $selectMarkOutcome = $gradeData[0]['id'];
                        }
                    }

                    $studyReason = '';
                    if(!empty($arrStudentCourse[0])){
                        $studyReason = $arrStudentCourse[0]['study_reason_id'];
                    }
                    $insertData[$j]['college_id'] = $collegeId;
                    $insertData[$j]['student_id'] = $studentId;
                    $insertData[$j]['student_course_id'] = $studentCourseId;
                    $insertData[$j]['course_id'] = $data['course'];
                    $insertData[$j]['semester_id'] = $data['semester'];
                    $insertData[$j]['term'] = $data['term'];
                    $insertData[$j]['batch'] = $data['batch'];
                    $insertData[$j]['generatedId'] = $generatedId;
                    $insertData[$j]['enroll_type'] = $data['enroll_type'];
                    $insertData[$j]['study_reason'] = ($studyReason == '') ? "@@" : ((strlen($studyReason) == 1)  ? '0'.$studyReason : $studyReason);
                    $insertData[$j]['select_final_outcome'] = (isset($data['select_final_outcome']) && !$isHigherEd) ? $data['select_final_outcome'] : NULL;
                    $insertData[$j]['select_mark_outcome'] = $selectMarkOutcome;
                    $insertData[$j]['grade'] = $grade;
                    $insertData[$j]['course_stage'] = '1';
                    $insertData[$j]['subject_id'] = $data['subject'];
                    $insertData[$j]['activity_start_date'] = date('Y-m-d', strtotime($data['start_date']));
                    $insertData[$j]['activity_finish_date'] = date('Y-m-d', strtotime($data['finish_date']));
                    $insertData[$j]['delivery_mode'] = $data['delivery_mode'];
                    $insertData[$j]['predominant_delivery_mode'] = $data['predominant_delivery_mode'];
                    $insertData[$j]['vanue_location'] = $data['venue'];
                    $insertData[$j]['funding_source_state'] = ($data['filterType'] == 'Offshore_Onshore') ? 'FEE' : $data['state'];
                    $insertData[$j]['funding_source_nat'] = $data['funding_source_nat'];
                    $insertData[$j]['created_by'] = $userId;
                    $insertData[$j]['updated_by'] = $userId;
                    $j++;
                }
            }

            if (!empty($insertData)) {
                for ($i = 0; $i < count($insertData); $i++) {

                    $objRtoTimetable = new Timetable();
                    $arrBatch = $objRtoTimetable->getBanchCapecityResultBulkEnrollmentByGroup($collegeId, $data['semester'], $data['batch']);
                    $objStudentSubjectEnrolment = new StudentSubjectEnrolment();
                    $checkClassCapecityRecord = $objStudentSubjectEnrolment->checkClassCapecityAssessmentBulkEnrollmentByGroup($collegeId, $data['semester'], $data['batch'], $data['term']);

                    if ($arrBatch[0]['class_capacity'] <= $checkClassCapecityRecord) {
                        $returnMessage[$i]['status'] = 'error';
                        $returnMessage[$i]['message'] = 'The class do not have enough capacity. ' . $insertData[$i]['generatedId'] . ' is not Enroll for this subject.';
                        $returnMessage[$i]['generatedId'] = $insertData[$i]['generatedId'];
                        return $returnMessage;
                    }

                    $objRtoUnitModule = new UnitModule();
                    $unitId = $objRtoUnitModule->getUnitId($collegeId, $insertData[$i]['subject_id']);

                    if(empty($unitId)){
                        $returnMessage[$i]['status'] = 'error';
                        $returnMessage[$i]['message'] = 'Selected subject does not have any unit. Add unit first';
                        $returnMessage[$i]['generatedId'] = $insertData[$i]['generatedId'];
                        return $returnMessage;
                    }

                    $attendanceEventFlag = false;
                    for ($j = 0; $j < count($unitId); $j++) {

                        $duplicateCount = $this->_checkDuplicateDataWithUnit($insertData[$i]['student_id'], $data, $unitId[$j]['id'],$isHigherEd);

                        if ($duplicateCount > 0) {
                            $returnMessage[$i]['status'] = 'error';
                            $returnMessage[$i]['message'] = $insertData[$i]['generatedId'] . ' is already Enroll for this subject.';
                            $returnMessage[$i]['generatedId'] = $insertData[$i]['generatedId'];
                            return $returnMessage;
                            //return 'duplicate_row';
                        }

                        $attempt = StudentSubjectEnrolment::where([
                            'student_id' => $insertData[$i]['student_id'],
                            'course_id' => $insertData[$i]['course_id'],
                            'subject_id' => $insertData[$i]['subject_id'],
                            'unit_id' => $unitId[$j]['id']
                        ])->count();

                        if($attempt > 0){
                            $checkDate = $this->_checkNextAttemptStartDateAndEndDate($insertData[$i]['student_id'], $data, $unitId[$j]['id'], $attempt, $insertData[$i]['activity_start_date']);

                            if ($checkDate) {
                                $returnMessage[$i]['status'] = 'error';
                                $returnMessage[$i]['message'] = $insertData[$i]['generatedId'] . ' Attempt '.++$attempt.' activity start date must be after previous attempt activity end date.';
                                $returnMessage[$i]['generatedId'] = $insertData[$i]['generatedId'];
                                return $returnMessage;
                            }
                        }
                        $objStudEnroll = new StudentSubjectEnrolment();
                        $objStudEnroll->college_id = $insertData[$i]['college_id'];
                        $objStudEnroll->student_id = $insertData[$i]['student_id'];
                        $objStudEnroll->course_id = $insertData[$i]['course_id'];
                        $objStudEnroll->student_course_id = $insertData[$i]['student_course_id'];
                        $objStudEnroll->semester_id = $insertData[$i]['semester_id'];
                        $objStudEnroll->term = $insertData[$i]['term'];
                        $objStudEnroll->batch = $insertData[$i]['batch'];
                        $objStudEnroll->enroll_type = $insertData[$i]['enroll_type'];
                        $objStudEnroll->unit_id = $unitId[$j]['id'];
                        $objStudEnroll->subject_attempt = ++$attempt;
                        $objStudEnroll->course_stage = $insertData[$i]['course_stage'];
                        $objStudEnroll->subject_id = $insertData[$i]['subject_id'];
                        $objStudEnroll->activity_start_date = $insertData[$i]['activity_start_date'];
                        $objStudEnroll->activity_finish_date = $insertData[$i]['activity_finish_date'];
                        $objStudEnroll->delivery_mode = $insertData[$i]['delivery_mode'];
                        $objStudEnroll->predominant_delivery_mode = $insertData[$i]['predominant_delivery_mode'];
                        $objStudEnroll->vanue_location = $insertData[$i]['vanue_location'];
                        $objStudEnroll->funding_source_state = $insertData[$i]['funding_source_state'];
                        $objStudEnroll->funding_source_nat = $insertData[$i]['funding_source_nat'];
                        $objStudEnroll->created_by = $insertData[$i]['created_by'];
                        $objStudEnroll->updated_by = $insertData[$i]['updated_by'];
                        $objStudEnroll->study_reason = $insertData[$i]['study_reason'];
                        $objStudEnroll->final_outcome = $insertData[$i]['select_final_outcome'];
                        $objStudEnroll->mark_outcome = $insertData[$i]['select_mark_outcome'];
                        $objStudEnroll->grade = $insertData[$i]['grade'];

                        $objStudEnroll->save();
                        $lastInsertedId = $objStudEnroll->id;

                        if($lastInsertedId){
                            $attendanceEventFlag = true;
                        }
                        $objStudentUnitEnrollment = new StudentUnitEnrollment();
                        $arrStudentUnitEnrollment = $objStudentUnitEnrollment->studentSubjectBulkUnitEnrollment($collegeId, $insertData[$i], $unitId[$j]['id'], $lastInsertedId);
                        $returnMessage[$i]['status'] = 'success';
                        $returnMessage[$i]['message'] = $insertData[$i]['generatedId'] . ' is Enroll SuccessFully.';
                    }

                    //TODO:: GNG-1711
                    if($attendanceEventFlag){
                        $studentId = $insertData[$i]['student_id'];
                        $batchArr = [$insertData[$i]['batch']];
                        event(new \App\Events\EnrollStudentAttendanceEvent($studentId, $batchArr));
                        //$this->manageDefaultAttendance($insertData[$i]['student_id'], [$insertData[$i]['batch']]);
                    }
                }
            }
        } else {
            $returnMessage[0]['status'] = 'alert-danger';
            $returnMessage[0]['message'] = 'No Student is selected.';
        }

        return $returnMessage;
    }

    public function _checkDuplicateData($studentId, $data) {
        return StudentSubjectEnrolment::where('student_id', $studentId)
                        ->where('course_id', $data['course'])
                        ->where('semester_id', $data['semester'])
                        ->where('subject_id', $data['subject'])
                        ->get()->count();
    }

    public function _checkDuplicateDataWithUnit($studentId, $data, $unitId, $isHigherEd) {

        $validRecord = StudentSubjectEnrolment::where('student_id', $studentId)
                        ->where('course_id', $data['course'])
                        ->where('subject_id', $data['subject'])
                        ->where('unit_id', $unitId);
        if($isHigherEd){
            $collegeId = Auth::user()->college_id;
            $arrSelectMarksOutcome = ResultGrade::getFailedGradeResult($collegeId);
            $filedResultArray = $arrSelectMarksOutcome->pluck('id')->toArray();
            $validRecord->whereNotIn('mark_outcome', $filedResultArray);
        }else{
            $validRecord->where('final_outcome', '!=','NYC');
        }

        return $validRecord->get()->count();
    }
    public function _checkNextAttemptStartDateAndEndDate($studentId, $data, $unitId, $attempt, $startDate) {
        $checkFinishDate = StudentSubjectEnrolment::where('student_id', $studentId)
                        ->where('course_id', $data['course'])
                        ->where('subject_id', $data['subject'])
                        ->where('unit_id', $unitId)
                        ->where('subject_attempt', $attempt)
                        ->get('activity_finish_date');
        if(strtotime($checkFinishDate[0]->activity_finish_date) > strtotime($startDate)){
            return true;
        }
        return false;
    }

    public function getSubjectEnrollmentData($enrollId) {
        return StudentSubjectEnrolment::where('id', $enrollId)
                        ->get(['subject_id'])->toarray();
    }

    public function _checkDateRange($studentId, $data , $studentCourseId = '') {
        $objDueDate = new AssessmentDueDate();
        $getDueDate = AssessmentDueDate::select('default_due_day')->get();
        $Due_Date = 0;
        foreach ($getDueDate as $due_date) {
            $Due_Date = $due_date['default_due_day'];
        }
        $studentCourseObj = StudentCourse::where('student_id', $studentId)
                        ->where('course_id', $data['course']);
                        if($studentCourseId != ''){
                            $studentCourseObj->where('id', $studentCourseId);
                        }
        $studentCourse =  $studentCourseObj->get(['start_date', 'finish_date'])->toarray();

        $input_start = $studentCourse[0]['start_date'];
        $input_end = $studentCourse[0]['finish_date'];
        $start = date('Y-m-d', strtotime($input_start . '- ' . $Due_Date . ' days'));
        $end = date('Y-m-d', strtotime($input_end . '+ ' . $Due_Date . ' days'));
        $dateConvertReplace_start = str_replace('/', '-', $data['start_date']);
        $dateConvertReplace_end = str_replace('/', '-', $data['finish_date']);
        $dateConvert_start = date('Y-m-d', strtotime($dateConvertReplace_start));
        $dateConvert_end = date('Y-m-d', strtotime($dateConvertReplace_end));

        if (strtotime($dateConvert_start) >= strtotime($start) && strtotime($dateConvert_end) <= strtotime($end)) {
            return false;
        } else {
            return true;
        }
    }

    public function getStudentEnrollDataByBatch($collegeId, $batchName) {
        return StudentSubjectEnrolment::where('college_id', '=', $collegeId)->where('batch', '=', $batchName)->get()->toArray();
    }

    public function getStudentEnrollDataById($collegeId, $enrollId) {
        return StudentSubjectEnrolment::where('college_id', '=', $collegeId)->where('id', '=', $enrollId)->get()->toArray();
    }

    public function saveStudentActivityInfo($collegeId, $selectedField, $formData, $resultSubject) {
        for ($i = 0; $i < count($selectedField); $i++) {
            if (!empty($selectedField[$i])) {
                $objSubjectEnrolment = StudentSubjectEnrolment::where('subject_id', '=', $resultSubject)
                        ->update([
                    $selectedField[$i] => $formData[$selectedField[$i]],
                ]);
            }
        }

        return array('status' => 'true', 'action' => 'alert-success', 'msg' => 'Student Activity AVETMISS Update Successfully.');
    }

    public function getStudentCourseListForPdf($studentId, $courseId,$postData) {

        // if(isset($postData['transcriptType']) && isset($postData['isIncludeFail']) && ($postData['transcriptType'] == "SOA" || $postData['transcriptType'] == "TOCA") && ($postData['isIncludeFail'] == "Y")){
        //     $resultArr = ['C','RPL','RCC','CT','W','CE','NYC'];
        // }else{
        //     $resultArr = ['C','RPL','RCC','CT','W'];
        // }
            
        if (isset($postData['transcriptType']) && isset($postData['isIncludeFail']) && (($postData['transcriptType'] == "TOCA") && ($postData['isIncludeFail'] == 'Y'))) {
            $resultArr = ['C', 'RPL', 'RCC', 'CT', 'WD', 'CE', 'NYC'];
        } elseif ($postData['transcriptType'] == "SOA") {
            $resultArr = ['C', 'RPL', 'CT'];
        } elseif ($postData['transcriptType'] == "TOCA") {
            $resultArr = ['C', 'RPL', 'RCC', 'CT', 'WD'];
        } else {
            $resultArr = ['C', 'RPL', 'RCC', 'CT', 'WD'];
        }

        // return StudentSubjectEnrolment::
        //                 select('rto_student_subject_enrolment.id','rsu.unit_code', 'rsu.unit_name', 'rsu.description', 'rto_student_subject_enrolment.activity_finish_date as year','rsue.competency_date as unitcomyear', DB::raw('IFNULL(rsue.compentency,rto_student_subject_enrolment.final_outcome) as final_outcome'))
        //                 ->leftjoin('rto_subject_unit as rsu', 'rsu.id', '=', 'rto_student_subject_enrolment.unit_id')
        //                 ->leftjoin('rto_student_unit_enrollment as rsue', 'rsue.student_subject_enrollment_id', '=', 'rto_student_subject_enrolment.id', 'AND','rsue.unit_id', '=', 'rsu.id')
        //                 ->where('rto_student_subject_enrolment.college_id', '=', Auth::user()->college_id)
        //                 ->where('rto_student_subject_enrolment.course_id', '=', $courseId)
        //                 ->where('rto_student_subject_enrolment.student_id', '=', $studentId)
        //                 ->whereIn('rsue.compentency', $resultArr)
        //                 ->groupby('rto_student_subject_enrolment.id')
        //                 ->get()->toArray();
        return StudentSubjectEnrolment::select('rto_student_subject_enrolment.id', 'sem.semester_name', 'rsu.unit_code', 'rsu.unit_name', 'rsu.description', 'rto_student_subject_enrolment.activity_finish_date as year', 'rsue.competency_date as unitcomyear', 'rto_student_subject_enrolment.final_outcome as final_outcome', 'rto_student_subject_enrolment.grade', 'rto_student_subject_enrolment.marks', 'rto_student_subject_enrolment.mark_outcome', 'rs.credit_point')
            ->leftjoin('rto_subject_unit as rsu', 'rsu.id', '=', 'rto_student_subject_enrolment.unit_id')
            ->leftjoin('rto_subject as rs', 'rs.id', '=', 'rto_student_subject_enrolment.subject_id')
            ->leftjoin('rto_student_unit_enrollment as rsue', 'rsue.student_subject_enrollment_id', '=', 'rto_student_subject_enrolment.id', 'AND', 'rsue.unit_id', '=', 'rsu.id')
            ->leftjoin('rto_semester as sem', 'sem.id', '=', 'rto_student_subject_enrolment.semester_id')
            ->where('rto_student_subject_enrolment.college_id', '=', Auth::user()->college_id)
            ->where('rto_student_subject_enrolment.course_id', '=', $courseId)
            ->where('rto_student_subject_enrolment.student_id', '=', $studentId)
            ->whereIn('rto_student_subject_enrolment.final_outcome', $resultArr)
            ->groupby('rto_student_subject_enrolment.id')->get()->toArray();
    }

    // Get Student Data for Bulk Enroll By subject
    public function getStudentInEnrollBySubject($semesterId, $term, $subjectId, $batch, $collegeId, $course_id = '') {

        $sql = StudentSubjectEnrolment::from('rto_student_subject_enrolment as rsse')
                ->leftjoin('rto_students as rs', 'rs.id', '=', 'rsse.student_id')
                ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsse.course_id')
                ->leftjoin('rto_student_courses as rsc', 'rsc.student_id', '=', 'rsse.student_id')
                ->leftjoin('rto_semester as sem', 'sem.id', '=', 'rsse.semester_id')
                ->leftjoin('rto_subject as sub', 'sub.id', '=', 'rsse.subject_id')
                ->where('rsse.college_id', '=', $collegeId)
                ->where('rsse.subject_id', '=', $subjectId)
                ->where('rsse.semester_id', '=', $semesterId);

        if (!empty($course_id)) {
            $sql->where('rsse.course_id', '=', $course_id);
        }

        $resultArr = $sql->where('rsse.term', '=', $term)
                        ->where('rsse.batch', '=', $batch)
                        ->groupby(['rsse.student_id'])
                        ->get([
                            'rs.id as student_id',
                            'rs.generated_stud_id',
                            'rs.first_name',
                            'rs.family_name',
                            'rc.course_code',
                            'rc.course_name',
                            'rc.course_type_id',
                            'rsc.course_attempt',
                            'rsse.final_outcome',
                            'rsse.mark_outcome',
                            'rsse.batch',
                            'rsse.activity_start_date',
                            'rsse.activity_finish_date',
                            'rsse.study_reason',
                            'rsse.funding_identifier',
                            'rsse.funding_source_state',
                            'rsse.grade',
                            'rsse.term',
                            'sem.semester_name',
                            'sub.subject_code',
                            'sub.subject_name',
                        ])->toArray();

        foreach ($resultArr as $r=>$row){
            $resultArr[$r]['competency_value'] = $row['final_outcome'];
            $resultArr[$r]['grade_value'] = $row['final_outcome'];
            if($row['final_outcome'] != 'Enrolled'){
                $isHigherEd = (new CourseType)->checkHigherEdGradingType($row['course_type_id']);
                if($isHigherEd){
                    $gradeData = (new ResultGrade)->getResultGradelistCollegeIdWise($row['mark_outcome']);
                    $resultArr[$r]['competency_value'] = ">=". $gradeData[0]['marks'];
                    $resultArr[$r]['grade_value'] = $gradeData[0]['grade'];
                }
            }

        }
        return $resultArr;
    }

    public function _getStudentSubjectEnrolmentData($dataId,$courseId) {
        return StudentSubjectEnrolment::where('subject_id', '=', $dataId)->where('course_id', '=', $courseId)->where('college_id', '=', Auth::user()->college_id)->get()->toarray();
    }

    public function getStudentListByCourse($collegeId, $courseId, $semesterId, $term, $weekPeriod) {

        $result = StudentSubjectEnrolment::leftjoin('rto_students', 'rto_students.id', '=', 'rto_student_subject_enrolment.student_id')
                ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_subject_enrolment.course_id')
                ->leftjoin('rto_student_courses', 'rto_student_courses.student_id', '=', 'rto_student_subject_enrolment.student_id')
                ->leftjoin('rto_timetable', 'rto_timetable.semester_id', '=', 'rto_student_subject_enrolment.semester_id')
                ->where('rto_student_subject_enrolment.college_id', '=', $collegeId)
                ->where('rto_student_subject_enrolment.course_id', '=', $courseId)
                ->where('rto_student_subject_enrolment.semester_id', '=', $semesterId)
                ->where('rto_student_subject_enrolment.term', '=', $term)
                ->where('rto_timetable.start_week_id', '<=', $weekPeriod)
                ->where('rto_timetable.end_week_id', '>=', $weekPeriod)
                ->orderBy('rto_students.first_name', 'ASC')
                ->orderBy('rto_students.family_name', 'ASC')
                ->groupBy(['rto_students.id', 'rto_timetable.id'])
                ->get([
                    'rto_students.generated_stud_id',
                    'rto_students.first_name',
                    'rto_students.family_name',
                    'rto_student_subject_enrolment.student_id',
                    'rto_courses.course_code',
                    'rto_courses.course_name',
                    'rto_student_subject_enrolment.term',
                    'rto_student_courses.start_date',
                    'rto_student_courses.finish_date',
                    'rto_student_courses.status',
                    'rto_student_courses.course_attempt',
                    'rto_timetable.id',
                    'rto_timetable.day',
                    'rto_timetable.start_time',
                    'rto_timetable.finish_time',
                ])
                ->toarray();

        $data = array();
        foreach ($result as $arrData) {
            $data[$arrData['student_id']]['generated_stud_id'] = $arrData['generated_stud_id'];
            $data[$arrData['student_id']]['student_id'] = $arrData['student_id'];
            $data[$arrData['student_id']]['student_name'] = $arrData['first_name'] . " " . $arrData['family_name'];
            $data[$arrData['student_id']]['course_name'] = $arrData['course_code'] . " : " . $arrData['course_name'];
            $data[$arrData['student_id']]['start_date'] = date('d-m-Y', strtotime($arrData['start_date']));
            $data[$arrData['student_id']]['finish_date'] = date('d-m-Y', strtotime($arrData['finish_date']));
            $data[$arrData['student_id']]['status'] = $arrData['status'];
            $data[$arrData['student_id']]['course_attempt'] = $arrData['course_attempt'];
            $t1 = StrToTime($arrData['finish_time']);
            $t2 = StrToTime($arrData['start_time']);
            $diff = $t1 - $t2;
            $hours = $diff / ( 60 * 60 );
            $totalHours = $hours;
            if (isset($data[$arrData['student_id']]['total_hours'])) {
                $totalHours = $data[$arrData['student_id']]['total_hours'] + $hours;
            }
            $data[$arrData['student_id']]['total_hours'] = $totalHours;
        }
        return $data;
    }

    public function addEnrollStudentInfo($collegeId, $data) {

        if ($data['studentType'] == 'New Student' && isset($data['intakeStudentId'])) {
            $data['studentId'] = $data['intakeStudentId'];
        } else if ($data['studentType'] != '' && isset($data['studentId'])) {
            $data['studentId'] = $data['studentId'];
        }

        if (isset($data['studentId']) && count($data['studentId']) > 0) {

            $arrUnitId = UnitModule::where('college_id', $collegeId)->where('subject_id', $data['subjectV2'])->get(['id', 'unit_name'])->toArray();

            $str = $data['dateDuration'];
            $sub = substr($str, strpos($str, '(') + strlen(')'), strlen($str));
            $duration = substr($sub, 0, strpos($sub, ')'));
            $dateDuration = explode(' - ', $duration);
            $data['startDate'] = ($dateDuration[0]);
            $data['finishDate'] = ($dateDuration[1]);

            //date_default_timezone_set('America/Los_Angeles');

            $msg = '';
            for ($i = 0; $i < count($data['studentId']); $i++) {
                for ($j = 0; $j < count($arrUnitId); $j++) {
                    $duplicateCount = $this->_checkDuplicateEnrollData($data['studentId'][$i], $arrUnitId[$j]['id'], $data);
                    if ($duplicateCount > 0) {
                        $msg .= $data['generatedId'][$i] . ' is already Enroll for this subject & Unit name = ' . $arrUnitId[$j]['unit_name'] . '<br>';
                    } else {
                        $this->_addStudentEnrollInfo($collegeId, $data['studentId'][$i], $arrUnitId[$j]['id'], $data);
                    }
                }
            }

            if ($msg == '') {
                return array('status' => 'true', 'action' => 'alert-success', 'msg' => 'Student Bulk Enroll Successfully.');
            } else {
                return array('status' => 'true', 'action' => 'alert-danger', 'msg' => $msg);
            }
        } else {
            return array('status' => 'null', 'action' => 'alert-danger', 'msg' => 'Please Select at least one Student Record.');
        }

        return array('status' => 'false', 'action' => 'alert-danger', 'msg' => 'Error occured while Bulk Enroll. Please try again.');
    }

    private function _checkDuplicateEnrollData($studentId, $unitId, $data) {
        return StudentSubjectEnrolment::where('student_id', $studentId)
                        ->where('course_id', $data['course'])
                        ->where('semester_id', $data['semesterV2'])
                        ->where('subject_id', $data['subjectV2'])
                        ->where('term', $data['termV2'])
                        ->where('unit_id', $unitId)
                        ->get()->count();
    }

    private function _addStudentEnrollInfo($collegeId, $studentId, $unitId, $data) {

        //echo $data['startDate'] .' To '. $data['finishDate'] ."<br>";
        $startDate = str_replace('/', '-', $data['startDate']);
        $finishDate = str_replace('/', '-', $data['finishDate']);

        $objRtoStudentCourse = new StudentCourse();
        $arrStudentCourse = $objRtoStudentCourse->getStudentCourseStudyReasonIdData($studentId, $data['course']);

        $userId = Auth::user()->id;
        $objSubjectEnrolment = new StudentSubjectEnrolment();

        $objSubjectEnrolment->college_id = $collegeId;
        $objSubjectEnrolment->student_id = $studentId;
        $objSubjectEnrolment->course_id = $data['course'];
        $objSubjectEnrolment->semester_id = $data['semesterV2'];
        $objSubjectEnrolment->term = $data['termV2'];
        $objSubjectEnrolment->subject_id = $data['subjectV2'];
        $objSubjectEnrolment->unit_id = $unitId;
        $objSubjectEnrolment->batch = $data['batchV2'];
        $objSubjectEnrolment->vanue_location = $data['venue'];
        $objSubjectEnrolment->study_reason = empty($arrStudentCourse[0]['study_reason_id']) ? '0' : $arrStudentCourse[0]['study_reason_id'];
        $objSubjectEnrolment->funding_source_nat = $data['funding_source'];
        $objSubjectEnrolment->final_outcome = 'Enrolled';
        $objSubjectEnrolment->activity_start_date = ($data['startDate'] != '') ? date('Y-m-d', strtotime($startDate)) : null;
        $objSubjectEnrolment->activity_finish_date = ($data['finishDate'] != '') ? date('Y-m-d', strtotime($finishDate)) : null;
        $objSubjectEnrolment->created_by = $userId;
        $objSubjectEnrolment->updated_by = $userId;

        $objSubjectEnrolment->save();
    }

    public function deleteStudentEnrollData($enrId) {
        //TODO:: GNG-1711
        $this->deleteStudentUnitEnrollmentData($enrId);
        return $this->deleteStudentSubjectEnrollmentData($enrId);
    }

    public function getFlexibleEnrollBySubject($semesterId, $term, $subjectId, $batch, $collegeId) {

        $arrFundingSourceList = Config::get('constants.arrFundingSourceNat');

        $result = StudentSubjectEnrolment::from('rto_student_subject_enrolment as rsse')
                ->leftjoin('rto_students as student', 'student.id', '=', 'rsse.student_id')
                ->leftjoin('rto_courses as course', 'course.id', '=', 'rsse.course_id')
                ->leftjoin('rto_student_courses as rsc', 'rsc.student_id', '=', 'rsse.student_id')
                ->leftjoin('rto_semester as semester', 'semester.id', '=', 'rsse.semester_id')
                ->leftjoin('rto_subject as subject', 'subject.id', '=', 'rsse.subject_id')
                ->leftjoin('rto_study_reasons as rsr', 'rsr.id', '=', 'rsse.study_reason')
                ->where('rsse.college_id', '=', $collegeId)
                ->where('rsse.subject_id', '=', $subjectId)
                ->where('rsse.semester_id', '=', $semesterId)
                ->where('rsse.term', '=', $term)
                ->where('rsse.batch', '=', $batch)
                ->groupby(['rsse.student_id'])
                ->get(['student.id as student_id',
            'student.generated_stud_id',
            'student.first_name',
            'student.family_name',
            'course.course_code',
            'course.course_name',
            'course.course_type_id',
            'rsc.course_attempt',
            'rsse.final_outcome',
            'subject.subject_code',
            'subject.subject_name',
            'rsse.activity_start_date',
            'rsse.activity_finish_date',
            'rsr.title as study_reason',
            'rsse.funding_source_nat',
            'rsse.final_outcome',
            'rsse.mark_outcome',
            'rsse.marks',
            'rsse.subject_attempt',
            'rsse.id'
        ]);
        //->toArray();

        foreach ($result as $row) {
            $start_date = (!empty($row->activity_start_date)) ? date('d/m/Y', strtotime($row->activity_start_date)) : '';
            $finish_date = (!empty($row->activity_finish_date)) ? date('d/m/Y', strtotime($row->activity_finish_date)) : '';
            $row->study_period = $start_date . ' - ' . $finish_date;
            $row->funding_source = (!empty($row->funding_source_nat)) ? $arrFundingSourceList[$row->funding_source_nat] : '-';

            $row->competency_value = $row->final_outcome;
            $row->grade_value = $row->final_outcome;
            if($row->final_outcome != 'Enrolled'){
                $isHigherEd = (new CourseType)->checkHigherEdGradingType($row->course_type_id);
                if($isHigherEd){
                    $gradeData = (new ResultGrade)->getResultGradelistCollegeIdWise($row->mark_outcome);
                    $row->competency_value = ">=". $gradeData[0]['marks'];
                    $row->grade_value = $gradeData[0]['grade'];
                }
            }
        }
        return $result;
    }

    public function getCourseProgressSummaryBySubject($collegeId, $studentId, $courseId) {
        $result = StudentSubjectEnrolment::from('rto_student_subject_enrolment as rsse')
                ->leftjoin('rto_subject as rs', 'rs.id', '=', 'rsse.subject_id')
                ->where('rsse.college_id', '=', $collegeId)
                ->where('rsse.student_id', '=', $studentId)
                ->where('rsse.course_id', '=', $courseId)
                ->groupBy('rsse.subject_id')
                ->get(['rsse.subject_id',
            'rsse.semester_id',
            'rs.subject_code',
            'rs.subject_name',
            'rsse.activity_start_date as start_date',
            'rsse.activity_finish_date as finish_date',
            'rsse.final_outcome',
            'rsse.batch']);

        foreach ($result as $row) {
            $row->start_date = empty($row->start_date) ? '-' : date('d/m/Y', strtotime($row->start_date));
            $row->finish_date = empty($row->finish_date) ? '-' : date('d/m/Y', strtotime($row->finish_date));
            $row->lecturer_name = $this->getLecturerInfo($collegeId, $row->subject_id, $row->semester_id, $row->batch);
            $row->batch = empty($row->batch) ? '-' : $row->batch;
        }
        return $result;
    }

    public function getLecturerInfo($collegeId, $subjectId, $semesterId, $batch) {
        //echo $collegeId.'====='.$subjectId.'====='.$semesterId.'====='.$batch;exit;
        $result = Timetable::from('rto_timetable as rt')
                ->leftjoin('rto_staff_and_teacher as rst', 'rst.id', '=', 'rt.teacher_id')
                ->where('rt.college_id', '=', $collegeId)
                ->where('rt.subject_id', '=', $subjectId)
                ->where('rt.semester_id', '=', $semesterId)
                ->where('rt.batch', '=', $batch)
                ->get(['rst.name_title', 'rst.first_name', 'rst.last_name']);

        $lecturer_name = '-';
        if ($result->count() > 0) {
            $lecturer_name = $result[0]->name_title . ' ' . $result[0]->first_name . ' ' . $result[0]->last_name;
        }
        return $lecturer_name;
    }

    public function getCourseProgressSummaryByUnit($collegeId, $studentId, $courseId) {
        return StudentSubjectEnrolment::from('rto_student_subject_enrolment as rsse')
                        ->leftjoin('rto_student_unit_enrollment as rsue', 'rsue.student_subject_enrollment_id', '=', 'rsse.id')
                        ->leftjoin('rto_subject_unit as rsu', 'rsu.id', '=', 'rsse.unit_id')
                        ->where('rsse.college_id', '=', $collegeId)
                        ->where('rsse.student_id', '=', $studentId)
                        ->where('rsse.course_id', '=', $courseId)
                        ->get(['rsu.id', 'rsu.unit_code', 'rsu.unit_name', 'rsue.compentency']);
    }

    public function getCourseProgressSummaryByUnitV2($collegeId, $studentId, $courseId) {

        return StudentSubjectEnrolment::from('rto_student_subject_enrolment as rsse')
                        ->leftjoin('rto_students', 'rto_students.id', '=', 'rsse.student_id')
                        ->leftjoin('rto_student_unit_enrollment as rsue', 'rsue.student_subject_enrollment_id', '=', 'rsse.id')
                        ->leftjoin('rto_subject_unit as rsu', 'rsu.id', '=', 'rsse.unit_id')
                        ->leftjoin('rto_subject as rs', 'rs.id', '=', 'rsse.subject_id')
                        ->leftjoin('rto_venue as rv', 'rv.id', '=', 'rsse.vanue_location')
                        ->where('rsse.college_id', '=', $collegeId)
                        ->where('rsse.student_id', '=', $studentId)
                        ->where('rsse.course_id', '=', $courseId)
                        ->get(['rsu.id',
                            'rsu.unit_code',
                            'rsu.vet_unit_code',
                            'rsu.unit_name',
                            'rsue.compentency',
                            'rs.subject_code',
                            'rv.postcode',
                            'rto_students.birth_country',
                            'rsse.*']);
    }

    public function getStudentSubjectEnrolmentDetailForXls($arrFilter) {


        $form_date = date('Y-m-d', strtotime($arrFilter['from_date']));
        $to_date = date('Y-m-d', strtotime($arrFilter['to_date']));

        $sql = StudentSubjectEnrolment::leftjoin('rto_semester', 'rto_semester.id', '=', 'rto_student_subject_enrolment.semester_id')
                ->leftjoin('rto_subject', 'rto_subject.id', '=', 'rto_student_subject_enrolment.subject_id')
                ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_subject_enrolment.course_id')
                ->leftjoin('rto_venue', 'rto_venue.id', '=', 'rto_student_subject_enrolment.vanue_location')
                ->leftjoin('rto_students', 'rto_students.id', '=', 'rto_student_subject_enrolment.student_id')
                ->leftjoin('rto_student_details', 'rto_student_details.student_id', '=', 'rto_students.id')
                ->leftjoin('rto_student_unit_enrollment as sue', 'sue.student_subject_enrollment_id', '=', 'rto_student_subject_enrolment.id')
                ->leftjoin('rto_student_courses', function($join) {
                    $join->on('rto_student_courses.student_id', '=', 'rto_student_subject_enrolment.student_id');
                    $join->on('rto_student_courses.course_id', '=', 'rto_student_subject_enrolment.course_id');
                })
                // ->leftjoin('rto_student_courses', 'rto_student_courses.course_id', '=', 'rto_student_subject_enrolment.course_id')
                ->leftjoin('rto_colleges', 'rto_colleges.id', '=', 'rto_student_subject_enrolment.college_id')
                ->leftjoin('rto_college_details', 'rto_college_details.college_id', '=', 'rto_colleges.id')
                ->leftjoin('rto_subject_unit', 'rto_subject_unit.id', '=', 'rto_student_subject_enrolment.unit_id')
                ->where('rto_student_subject_enrolment.college_id', '=', Auth::user()->college_id)

                //  ->groupBy('rto_student_subject_enrolment.subject_id')
                ->groupBy('rto_student_subject_enrolment.id')
                ->orderBy('rto_student_subject_enrolment.id');

        $sql = $this->applyGlobalFilter($sql,$arrFilter,'nat120');
        // if (!empty($arrFilter['year'])) {
        //     if($arrFilter['reportType']==1 || $arrFilter['reportType']==2){
        //         $year = $arrFilter['year'];

        //         $sql->where(function($nest) use($year) {
        //             $nest->whereYear('rto_student_subject_enrolment.activity_finish_date', '=',$year);
        //             $nest->orWhereYear('rto_student_subject_enrolment.activity_start_date', '=', $year);
        //         });

        //     }
        // }
        // if (!empty($arrFilter['month']) && $arrFilter['reportType'] == 2) {
        //     $sql->whereMonth('rto_student_subject_enrolment.activity_finish_date', '=', $arrFilter['month']);
        // }
        // if (!empty($arrFilter['stateName']) && $arrFilter['export_type'] == 1) {
        //    $sql->where('rto_students.current_state', '=', $arrFilter['stateName']);
        // }
        // if (!empty($arrFilter['from_date']) && !empty($arrFilter['to_date']) && $arrFilter['reportType'] == 3) {
        //     $sql->whereBetween('rto_student_subject_enrolment.activity_finish_date', array($form_date, $to_date));
        // }
        // if (!empty($arrFilter['claim_only']) && $arrFilter['claim_only'] == 1) {
        //     $sql->where('rto_student_courses.is_claim', '=', $arrFilter['claim_only']);
        // }

        $sql = $this->applySmartAndSkillFilterWithStudent($sql, $arrFilter);

        // $sql->take(5);
        $result = $sql->get(['rto_student_subject_enrolment.*', 'rto_subject_unit.unit_code', 'rto_colleges.RTO_code',
            'rto_subject_unit.internal', 'rto_subject_unit.external', 'rto_subject_unit.workplace_based_delivery',
            'rto_venue.state as venue_state', 'rto_courses.national_code', 'rto_venue.venue_code',
            'rto_students.generated_stud_id', 'rto_semester.semester_name', 'rto_subject.subject_code',
            'rto_subject.subject_name', 'rto_student_courses.campus_id', 'rto_subject.subject_code as subject_id',
            'rto_courses.flexible_attendance', 'sue.*', 'rto_student_subject_enrolment.id as ssendId', 'rto_student_subject_enrolment.delivery_mode as deliveryModetxtfile', 'rto_student_subject_enrolment.predominant_delivery_mode', 'rto_courses.module_delivery',
            'rto_college_details.NSW_TOID', 'rto_college_details.VIC_TOID','rto_college_details.QLD_TOID', 'rto_college_details.SA_TOID',
            'rto_college_details.WA_TOID', 'rto_college_details.TAS_TOID','rto_college_details.NT_TOID', 'rto_college_details.ACT_TOID',
            'rto_student_details.school_type','rto_student_details.attending_secondary_school','rto_student_courses.purchasing_contract_identifier','rto_student_courses.associated_course_identifier',
            'rto_student_courses.purchasing_contract_schedule_identifier','rto_student_courses.is_fulltimelearing','rto_student_courses.is_claim','rto_student_courses.finish_date as coursefinishdate']);

        return $result;
    }

    public function getStudentsFailingOneSemester($courseTypeId, $courseId, $semesterId, $collegeId) {
        $sql = StudentSubjectEnrolment::leftjoin('rto_students', 'rto_students.id', '=', 'rto_student_subject_enrolment.student_id')
                ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_subject_enrolment.course_id')
                ->leftJoin('rto_student_courses', function($join) {
                    $join->on('rto_student_courses.student_id', '=', 'rto_student_subject_enrolment.student_id');
                    $join->on('rto_student_courses.course_id', '=', 'rto_student_subject_enrolment.course_id');
                })
                ->leftjoin('rto_semester', 'rto_semester.id', '=', 'rto_student_subject_enrolment.semester_id')
                ->leftjoin('rto_subject', 'rto_subject.id', '=', 'rto_student_subject_enrolment.subject_id')
                ->where('rto_student_subject_enrolment.course_id', '=', $courseId)
                ->where('rto_student_subject_enrolment.semester_id', '=', $semesterId)
                ->where('rto_semester.course_type_id', '=', $courseTypeId)
                ->where('rto_students.college_id', '=', $collegeId)
                ->groupby(['rto_student_subject_enrolment.student_id', 'rto_student_subject_enrolment.semester_id'])
                ->select('rto_students.generated_stud_id', 'rto_students.first_name', 'rto_students.family_name', 'rto_courses.course_name', 'rto_courses.course_code', 'rto_student_courses.course_attempt', 'rto_semester.semester_name', DB::raw('SUM(rto_student_subject_enrolment.`final_outcome`="NYC") AS total_subjects_NYC'), DB::raw('COUNT(rto_student_subject_enrolment.subject_id) AS total_subjects'));
        $arrResult = $sql->get()->toArray();
        $arrReturn = array();
        foreach ($arrResult as $result) {
            $cntNyc = $result['total_subjects_NYC'];
            $totalSubjects = $result['total_subjects'];
            $halfSubjectCount = $totalSubjects / 2;
            if ($cntNyc >= $halfSubjectCount) {
                $arrReturnTmp = array();
                $arrReturnTmp['student_id'] = $result['generated_stud_id'];
                $arrReturnTmp['student_name'] = $result['first_name'] . " " . $result['family_name'];
                $arrReturnTmp['semester_name'] = $result['semester_name'];
                $arrReturnTmp['course_name'] = $result['course_name'];
                $arrReturnTmp['course_code'] = $result['course_code'];
                $arrReturnTmp['course_attempt'] = $result['course_attempt'];
                $arrReturnTmp['total_subjects_NYC'] = $result['total_subjects_NYC'];
                $arrReturn[] = $arrReturnTmp;
            }
        }
        return $arrReturn;
    }

    public function _getStudentLetterData($semesterId, $courseId, $collegeId) {
        $sql = StudentSubjectEnrolment::leftjoin('rto_students', 'rto_students.id', '=', 'rto_student_subject_enrolment.student_id')
                        ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_subject_enrolment.course_id')
                        ->leftJoin('rto_student_courses', function($join) {
                            $join->on('rto_student_courses.student_id', '=', 'rto_student_subject_enrolment.student_id');
                            $join->on('rto_student_courses.course_id', '=', 'rto_student_subject_enrolment.course_id');
                        })
                        ->leftjoin('rto_semester', 'rto_semester.id', '=', 'rto_student_subject_enrolment.semester_id')
                        ->leftjoin('rto_subject', 'rto_subject.id', '=', 'rto_student_subject_enrolment.subject_id')
                        ->leftjoin('rto_subject_unit', 'rto_subject_unit.id', '=', 'rto_student_subject_enrolment.unit_id')
                        ->where('rto_student_subject_enrolment.course_id', '=', $courseId)
                        ->where('rto_student_subject_enrolment.semester_id', '=', $semesterId)
                        ->where('rto_students.college_id', '=', $collegeId)
                        ->where('rto_student_subject_enrolment.final_outcome', '=', "NYC")
                        ->groupby(['rto_student_subject_enrolment.student_id'])
                        ->get(['rto_students.*',
                            'rto_courses.course_code',
                            'rto_courses.course_name',
                            'rto_student_courses.course_attempt',
                            'rto_student_courses.start_date',
                            'rto_student_courses.finish_date',
                            'rto_student_courses.status',
                            DB::raw('group_concat(CONCAT(rto_subject.subject_code,":",rto_subject.subject_name))as subjectGroup'),
                            DB::raw('group_concat(CONCAT(rto_subject_unit.vet_unit_code,":",rto_subject_unit.unit_name))as unitGroup')
                        ])->toarray();

        return $sql;
    }

    public function getStudentsFailingConsecutiveSemester($collegeId, $courseTypeId, $courseId, $semesterId) {
        $sql = StudentSubjectEnrolment::leftjoin('rto_students', 'rto_students.id', '=', 'rto_student_subject_enrolment.student_id')
                ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_subject_enrolment.course_id')
                ->leftJoin('rto_student_courses', function($join) {
                    $join->on('rto_student_courses.student_id', '=', 'rto_student_subject_enrolment.student_id');
                    $join->on('rto_student_courses.course_id', '=', 'rto_student_subject_enrolment.course_id');
                })
                ->leftjoin('rto_semester', 'rto_semester.id', '=', 'rto_student_subject_enrolment.semester_id')
                ->leftjoin('rto_subject', 'rto_subject.id', '=', 'rto_student_subject_enrolment.subject_id')
                ->where('rto_student_subject_enrolment.course_id', '=', $courseId)
                ->where('rto_student_subject_enrolment.semester_id', '=', $semesterId)
                ->where('rto_semester.course_type_id', '=', $courseTypeId)
                ->where('rto_students.college_id', '=', $collegeId)
                ->groupby(['rto_student_subject_enrolment.student_id', 'rto_student_subject_enrolment.semester_id'])
                ->select('rto_students.id as student_id', 'rto_students.email', 'rto_students.student_type', 'rto_students.generated_stud_id', 'rto_students.first_name', 'rto_students.family_name', 'rto_courses.course_name', 'rto_courses.course_code', 'rto_student_courses.course_attempt', 'rto_student_courses.start_date', 'rto_student_courses.finish_date', 'rto_student_courses.status', 'rto_semester.semester_name', DB::raw('SUM(rto_student_subject_enrolment.`final_outcome`="NYC") AS total_subjects_NYC'), DB::raw('COUNT(rto_student_subject_enrolment.subject_id) AS total_subjects'));
        $arrResult = $sql->get()->toArray();
        $arrReturn = array();
        foreach ($arrResult as $result) {
            $cntNyc = $result['total_subjects_NYC'];
            $totalSubjects = $result['total_subjects'];
            $halfSubjectCount = $totalSubjects / 2;
            if ($cntNyc >= $halfSubjectCount) {
                $arrReturnTmp = array();
                $arrReturnTmp['student_id'] = $result['generated_stud_id'];
                $arrReturnTmp['email'] = $result['email'];
                $arrReturnTmp['student_type'] = $result['student_type'];
                $arrReturnTmp['student_name'] = $result['first_name'] . " " . $result['family_name'];
                $arrReturnTmp['semester_name'] = $result['semester_name'];
                $arrReturnTmp['course_name'] = $result['course_name'];
                $arrReturnTmp['course_code'] = $result['course_code'];
                $arrReturnTmp['course_attempt'] = $result['course_attempt'];
                $arrReturnTmp['start_date'] = date('d-m-Y', strtotime($result['start_date']));
                $arrReturnTmp['finish_date'] = date('d-m-Y', strtotime($result['finish_date']));
                $arrReturnTmp['status'] = $result['status'];
                $arrReturnTmp['total_subjects_NYC'] = $result['total_subjects_NYC'];
                $arrSubjectsTmp = $this->getStudentsFailedSubjects($result['student_id'], $courseId, $semesterId);
                //$arrReturnTmp['subjects'] = $arrSubjectsTmp;
                $arrReturn[] = $arrReturnTmp + $arrSubjectsTmp;
            }
        }
        return $arrReturn;
    }

    public function getStudentsFailedSubjects($studentId, $courseId, $semesterId) {
        $sql = StudentSubjectEnrolment::leftjoin('rto_subject', 'rto_subject.id', '=', 'rto_student_subject_enrolment.subject_id')
                ->leftjoin('rto_subject_unit', 'rto_subject_unit.id', '=', 'rto_student_subject_enrolment.unit_id')
                ->where('rto_student_subject_enrolment.course_id', '=', $courseId)
                ->where('rto_student_subject_enrolment.semester_id', '=', $semesterId)
                ->where('rto_student_subject_enrolment.student_id', '=', $studentId)
                ->where('rto_student_subject_enrolment.final_outcome', '=', "NYC")
                ->select('rto_subject.subject_code', 'rto_subject.subject_name', 'rto_subject_unit.unit_code', 'rto_subject_unit.unit_name');
        $arrResult = $sql->get()->toArray();

        $arrReturn = array();
        foreach ($arrResult as $result) {
            $arrReturnTmp = array();
            $arrReturnTmpSubject[] = $result['subject_code'] . " : " . $result['subject_name'];
            $arrReturnTmpUnit[] = $result['unit_code'] . " : " . $result['unit_name'];
        }
        $arrSubject['subject'] = array_values(array_unique($arrReturnTmpSubject));
        $arrUnit['unit'] = array_values(array_unique($arrReturnTmpUnit));
        $arrReturn = $arrSubject + $arrUnit;
        return $arrReturn;
    }

    public function getStudentListByCourseClass($perPage, $courseId, $semesterId, $term, $subjectId, $batch, $classStart, $classFinish, $collegeId) {
        $sql = StudentSubjectEnrolment::leftjoin('rto_students', 'rto_students.id', '=', 'rto_student_subject_enrolment.student_id')
                ->leftjoin('rto_subject', 'rto_subject.id', '=', 'rto_student_subject_enrolment.subject_id')
                ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_subject_enrolment.course_id')
                ->leftjoin('rto_student_courses', 'rto_student_courses.student_id', '=', 'rto_student_subject_enrolment.student_id')
                ->leftjoin('rto_campus', 'rto_campus.id', '=', 'rto_student_courses.campus_id')
                ->leftjoin('rto_timetable', 'rto_timetable.semester_id', '=', 'rto_student_subject_enrolment.semester_id')
                ->where('rto_student_subject_enrolment.college_id', '=', $collegeId)
                ->where('rto_student_subject_enrolment.course_id', '=', $courseId)
                ->where('rto_student_subject_enrolment.semester_id', '=', $semesterId)
                ->where('rto_student_subject_enrolment.term', '=', $term)
                ->where('rto_student_subject_enrolment.subject_id', '=', $subjectId)
                ->where('rto_student_subject_enrolment.batch', '=', $batch)
                ->where('rto_timetable.start_week', '=', $classStart)
                ->where('rto_timetable.end_week', '=', $classFinish)
                ->groupBy(['rto_students.id'])
                ->select([
            'rto_students.generated_stud_id',
            'rto_students.first_name',
            'rto_students.family_name',
            'rto_students.nickname',
            'rto_students.email',
            'rto_students.current_mobile_phone',
            'rto_students.current_building_name',
            'rto_students.current_unit_detail',
            'rto_students.current_street_no',
            'rto_students.current_street_name',
            'rto_students.current_city',
            'rto_students.current_state',
            'rto_students.current_postcode',
            'rto_students.current_country',
            'rto_courses.course_code',
            'rto_courses.course_name',
            'rto_student_subject_enrolment.term',
            'rto_student_courses.start_date',
            'rto_student_courses.finish_date',
            'rto_student_courses.status',
            'rto_campus.name as campus_name',
            'rto_subject.subject_code'
        ]);
        if (!empty($perPage)) {
            return $sql->paginate($perPage);
        } else {
            return $sql->get();
        }
    }

    public function getStudentContactUploadReport($perPage, $courseTypeId, $courseId, $semesterId, $status, $collegeId) {
        $sql = StudentSubjectEnrolment::leftjoin('rto_students', 'rto_students.id', '=', 'rto_student_subject_enrolment.student_id')
                ->leftjoin('rto_semester', 'rto_semester.id', '=', 'rto_student_subject_enrolment.semester_id')
                ->leftjoin('rto_student_courses', 'rto_student_courses.student_id', '=', 'rto_student_subject_enrolment.student_id')
                ->where('rto_student_subject_enrolment.college_id', '=', $collegeId)
                ->where('rto_semester.course_type_id', '=', $courseTypeId)
                ->where('rto_student_subject_enrolment.semester_id', '=', $semesterId)
                ->groupBy(['rto_students.id'])
                ->select([
            'rto_students.generated_stud_id',
            'rto_students.first_name',
            'rto_students.family_name',
            'rto_students.nickname',
            'rto_students.email',
            'rto_students.current_home_phone',
            'rto_students.current_mobile_phone',
            'rto_students.current_building_name',
            'rto_students.current_unit_detail',
            'rto_students.current_street_no',
            'rto_students.current_street_name',
            'rto_students.current_city',
            'rto_students.current_state',
            'rto_students.current_postcode',
            'rto_students.current_country',
            'rto_student_courses.coe_name'
        ]);
        if (!empty($courseId)) {
            $sql->where('rto_student_subject_enrolment.course_id', '=', $courseId);
        }
        if (!empty($status)) {
            $sql->where('rto_student_courses.status', '=', $status);
        }
        if (!empty($perPage)) {
            return $sql->paginate($perPage);
        } else {
            return $sql->get();
        }
    }

    public function getStudentListForTeacher($dataArr, $collegeId, $userId) {

        return StudentSubjectEnrolment::from('rto_student_subject_enrolment as rsse')
                        ->leftJoin('rto_student_courses as rsc', function($join) {
                            $join->on('rsc.student_id', '=', 'rsse.student_id');
                            $join->on('rsc.course_id', '=', 'rsse.course_id');
                        })
                        ->join('rto_timetable as rt', function($join) {
                            $join->on('rt.subject_id', '=', 'rsse.subject_id');
                            $join->on('rt.semester_id', '=', 'rsse.semester_id');
                            $join->on('rt.term', '=', 'rsse.term');
                            $join->on('rt.batch', '=', 'rsse.batch');
                        })
                        ->join('rto_staff_and_teacher as rst', 'rst.id', 'rt.teacher_id')
                        ->join('rto_users as ru', 'ru.id', 'rst.user_id')
                        ->leftjoin('rto_students as rs', 'rs.id', '=', 'rsse.student_id')
                        ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsse.course_id')
                        ->leftjoin('rto_colleges as rclg', 'rclg.id', '=', 'rsse.college_id')
                        ->where('rsse.subject_id', $dataArr['subjectId'])
                        ->where('rsse.semester_id', $dataArr['semesterId'])
                        ->where('rsse.term', $dataArr['termId'])
                        ->where('rsse.batch', $dataArr['batch'])
                        ->where('rsse.college_id', $collegeId)
                        ->where('ru.id', $userId)
                        ->groupby(['rsse.student_id'])
                        ->select('rs.generated_stud_id', 'rs.id as student_id', DB::raw('concat(rs.name_title," ",rs.first_name," ",rs.family_name) as student_name'), 'rsse.course_id', 'rc.course_code', 'rsc.agent_id', 'rsc.status', 'rs.current_mobile_phone as phone', 'rs.email as private_email', 'rclg.contact_email as college_email')
                        ->get();
    }

    public function getStudentNameListForTeacher($dataArr, $collegeId) {

        $resultArr = StudentSubjectEnrolment::from('rto_student_subject_enrolment as rsse')
                ->leftjoin('rto_students as rs', 'rs.id', '=', 'rsse.student_id')
                ->where('rsse.subject_id', $dataArr['subjectId'])
                ->where('rsse.semester_id', $dataArr['semesterId'])
                ->where('rsse.term', $dataArr['termId'])
                ->where('rsse.batch', $dataArr['batch'])
                ->where('rsse.college_id', $collegeId)
                ->select('rs.id', DB::raw('concat(rs.name_title,rs.first_name," ",rs.family_name, " (", rs.generated_stud_id, ")") as student_name'))
                ->get();

        $nullResult = array('' => 'No Student Found');
        foreach ($resultArr as $row) {
            $result[$row->id] = $row->student_name;
        }
        return $result = ($resultArr->count() > 0) ? $result : $nullResult;
    }

    public function getStudentExcelData($studentId, $courseId) {
        return $sql = StudentSubjectEnrolment::leftjoin('rto_students', 'rto_students.id', '=', 'rto_student_subject_enrolment.student_id')
                        ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_subject_enrolment.course_id')
                        ->leftjoin('rto_semester', 'rto_semester.id', '=', 'rto_student_subject_enrolment.semester_id')
                        ->leftjoin('rto_subject', 'rto_subject.id', '=', 'rto_student_subject_enrolment.subject_id')
                        ->leftjoin('rto_subject_unit', 'rto_subject_unit.id', '=', 'rto_student_subject_enrolment.unit_id')
                        ->leftjoin('rto_student_unit_enrollment', 'rto_student_unit_enrollment.student_subject_enrollment_id', '=', 'rto_student_subject_enrolment.id')
                        ->where('rto_student_subject_enrolment.student_id', $studentId)
                        ->where('rto_student_subject_enrolment.course_id', $courseId)
                        ->get(['rto_students.first_name',
                            'rto_students.middel_name',
                            'rto_students.generated_stud_id',
                            'rto_students.created_at',
                            'rto_courses.course_name',
                            'rto_courses.course_code',
                            'rto_semester.semester_name',
                            'rto_student_subject_enrolment.term',
                            'rto_student_subject_enrolment.course_stage',
                            'rto_student_subject_enrolment.final_outcome',
                            'rto_student_subject_enrolment.grade',
                            'rto_student_subject_enrolment.marks',
                            'rto_student_subject_enrolment.activity_start_date',
                            'rto_student_subject_enrolment.activity_finish_date',
                            'rto_student_subject_enrolment.batch',
                            'rto_subject.subject_name',
                            'rto_subject.subject_code',
                            'rto_subject_unit.unit_name',
                            'rto_student_unit_enrollment.study_from',
                            'rto_student_unit_enrollment.study_to',
                            'rto_student_unit_enrollment.compentency',
                        ])->toarray();
    }

    public function getAssessmentResultList($collegeId, $dataArr, $arrStudentIds = [], $isHigherEd=false) {
        $arrTemp = explode("-", $dataArr['semesterId']);
        $semesterId = $arrTemp[0];
        $term = $arrTemp[1];

        $totalMarks = $passMarks = $weightMarks = 0;
        if($isHigherEd){
            $whereArr = [
                'college_id'        => $collegeId,
                'subject_id'        => $dataArr['subjectId'],
                'semester_id'       => $semesterId,
                'term'              => $term,
                'batch'             => $dataArr['batch'],
                'assessment_task_id'=> $dataArr['assessment']
            ];
            $marksRes = AssignedAssessmentTask::Where($whereArr)->get()->first();
            $totalMarks = (isset($marksRes->total_marks) && !empty($marksRes->total_marks)) ? $marksRes->total_marks : 0;
            $passMarks = (isset($marksRes->pass_marks) && !empty($marksRes->pass_marks)) ? $marksRes->pass_marks : 0;
            $weightMarks = (isset($marksRes->assessment_weight) && !empty($marksRes->assessment_weight)) ? $marksRes->assessment_weight : 0;
        }

        $qry = StudentSubjectEnrolment::from('rto_student_subject_enrolment as rsse')
                ->leftJoin('rto_student_assigned_assessment_tasks as rsaat', function($join) use ($dataArr) {
                    $join->on('rsaat.student_id', '=', 'rsse.student_id');
                    $join->on('rsaat.assessment_task_id', '=', DB::raw($dataArr['assessment']));
                })
                ->leftjoin('rto_students as rs', 'rs.id', '=', 'rsse.student_id')
                ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsse.course_id')
                ->leftJoin('rto_student_courses as rsc', function($join) {
                    $join->on('rsc.student_id', '=', 'rsse.student_id');
                    $join->on('rsc.course_id', '=', 'rsse.course_id');
                })
                ->where([
                    'rsse.college_id'   => $collegeId,
                    'rsse.semester_id'  => $semesterId,
                    'rsse.subject_id'   => $dataArr['subjectId'],
                    'rsse.term'         => $term,
                    'rsse.batch'        => $dataArr['batch']
                ]);

                if (!empty($arrStudentIds)) {
                    $qry->whereIn('rs.id', $arrStudentIds);
                }

                $qry->select([
                    'rsse.id',
                    'rsse.subject_attempt',
                    'rsse.final_outcome',
                    'rsse.created_at',
                    'rc.course_code',
                    'rc.course_name',
                    'rsc.campus_id',
                    'rsc.status',
                    'rsc.course_attempt',
                    'rsaat.comments',
                    'rsaat.is_locked',
                    'rsaat.is_approved',
                    'rsaat.competency',
                    'rsaat.comments',
                    'rsaat.subject_attempts',
                    'rsaat.assessment_type',
                    'rsaat.result_comment',
                    'rc.flexible_attendance',
                    'rc.course_type_id',
                    'rc.id as course_id',
                    'rsse.student_id',
                    'rs.generated_stud_id',
                    'rs.first_name',
                    'rs.family_name',
                    'rsse.marks',
                    'rsaat.marks as assign_marks',
                    DB::raw('concat(rs.name_title, rs.first_name," ",rs.family_name) as student_name'),
                    DB::raw("$totalMarks as total_marks"),
                    DB::raw("$passMarks as pass_marks"),
                    DB::raw("$weightMarks as weight_marks")
                ]);

        return $qry->groupby(['rsse.student_id'])->get();
        //->toArray();
    }

    public function getSubjectListForSubjectMaterial($collegeId, $studentId) {
        $result = array();
        $resultArr = StudentSubjectEnrolment::from('rto_student_subject_enrolment as rsse')
                ->leftjoin('rto_subject as rs', 'rs.id', 'rsse.subject_id')
                ->where('rsse.college_id', '=', $collegeId)
                ->where('rsse.student_id', '=', $studentId)
                ->groupBy('rsse.subject_id')
                ->get(['rs.id', 'rs.subject_code', 'rs.subject_name']);
        foreach ($resultArr as $row) {
            $result[] = $row->subject_code;
        }
        return $result;
    }

    public function getBatchDerictoryName($studentId, $subjectId, $collegeId) {
        $result = array();
        $resultArr = StudentSubjectEnrolment::from('rto_student_subject_enrolment as rsse')
                ->leftjoin('rto_semester as rs', 'rs.id', 'rsse.semester_id')
                ->where('rsse.college_id', $collegeId)
                ->where('rsse.subject_id', $subjectId)
                ->where('rsse.student_id', $studentId)
                ->groupBy('rsse.batch')
                ->get(['rsse.batch', 'rsse.term', 'rs.semester_name']);
        foreach ($resultArr as $row) {
            $result[] = $row->semester_name . '_' . $row->term . '_' . $row->batch;
        }
        return $result;
    }

    // get subjects based on the student id and course id for NSW V2
    public function getStudentUnitDataForNSWV2($student_id, $nswCourseSiteId) {

        $sql = StudentSubjectEnrolment::leftjoin('rto_subject_unit', 'rto_subject_unit.id', '=', 'rto_student_subject_enrolment.unit_id')
                ->leftjoin('rto_subject', 'rto_subject.id', '=', 'rto_student_subject_enrolment.student_id')
                ->leftjoin('rto_venue', 'rto_venue.id', '=', 'rto_student_subject_enrolment.vanue_location')
                ->where('rto_student_subject_enrolment.student_id', '=', $student_id);

        $arrStudentUnitResult = $sql->select('rto_subject_unit.unit_code', 'rto_subject_unit.unit_name', 'rto_subject_unit.vet_unit_code', 'rto_subject_unit.unit_type', 'rto_subject.subject_code', 'rto_subject.subject_name', 'rto_student_subject_enrolment.final_outcome', 'rto_student_subject_enrolment.course_site_id', 'rto_student_subject_enrolment.activity_start_date', 'rto_student_subject_enrolment.activity_finish_date', 'rto_venue.venue_name', 'rto_venue.venue_code', 'rto_venue.state')
                ->get();
        return $arrStudentUnitResult;
    }

    public function getStudentSubjectEnrolmentDetailForNSW($arrFilter) {


        $sql = StudentSubjectEnrolment::leftjoin('rto_semester', 'rto_semester.id', '=', 'rto_student_subject_enrolment.semester_id')
                ->leftjoin('rto_subject', 'rto_subject.id', '=', 'rto_student_subject_enrolment.subject_id')
                ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_subject_enrolment.course_id')
                ->leftjoin('rto_venue', 'rto_venue.college_id', '=', 'rto_student_subject_enrolment.college_id')
                ->leftjoin('rto_students', 'rto_students.id', '=', 'rto_student_subject_enrolment.student_id')
                ->leftjoin('rto_student_unit_enrollment as sue', 'sue.student_subject_enrollment_id', '=', 'rto_student_subject_enrolment.id')
                ->leftjoin('rto_student_courses', 'rto_student_courses.course_id', '=', 'rto_student_subject_enrolment.course_id')
                ->leftjoin('rto_college_details', 'rto_college_details.college_id', '=', 'rto_student_subject_enrolment.college_id')
                ->leftjoin('rto_subject_unit', 'rto_subject_unit.id', '=', 'sue.unit_id')
                ->where('rto_student_subject_enrolment.college_id', '=', Auth::user()->college_id)
                ->groupBy('rto_student_subject_enrolment.subject_id')
                ->groupBy('rto_student_subject_enrolment.semester_id')
                ->orderBy('rto_student_subject_enrolment.id');
        if (!empty($arrFilter['claim_stage'])) {
            $sql->leftjoin('rto_student_claim_tracking', 'rto_student_claim_tracking.student_id', '=', 'rto_students.id');
            $sql->where('rto_student_claim_tracking.lodgement_type', $arrFilter['claim_stage']);
        }
        $sql->where('rto_venue.state', '=', 'NSW');

        $result = $sql->get(['rto_student_subject_enrolment.*', 'rto_subject_unit.unit_code', 'rto_college_details.training_organisation_type_id',
            'rto_venue.state as venue_state', 'rto_courses.national_code', 'rto_venue.venue_code',
            'rto_students.generated_stud_id', 'rto_semester.semester_name', 'rto_subject.subject_code',
            'rto_subject.subject_name', 'rto_student_courses.campus_id', 'rto_subject.subject_code as subject_id',
            'rto_courses.flexible_attendance', 'sue.*']);

        return $result;
    }

    // get subjects based on the student id and course id for NSW V2
    public function getStudentUnitDataForNSWV1($student_id, $nswCourseSiteId, $collegeId) {

        $sql = StudentSubjectEnrolment::leftjoin('rto_subject_unit', 'rto_subject_unit.id', '=', 'rto_student_subject_enrolment.unit_id')
                ->leftjoin('rto_subject', 'rto_subject.id', '=', 'rto_student_subject_enrolment.student_id')
                ->leftjoin('rto_venue', 'rto_venue.id', '=', 'rto_student_subject_enrolment.vanue_location')
                ->where('rto_student_subject_enrolment.student_id', '=', $student_id)
                ->where('rto_student_subject_enrolment.college_id', '=', $collegeId);

        $arrStudentUnitResult = $sql->select('rto_subject_unit.unit_code', 'rto_subject_unit.unit_name', 'rto_subject_unit.vet_unit_code', 'rto_subject_unit.unit_type', 'rto_subject.subject_code', 'rto_subject.subject_name', 'rto_student_subject_enrolment.final_outcome', 'rto_student_subject_enrolment.course_site_id', 'rto_student_subject_enrolment.activity_start_date', 'rto_student_subject_enrolment.activity_finish_date', 'rto_venue.venue_name', 'rto_venue.venue_code', 'rto_venue.state')
                ->get();
        return $arrStudentUnitResult;
    }

    public function getSemesterListByCourse($collegeId, $courseId, $studentId) {

        $resultArr = StudentSubjectEnrolment::from('rto_student_subject_enrolment as rsse')
                ->leftjoin('rto_semester as rs', 'rs.id', '=', 'rsse.semester_id')
                ->where('rsse.college_id', $collegeId)
                ->where('rsse.course_id', $courseId)
                ->where('rsse.student_id', $studentId)
                ->pluck('rs.semester_name', 'rs.id')
                ->toArray();

        $nullResult[''] = 'No Semester Found';
        $result = (count($resultArr) > 0) ? $resultArr : $nullResult;
        return $result;
    }

    public function getTermListBySemester($collegeId, $courseId, $semesterId, $studentId) {

        $resultArr = StudentSubjectEnrolment::where('college_id', $collegeId)
                ->where('course_id', $courseId)
                ->where('semester_id', $semesterId)
                ->where('student_id', $studentId)
                ->pluck('term', 'term')
                ->toArray();

        $nullResult[''] = 'No Term Found';
        $result = (count($resultArr) > 0) ? $resultArr : $nullResult;
        return $result;
    }

    public function getTermListBySemesterForEvaluation($collegeId, $semesterId, $studentId) {

        $resultArr = StudentSubjectEnrolment::where('college_id', $collegeId)
                ->where('semester_id', $semesterId)
                ->where('student_id', $studentId)
                ->pluck('term', 'term')
                ->toArray();

        $nullResult[''] = 'No Term Found';
        $result = (count($resultArr) > 0) ? $resultArr : $nullResult;
        return $result;
    }

    public function getNotSendRequestStudentList($collegeId, $semesterId, $subjectId, $batch, $status = '') {

        $sql = StudentSubjectEnrolment::from('rto_student_subject_enrolment as rsse')
                ->leftjoin('rto_students as rs', 'rs.id', '=', 'rsse.student_id')
                ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsse.course_id')
                ->leftJoin('rto_student_courses as rsc', function($join) {
                    $join->on('rsc.course_id', '=', 'rsse.course_id');
                    $join->on('rsc.student_id', '=', 'rsse.student_id');
                })
                ->where('rsse.college_id', '=', $collegeId)
                ->where('rsse.semester_id', '=', $semesterId)
                ->where('rsse.subject_id', '=', $subjectId)
                ->where('rsse.batch', '=', $batch)
                ->groupby(['rsse.student_id']);

        if (!empty($status)) {
            $sql->where('rsc.offer_status', $status);
        }

        $result = $sql->select('rsse.student_id', 'rs.generated_stud_id', 'rs.name_title', 'rs.first_name', 'rs.family_name', DB::raw('concat(rs.name_title, rs.first_name, " ", rs.family_name) as student_name'), 'rs.email', 'rc.course_code', 'rsc.course_attempt', 'rsc.offer_status', 'rsc.status')
                ->get();

        return $result;
    }

    public function getCourseListForEvalution($semesterId, $termId, $studentId) {
        $resultArr = StudentSubjectEnrolment::from('rto_student_subject_enrolment as rsse')
                ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsse.course_id')
                ->where('rsse.semester_id', $semesterId)
                ->where('rsse.student_id', $studentId)
                ->where('rsse.term', $termId)
                ->groupBy('rc.id')
                ->select(DB::raw('concat(rc.course_code," : ",rc.course_name) as courseName'), 'rc.id')
                ->pluck('courseName', 'id')
                ->toArray();
        //->get();
        if (count($resultArr) > 0) {
            return $resultArr;
        } else {
            $result[''] = 'No Course Found';
            return $result;
        }
    }

    //THIS FUNCTION IS USED FOR GENERATE PDF AND EXCEL FOR PRINT ATTADANCE.
    public function getSemsterEnrollStudentAttendanceV2($semesterId, $term, $subjectId, $batch, $collegeId) {
        $result = StudentSubjectEnrolment::from('rto_student_subject_enrolment as studentSubject')
                ->leftjoin('rto_students', 'rto_students.id', '=', 'studentSubject.student_id')
                ->leftjoin('rto_courses', 'rto_courses.id', '=', 'studentSubject.course_id')
                ->leftJoin('rto_student_courses as studentCourse', function($join) {
                    $join->on('studentCourse.course_id', '=', 'studentSubject.course_id');
                    $join->on('studentCourse.student_id', '=', 'studentSubject.student_id');
                })
                ->leftjoin('rto_semester', 'rto_semester.id', '=', 'studentSubject.semester_id')
                ->leftjoin('rto_timetable', 'rto_timetable.semester_id', '=', 'rto_semester.id')
                ->leftjoin('rto_classroom', 'rto_classroom.id', '=', 'rto_timetable.classroom_id')
                ->leftjoin('rto_subject', 'rto_subject.id', '=', 'studentSubject.subject_id')
                ->where('studentSubject.college_id', '=', $collegeId)
                ->where('studentSubject.subject_id', '=', $subjectId)
                ->where('studentSubject.semester_id', '=', $semesterId)
                ->where('studentSubject.term', '=', $term)
                ->where('studentSubject.batch', '=', $batch)
                ->groupby(['studentSubject.student_id'])
                ->get([
                    'rto_students.generated_stud_id',
                    'rto_students.first_name',
                    'studentSubject.student_id',
                    'rto_semester.semester_name',
                    'studentSubject.term',
                    'studentCourse.start_date as courseStartDate',
                    'studentCourse.finish_date as courseFinishDate',
                    'studentCourse.course_id as coursecourse_id',
                    'studentCourse.status as studentCourseStatus',
                    'rto_courses.course_code',
                    'rto_subject.subject_code',
                    'rto_subject.subject_name',
                    'studentSubject.batch',
                    'studentSubject.activity_start_date',
                    'studentSubject.activity_finish_date',
                    'studentSubject.study_reason',
                    'studentSubject.funding_source_state',
                    'studentSubject.final_outcome',
                    'rto_timetable.day',
                    'rto_timetable.class_type',
                    'rto_timetable.term',
                    'rto_timetable.class_capacity',
                    'rto_timetable.attendance_type',
                    'rto_timetable.start_time',
                    'rto_timetable.finish_time',
                    'rto_timetable.start_week',
                    'rto_timetable.end_week',
                    'rto_timetable.start_week_id',
                    'rto_timetable.end_week_id',
                    'rto_timetable.batch'
                ])
                ->toarray();

        return $result;
    }

    public function getStudentListByIndividualClass($collegeId, $courseTypeId, $semesterId, $termId, $subjectId, $batch) {

        $res = StudentSubjectEnrolment::from('rto_student_subject_enrolment as rsse')
                ->leftjoin('rto_students as rs', 'rs.id', '=', 'rsse.student_id')
                ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsse.course_id')
                ->leftJoin('rto_student_courses as rsc', function($join) {
                    $join->on('rsc.student_id', '=', 'rsse.student_id');
                    $join->on('rsc.course_id', '=', 'rsse.course_id');
                })
                ->leftjoin('rto_subject as sub', 'sub.id', '=', 'rsse.subject_id')
                ->where('rc.course_type_id', $courseTypeId)
                ->where('rsse.college_id', $collegeId)
                ->where('rsse.semester_id', $semesterId)
                ->where('rsse.subject_id', $subjectId)
                ->where('rsse.term', $termId)
                ->where('rsse.batch', $batch)
                ->groupby('rsse.student_id')
                ->select('rs.id as stud_id', 'rs.generated_stud_id as student_id', DB::raw('CONCAT(rs.first_name, " ", rs.family_name) AS student_name'), DB::raw('CONCAT(rc.course_code, " ", rc.course_name) AS course'), 'rsc.course_attempt', DB::raw("DATE_FORMAT(rsc.start_date, '%d/%m/%Y') AS start"), DB::raw("DATE_FORMAT(rsc.finish_date, '%d/%m/%Y') AS finish"), 'sub.subject_code', 'sub.subject_name', 'rsse.batch', 'rsse.subject_attempt')
                ->get();
        return $res;
    }

    public function getStudentListByCourseWithFilter($collegeId, $courseId, $semesterId, $term, $weekPeriod, $order_type, $orderBy) {

        $batchArr = StudentSubjectEnrolment::select(DB::raw('GROUP_CONCAT(DISTINCT batch SEPARATOR ", ") AS batch'), 'student_id')
                ->where('college_id', '=', $collegeId)
                ->where('course_id', '=', $courseId)
                ->where('semester_id', '=', $semesterId)
                ->where('term', '=', $term)
                ->groupBy(['student_id'])
                ->pluck('batch', 'student_id');

        $sql = StudentSubjectEnrolment::from('rto_student_subject_enrolment as rsse')
                ->leftjoin('rto_students as rs', 'rs.id', '=', 'rsse.student_id')
                ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsse.course_id')
                ->leftjoin('rto_student_courses as rsc', 'rsc.student_id', '=', 'rsse.student_id')
                ->leftjoin('rto_timetable as rt', 'rt.semester_id', '=', 'rsse.semester_id')
                ->leftJoin('rto_semester_division', function($join) {
            $join->on('rto_semester_division.semester_id', '=', 'rsse.semester_id');
            $join->on('rto_semester_division.term', '=', 'rt.term');
        });

        $sql->where('rsse.college_id', '=', $collegeId);
        $sql->where('rsse.course_id', '=', $courseId);
        $sql->where('rsse.semester_id', '=', $semesterId);
        $sql->where('rsse.term', '=', $term);
        $sql->where('rto_semester_division.week_period', '=', $weekPeriod);

        $sql->orderBy('rs.first_name', 'ASC')
        ->orderBy('rs.family_name', 'ASC')->groupBy(['rs.id', 'rt.id']);

        if ($orderBy == 'student_id') {
            $sql->orderBy('rs.generated_stud_id', $order_type);
        }

        if ($orderBy == 'lastName') {
            $sql->orderBy('rs.family_name', $order_type);
        }

        $result = $sql->get([
                    'rs.generated_stud_id',
                    'rs.first_name',
                    'rs.family_name',
                    'rsse.student_id',
                    'rt.batch',
                    'rc.course_code',
                    'rc.course_name',
                    'rsse.term',
                    'rsc.start_date',
                    'rsc.finish_date',
                    'rsc.status',
                    'rsc.course_attempt',
                    'rt.id',
                    'rt.day',
                    'rt.start_time',
                    'rt.finish_time',
                    'rt.break_from',
                    'rt.break_to',
                    'rto_semester_division.week_start',
                    'rto_semester_division.week_finish',
                ])->toarray();

//        echo "<pre>";print_r($result);exit;

        $objHoliday = new Holiday();
        $arrHoliday = $objHoliday->getHolidayArrayList($collegeId);

        $data = array();
        foreach ($result as $arrData) {
            $data[$arrData['student_id']]['generated_stud_id'] = $arrData['generated_stud_id'];
            $data[$arrData['student_id']]['student_id'] = $arrData['student_id'];
            $data[$arrData['student_id']]['student_name'] = $arrData['first_name'] . " " . $arrData['family_name'];
            $data[$arrData['student_id']]['course_name'] = $arrData['course_code'] . " : " . $arrData['course_name'];
            $data[$arrData['student_id']]['start_date'] = date('d-m-Y', strtotime($arrData['start_date']));
            $data[$arrData['student_id']]['finish_date'] = date('d-m-Y', strtotime($arrData['finish_date']));
            $data[$arrData['student_id']]['status'] = $arrData['status'];
            $data[$arrData['student_id']]['course_attempt'] = $arrData['course_attempt'];

            $objTimetable = new Timetable();
            $arrDays = $objTimetable->getDays($arrData['week_start'], $arrData['week_finish']);
            if($arrData['day'] != ''){
                if (!in_array($arrDays[$arrData['day']], $arrHoliday)) {
                    if (isset($batchArr[$arrData['student_id']])) {
                        $batchList = explode(', ', $batchArr[$arrData['student_id']]);
                        if (in_array($arrData['batch'], $batchList)) {
                            //echo $arrData['batch'];exit;
                            $t1 = StrToTime($arrData['finish_time']);
                            $t2 = StrToTime($arrData['start_time']);
                            $diff = $t1 - $t2;
                            $hours = $diff / ( 60 * 60 );

                            $objTimetableBreaktimeArr = new TimetableBreaktime();
                            $breakHours = $objTimetableBreaktimeArr->getTimetableBreaktimeFromToCalculate($arrData['id']);

                            $totalHours = $hours;
                            if (isset($data[$arrData['student_id']]['total_hours'])) {
                                $totalHours = $data[$arrData['student_id']]['total_hours'] + $hours;
                            }
                            $data[$arrData['student_id']]['total_hours'] = $totalHours - $breakHours;
                        }
                    }
                }
            }
        }

        return $data;
    }

    public function checkUnitId($collegeId, $unitId) {
        return StudentSubjectEnrolment::where('college_id', '=', $collegeId)
                        ->where('unit_id', '=', $unitId)
                        ->count();
    }

    public function countStudentSubjectEnrolment($course_id, $student_id, $collegeId) {
        return StudentSubjectEnrolment::where('college_id', '=', $collegeId)
                        ->where('college_id', '=', $collegeId)
                        ->where('course_id', '=', $course_id)
                        ->where('student_id', '=', $student_id)
                        ->count();
    }

    public function getVetFeeStudentUnitInfo($collegeId, $studentId, $courseId) {

        return StudentSubjectEnrolment::from('rto_student_subject_enrolment as rsse')
                        ->leftjoin('rto_student_unit_enrollment as rsue', 'rsue.student_subject_enrollment_id', '=', 'rsse.id')
                        ->leftjoin('rto_subject_unit as rsu', 'rsu.id', '=', 'rsse.unit_id')
                        ->where('rsse.college_id', '=', $collegeId)
                        ->where('rsse.student_id', '=', $studentId)
                        ->where('rsse.course_id', '=', $courseId)
                        ->get(['rsu.id',
                            'rsu.unit_code',
                            'rsu.vet_unit_code',
                            'rsse.subject_attempt as attempt',
                            'rsu.unit_name',
                            'rsue.compentency',
                            'rsse.activity_start_date as start_date',
                            'rsse.activity_finish_date as finish_date',
                            'rsse.census_date']);
    }

    public function deleteStudentSubjectEmployment($studentId, $courseId) {
        return StudentSubjectEnrolment::where('college_id', '=', Auth::user()->college_id)
                        ->where('student_id', $studentId)
                        ->where('course_id', $courseId)
                        ->delete();
    }

    public function UpdateStudentSubjectEnrolmentFinalOutcomeV2($collegeId, $studentId, $subjectId, $semesterId, $term, $batch, $finalOutcome,$finish_date='',$unitId) {
        $objSubjectEnrolment = StudentSubjectEnrolment::from('rto_student_subject_enrolment as rsse')
                        ->where('rsse.student_id', '=', $studentId)
                        ->where('rsse.subject_id', '=', $subjectId)
                        ->where('rsse.unit_id', '=', $unitId)
                        ->where('rsse.semester_id', '=', $semesterId)
                        ->where('rsse.term', '=', $term)
                        ->where('rsse.batch', '=', $batch)
                        ->where('rsse.college_id', '=', $collegeId)
                        ->get(['rsse.id'])->toArray();

        $finishDate = (($finish_date != "") ? date('Y-m-d',strtotime($finish_date. '+1 day')):date('Y-m-d'));
        $last_assessment_approved_date = (($finish_date != "") ? date('Y-m-d',strtotime($finish_date)):date('Y-m-d'));

        foreach ($objSubjectEnrolment as $row) {
            $StudentSubjectEnrolment = StudentSubjectEnrolment::find($row['id']);

            if ($StudentSubjectEnrolment) {
                $StudentSubjectEnrolment->final_outcome = $finalOutcome;
                $StudentSubjectEnrolment->last_assessment_approved_date = $last_assessment_approved_date;
                // $StudentSubjectEnrolment->activity_finish_date = $finishDate;
                $StudentSubjectEnrolment->save();
            }

            // $objSubjectEnrolment = StudentSubjectEnrolment::from('rto_student_subject_enrolment as rsse')
            //         ->where('rsse.id', '=', value: $row['id'])
            //         ->update(['rsse.final_outcome' => $finalOutcome, 'rsse.last_assessment_approved_date'=>$last_assessment_approved_date,'rsse.activity_finish_date' => $finishDate]);

            $updateUnitCompentency = ['compentency' => $finalOutcome, 'study_to' => $finishDate];
            if($finalOutcome == 'C'){
               $updateUnitCompentency['competency_date'] = date('Y-m-d');
            } else {
                $updateUnitCompentency['competency_date'] = null;
            }
            $objSubjectEnrolment = StudentUnitEnrollment::where('student_subject_enrollment_id', $row['id'])->first();

            if ($objSubjectEnrolment) {
                $res  = StudentUnitEnrollment::find($objSubjectEnrolment->id);

                $res->compentency = $finalOutcome;
                $res->study_to = $finishDate;

                if($finalOutcome == 'C'){
                    $res->competency_date = date('Y-m-d');
                 } else {
                    $res->competency_date = null;
                 }
                 $res->save();

                // TODO:: GNG-4357 (Update course unit grade for moodle, when transfer result from galaxy)
                $newUnit = SubjectUnits::where('synced_for', $unitId)->first();
                if (!is_null($newUnit) && isset($newUnit->id)) {
                    dispatch_sync(new SyncStudentCourseUnitGradeReportFromMoodle($studentId, $newUnit->id));
                }
            }
        }

        return $objSubjectEnrolment;
    }
    public function UpdateStudentSubjectEnrolmentFinalOutcome($collegeId, $studentId, $subjectId, $semesterId, $term, $batch, $finalOutcome,$finish_date='') {
        $objSubjectEnrolment = StudentSubjectEnrolment::from('rto_student_subject_enrolment as rsse')
                        ->where('rsse.student_id', '=', $studentId)
                        ->where('rsse.subject_id', '=', $subjectId)
                        ->where('rsse.semester_id', '=', $semesterId)
                        ->where('rsse.term', '=', $term)
                        ->where('rsse.batch', '=', $batch)
                        ->where('rsse.college_id', '=', $collegeId)
                        ->get(['rsse.id'])->toArray();

        $finishDate = (($finish_date != "") ? date('Y-m-d',strtotime($finish_date. '+1 day')):date('Y-m-d'));
        $last_assessment_approved_date = (($finish_date != "") ? date('Y-m-d',strtotime($finish_date)):date('Y-m-d'));

        foreach ($objSubjectEnrolment as $row) {
            // $objSubjectEnrolment = StudentSubjectEnrolment::from('rto_student_subject_enrolment as rsse')
            //         ->where('rsse.id', '=', value: $row['id'])
            //         ->update(['rsse.final_outcome' => $finalOutcome, 'rsse.last_assessment_approved_date'=>$last_assessment_approved_date,'rsse.activity_finish_date' => $finishDate]);
                    $StudentSubjectEnrolment = StudentSubjectEnrolment::find($row['id']);

                    if ($StudentSubjectEnrolment) {
                        $StudentSubjectEnrolment->final_outcome = $finalOutcome;
                        $StudentSubjectEnrolment->last_assessment_approved_date = $last_assessment_approved_date;
                        $StudentSubjectEnrolment->activity_finish_date = $finishDate;
                        $StudentSubjectEnrolment->save();
                    }
                    
            $updateUnitCompentency = ['compentency' => $finalOutcome, 'study_to' => $finishDate];
            if($finalOutcome == 'C'){
               $updateUnitCompentency['competency_date'] = date('Y-m-d');
            } else {
                $updateUnitCompentency['competency_date'] = null;
            }
            // dd($row['id']);
           $objSubjectEnrolment = StudentUnitEnrollment::where('student_subject_enrollment_id', $row['id'])->first();

            if ($objSubjectEnrolment) {
                $res  = StudentUnitEnrollment::find($objSubjectEnrolment->id);

                $res->compentency = $finalOutcome;
                $res->study_to = $finishDate;

                if($finalOutcome == 'C'){
                    $res->competency_date = date('Y-m-d');
                 } else {
                    $res->competency_date = null;
                 }
                 $res->save();
            }


        }

        return $objSubjectEnrolment;
    }
    public function UpdateStudentFinalOutcomeFromVocational($collegeId, $studentId, $subjectId, $semesterId, $term, $batch, $finalOutcome) {
        $objSubjectEnrolment = StudentSubjectEnrolment::from('rto_student_subject_enrolment as rsse')
                        ->where('rsse.student_id', '=', $studentId)
                        ->where('rsse.subject_id', '=', $subjectId)
                        ->where('rsse.semester_id', '=', $semesterId)
                        ->where('rsse.term', '=', $term)
                        ->where('rsse.batch', '=', $batch)
                        ->where('rsse.college_id', '=', $collegeId)
                        ->get(['rsse.id'])->toArray();

        foreach ($objSubjectEnrolment as $row) {
            // $objSubjectEnrolment = StudentSubjectEnrolment::from('rto_student_subject_enrolment as rsse')
            //         ->where('rsse.id', '=', $row['id'])
            //         ->update(['rsse.final_outcome' => $finalOutcome]);
                    $objSubjectEnrolment = StudentSubjectEnrolment::find($row['id']);

                    if ($objSubjectEnrolment) {
                        $objSubjectEnrolment->final_outcome = $finalOutcome;
                        $objSubjectEnrolment->save();
                    }
            $updateUnitCompentency = ['compentency' => $finalOutcome];
            if($finalOutcome == 'C'){
               $updateUnitCompentency['competency_date'] = date('Y-m-d');
            } else {
                $updateUnitCompentency['competency_date'] = null;
            }
            // $objSubjectEnrolment = StudentUnitEnrollment::where('student_subject_enrollment_id', '=', $row['id'])
            //         ->update($updateUnitCompentency);
                    $objSubjectEnrolment = StudentUnitEnrollment::where('student_subject_enrollment_id', $row['id'])->first();

                    if ($objSubjectEnrolment) {
                        $res  = StudentUnitEnrollment::find($objSubjectEnrolment->id);

                        if($finalOutcome == 'C'){
                            $res->competency_date = date('Y-m-d');
                         } else {
                            $res->competency_date = null;
                         }
                         $res->save();
                    }
        }

        return $objSubjectEnrolment;
    }

    //update final_outcome after assessment confirmation
    public function updateStudentSubjectEnrolmentByActivityDate($collegeId, $enrollId, $activity_finish_date, $finalOutcome) {
        $objStudentEnrolment = StudentSubjectEnrolment::find($enrollId);

        $objStudentEnrolment->final_outcome =  $finalOutcome;
        $objStudentEnrolment->activity_finish_date = date('Y-m-d', strtotime($activity_finish_date));
        $objStudentEnrolment->updated_by = Auth::user()->id;
        $objStudentEnrolment->save();

        $updateUnitCompentency = ['compentency' => $finalOutcome];
        $objSubjectEnrolment = StudentUnitEnrollment::where('student_subject_enrollment_id', '=', $enrollId)
                    ->update($updateUnitCompentency);

    }

    public function lockResult($arrFormData){
        $isLockUnlock = ($arrFormData['action'] == 'lockresult')?'1':'0';


        foreach($arrFormData['student_id'] as $studentId){
            $unitIds = explode(',',$arrFormData['studentUpdatedUnitId'][$studentId]);

            foreach($unitIds as  $unitId){
                // Retrieve the records matching the criteria
                $enrollments = StudentSubjectEnrolment::where('batch', '=', $arrFormData['batch'])
                ->where('unit_id', '=', $unitId)
                ->where('subject_id', '=', $arrFormData['subject_id'])
                ->where('student_id', '=', $studentId)
                ->get();

                // Loop through each record, update, and save
                foreach ($enrollments as $enrollment) {
                    $enrollment->is_result_lock = $isLockUnlock;
                    $enrollment->updated_by = Auth::user()->id;
                    $enrollment->save(); // Save the changes to the database
                }
            }

        }


        return count($enrollments);
    }
}
