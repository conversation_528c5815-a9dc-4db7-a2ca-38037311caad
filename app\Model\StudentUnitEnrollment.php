<?php

namespace App\Model;

use App\Model\v2\StudentSubjectEnrolment;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Spatie\Activitylog\Contracts\Activity;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class StudentUnitEnrollment extends Model {
   use LogsActivity;

    protected $table = 'rto_student_unit_enrollment';

    protected $fillable = [
        'college_id',
        'student_subject_enrollment_id',
        'compentency',
        'unit_id',
        'schedule_hours',
        'delivery_mode',
        'tution_fee',
        'competency_date',
        'amount_paid',
        'comment',
        'attended_hour',
        'study_from',
        'study_to',
        'created_by',
        'updated_by'
    ];
    protected $logAttributes =  [
        'college_id',
        'student_subject_enrollment_id',
        'compentency',
        'unit_id',
        'schedule_hours',
        'delivery_mode',
        'tution_fee',
        'competency_date',
        'amount_paid',
        'comment',
        'attended_hour',
        'study_from',
        'study_to',
        'created_by',
        'updated_by'
    ];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logFillable()
            ->logOnlyDirty()
            ->setDescriptionForEvent(fn(string $eventName) => ":subject.galaxy_log_name Student Enrolled Unit Info has been {$eventName}");
    }

   public function tapActivity(Activity $activity, string $eventName)
   {
    $activity->subject_type = self::class;
    $activity->log_name = (new self)->getMorphClass() . '_' . $this->subjectenrollment->student_id .'_'. $this->subjectenrollment->course_id;
   }

   public function subjectenrollment()
    {
        return $this->hasOne(StudentSubjectEnrolment::class, 'id', 'student_subject_enrollment_id');
    }

    public function getGalaxyLogNameAttribute(): string
    {
        return implode("-", [@$this->subjectenrollment->student->generated_stud_id, @$this->subjectenrollment->course->course_code,@$this->subjectenrollment->unit->unit_code]);
    }
    public function saveUnitEnrollmentModule($collegeId, $request) {

        $enrollId = $request->input('enroll_id');
        $unitId = $request->input('unit_id');
        
        //$objUnitEnrollment = new StudentUnitEnrollment();
        $objUnitEnrollment = StudentUnitEnrollment::firstOrCreate(array('college_id' => $collegeId, 'student_subject_enrollment_id' => $enrollId, 'unit_id' => $unitId));
        $objUnitEnrollment->college_id = $collegeId;
      
        $objUnitEnrollment->student_subject_enrollment_id = $enrollId;
        $objUnitEnrollment->unit_id = $request->input('unit_id');
        $objUnitEnrollment->study_from = ($request->input('study_from') != '') ? date('Y-m-d',strtotime($request->input('study_from'))) : null;
        $objUnitEnrollment->study_to = ($request->input('study_to') != '') ? date('Y-m-d',strtotime($request->input('study_to'))) : null;
        $objUnitEnrollment->schedule_hours = ($request->input('schedule_hours') != '') ? $request->input('schedule_hours') : null;
        $objUnitEnrollment->delivery_mode = ($request->input('delivery_mode') != '') ? $request->input('delivery_mode') : null;
        $objUnitEnrollment->tution_fee = ($request->input('tution_fee') != '') ? $request->input('tution_fee') : null;
        $objUnitEnrollment->amount_paid = ($request->input('amount_paid') != '') ? $request->input('amount_paid') : null;
        $objUnitEnrollment->competency_date = ($request->input('competency_date') != '') ? date('Y-m-d',strtotime($request->input('competency_date'))) : null;
        $objUnitEnrollment->client_fees = ($request->input('client_fees') != '') ? $request->input('client_fees') : null;
        $objUnitEnrollment->compentency = ($request->input('compentency') != '') ? $request->input('compentency') : null;
        $objUnitEnrollment->attended_hour = ($request->input('attended_hour') != '') ? $request->input('attended_hour') : null;
        $objUnitEnrollment->sace_student_id = ($request->input('sace_student_id') != '') ? $request->input('sace_student_id') : null;
        $objUnitEnrollment->learner_unique_identifier = ($request->input('learner_unique_identifier') != '') ? $request->input('learner_unique_identifier') : null;
        $objUnitEnrollment->comment = ($request->input('comment') != '') ? $request->input('comment') : null;
   
        $objUnitEnrollment->created_by = Auth::user()->id;
        $objUnitEnrollment->updated_by = Auth::user()->id;
        $objUnitEnrollment->save();
    }
    
    public function studentSubjectBulkUnitEnrollment($collegeId, $insertData, $unitId ,$lastInsertedId){
        
         $objRtoUnitModule = new UnitModule();
         $unitaData = $objRtoUnitModule->getUnitDetailsData($collegeId, $unitId);
        $objUnitEnrollment = StudentUnitEnrollment::firstOrCreate(array('college_id' => $collegeId, 'student_subject_enrollment_id' => $lastInsertedId, 'unit_id' => $unitId));
        $objUnitEnrollment->college_id = $collegeId;
        $objUnitEnrollment->student_subject_enrollment_id = $lastInsertedId;
        $objUnitEnrollment->unit_id = $unitId;
        $objUnitEnrollment->compentency = $insertData['select_final_outcome'];
        $objUnitEnrollment->study_from = date('Y-m-d', strtotime($insertData['activity_start_date']));
        $objUnitEnrollment->study_to =date('Y-m-d', strtotime($insertData['activity_finish_date'])); 
        $objUnitEnrollment->schedule_hours = $unitaData[0]['nominal_hours'];
        $objUnitEnrollment->delivery_mode = $insertData['delivery_mode'];
        $objUnitEnrollment->tution_fee = $unitaData[0]['tution_fees'];
        $objUnitEnrollment->attended_hour = 0;
        $objUnitEnrollment->created_by = Auth::user()->id;
        $objUnitEnrollment->updated_by = Auth::user()->id;
        $objUnitEnrollment->save();
       
    }

    public function getStudentUnitEnrollment($collegeId, $enrollIdList){
        return StudentUnitEnrollment::where('college_id','=',$collegeId)
                                ->whereIn('student_subject_enrollment_id',$enrollIdList)
                                ->get();
    }
    
    public function updateStudentUnitEnrollment($unitEnrollId,$request){
        $objUpdateStudentEnroll = StudentUnitEnrollment::find($unitEnrollId);
        $objUpdateStudentEnroll->study_from = ($request->input('study_from') != '') ? date('Y-m-d',strtotime($request->input('study_from'))) : null;
        $objUpdateStudentEnroll->study_to = ($request->input('study_to') != '') ? date('Y-m-d',strtotime($request->input('study_to'))) : null;
        $objUpdateStudentEnroll->schedule_hours = ($request->input('schedule_hours') != '') ? $request->input('schedule_hours') : null;
        $objUpdateStudentEnroll->delivery_mode = ($request->input('delivery_mode') != '') ? $request->input('delivery_mode') : null;
        $objUpdateStudentEnroll->tution_fee = ($request->input('tution_fee') != '') ? $request->input('tution_fee') : null;
        $objUpdateStudentEnroll->amount_paid = ($request->input('amount_paid') != '') ? $request->input('amount_paid') : null;
        $objUpdateStudentEnroll->competency_date = ($request->input('competency_date') != '') ? date('Y-m-d',strtotime($request->input('competency_date'))) : null;
        $objUpdateStudentEnroll->client_fees = ($request->input('client_fees') != '') ? $request->input('client_fees') : null;
        $objUpdateStudentEnroll->compentency = ($request->input('compentency') != '') ? $request->input('compentency') : null;
        $objUpdateStudentEnroll->attended_hour = ($request->input('attended_hour') != '') ? $request->input('attended_hour') : null;
        $objUpdateStudentEnroll->sace_student_id = ($request->input('sace_student_id') != '') ? $request->input('sace_student_id') : null;
        $objUpdateStudentEnroll->learner_unique_identifier = ($request->input('learner_unique_identifier') != '') ? $request->input('learner_unique_identifier') : null;
        $objUpdateStudentEnroll->comment = ($request->input('comment') != '') ? $request->input('comment') : null;
   
        $objUpdateStudentEnroll->updated_by = Auth::user()->id;
        $objUpdateStudentEnroll->save();
    }
    
    public function deleteUnitEnrollment($collegeId, $unitEnrollId){
        
        $getEnrollSubjectId = StudentUnitEnrollment::where('college_id', '=', $collegeId)->where('id','=',$unitEnrollId)->get(['student_subject_enrollment_id']);
        $deleteUnitEnrollment = StudentUnitEnrollment::where('college_id','=',$collegeId)->where('id','=',$unitEnrollId)->delete();
        
        return $getEnrollSubjectId[0]->student_subject_enrollment_id;
    }
    
    public function deleteStudentUnitEnrollmentInfo($student_subject_enrollment_id){
        foreach($student_subject_enrollment_id as $row){
             StudentUnitEnrollment::where('college_id','=',Auth::user()->college_id)
                                 ->where('student_subject_enrollment_id',$row['id'])
                                ->delete();
        }
        return true;
    }
    
    public function saveUnitEnrollmentInfo($collegeId, $request){
        
        $enrollId = $request->input('enroll_id');
        $unitId = $request->input('unit_id');
        $userId = Auth::user()->id;
        
        //$objUnitEnrollment = new StudentUnitEnrollment();
        $objUnitEnrollment = StudentUnitEnrollment::firstOrCreate(array('college_id' => $collegeId, 
                                                                        'unit_id' => $unitId, 
                                                                        'student_subject_enrollment_id' => $enrollId));
        
        $objUnitEnrollment->college_id = $collegeId;
        $objUnitEnrollment->student_subject_enrollment_id = $enrollId;
        $objUnitEnrollment->unit_id = $unitId;
        
        $objUnitEnrollment->study_from = ($request->input('start_date') != '') ? date('Y-m-d',strtotime($request->input('start_date'))) : null;
        $objUnitEnrollment->study_to = ($request->input('finish_date') != '') ? date('Y-m-d',strtotime($request->input('finish_date'))) : null;
        $objUnitEnrollment->schedule_hours = ($request->input('schedule_hours') != '') ? $request->input('schedule_hours') : null;
        $objUnitEnrollment->attended_hour = ($request->input('attended_hour') != '') ? $request->input('attended_hour') : null;
        $objUnitEnrollment->tution_fee = ($request->input('tution_fee') != '') ? $request->input('tution_fee') : null;
        $objUnitEnrollment->delivery_mode = ($request->input('delivery_mode') != '') ? $request->input('delivery_mode') : null;
       // $objUnitEnrollment->compentency = ($request->input('competency') != '') ? $request->input('competency') : null;
        
        $objUnitEnrollment->created_by = $userId;
        $objUnitEnrollment->updated_by = $userId;
        $objUnitEnrollment->save();
    }
    public function updateUnitEnrollmentInfo($collegeId, $request){

        $enrollId = $request->input('enroll_id');
        $unitId = $request->input('unit_id');
        $userId = Auth::user()->id;
        
        //$objUnitEnrollment = new StudentUnitEnrollment();
        $objUnitEnrollment = StudentUnitEnrollment::firstOrCreate(array('student_subject_enrollment_id' => $enrollId));
        
        $objUnitEnrollment->college_id = $collegeId;
        $objUnitEnrollment->student_subject_enrollment_id = $enrollId;
        $objUnitEnrollment->unit_id = $unitId;
        
        $objUnitEnrollment->study_from = ($request->input('start_date') != '') ? date('Y-m-d',strtotime($request->input('start_date'))) : null;
        $objUnitEnrollment->study_to = ($request->input('finish_date') != '') ? date('Y-m-d',strtotime($request->input('finish_date'))) : null;
        $objUnitEnrollment->schedule_hours = ($request->input('schedule_hours') != '') ? $request->input('schedule_hours') : null;
        $objUnitEnrollment->attended_hour = ($request->input('attended_hour') != '') ? $request->input('attended_hour') : null;
        $objUnitEnrollment->tution_fee = ($request->input('tution_fee') != '') ? $request->input('tution_fee') : null;
        $objUnitEnrollment->delivery_mode = ($request->input('delivery_mode') != '') ? $request->input('delivery_mode') : null;
        $objUnitEnrollment->compentency = ($request->input('competency') != '') ? $request->input('competency') : null;
        
        $objUnitEnrollment->created_by = $userId;
        $objUnitEnrollment->updated_by = $userId;
        $objUnitEnrollment->save();
    }
    public function studentUnitUpadte($collegeId, $arrStudentSubjectEnrolment,$request) {
     
         $objSubjectEnrolment = StudentUnitEnrollment::whereIn('student_subject_enrollment_id',$arrStudentSubjectEnrolment)
                               ->where('study_to', '>' ,date('Y-m-d', strtotime($request->input('finish_date'))))
                               ->update(['study_to' => date('Y-m-d', strtotime($request->input('finish_date'))),
                              ]);
    }
    
    public function saveSubjectUnitEnrollmentCompentency($studentEnrollmentId,$unitId,$compentency){
        $objSubjectEnrolment = StudentUnitEnrollment::where('student_subject_enrollment_id',$studentEnrollmentId)
                               ->where('unit_id', '=' ,$unitId)
                               ->update(['compentency' => $compentency]);
    }
    
    public function updateStudentUnitEnrolmentByStudyTo($collegeId,$id,$study_to,$compentency){
     
         $objSubjectEnrolment = StudentUnitEnrollment::where('college_id', '=', $collegeId)
                              ->where('id',$id)
                               ->update(['study_to' => date('Y-m-d', strtotime($study_to)),
                                         'compentency' => $compentency]);
    }
}
