<?php

namespace App\Model;

use App\Model\v2\Student;
use App\Model\v2\StudentCourses;
use App\Traits\AvetMissTrait;
use App\Traits\CommonTrait;
use App\Traits\ResponseTrait;
use Domains\Xero\Facades\Xero;
use Helpers;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Validator;

class Students extends Model
{
    use AvetMissTrait;
    use CommonTrait;
    use ResponseTrait;

    //
    protected $table = 'rto_students';

    public function approveDeapproveStaff($id)
    {

        $arrStaff = Students::select('is_applicant', 'is_offered')
            ->where('id', $id)
            ->get()
            ->toArray();

        $is_applicant = $arrStaff[0]['is_applicant'];
        $is_offered = $arrStaff[0]['is_offered'];

        if ($is_applicant == 1) {
            $temp_is_applicant = 0;
        } else {
            $temp_is_applicant = $is_applicant;
        }

        if ($is_offered == 0) {
            $temp_is_offered = 1;
        } else {
            $temp_is_offered = $is_offered;
        }

        $objStudent = Student::find($id);
        $objStudent->is_applicant = $temp_is_applicant;
        $objStudent->is_offered = $temp_is_offered;
        $objStudent->save();

        return true;

    }

    public function updateCurrentSituation($collegeId, $studentId, $currentSituation)
    {
        // update data if exixts with same id -> rto_students
        $studentsInfo = Students::find($studentId);
        $studentsInfo->current_situation = $currentSituation;
        $studentsInfo->save();
    }

    public function updateStudentInfo($studentId, $collegeId, $request, $loginUserID)
    {
        // update data if exixts with same id -> rto_students
        $studentsInfo = Students::find($studentId);
        $studentsInfo->college_id = $collegeId;
        $studentsInfo->USI = ($request->input('USI') != '') ? $request->input('USI') : null;
        $studentsInfo->student_type = ($request->input('student_type') != '') ? $request->input('student_type') : null;
        $studentsInfo->name_title = ($request->input('name_title') != '') ? $request->input('name_title') : null;
        $studentsInfo->first_name = ($request->input('first_name') != '') ? $request->input('first_name') : null;
        $studentsInfo->middel_name = ($request->input('middel_name') != '') ? $request->input('middel_name') : null;
        $studentsInfo->family_name = ($request->input('family_name') != '') ? $request->input('family_name') : null;
        $studentsInfo->nickname = ($request->input('nickname') != '') ? $request->input('nickname') : null;
        $studentsInfo->gender = ($request->input('gender') != '') ? $request->input('gender') : null;
        $studentsInfo->DOB = ($request->input('DOB') != '') ? date('Y-m-d', strtotime($request->input('DOB'))) : null;
        $studentsInfo->email = ($request->input('email') != '') ? $request->input('email') : null;
        $studentsInfo->optional_email = ($request->input('optional_email') != '') ? $request->input('optional_email') : null;
        $studentsInfo->birthplace = ($request->input('birthplace') != '') ? $request->input('birthplace') : null;
        $studentsInfo->birth_country = (! empty($request->input('birth_country'))) ? $request->input('birth_country') : null;
        $studentsInfo->nationality = ($request->input('nationality') != '') ? $request->input('nationality') : null;
        $studentsInfo->passport_no = ($request->input('passport_no') != '') ? $request->input('passport_no') : null;
        $studentsInfo->passport_expiry = ($request->input('passport_expiry') != '' && $request->input('student_type') != 'Domestic') ? date('Y-m-d', strtotime($request->input('passport_expiry'))) : null;
        $studentsInfo->visa_status = ($request->input('visa_status') != '') ? $request->input('visa_status') : null;
        $studentsInfo->visa_number = ($request->input('visa_number') != '') ? $request->input('visa_number') : null;
        $studentsInfo->visa_expiry_date = (! empty($request->input('visa_expiry_date')) && $request->input('student_type') != 'Domestic') ? date('Y-m-d', strtotime($request->input('visa_expiry_date'))) : null;

        // $studentsInfo->current_country = ($request->input('current_country') != '') ? $request->input('current_country') : null;
        // $studentsInfo->current_building_name = ($request->input('current_building_name') != '') ? $request->input('current_building_name') : null;
        // $studentsInfo->current_unit_detail = ($request->input('current_unit_detail') != '') ? $request->input('current_unit_detail') : null;
        // $studentsInfo->current_street_no = ($request->input('current_street_no') != '') ? $request->input('current_street_no') : null;
        // $studentsInfo->current_street_name = ($request->input('current_street_name') != '') ? $request->input('current_street_name') : null;
        // $studentsInfo->current_city = ($request->input('current_city') != '') ? $request->input('current_city') : null;
        // $studentsInfo->current_state = ($request->input('current_state') != '') ? $request->input('current_state') : null;
        // $studentsInfo->current_postcode = ($request->input('current_postcode') != '') ? $request->input('current_postcode') : null;
        $studentsInfo->current_home_phone = ($request->input('current_home_phone') != '') ? $request->input('current_home_phone') : null;
        $studentsInfo->current_work_phone = ($request->input('current_work_phone') != '') ? $request->input('current_work_phone') : null;
        $studentsInfo->current_mobile_phone = ($request->input('current_mobile_phone') != '') ? $request->input('current_mobile_phone') : null;
        // $studentsInfo->is_postal_address = ($request->input('is_postal_address') != '') ? $request->input('is_postal_address') : null;
        // $studentsInfo->is_parmanent_address = ($request->input('is_parmanent_address') != '') ? $request->input('is_parmanent_address') : null;

        // /* Update Postal Address Detail */
        // $studentsInfo->postal_country = ($request->input('postal_country') != '') ? $request->input('postal_country') : null;
        // $studentsInfo->postal_building_name = ($request->input('postal_building_name') != '') ? $request->input('postal_building_name') : null;
        // $studentsInfo->postal_unit_detail = ($request->input('postal_unit_detail') != '') ? $request->input('postal_unit_detail') : null;
        // $studentsInfo->postal_street_no = ($request->input('postal_street_no') != '') ? $request->input('postal_street_no') : null;
        // $studentsInfo->postal_street_name = ($request->input('postal_street_name') != '') ? $request->input('postal_street_name') : null;
        // $studentsInfo->postal_po_box = ($request->input('postal_po_box') != '') ? $request->input('postal_po_box') : null;
        // $studentsInfo->postal_city = ($request->input('postal_city') != '') ? $request->input('postal_city') : null;
        // $studentsInfo->postal_state = ($request->input('postal_state') != '') ? $request->input('postal_state') : null;
        // $studentsInfo->postal_postcode = ($request->input('postal_postcode') != '') ? $request->input('postal_postcode') : null;
        // $studentsInfo->postal_home_phone = ($request->input('postal_home_phone') != '') ? $request->input('postal_home_phone') : null;
        // $studentsInfo->postal_work_phone = ($request->input('postal_work_phone') != '') ? $request->input('postal_work_phone') : null;
        // $studentsInfo->postal_mobile_phone = ($request->input('postal_mobile_phone') != '') ? $request->input('postal_mobile_phone') : null;
        // /* Update Overseas/Permanent Address Detail */

        // $studentsInfo->permanent_country = ($request->input('permanent_country') != '') ? $request->input('permanent_country') : null;
        // $studentsInfo->permanent_street_name = ($request->input('permanent_street_name') != '') ? $request->input('permanent_street_name') : null;
        // $studentsInfo->permanent_city = ($request->input('permanent_city') != '') ? $request->input('permanent_city') : null;
        // $studentsInfo->permanent_state = ($request->input('permanent_state') != '') ? $request->input('permanent_state') : null;
        // $studentsInfo->permanent_postcode = ($request->input('permanent_postcode') != '') ? $request->input('permanent_postcode') : null;
        // $studentsInfo->permanent_home_phone = ($request->input('permanent_home_phone') != '') ? $request->input('permanent_home_phone') : null;
        // $studentsInfo->permanent_work_phone = ($request->input('permanent_work_phone') != '') ? $request->input('permanent_work_phone') : null;
        // $studentsInfo->permanent_mobile_phone = ($request->input('permanent_mobile_phone') != '') ? $request->input('permanent_mobile_phone') : null;

        $studentsInfo->status = 1;
        $studentsInfo->updated_by = $loginUserID;
        $studentsInfo->save();
    }

    public function updateStudentAddressInfo($studentId, $collegeId, $request, $loginUserID)
    {
        // update data if exixts with same id -> rto_students
        $studentsInfo = Students::find($studentId);

        $studentsInfo->current_country = ($request->input('current_country') != '') ? $request->input('current_country') : null;
        $studentsInfo->current_building_name = ($request->input('current_building_name') != '') ? $request->input('current_building_name') : null;
        $studentsInfo->current_unit_detail = ($request->input('current_unit_detail') != '') ? $request->input('current_unit_detail') : null;
        $studentsInfo->current_street_no = ($request->input('current_street_no') != '') ? $request->input('current_street_no') : null;
        $studentsInfo->current_street_name = ($request->input('current_street_name') != '') ? $request->input('current_street_name') : null;
        $studentsInfo->current_city = ($request->input('current_city') != '') ? $request->input('current_city') : null;
        $studentsInfo->current_state = ($request->input('current_state') != '') ? $request->input('current_state') : null;
        $studentsInfo->current_postcode = ($request->input('current_postcode') != '') ? $request->input('current_postcode') : null;

        $studentsInfo->is_postal_address = ($request->input('is_postal_address') != '') ? $request->input('is_postal_address') : null;
        $studentsInfo->is_parmanent_address = ($request->input('is_parmanent_address') != '') ? $request->input('is_parmanent_address') : null;

        /* Update Postal Address Detail */
        $studentsInfo->postal_country = ($request->input('postal_country') != '') ? $request->input('postal_country') : null;
        $studentsInfo->postal_building_name = ($request->input('postal_building_name') != '') ? $request->input('postal_building_name') : null;
        $studentsInfo->postal_unit_detail = ($request->input('postal_unit_detail') != '') ? $request->input('postal_unit_detail') : null;
        $studentsInfo->postal_street_no = ($request->input('postal_street_no') != '') ? $request->input('postal_street_no') : null;
        $studentsInfo->postal_street_name = ($request->input('postal_street_name') != '') ? $request->input('postal_street_name') : null;
        $studentsInfo->postal_po_box = ($request->input('postal_po_box') != '') ? $request->input('postal_po_box') : null;
        $studentsInfo->postal_city = ($request->input('postal_city') != '') ? $request->input('postal_city') : null;
        $studentsInfo->postal_state = ($request->input('postal_state') != '') ? $request->input('postal_state') : null;
        $studentsInfo->postal_postcode = ($request->input('postal_postcode') != '') ? $request->input('postal_postcode') : null;
        $studentsInfo->postal_home_phone = ($request->input('postal_home_phone') != '') ? $request->input('postal_home_phone') : null;
        $studentsInfo->postal_work_phone = ($request->input('postal_work_phone') != '') ? $request->input('postal_work_phone') : null;
        $studentsInfo->postal_mobile_phone = ($request->input('postal_mobile_phone') != '') ? $request->input('postal_mobile_phone') : null;
        /* Update Overseas/Permanent Address Detail */

        $studentsInfo->permanent_country = ($request->input('permanent_country') != '') ? $request->input('permanent_country') : null;
        $studentsInfo->permanent_street_name = ($request->input('permanent_street_name') != '') ? $request->input('permanent_street_name') : null;
        $studentsInfo->permanent_city = ($request->input('permanent_city') != '') ? $request->input('permanent_city') : null;
        $studentsInfo->permanent_state = ($request->input('permanent_state') != '') ? $request->input('permanent_state') : null;
        $studentsInfo->permanent_postcode = ($request->input('permanent_postcode') != '') ? $request->input('permanent_postcode') : null;
        $studentsInfo->permanent_home_phone = ($request->input('permanent_home_phone') != '') ? $request->input('permanent_home_phone') : null;
        $studentsInfo->permanent_work_phone = ($request->input('permanent_work_phone') != '') ? $request->input('permanent_work_phone') : null;
        $studentsInfo->permanent_mobile_phone = ($request->input('permanent_mobile_phone') != '') ? $request->input('permanent_mobile_phone') : null;

        $studentsInfo->status = 1;
        $studentsInfo->updated_by = $loginUserID;
        $studentsInfo->save();

        return true;
    }

    public function saveStudentInfo($collegeId, $request, $applicationReferenceId, $userId)
    {
        $studentsInfo = new Students;
        $studentsInfo->college_id = $collegeId;
        $studentsInfo->application_reference_id = $applicationReferenceId;
        $studentsInfo->USI = (empty($request->input('USI'))) ? null : $request->input('USI');
        $studentsInfo->student_type = ($request->input('student_type') != '') ? $request->input('student_type') : null;
        $studentsInfo->name_title = ($request->input('name_title') != '') ? $request->input('name_title') : null;
        $studentsInfo->first_name = ($request->input('first_name') != '') ? $request->input('first_name') : null;
        $studentsInfo->middel_name = ($request->input('middel_name') != '') ? $request->input('middel_name') : null;
        $studentsInfo->family_name = ($request->input('family_name') != '') ? $request->input('family_name') : null;
        $studentsInfo->nickname = ($request->input('nickname') != '') ? $request->input('nickname') : null;
        $studentsInfo->gender = ($request->input('gender') != '') ? $request->input('gender') : null;
        $studentsInfo->DOB = ($request->input('DOB') != '') ? date('Y-m-d', strtotime($request->input('DOB'))) : null;
        $studentsInfo->email = ($request->input('email') != '') ? $request->input('email') : null;
        $studentsInfo->optional_email = ($request->input('optional_email') != '') ? $request->input('optional_email') : null;
        $studentsInfo->birthplace = ($request->input('birthplace') != '') ? $request->input('birthplace') : null;
        $studentsInfo->birth_country = (! empty($request->input('birth_country'))) ? $request->input('birth_country') : null;
        $studentsInfo->nationality = ($request->input('nationality') != '') ? $request->input('nationality') : null;
        $studentsInfo->passport_no = ($request->input('passport_no') != '') ? $request->input('passport_no') : null;
        $studentsInfo->passport_expiry = ($request->input('passport_expiry') != '' && $request->input('student_type') != 'Domestic') ? date('Y-m-d', strtotime($request->input('passport_expiry'))) : null;
        $studentsInfo->visa_status = ($request->input('visa_status') != '') ? $request->input('visa_status') : null;
        $studentsInfo->visa_number = ($request->input('visa_number') != '') ? $request->input('visa_number') : null;
        $studentsInfo->visa_expiry_date = (! empty($request->input('visa_expiry_date')) && $request->input('student_type') != 'Domestic') ? date('Y-m-d', strtotime($request->input('visa_expiry_date'))) : null;

        $studentsInfo->current_country = ($request->input('current_country') != '') ? $request->input('current_country') : null;
        $studentsInfo->current_building_name = ($request->input('current_building_name') != '') ? $request->input('current_building_name') : null;
        $studentsInfo->current_unit_detail = ($request->input('current_unit_detail') != '') ? $request->input('current_unit_detail') : null;
        $studentsInfo->current_street_no = ($request->input('current_street_no') != '') ? $request->input('current_street_no') : null;
        $studentsInfo->current_street_name = ($request->input('current_street_name') != '') ? $request->input('current_street_name') : null;
        $studentsInfo->current_city = ($request->input('current_city') != '') ? $request->input('current_city') : null;
        $studentsInfo->current_state = ($request->input('current_state') != '') ? $request->input('current_state') : null;
        $studentsInfo->current_postcode = ($request->input('current_postcode') != '') ? $request->input('current_postcode') : null;
        $studentsInfo->current_home_phone = ($request->input('current_home_phone') != '') ? $request->input('current_home_phone') : null;
        $studentsInfo->current_work_phone = ($request->input('current_work_phone') != '') ? $request->input('current_work_phone') : null;
        $studentsInfo->current_mobile_phone = ($request->input('current_mobile_phone') != '') ? $request->input('current_mobile_phone') : null;
        $studentsInfo->is_postal_address = ($request->input('is_postal_address') != '') ? $request->input('is_postal_address') : null;
        $studentsInfo->is_parmanent_address = ($request->input('is_parmanent_address') != '') ? $request->input('is_parmanent_address') : null;

        $studentsInfo->postal_country = ($request->input('postal_country') != '') ? $request->input('postal_country') : null;
        $studentsInfo->postal_building_name = ($request->input('postal_building_name') != '') ? $request->input('postal_building_name') : null;
        $studentsInfo->postal_unit_detail = ($request->input('postal_unit_detail') != '') ? $request->input('postal_unit_detail') : null;
        $studentsInfo->postal_street_no = ($request->input('postal_street_no') != '') ? $request->input('postal_street_no') : null;
        $studentsInfo->postal_street_name = ($request->input('postal_street_name') != '') ? $request->input('postal_street_name') : null;
        $studentsInfo->postal_po_box = ($request->input('postal_po_box') != '') ? $request->input('postal_po_box') : null;
        $studentsInfo->postal_city = ($request->input('postal_city') != '') ? $request->input('postal_city') : null;
        $studentsInfo->postal_state = ($request->input('postal_state') != '') ? $request->input('postal_state') : null;
        $studentsInfo->postal_postcode = ($request->input('postal_postcode') != '') ? $request->input('postal_postcode') : null;
        $studentsInfo->postal_home_phone = ($request->input('postal_home_phone') != '') ? $request->input('postal_home_phone') : null;
        $studentsInfo->postal_work_phone = ($request->input('postal_work_phone') != '') ? $request->input('postal_work_phone') : null;
        $studentsInfo->postal_mobile_phone = ($request->input('postal_mobile_phone') != '') ? $request->input('postal_mobile_phone') : null;

        $studentsInfo->permanent_country = ($request->input('permanent_country') != '') ? $request->input('permanent_country') : null;
        $studentsInfo->permanent_street_name = ($request->input('permanent_street_name') != '') ? $request->input('permanent_street_name') : null;
        $studentsInfo->permanent_city = ($request->input('permanent_city') != '') ? $request->input('permanent_city') : null;
        $studentsInfo->permanent_state = ($request->input('permanent_state') != '') ? $request->input('permanent_state') : null;
        $studentsInfo->permanent_postcode = ($request->input('permanent_postcode') != '') ? $request->input('permanent_postcode') : null;
        $studentsInfo->permanent_home_phone = ($request->input('permanent_home_phone') != '') ? $request->input('permanent_home_phone') : null;
        $studentsInfo->permanent_work_phone = ($request->input('permanent_work_phone') != '') ? $request->input('permanent_work_phone') : null;
        $studentsInfo->permanent_mobile_phone = ($request->input('permanent_mobile_phone') != '') ? $request->input('permanent_mobile_phone') : null;

        $studentsInfo->current_situation = 1;
        $studentsInfo->status = 1;
        $studentsInfo->is_applicant = 1;
        $studentsInfo->is_offered = 0;
        $studentsInfo->is_student = 0;

        $studentsInfo->created_by = $userId;
        $studentsInfo->updated_by = $userId;
        $studentsInfo->save();

        // insert last id into student_details table
        $studentDetails = new StudentDetails;
        $studentDetails->student_id = $studentsInfo->id;
        $studentDetails->created_by = $userId;
        $studentDetails->updated_by = $userId;
        $studentDetails->save();

        return $studentsInfo->id;
    }

    public function saveUSIInfo($collegeId, $userId, $studentId, $request)
    {

        $identityArr = $request->input('identity');
        //        echo $request->input('usi_identity');
        //        print_r($identityArr);
        //        EXIT;
        $studentsInfo = Students::find($studentId);
        $studentsInfo->college_id = $collegeId;
        $studentsInfo->is_apply_usi = ($request->input('is_apply_usi') == 1) ? 'YES' : 'NO';
        if (! empty($identityArr)) {
            $studentsInfo->usi_identity = (count($identityArr) > 0) ? implode(',', $identityArr) : '';
        }
        $studentsInfo->read_and_agree = ($request->input('is_apply_usi') == 1) ? (($request->input('read_and_agree') == 1) ? 'YES' : 'NO') : 'NO';
        $studentsInfo->city_of_birth = ($request->input('is_apply_usi') == 1) ? ($request->input('city_of_birth')) : null;

        if ($request->input('is_apply_usi') == 1) {

            $file = $request->file('usi_file');
            if (! empty($file)) {

                // $destinationPath = Config::get('constants.CollegeMarterials.DocumentsPath');
                $filePath = Config::get('constants.uploadFilePath.USIFile');
                $destinationPath = Helpers::changeRootPath($filePath, $studentId);
                $time = time();
                $originalName = $file->getClientOriginalName();
                $filenm = hashFileName($originalName);
                $fileName = $this->removeSpecialCharacter($filenm);
                $file_size = $file->getSize();
                $fileSize = ceil($file_size / 1024);
                $upload_success = $file->move($destinationPath['default'], $fileName);

                if ($upload_success) {
                    $oldfile = $destinationPath['default'].$studentsInfo->usi_file;
                    if (file_exists($oldfile) && ! empty($studentsInfo->usi_file)) {
                        unlink($oldfile);
                    }
                    $studentsInfo->usi_file = $fileName;
                    $studentsInfo->file_path = $destinationPath['view'];
                    $studentsInfo->original_name = $originalName;
                    $studentsInfo->file_size = $fileSize;
                }
            }
        }

        $studentsInfo->created_by = $userId;
        $studentsInfo->updated_by = $userId;

        if ($studentsInfo->save()) {
            return true;
        }
    }

    public function getStudents($studentId)
    {
        return Students::find($studentId);
    }

    public function getStudentDetail($studentId)
    {

        $arrRoleType = Config::get('constants.arrRoleType');
        $studentRoleType = array_search('Student', $arrRoleType);

        return Students::join('rto_colleges', 'rto_colleges.id', '=', 'rto_students.college_id')
            ->join('rto_student_details as rsd', 'rsd.student_id', '=', 'rto_students.id')
            ->leftjoin('rto_users', function ($join) use ($studentRoleType) {
                $join->on('rto_users.username', '=', 'rto_students.generated_stud_id');
                $join->on('rto_users.role_id', '=', DB::raw($studentRoleType));
            })
            ->leftjoin('rto_student_courses as rsc', 'rto_students.id', '=', 'rsc.student_id')
            ->leftjoin('rto_agents', 'rto_agents.id', '=', 'rsc.agent_id')
            ->leftjoin('rto_country as sc', 'sc.id', '=', 'rto_students.current_country')
            ->leftjoin('rto_country as ac', 'ac.id', '=', 'rto_agents.office_country')
            ->leftjoin('rto_setup_section as rss1', 'rss1.id', '=', 'rto_students.visa_status')
            ->leftjoin('rto_setup_section as rss2', 'rss2.id', '=', 'rsd.EPL_test_name')
            ->where('rto_students.id', '=', $studentId)
            ->select('rto_colleges.college_logo', 'rto_students.*', 'rto_users.username', 'rsd.picup_fee', 'rsd.service_fee', 'rsd.OSHC_fee', 'rsd.account_manager_id', 'rsd.EPL_overall_score', 'rto_agents.agency_name', 'rto_agents.office_address as agent_address', 'rto_agents.office_city as agent_city', 'rto_agents.office_state as agent_state', 'rto_agents.office_postcode as agent_postcode', 'ac.name as agent_country', 'sc.nationality', 'sc.nationality as stud_nationality', 'sc.name as country_name', 'rss1.value as visaStatus', 'rss2.value as EPL_test_name')
            ->get();
    }

    public function getReferenceId()
    {
        $applicationReferenceId = mt_rand(*********, *********);

        // check application reference id if exits then generate new
        $appNewrefenceID = Students::where('application_reference_id', '=', $applicationReferenceId)->get(['application_reference_id']);
        if ($appNewrefenceID->count() > 0) {
            $newApprefId = new Students;

            return $newApprefId->getReferenceId();
        } else {
            return $applicationReferenceId;
        }
    }

    public function getExistsReferenceId($studentId, $collegeId)
    {
        // $collegeId = Auth::user()->college_id;
        return Students::where('college_id', '=', $collegeId)
            ->where('id', '=', $studentId)
            ->select('id', 'college_id', 'application_reference_id', 'email', 'generated_stud_id', 'name_title', 'first_name', 'middel_name', 'student_type', 'generated_stud_id', DB::raw('concat(name_title, first_name, " ", family_name) as fullName'))
            ->get();

        //                        ->get(['id', 'college_id',
        //                            'application_reference_id',
        //                            'email',
        //                            'generated_stud_id',
        //                            'name_title',
        //                            'first_name',
        //                            'middel_name',
        //                            'student_type',
        //                            'generated_stud_id']);
    }

    public function getExistsReferenceIdV2($studentId, $collegeId)
    {
        // $collegeId = Auth::user()->college_id;
        //        ->leftJoin('rto_student_courses as rsc', function($join) {
        //                $join->on('rsc.student_id', '=', 'rsse.student_id');
        //                $join->on('rsc.course_id', '=', 'rsse.course_id');
        //        }
        return Students::from('rto_students as rs')
            ->leftjoin('rto_country as permanentCountry', 'permanentCountry.id', '=', 'rs.current_country')
            ->leftjoin('rto_country as postalCountry', 'postalCountry.id', '=', 'rs.postal_country')
            ->leftjoin('rto_country as overseasCountry', 'overseasCountry.id', '=', 'rs.permanent_country')
            ->leftjoin('rto_country as birthCountry', 'birthCountry.id', '=', 'rs.birth_country')
            ->leftjoin('rto_student_details as rsd', 'rsd.student_id', '=', 'rs.id')
            ->leftjoin('rto_visa_status as rvs', 'rvs.id', '=', 'rs.visa_status')
                        // ->leftjoin('rto_student_wet_fee_help as rsvfh', 'rsvfh.student_id', '=', 'rs.id')
            ->where('rs.college_id', '=', $collegeId)
            ->where('rs.id', '=', $studentId)
            ->get(['rs.*',
                'permanentCountry.name as countryName',
                'postalCountry.name as postalCountryName',
                'birthCountry.name as birthCountryName',
                'birthCountry.absvalue as absValue',
                'rsd.main_lang',
                'rsd.disability',
                'rsd.area_of_disability',
                'rsd.highest_completed_school_level',
                'rsd.completed_school_year',
                'rvs.name as visaName',
                'overseasCountry.name as overseasCountryName',
                // 'rsvfh.chessan_number'
            ]);
    }

    //    public function getOfferManageStudentData($studentId) {
    //        $collegeId = Auth::user()->college_id;
    //        return Students::leftjoin('rto_student_courses', 'rto_students.id', '=', 'rto_student_courses.student_id')
    //                        ->where('rto_students.college_id', '=', $collegeId)
    //                        ->where('rto_students.id', '=', $studentId)
    //                        ->get(['rto_students.*','rto_student_courses.course_id'])
    //                         ->toarray();
    //    }

    public function getOfferedStatus($collegeId, $studentIdOffer)
    {
        return Students::where('college_id', '=', $collegeId)->where('id', '=', $studentIdOffer)->get(['is_offered', 'is_student']);
    }

    public function getStudentDetails($studentId, $studentCourseId = '')
    {

        $objstudentDetails = Students::leftjoin('rto_student_details as rsd', 'rto_students.id', '=', 'rsd.student_id')
            ->leftjoin('rto_student_education as rst', 'rto_students.id', '=', 'rst.student_id')
            ->leftjoin('rto_country as country', 'country.id', '=', 'rto_students.nationality')
            ->leftjoin('rto_country as birthcountry', 'birthcountry.id', '=', 'rto_students.birth_country')
            ->leftjoin('rto_english_test as ret', 'rsd.EPL_test_name', '=', 'ret.id')
            ->leftjoin('rto_colleges', 'rto_colleges.id', '=', 'rto_students.college_id')
            ->leftjoin('rto_employment_status as res', 'rsd.current_employment_status', '=', 'res.id')
            ->leftjoin('rto_student_courses as rsc', 'rto_students.id', '=', 'rsc.student_id')
            ->leftjoin('rto_agents as ra', 'ra.id', '=', 'rsc.agent_id')
            ->leftjoin('rto_campus as rc', 'rc.id', '=', 'rsc.campus_id')
            ->leftjoin('rto_courses as cour', 'cour.id', '=', 'rsc.course_id')
            ->leftjoin('rto_setup_section as rvc', 'rvc.id', '=', 'rto_students.visa_status')
            ->leftjoin('rto_country', 'rto_country.id', '=', 'rto_students.current_country')
            ->select('rto_students.*', 'rsd.*', 'rst.*', 'ret.name as english_name', 'rsc.issued_date as issued_date', 'cour.course_name as course_name', 'rsc.start_date as start_date', 'rsc.finish_date as finish_date', 'rsc.enroll_fee as enroll_fee', 'rsc.course_id as course_id', 'rsc.course_fee as course_fee', 'rsc.course_upfront_fee as course_upfront_fee', 'rsc.course_material_fee as course_material_fee', 'rsc.offer_id as offer_id', 'ra.agency_name', 'rc.name as campus_name', 'rvc.value as visa_type', 'rto_country.nationality as stud_nationality', 'rto_country.name as country_name', 'rto_colleges.college_name', 'country.nationality as nationality_of_student', 'birthcountry.name as country_of_birth')
            ->where('rto_students.id', '=', $studentId);
        if ($studentCourseId != '') {
            $objstudentDetails->where('rsc.id', '=', $studentCourseId);
        }
        $studentDetails = $objstudentDetails->get();
        $studentDetails[0]->id = $studentId;

        return $studentDetails;
    }

    public function getStudentDetailsV2($studentId, $courseId)
    {

        return Students::leftjoin('rto_student_details as rsd', 'rto_students.id', '=', 'rsd.student_id')
            ->leftjoin('rto_country as country', 'country.id', '=', 'rto_students.nationality')
            ->leftjoin('rto_country as birthcountry', 'birthcountry.id', '=', 'rto_students.birth_country')
            ->leftjoin('rto_english_test as ret', 'rsd.EPL_test_name', '=', 'ret.id')
            ->leftjoin('rto_colleges', 'rto_colleges.id', '=', 'rto_students.college_id')
            ->leftjoin('rto_employment_status as res', 'rsd.current_employment_status', '=', 'res.id')
            ->leftjoin('rto_student_courses as rsc', 'rto_students.id', '=', 'rsc.student_id')
            ->leftjoin('rto_agents as ra', 'ra.id', '=', 'rsc.agent_id')
            ->leftjoin('rto_campus as rc', 'rc.id', '=', 'rsc.campus_id')
            ->leftjoin('rto_courses as cour', 'cour.id', '=', 'rsc.course_id')
            ->leftjoin('rto_setup_section as rvc', 'rvc.id', '=', 'rto_students.visa_status')
            ->leftjoin('rto_country', 'rto_country.id', '=', 'rto_students.current_country')
            ->select('rto_students.*', 'rsd.*', 'ret.name as english_name', 'rsc.issued_date as issued_date', 'cour.course_name as course_name', 'rsc.start_date as start_date', 'rsc.finish_date as finish_date', 'rsc.enroll_fee as enroll_fee', 'rsc.course_id as course_id', 'rsc.course_fee as course_fee', 'rsc.course_upfront_fee as course_upfront_fee', 'rsc.course_material_fee as course_material_fee', 'rsc.offer_id as offer_id', 'ra.agency_name', 'rc.name as campus_name', 'rvc.value as visa_type', 'rto_country.nationality as stud_nationality', 'rto_country.name as country_name', 'rto_colleges.college_name', 'country.nationality as nationality_of_student', 'birthcountry.name as country_of_birth')
            ->where('rto_students.id', '=', $studentId)
            ->where('rsc.course_id', '=', $courseId)
            ->get();
    }

    public function getStudentDetailsV3($studentId, $courseId)
    {

        return Students::leftjoin('rto_student_details as rsd', 'rto_students.id', '=', 'rsd.student_id')
            ->leftjoin('rto_country as country', 'country.id', '=', 'rto_students.nationality')
            ->leftjoin('rto_english_test as ret', 'rsd.EPL_test_name', '=', 'ret.id')
            ->leftjoin('rto_colleges', 'rto_colleges.id', '=', 'rto_students.college_id')
            ->leftjoin('rto_employment_status as res', 'rsd.current_employment_status', '=', 'res.id')
            ->leftjoin('rto_student_courses as rsc', 'rto_students.id', '=', 'rsc.student_id')
            ->leftjoin('rto_agents as ra', 'ra.id', '=', 'rsc.agent_id')
            ->leftjoin('rto_campus as rc', 'rc.id', '=', 'rsc.campus_id')
            ->leftjoin('rto_courses as cour', 'cour.id', '=', 'rsc.course_id')
            ->leftjoin('rto_setup_section as rvc', 'rvc.id', '=', 'rto_students.visa_status')
            ->leftjoin('rto_country', 'rto_country.id', '=', 'rto_students.current_country')
            ->select('rto_students.*', 'rsd.*', 'ret.name as english_name', 'rsc.issued_date as issued_date', 'rsc.is_material_fee_inc_initail_payment', 'cour.course_name as course_name', 'rsc.start_date as start_date', 'rsc.finish_date as finish_date', 'rsc.enroll_fee as enroll_fee', 'rsc.course_id as course_id', 'rsc.course_fee as course_fee', 'rsc.course_upfront_fee as course_upfront_fee', 'rsc.course_material_fee as course_material_fee', 'rsc.offer_id as offer_id', 'ra.agency_name', 'rc.name as campus_name', 'rvc.value as visa_type', 'rto_country.nationality as stud_nationality', 'rto_country.name as country_name', 'rto_colleges.college_name', 'country.nationality as nationality_of_student')
            ->where('rto_students.id', '=', $studentId)
            ->get();
    }

    public function getAreaofDisability($areaOfDisability)
    {
        $arrAreaOfDisabilitys = explode(',', $areaOfDisability);

        $arrAreaOfDisabilityName = [];

        for ($i = 0; $i < count($arrAreaOfDisabilitys); $i++) {
            $arrAreaOfDisability = $arrAreaOfDisabilitys[$i];
            $getName = AreaOfDisability::select('name')->where('avaitmiss_id', $arrAreaOfDisability)->get()->toArray();
            if (count($getName) > 0) {
                $arrAreaOfDisabilityName[] = $getName[0]['name'];
            }
        }

        $data = implode(', ', $arrAreaOfDisabilityName);

        return $data;
    }

    public function getApplicantDetails($studentId, $collegeId)
    {
        return Students::join('rto_student_offers as rso', 'rso.student_id', '=', 'rto_students.id')
            ->where('rto_students.id', '=', $studentId)
            ->where('rto_students.college_id', '=', $collegeId)
            ->get(['rso.id as offer_id', 'rto_students.application_reference_id', 'rto_students.first_name', 'rto_students.middel_name', 'rto_students.family_name', 'rto_students.email']);
    }

    public function getStudentUserId($genStudentId)
    {
        return Students::where('generated_stud_id', '=', $genStudentId)
            ->get(['id'])->toarray();
    }

    public function getStudentList($perPage, $collegeId)
    {

        // $select('rto_students.is_applicant', 'rto_students.is_offered', 'rto_students.is_student');

        return Students::leftjoin('rto_student_courses as rsc', 'rto_students.id', '=', 'rsc.student_id')
            ->leftjoin('rto_agents as ra', 'ra.id', '=', 'rsc.agent_id')
            ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->leftjoin('rto_campus as rcms', 'rcms.id', '=', 'rsc.campus_id')
            ->select('rto_students.id', 'rto_students.first_name', 'rto_students.family_name', 'rto_students.nickname', 'rto_students.application_reference_id', 'rto_students.status', 'rto_students.created_at', 'rto_students.USI', 'rsc.start_date as startDate', 'rsc.finish_date as finishDate', 'ra.agency_name as agent_name', 'rc.course_code', 'rc.course_name', 'rsc.coe_name', 'rsc.coe_applicable', 'rsc.coe_image', 'rc.id as course_id', 'rsc.id as primary_id', 'rsc.offer_status as offer_status', 'rsc.offer_id', 'rto_students.generated_stud_id', 'rto_students.stud_id_type', 'rto_students.is_applicant', 'rto_students.is_offered', 'rto_students.is_student', 'rcms.name as campus_name', 'rsc.intake_date')
            ->where('rto_students.college_id', '=', $collegeId)
            ->where('rto_students.is_offered', '=', 1)
                        // ->where('rsc.status', '!=', 'Enrolled')
            ->where('rsc.offer_status', '!=', 'Enrolled')
            ->orderBy('rsc.id', 'DESC')
            ->paginate($perPage);
    }

    public function getStudentListDesc($perPage, $collegeId)
    {
        /* $data = Students::leftjoin('rto_users', function($join) use ($studentRoleType) {
          $join->on('rto_users.username', '=', 'rto_students.generated_stud_id');
          $join->on('rto_users.role_id', '=', DB::raw($studentRoleType));
          })
          ->where('rto_students.college_id', '=', $collegeId)->where('is_student', '=', 1)->orderBy('id', 'DESC')
          ->select(['rto_students.*', 'rto_users.username'])
          ->paginate($perPage);

          return $data; */
        return Students::where('college_id', '=', $collegeId)->where('is_student', '=', 1)->orderBy('id', 'DESC')->paginate($perPage);
    }

    public function getStudentsList($collegeId, $studentRoleType, $request)
    {
        $tenantPrefix = Config::get('tenancy.database.prefix');
        $currentUrl = explode('/', url()->current());
        $currentUrl = explode('.', $currentUrl[2]);
        $applicationurl = getStudentApplicationUrl().Auth::user()->id;

        $requestData = $_REQUEST;
        $columns = [
            // datatable column index  => database column name
            0 => 'rs.id',
            1 => 'rs.generated_stud_id',
            2 => 'rs.name_title',
            3 => 'rs.first_name',
            4 => 'rs.family_name',
            // 5 => 'rs.nickname',
            5 => 'rs.gender',
            6 => 'rc.course_code',
            7 => 'cmp.name',
            8 => 'rs.student_type',
            9 => 'rsc.status',
            10 => 'rs.DOB',
            11 => 'ru.username',
            12 => 'rs.USI',
            13 => 'rs.email',
            14 => 'rs.birthplace',
            15 => 'rs.passport_no',
            16 => 'rs.visa_number',
            17 => 'rsc.start_date',
            18 => 'rsc.finish_date',
            19 => 'ra.agency_name',
            20 => 'rs.application_reference_id',

        ];

        $query = Students::from('rto_students as rs')
            ->leftjoin('rto_users as ru', function ($join) use ($studentRoleType) {
                $join->on('ru.username', '=', 'rs.generated_stud_id');
                $join->on('ru.role_id', '=', DB::raw($studentRoleType));
            })
            ->leftjoin('rto_student_courses as rsc', 'rsc.student_id', '=', 'rs.id') // for join with course
            ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsc.course_id') // for join with course
            ->leftjoin('rto_agents as ra', 'ra.id', '=', 'rsc.agent_id') // get agent name
            ->leftjoin('rto_campus as cmp', 'cmp.id', '=', 'rsc.campus_id') // get campus
            ->where('rs.college_id', '=', $collegeId)
            ->where('is_student', '=', 1);
        // ->orderBy('rs.generated_stud_id', 'DESC')

        if (! empty($requestData['search']['value'])) {   // if there is a search parameter, $requestData['search']['value'] contains search parameter
            $searchVal = trim($requestData['search']['value']);
            $query->where(function ($query) use ($columns, $searchVal, $requestData) {
                $statusArray = ['New Application Request', 'New', 'Request', 'New Application', 'Application Request'];
                $flag = 0;
                foreach ($columns as $key => $value) {
                    $searchVal = trim($requestData['search']['value']);
                    if ($key == 10) {
                        $searchVal = date('Y-m-d', strtotime($searchVal));
                    } elseif ($key == 9 && in_array(ucwords($searchVal), $statusArray)) {
                        //                            }else if($key == 5 && strcasecmp("New Application Request",$searchVal) == 0 || strcasecmp("New Application",$searchVal) == 0 || strcasecmp("New",$searchVal) == 0){
                        $searchVal = 'In Application';
                    }
                    if ($requestData['columns'][$key]['searchable'] == 'true' && $searchVal != '') {
                        if ($flag == 0) {
                            $query->where($value, 'like', '%'.$searchVal.'%');
                            $flag = $flag + 1;
                        } else {
                            if ($key == 3) {
                                $query->orWhere(DB::raw('CONCAT('.$value.'," ", '.$columns[$key + 1].')'), 'like', '%'.$searchVal.'%');
                            } else {
                                $query->orWhere($value, 'like', '%'.$searchVal.'%');
                            }
                        }
                    }
                }
            });
        }

        $dropDownValue5 = $requestData['columns'][9]['search']['value'];

        $dropDownSeperatedval = explode(',', $dropDownValue5);

        if (! empty($dropDownValue5) && $dropDownValue5 !== 'Unknown') {

            //          $query->where('rsc.offer_status', 'like', '%' . $dropDownValue5 . '%');
            $query->whereIn('rsc.status', $dropDownSeperatedval);
        }
        $query->where('rsc.offer_status', 'Enrolled');
        //        $query->groupBy('rsc.student_id');
        $temp = $query->orderBy($columns[$requestData['order'][0]['column']], $requestData['order'][0]['dir']);

        $totalData = ($temp->count());
        $totalFiltered = ($temp->count());
        // $totalData = count($temp->get());
        // $totalFiltered = count($temp->get());

        $resultArr = $query->skip($requestData['start'])
            ->take($requestData['length'])
            ->select('rs.id', 'rs.uuid', 'rs.generated_stud_id', 'rs.name_title', 'rs.first_name', 'rs.family_name', 'rs.gender', 'rs.DOB', 'rs.USI', 'rs.email', 'rs.birthplace', 'rs.passport_no', 'rs.visa_number',
                // DB::raw('DATE_FORMAT(rs.DOB, "%d %b %Y") as new_dob'),
                'rs.student_type', 'ru.username', 'cmp.name as campusName', 'rsc.status', 'rc.id as course_id', 'rc.course_name', 'rc.course_code', 'rsc.start_date', 'rsc.finish_date', 'ra.agency_name', 'rs.application_reference_id')
            ->get();

        // echo "<pre>";print_r($resultArr);exit;
        // echo "<pre>";print_r($resultArr);exit;

        $data = [];
        $sessionPermission = $request->session()->get('arrPermissionList', 'default');
        $sessionPermissionData = (isset($sessionPermission['delete_student_account'])) ? $sessionPermission['delete_student_account'] : '';
        $sessionPermissionDataEdit = (isset($sessionPermission['deny_staff_to_update_student_profile_info'])) ? $sessionPermission['deny_staff_to_update_student_profile_info'] : '';
        // $sessionPermissionDataEdit = $sessionPermission['deny_staff_to_update_student_profile_info'];

        foreach ($resultArr as $row) {

            $nestedData = [];
            $action2 = $action3 = $action4 = '';
            if (empty($row['username'])) {
                $action2 = '<span data-toggle="modal" class="create" data-id="'.$row['id'].'">
                                <a class="link-black text-sm" data-toggle="tooltip" data-original-title="Create user account for the selected student" href="javascript:;"><i class="fa fa-user-plus"></i> </a>
                            </span>';
            } else {
                $action2 = '<span data-toggle="modal" class="reset" data-id="'.$row['id'].'" >
                                <a class="link-black text-sm" data-toggle="tooltip" data-original-title="Reset password for the selected student?" href="javascript:;"><i class="fa fa-key"></i> </a>
                            </span>';
            }

            if ($sessionPermissionDataEdit == 'yes') {
                // $action3 = '<a class="link-black text-sm" data-toggle="tooltip" data-original-title="Edit" href="' . route('student-profile-edit', array('id' => $row["id"])) . '"><i class="fa fa-edit"></i> </a>';
                $action3 = '<a class="link-black text-sm" data-toggle="tooltip" data-original-title="Edit" target="_blank" href="'.$applicationurl.'/'.$row['uuid'].getStudentApplicationTokenParam().'"><i class="fa fa-edit"></i> </a>';
            }

            if ($sessionPermissionData == 'yes') {
                /*$action4 = '<span data-toggle = "modal" class="delete" data-id = "' . $row["id"] . "-" . $row["course_id"] . '" data-target = "#deleteModal">
                                <a class="link-black text-sm delete" data-toggle = "tooltip" data-original-title = "Delete" href = "javascript:;"><i class="fa fa-remove"></i> </a>
                            </span>';*/
            }

            $status_color = '';
            if ($row['status'] == 'Agent Apply') {
                $status_color = 'gray';
            } elseif ($row['status'] == 'Finished') {
                $status_color = '#f0d723';
            } elseif ($row['status'] == 'Completed') {
                $status_color = '#70b23f';
            } elseif ($row['status'] == 'Current Student') {
                $status_color = '#3ab6cd';
            } elseif ($row['status'] == 'Cancelled') {
                $status_color = '#ed1c24';
            } else {
                $status_color = '#a362cb';
            }

            $action = '<div class="action-overlay">
                            <ul class="icon-actions-set">
                                <li>
                                    <a class="link-black text-sm"  data-toggle="tooltip" data-original-title="Preview Student Profile" href="'.route('student-profile', ['id' => $row['id']]).'"><i class="fa fa-file-code"></i> </a>
                                </li>
                                <li>'.$action2.'</li>
                                <li>'.$action3.'</li>
                                <li>'.$action4.'</li>
                            </ul>
                        </div>';

            $nestedData[] = $row['id'];
            $nestedData[] = $row['generated_stud_id'].$action;
            $nestedData[] = $row['name_title'];
            $nestedData[] = $row['first_name'];
            $nestedData[] = $row['family_name'];
            // $nestedData[] = $row["nickname"];
            $nestedData[] = $row['gender'];
            $nestedData[] = '<span data-toggle="tooltip" data-original-title="'.$row['course_name'].'" >'.$row['course_code'].'</span>';
            $nestedData[] = $row['campusName'];
            $nestedData[] = $row['student_type'];
            $nestedData[] = '<span class="status-box" style="background-color:'.$status_color.'"> '.$row['status'].' </span>';
            //            $nestedData[] = $row["DOB"];
            $nestedData[] = date('d-m-Y', strtotime($row['DOB']));
            // $nestedData[] = $row["new_dob"];
            // $nestedData[] = (!empty($row["DOB"])) ? ((date("d M Y", strtotime($row["DOB"])))) : '-';
            $nestedData[] = $row['username'];
            $nestedData[] = $row['USI'];
            $nestedData[] = $row['email'];
            $nestedData[] = $row['birthplace'];
            $nestedData[] = $row['passport_no'];
            $nestedData[] = $row['visa_number'];
            $nestedData[] = date('d-m-Y', strtotime($row['start_date']));
            $nestedData[] = date('d-m-Y', strtotime($row['finish_date']));
            $nestedData[] = $row['agency_name'];
            $nestedData[] = $row['application_reference_id'];
            $data[] = $nestedData;
        }
        // echo "<pre>";print_r($data);exit;

        $json_data = [
            'draw' => intval($requestData['draw']), // for every request/draw by clientside , they send a number as a parameter, when they recieve a response/data they first check the draw number, so we are sending same number in draw.
            'recordsTotal' => intval($totalData), // total number of records
            'recordsFiltered' => intval($totalFiltered), // total number of records after searching, if there is no searching then totalFiltered = totalData
            'data' => $data,   // total data array
        ];

        return $json_data;
    }

    public function getStudentsListV2($perPage, $collegeId, $studentRoleType)
    {
        $data = Students::leftjoin('rto_users', function ($join) use ($studentRoleType) {
            $join->on('rto_users.username', '=', 'rto_students.generated_stud_id');
            $join->on('rto_users.role_id', '=', DB::raw($studentRoleType));
        })
            ->leftjoin('rto_student_courses', 'rto_student_courses.student_id', '=', 'rto_students.id') // for join with course
            ->leftjoin('rto_campus as cmp', 'cmp.id', '=', 'rto_student_courses.campus_id') // get campus
            ->where('rto_students.college_id', '=', $collegeId)
            ->where('is_student', '=', 1)
                // ->orderBy('id', 'DESC')
            ->orderBy('rto_students.generated_stud_id', 'DESC')
            ->groupBy('rto_student_courses.student_id')
            ->select(['rto_students.*', 'rto_users.username', 'cmp.name as campusName'])
            ->paginate($perPage);

        return $data;
        // return Students::where('college_id', '=', $collegeId)->where('is_student', '=', 1)->orderBy('id', 'DESC')->paginate($perPage);
    }

    public function getStudentListForAttendance($perPage, $collegeId)
    {
        return Students::leftjoin('rto_country', 'rto_students.nationality', '=', 'rto_country.id')
            ->select('rto_students.generated_stud_id', 'rto_students.id', 'rto_students.first_name', 'rto_students.family_name', 'rto_students.DOB', 'rto_students.nationality', 'rto_country.name as student_nationality')
            ->where('rto_students.college_id', '=', $collegeId)
            ->where('rto_students.is_student', '=', 1)
            ->orderBy('rto_students.first_name', 'ASC')
            ->orderBy('rto_students.family_name', 'ASC')
            ->paginate($perPage);
    }

    public function searchStudentsForAttendance($searchBy, $searchString, $perPage, $studentId, $collegeId)
    {
        if (isset($studentId) && $studentId != '') {
            return Students::leftjoin('rto_country', 'rto_students.nationality', '=', 'rto_country.id')
                ->select('rto_students.generated_stud_id', 'rto_students.id', 'rto_students.first_name', 'rto_students.family_name', 'rto_students.DOB', 'rto_students.nationality', 'rto_country.name as student_nationality')
                ->where('rto_students.college_id', '=', $collegeId)
                ->where('rto_students.is_student', '=', 1)
                ->where('rto_students.'.$searchBy, '=', $searchString)
                ->whereIn('rto_students.id', $studentId)->paginate($perPage);
            // return Students::where([['rto_students.' . $searchBy, '=', $searchString]])->where('college_id', '=', $collegeId)->where('is_student', '=', 1)->whereIn('id', $studentId)->paginate($perPage);
        } else {
            return Students::leftjoin('rto_country', 'rto_students.nationality', '=', 'rto_country.id')
                ->select('rto_students.generated_stud_id', 'rto_students.id', 'rto_students.first_name', 'rto_students.family_name', 'rto_students.DOB', 'rto_students.nationality', 'rto_country.name as student_nationality')
                ->where('rto_students.college_id', '=', $collegeId)
                ->where('rto_students.is_student', '=', 1)
                ->where('rto_students.'.$searchBy, '=', $searchString)
                ->paginate($perPage);
            // return Students::where([['rto_students.' . $searchBy, '=', $searchString]])->where('college_id', '=', $collegeId)->where('is_student', '=', 1)->paginate($perPage);
        }
    }

    public function searchStudents($searchBy, $searchString, $perPage, $studentId, $collegeId, $studentRoleType)
    {
        /* if (isset($studentId) && $studentId != '') {
          return Students::where([['rto_students.' . $searchBy, '=', $searchString]])->where('college_id', '=', $collegeId)->where('is_student', '=', 1)->whereIn('id', $studentId)->paginate($perPage);
          } else {
          return Students::where([['rto_students.' . $searchBy, '=', $searchString]])->where('college_id', '=', $collegeId)->where('is_student', '=', 1)->paginate($perPage);
          } */
        if (isset($studentId) && $studentId != '') {
            $data = Students::leftjoin('rto_users', function ($join) use ($studentRoleType) {
                $join->on('rto_users.username', '=', 'rto_students.generated_stud_id');
                $join->on('rto_users.role_id', '=', DB::raw($studentRoleType));
            })
                ->where([['rto_students.'.$searchBy, '=', $searchString]])->where([['rto_students.'.$searchBy, '=', $searchString]])->where('rto_students.college_id', '=', $collegeId)->where('is_student', '=', 1)->whereIn('rto_students.id', $studentId)
                ->select(['rto_students.*', 'rto_users.username'])
                ->paginate($perPage);
        } else {
            $data = Students::leftjoin('rto_users', function ($join) use ($studentRoleType) {
                $join->on('rto_users.username', '=', 'rto_students.generated_stud_id');
                $join->on('rto_users.role_id', '=', DB::raw($studentRoleType));
            })
                ->where([['rto_students.'.$searchBy, '=', $searchString]])->where([['rto_students.'.$searchBy, '=', $searchString]])->where('rto_students.college_id', '=', $collegeId)->where('is_student', '=', 1)
                ->select(['rto_students.*', 'rto_users.username'])
                ->paginate($perPage);
        }

        return $data;
    }

    public function searchStudentsV2($searchBy, $searchString, $perPage, $studentId, $collegeId, $studentRoleType)
    {
        /* if (isset($studentId) && $studentId != '') {
          return Students::where([['rto_students.' . $searchBy, '=', $searchString]])->where('college_id', '=', $collegeId)->where('is_student', '=', 1)->whereIn('id', $studentId)->paginate($perPage);
          } else {
          return Students::where([['rto_students.' . $searchBy, '=', $searchString]])->where('college_id', '=', $collegeId)->where('is_student', '=', 1)->paginate($perPage);
          } */

        if (isset($studentId) && $studentId != '') {
            $sql = Students::leftjoin('rto_users', function ($join) use ($studentRoleType) {
                $join->on('rto_users.username', '=', 'rto_students.generated_stud_id');
                $join->on('rto_users.role_id', '=', DB::raw($studentRoleType));
            });
            $sql->leftjoin('rto_student_courses as rsc', 'rto_students.id', '=', 'rsc.student_id');
            $sql->leftjoin('rto_campus as cmp', 'cmp.id', '=', 'rsc.campus_id'); // get campus name

            if ($searchBy == 'course_id' || $searchBy == 'agent_id' || $searchBy == 'status' || $searchBy == 'start_date' || $searchBy == 'coe_name' || $searchBy == 'offer_id') {

                $sql->where('rsc.'.$searchBy, '=', $searchString);
            } elseif ($searchBy == 'username') {

                $sql->where('rto_users.'.$searchBy, '=', $searchString);
            } elseif ($searchBy == 'id_first_last') {

                $sql->where(function ($query) use ($searchString) {
                    $query->where('rto_students.generated_stud_id', 'like', '%'.$searchString.'%')
                        ->orwhere('rto_students.first_name', 'like', '%'.$searchString.'%')
                        ->orwhere('rto_students.family_name', 'like', '%'.$searchString.'%');
                });
            } elseif ($searchBy == 'first_last') {

                $sql->where(function ($query) use ($searchString) {
                    $query->where('rto_students.first_name', 'like', '%'.$searchString.'%')
                        ->orwhere('rto_students.family_name', 'like', '%'.$searchString.'%');
                });
            } else {

                $sql->where('rto_students.'.$searchBy, 'like', '%'.$searchString.'%');
            }
            $sql->where('rto_students.college_id', '=', $collegeId)
                ->where('is_student', '=', 1)
                ->whereIn('rto_students.id', $studentId)
                ->groupBy('rto_students.id')
                ->orderBy('rto_students.generated_stud_id', 'DESC');

            $data = $sql->select(['rto_students.*', 'rto_users.username', 'cmp.name as campusName'])
                ->paginate($perPage);
        } else {

            $data = Students::leftjoin('rto_users', function ($join) use ($studentRoleType) {
                $join->on('rto_users.username', '=', 'rto_students.generated_stud_id');
                $join->on('rto_users.role_id', '=', DB::raw($studentRoleType));
            })
                ->leftjoin('rto_student_courses', 'rto_student_courses.student_id', '=', 'rto_students.id') // for join with course
                ->leftjoin('rto_campus as cmp', 'cmp.id', '=', 'rto_student_courses.campus_id') // get campus
                    // ->where('rto_students.' . $searchBy, 'like', '%' . $searchString . '%')
                ->where('rto_students.generated_stud_id', 'like', '%'.$searchString.'%')
                ->orwhere('rto_students.first_name', 'like', '%'.$searchString.'%')
                ->orwhere('rto_students.family_name', 'like', '%'.$searchString.'%')
                ->where('rto_students.college_id', '=', $collegeId)
                ->where('is_student', '=', 1)
                ->groupBy('rto_student_courses.student_id')
                ->orderBy('rto_students.generated_stud_id', 'DESC')
                ->select(['rto_students.*', 'rto_users.username', 'cmp.name as campusName'])
                ->paginate($perPage);
            // print_r($data);exit;
        }

        return $data;
    }

    public function deleteStudents($studentId)
    {
        return Students::where('id', '=', $studentId)->delete();
    }

    public function getStudentType($studentId)
    {
        return Students::where('id', '=', $studentId)->get(['student_type']);
    }

    public function getStudentId($collegeId, $studentId)
    {
        return Students::where('college_id', '=', $collegeId)->where('generated_stud_id', '=', $studentId)->get(['id'])->toarray();
    }

    // insert time
    public function checkFirstNameAndDOB($collegeId, $request)
    {

        $firstName = $request->input('first_name');
        $DOB = date('Y-m-d', strtotime($request->input('DOB')));
        $validations = [];
        $studentInfo = new Students;
        $studentData = Students::where('college_id', '=', $collegeId)->where('first_name', '=', $firstName)->where('DOB', '=', $DOB)->get();
        if ($studentData->count() > 0) {
            $validations['first_name'] = 'unique:rto_students,first_name';
            $failedValidations = [
                'first_name.unique' => 'First name and Date of Birth already exist.',
            ];
            $validator12 = Validator::make($request->all(), $validations, $failedValidations);

            return $validator12;
        } else {

            $validations['first_name'] = 'min:1';
            $validator12 = Validator::make($request->all(), $validations);

            return $validator12;
        }
    }

    // update time
    public function updateFirstNameAndDOB($studentId, $collegeId, $request, $userId)
    {
        // echo $studentId;
        // exit;
        $firstName = $request->input('first_name');
        $DOB = date('Y-m-d', strtotime($request->input('DOB')));
        $validations = [];
        $studentInfo = new Students;
        $studentData = Students::where('college_id', '=', $collegeId)->where('first_name', '=', $firstName)->where('DOB', '=', $DOB)->get();
        $studentCount = Students::where('college_id', '=', $collegeId)->where('first_name', '=', $firstName)->where('id', '!=', $studentId)->where('DOB', '=', $DOB)->count();

        if ($studentCount == 0) {
            //        if ($studentData[0]->id == $studentId) {
            $updateStudents = $studentInfo->updateStudentInfo($studentId, $collegeId, $request, $userId);

            $studentProfile = $studentInfo->getStudents($studentId);
            if ($request->input('generated_stud_id') != '') {
                if ($studentProfile->generated_stud_id == $request->input('generated_stud_id')) {
                    $objRtoUsers = new Users;
                    $lastUserId = $objRtoUsers->editUsersFromStudentProfile($studentProfile->generated_stud_id, $request);
                }
            }

            $validations['first_name'] = 'min:1';
            $validator = Validator::make($request->all(), $validations);

            return $validator;
        } else {
            $validations['first_name'] = 'unique:rto_students,first_name';
            $failedValidations = [
                'first_name.unique' => 'First name and DOB already exixts.',
            ];
            $validator = Validator::make($request->all(), $validations, $failedValidations);

            return $validator;
        }
    }

    public function compareStudentId($studentId)
    {
        return Students::where('generated_stud_id', '=', $studentId)->get(['student_type']);
    }

    public function updateStudentDetail($studentId, $generateID, $studIDType, $primaryID, $request)
    {

        if (! empty($primaryID) && $primaryID > 0) {
            $getCurrentStatus = StudentCourse::where('id', $primaryID)->select('offer_status', 'student_id')->get()->toArray();
            $studCourse = StudentCourse::find($primaryID);
            $studCourse->status = 'Enrolled';
            $studCourse->offer_status = 'Enrolled';
            $studCourse->save();

            $offeredStatus = 'Enrolled';
            $objStudentCommunicationLog = new StudentCommunicationLog;
            $saveLog = $objStudentCommunicationLog->saveApproveStatusLog($request, $studentId, $primaryID, $getCurrentStatus[0]['offer_status'], $offeredStatus);

            $studentsInfo = Students::find($studentId);
            $studentsInfo->is_student = 1;
            $studentsInfo->update();
            if (galaxy_feature('galaxy_webhooks')) {
                $objStudentCourse = StudentCourses::find($primaryID);
                \Webhooks\Facades\Webhook::dispatch(new \Webhooks\Events\Student\StudentEnrolledEvent($objStudentCourse));
            }
        }
        // echo $studentId.'----'.$generateID.'----'.$studIDType.'--------'.$primaryID;

        $collegeId = Auth::user()->college_id;
        $studentsInfo = Students::find($studentId);
        $studCourse = StudentCourse::where('student_id', '=', $studentId)->get();
        //        print_r($studCourse);
        foreach ($studCourse as $arr) {
            if ($arr->status == 'Offered') {
                $status = 'true';
                break;
            } else {
                $status = 'false';
            }
        }

        if (preg_match('/:/', $generateID)) {
            $primaryID = substr($generateID, 4);

            if ($studIDType == 'generate' && ! empty($primaryID)) {

                $studCourse = StudentCourse::find($primaryID);
                $studCourse->status = 'Enrolled';
                $studCourse->offer_status = 'Enrolled';
                $studCourse->save();
                if ($status == 'false') {
                    $studentsInfo->is_student = 1;
                    $studentsInfo->is_offered = 0;
                    $studentsInfo->is_applicant = 0;
                }
            }
        } else {
            if ($studIDType == 'generate' && $status == 'false') {
                $studentsInfo->is_student = 1;
                $studentsInfo->is_offered = 0;
                $studentsInfo->is_applicant = 0;
                $studentsInfo->generated_stud_id = $generateID;
            } elseif ($studIDType == 'generate' && $status == 'true') {
                $studentsInfo->generated_stud_id = $generateID;
            } elseif ($studIDType == 'reserve') {
                $studentsInfo->is_offered = 1;
                $studentsInfo->generated_stud_id = $generateID;
            }
        }

        $studentsInfo->stud_id_type = $studIDType;
        $studentsInfo->updated_by = Auth::user()->id;
        $studentsInfo->update();

        if ($studentsInfo->is_student == 1) {
            $StudentListData = [
                'college_id' => $collegeId,
                'tenant_id' => tenant('id'),
            ];
            $this->updateStudentList($StudentListData);
            if (galaxy_feature('galaxy_webhooks')) {
                $objStudentCourse = StudentCourses::find($primaryID);
                \Webhooks\Facades\Webhook::dispatch(new \Webhooks\Events\Student\StudentEnrolledEvent($objStudentCourse));
            }
        }

        $studIDFormate = StudentIdFormate::where('college_id', $collegeId)->get();
        $studIDFormate[0]->auto_increment = ($studIDFormate[0]->auto_increment + 1);
        $studIDFormate[0]->save();
    }

    public function updateStudentList($StudentListData)
    {
        // if(!config('features.scout') && !config('features.scout_beta_search')){
        // event(new \App\Events\StudentListEvent($StudentListData));
        // }
    }

    public function checkAvailabeStudentID($generateID, $IDtype, $studID = '')
    {

        $result = Students::where('generated_stud_id', '=', $generateID)->where('college_id', '=', Auth::user()->college_id);

        $resArr = $result->get(['id']);

        if ($resArr->count() > 0) {
            return 'FALSE';
        } else {
            return 'TRUE';
        }

    }

    // get student by application id for aplly-online(step1)
    public function getStudentByApplicationId($collegeId, $request)
    {
        $applicationId = $request->input('application_id');

        return Students::where('college_id', '=', $collegeId)->where('application_reference_id', '=', $applicationId)->get(['id']);
    }

    // update is_offered status after submited 5th step (apply-online-final-step)
    public function updateOfferedStatus($studentId)
    {
        $updateOfferedStatus = Students::find($studentId);
        $updateOfferedStatus->is_applicant = 0;
        $updateOfferedStatus->is_offered = 1;
        $updateOfferedStatus->save();
    }

    // search student for offer-manage
    public function _searchOfferManageStudent($searchBy, $collegeCampus, $searchString, $createdAt, $perPage, $courseFilter, $applicationStautus)
    {

        $collegeId = Auth::user()->college_id;

        $sql = Students::leftjoin('rto_student_courses as rsc', 'rto_students.id', '=', 'rsc.student_id')
            ->leftjoin('rto_agents as ra', 'ra.id', '=', 'rsc.agent_id')
            ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->select('rto_students.id', 'rto_students.first_name', 'rto_students.family_name', 'rto_students.nickname', 'rto_students.application_reference_id', 'rto_students.status', 'rto_students.created_at', 'rto_students.USI', 'rsc.start_date as startDate', 'rsc.finish_date as finishDate', 'ra.agency_name as agent_name', 'rc.course_code', 'rc.course_name', 'rsc.coe_name', 'rc.id as course_id', 'rsc.id as primary_id', 'rsc.offer_status as offer_status', 'rsc.offer_id', 'rsc.status as checkstatus', 'rto_students.generated_stud_id', 'rto_students.stud_id_type', 'rto_students.is_applicant', 'rto_students.is_offered', 'rto_students.is_student')
            ->where('rto_students.college_id', '=', $collegeId)
            ->where('rto_students.is_offered', '=', 1)
            ->where('rsc.offer_status', '!=', 'Enrolled');

        if (! empty($applicationStautus)) {
            if ($applicationStautus == 'All') {
                $sql->where('rsc.status', '!=', null);
            } else {
                $sql->where('rsc.status', '=', $applicationStautus);
            }
        }

        if (! empty($collegeCampus)) {
            $sql->where('rsc.campus_id', '=', $collegeCampus);
        }

        if (! empty($searchBy)) {

            if ($searchBy == 'offer_id' && (! empty($searchString))) {
                $sql->where('rsc.offer_id', 'like', "%$searchString%");
            } elseif ($searchBy == 'first_name' && (! empty($searchString))) {
                $sql->where('rto_students.first_name', 'like', "%$searchString%");
            } elseif ($searchBy == 'agent_name' && (! empty($searchString))) {
                $sql->where('ra.agency_name', 'like', "%$searchString%");
            } elseif ($searchBy == 'course_name' && ! empty($courseFilter)) {
                $sql->where('rc.id', $courseFilter);
            } elseif ($searchBy == 'created_at' && ! empty($createdAt)) {
                $created_at = date('Y-m-d', strtotime($createdAt));
                $sql->where('rto_students.created_at', 'like', "%$created_at%");
            } elseif ($searchBy == 'generated_stud_id' && $searchBy != '') {
                $sql->where('rto_students.generated_stud_id', 'like', "%$searchString%");
            }
        }

        $returnData = $sql->paginate($perPage);

        return $returnData;
    }

    public function getOfferManagaDatatableData($request)
    {

        $collegeId = Auth::user()->college_id;
        $requestData = $_REQUEST;

        $columns = [
            // datatable column index  => database column name
            1 => 'rsc.offer_id',
            2 => 'rto_students.generated_stud_id',
            3 => 'rto_students.created_at',
            4 => 'rto_students.first_name',
            5 => 'ra.agency_name',
            6 => 'ru.name',
            7 => 'rto_students.student_type',
            8 => 'rcms.name',
            9 => 'rc.course_code',
            10 => 'rsc.offer_status',
            11 => 'rol.label_name',
            12 => 'rsc.start_date',
            13 => 'rto_students.generated_stud_id',
            14 => 'rsc.coe_name',
            15 => 'rto_students.USI',
            16 => 'rto_students.application_reference_id',
            17 => 'rsc.intake_date',
        ];

        $query = Students::from('rto_students')
            ->leftjoin('rto_student_courses as rsc', 'rto_students.id', '=', 'rsc.student_id')
            ->leftjoin('rto_agents as ra', 'ra.id', '=', 'rsc.agent_id')
            ->leftjoin('rto_offer_label as rol', 'rol.id', '=', 'rsc.offer_label_id')
            ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->leftjoin('rto_campus as rcms', 'rcms.id', '=', 'rsc.campus_id')
            ->leftjoin('rto_users as ru', 'ru.id', '=', 'rto_students.created_by')
            ->where('rto_students.college_id', '=', $collegeId)
            ->where(function ($query) {
                $query->where(function ($innerQuery) {
                    $innerQuery->where('rto_students.is_offered', '=', 1)
                        ->where('rsc.offer_status', '!=', 'Enrolled');
                })->orWhere(function ($innerQuery) {
                    $innerQuery->whereNull('rto_students.is_student')
                        ->where('rsc.offer_status', '=', 'Enrolled');
                })->orWhere(function ($innerQuery) {
                    $innerQuery->where('rto_students.is_student', '=', 0)
                        ->where('rsc.offer_status', '=', 'Enrolled');
                });
            });
        if (! empty($requestData['search']['value'])) {   // if there is a search parameter, $requestData['search']['value'] contains search parameter
            $searchVal = $requestData['search']['value'];
            $query->where(function ($query) use ($columns, $searchVal, $requestData) {
                $flag = 0;
                $statusArray = ['New Application Request', 'New', 'Request', 'New Application', 'Application Request'];
                foreach ($columns as $key => $value) {
                    $searchVal = $requestData['search']['value'];
                    if ($key == 3 || $key == 12) {
                        $search = date('Y-m-d', strtotime($searchVal));
                        $searchVal = ($search == '1970-01-01') ? null : $search;
                    } elseif ($key == 5 && in_array(ucwords($searchVal), $statusArray)) {
                        $searchVal = 'In Application';
                    }
                    if ($requestData['columns'][$key]['searchable'] == 'true' && $searchVal != '') {

                        if ($flag == 0) {
                            if ($value == 'rto_students.first_name') {
                                $query->where('rto_students.first_name', 'like', '%'.$searchVal.'%');
                                $query->orWhere('rto_students.family_name', 'like', '%'.$searchVal.'%');
                                $query->orWhere(DB::raw("CONCAT(rto_students.first_name, ' ', rto_students.family_name)"), 'like', $searchVal);
                            } else {
                                $query->where($value, 'like', '%'.$searchVal.'%');
                            }

                            $flag = $flag + 1;
                        } else {
                            if ($value == 'rto_students.first_name') {
                                $query->orWhere('rto_students.first_name', 'like', '%'.$searchVal.'%');
                                $query->orWhere('rto_students.family_name', 'like', '%'.$searchVal.'%');
                                $query->orWhere(DB::raw("CONCAT(rto_students.first_name, ' ', rto_students.family_name)"), 'like', $searchVal);
                            } else {
                                $query->orWhere($value, 'like', '%'.$searchVal.'%');
                            }

                        }
                    }
                }
            });
        }

        if ($requestData['columns'][3]['search']['value'] != '') {
            $dropDownValue2 = json_decode($requestData['columns'][3]['search']['value']);

            if (! empty($dropDownValue2) && $dropDownValue2 !== 'Unknown') {
                if ($dropDownValue2[0]->startdate != '' && $dropDownValue2[0]->enddate != '') {
                    $startDate = Carbon::parse($dropDownValue2[0]->startdate);
                    $endDate = Carbon::parse($dropDownValue2[0]->enddate);
                    $query->whereBetween('rto_students.created_at', [$startDate, $endDate]);
                }
            }
        }

        if ($requestData['columns'][8]['search']['value'] != '') {
            $dropDownValue6 = $requestData['columns'][8]['search']['value'];
            $dropDownSeperatedCampusval = explode(',', $dropDownValue6);

            if (! empty($dropDownValue6) && $dropDownValue6 !== 'Unknown') {
                $query->whereIn('rcms.id', $dropDownSeperatedCampusval);
            }
        }

        if ($requestData['columns'][10]['search']['value'] != '') {
            $dropDownValue8 = $requestData['columns'][10]['search']['value'];
            $dropDownSeperatedval = explode(',', $dropDownValue8);

            if (! empty($dropDownValue8) && $dropDownValue8 !== 'Unknown') {
                $query->whereIn('rsc.offer_status', $dropDownSeperatedval);
            }
        }

        if ($requestData['columns'][11]['search']['value'] != '') {
            $dropDownValue10 = $requestData['columns'][11]['search']['value'];
            $arrOfferLabel = explode(',', $dropDownValue10);

            if (! empty($dropDownValue10) && $dropDownValue10 !== 'Unknown') {
                $query->whereIn('rsc.offer_label_id', $arrOfferLabel);
            }

        }
        if (isset($requestData['course_id'])) {

            $course_id = $requestData['course_id'];
            if (! empty($course_id) && $course_id !== 'null') {
                $query->where('rc.id', $course_id);
            }
        }
        if (isset($requestData['status'])) {
            $status = $requestData['status'];
            if (! empty($status)) {
                if ($status != 'All') {
                    $query->where('rsc.status', $status);
                }
            }
        }
        if (isset($requestData['intake_name'])) {
            $intake_name = $requestData['intake_name'];
            if (! empty($intake_name) && $intake_name !== 'null') {
                $query->where('rsc.intake_id', $intake_name);
            }
        }
        if (isset($requestData['campus'])) {
            $campus = $requestData['campus'];
            if (! empty($campus) && $intake_name !== 'null') {
                $query->where('rsc.campus_id', $campus);
            }
        }
        if (isset($requestData['course_type_id'])) {
            $course_type_id = $requestData['course_type_id'];
            if (! empty($course_type_id)) {
                if ($course_type_id != 'All') {
                    $query->where('rsc.course_type_id', $course_type_id);
                }
            }
        }
        if (isset($requestData['intake_date'])) {
            if (! empty($requestData['intake_date'])) {
                $intake_date = date('Y-m-d', strtotime($requestData['intake_date'])); // Format date safely
                $query->whereDate('rsc.intake_date', $intake_date);
            }
        }

        $applied_date_from = ! empty($requestData['applied_date_from']) ? Carbon::parse($requestData['applied_date_from'])->startOfDay() : null;
        $applied_date_to = ! empty($requestData['applied_date_to']) ? Carbon::parse($requestData['applied_date_to'])->endOfDay() : null;
        if ($applied_date_from !== null && $applied_date_to !== null) {
            $query->whereBetween('rsc.created_at', [$applied_date_from, $applied_date_to]);
        }

        $totalData = 0;
        $totalFiltered = 0;
        if ($requestData['order'][0]['column'] != '' && $requestData['order'][0]['dir'] != '') {
            $temp = $query->orderBy($columns[$requestData['order'][0]['column']], $requestData['order'][0]['dir']);
            $totalData = $temp->count();
            $totalFiltered = $temp->count();
        }

        $resultArr = $query->skip($requestData['start'])
            ->take($requestData['length'])
            ->select(
                'rto_students.id',
                'rto_students.uuid',
                DB::raw("CONCAT(rto_students.first_name,' ',rto_students.family_name) AS fullname"),
                'rto_students.nickname',
                'rto_students.application_reference_id',
                'rsc.offer_status',
                'rto_students.created_at as createdAt',
                'rto_students.USI',
                'rsc.start_date',
                'rsc.finish_date',
                'ra.agency_name as agent_name',
                'rc.course_code',
                'rc.course_name',
                'rsc.coe_name',
                'rsc.coe_image',
                'rc.id as course_id',
                'rsc.id as primary_id',
                'rto_students.status',
                'rsc.offer_id',
                'rsc.status as checkstatus',
                'rto_students.generated_stud_id',
                'rto_students.stud_id_type',
                'rto_students.is_applicant',
                'rto_students.is_offered',
                'rto_students.is_student',
                'rcms.name as campus_name',
                'rto_students.student_type',
                'ru.name',
                'rsc.intake_date',
                'rol.label_name'
            )
            ->get();

        $data = [];
        $xeroConnect = (Xero::isConnected()) ? true : false;

        foreach ($resultArr as $row) {
            $student = Student::findOrFail($row->id);

            $row->xeroContact = $student->fresh()->xeroContact;
            $outstandingBalance = 0;
            $unallocatedCredit = 0;
            if ($xeroConnect) {
                if ($student->isXeroContactCreatated()) {
                    // dispatch_sync(new \Domains\Xero\Jobs\SyncContactFromXero($student->xeroContact));
                    $outstandingBalance = $student->xeroContact->fresh()->outstanding_balance ? $student->xeroContact->fresh()->outstanding_balance : 0;
                    $unallocatedCredit = $student->xeroContact->fresh()->unallocated_credit ?? 0;
                }
            }

            $row->outstanding_balance = abs($outstandingBalance);
            $row->unallocatedCredit = abs($unallocatedCredit);

            $actionHtml = '';
            $applied_course_code = empty($row['course_code']) ? '' : $row['course_code'];
            $applied_course_name = empty($row['course_name']) ? '' : $row['course_name'];
            $applied_campus = empty($row['campus_name']) ? '' : $row['campus_name'];
            $applied_campus_name = (strlen($applied_campus) > 15) ? (substr($applied_campus, 0, 15).'...') : $applied_campus;
            $ostatus = ($row['offer_status'] == 'In Application') ? 'New Application Request' : $row['offer_status'];
            $status_color = '';
            if ($ostatus == 'New Application Request') {
                $status_color = 'gray';
            } elseif ($ostatus == 'Reconsider') {
                $status_color = '#f0d723';
            } elseif ($ostatus == 'Rejected') {
                $status_color = '#ea6747';
            } elseif ($ostatus == 'Offered') {
                $status_color = '#70b23f';
            } elseif ($ostatus == 'Pending') {
                $status_color = '#3ab6cd';
            } else {
                $status_color = '#a9dbd2';
            }
            $applied_course = $applied_course_code.' : '.$applied_course_name;
            $actionHtml = $this->getManageOfferActionHtml($row);

            $action = '<div class="action-overlay">
                            <ul class="icon-actions-set">
                                '.$actionHtml.'
                            </ul>
                        </div>';
            $fullCourse = $applied_course;
            $applied_course = (strlen($applied_course) > 25) ? (substr($applied_course, 0, 25).'...') : $applied_course;
            $agent_name = (strlen($row['agent_name']) > 19) ? (substr($row['agent_name'], 0, 19).'...') : $row['agent_name'];

            $nestedData = [];
            $nestedData[] = '<div class="form-group1"> <input type="checkbox" class="flat-red label-value-view applicable custom-checkbox-input custom-column-filter studentId" name="student_id" data-student-id="'.$row['id'].'" value="'.$row['primary_id'].'"></div>'.$action;
            $nestedData[] = empty($row['offer_id']) ? '-' : $row['offer_id'];
            // $nestedData[] = ($row['generated_stud_id'] != "" ? $row['generated_stud_id'] : '-');
            $nestedData[] = $this->getStudentXeroStatus($row, $xeroConnect);
            $nestedData[] = date('d M Y', strtotime($row['createdAt']));
            $nestedData[] = $row['fullname'];
            $nestedData[] = '<span data-toggle="tooltip" data-original-title="'.$row['agent_name'].'" aria-hidden="true">'.$agent_name.'</span>';
            $nestedData[] = $row['name'];
            $nestedData[] = $row['student_type'];
            $nestedData[] = '<span data-toggle="tooltip" data-original-title="'.$applied_campus.'" aria-hidden="true">'.$applied_campus_name.'</span>';
            $nestedData[] = '<span data-toggle="tooltip" data-original-title="'.$fullCourse.'" aria-hidden="true">'.$applied_course.'</span>';
            $nestedData[] = '<span class="status-box" style="background-color:'.$status_color.'"> '.$ostatus.' </span>';
            $nestedData[] = $row['label_name'];
            $nestedData[] = (empty($row['start_date']) ? '-' : date('d M Y', strtotime($row['start_date']))).' - '.(empty($row['finish_date']) ? '-' : date('d M Y', strtotime($row['finish_date'])));
            $nestedData[] = ($row['stud_id_type'] == 'reserve' ? $row['generated_stud_id'] : '-');
            $nestedData[] = $row['coe_name'];
            $nestedData[] = $row['USI'];
            $nestedData[] = $row['application_reference_id'];
            $nestedData[] = date('d M Y', strtotime($row['intake_date']));
            $data[] = $nestedData;
        }
        $data = $this->convertToUTF_1($data);

        $json_data = [
            'draw' => intval($requestData['draw']), // for every request/draw by clientside , they send a number as a parameter, when they recieve a response/data they first check the draw number, so we are sending same number in draw.
            'recordsTotal' => intval($totalData), // total number of records
            'recordsFiltered' => intval($totalFiltered), // total number of records after searching, if there is no searching then totalFiltered = totalData
            'data' => $data,   // total data array
        ];

        return $json_data;
    }

    private function getStudentXeroStatus($row, $xeroConnect = false)
    {
        $generatedStudentId = $row['generated_stud_id'];
        if (! empty($generatedStudentId)) {
            if ($xeroConnect) {
                if (! empty($row['xeroContact'])) {
                    if (empty($row['xeroContact']['xero_failed_at'])) {
                        return '<span class="text-green-500">'.$generatedStudentId.'</span>';
                    } else {
                        return '<span class="text-red-500">'.$generatedStudentId.'</span>';
                    }
                }
            }

            return $generatedStudentId;
        }

        return '-';
    }

    public function getManageOfferActionHtml($row)
    {

        $tenantPrefix = Config::get('tenancy.database.prefix');
        $collegeId = Auth::user()->college_id;
        $userId = Auth::user()->id;
        $currentUrl = explode('/', url()->current());
        $currentUrl = explode('.', $currentUrl[2]);
        $applicationurl = getStudentApplicationUrl().Auth::user()->id;

        $actionHtml = '';
        $actionHtml .= "<li><a class='link-black text-sm' data-toggle='tooltip' data-original-title='Combined Course Student Invoice' href='".route('download-combined-invoice', ['id' => $row['id'], 'courseID' => $row['course_id'], 'download' => 'download'])."'><i class='fa fa-file'></i></a></li>";

        if ($row['offer_status'] == 'Offered' || $row['offer_status'] == 'Enrolled') {

            if ($row['student_type'] == 'Domestic') {
                $actionHtml .= "<li><a class='link-black text-sm' data-original-title='Download Domestic Offer Letter' data-toggle='tooltip' href='".route('domestic-offer-letter-pdf', ['id' => $row['id'], 'courseID' => $row['course_id'], 'studentCourseID' => $row['primary_id'], 'download' => 'download'])."'><i class='fa fa-cloud-download-alt'></i></a></li>";
            } else {
                $actionHtml .= "<li><a class='link-black text-sm' data-original-title='Download Offer Letter' data-toggle='tooltip' href='".route('offer-letter-pdf-new', ['id' => $row['id'], 'courseID' => $row['course_id'], 'studentCourseID' => $row['primary_id'], 'download' => 'download'])."'><i class='fa fa-cloud-download-alt'></i></a></li>";
            }

            $actionHtml .= "<li><a class='link-black text-sm' data-original-title='View Edit this Application Detail' data-toggle='tooltip' id='' href='".$applicationurl.'/'.$row['uuid'].getStudentApplicationTokenParam()."'><i class='fa fa-book'></i></a></li>";

            $actionHtml .= " <li><span data-toggle='modal' class='delete'  data-target='#coeModal'>
                                                    <a href='javascript:;'  class='add-coe-offer-manage link-black text-sm'
                                                       data-toggle='tooltip' data-original-title='Add COE No.for this offer'
                                                       id='".$row['id']."'  data-id='".$row['offer_id']."'
                                                       data-student-course-id='".$row['primary_id']."'
                                                       data-course-code='".$row['course_code']."'
                                                       data-course-name='".$row['course_name']."' >
                                                        <i class='fa fa-plus'></i>
                                                    </a>
                                                </span>
                                            </li>";
            $actionHtml .= "<li>
                <span data-toggle='".(! empty($row['coe_name']) && empty($row['generated_stud_id']) ? 'tooltip' : ($row['stud_id_type'] != 'generate' ? 'modal' : 'tooltip'))."' data-original-title='Generate Student ID'>
                    <a class='link-black text-sm generateStudID'
                        data-coe='{$row['coe_name']}'
                        data-id='{$row['id']}'
                        data-id-type='{$row['stud_id_type']}'
                        data-application-id='{$row['offer_id']}'
                        data-primary-id='{$row['primary_id']}'
                        data-toggle='".(! empty($row['coe_name']) && empty($row['generated_stud_id']) ? 'modal' : '')."'
                        data-target='".(! empty($row['coe_name']) && empty($row['generated_stud_id']) ? '#generateIDModal' : '')."'
                        data-generate-id='".(! empty($row['coe_name']) && empty($row['generated_stud_id']) ? 'true' : ($row['stud_id_type'] != 'generate' ? $row['generated_stud_id'] : 'false'))."'
                        href='javascript:;'>
                        <i class='fa fa-user-plus'></i>
                    </a>
                </span>
            </li>";
            if ($row['offer_status'] == 'Offered') {
                $actionHtml .= "<li><a class='link-black text-sm' data-toggle='tooltip' data-original-title='Pending Offer Letter' href='".route('pending-offer', ['student_id' => $row['primary_id']])."'><i class='fa fa-lock'></i></a></li>";
                $actionHtml .= "<li><a class='link-black text-sm' data-toggle='tooltip' data-original-title='Re Approve the offer letter' ><i class='fa fa-check-circle'></i> </a></li>";
            }

            $actionHtml .= "<li><a class='link-black text-sm' data-toggle='tooltip' data-original-title='Generate Student Invoice' href='".route('stdent-invoice-pdf', ['id' => $row['id'], 'courseID' => $row['course_id'], 'download' => 'download'])."'><i class='fa fa-file'></i></a> </li>";
            $actionHtml .= "<li><a class='link-black text-sm' data-toggle='tooltip' data-original-title='Generate Agent Payment Invoice' href='".route('agent-payment-invoice-pdf', ['id' => $row['id'], 'courseID' => $row['course_id']])."'><i class='fa fa-dollar'></i></a></li>";
        } elseif ($row['offer_status'] == 'Pending' || $row['offer_status'] == 'Rejected') {

            $actionHtml .= "<li><a class='link-black text-sm' id='' data-toggle='tooltip' data-original-title='Reconsider this application' href='".route('reconsider-offer', ['student_id' => $row['primary_id']])."'><i class='fa fa-unlock-alt'></i></a></li>";
            $actionHtml .= "<li><a class='link-black text-sm' id='' data-toggle='tooltip' data-original-title='View Edit this Application Detail' target='_blank' href='".$applicationurl.'/'.$row['uuid'].getStudentApplicationTokenParam()."'><i class='fa fa-book'></i></a></li>";

            if ($row['offer_status'] == 'Rejected') {
                $actionHtml .= "<li><span data-toggle='modal' class='delete' data-id='".$row['primary_id']."' data-target='#deleteModal'>
                    <a  class='link-black text-sm delete' data-toggle='tooltip' data-original-title='Delete' href='javascript:;'><i class='fa fa-remove'></i> </a>
                    </span></li>";
            } else {
                $actionHtml .= "<li><a class='link-black text-sm' data-toggle='tooltip' data-original-title='Reject this application ?' href='".route('student-course-rejected', ['primaryId' => $row['primary_id']])."'><i class='fa fa-times-circle'></i></a></li>";
            }
        } else {

            if (isset($row['course_id'])) {

                if ($row['student_type'] == 'Domestic') {
                    $actionHtml .= '<li><a class="link-black text-sm" data-toggle="tooltip" data-original-title="Preview the Domestic offer letter before Approval" id="'.$row['id'].'" data-id="'.$row['course_id'].'" data-student-course-id="'.$row['primary_id'].'" data-load-remote-domestic="'.route('domestic-offer-letter', ['courseID' => $row['course_id'], 'id' => $row['id'], 'studentCourseID' => $row['primary_id']]).'"  href="#myModal"><i class="fa fa-search"></i></a></li>';
                } else {
                    $actionHtml .= '<li><a class="link-black text-sm" data-toggle="tooltip" data-original-title="Preview the offer letter before Approval" id="'.$row['id'].'" data-id="'.$row['course_id'].'" data-student-course-id="'.$row['primary_id'].'" data-load-remote="'.route('offer-letter', ['courseID' => $row['course_id'], 'id' => $row['id'], 'studentCourseID' => $row['primary_id']]).'"  href="#myModal"><i class="fa fa-search"></i></a></li>';
                }
            } else {
                $actionHtml .= '<li><a class="link-black text-sm" data-toggle="tooltip" data-original-title="Please Add Course Detail Before Preview" ><i class="fa  fa-search "></i> </a></li>';
            }
            $actionHtml .= "<li><a class='link-black text-sm' id='' data-toggle='tooltip' data-original-title='View Edit this Application Detail' target='_blank' href='".$applicationurl.'/'.$row['uuid'].getStudentApplicationTokenParam()."'><i class='fa fa-book'></i></a></li>";
            if ($row['offer_status'] == 'Offered') {
                $actionHtml .= '<li><a class="link-black text-sm" data-original-title="This Offer is approved" data-toggle="tooltip"><i class="fa fa-check-circle"></i> </a></li>';
            } else {
                if (isset($row['course_id'])) {
                    $actionHtml .= '<li><a class="link-black text-sm" data-toggle="tooltip" data-original-title="Approve the offer letter" href="'.route('student-approve', ['student_id' => $row['id'], 'offer_id' => $row['primary_id']]).'"><i class="fa  fa-check-circle"></i></a></li>';
                } else {
                    $actionHtml .= '<li><a class="link-black text-sm" data-original-title="Please Add Course Detail Before Preview" data-toggle="tooltip"><i class="fa fa-check-circle"></i> </a></li>';
                }
            }
            $actionHtml .= '<li><a class="link-black text-sm" data-toggle="tooltip" data-original-title="Pending Offer Letter" href="'.route('pending-offer', ['student_id' => $row['primary_id']]).'"><i class="fa fa-lock"></i></a></li>';
            $actionHtml .= '<li><span data-toggle="modal" class="delete" data-id="'.$row['primary_id'].'" data-target="#deleteModal"><a class="link-black text-sm delete" data-toggle="tooltip" data-original-title="Delete" href="javascript:;"><i class="fa fa-remove"></i></a></span></li>';
        }

        $actionHtml .= "<li><a class='link-black text-sm' data-toggle='tooltip' data-original-title='Document' href='".route('list-student-document', ['student_id' => $row['id'], 'parent_id' => '0'])."'><i class='fa fa-pen-square'></i></a></li>";
        $actionHtml .= "<li><a class='link-black text-sm' data-original-title='Send E-mail' data-toggle='tooltip' href='".route('offer-manage-send-mail', ['id' => $row['id']])."'><i class='fa fa-envelope'></i></a> </li>";
        $actionHtml .= '<li><span data-toggle="tooltip" data-original-title="Reserve ID">
                                <a class="link-black text-sm reserveStudID"
                                data-id="'.$row['id'].'"
                                data-id-type="'.$row['stud_id_type'].'"
                                data-offer-id="'.$row['offer_id'].'"
                                data-reserve-id="'.((empty($row['generated_stud_id']) || $row['generated_stud_id'] == null) ? 'true' : 'false').'"
                                data-target="'.((empty($row['generated_stud_id']) || $row['generated_stud_id'] == null) ? '#reserveIDModal' : '').'"
                                data-toggle="'.((empty($row['generated_stud_id']) || $row['generated_stud_id'] == null) ? 'modal' : 'ddd').'"
                                href="javascript:;">
                                    <i class="fa fa-user"></i>
                                </a>
                            </span></li>';
        $actionHtml .= "<li><a class='link-black text-sm' data-toggle='tooltip' data-original-title='Add/View Offer Communication' href='".route('offer-manage-communication-log', ['id' => $row['id']])."'><i class='fa fa-list-alt'></i></a></li>";
        $actionHtml .= "<li><a class='link-black text-sm' data-toggle='tooltip' data-original-title='View/Edit Upfront Fee Schedule' href='".route('offer-manage-upfront-fee-schedule-new', ['studentCourseId' => $row['primary_id']])."'><i class='fa fa-shield-alt'></i></a></li>";
        $actionHtml .= '<li><a class="link-black text-sm" data-toggle="tooltip" data-original-title="Offer Checklist" href="'.route('student-offer-checklist', ['id' => $row['id']]).'"><i class="fa fa-check"></i></a></li>';
        $actionHtml .= "<li><span data-toggle='modal' class='resetLabelId' data-student-course-id='".$row['primary_id']."' data-target='#resetOfferLabel'>
                            <a  class='link-black text-sm ' data-toggle='tooltip' data-original-title='Reset Offer Label' href='javascript:;'><i class='fa fa-history'></i> </a>
                        </span></li>";

        // If COE added then option allow to Sync with XERO
        // if(!empty($row['coe_name']) || !empty($row['coe_image'])){ }
        if (Xero::isConnected() && ! empty($row['generated_stud_id'])) {
            if (! empty($row['xeroContact'])) {
                $actionHtml .= "<li><span data-toggle='modal' class='syncData' data-target='#syncDataModel' data-student-id='".$row['id']."'>
                                    <a class='link-black text-sm' data-toggle='tooltip' data-original-title='Sync Detail' href='javascript:void(0);'><i class='fa fa-refresh'></i></a>
                                </span></li>";
                if (empty($row['xeroContact']['xero_failed_at'])) {
                    $actionHtml .= "<li><span data-toggle='modal' class='unallocatedCredit' data-target='#unallocatedCreditModel' data-student-id='".$row['id']."' data-amount='".$row['outstanding_balance']."' data-unallocatedAmount='".$row['unallocatedCredit']."'>
                                        <a class='link-black text-sm' data-toggle='tooltip' data-original-title='Unallocated Credit' href='javascript:void(0);'> <i class='fa fa-credit-card-alt'></i> </a>
                                    </span></li>";
                }
            } else {
                $actionHtml .= "<li><span data-toggle='modal' class='syncToXero' data-target='#syncToXeroModel' data-student-id='".$row['id']."' >
                                    <a class='link-black text-sm' data-toggle='tooltip' data-original-title='Sync to XERO' href='javascript:void(0);'><i class='fa fa-refresh'></i></a>
                                </span></li>";
            }
        }

        return $actionHtml;
    }

    public function getOfferMailData($perPage, $collegeId)
    {

        $studentData = Students::select('rto_students.id', 'rto_students.name_title', 'rto_students.first_name', 'rto_students.family_name', 'rto_students.created_at', 'rto_students.email')
            ->where('college_id', '=', $collegeId)
            ->orderBy('rto_students.id', 'DESC')
            ->groupBy('rto_students.id')
            ->paginate($perPage);

        for ($i = 0; $i < count($studentData); $i++) {
            $studentData[$i]['courseData'] = StudentCourse::leftjoin('rto_agents', 'rto_agents.id', '=', 'rto_student_courses.agent_id')
                ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
                ->leftjoin('rto_campus', 'rto_campus.id', '=', 'rto_student_courses.campus_id')
                ->select('rto_agents.agency_name', 'rto_student_courses.id', 'rto_student_courses.status', 'rto_courses.course_code', 'rto_courses.course_name', 'rto_campus.name as campus_name')
                ->where('rto_student_courses.student_id', '=', $studentData[$i]['id'])
                ->where('rto_student_courses.status', '!=', 'Enrolled')
                ->get();
        }

        return $studentData;
    }

    public function getOfferMailDataV2($campus, $collegeId, $status, $searchString, $searchData)
    {
        $stuResult = Students::leftjoin('rto_student_courses', 'rto_student_courses.student_id', '=', 'rto_students.id')
            ->select('rto_student_courses.offer_id', 'rto_students.*')
            ->where('rto_students.college_id', '=', $collegeId);

        if ($searchData != '') {
            if ($searchData == 'offer_id') {
                $stuResult->where('rto_student_courses.offer_id', '=', $searchString);
            } else {
                $stuResult->where('rto_students.'.$searchData, '=', $searchString);
            }
        }

        if ($status != '') {
            $stuResult->where('rto_student_courses.status', '=', $status);
        }

        $stuResult->orderBy('rto_students.id', 'DESC')->groupBy('rto_students.id');

        $studentData = $stuResult->get();

        for ($i = 0; $i < count($studentData); $i++) {
            $result = StudentCourse::leftjoin('rto_agents', 'rto_agents.id', '=', 'rto_student_courses.agent_id')
                ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
                ->leftjoin('rto_campus', 'rto_campus.id', '=', 'rto_student_courses.campus_id')
                ->select('rto_agents.agency_name', 'rto_student_courses.*', 'rto_courses.course_code', 'rto_courses.course_name', 'rto_campus.name as campus_name');

            $result->where('rto_student_courses.student_id', '=', $studentData[$i]['id'])
                ->where('rto_student_courses.status', '!=', 'Enrolled');
            if ($status != '') {
                $result->where('rto_student_courses.status', '=', $status);
            }
            if ($campus != '') {
                $result->where('rto_student_courses.campus_id', '=', $campus);
            }

            $finalResult = $result->get();
            $studentData[$i]['courseData'] = $finalResult;
        }

        return $studentData;
    }

    public function searchStudentsAccount($dropDownId, $searchValue, $collegeId)
    {

        if (! empty($dropDownId)) {
            return Students::where('college_id', '=', $collegeId)
                ->where($dropDownId, 'like', '%'.$searchValue.'%')
                ->where('is_student', '=', 1)
                ->orderBy('id', 'DESC')
                ->get();
        } else {
            return Students::where('college_id', '=', $collegeId)
                ->where('is_student', '=', 1)
                ->orderBy('id', 'DESC')
                ->get();
        }
    }

    public function viewStudentPayment($id, $collegeId)
    {

        //   $collegeId = Auth::user()->college_id;
        return Students::where('college_id', '=', $collegeId)->where('id', '=', $id)->get(['generated_stud_id', 'first_name', 'middel_name', 'family_name', 'current_country', 'current_building_name', 'current_street_no', 'current_unit_detail', 'current_street_name', 'current_city', 'current_state', 'current_postcode']);
    }

    public function saveStudentProfilePicture($studentId, $request)
    {

        $parent_id = $request->id;
        $userId = Auth::user()->id;
        $collegeId = Auth::user()->college_id;
        $studentsInfo = Students::find($studentId);

        $rootFolder = Config::get('constants.arrCollegeRootFolder');
        $dataArr = [
            'college_id' => $collegeId,
            'folder_name' => $rootFolder['StudentPics'],
            'sub_folder_name' => $studentId,
            'user_id' => $userId,
        ];

        $objCommonModel = new CommonModel;
        $clgMaterialParentId = $objCommonModel->getSubParentId($dataArr);

        if (! empty($request['images'])) {
            $files = $request->file('images');
            $file_count = count($files);
            $uploadcount = 0;
            $count = 0;
            foreach ($files as $file) {
                $rules = ['file' => 'required'];
                $validator = Validator::make(['file' => $file], $rules);
                if ($validator->passes()) {
                    $originalName = $file->getClientOriginalName();
                    $time = time();
                    $originalName = $file->getClientOriginalName();
                    $filenm = hashFileName($originalName);
                    $fileName = $this->removeSpecialCharacter($filenm);
                    $file_size = $file->getSize();
                    $fileSize = ceil($file_size / 1024);

                    $filePath = Config::get('constants.uploadFilePath.StudentPics');
                    $destinationPath = Helpers::changeRootPath($filePath, $studentId, $collegeId);
                    $this->generateThumbImage($file, $destinationPath['default'], $fileName);

                    $filePath = Config::get('constants.uploadFilePath.StudentPics');
                    $destinationPath = Helpers::changeRootPath($filePath, $studentId);
                    $upload_success = $file->move($destinationPath['default'], $fileName);
                    $uploadcount++;

                    $agentDataArr = [
                        'college_id' => $collegeId,
                        'original_name' => $originalName,
                        'file_name' => $fileName,
                        'size' => $fileSize,
                        'type' => 'File',
                        'parent_id' => $clgMaterialParentId,
                        'file_path' => $destinationPath['view'],
                        'user_id' => $userId,
                    ];
                    $res = $objCommonModel->addCollegeMaterialInfo($agentDataArr);
                    if ($res && $count == 0) {
                        $studentsInfo->profile_picture = $fileName;
                        $studentsInfo->updated_by = $userId;
                        $studentsInfo->save();
                        $count++;
                    }
                }
            }

            return true;
        }

        return false;
    }

    // THIS FUNCTION IS REMOVE SPECIAL CHARACTER FROM THE FILE NAME.
    public function removeSpecialCharacter($fileName)
    {
        $fileName = str_replace(' ', '-', $fileName); // Replaces all spaces with hyphens.

        return preg_replace('/[^A-Za-z0-9.\-]/', '', $fileName); // Removes special chars.
    }

    public function getServicePaymentDetail($collegeId, $studentId)
    {
        return Students::from('rto_students as rs')
            ->leftjoin('rto_student_courses as rsc', 'rsc.student_id', '=', 'rs.id')
            ->leftjoin('rto_student_details as rsd', 'rsd.student_id', '=', 'rs.id')
            ->leftjoin('rto_oshc_providers as rop', 'rop.id', '=', 'rsd.OSHC_provider')
            ->where('rs.college_id', '=', $collegeId)
            ->where('rs.id', '=', $studentId)
            ->groupBy('rsc.offer_id')
            ->get(['rs.generated_stud_id',
                'rs.first_name',
                'rs.family_name',
                'rsc.offer_id',
                'rsd.home_stay',
                'rsd.airport_pickup',
                'rsd.arrange_OSHC',
                'rsd.arrival_date',
                'rop.provider_name']);
    }

    public function getStudentHasGroup($groupId, $orderBy, $ascDescOrder, $filterType, $campusId = '')
    {

        if ($groupId == '') {
            return [];
        }
        $collegeId = Auth::user()->college_id;
        $arrStudentHasGroup = Students::leftjoin('rto_student_courses', 'rto_student_courses.student_id', '=', 'rto_students.id')
            ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->leftjoin('rto_campus', 'rto_campus.id', '=', 'rto_student_courses.campus_id')
            ->leftjoin('rto_study_reasons', 'rto_study_reasons.id', '=', 'rto_student_courses.study_reason_id')
            ->where('rto_student_courses.group_id', '=', $groupId)
            ->where('rto_students.college_id', '=', $collegeId)
            ->groupBy('rto_students.id')
            ->select('rto_students.id as studentid', 'rto_student_courses.id as studentcourseid', 'rto_students.student_type', 'rto_students.generated_stud_id', 'rto_student_courses.group_id', DB::raw('concat(rto_students.first_name, " ", rto_students.family_name) as student_name'), 'rto_students.first_name', 'rto_campus.name as campus_name', 'rto_courses.course_code', 'rto_courses.course_name', 'rto_student_courses.course_attempt', 'rto_student_courses.study_reason_id', 'rto_student_courses.start_date', 'rto_student_courses.finish_date', 'rto_study_reasons.title as reasionTitle', 'rto_student_courses.status');
        if ($orderBy != '' && $orderBy == 'StudentId') {
            $arrStudentHasGroup->orderBy('rto_students.generated_stud_id', $ascDescOrder);
        }
        if ($orderBy != '' && $orderBy == 'StudentName') {
            $arrStudentHasGroup->orderBy('rto_students.first_name', $ascDescOrder);
        }
        if ($orderBy != '' && $orderBy == 'StartDate') {
            $arrStudentHasGroup->orderBy('rto_student_courses.start_date', $ascDescOrder);
        }
        if ($filterType == 'Domestic') {
            $arrStudentHasGroup->where('rto_students.student_type', 'Domestic');
        } else {
            $whereInArray = ['Offshore', 'Onshore'];
            $arrStudentHasGroup->whereIn('rto_students.student_type', $whereInArray);
        }
        if ($campusId != '') {
            $arrStudentHasGroup->where('rto_student_courses.campus_id', $campusId);
        }
        $arrStuden = $arrStudentHasGroup->get();

        return $arrStuden;
    }

    public function getSemsterUnenrollStudent($groupId, $orderBy, $ascDescOrder, $filterType, $students)
    {
        $studentArr = explode(',', $students);

        if ($groupId == '') {
            return [];
        }
        $collegeId = Auth::user()->college_id;
        $arrStudentHasGroup = Students::leftjoin('rto_student_courses', 'rto_student_courses.student_id', '=', 'rto_students.id')
            ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->leftjoin('rto_campus', 'rto_campus.id', '=', 'rto_student_courses.campus_id')
            ->leftjoin('rto_study_reasons', 'rto_study_reasons.id', '=', 'rto_student_courses.study_reason_id')
            ->leftjoin('rto_student_subject_enrolment', 'rto_student_subject_enrolment.student_id', '=', 'rto_students.id')
            ->where('rto_student_courses.group_id', '=', $groupId)
            ->where('rto_students.college_id', '=', $collegeId)
            ->groupBy('rto_students.id')
            ->select('rto_students.id as studentid', 'rto_students.student_type', 'rto_students.generated_stud_id', 'rto_student_courses.group_id', DB::raw('concat(rto_students.first_name, " ", rto_students.family_name) as student_name'), 'rto_students.first_name', 'rto_campus.name as campus_name', 'rto_courses.course_code', 'rto_courses.course_name', 'rto_student_courses.course_attempt', 'rto_student_courses.study_reason_id', 'rto_student_courses.start_date', 'rto_student_courses.finish_date', 'rto_study_reasons.title as reasionTitle', 'rto_student_courses.status');
        if ($orderBy != '' && $orderBy == 'StudentId') {
            $arrStudentHasGroup->orderBy('rto_students.generated_stud_id', $ascDescOrder);
        }
        if ($orderBy != '' && $orderBy == 'StudentName') {
            $arrStudentHasGroup->orderBy('rto_students.first_name', $ascDescOrder);
        }
        if ($orderBy != '' && $orderBy == 'StartDate') {
            $arrStudentHasGroup->orderBy('rto_student_courses.start_date', $ascDescOrder);
        }
        if ($filterType == 'Domestic') {
            $arrStudentHasGroup->where('rto_students.student_type', 'Domestic');
        } else {
            $whereInArray = ['Offshore', 'Onshore'];
            $arrStudentHasGroup->whereIn('rto_students.student_type', $whereInArray);
        }
        $arrStuden = $arrStudentHasGroup->get();

        return $arrStuden;
    }

    public function getStudentHasGroupEnrolled($groupId, $courseId, $semesterId, $termId, $batchId)
    {
        $collegeId = Auth::user()->college_id;

        return Students::leftjoin('rto_student_subject_enrolment', 'rto_student_subject_enrolment.student_id', '=', 'rto_students.id')
            ->where('rto_student_subject_enrolment.college_id', '=', $collegeId)
            ->where('rto_student_subject_enrolment.course_id', '=', $courseId)
            ->where('rto_student_subject_enrolment.semester_id', '=', $semesterId)
            ->where('rto_student_subject_enrolment.term', '=', $termId)
            ->where('rto_student_subject_enrolment.batch', '=', $batchId)
            ->leftjoin('rto_student_courses', 'rto_student_courses.student_id', '=', 'rto_student_subject_enrolment.student_id')
            ->where('rto_student_courses.group_id', '=', $groupId)
            ->groupBy('rto_students.id')
            ->get(['rto_students.id as studentid',
                'rto_students.generated_stud_id',
                'rto_students.first_name',
                'rto_student_courses.course_attempt',
                'rto_student_courses.start_date',
                'rto_student_courses.finish_date',
                'rto_student_courses.offer_status as status',
                'rto_student_courses.status as studentStatus',
            ]);
    }

    public function classStudentListArr($collegeId, $dataArr)
    {

        $subjectId = $batch = '';
        if ((! empty($dataArr['subject'])) && (preg_match('/@/', $dataArr['subject']))) {
            $arrSubject = explode('@', $dataArr['subject']);
            $subjectId = $arrSubject[0];
            $batch = $arrSubject[1];
        }
        $sql = Students::from('rto_students as rs')
            ->leftjoin('rto_student_subject_enrolment as rees', 'rees.student_id', '=', 'rs.id')
            ->leftjoin('rto_student_courses as rsc', 'rsc.student_id', '=', 'rs.id')
            ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->leftjoin('rto_subject as rsub', 'rsub.id', '=', 'rees.subject_id')
            ->where('rs.college_id', '=', $collegeId)
            ->where('rc.course_type_id', '=', $dataArr['courseType'])
            ->where('rc.id', '=', $dataArr['course'])
            ->where('rees.subject_id', '=', $subjectId)
            ->where('rees.batch', '=', $batch)
            ->groupBy('rs.id');

        if (isset($dataArr['activeCourse']) && $dataArr['activeCourse'] != '') {
            $sql->where('rc.activated_now', '=', $dataArr['activeCourse']);
        }
        if ($dataArr['vanueFilter'] != '') {
            $sql->where('rees.vanue_location', '=', $dataArr['vanueFilter']);
        }
        $sql->where('rees.semester_id', '=', $dataArr['semester']);
        $sql->where('rees.term', '=', $dataArr['term']);
        $result = $sql->get(['rs.id as studentid',
            'rs.generated_stud_id', 'rs.name_title', 'rs.first_name', 'rs.family_name',
            'rc.course_code', 'rc.course_name', 'rsc.course_attempt', 'rsc.status',
            'rsc.course_upfront_fee', 'rsc.course_fee', 'rsc.enroll_fee', 'rsc.course_material_fee',
            'rsub.subject_code', 'rsub.subject_name', 'rees.activity_start_date as dt_start',
            'rees.activity_finish_date as dt_end', 'rees.final_outcome',
            'rees.student_id as sub_student_id ', 'rees.course_id as sub_course_id',
            'rees.semester_id as sub_semester_id', 'rees.subject_id as sub_subject_id',
            'rees.id']);

        for ($i = 0; $i < count($result); $i++) {
            $result[$i]['dt_start'] = date('d/m/Y', strtotime($result[$i]['dt_start']));
            $result[$i]['dt_end'] = date('d/m/Y', strtotime($result[$i]['dt_end']));
        }

        return $result;
    }

    public function classStudentListArrV2($collegeId, $dataArr)
    {

        $subjectId = $batch = '';
        if ((! empty($dataArr['subject'])) && (preg_match('/@/', $dataArr['subject']))) {
            $arrSubject = explode('@', $dataArr['subject']);
            $subjectId = $arrSubject[0];
            $batch = $arrSubject[1];
        }
        $sql = Students::from('rto_students as rs')
            ->leftjoin('rto_student_subject_enrolment as rees', 'rees.student_id', '=', 'rs.id')
            ->leftjoin('rto_student_courses as rsc', 'rsc.student_id', '=', 'rs.id')
            ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->leftjoin('rto_subject as rsub', 'rsub.id', '=', 'rees.subject_id')
            ->where('rs.college_id', '=', $collegeId)
            ->where('rc.course_type_id', '=', $dataArr['courseType'])
            ->where('rc.id', '=', $dataArr['course'])
            ->where('rees.subject_id', '=', $dataArr['subjectCourse'])
            ->groupBy('rs.id');
        if (! empty($dataArr['activeCourse'])) {
            $sql->where('rc.activated_now', '=', $dataArr['activeCourse']);
        }
        if ($dataArr['vanueFilter'] != '') {
            $sql->where('rees.vanue_location', '=', $dataArr['vanueFilter']);
        }
        $sql->where('rees.semester_id', '=', $dataArr['semester']);
        $sql->where('rees.term', '=', $dataArr['term']);
        $result = $sql->get(['rs.id as studentid',
            'rs.generated_stud_id',
            'rs.name_title',
            'rs.first_name',
            'rs.family_name',
            'rc.course_code',
            'rc.course_name',
            'rsc.course_attempt',
            'rsc.status',
            'rsub.subject_code',
            'rsub.subject_name',
            'rees.activity_start_date as dt_start',
            'rees.activity_finish_date as dt_end',
            'rees.final_outcome',
            'rees.student_id as sub_student_id ',
            'rees.course_id as sub_course_id',
            'rees.semester_id as sub_semester_id',
            'rees.subject_id as sub_subject_id',
            'rees.id']);

        for ($i = 0; $i < count($result); $i++) {
            $result[$i]['dt_start'] = date('d/m/Y', strtotime($result[$i]['dt_start']));
            $result[$i]['dt_end'] = date('d/m/Y', strtotime($result[$i]['dt_end']));
        }

        return $result;
    }

    public function unitStudentListArr($collegeId, $dataArr)
    {

        $subjectId = $batch = '';
        if ((! empty($dataArr['subject'])) && (preg_match('/@/', $dataArr['subject']))) {
            $arrSubject = explode('@', $dataArr['subject']);
            $subjectId = $arrSubject[0];
            $batch = $arrSubject[1];
        }
        if ($dataArr['criteria'] == 0) {
            $subjectId = $dataArr['subjectCourse'];
        }

        $dataArrQuery = Students::from('rto_students as rs')
            ->leftjoin('rto_student_subject_enrolment as rees', 'rees.student_id', '=', 'rs.id')
            ->leftjoin('rto_subject_unit as rsu', 'rsu.id', '=', 'rees.unit_id')
            ->leftjoin('rto_student_unit_enrollment as rsue', 'rsue.student_subject_enrollment_id', '=', 'rees.id')
            ->leftjoin('rto_student_courses as rsc', 'rsc.student_id', '=', 'rs.id')
            ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->leftjoin('rto_subject as rsub', 'rsub.id', '=', 'rees.subject_id')
            ->where('rs.college_id', '=', $collegeId)
            ->where('rc.course_type_id', '=', $dataArr['courseType'])
            ->where('rc.id', '=', $dataArr['course'])
            ->where('rees.subject_id', '=', $subjectId)
            ->where('rees.unit_id', '=', $dataArr['unitId']);
        if ($dataArr['criteria'] == 1) {
            $dataArrQuery->where('rees.batch', '=', $batch);
        }
        $dataArrQuery->groupBy('rs.id');
        $result = $dataArrQuery->get(['rs.id as studentid',
            'rs.generated_stud_id',
            'rs.name_title',
            'rs.first_name',
            'rs.family_name',
            'rc.course_code',
            'rc.course_name',
            'rsc.course_attempt',
            'rsc.status',
            'rsc.is_claim',
            'rsu.unit_code',
            'rsu.unit_name',
            'rsub.subject_code',
            'rsub.subject_name',
            'rees.activity_start_date as dt_start',
            'rees.activity_finish_date as dt_end',
            'rsue.compentency',
            'rees.id']);

        for ($i = 0; $i < count($result); $i++) {
            $result[$i]['dt_start'] = date('d/m/Y', strtotime($result[$i]['dt_start']));
            $result[$i]['dt_end'] = date('d/m/Y', strtotime($result[$i]['dt_end']));
        }

        return $result;
    }

    public function unitStudentListArrV2($collegeId, $dataArr)
    {
        $subjectId = $batch = '';
        if ((! empty($dataArr['subject'])) && (preg_match('/@/', $dataArr['subject']))) {
            $arrSubject = explode('@', $dataArr['subject']);
            $subjectId = $arrSubject[0];
            $batch = $arrSubject[1];
        }

        $result = Students::from('rto_students as rs')
            ->leftjoin('rto_student_subject_enrolment as rees', 'rees.student_id', '=', 'rs.id')
            ->leftjoin('rto_subject_unit as rsu', 'rsu.id', '=', 'rees.unit_id')
            ->leftjoin('rto_student_unit_enrollment as rsue', 'rsue.student_subject_enrollment_id', '=', 'rees.id')
            ->leftjoin('rto_student_courses as rsc', 'rsc.student_id', '=', 'rs.id')
            ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->leftjoin('rto_subject as rsub', 'rsub.id', '=', 'rees.subject_id')
            ->where('rs.college_id', '=', $collegeId)
            ->where('rc.course_type_id', '=', $dataArr['courseType'])
            ->where('rc.id', '=', $dataArr['course'])
            ->where('rees.subject_id', '=', $subjectId)
            ->where('rees.unit_id', '=', $dataArr['unitId'])
            ->groupBy('rs.id')
            ->get(['rs.id as studentid',
                'rs.generated_stud_id',
                'rs.name_title',
                'rs.first_name',
                'rs.family_name',
                'rc.course_code',
                'rc.course_name',
                'rsc.course_attempt',
                'rsc.status',
                'rsu.unit_code',
                'rsu.unit_name',
                'rsub.subject_code',
                'rsub.subject_name',
                'rees.activity_start_date as dt_start',
                'rees.activity_finish_date as dt_end',
                'rsue.compentency',
                'rees.id']);

        for ($i = 0; $i < count($result); $i++) {
            $result[$i]['dt_start'] = date('d/m/Y', strtotime($result[$i]['dt_start']));
            $result[$i]['dt_end'] = date('d/m/Y', strtotime($result[$i]['dt_end']));
        }

        return $result;
    }

    public function getStudentDetailForXls($collegeId, $arrFilter)
    {

        // $form_date = date('Y-m-d', strtotime($arrFilter['from_date']));
        // $to_date = date('Y-m-d', strtotime($arrFilter['to_date']));

        $sql = StudentSubjectEnrolment::leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_subject_enrolment.course_id')
            ->leftjoin('rto_venue', 'rto_venue.id', '=', 'rto_student_subject_enrolment.vanue_location')
            ->leftjoin('rto_students', 'rto_students.id', '=', 'rto_student_subject_enrolment.student_id')
            ->leftjoin('rto_country as sc', 'sc.id', '=', 'rto_students.birth_country')
            ->leftjoin('rto_student_details as rsd', 'rsd.student_id', '=', 'rto_students.id')
            ->leftjoin('rto_language as lang', 'lang.id', '=', 'rsd.main_lang')
            ->leftjoin('rto_setup_section as rss1', 'rss1.id', '=', 'rto_students.visa_status')
            ->leftjoin('rto_setup_section as rss2', 'rss2.id', '=', 'rsd.EPL_test_name')
            ->leftjoin('rto_student_unit_enrollment as sue', 'sue.student_subject_enrollment_id', '=', 'rto_student_subject_enrolment.id')
            ->leftjoin('rto_student_education', 'rto_student_education.student_id', '=', 'rto_students.id')
            ->leftJoin('rto_student_courses', function ($join) {
                $join->on('rto_student_courses.student_id', '=', 'rto_student_subject_enrolment.student_id');
                $join->on('rto_student_courses.course_id', '=', 'rto_student_subject_enrolment.course_id');
            })
            ->leftjoin('rto_colleges', 'rto_colleges.id', '=', 'rto_student_subject_enrolment.college_id')
            ->leftjoin('rto_subject_unit', 'rto_subject_unit.id', '=', 'sue.unit_id')
            ->where('rto_student_subject_enrolment.college_id', '=', Auth::user()->college_id)
            ->groupBy('rto_students.id')
            ->orderBy('rto_students.id');

        $sql = $this->applyGlobalFilter($sql, $arrFilter, 'nat80');
        // if (!empty($arrFilter['year'])) {
        //     if($arrFilter['reportType']==1 || $arrFilter['reportType']==2){
        //         $year = $arrFilter['year'];

        //         $sql->where(function($nest) use($year) {
        //             $nest->whereYear('rto_student_subject_enrolment.activity_finish_date', '=',$year);
        //             $nest->orWhereYear('rto_student_subject_enrolment.activity_start_date', '=', $year);
        //         });

        //     }
        // }
        // if (!empty($arrFilter['month']) && $arrFilter['reportType'] == 2) {
        //     $sql->whereMonth('rto_student_subject_enrolment.activity_finish_date', '=', $arrFilter['month']);
        // }
        // if (!empty($arrFilter['stateName']) && $arrFilter['export_type'] == 1) {
        //    $sql->where('rto_students.current_state', '=', $arrFilter['stateName']);
        // }
        // if (!empty($arrFilter['from_date']) && !empty($arrFilter['to_date']) && $arrFilter['reportType'] == 3) {
        //     $sql->whereBetween('rto_student_subject_enrolment.activity_finish_date', array($form_date, $to_date));
        // }
        // if (!empty($arrFilter['claim_only']) && $arrFilter['claim_only'] == 1) {
        //     $sql->where('rto_student_courses.is_claim', '=', ($arrFilter['claim_only']));
        // }

        $sql = $this->applySmartAndSkillFilterWithStudent($sql, $arrFilter);

        $result = $sql->get(['rto_students.*', 'rsd.*', 'lang.abs_value as language_abs_value',
            'sc.nationality', 'sc.nationality as stud_nationality', 'sc.absvalue as country_name',
            'rss1.value as visaStatus', 'rss2.value as EPL_test_name', 'rto_venue.state',
            'rto_student_subject_enrolment.activity_start_date',
            'rto_student_subject_enrolment.activity_finish_date', 'rsd.scs as survey_contact_status',
            'rto_student_education.qualification_level_id']);

        return $result;
    }

    public function getStudentDetailForXls100($collegeId, $arrFilter)
    {

        $sql = StudentSubjectEnrolment::leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_subject_enrolment.course_id')
            ->leftjoin('rto_venue', 'rto_venue.id', '=', 'rto_student_subject_enrolment.vanue_location')
            ->leftjoin('rto_students', 'rto_students.id', '=', 'rto_student_subject_enrolment.student_id')
            ->leftjoin('rto_student_education', 'rto_student_education.student_id', '=', 'rto_students.id')
            ->leftjoin('rto_student_details as rsd', 'rsd.student_id', '=', 'rto_students.id')
            ->leftJoin('rto_student_courses', function ($join) {
                $join->on('rto_student_courses.student_id', '=', 'rto_student_subject_enrolment.student_id');
                $join->on('rto_student_courses.course_id', '=', 'rto_student_subject_enrolment.course_id');
            })
            ->orderby('rto_students.id')
            ->where('rto_students.college_id', '=', $collegeId);

        $sql->groupBy('rto_students.id');

        $sql = $this->applyGlobalFilter($sql, $arrFilter, 'nat100');
        // if (!empty($arrFilter['year'])) {
        //     if($arrFilter['reportType']==1 || $arrFilter['reportType']==2){
        //         $year = $arrFilter['year'];

        //         $sql->where(function($nest) use($year) {
        //             $nest->whereYear('rto_student_subject_enrolment.activity_finish_date', '=',$year);
        //             $nest->orWhereYear('rto_student_subject_enrolment.activity_start_date', '=', $year);
        //         });

        //     }
        // }
        // if (!empty($arrFilter['month']) && $arrFilter['reportType'] == 2) {
        //     $sql->whereMonth('rto_student_subject_enrolment.activity_start_date', '=', $arrFilter['month']);
        // }
        // if (!empty($arrFilter['stateName']) && $arrFilter['export_type'] == 1) {
        //     $sql->where('rto_students.current_state', '=', $arrFilter['stateName']);
        // }
        // if (!empty($arrFilter['from_date']) && !empty($arrFilter['to_date']) &&  $arrFilter['reportType'] == 3) {
        //     $sql->whereBetween('rto_student_subject_enrolment.activity_start_date', array($form_date, $to_date));
        // }
        // if (!empty($arrFilter['claim_only']) && $arrFilter['claim_only'] == 1) {
        //     $sql->where('rto_student_courses.is_claim', '=', $arrFilter['claim_only']);
        // }

        $sql = $this->applySmartAndSkillFilterWithStudent($sql, $arrFilter);

        $result = $sql->get(['rto_students.*', 'rto_student_education.qualification_level_id',
            'rto_student_subject_enrolment.activity_start_date',
            'rto_student_subject_enrolment.activity_finish_date', 'rsd.*']);

        return $result;
    }

    public function generateApplicationReport($perPage, $query, $collegeId)
    {

        $sql = Students::leftjoin('rto_country', 'rto_country.id', '=', 'rto_students.nationality')
            ->select('application_reference_id', 'rto_students.created_at', 'first_name', 'family_name', 'current_situation', 'DOB', 'rto_country.nationality', 'gender', 'email', 'passport_no')
            ->where('rto_students.college_id', '=', $collegeId)
            ->where('rto_students.is_applicant', '=', '1');
        if ($query != '') {
            $sql->where('rto_students.application_reference_id', '=', $query);
            $sql->orwhere('rto_students.first_name', '=', $query);
            $sql->orwhere('rto_students.family_name', '=', $query);
            $sql->orwhere('rto_students.passport_no', '=', $query);
            if (strtotime($query)) {
                $sql->orwhere('rto_students.DOB', '=', date('Y-m-d', strtotime($query)));
            }
        }
        if (isset($perPage)) {
            return $sql->paginate($perPage);
        } else {
            return $sql->get();
        }
    }

    public function getStudentCountByCountryId($countryId, $collegeId)
    {

        return Students::where('college_id', '=', $collegeId)
            ->where('birth_country', '=', $countryId)
            ->orwhere('current_country', '=', $countryId)
            ->orwhere('nationality', '=', $countryId)
            ->orwhere('permanent_country', '=', $countryId)
            ->orwhere('postal_country', '=', $countryId)
            ->count();
    }

    public function getStudentListMissingUSINumbers($perPage, $courseTypeId, $courseId, $status, $fromDate, $toDate, $collegeId, $isxls = null)
    {
        $sql = Students::leftjoin('rto_student_courses', 'rto_student_courses.student_id', '=', 'rto_students.id')
            ->leftjoin('rto_visa_status', 'rto_visa_status.id', '=', 'rto_students.visa_status')
            ->leftjoin('rto_country as bc', 'bc.id', '=', 'rto_students.birth_country')
            ->leftjoin('rto_country as nc', 'nc.id', '=', 'rto_students.nationality')
            ->where('rto_students.college_id', '=', $collegeId)
            ->where('rto_students.USI', '=', '')
            ->select('bc.name as birthcountry', 'nc.name as nationality_name', 'rto_visa_status.name as visa_type', 'rto_students.*');
        if ($courseTypeId != '') {
            $sql->where('rto_student_courses.course_type_id', '=', $courseTypeId);
        }
        if ($courseId != '') {
            $sql->where('rto_student_courses.course_id', '=', $courseId);
        }
        if ($status != '' && $status != '0') {
            $sql->where('rto_student_courses.status', '=', $status);
        }
        if ($fromDate != '' && $fromDate != 0) {
            $checkFromDate = date('Y-m-d', strtotime($fromDate));
            $sql->where('rto_student_courses.start_date', '>=', $checkFromDate);
        }
        if ($toDate != '') {
            $checkToDate = date('Y-m-d', strtotime($toDate));
            $sql->where('rto_student_courses.start_date', '<=', $checkToDate);
        }
        $sql->groupBy('rto_students.id');
        if (! empty($perPage)) {
            return $sql->paginate($perPage);
        } else {
            return $sql->get();
        }
    }

    public function getGeneratedStudentId($studentId)
    {

        return Students::where('id', '=', $studentId)
            ->get(['generated_stud_id'])->toarray();
    }

    public function getStudentEmailId($studentId)
    {

        return Students::where('id', '=', $studentId)
            ->get(['email'])->toarray();
    }

    public function getStudentNationalReport($collegeId, $userId)
    {

        $agentId = Agent::where('college_id', $collegeId)->where('user_id', $userId)->value('id');

        return Students::leftjoin('rto_student_courses', 'rto_students.id', '=', 'rto_student_courses.student_id')
            ->leftjoin('rto_country', 'rto_students.current_country', '=', 'rto_country.id')
            ->where('rto_student_courses.agent_id', $agentId)
            ->where('rto_student_courses.offer_status', 'Enrolled')
            ->groupBy(['rto_country.id'])
            ->get(['rto_country.name as countryName', DB::raw('COUNT( rto_students.`id`) AS total_students')])
            ->toarray();
    }

    public function getStudentListFilter($collegeId, $dataArr)
    {
        // echo "<pre>";print_r($dataArr);exit;
        $sql = Students::from('rto_students as rs')
            ->leftjoin('rto_country as rc', 'rc.id', '=', 'rs.birth_country')
            ->leftjoin('rto_student_courses as rsc', 'rsc.student_id', '=', 'rs.id')
            ->where('rs.college_id', '=', $collegeId)
            ->where('rs.generated_stud_id', '!=', null);

        if ($dataArr['usiFilter'] == 'has_usi') {
            $sql->where('rs.USI', '!=', '');
        } elseif ($dataArr['usiFilter'] == 'no_usi') {
            $sql->where('rs.USI', '=', '');
        }

        if ($dataArr['courseType'] != 'all') {
            $sql->where('rsc.course_type_id', '=', $dataArr['courseType']);
        }

        if ($dataArr['dvsFilter'] == 'has_dvs') {
            $sql->where('rs.DVS', '=', 'YES');
        } elseif ($dataArr['dvsFilter'] == 'no_dvs') {
            $sql->where('rs.DVS', '=', 'NO');
        }

        $viewBy = $dataArr['search_by'];
        $string = $dataArr['string'];

        if (! empty($string)) {
            if ($viewBy == 'generated_stud_id') {
                $sql->where('rs.'.$viewBy, '=', $string);
            } elseif ($viewBy == 'DOB') {
                $date = date('Y-m-d', strtotime($string));
                $sql->where('rs.'.$viewBy, 'like', "%$date%");
            } elseif ($viewBy == 'first_name' ||
                    $viewBy == 'family_name' ||
                    $viewBy == 'student_type' ||
                    $viewBy == 'email' ||
                    $viewBy == 'current_mobile_phone') {

                $sql->where('rs.'.$viewBy, 'like', "%$string%");
            } elseif ($viewBy == 'status') {
                $sql->where('rsc.'.$viewBy, 'like', "%$string%");
            } elseif ($viewBy == 'start_date') {
                $start_date = date('Y-m-d', strtotime($string));
                $sql->where('rsc.'.$viewBy, 'like', "%$start_date%");
            } elseif ($viewBy == 'offer_id') {
                $sql->where('rsc.'.$viewBy, '=', "$string");
            }
        }

        // $sql->skip(0)->take(10);
        return $result = $sql->groupBy('rs.id')->get(['rs.*', 'rc.name as country_name']);
    }

    public function getStudentListFilterv2($collegeId, $requestArr)
    {
        $dataArr = $requestArr['data'];
        $sql = Students::from('rto_students as rs')
            ->join('rto_student_courses as rsc', 'rsc.student_id', '=', 'rs.id')
            ->where('rs.college_id', '=', $collegeId)
            ->where('rs.generated_stud_id', '!=', null);

        if ($dataArr['usiFilter'] == 'has_usi') {
            $sql->where('rs.USI', '!=', '');
        } elseif ($dataArr['usiFilter'] == 'no_usi') {
            $sql->where('rs.USI', '=', '');
        }
        if ($dataArr['usiFilter'] == 'has_verified_usi') {
            $sql->where('rs.USI', '!=', '')
                ->where('rs.is_usi_verified', '=', 1);
        } elseif ($dataArr['usiFilter'] == 'has_unverified_usi') {
            $sql->where('rs.USI', '!=', '')
                ->where('rs.is_usi_verified', '=', 0);
        }

        if ($dataArr['courseType'] != 'all') {
            $sql->where('rsc.course_type_id', '=', $dataArr['courseType']);
        }

        //        note: disable for now
        //        if ($dataArr['dvsFilter'] == 'has_dvs') {
        //            $sql->where('rs.DVS', '=', 'YES');
        //        } elseif ($dataArr['dvsFilter'] == 'no_dvs') {
        //            $sql->where('rs.DVS', '=', 'NO');
        //        }

        $viewBy = $dataArr['search_by'];
        $string = $dataArr['string'];

        if (! empty($string)) {
            if ($viewBy == 'generated_stud_id') {
                $sql->where('rs.'.$viewBy, '=', $string);
            } elseif ($viewBy == 'DOB') {
                $date = date('Y-m-d', strtotime($string));
                $sql->where('rs.'.$viewBy, 'like', "%$date%");
            } elseif ($viewBy == 'first_name' ||
                    $viewBy == 'family_name' ||
                    $viewBy == 'student_type' ||
                    $viewBy == 'email' ||
                    $viewBy == 'current_mobile_phone') {

                $sql->where('rs.'.$viewBy, 'like', "%$string%");
            } elseif ($viewBy == 'status') {
                $sql->where('rsc.'.$viewBy, 'like', "%$string%");
            } elseif ($viewBy == 'start_date') {
                $start_date = date('Y-m-d', strtotime($string));
                $sql->where('rsc.'.$viewBy, 'like', "%$start_date%");
            } elseif ($viewBy == 'offer_id') {
                $sql->where('rsc.'.$viewBy, '=', "$string");
            }
        }

        $columns = [
            // datatable column index  => database column name
            0 => 'rs.generated_stud_id',
            1 => 'rs.name_title',
            2 => 'rs.first_name',
            3 => 'rs.family_name',
            4 => 'rs.nickname',
            5 => 'rs.gender',
            6 => 'rs.student_type',
            7 => 'rs.DOB',
            8 => 'rs.USI',
            9 => 'rs.email',
            10 => 'rs.is_usi_verified',
        ];

        if (! empty($requestArr['search']['value'])) {   // if there is a search parameter, $requestArr['search']['value'] contains search parameter
            $searchVal = $requestArr['search']['value'];
            $sql->where(function ($sql) use ($columns, $searchVal, $requestArr) {

                $flag = 0;
                foreach ($columns as $key => $value) {
                    $searchVal = $requestArr['search']['value'];
                    if ($key == 7) {
                        $searchVal = date('Y-m-d', strtotime($searchVal));
                    }
                    if ($requestArr['columns'][$key]['searchable'] == 'true') {
                        if ($flag == 0) {
                            $sql->where($value, 'like', '%'.$searchVal.'%');
                            $flag = $flag + 1;
                        } else {
                            $sql->orWhere($value, 'like', '%'.$searchVal.'%');
                        }
                    }
                }
            });
        }

        $totalData = ($sql->groupBy('rs.id')->get(['rs.*'])->count());

        $sql->skip($requestArr['start'])->take($requestArr['length']);
        $resultArr = $sql->groupBy('rs.id')->get(['rs.*'])->toArray();

        $data = [];
        foreach ($resultArr as $row) {

            $nestedData = [];
            $actionHtml = '';
            $studentId = $row['id'];
            $actionHtml = '<li>
                                <a data-student-id="'.$studentId.'" class="link-black text-sm student_usi_verify_btn" data-toggle="tooltip" data-original-title="USI Verify for this student whether its matching correctly to the student first & last name and dob or not with USI web service call" href="javascript:;"><i class="fa fa-file-signature"></i> </a>
                            </li>
                            <li>
                                <a class="link-black text-sm updateStudentData" data-toggle="tooltip" data-original-title="Update data for this selected student" href="javascript:;" data-stud-id="'.$row['id'].'" ><i class="fa fa-check"></i> </a>
                            </li>
                            <li>
                                <span data-toggle="modal" class="dvsModal" data-stud-id="'.$row['id'].'" data-student-id="'.$row['generated_stud_id'].'" data-target="#dvsModal">
                                    <a class="link-black text-sm" data-toggle="tooltip" data-original-title="Add/Update Student DVS information for this student" href="javascript:;" class=""><i class="fa fa-edit"></i> </a>
                                </span>
                            </li>';
            $action = '<div class="action-overlay">
                            <ul class="icon-actions-set">
                              '.$actionHtml.'
                            </ul>
                        </div>';
            $nestedData[] = '<div class="form-group1"> <input type="checkbox" class="flat-red label-value-view applicable custom-checkbox-input studentId" name="student_id"  value="'.$row['id'].'"></div>'.$action;
            $nestedData[] = $row['generated_stud_id'];
            $nestedData[] = $row['name_title'].''.$row['first_name'].' '.$row['family_name'];
            $nestedData[] = '<span> <b> DOB :</b> '.date('d/m/Y', strtotime($row['DOB'])).' </span><br>
                                <span> <b> Gender :</b> '.$row['gender'].'</span><br>
                                <span> <b> Email: </b> '.$row['email'].' </span>';
            $nestedData[] = '<input type="text" class="form-control usitabletextfield birthplace_'.$row['id'].'" placeholder="City of Birth" name="birthplace_'.$row['id'].'" value="'.$row['birthplace'].'">';
            $nestedData[] = '<input type="text" class="form-control usitabletextfield usi_'.$row['id'].'" placeholder="Enter USI" name="usi_'.$row['id'].'" value="'.$row['USI'].'">';
            $usiVerified = '';
            if ($row['is_usi_verified'] == 1) {
                $usiVerified = '<span class="label label-success">Verified</span>';
            } else {
                $usiVerified = '<span class="label label-danger">Not Verified</span>';
            }
            $nestedData[] = $usiVerified;
            //            note: disabe for now
            //            $nestedData[] = 'Not Provided';
            //            $nestedData[] = ($row['DVS'] == 'YES') ? 'Provide' : 'Not Provided yet!';
            //            $nestedData[] = '<input type="checkbox" class="fileCheck_'.$row['id'].' is_file" name="fileCheck_'.$row['id'].'" value="'.$row['id'].'">';
            //            $nestedData[] = '<input type="checkbox" class="staffVerify_'.$row['id'].' is_staff" name="staffVerify_'.$row['id'].'" value="'.$row['id'].'">';
            $data[] = $nestedData;
        }

        $json_data = [
            'draw' => $requestArr['draw'], // for every request/draw by clientside , they send a number as a parameter, when they recieve a response/data they first check the draw number, so we are sending same number in draw.
            'recordsTotal' => $totalData, // total number of records
            'recordsFiltered' => $totalData, // total number of records after searching, if there is no searching then totalFiltered = totalData
            'data' => $data,   // total data array
        ];

        return $json_data;
    }

    public function editStudentDVSData($collegeId, $userId, $dataArr)
    {

        // print_r($dataArr);exit;
        return Students::where('college_id', $collegeId)
            ->where('id', $dataArr['studentId'])
            ->update([
                'birthplace' => $dataArr['birthplace'],
                'USI' => $dataArr['USI'],
                'is_file' => $dataArr['is_file'],
                'staff_verify' => $dataArr['staff_verify'],
                'file_check_by' => ($dataArr['is_file'] == 'YES') ? $userId : null,
                'file_check_when' => ($dataArr['is_file'] == 'YES') ? date('Y-m-d') : null,
                'staff_check_by' => ($dataArr['staff_verify'] == 'YES') ? $userId : null,
                'staff_check_when' => ($dataArr['staff_verify'] == 'YES') ? date('Y-m-d') : null,
                'updated_by' => $userId,
            ]);
    }

    public function updateSelectedStudentDVSData($collegeId, $userId, $studentIdArr, $request)
    {

        // echo "<pre>";print_r($request->input());exit;

        for ($i = 0; $i < count($studentIdArr); $i++) {

            Students::where('college_id', $collegeId)
                ->where('id', $studentIdArr[$i])
                ->update([
                    'birthplace' => $request->input("birthplace_$studentIdArr[$i]"),
                    'USI' => $request->input("usi_$studentIdArr[$i]"),
                    'is_file' => ($request->input("fileCheck_$studentIdArr[$i]") == '1') ? 'YES' : 'NO',
                    'staff_verify' => ($request->input("staffVerify_$studentIdArr[$i]") == '1') ? 'YES' : 'NO',
                    'file_check_by' => ($request->input("fileCheck_$studentIdArr[$i]") == '1') ? $userId : null,
                    'file_check_when' => ($request->input("fileCheck_$studentIdArr[$i]") == '1') ? date('Y-m-d') : null,
                    'staff_check_by' => ($request->input("staffVerify_$studentIdArr[$i]") == '1') ? $userId : null,
                    'staff_check_when' => ($request->input("staffVerify_$studentIdArr[$i]") == '1') ? date('Y-m-d') : null,
                    'updated_by' => $userId]);
        }

        return true;
    }

    public function getUSIStudentListFilter($collegeId, $dataArr)
    {
        // echo "<pre>";print_r($dataArr);exit;
        $sql = Students::from('rto_students as rs')
            ->leftjoin('rto_student_dvs_info as dvs', 'dvs.student_id', '=', 'rs.id')
            ->leftjoin('rto_country as rc1', 'rc1.id', '=', 'rs.birth_country')
            ->leftjoin('rto_country as rc2', 'rc2.id', '=', 'rs.current_country')
            ->leftjoin('rto_country as rc3', 'rc3.id', '=', 'rs.postal_country')
            ->leftjoin('rto_student_courses as rsc', 'rsc.student_id', '=', 'rs.id')
            ->where('rs.college_id', '=', $collegeId)
            ->where('rs.generated_stud_id', '!=', null)
            ->where('rs.staff_verify', '=', 'YES');

        $viewBy = $dataArr['search_by'];
        $string = $dataArr['string'];

        if (! empty($string)) {
            if ($viewBy == 'generated_stud_id') {
                $sql->where('rs.'.$viewBy, '=', $string);
            } elseif ($viewBy == 'DOB') {
                $date = date('Y-m-d', strtotime($string));
                $sql->where('rs.'.$viewBy, 'like', "%$date%");
            } elseif ($viewBy == 'first_name' ||
                    $viewBy == 'family_name' ||
                    $viewBy == 'student_type' ||
                    $viewBy == 'email' ||
                    $viewBy == 'current_mobile_phone') {

                $sql->where('rs.'.$viewBy, 'like', "%$string%");
            } elseif ($viewBy == 'status') {
                $sql->where('rsc.'.$viewBy, 'like', "%$string%");
            } elseif ($viewBy == 'start_date') {
                $start_date = date('Y-m-d', strtotime($string));
                $sql->where('rsc.'.$viewBy, 'like', "%$start_date%");
            } elseif ($viewBy == 'offer_id') {
                $sql->where('rsc.'.$viewBy, '=', "$string");
            }
        }

        return $result = $sql->groupBy('rs.id')
            ->get(['rs.*',
                'dvs.doc_type',
                'rc1.name as birth_country',
                'rc2.name as residence_country',
                'rc3.name as study_country']);
    }

    public function getNswAvetmissAttpv2Data($collegeId, $actionType, $nswCourseSiteId, $traningActivityYear)
    {

        //        $form_date = date('Y-m-d', strtotime($arrFilter['from_date']));
        //        $to_date = date('Y-m-d', strtotime($arrFilter['to_date']));

        $sql = Students::leftjoin('rto_student_subject_enrolment', 'rto_student_subject_enrolment.student_id', '=', 'rto_students.id')
            ->leftjoin('rto_venue', 'rto_venue.id', '=', 'rto_student_subject_enrolment.vanue_location')
            ->leftjoin('rto_student_courses as rsc', 'rsc.student_id', '=', 'rto_students.id')
            ->leftjoin('rto_courses', 'rto_courses.college_id', '=', 'rto_students.college_id')
            ->orderby('rto_students.id')
            ->where('rto_venue.state', '=', 'NSW')
            ->where('rto_students.college_id', '=', $collegeId);
        $sql->groupBy('rto_student_subject_enrolment.student_id');

        if (isset($traningActivityYear)) {
            $sql->whereYear('rto_student_subject_enrolment.activity_start_date', '=', $traningActivityYear);
        }

        $result = $sql->get(['rto_students.*',
            'rto_courses.course_code', 'rto_courses.cricos_code',
            'rto_courses.course_name', 'rto_courses.course_duration',
            'rto_courses.couse_duration_type', 'rsc.status',
            'rsc.finish_date', 'rsc.start_date', 'rsc.course_attempt',
            'rto_student_subject_enrolment.vanue_location',
            'rto_venue.state as venue_state',
        ]);
        for ($j = 0; $j < count($result); $j++) {
            $objStudentSubjectEnrolment = new StudentSubjectEnrolment;
            $result[$j]['arrSubjectUnitList'] = $objStudentSubjectEnrolment->getStudentUnitDataForNSWV2($result[$j]['id'], $nswCourseSiteId);
        }

        return $result;
    }

    public function getLocateMissingStudentListFilter($collegeId, $dataArr)
    {
        // echo "<pre>";print_r($dataArr);exit;
        $sql = Students::from('rto_students as rs')
            ->leftjoin('rto_student_dvs_info as dvs', 'dvs.student_id', '=', 'rs.id')
            ->leftjoin('rto_country as rc', 'rc.id', '=', 'rs.birth_country')
            ->leftjoin('rto_student_courses as rsc', 'rsc.student_id', '=', 'rs.id')
            ->where('rs.college_id', '=', $collegeId)
            ->where('rs.generated_stud_id', '!=', null);

        $viewBy = $dataArr['search_by'];
        $string = $dataArr['string'];

        if (! empty($string)) {
            if ($viewBy == 'generated_stud_id') {
                $sql->where('rs.'.$viewBy, '=', $string);
            } elseif ($viewBy == 'DOB') {
                $date = date('Y-m-d', strtotime($string));
                $sql->where('rs.'.$viewBy, 'like', "%$date%");
            } elseif ($viewBy == 'first_name' ||
                    $viewBy == 'family_name' ||
                    $viewBy == 'student_type' ||
                    $viewBy == 'email' ||
                    $viewBy == 'current_mobile_phone') {

                $sql->where('rs.'.$viewBy, 'like', "%$string%");
            } elseif ($viewBy == 'status') {
                $sql->where('rsc.'.$viewBy, 'like', "%$string%");
            } elseif ($viewBy == 'start_date') {
                $start_date = date('Y-m-d', strtotime($string));
                $sql->where('rsc.'.$viewBy, 'like', "%$start_date%");
            } elseif ($viewBy == 'offer_id') {
                $sql->where('rsc.'.$viewBy, '=', "$string");
            }
        }

        return $result = $sql->groupBy('rs.id')->get(['rs.*', 'rc.name as birth_country']);
    }

    // For XLS file //
    public function getUSIVerifyStudentList($collegeId)
    {
        // echo "<pre>";print_r($dataArr);exit;
        $arrDVSDocumentTypeList = Config::get('constants.arrDVSDocumentTypeList');
        $arrStateList = Config::get('constants.arrState');

        $resultArr = Students::from('rto_students as rs')
            ->leftjoin('rto_student_dvs_info as dvs', 'dvs.student_id', '=', 'rs.id')
            ->leftjoin('rto_country as rc', 'rc.id', '=', 'rs.birth_country')
            ->leftjoin('rto_users as ru1', 'ru1.id', '=', 'rs.file_check_by')
            ->leftjoin('rto_users as ru2', 'ru2.id', '=', 'rs.staff_check_by')
            ->leftjoin('rto_users as ru3', 'ru3.id', '=', 'dvs.updated_by')
            ->where('rs.college_id', '=', $collegeId)
            ->where('rs.generated_stud_id', '!=', null)
            ->groupBy('rs.id')
            ->get(['rs.id as student_id',
                'rs.generated_stud_id',
                'rs.first_name',
                'rs.family_name',
                'rs.gender',
                'rs.DOB',
                'rs.email',
                'rs.birthplace',
                'rc.name as country_of_birth',
                'rs.USI',
                'rs.DVS',
                'dvs.doc_type',
                'rs.is_file',
                'ru1.name as file_check_by',
                'rs.file_check_when',
                'rs.staff_verify',
                'ru2.name as staff_check_by',
                'rs.staff_check_when',
                'dvs.*',
                'ru3.name as data_by',
            ]);

        // echo "<pre>";print_r($resultArr);exit;

        foreach ($resultArr as $row) {

            $dvsDocType = $row->doc_type;
            $row->dvs_detail = '';
            $dataBy = "DataBy : $row->data_by".(! empty($row->updated_at) ? '('.date('d/m/Y', strtotime(($row->updated_at))).')' : '');

            if ($dvsDocType == 'DriverLicense') {
                $row->dvs_detail .= 'Driver State : '.$arrStateList[$row->driver_state].', ';
                $row->dvs_detail .= "License Number : $row->license_no".', ';
            } elseif ($dvsDocType == 'Medicare') {
                $row->dvs_detail .= "Card number : $row->medicare_no".', ';
                $row->dvs_detail .= "Name on card : $row->name".', ';
                $row->dvs_detail .= "Expiry date : $row->expiry_year-$row->expiry_month ".', ';
            } elseif ($dvsDocType == 'AustralianPassport') {
                $row->dvs_detail .= "Document number : $row->document_no".', ';
            } elseif ($dvsDocType == 'Visa') {
                $row->dvs_detail .= "Passport number : $row->passport_no".', ';
                $row->dvs_detail .= "Issued in country : $row->issue_country".', ';
            } elseif ($dvsDocType == 'BirthCert') {
                $row->dvs_detail .= 'State : '.$arrStateList[$row->aus_state].', ';
                $row->dvs_detail .= "Registration number: $row->reg_no".', ';
                $row->dvs_detail .= "Registration year : $row->reg_year".', ';
                $row->dvs_detail .= "Certificate number : $row->certi_no".', ';
            } elseif ($dvsDocType == 'Descent') {
                $row->dvs_detail .= 'Acquisition date : '.(! empty($row->acquisition_date) ? date('Y-m-d', strtotime($row->acquisition_date)) : '').', ';
            } elseif ($dvsDocType == 'Citizenship') {
                $row->dvs_detail .= "Stock number : $row->stock_no".', ';
                $row->dvs_detail .= 'Acquisition date : '.(! empty($row->dt_acquisition) ? date('Y-m-d', strtotime($row->dt_acquisition)) : '').', ';
            } elseif ($dvsDocType == 'ImmiCard') {
                $row->dvs_detail .= "ImmiCard number : $row->immicard_no".', ';
            }

            $row->DOB = date('d/m/Y', strtotime($row->DOB));
            $row->doc_type = (isset($arrDVSDocumentTypeList[$row->doc_type])) ? $arrDVSDocumentTypeList[$row->doc_type] : '';
            $row->file_check_when = ! empty($row->file_check_when) ? date('d/m/Y', strtotime($row->file_check_when)) : '';
            $row->staff_check_when = ! empty($row->staff_check_when) ? date('d/m/Y', strtotime($row->staff_check_when)) : '';
            $row->dvs_detail .= $dataBy;
        }

        return $resultArr;
    }

    public function getStaffVerifiedStudentList($collegeId)
    {

        // echo "<pre>";print_r($dataArr);exit;
        $arrDVSDocumentTypeList = Config::get('constants.arrDVSDocumentTypeList');

        $resultArr = Students::from('rto_students as rs')
            ->leftjoin('rto_student_dvs_info as dvs', 'dvs.student_id', '=', 'rs.id')
            ->leftjoin('rto_country as rc1', 'rc1.id', '=', 'rs.birth_country')
            ->leftjoin('rto_country as rc2', 'rc2.id', '=', 'rs.current_country')
            ->leftjoin('rto_country as rc3', 'rc3.id', '=', 'rs.postal_country')
            ->where('rs.college_id', '=', $collegeId)
            ->where('rs.generated_stud_id', '!=', null)
            ->where('rs.staff_verify', '=', 'YES')
            ->groupBy('rs.id')
            ->select('rs.id as student_id', 'rs.generated_stud_id', DB::raw('concat(rs.name_title, rs.first_name, " ", rs.family_name) as student_name'), 'rs.DOB', 'rs.gender', 'rs.email', DB::raw('concat(rs.birthplace, ", ", rc1.name) as birth_place'), 'rc2.name as current_country', 'rc3.name as postal_country', 'rs.USI', 'dvs.doc_type')
            ->get();

        foreach ($resultArr as $row) {
            $row->DOB = ! empty($row->DOB) ? date('d/m/Y', strtotime($row->DOB)) : '';
            $row->dvs_info = 'Proof of ID type : '.$arrDVSDocumentTypeList[$row->doc_type];
        }

        return $resultArr;
    }

    public function getLocateMissingStudentList($collegeId)
    {
        $dataArr = Students::from('rto_students as rs')
            ->leftjoin('rto_country as rc', 'rc.id', '=', 'rs.birth_country')
            ->where('rs.college_id', '=', $collegeId)
            ->where('rs.generated_stud_id', '!=', null)
            ->select('rs.id as student_id', 'rs.generated_stud_id', DB::raw('concat(rs.name_title, rs.first_name, " ", rs.family_name) as student_name'), 'rs.DOB', 'rs.gender', 'rs.email', 'rs.USI', DB::raw('concat(rs.birthplace, ", ", rc.name) as birth_place'))
            ->get();

        foreach ($dataArr as $row) {
            $row->DOB = ! empty($row->DOB) ? date('d/m/Y', strtotime($row->DOB)) : '';
        }

        return $dataArr;
    }

    public function getStudentDetailForNSW($collegeId, $arrFilter)
    {

        $sql = Students::join('rto_colleges', 'rto_colleges.id', '=', 'rto_students.college_id')
            ->join('rto_student_details as rsd', 'rsd.student_id', '=', 'rto_students.id')
            ->leftjoin('rto_student_courses as rsc', 'rto_students.id', '=', 'rsc.student_id')
            ->leftjoin('rto_language as lang', 'lang.id', '=', 'rsd.is_english_main_lang')
            ->leftjoin('rto_agents', 'rto_agents.id', '=', 'rsc.agent_id')
            ->leftjoin('rto_country as sc', 'sc.id', '=', 'rto_students.current_country')
            ->leftjoin('rto_country as ac', 'ac.id', '=', 'rto_agents.office_country')
            ->leftjoin('rto_setup_section as rss1', 'rss1.id', '=', 'rto_students.visa_status')
            ->leftjoin('rto_setup_section as rss2', 'rss2.id', '=', 'rsd.EPL_test_name')
            ->leftjoin('rto_student_subject_enrolment', 'rto_student_subject_enrolment.student_id', '=', 'rto_students.id')
            ->leftjoin('rto_student_courses', 'rto_student_courses.student_id', '=', 'rto_students.id')
            ->leftjoin('rto_venue', 'rto_venue.id', '=', 'rto_student_subject_enrolment.vanue_location')
            ->orderby('rto_students.id')
            ->where('rto_students.college_id', '=', $collegeId);
        $sql->groupBy('rto_students.id');
        if (! empty($arrFilter['claim_stage'])) {
            $sql->leftjoin('rto_student_claim_tracking', 'rto_student_claim_tracking.student_id', '=', 'rto_students.id');
            $sql->where('rto_student_claim_tracking.lodgement_type', $arrFilter['claim_stage']);
        }
        $sql->where('rto_venue.state', '=', 'NSW');

        $result = $sql->get(['rto_students.*', 'rsd.*', 'lang.abs_value as language_abs_value',
            'rto_agents.agency_name', 'rto_agents.office_address as agent_address',
            'rto_agents.office_city as agent_city', 'rto_agents.office_state as agent_state',
            'rto_agents.office_postcode as agent_postcode', 'ac.name as agent_country',
            'rto_agents.alertnet_email',
            'sc.nationality', 'sc.nationality as stud_nationality', 'sc.absvalue as country_name',
            'rss1.value as visaStatus', 'rss2.value as EPL_test_name', 'rto_venue.state',
            'rto_student_subject_enrolment.activity_start_date',
            'rto_student_subject_enrolment.activity_finish_date']);

        return $result;
    }

    public function getStudentDetailForNSW100($collegeId, $arrFilter)
    {

        $sql = Students::join('rto_colleges', 'rto_colleges.id', '=', 'rto_students.college_id')
            ->join('rto_student_education', 'rto_student_education.student_id', '=', 'rto_students.id')
            ->leftjoin('rto_student_subject_enrolment', 'rto_student_subject_enrolment.student_id', '=', 'rto_students.id')
            ->leftjoin('rto_venue', 'rto_venue.id', '=', 'rto_student_subject_enrolment.vanue_location')
            ->leftjoin('rto_student_courses', 'rto_student_courses.student_id', '=', 'rto_students.id')
            ->orderby('rto_students.id')
            ->where('rto_students.college_id', '=', $collegeId);
        $sql->groupBy('rto_students.id');
        if (! empty($arrFilter['claim_stage'])) {
            $sql->leftjoin('rto_student_claim_tracking', 'rto_student_claim_tracking.student_id', '=', 'rto_students.id');
            $sql->where('rto_student_claim_tracking.lodgement_type', $arrFilter['claim_stage']);
        }
        $sql->where('rto_venue.state', '=', 'NSW');

        $result = $sql->get(['rto_students.*', 'rto_student_education.qualification_level_id',
            'rto_venue.state', 'rto_student_subject_enrolment.activity_start_date',
            'rto_student_subject_enrolment.activity_finish_date']);

        return $result;
    }

    public function getNswAvetmissAttpv1Data($collegeId, $claimStage, $nswCourseSiteId, $traningActivityYear)
    {

        //        $form_date = date('Y-m-d', strtotime($arrFilter['from_date']));
        //        $to_date = date('Y-m-d', strtotime($arrFilter['to_date']));

        $sql = Students::leftjoin('rto_student_subject_enrolment', 'rto_student_subject_enrolment.student_id', '=', 'rto_students.id')
            ->leftjoin('rto_venue', 'rto_venue.id', '=', 'rto_student_subject_enrolment.vanue_location')
            ->leftjoin('rto_student_courses as rsc', 'rsc.student_id', '=', 'rto_students.id')
            ->leftjoin('rto_courses', 'rto_courses.college_id', '=', 'rto_students.college_id')
            ->leftjoin('rto_student_claim_tracking', 'rto_student_claim_tracking.student_id', '=', 'rto_students.id')
            ->orderby('rto_students.id')
            ->where('rto_venue.state', '=', 'NSW')
            ->where('rto_students.college_id', '=', $collegeId);
        if (! empty($claimStage) && isset($claimStage)) {
            $sql->where('rto_student_claim_tracking.lodgement_type', '=', $claimStage);
        }
        $sql->groupBy('rto_student_subject_enrolment.student_id');

        if (isset($traningActivityYear)) {
            $sql->whereYear('rto_student_subject_enrolment.activity_start_date', '=', $traningActivityYear);
        }

        $result = $sql->get(['rto_students.*',
            'rto_courses.course_code', 'rto_courses.cricos_code',
            'rto_courses.course_name', 'rto_courses.course_duration',
            'rto_courses.couse_duration_type', 'rsc.status',
            'rsc.finish_date', 'rsc.start_date', 'rsc.course_attempt',
            'rto_student_subject_enrolment.vanue_location',
            'rto_student_claim_tracking.claim_stage_year', 'rto_student_claim_tracking.lodgement_type',
            'rto_student_claim_tracking.lodgement_id', 'rto_student_claim_tracking.lodgement_date',
            'rto_student_claim_tracking.student_id as enrolment_student_id', 'rto_student_claim_tracking.claim_amount',
            'rto_venue.state as venue_state',
        ]);

        for ($j = 0; $j < count($result); $j++) {
            $objStudentSubjectEnrolment = new StudentSubjectEnrolment;
            $result[$j]['arrSubjectUnitList'] = $objStudentSubjectEnrolment->getStudentUnitDataForNSWV1($result[$j]['id'], $nswCourseSiteId, $collegeId);
        }

        return $result;
    }

    public function saveStudentShortCourseInfo($collegeId, $request, $applicationReferenceId, $userId)
    {

        // insert first time if not exixts -> rto_students
        $studentsInfo = new Students;
        $studentsInfo->college_id = $collegeId;
        $studentsInfo->application_reference_id = $applicationReferenceId;
        $studentsInfo->USI = (empty($request->input('USI'))) ? null : $request->input('USI');
        $studentsInfo->student_type = ($request->input('student_type') != '') ? $request->input('student_type') : null;
        $studentsInfo->name_title = ($request->input('name_title') != '') ? $request->input('name_title') : null;
        $studentsInfo->first_name = ($request->input('first_name') != '') ? $request->input('first_name') : null;
        $studentsInfo->middel_name = ($request->input('middel_name') != '') ? $request->input('middel_name') : null;
        $studentsInfo->family_name = ($request->input('family_name') != '') ? $request->input('family_name') : null;
        $studentsInfo->nickname = ($request->input('nickname') != '') ? $request->input('nickname') : null;
        $studentsInfo->gender = ($request->input('gender') != '') ? $request->input('gender') : null;
        $studentsInfo->DOB = ($request->input('DOB') != '') ? date('Y-m-d', strtotime($request->input('DOB'))) : null;
        $studentsInfo->email = ($request->input('email') != '') ? $request->input('email') : null;
        $studentsInfo->birthplace = ($request->input('birthplace') != '') ? $request->input('birthplace') : null;
        $studentsInfo->birth_country = (! empty($request->input('birth_country'))) ? $request->input('birth_country') : null;
        $studentsInfo->nationality = ($request->input('nationality') != '') ? $request->input('nationality') : null;
        $studentsInfo->passport_no = ($request->input('passport_no') != '') ? $request->input('passport_no') : null;
        $studentsInfo->passport_expiry = ($request->input('passport_expiry') != '') ? date('Y-m-d', strtotime($request->input('passport_expiry'))) : null;
        $studentsInfo->visa_status = ($request->input('visa_status') != '') ? $request->input('visa_status') : null;
        $studentsInfo->visa_number = ($request->input('visa_number') != '') ? $request->input('visa_number') : null;
        $studentsInfo->visa_expiry_date = (! empty($request->input('visa_expiry_date'))) ? date('Y-m-d', strtotime($request->input('visa_expiry_date'))) : null;

        $studentsInfo->current_country = ($request->input('current_country') != '') ? $request->input('current_country') : null;
        $studentsInfo->current_building_name = ($request->input('current_building_name') != '') ? $request->input('current_building_name') : null;
        $studentsInfo->current_unit_detail = ($request->input('current_unit_detail') != '') ? $request->input('current_unit_detail') : null;
        $studentsInfo->current_street_no = ($request->input('current_street_no') != '') ? $request->input('current_street_no') : null;
        $studentsInfo->current_street_name = ($request->input('current_street_name') != '') ? $request->input('current_street_name') : null;
        $studentsInfo->current_city = ($request->input('current_city') != '') ? $request->input('current_city') : null;
        $studentsInfo->current_state = ($request->input('current_state') != '') ? $request->input('current_state') : null;
        $studentsInfo->current_postcode = ($request->input('current_postcode') != '') ? $request->input('current_postcode') : null;
        $studentsInfo->current_home_phone = ($request->input('current_home_phone') != '') ? $request->input('current_home_phone') : null;
        $studentsInfo->current_work_phone = ($request->input('current_work_phone') != '') ? $request->input('current_work_phone') : null;
        $studentsInfo->current_mobile_phone = ($request->input('current_mobile_phone') != '') ? $request->input('current_mobile_phone') : null;
        $studentsInfo->is_postal_address = ($request->input('is_postal_address') != '') ? $request->input('is_postal_address') : null;
        $studentsInfo->is_parmanent_address = ($request->input('is_parmanent_address') != '') ? $request->input('is_parmanent_address') : null;

        /* Insert Postal Address Detail */
        if ($request->input('is_postal_address') == '1' && $request->input('is_same_as') == '1') {
            $studentsInfo->postal_country = ($request->input('current_country') != '') ? $request->input('current_country') : null;
            $studentsInfo->postal_building_name = ($request->input('current_building_name') != '') ? $request->input('current_building_name') : null;
            $studentsInfo->postal_unit_detail = ($request->input('current_unit_detail') != '') ? $request->input('current_unit_detail') : null;
            $studentsInfo->postal_street_no = ($request->input('current_street_no') != '') ? $request->input('current_street_no') : null;
            $studentsInfo->postal_street_name = ($request->input('current_street_name') != '') ? $request->input('current_street_name') : null;
            $studentsInfo->postal_po_box = ($request->input('postal_po_box') != '') ? $request->input('postal_po_box') : null;
            $studentsInfo->postal_city = ($request->input('current_city') != '') ? $request->input('current_city') : null;
            $studentsInfo->postal_state = ($request->input('current_state') != '') ? $request->input('current_state') : null;
            $studentsInfo->postal_postcode = ($request->input('current_postcode') != '') ? $request->input('current_postcode') : null;
            $studentsInfo->postal_home_phone = ($request->input('current_home_phone') != '') ? $request->input('current_home_phone') : null;
            $studentsInfo->postal_work_phone = ($request->input('current_work_phone') != '') ? $request->input('current_work_phone') : null;
            $studentsInfo->postal_mobile_phone = ($request->input('current_mobile_phone') != '') ? $request->input('current_mobile_phone') : null;
        } elseif ($request->input('is_postal_address') == '1') {
            $studentsInfo->postal_country = ($request->input('postal_country') != '') ? $request->input('postal_country') : null;
            $studentsInfo->postal_building_name = ($request->input('postal_building_name') != '') ? $request->input('postal_building_name') : null;
            $studentsInfo->postal_unit_detail = ($request->input('postal_unit_detail') != '') ? $request->input('postal_unit_detail') : null;
            $studentsInfo->postal_street_no = ($request->input('postal_street_no') != '') ? $request->input('postal_street_no') : null;
            $studentsInfo->postal_street_name = ($request->input('postal_street_name') != '') ? $request->input('postal_street_name') : null;
            $studentsInfo->postal_po_box = ($request->input('postal_po_box') != '') ? $request->input('postal_po_box') : null;
            $studentsInfo->postal_city = ($request->input('postal_city') != '') ? $request->input('postal_city') : null;
            $studentsInfo->postal_state = ($request->input('postal_state') != '') ? $request->input('postal_state') : null;
            $studentsInfo->postal_postcode = ($request->input('postal_postcode') != '') ? $request->input('postal_postcode') : null;
            $studentsInfo->postal_home_phone = ($request->input('postal_home_phone') != '') ? $request->input('postal_home_phone') : null;
            $studentsInfo->postal_work_phone = ($request->input('postal_work_phone') != '') ? $request->input('postal_work_phone') : null;
            $studentsInfo->postal_mobile_phone = ($request->input('postal_mobile_phone') != '') ? $request->input('postal_mobile_phone') : null;
        } else {
            $studentsInfo->postal_country = null;
            $studentsInfo->postal_building_name = null;
            $studentsInfo->postal_unit_detail = null;
            $studentsInfo->postal_street_no = null;
            $studentsInfo->postal_street_name = null;
            $studentsInfo->postal_po_box = null;
            $studentsInfo->postal_city = null;
            $studentsInfo->postal_state = null;
            $studentsInfo->postal_postcode = null;
            $studentsInfo->postal_home_phone = null;
            $studentsInfo->postal_work_phone = null;
            $studentsInfo->postal_mobile_phone = null;
        }

        /* Insert Overseas/Permanent Address Detail */
        if ($request->input('is_parmanent_address') == '1') {
            $studentsInfo->permanent_country = ($request->input('permanent_country') != '') ? $request->input('permanent_country') : null;
            $studentsInfo->permanent_street_name = ($request->input('permanent_street_name') != '') ? $request->input('permanent_street_name') : null;
            $studentsInfo->permanent_city = ($request->input('permanent_city') != '') ? $request->input('permanent_city') : null;
            $studentsInfo->permanent_state = ($request->input('permanent_state') != '') ? $request->input('permanent_state') : null;
            $studentsInfo->permanent_postcode = ($request->input('permanent_postcode') != '') ? $request->input('permanent_postcode') : null;
            $studentsInfo->permanent_home_phone = ($request->input('permanent_home_phone') != '') ? $request->input('permanent_home_phone') : null;
            $studentsInfo->permanent_work_phone = ($request->input('permanent_work_phone') != '') ? $request->input('permanent_work_phone') : null;
            $studentsInfo->permanent_mobile_phone = ($request->input('permanent_mobile_phone') != '') ? $request->input('permanent_mobile_phone') : null;
        } else {
            $studentsInfo->permanent_country = null;
            $studentsInfo->permanent_street_name = null;
            $studentsInfo->permanent_city = null;
            $studentsInfo->permanent_state = null;
            $studentsInfo->permanent_postcode = null;
            $studentsInfo->permanent_home_phone = null;
            $studentsInfo->permanent_work_phone = null;
            $studentsInfo->permanent_mobile_phone = null;
        }

        $studentsInfo->status = 1;
        $studentsInfo->is_applicant = 1;
        $studentsInfo->is_offered = 0;
        $studentsInfo->is_student = 0;

        $studentsInfo->created_by = $userId;
        $studentsInfo->updated_by = $userId;
        $studentsInfo->save();

        // insert last id into student_details table
        $studentDetails = new StudentDetails;
        $studentDetails->student_id = $studentsInfo->id;
        $studentDetails->hear_about_id = ($request->input('hear_about_id') != '') ? $request->input('hear_about_id') : null;
        $studentDetails->created_by = $userId;
        $studentDetails->updated_by = $userId;
        $studentDetails->save();

        return $studentsInfo->id;
    }

    public function getStudentSendRequestOLD($collegeId, $beforeSubmitDate, $formId, $courseId, $studentStatus, $studentByClass, $toDate, $fromDate, $arrStudentId)
    {

        $sql = Students::from('rto_students as rs')
            ->leftjoin('rto_student_courses as rsc', 'rsc.student_id', '=', 'rs.id')
            ->leftjoin('rto_courses', 'rto_courses.college_id', '=', 'rs.college_id');

        if (! empty($arrStudentId['course_id']) && ! empty($arrStudentId['student_id'])) {
            $sql->join('rto_survey_send_request as studReq', 'studReq.student_id', '!=', 'rsc.student_id');
            $sql->join('rto_survey_send_request as CourseReq', 'CourseReq.course_id', '!=', 'rsc.course_id');
            $sql->whereNotIn('rsc.course_id', [$arrStudentId['course_id']]);
            $sql->whereNotIn('rsc.student_id', [$arrStudentId['student_id']]);
        }

        if ($arrStudentId['course_id'] != '' && $arrStudentId['student_id'] != '') {

        }
        $sql->where('rs.college_id', '=', $collegeId);

        $sql->groupBy('rsc.course_id');

        if ($courseId != '' && ! empty($courseId)) {
            $sql->where('rsc.course_id', $courseId);
        }

        if ($studentStatus != '' && ! empty($studentStatus)) {
            $sql->where('rsc.status', $studentStatus);
        }

        if ($toDate != '' && $fromDate != '' && $toDate != null && $fromDate != null) {
            $fromDate = date('Y-m-d', strtotime($fromDate));
            $toDate = date('Y-m-d', strtotime($toDate));
            $sql->where('rsc.start_date', '>=', $toDate);
            $sql->where('rsc.finish_date', '<=', $fromDate);
        }

        $result = $sql->get(['rs.generated_stud_id',
            'rs.name_title',
            'rs.email',
            'rs.first_name',
            'rs.family_name',
            'rsc.student_id',
            'rto_courses.course_code',
            'rto_courses.cricos_code',
            'rsc.course_id as course_id',
            'rto_courses.course_name',
            'rto_courses.course_duration',
            'rto_courses.couse_duration_type',
            'rsc.status',
            'rsc.finish_date',
            'rsc.start_date',
            'rsc.course_attempt']);

        return $result;
    }

    public function getStudentNotSendRequest($collegeId, $dataArr)
    {

        $courseId = $dataArr['courseId'];
        $status = $dataArr['studentStatus'];
        $dtFrom = ! empty($dataArr['fromDate']) ? date('Y-m-d', strtotime($dataArr['fromDate'])) : null;
        $dtEnd = ! empty($dataArr['toDate']) ? date('Y-m-d', strtotime($dataArr['toDate'])) : null;

        $sql = StudentCourse::from('rto_student_courses as rsc')
            ->join('rto_students as rs', 'rs.id', '=', 'rsc.student_id')
            ->join('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->where('rs.college_id', '=', $collegeId)
            ->groupBy('rsc.student_id');

        if (! empty($courseId)) {
            $sql->where('rsc.course_id', $courseId);
        }

        if (! empty($status)) {
            $sql->where('rsc.status', $status);
        }

        if (! empty($dtFrom) && ! empty($dtEnd)) {
            $sql->where('rsc.start_date', '>=', $dtEnd);
            $sql->where('rsc.finish_date', '<=', $dtFrom);
        }

        $result = $sql->get(['rs.generated_stud_id',
            'rs.name_title',
            'rs.email',
            'rs.first_name',
            'rs.family_name',
            'rsc.student_id',
            'rc.course_code',
            'rc.cricos_code',
            'rsc.course_id as course_id',
            'rc.course_name',
            'rc.course_duration',
            'rc.couse_duration_type',
            'rsc.status',
            'rsc.finish_date',
            'rsc.start_date',
            'rsc.course_attempt',
            'rsc.offer_status']);

        // echo "<pre>";print_r($result);exit;
        return $result;
    }

    public function getStudentSendRequest($collegeId, $formId)
    {

        $result = StudentCourse::from('rto_survey_send_request as rssr')
            ->leftjoin('rto_student_courses as rsc', 'rsc.student_id', '=', 'rssr.student_id')
            ->join('rto_students as rs', 'rs.id', '=', 'rsc.student_id')
            ->join('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->where('rssr.college_id', $collegeId)
            ->where('rssr.form_name_id', $formId)
            ->groupBy('rssr.student_id')
            ->get(['rs.id as studeId',
                'rs.generated_stud_id',
                'rs.name_title',
                'rs.first_name',
                'rs.family_name',
                'rs.email',
                'rsc.student_id',
                'rc.course_code',
                'rc.course_name',
                'rsc.status',
                'rsc.course_attempt',
                'rsc.offer_status',
                'rsc.course_id',
                'rssr.group_id',
                'rssr.supervisor',
                'rssr.before_submit',
                'rssr.created_at']);

        return $result;
    }

    public function getStudentResendRequest($collegeId, $formId)
    {

        $sql = Students::from('rto_students as rs')
            ->leftjoin('rto_student_courses as rsc', 'rsc.student_id', '=', 'rs.id')
            ->join('rto_survey_send_request as studReq', 'studReq.student_id', '=', 'rsc.student_id')
            ->join('rto_survey_send_request as CourseReq', 'CourseReq.course_id', '=', 'rsc.course_id')
            ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->where('rs.college_id', '=', $collegeId)
            ->where('studReq.form_name_id', '=', $formId);
        $sql->groupBy('rsc.course_id');

        $result = $sql->get(['rs.generated_stud_id',
            'rs.name_title',
            'rs.email',
            'rs.first_name',
            'rs.family_name',
            'rsc.student_id as studeId',
            'CourseReq.*',
            'rc.course_code',
            'rc.cricos_code',
            'rsc.id as course_id',
            'rc.course_name',
            'rc.course_duration',
            'rc.couse_duration_type',
            'rsc.status',
            'rsc.finish_date',
            'rsc.start_date',
            'rsc.course_attempt']);

        return $result;
    }

    public function getStudentProvider($studentId, $collegeId)
    {
        //        echo $studentId;
        //        echo $collegeId;exit;
        $result = Students::leftjoin('rto_student_courses as rsc', 'rsc.student_id', '=', 'rto_students.id')
            ->whereNotNull('rsc.offer_id')
            ->whereNotNull('rsc.course_id')
            ->where('rto_students.id', '=', $studentId)
            ->where('rto_students.college_id', '=', $collegeId)
            ->first(['rsc.offer_id as offer_id', 'rsc.course_id as course_id',
                'rto_students.application_reference_id',
                'rto_students.id as student_id',
                'rto_students.first_name', 'rto_students.middel_name', 'rto_students.family_name', 'rto_students.email']);
        if ($result) {
            $result = $result->toArray();
        }

        // dd($result);
        return $result;
    }

    // get student finance datatable records
    public function getFinanceStudentListDesc($collegeId, $request)
    {
        $requestData = $_REQUEST;
        $columns = [
            // datatable column index  => database column name
            0 => 'rs.generated_stud_id',
            1 => 'rs.name_title',
            2 => 'rs.first_name',
            3 => 'rs.family_name',
            4 => 'rs.nickname',
            5 => 'rs.gender',
            6 => 'rs.student_type',
            7 => 'rs.DOB',
            8 => 'rs.USI',
            9 => 'rs.email',
            10 => 'rs.birthplace',
            11 => 'rs.passport_no',
        ];

        $query = Students::from('rto_students as rs')
            ->where('rs.college_id', '=', $collegeId)
            ->where('is_student', '=', 1);

        if (! empty($requestData['search']['value'])) {   // if there is a search parameter, $requestData['search']['value'] contains search parameter
            $searchVal = $requestData['search']['value'];
            $query->where(function ($query) use ($columns, $searchVal, $requestData) {

                $flag = 0;
                foreach ($columns as $key => $value) {
                    $searchVal = $requestData['search']['value'];
                    if ($key == 7) {
                        //                            if ($key == 7 && strtotime($searchVal) == true) {
                        $searchVal = date('Y-m-d', strtotime($searchVal));
                    }
                    if ($requestData['columns'][$key]['searchable'] == 'true') {
                        if ($flag == 0) {
                            $query->where($value, 'like', '%'.$searchVal.'%');
                            $flag = $flag + 1;
                        } else {
                            $query->orWhere($value, 'like', '%'.$searchVal.'%');
                        }
                    }
                }
            });
        }

        $temp = $query->orderBy($columns[$requestData['order'][0]['column']], $requestData['order'][0]['dir']);

        $totalData = count($temp->get());
        $totalFiltered = count($temp->get());

        $resultArr = $query->skip($requestData['start'])
            ->take($requestData['length'])
            ->select('rs.generated_stud_id', 'rs.name_title', 'rs.first_name', 'rs.family_name', 'rs.nickname', 'rs.gender', 'rs.student_type', 'rs.DOB', 'rs.USI', 'rs.email', 'rs.birthplace', 'rs.passport_no', 'rs.visa_number', 'rs.birthplace', 'rs.passport_no', 'rs.id'
            )
            ->get();

        //        echo "<pre>";print_r($resultArr);exit;

        $data = [];
        foreach ($resultArr as $row) {

            $nestedData = [];
            $actionHtml = '';
            $actionHtml = '<li><a class="link-black text-sm "  data-toggle="tooltip" data-original-title="View Student Payment Summary" href="'.route('student-payment-summary', ['id' => $row['id']]).'"><i class="fa fa-search"></i> </a></li>';
            $action = '<div class="action-overlay">
                            <ul class="icon-actions-set">
                              '.$actionHtml.'
                            </ul>
                        </div>';
            $nestedData[] = $row['generated_stud_id'].$action;
            $nestedData[] = $row['name_title'];
            $nestedData[] = $row['first_name'];
            $nestedData[] = $row['family_name'];
            $nestedData[] = $row['nickname'];
            $nestedData[] = $row['gender'];
            $nestedData[] = $row['student_type'];
            $nestedData[] = date('d-m-Y', strtotime($row['DOB']));
            $nestedData[] = $row['USI'];
            $nestedData[] = $row['email'];
            $nestedData[] = $row['birthplace'];
            $nestedData[] = $row['passport_no'];

            $data[] = $nestedData;
        }
        //        echo "<pre>";print_r($data);exit;

        $json_data = [
            'draw' => intval($requestData['draw']), // for every request/draw by clientside , they send a number as a parameter, when they recieve a response/data they first check the draw number, so we are sending same number in draw.
            'recordsTotal' => intval($totalData), // total number of records
            'recordsFiltered' => intval($totalFiltered), // total number of records after searching, if there is no searching then totalFiltered = totalData
            'data' => $data,   // total data array
        ];

        return $json_data;

        //        return Students::where('college_id', '=', $collegeId)->where('is_student', '=', 1)->orderBy('id', 'DESC')->get();
    }

    public function getAutocompleteData($request, $collegeId)
    {
        $sql = Students::select('rto_students.id', 'rto_students.first_name', 'rto_students.generated_stud_id', 'rto_students.middel_name', 'rto_students.college_id', 'rto_students.profile_picture', 'rto_students.family_name')
            ->where('rto_students.college_id', '=', $collegeId)->limit(10);
        $searchString = $request->input('term');
        $sql->where('rto_students.first_name', 'like', "%{$searchString}%")
            ->orwhere('rto_students.middel_name', 'like', '%'.$searchString.'%')
            ->orwhere('rto_students.family_name', 'like', '%'.$searchString.'%')
            ->orWhereRaw("concat(rto_students.first_name, ' ', rto_students.family_name) like '%$searchString%'")
            ->orwhere('rto_students.nickname', 'like', '%'.$searchString.'%')
            ->orwhere('rto_students.generated_stud_id', 'like', '%'.$searchString.'%');
        $result = $sql->get()->toArray();
        $returnData = [];
        $filePath = Config::get('constants.uploadFilePath.StudentPics');

        for ($i = 0; $i < count($result); $i++) {
            $destinationPath = Helpers::changeRootPath($filePath, $result[$i]['id']);
            $profile_pic = $destinationPath['default'].$result[$i]['profile_picture'];
            if (file_exists($profile_pic) && ! empty($result[$i]['profile_picture'])) {
                $returnData[$i]['profile_picture'] = $destinationPath['view'].$result[$i]['profile_picture'];
            } else {
                $returnData[$i]['profile_picture'] = url('/').'/icon/profile.png';
            }
            $returnData[$i]['id'] = $result[$i]['id'];
            $returnData[$i]['first_name'] = $result[$i]['first_name'].' '.$result[$i]['family_name'].' <div class="stu_id">Student ID:'.($result[$i]['generated_stud_id'] == '' ? 'N/A' : $result[$i]['generated_stud_id']).'</div>';
            $returnData[$i]['generatedId'] = ($result[$i]['generated_stud_id'] == '' ? 'N/A' : $result[$i]['generated_stud_id']);
        }
        if (count($result) == 0) {
            $returnData[0]['id'] = '';
            $returnData[0]['first_name'] = 'No record Found';
            $returnData[0]['profile_picture'] = url('/').'/icon/profile.png';
            $returnData[0]['generatedId'] = '';
        }
        $countTotal = (count($result) == 0 ? 1 : count($result));
        $returnData[$countTotal]['id'] = 'All';
        $returnData[$countTotal]['first_name'] = 'Go To Student Search';
        $returnData[$countTotal]['profile_picture'] = url('/').'/icon/search.png';
        $returnData[$countTotal]['generatedId'] = '';

        return $returnData;
    }

    public function getStudentAutocompleteData($request, $collegeId)
    {
        $sql = Students::select('rto_students.id', 'rto_students.first_name', 'rto_students.generated_stud_id', 'rto_students.middel_name', 'rto_students.college_id', 'rto_students.profile_picture', 'rto_students.family_name')
            ->where('rto_students.college_id', '=', $collegeId);
        $searchString = $request->input('term');
        $sql->where('rto_students.first_name', 'like', "%{$searchString}%")
            ->orwhere('rto_students.middel_name', 'like', '%'.$searchString.'%')
            ->orwhere('rto_students.family_name', 'like', '%'.$searchString.'%')
            ->orWhereRaw("concat(rto_students.first_name, ' ', rto_students.family_name) like '%$searchString%'")
            ->orwhere('rto_students.nickname', 'like', '%'.$searchString.'%')
            ->orwhere('rto_students.generated_stud_id', 'like', '%'.$searchString.'%');
        $result = $sql->get()->toArray();
        $returnData = [];
        $filePath = Config::get('constants.uploadFilePath.StudentPics');

        for ($i = 0; $i < count($result); $i++) {
            $destinationPath = Helpers::changeRootPath($filePath, $result[$i]['id']);
            $profile_pic = $destinationPath['default'].$result[$i]['profile_picture'];
            if (file_exists($profile_pic) && ! empty($result[$i]['profile_picture'])) {
                $returnData[$i]['profile_picture'] = $destinationPath['view'].$result[$i]['profile_picture'];
            } else {
                $returnData[$i]['profile_picture'] = url('/').'/icon/profile.png';
            }
            $returnData[$i]['id'] = $result[$i]['id'];
            $returnData[$i]['first_name'] = $result[$i]['first_name'].' '.$result[$i]['family_name'].' <div class="stu_id">Student ID:'.($result[$i]['generated_stud_id'] == '' ? 'N/A' : $result[$i]['generated_stud_id']).'</div>';
            $returnData[$i]['generatedId'] = ($result[$i]['generated_stud_id'] == '' ? 'N/A' : $result[$i]['generated_stud_id']);
        }
        if (count($result) == 0) {
            $returnData[0]['id'] = '';
            $returnData[0]['first_name'] = 'No record Found';
            $returnData[0]['profile_picture'] = url('/').'/icon/profile.png';
            $returnData[0]['generatedId'] = '';
        }
        $countTotal = (count($result) == 0 ? 1 : count($result));

        return $returnData;
    }

    // Chetan changes Start --> 04-09-2018
    public function getUnCompleteApplicationdatatableData($request)
    {

        $tenantPrefix = Config::get('tenancy.database.prefix');
        $collegeId = Auth::user()->college_id;
        $currentUrl = explode('/', url()->current());
        $currentUrl = explode('.', $currentUrl[2]);
        $applicationurl = getStudentApplicationUrl().Auth::user()->id;

        $requestData = $_REQUEST;
        $columns = [
            // datatable column index  => database column name
            0 => 'rto_students.application_reference_id',
            1 => 'rto_students.created_at',
            2 => 'rto_students.first_name',
            3 => 'ra.agency_name',
            4 => 'rc.course_code',
            5 => 'rsc.offer_status',
            //          5 => 'rc.course_name',
            6 => 'rsc.start_date',
            7 => 'rto_students.USI',
            8 => 'rto_students.generated_stud_id',
            9 => 'rsc.coe_name',
            10 => 'rsc.offer_id',
            11 => 'rsc.finish_date',
            12 => 'ru.name',
        ];

        $query = Students::from('rto_students')
            ->leftjoin('rto_student_courses as rsc', 'rto_students.id', '=', 'rsc.student_id')
            ->leftjoin('rto_agents as ra', 'ra.id', '=', 'rsc.agent_id')
            ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->leftjoin('rto_campus as rcms', 'rcms.id', '=', 'rsc.campus_id')
            ->leftjoin('rto_users as ru', 'ru.id', '=', 'rto_students.created_by')
            ->where('rto_students.college_id', '=', $collegeId)
            ->where('rto_students.is_applicant', '=', 1);
        //                ->where('rto_students.current_situation', '!=', 6)
        //                ->where('rsc.offer_status', '!=', 'Enrolled');

        if (! empty($requestData['search']['value'])) {   // if there is a search parameter, $requestData['search']['value'] contains search parameter
            $searchVal = $requestData['search']['value'];
            $query->where(function ($query) use ($columns, $searchVal, $requestData) {
                $flag = 0;
                $statusArray = ['New Application Request', 'New', 'Request', 'New Application', 'Application Request'];
                foreach ($columns as $key => $value) {
                    $searchVal = $requestData['search']['value'];
                    if ($key == 6 || $key == 11) {
                        //                                $searchVal = date('Y-m-d', strtotime($searchVal));
                        $search = date('Y-m-d', strtotime($searchVal));
                        $searchVal = ($search == '1970-01-01') ? null : $search;
                    } elseif ($key == 5 && in_array(ucwords($searchVal), $statusArray)) {
                        //                            }else if($key == 5 && strcasecmp("New Application Request",$searchVal) == 0 || strcasecmp("New Application",$searchVal) == 0 || strcasecmp("New",$searchVal) == 0){
                        $searchVal = 'In Application';
                    }
                    if ($requestData['columns'][$key]['searchable'] == 'true' && $searchVal != '') {
                        if ($flag == 0) {
                            $query->where($value, 'like', '%'.$searchVal.'%');
                            $flag = $flag + 1;
                        } else {
                            $query->orWhere($value, 'like', '%'.$searchVal.'%');
                        }
                    }
                }
            });
        }

        $dropDownValue5 = $requestData['columns'][5]['search']['value'];

        $dropDownSeperatedval = explode(',', $dropDownValue5);

        if (! empty($dropDownValue5) && $dropDownValue5 !== 'Unknown') {
            $query->whereIn('rsc.offer_status', $dropDownSeperatedval);
        }

        $temp = $query->orderBy($columns[$requestData['order'][0]['column']], $requestData['order'][0]['dir']);

        $totalData = count($temp->get());
        $totalFiltered = count($temp->get());

        $resultArr = $query->skip($requestData['start'])
            ->take($requestData['length'])
            ->select('rto_students.id', 'rto_students.uuid', 'rsc.course_id as stucourseid', 'rto_students.first_name', 'rto_students.family_name', 'rto_students.nickname', 'rto_students.application_reference_id', 'rsc.offer_status', 'rto_students.created_at as createdAt', 'rto_students.USI', 'rsc.start_date', 'rsc.finish_date', 'ra.agency_name as agent_name', 'rc.course_code', 'rc.course_name', 'rsc.coe_name', 'rc.id as course_id', 'rsc.id as primary_id', 'rto_students.status', 'rsc.offer_id', 'rsc.status as checkstatus', 'rto_students.generated_stud_id', 'rto_students.stud_id_type', 'rto_students.is_applicant', 'rto_students.is_offered', 'rto_students.is_student', 'rcms.name as campus_name', 'ru.name as created_by_user')
            ->get();

        $data = [];
        foreach ($resultArr as $row) {
            $actionHtml = '';
            $applied_course_code = empty($row['course_code']) ? '' : $row['course_code'];
            $applied_course_name = empty($row['course_name']) ? '' : $row['course_name'];
            $applied_campus = empty($row['campus_name']) ? '' : $row['campus_name'];
            $applied_campus_name = (strlen($applied_campus) > 10) ? (substr($applied_campus, 0, 10).'...') : $applied_campus;
            $ostatus = (($row['offer_status'] == 'In Application') ? 'New Application Request' : (! empty($row['offer_status']))) ? $row['offer_status'] : 'Incomplete';
            $status_color = '';
            if ($ostatus == 'New Application Request') {
                $status_color = 'gray';
            } elseif ($ostatus == 'Reconsider') {
                $status_color = '#f0d723';
            } elseif ($ostatus == 'Rejected') {
                $status_color = '#ea6747';
            } elseif ($ostatus == 'Offered') {
                $status_color = '#70b23f';
            } elseif ($ostatus == 'Pending') {
                $status_color = '#3ab6cd';
            } elseif ($ostatus == 'Incomplete') {
                $status_color = '#c33d43';
            } else {
                $status_color = '#087cff';
            }
            $applied_course = (empty($applied_course_code)) ? '' : $applied_course_code.' : '.$applied_course_name;
            $actionHtml .= "<li><a class='link-black text-sm' data-original-title='View Edit this Application Detail' data-toggle='tooltip' id='' target='_blank' href='".$applicationurl.'/'.$row['uuid'].getStudentApplicationTokenParam()."'><i class='fa fa-book'></i></a></li>";
            $actionHtml .= "<li><span data-toggle='modal' class='delete' data-id='".$row['id'].'-'.$row['stucourseid']."'  data-target='#deleteModal'> <a class='link-black text-sm delete' data-toggle='tooltip' data-original-title='Delete' href='javascript:;'><i class='fa fa-remove'></i> </a> </span></li>";

            $action = '<div class="action-overlay">
                            <ul class="icon-actions-set">
                                '.$actionHtml.'
                            </ul>
                        </div>';
            $fullCourse = $applied_course;

            $applied_course = (strlen($applied_course) > 25) ? (substr($applied_course, 0, 25).'...') : $applied_course;
            $agent_name = (strlen($row['agent_name']) > 19) ? (substr($row['agent_name'], 0, 19).'...') : $row['agent_name'];
            $nestedData = [];
            $nestedData[] = $row['application_reference_id'].$action;
            $nestedData[] = date('d M Y', strtotime($row['createdAt']));
            $nestedData[] = $row['first_name'].' '.$row['family_name'];
            $nestedData[] = '<span data-toggle="tooltip" data-original-title="'.$row['agent_name'].'" aria-hidden="true">'.$agent_name.'</span>';
            $nestedData[] = '<span data-toggle="tooltip" data-original-title="'.$applied_campus.'" aria-hidden="true">'.$applied_campus_name.'</span>';
            $nestedData[] = '<span data-toggle="tooltip" data-original-title="'.$fullCourse.'" aria-hidden="true">'.$applied_course.'</span>';
            $nestedData[] = '<span class="status-box" style="background-color:'.$status_color.'"> '.$ostatus.' </span>';
            $nestedData[] = (empty($row['start_date']) ? '-' : date('d M Y', strtotime($row['start_date']))).' - '.(empty($row['finish_date']) ? '-' : date('d M Y', strtotime($row['finish_date'])));
            $nestedData[] = ($row['stud_id_type'] == 'generate' ? $row['generated_stud_id'] : '-');
            $nestedData[] = ($row['stud_id_type'] == 'reserve' ? $row['generated_stud_id'] : '-');
            $nestedData[] = $row['coe_name'];
            $nestedData[] = $row['USI'];
            $nestedData[] = empty($row['offer_id']) ? '-' : $row['offer_id'];
            $nestedData[] = $row['created_by_user'];

            $data[] = $nestedData;
        }
        $json_data = [
            'draw' => intval($requestData['draw']), // for every request/draw by clientside , they send a number as a parameter, when they recieve a response/data they first check the draw number, so we are sending same number in draw.
            'recordsTotal' => intval($totalData), // total number of records
            'recordsFiltered' => intval($totalFiltered), // total number of records after searching, if there is no searching then totalFiltered = totalData
            'data' => $data,   // total data array
        ];

        return $json_data;
    }

    public function getUnCompleteApplicationdatatableDataForAgent($collegeId, $userId)
    {

        /*$query = Students::from('rto_students')
                ->leftjoin('rto_student_courses as rsc', 'rto_students.id', '=', 'rsc.student_id')
                ->leftjoin('rto_agents as ra', 'ra.id', '=', 'rsc.agent_id')
                ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
                ->leftjoin('rto_campus as rcms', 'rcms.id', '=', 'rsc.campus_id')
                ->leftjoin('rto_users as ru', 'ru.id', '=', 'rto_students.created_by')
                ->where('rto_students.created_by', '=', $userId)
                ->where('rto_students.college_id', '=', $collegeId)
                ->where('rto_students.is_applicant', '=', 1)
                ->get(['rto_students.id', 'rto_students.first_name', 'rto_students.family_name', 'rto_students.nickname', 'rto_students.application_reference_id', 'rsc.offer_status', 'rto_students.created_at as createdAt', 'rto_students.USI', 'rsc.start_date', 'rsc.finish_date', 'ra.agency_name as agent_name', 'rc.course_code', 'rc.course_name', 'rsc.coe_name', 'rc.id as course_id', 'rsc.id as primary_id', 'rto_students.status', 'rsc.offer_id', 'rsc.status as checkstatus', 'rto_students.generated_stud_id', 'rto_students.stud_id_type', 'rto_students.is_applicant', 'rto_students.is_offered', 'rto_students.is_student', 'rcms.name as campus_name', 'ru.name as created_by_user']);*/

        $query = Students::from('rto_students')
            ->leftjoin('rto_student_courses as rsc', 'rto_students.id', '=', 'rsc.student_id')
            ->leftjoin('rto_agents as ra', 'ra.id', '=', 'rsc.agent_id')
            ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->leftjoin('rto_campus as rcms', 'rcms.id', '=', 'rsc.campus_id')
            ->leftjoin('rto_users as ru', 'ru.id', '=', 'rto_students.created_by')
            ->where('rto_students.created_by', '=', $userId)
            ->where('rto_students.college_id', '=', $collegeId)
            ->where('rto_students.is_applicant', '!=', 0)
            ->get(['rto_students.id', 'rto_students.uuid', 'rto_students.first_name', 'rto_students.family_name', 'rto_students.nickname', 'rto_students.application_reference_id', 'rsc.offer_status', 'rto_students.created_at as createdAt', 'rto_students.USI', 'rsc.start_date', 'rsc.finish_date', 'ra.agency_name as agent_name', 'rc.course_code', 'rc.course_name', 'rsc.coe_name', 'rc.id as course_id', 'rto_students.status', 'rsc.offer_id', 'rsc.status as checkstatus', 'rto_students.generated_stud_id', 'rto_students.stud_id_type', 'rto_students.is_applicant', 'rto_students.is_offered', 'rto_students.is_student', 'rcms.name as campus_name', 'ru.name as created_by_user', 'rto_students.student_type', 'rto_students.id as student_id', 'rsc.course_id', 'rsc.id as student_course_id']);

        return $query;
    }

    public function getCompleteApplicationdatatableDataForAgentStaff($collegeId, $userId)
    {

        $query = Students::from('rto_students')
            ->leftjoin('rto_student_courses as rsc', 'rto_students.id', '=', 'rsc.student_id')
            ->leftjoin('rto_agents as ra', 'ra.id', '=', 'rsc.agent_id')
            ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->leftjoin('rto_campus as rcms', 'rcms.id', '=', 'rsc.campus_id')
            ->leftjoin('rto_users as ru', 'ru.id', '=', 'rto_students.created_by')
            ->where('rto_students.created_by', '=', $userId)
            ->where('rto_students.college_id', '=', $collegeId)
            ->where('rto_students.is_applicant', '=', 0)
            ->get(['rto_students.id', 'rto_students.first_name', 'rto_students.family_name', 'rto_students.nickname', 'rto_students.application_reference_id', 'rsc.offer_status', 'rto_students.created_at as createdAt', 'rto_students.USI', 'rsc.start_date', 'rsc.finish_date', 'ra.agency_name as agent_name', 'rc.course_code', 'rc.course_name', 'rsc.coe_name', 'rc.id as course_id', 'rsc.id as primary_id', 'rto_students.status', 'rsc.offer_id', 'rsc.status as checkstatus', 'rto_students.generated_stud_id', 'rto_students.stud_id_type', 'rto_students.is_applicant', 'rto_students.is_offered', 'rto_students.is_student', 'rcms.name as campus_name', 'ru.name as created_by_user', 'rto_students.student_type', 'rsc.student_id', 'rsc.course_id', 'rsc.id as student_course_id']);

        return $query;
    }

    public function editStudentsFromStudentProfile($collegeId, $request)
    {
        $username = ($request->input('username') != '') ? $request->input('username') : null;
        $phone = ($request->input('phone') != '') ? $request->input('phone') : null;
        $mobile = ($request->input('mobile') != '') ? $request->input('mobile') : null;
        $email = ($request->input('email') != '') ? $request->input('email') : null;
        $name = ($request->input('name') != '') ? $request->input('name') : null;

        $arrName = explode(' ', $name);
        $first_name = $arrName[0];
        $family_name = ($arrName[1] != '') ? $arrName[1] : null;

        $objRtoUsers = Students::where('generated_stud_id', '=', $username)->where('college_id', '=', $collegeId)->update([
            'first_name' => $first_name,
            'family_name' => $family_name,
            'current_work_phone' => $phone,
            'current_mobile_phone' => $mobile,
            'email' => $email]);

        return true;
    }
}
