<?php

namespace App\Model\Traits;

use App\Model\v2\Country;
use App\Model\v2\StudentCourses;
use App\Model\v2\StudentDetails;

trait AgentStudentFilterTrait
{
    public function studentDetails()
    {
        return $this->hasOne(StudentDetails::class, 'student_id', 'id');
    }

    public function studentCourses()
    {
        return $this->hasMany(StudentCourses::class, 'student_id', 'id');
    }

    public function country()
    {
        return $this->belongsTo(Country::class, 'current_country', 'id');
    }

    public function scopeFilterCollegeId($query, $value)
    {
        if (empty($value)) {
            return $query;
        }

        return $query->where('college_id', $value);
    }

    public function scopeFilterAgentId($query, $value)
    {
        if (empty($value)) {
            return $query;
        }

        return $query->whereHas('studentCourses', function ($q) use ($value) {
            $q->where('agent_id', $value);
        });
    }
}
