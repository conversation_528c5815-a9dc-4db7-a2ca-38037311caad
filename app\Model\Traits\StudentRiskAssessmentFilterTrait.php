<?php

namespace App\Model\Traits;

trait StudentRiskAssessmentFilterTrait
{

    public function scopeFilterStatus(
        $query,
        $val
    ) {
        if ($val === null || $val === '') {
            return $query;
        }

        return $query->where('status', $val);
    }

    public function scopeFilterRiskType(
        $query,
        $val
    ) {
        if ($val === null || $val === '') {
            return $query;
        }

        return $query->where('risk_type', $val);
    }
}
