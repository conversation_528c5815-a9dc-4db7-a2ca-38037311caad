<?php

namespace App\Model\Traits;

trait StudentRiskAssessmentFilterTrait
{

    public function scopeInitFilters($query)
    {
        // Apply filters from request
        $filters = request()->input('filters', []);
        if (is_string($filters)) {
            $filters = json_decode($filters, true) ?? [];
        }

        // Apply risk type filter (handle 0 value properly)
        if (isset($filters['risk_type']) && $filters['risk_type'] !== null && $filters['risk_type'] !== '') {
            $query->where('risk_type', $filters['risk_type']);
        }

        // Apply status filter (single select)
        if (isset($filters['status']) && $filters['status'] !== null && $filters['status'] !== '') {
            $query->where('status', $filters['status']);
        }

        return $query;
    }

    public function scopeFilterStatus(
        $query,
        $val
    ) {
        if ($val === null || $val === '' || $val === 'all') {
            return $query;
        }

        return $query->where('status', $val);
    }

    public function scopeFilterRisk_type(
        $query,
        $val
    ) {
        if ($val === null || $val === '' || $val === 'all') {
            return $query;
        }

        return $query->where('risk_type', $val);
    }
}
