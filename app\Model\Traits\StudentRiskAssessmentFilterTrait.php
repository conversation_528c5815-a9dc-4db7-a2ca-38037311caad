<?php

namespace App\Model\Traits;

trait StudentRiskAssessmentFilterTrait
{
    public function scopeInitAllFilters($query)
    {
        $filters = request()->input('filters', []);
        if (is_string($filters)) {
            $filters = json_decode($filters, true) ?? [];
        }
        if (isset($filters['query']) && ! empty($filters['query'])) {
            $search = $filters['query'];
            $query->where(function ($q) use ($search) {
                $q->where('remarks', 'like', "%{$search}%");
            });
        }

        return $query;
    }

    public function scopeFilterStatus($query, $val)
    {
        if ($val === null || $val === '' || $val === 'all') {
            return $query;
        }

        return $query->where('status', $val);
    }

    public function scopeFilterRisk_type($query, $val)
    {
        if ($val === null || $val === '' || $val === 'all') {
            return $query;
        }

        return $query->where('risk_type', $val);
    }
}
