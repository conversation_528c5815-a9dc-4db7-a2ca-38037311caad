<?php

namespace App\Model\v2;

use App\Classes\SiteConstants;
use App\Model\Traits\AgentFilterTrait;
use DB;
use Domains\Students\Collections\Invoice\Update\Collection;
use Domains\Xero\Traits\XeroableContact;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;
use Integrations\Base\Models\IntegrationItem;
use Integrations\Base\Traits\ThirdPartySyncableItem;
use Integrations\Zoho\Contracts\CanConnectToZoho;
use Integrations\Zoho\Entities\RecruitmentAgent;
use Integrations\Zoho\Entities\ZohoPayload;
use Integrations\Zoho\Traits\ZohoSyncableItem;

class Agent extends Model implements CanConnectToZoho
{
    use AgentFilterTrait;
    use ThirdPartySyncableItem;
    use XeroableContact;
    use ZohoSyncableItem;

    protected $table = 'rto_agents';

    protected $fillable = [
        'college_id',
        'agent_code',
        'agency_name',
        'contact_person',
        'primary_email',
        'alertnet_email',
        'website',
        'telephone',
        'fax',
        'mobile1',
        'mobile2',
        'industry_id',
        'account_manager_id',
        'super_agent_id',
        'total_employess',
        'notes',
        'office_address',
        'office_country',
        'office_city',
        'office_state',
        'office_postcode',
        'office_ABN',
        'postal_address',
        'postal_country',
        'postal_city',
        'postal_state',
        'postal_postcode',
        'postal_ABN',
        'target_primary_country',
        'target_secondry_country',
        'targeted_country',
        'status',
        'is_lock',
        'bank_name',
        'bank_branch',
        'bank_account_name',
        'bank_account_number',
        'BSB',
        'bank_swift_code',
        'bank_country',
        'user_id',
        'isCompleteStepNo',
        'created_by',
        'updated_by',
        'is_tenant_default',
    ];

    protected $casts = [
        'isCompleteStepNo' => 'array',
    ];

    public const STEPS = [
        1 => 'agency_details',
        2 => 'address_and_postal',
        3 => 'bank_details',
        4 => 'target_recruitment_country',
        5 => 'referee_detail',
        6 => 'referee_detail',
    ];

    use HasFactory;

    protected static function booted()
    {
        // static::addGlobalScope('default', function ($builder) {
        //     $builder->where('is_tenant_default', 0);
        // });
    }

    public function getFormattedNameAttribute()
    {
        return collect([$this->agency_name, '-', 'AG'.$this->id])->filter(function ($item) {
            return $item != '';
        })->join('');
    }

    public function getZohoPayload(): ZohoPayload
    {
        return RecruitmentAgent::FromAgent($this);
    }

    public static function CreateFromZohoPayload(RecruitmentAgent $agentPayload, $collegeId = null)
    {
        // Get current user's college_id if not provided
        if (! $collegeId) {
            $collegeId = auth()->check() ? auth()->user()->college_id : null;

            // If no authenticated user, try to get from tenant context or default
            if (! $collegeId && function_exists('tenant')) {
                $tenant = tenant();
                if ($tenant && isset($tenant->college_id)) {
                    $collegeId = $tenant->college_id;
                }
            }
        }

        // Check if agent already exists by email and agent_code
        $existingAgent = null;

        /*if ($agentPayload->name) {
            // $query = Agent::where('primary_email', $agentPayload->email);
            $query = Agent::where('agency_name', $agentPayload->name);
            if ($collegeId) {
                $query->where('college_id', $collegeId);
            }
            $existingAgent = $query->first();
        }*/

        // TODO:: GNG-4939 (Check by both email and agent_code as requested)
        if ($agentPayload->email && $agentPayload->agent_code) {
            $query = Agent::where(function ($q) use ($agentPayload) {
                if ($agentPayload->email) {
                    $q->where('primary_email', $agentPayload->email);
                }
                if ($agentPayload->agent_code) {
                    $q->where('agent_code', $agentPayload->agent_code);
                }
            });

            // If we have college_id, scope the search to that college
            if ($collegeId) {
                $query->where('college_id', $collegeId);
            }

            $existingAgent = $query->first();
        }

        // Use existing agent or create new one
        $agent = $existingAgent ?: new Agent;

        // Set college_id if we have it and it's a new agent
        if ($collegeId && ! $existingAgent) {
            $agent->college_id = $collegeId;
        }

        // Update agent data from Zoho payload - always override existing data
        if ($agentPayload->name) {
            $agent->agency_name = $agentPayload->name;
        }

        // Always update email, phone, website, and agent_code if provided (as requested by user)
        if ($agentPayload->email) {
            $agent->primary_email = $agentPayload->email;
        }

        if ($agentPayload->phone) {
            $agent->telephone = $agentPayload->phone;
        }

        if ($agentPayload->website) {
            $agent->website = $agentPayload->website;
        }

        if ($agentPayload->agent_code) {
            $agent->agent_code = $agentPayload->agent_code;
        }

        if ($agentPayload->status) {
            $agent->status = $agentPayload->status == 'Inactive' ? 0 : 1;
        }

        if ($agentPayload->state) {
            $agent->postal_state = $agentPayload->state;
        }

        if ($agentPayload->person) {
            $agent->contact_person = $agentPayload->person;
        }

        // Set default values for new agents
        if (! $existingAgent) {
            $agent->is_tenant_default = 0;
            if (auth()->check()) {
                $agent->created_by = auth()->user()->id;
                $agent->updated_by = auth()->user()->id;
            }
        } else {
            // Update the updated_by field for existing agents
            if (auth()->check()) {
                $agent->updated_by = auth()->user()->id;
            }
        }

        $agent->save();

        Log::info('Agent '.($existingAgent ? 'updated' : 'created').' from Zoho', [
            'agent_id' => $agent->id,
            'email' => $agent->primary_email,
            'name' => $agent->agency_name,
            'phone' => $agent->telephone,
            'website' => $agent->website,
            'agent_code' => $agent->agent_code,
            'action' => $existingAgent ? 'update' : 'create',
            'zoho_sync' => true,
        ]);

        return $agent;
    }

    public function integrationItem()
    {
        return $this->morphOne(IntegrationItem::class, 'syncable');
    }

    public static function getLoginAgentInfo()
    {
        return Agent::where('user_id', auth()->guard()->user()->id)->get(['agency_name', 'id'])->first();
    }

    public function getAgentList($collegeId)
    {
        return Agent::where('college_id', $collegeId)
            ->orderBy('agency_name', 'ASC')
            ->get(['id as value', 'agency_name as label'])
            ->where('is_tenant_default', 0)
            ->toArray();
    }

    public static function getAgentsListForApi($data = [])
    {
        $qry = self::with([
            'parentAgent',
            'officeCountry',
            'postalCountry',
            'targetPrimaryCountry',
            'targetedCountry',
            'targetSecondryCountry',
            'bankCountry',
            'loginData',
        ])->withCount(['childAgents'])
            ->where('is_tenant_default', 0);

        $filterFields = [
            'name' => 'agency_name',
            'person' => 'contact_person',
            'email' => 'primary_email',
            'telephone' => 'telephone',
            'mobile' => ['mobile1', 'mobile2'],
            'country' => ['office_country', 'target_primary_country', 'target_secondry_country'],
            'recent' => 'created_at',
            'locked' => 'is_lock',
            'status' => 'status',
        ];
        $searchText = strtolower($data['search']);
        if (! empty($data['search'])) {

            $qry->whereRaw('LOWER(agency_name) LIKE ?', ['%'.$searchText.'%'])
                ->orWhereRaw('LOWER(contact_person) LIKE ?', ['%'.$searchText.'%'])
                ->orWhereRaw('LOWER(primary_email) LIKE ?', [$searchText.'%'])
                ->orWhereRaw('LOWER(telephone) LIKE ?', [$searchText.'%'])
                ->orWhereRaw('LOWER(mobile1) LIKE ?', [$searchText.'%'])
                ->orWhereRaw('LOWER(mobile2) LIKE ?', [$searchText.'%'])
                ->orWhereRaw('LOWER(office_address) LIKE ?', ['%'.$searchText.'%'])
                ->orWhereRaw('LOWER(office_city) LIKE ?', ['%'.$searchText.'%'])
                ->orWhereRaw('LOWER(office_state) LIKE ?', ['%'.$searchText.'%'])
                ->orWhereRaw('LOWER(office_ABN) LIKE ?', [$searchText.'%'])
                ->orWhereRaw('LOWER(tracking_text) LIKE ?', ['%'.$searchText.'%']);
        }
        if (! empty($data['sort']) && isset($filterFields[$data['sort']])) {
            if (is_array($filterFields[$data['sort']])) {
                foreach ($filterFields[$data['sort']] as $val) {
                    $qry->orderBy($val, $data['order'] ?? 'asc');
                }
            } else {
                $qry->orderBy($filterFields[$data['sort']], $data['order'] ?? 'asc');
            }
        } else {
            $qry->orderBy('agency_name', $data['order'] ?? 'asc');
        }

        return $qry->paginate($data['limit'] ?? 25);
    }

    public static function getAgentDetailForApi($id)
    {
        $agent = self::with([
            'childAgents' => function ($q) {
                $q->orderBy('agency_name', 'asc')->limit(100);
            },
            'parentAgent',
            'officeCountry',
            'postalCountry',
            'targetPrimaryCountry',
            'targetedCountry',
            'targetSecondryCountry',
            'bankCountry',
            'loginData',
        ])->findOrFail($id);

        return $agent;
    }

    public static function addDateFiltersToQuery($q, $dateFilters = [])
    {
        if (! empty($dateFilters['startDate']) && ! empty($dateFilters['endDate'])) {
            $q->whereBetween('created_at', [$dateFilters['startDate'], $dateFilters['endDate']]);
        } elseif ($dateFilters['startDate']) {
            $q->where('created_at', '>=', $dateFilters['startDate']);
        } elseif ($dateFilters['endDate']) {
            $q->where('created_at', '<=', $dateFilters['endDate']);
        }
    }

    public static function getAgentStatsForApi($id, $dateFilters = [])
    {
        $agent = self::withCount([
            'totalStudentApplications' => function ($q) use ($dateFilters) {
                self::addDateFiltersToQuery($q, $dateFilters);
            },
            'totalStudentEnrollments' => function ($q) use ($dateFilters) {
                self::addDateFiltersToQuery($q, $dateFilters);
            },
            'totalStudentOfferGrants' => function ($q) use ($dateFilters) {
                self::addDateFiltersToQuery($q, $dateFilters);
            },
            'totalStudentOfferRejections' => function ($q) use ($dateFilters) {
                self::addDateFiltersToQuery($q, $dateFilters);
            },
            'totalCurrentStudents' => function ($q) use ($dateFilters) {
                self::addDateFiltersToQuery($q, $dateFilters);
            },
            'totalDistinctStudents' => function ($q) use ($dateFilters) {
                self::addDateFiltersToQuery($q, $dateFilters);
            },
        ])->findOrFail($id);

        $enrolledDataQry = StudentCourses::select([
            'status',
            DB::raw('count(id) AS count'),
        ])->where([
            'agent_id' => $id,
            'offer_status' => SiteConstants::ENROLLED_STATUS,
        ]);

        self::addDateFiltersToQuery($enrolledDataQry, $dateFilters);

        $enrolledData = $enrolledDataQry->groupBy('status')->get()->toArray();

        $agent->enrolledData = $enrolledData;

        return new Collection([
            'total_applications' => $agent->total_student_applications_count ?? 0,
            'total_enrollments' => $agent->total_student_enrollments_count ?? 0,
            'total_offer_grants' => $agent->total_student_offer_grants_count ?? 0,
            'total_offer_rejections' => $agent->total_student_offer_rejections_count ?? 0,
            'total_current_students' => $agent->total_current_students_count ?? 0,
            'students_count_by_status' => $agent->enrolledData ?? 0,
            'total_distinct_students' => $agent->total_distinct_students_count ?? 0,
            'filters' => $dateFilters,
        ]);

    }

    public static function CreateDefaultAgentForShortCourse()
    {
        $agent = new Agent([
            'college_id' => auth()->user()->college_id,
            'agency_name' => 'No Agent',
            'agent_code' => 'DEFAULT-AGENT',
            'status' => 1,
            'is_lock' => 1,
            'is_tenant_default' => 1,
        ]);
        $agent->save();

        return $agent;
    }
}
