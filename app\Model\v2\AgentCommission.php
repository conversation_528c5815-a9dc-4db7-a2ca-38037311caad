<?php

namespace App\Model\v2;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class AgentCommission extends Model
{
    use HasFactory;

    protected $table = 'rto_agent_commission';

    protected $fillable = [
        'college_id',
        'agent_status',
        'agent_id',
        'course_type',
        'course',
        'commission_period',
        'commission',
        'gst',
        'rate_valid_from',
        'rate_valid_to',
        'created_by',
        'updated_by',
    ];

    public function agent()
    {
        return $this->belongsTo(Agent::class, 'agent_id');
    }

    public function courseItem()
    {
        return $this->belongsTo(Courses::class, 'course');
    }

    public function scopeCollegeId($query, $value)
    {
        if (empty($value)) {
            return $query;
        }

        return $query->where('college_id', $value);
    }

    public function scopeFilterAgentId($query, $value)
    {
        if (empty($value)) {
            return $query;
        }

        return $query->where('agent_id', $value);
    }

    public function scopeFilterCourse($query, $value)
    {
        if (empty($value)) {
            return $query;
        }

        return $query->where('course', $value);
    }

    public function scopeFilterCommissionPeriod($query, $value)
    {
        if (empty($value)) {
            return $query;
        }

        return $query->where('commission_period', $value);
    }
}
