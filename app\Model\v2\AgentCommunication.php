<?php

namespace App\Model\v2;

use App\Helpers\Helpers;
use App\Model\SendMail;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Support\Traits\CreaterUpdaterTrait;

class AgentCommunication extends Model
{
    use CreaterUpdaterTrait;

    protected $table = 'rto_agent_communication';

    protected $fillable = [
        'college_id',
        'agent_id',
        'comment_by',
        'category',
        'status',
        'comment',
        'created_by',
        'updated_by',
    ];

    public function addAgentCommunication($request)
    {

        $loginUserId = Auth::user()->id;
        $collegeId = Auth::user()->college_id;
        $objAgentCommunication = new AgentCommunication;
        $objAgentCommunication->college_id = $collegeId;
        $objAgentCommunication->agent_id = $request->input('agent_id');
        $objAgentCommunication->comment_by = $request->input('comment_by');
        $objAgentCommunication->category = $request->input('category');
        $objAgentCommunication->status = $request->input('status');
        $objAgentCommunication->comment = $request->input('comment');
        $objAgentCommunication->save();
    }

    public function addAgentCommunicationData($request, $sendMail = false)
    {

        $college_id = $request['college_id'] ?? '';
        $agent_id = $request['agent_id'] ?? '';
        $comment_by = $request['comment_by'] ?? '';
        $from_email = $request['from_email'] ?? '';
        $to_email = $request['to_email'] ?? '';
        $request_category = $request['request_category'] ?? '';
        $request_subject = $request['request_subject'] ?? '';
        $request_content = $request['request_content'] ?? '';
        $status = $request['status'] ?? '';
        $comment = prepareCommunicationLogContent($to_email, $from_email, $request_subject, $request_content);

        $objRtoAgentEmail = new AgentCommunication;

        $objRtoAgentEmail->college_id = $college_id;
        $objRtoAgentEmail->agent_id = $agent_id;
        $objRtoAgentEmail->comment_by = $comment_by;
        $objRtoAgentEmail->category = $request_category;
        $objRtoAgentEmail->status = $status;
        $objRtoAgentEmail->comment = $comment;
        $objRtoAgentEmail->created_by = $comment_by;
        $objRtoAgentEmail->save();
        if ($sendMail) {
            $mailData = [
                'to' => $to_email,
                'from' => $from_email,
                'cc' => '',
                'replyTo' => $from_email,
                'bcc' => '',
                'subject' => $request_subject,
                'body' => $request_content,
            ];
            $mailStatus = (new SendMail)->sendSmtpMail($mailData);
        }

        return $objRtoAgentEmail->id;
    }

    public function editAgentCommunication($request, $agentId, $collegeId)
    {

        $objAgentCommunication = AgentCommunication::find($agentId);
        $objAgentCommunication->college_id = $collegeId;
        $objAgentCommunication->agent_id = $request->input('agent_id');
        $objAgentCommunication->comment_by = $request->input('comment_by');
        $objAgentCommunication->category = $request->input('category');
        $objAgentCommunication->status = $request->input('status');
        $objAgentCommunication->comment = $request->input('comment');
        $objAgentCommunication->created_by = $agentId;
        $objAgentCommunication->updated_by = $agentId;
        $objAgentCommunication->save();

    }

    public function getAgentCommunicationData($request, $type = 'all')
    {

        $collegeId = $request->college_id ?? null;
        $userId = $request->user_id ?? null;
        $agentId = $request->agent_id ?? null;
        $perPage = $request->take ?? 10;
        $search = $request->search ?? '';
        $start = $request->start ?? null;
        $end = $request->end ?? null;
        $requesttype = $request->requesttype ?? '';
        $sort = $request->sort ?? '';
        $dir = $request->dir ?? 'desc';
        if ($dir != 'asc' && $dir != 'desc') {
            $dir = 'desc';
        }
        $columnArr = [
            'rto_agent_communication.*',
            'section.value as category_name',
            'status.value as status_value',
            'agents.agency_name',
            'agents.contact_person',
            'agents.primary_email',
            'agents.website',
            'agents.telephone',
            'agents.fax',
            'agents.mobile1',
            'agents.mobile2',
            'agents.office_address',
            'agents.id AS agent_primary_id',
            'users.name AS user_name',
            'users.id AS user_id',
            'users_commented.name AS commented_by',
            DB::raw("'{$type}' as record_type"),
        ];

        $arrAgentCommunication = AgentCommunication::select($columnArr)
            ->join('rto_users as users_commented', 'users_commented.id', '=', 'rto_agent_communication.comment_by')
            ->join('rto_agents as agents', 'agents.id', '=', 'rto_agent_communication.agent_id')
            ->join('rto_users as users', 'users.id', '=', 'agents.user_id')
            ->join('rto_setup_section as section', 'section.id', '=', 'rto_agent_communication.category')
            ->join('rto_setup_section as status', 'status.id', '=', 'rto_agent_communication.status')
            ->where('rto_agent_communication.college_id', '=', $collegeId);
        if (! empty($requesttype) && $requesttype != 'all') {
            $arrAgentCommunication->where('category', $requesttype);
        }
        if ($start && $end) {
            $arrAgentCommunication->whereBetween('rto_agent_communication.created_at', [$start, $end]);
        } elseif ($start) {
            $arrAgentCommunication->where('rto_agent_communication.created_at', '>=', $start);
        } elseif ($end) {
            $arrAgentCommunication->where('rto_agent_communication.created_at', '<=', $end);
        }

        if (! empty($search)) {
            $arrAgentCommunication->where('comment', 'Like', "%{$search}%");
        }
        if ($type == 'inbox') {
            $arrAgentCommunication->where('rto_agent_communication.agent_id', '=', $agentId)
                ->where('rto_agent_communication.comment_by', '!=', $userId);
        } elseif ($type == 'outbox') {
            $arrAgentCommunication->where('rto_agent_communication.comment_by', '=', $userId);
        }
        if ($request->category) {
            if (is_array($this->category)) {
                $arrAgentCommunication->whereIn('rto_agent_communication.category', $request->category);

            } else {
                $arrAgentCommunication->where('rto_agent_communication.category', $request->category);
            }
        }
        $sortsMap = [
            'created_at' => 'rto_agent_communication.created_at',
            'commented_by' => 'users_commented.name',
            'category' => 'section.value',
        ];
        if (! empty($sort) && isset($sortsMap[$sort])) {
            // created_at
            $arrAgentCommunication->orderBy($sortsMap[$sort], $dir);
        } else {
            $arrAgentCommunication->orderBy('rto_agent_communication.id', $dir);
        }

        $result = $arrAgentCommunication->paginate($perPage);

        return $result;
    }

    public function listAgentCommunicationAjax($agentId, $collegeId)
    {
        return AgentCommunication::from('rto_agent_communication as rac')
            ->leftjoin('rto_agents as agents', 'agents.id', '=', 'rac.agent_id')
            ->leftjoin('rto_users as users', 'users.id', '=', 'rac.comment_by')
            ->leftjoin('rto_setup_section as rss1', 'rss1.id', '=', 'rac.category')
            ->leftjoin('rto_setup_section as rss2', 'rss2.id', '=', 'rac.status')
            ->where('rac.college_id', '=', $collegeId)
            ->where('rac.agent_id', '=', $agentId)
            ->get(['rac.*', 'agents.agency_name as agency_name', 'users.name as user_name', 'rss1.value as category', 'rss2.value as status']);
    }

    public function listAgentCommunicationData($collegeId, $agentId)
    {
        return AgentCommunication::where('id', '=', $agentId)
            ->where('college_id', '=', $collegeId)
            ->get()
            ->toarray();
    }

    public function rtoAgentCommunication($request, $savedEmails)
    {

        $totalAgentSelect = $request->input('agent_select');
        //        $agent_email = $request->input('agent_email');

        $loginUserId = Auth::user()->id;
        $collegeId = Auth::user()->college_id;

        // Start Default Status & Type/Category ID
        $sectionId = 3;                     // 3 for Diary-Agent as per DB:rto_setup_section_option
        $typeId = 2;                        // 6 for type_name="Type" as per DB:rto_setup_section_type
        $statusId = 3;                      // 7 for type_name="Status" as per DB:rto_setup_section_type
        $type_name = 'General';             // as per DB:rto_default_setup_section
        $status_name = 'Email Sent';        // as per DB:rto_default_setup_section
        $objSetupSection = new SetupSection;
        $defaultType = $objSetupSection->getSetupSectionId($collegeId, $sectionId, $typeId, $type_name);           // replace by 31
        $defaultStatus = $objSetupSection->getSetupSectionId($collegeId, $sectionId, $statusId, $status_name);     // replace by 34
        // End Default Status & Type/Category ID
        $savedEmails = collect($savedEmails);
        $rootFolder = config('constants.arrCollegeRootFolder');
        foreach ($totalAgentSelect as $key => $value) {

            $agentEmailSaved = $savedEmails->where('agent_id', $value)->first();
            $userId = $agentEmailSaved->crated_by;
            $fileName = $agentEmailSaved->attachment;
            $filePath = config('constants.uploadFilePath.AgentMailAttach');
            $destinationPath = Helpers::changeRootPath($filePath, $value);
            $fileSavedPath = $destinationPath['view'];
            $fileFullPath = $fileSavedPath.$fileName;
            $to = $request->input('agent_email'.$value) ?? null;
            $cc = $request->input('emial_CC') ?? null;
            $bcc = $request->input('emial_BCC') ?? null;
            $from = $request->input('email_from') ?? null;
            $subject = $request->input('subject') ?? null;
            $body = $request->input('email_body') ?? null;
            $attachments = ($fileFullPath) ? url($fileFullPath) : null;
            $attachments = '<a href="'.$attachments.'" title="'.$fileName.'">'.$fileName.'</a>';
            $comment = prepareCommunicationLogContent($to, $from, $subject, $body, $attachments, ['cc' => $cc, 'bcc' => $bcc]);

            $objRtoAgentEmail = new AgentCommunication;

            $objRtoAgentEmail->college_id = $collegeId;
            $objRtoAgentEmail->agent_id = $value;
            $objRtoAgentEmail->comment_by = $loginUserId;
            $objRtoAgentEmail->category = $defaultType;
            $objRtoAgentEmail->status = $defaultStatus;
            $objRtoAgentEmail->comment = $comment;
            $objRtoAgentEmail->created_by = $loginUserId;
            $objRtoAgentEmail->updated_by = $loginUserId;
            $objRtoAgentEmail->save();
        }
    }

    public function agent()
    {
        return $this->belongsTo(Agent::class, 'agent_id');
    }

    public function user()
    {
        return $this->belongsTo(Users::class, 'comment_by');
    }

    public function setupType()
    {
        return $this->belongsTo(SetupSection::class, 'category');
    }

    public function setupStatus()
    {
        return $this->belongsTo(SetupSection::class, 'status');
    }

    public function scopeFilterAgentId($query, $value)
    {
        if (empty($value)) {
            return $query;
        }

        return $query->where('agent_id', $value);
    }

    public function scopeCollegeId($query, $value)
    {
        if (empty($value)) {
            return $query;
        }

        return $query->where('college_id', $value);
    }
}
