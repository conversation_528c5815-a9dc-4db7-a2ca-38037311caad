<?php

namespace App\Model\v2;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Support\Traits\CreaterUpdaterTrait;

class CollegeCampus extends Model
{
    use CreaterUpdaterTrait;
    use HasFactory;

    const STATUS_ACTIVE = 1;

    protected $table = 'rto_campus';

    protected $fillable = [
        'college_id',
        'name',
        'status_default',
        'status',
        'created_by',
        'updated_by',
    ];

    public function intakeDates()
    {
        return $this->hasMany(CampusIntake::class, 'id', 'campus_id');
    }

    public function courses()
    {
        return $this->hasMany(CollegeCourse::class, 'id', 'campus_id');
    }

    public function venues()
    {
        return $this->hasMany(CampusVenue::class, 'campus_id', 'id');
    }

    public function scopeActive($q)
    {
        return $q->where('status', self::STATUS_ACTIVE);
    }

    public function toSearchableArray($context = null): array
    {
        return [
            'id' => (int) $this->id,
            'name' => $this->name,
        ];
    }
}
