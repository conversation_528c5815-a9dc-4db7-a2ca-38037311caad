<?php

namespace App\Model\v2;

use App\DTO\commons\CollegeContactDTO;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Str;

class Colleges extends Model
{
    use HasFactory;

    protected $table = 'rto_colleges';

    protected $fillable = [
        'id', 'RTO_code', 'CRICOS_code', 'college_name', 'public_RTO', 'legal_name', 'contact_person', 'contact_phone',
        'contact_email', 'college_url', 'college_logo', 'college_signature', 'dean_name', 'dean_signature', 'admission_manager_signature',
        'student_support_signature', 'contract_start_date', 'contract_end_date', 'status', 'timezone',
        'onboard_setup', 'course_type_for_api', 'color_profile', 'enable_stripe_checkout_from_api', 'created_at', 'updated_at',
    ];

    public function collegedetail()
    {
        return $this->hasOne(CollegeDetails::class, 'college_id', 'id');
    }

    public function setupsection()
    {
        return $this->hasOne(SetupSection::class, 'college_id', 'id');
    }

    public function offerdocumnet()
    {
        return $this->hasOne(OfferDocumentChecklist::class, 'college_id', 'id');
    }

    public function agentdocumnet()
    {
        return $this->hasOne(AgentDocumentChecklist::class, 'college_id', 'id');
    }

    public function offertracking()
    {
        return $this->hasOne(OfferTrackingStatus::class, 'college_id', 'id');
    }

    public function customchecklist()
    {
        return $this->hasOne(Checklist::class, 'college_id', 'id');
    }

    public function campus()
    {
        return $this->hasOne(CollegeCampus::class, 'college_id', 'id');
    }

    public function venue()
    {
        return $this->hasOne(CampusVenue::class, 'college_id', 'id');
    }

    public function room()
    {
        return $this->hasOne(Classroom::class, 'college_id', 'id');
    }

    public function getCollegeMail($collegeId): CollegeContactDTO
    {
        $result = Colleges::join('rto_college_details as rcd', 'rcd.college_id', '=', 'rto_colleges.id')
            ->where('rto_colleges.id', '=', $collegeId)
            ->select([
                'rto_colleges.contact_email',
                'rcd.marketing_email',
                'rcd.orgazination_id',
                'rcd.admission_email',
                'rcd.it_email',
                'rcd.acedemic_email',
                'rcd.account_email',
            ])
            ->first();

        $fieldValueMap = [
            'contact_email' => ['type' => 'Contact Email', 'email' => null],
            'marketing_email' => ['type' => 'Marketing Email', 'email' => null],
            'admission_email' => ['type' => 'Admission Email', 'email' => null],
            'it_email' => ['type' => 'IT Email', 'email' => null],
            'acedemic_email' => ['type' => 'Acedemic Email', 'email' => null],
            'account_email' => ['type' => 'Account Email', 'email' => null],
        ];
        $resultData = [];
        foreach ($fieldValueMap as $key => $value) {
            $resultData[$key] = $result->$key ?? null;
        }

        return CollegeContactDTO::LazyFromArray($resultData);
    }

    public function getCollegeMailV2($collegeId)
    {
        $result = Colleges::join('rto_college_details as rcd', 'rcd.college_id', '=', 'rto_colleges.id')
            ->where('rto_colleges.id', $collegeId)
            ->first([
                'rto_colleges.contact_email',
                'rcd.marketing_email',
                'rcd.orgazination_id',
                'rcd.admission_email',
                'rcd.it_email',
                'rcd.acedemic_email',
                'rcd.account_email',
            ]);

        if (! $result) {
            return []; // Return an empty array if no record is found
        }

        $arrList = [];

        $arrList = collect($result)
            ->filter(fn ($email, $key) => ! empty($email) && str_ends_with($key, '_email'))
            ->map(fn ($email, $key) => [
                'type' => Str::title(str_replace('_', ' ', $key)),
                'email' => $email,
            ])
            ->sortBy('type')
            ->values()
            ->toArray();

        return $arrList;
    }

    public function collegeEmailGet($collegeId)
    {
        // return Colleges::where('id', $collegeId)->get(['contact_email'])->toarray();
        return CollegeDetails::where('college_id', $collegeId)->value('marketing_email');
    }

    public static function getCollegeInfoQuery()
    {
        $collegeId = auth()->user()->college_id ?? null;
        if ($collegeId) {
            $college = Colleges::where('id', $collegeId);
        } else {
            $college = Colleges::orderByDesc('status')->orderByDesc('id');
        }

        return $college;
    }
}
