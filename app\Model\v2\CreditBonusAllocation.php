<?php

namespace App\Model\v2;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class CreditBonusAllocation extends Model
{
    use HasFactory;

    protected $table = 'rto_credit_bonus_allocation';

    protected $fillable = [
        'college_id',
        'agent_id',
        'std_transaction_no',
        'type',
        'amount',
        'payment_mode',
        'credit_used',
        'payment_date',
        'comment',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'payment_date' => 'date',
        'created_at' => 'datetime',
        'updated_at' => 'datetime',
    ];

    public function agent()
    {
        return $this->belongsTo(Agent::class, 'agent_id', 'id');
    }

    public function createdBy()
    {
        return $this->belongsTo(Users::class, 'created_by', 'id');
    }

    public function updatedBy()
    {
        return $this->belongsTo(Users::class, 'updated_by', 'id');
    }

    public function paymentMode()
    {
        return $this->belongsTo(PaymentMode::class, 'payment_mode', 'id');
    }

    public function scopeFilterAgentId($query, $value)
    {
        if (empty($value)) {
            return $query;
        }

        return $query->where('agent_id', $value);
    }

    public function scopeFilterQuery($query, $value)
    {
        if (empty($value)) {
            return $query;
        }

        $searchTerm = '%'.trim($value).'%';

        return $query->where(function ($q) use ($searchTerm) {
            $q->where('comment', 'like', $searchTerm)
                ->orWhere('amount', 'like', $searchTerm)
                ->orWhere('credit_used', 'like', $searchTerm)
                ->orWhere('payment_date', 'like', $searchTerm)
                ->orWhereHas('agent', function ($q) use ($searchTerm) {
                    $q->where('first_name', 'like', $searchTerm)
                        ->orWhere('family_name', 'like', $searchTerm);
                })
                ->orWhereHas('paymentMode', function ($q) use ($searchTerm) {
                    $q->where('name', 'like', $searchTerm);
                });
        });
    }
}
