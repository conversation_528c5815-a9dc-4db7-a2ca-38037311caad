<?php

namespace App\Model\v2;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\Contracts\Activity;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class EmailTemplate extends Model
{
    use HasFactory;
    use LogsActivity;

    protected $table = 'rto_email_template';

    protected $fillable = [
        'id',
        'college_id',
        'email_subject',
        'template_name',
        'recipient',
        'content',
        'status',
        'file',
        'locked',
        'created_by',
        'updated_by',
    ];

    protected $logAttributes = [
        'college_id',
        'email_subject',
        'template_name',
        'recipient',
        'content',
        'status',
        'file',
        'locked',
    ];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logFillable()
            ->logOnlyDirty()
            ->setDescriptionForEvent(fn (string $eventName) => "Email template has been {$eventName}");
    }

    public function tapActivity(Activity $activity, string $eventName)
    {
        $activity->log_name = (new self)->getMorphClass();
        $activity->description = "$this->template_name email template $eventName";

    }

    public function attachments()
    {
        return $this->hasMany(EmailTemplateDocuments::class, 'email_template_id', 'id');
    }

    public function scopeFilterRecipient($query, $value)
    {
        if (empty($value)) {
            return $query;
        }

        return $query->where('recipient', $value)->pluck('template_name as name', 'id');
    }
}
