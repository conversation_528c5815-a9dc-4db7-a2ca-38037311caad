<?php

namespace App\Model\v2;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class EmailTemplate extends Model
{
    use HasFactory;

    protected $table = 'rto_email_template';

    protected $fillable = [
        'id',
        'college_id',
        'email_subject',
        'template_name',
        'recipient',
        'content',
        'status',
        'file',
        'locked',
        'created_by',
        'updated_by',
    ];

    public function attachments()
    {
        return $this->hasMany(EmailTemplateDocuments::class, 'email_template_id', 'id');
    }

    public function scopeFilterRecipient($query, $value)
    {
        if (empty($value)) {
            return $query;
        }

        return $query->where('recipient', $value)->pluck('template_name as name', 'id');
    }
}
