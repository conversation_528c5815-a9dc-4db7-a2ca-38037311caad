<?php

namespace App\Model\v2;

use App\Model\CommonModel;
use Auth;
use Config;
use Helpers;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Validator;
use Spatie\Activitylog\Contracts\Activity;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class EmailTemplateDocuments extends Model
{
    use LogsActivity;

    //
    protected $table = 'rto_email_template_documents';

    protected $fillable = ['id', 'email_template_id', 'file'];

    protected $logAttributes = [
        'email_template_id',
        'file',
    ];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logFillable()
            ->logOnlyDirty()
            ->setDescriptionForEvent(fn (string $eventName) => "Email template documents has been {$eventName}");
    }

    public function tapActivity(Activity $activity, string $eventName)
    {
        // Handle null or empty template name
        $templateName = optional($this->template)->template_name ?? '';
        $activity->log_name = (new self)->getMorphClass();
        $activity->description = "$templateName email template documents $eventName";

    }

    public function template()
    {
        return $this->belongsTo(EmailTemplate::class, 'email_template_id');
    }

    public $timestamps = false;

    public function saveEmailTemplateDocuments($collegeId, $userId, $templateId, $request)
    {
        if (isset($request['documents'])) {
            $files = $request->file('documents');
            $file_count = count($files);
            $uploadcount = 0;

            $rootFolder = Config::get('constants.arrCollegeRootFolder');

            $filePath = Config::get('constants.uploadFilePath.Templates');
            $destinationPath = Helpers::changeRootPath($filePath, $templateId);
            $dataArr = [
                'college_id' => $collegeId,
                'folder_name' => $rootFolder['Templates'],
                'sub_folder_name' => $templateId,
                'user_id' => $userId,
            ];
            $objCommonModel = new CommonModel;
            $clgMaterialParentId = $objCommonModel->getSubParentId($dataArr);

            foreach ($files as $file) {

                $rules = ['file' => 'required'];
                $validator = Validator::make(['file' => $file], $rules);
                if ($validator->passes()) {
                    // $destinationPath = Config::get('constants.EmailTemplate.DocumentsPath');
                    $originalName = $file->getClientOriginalName();
                    $fileSize = $file->getSize();
                    $filename = time().$originalName;
                    $upload_success = $file->move($destinationPath['default'], $filename);
                    $uploadcount++;
                    $reqAgentImage = $filename;
                    $objRtoAgentImages = new EmailTemplateDocuments;
                    $objRtoAgentImages->email_template_id = $templateId;
                    $objRtoAgentImages->file = $filename;
                    if ($objRtoAgentImages->save()) {
                        $templateDataArr = [
                            'college_id' => $collegeId,
                            'original_name' => $originalName,
                            'file_name' => $filename,
                            'size' => $fileSize,
                            'type' => 'File',
                            'parent_id' => $clgMaterialParentId,
                            'file_path' => $destinationPath['view'],
                            'user_id' => $userId,
                        ];
                        $objCommonModel->addCollegeMaterialInfo($templateDataArr);
                    }
                }
            }

            return true;
        }
    }

    public function getDocumentsList($emailTemplateId)
    {
        $etEditDocuments = EmailTemplateDocuments::where('email_template_id', $emailTemplateId)
            ->get(['file', 'id'])->toArray();

        return $etEditDocuments;
    }

    public function editEmailTemplateStatus($emailTemplateId, $status)
    {
        $objEmailTemplate = EmailTemplate::find($emailTemplateId);

        $userId = Auth::user()->id;
        if ($status == 0) {
            $objEmailTemplate->status = 1;
        }
        if ($status == 1) {
            $objEmailTemplate->status = 0;
        }
        $objEmailTemplate->created_by = $userId;
        $objEmailTemplate->updated_by = $userId;
        $objEmailTemplate->save();
    }

    public function getDocumentsListDelete($emailTemplateId, $etDocumentId)
    {
        $etDocuments = EmailTemplateDocuments::find($etDocumentId);

        if (! is_null($etDocuments)) {
            $destinationPath = Config::get('constants.EmailTemplate.DocumentsPath');
            $file = $destinationPath.$etDocuments['file'];
            if (file_exists($file)) {
                unlink($file);
            }
            $etDocuments->delete();
        }

        $etDGetdocuments = EmailTemplateDocuments::where('email_template_id', $emailTemplateId)
            ->get(['file', 'id', 'email_template_id'])->toArray();

        return $etDGetdocuments;
    }
}
