<?php

namespace App\Model\v2;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\Contracts\Activity;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class ReportLetterFileAttachment extends Model
{
    use HasFactory;
    use LogsActivity;

    protected $table = 'rto_letter_file_attachment';

    protected $logAttributes = [
        'college_id',
        'letter_id',
        'letter_attachment',
    ];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logFillable()
            ->logOnlyDirty()
            ->setDescriptionForEvent(fn (string $eventName) => "letter template file attachment has been {$eventName}");
    }

    public function tapActivity(Activity $activity, string $eventName)
    {
        // Handle null or empty letter name
        $letterName = optional($this->letter)->letter_name ?? '';
        $activity->log_name = (new self)->getMorphClass();
        $activity->description = "$letterName letter template file attachment $eventName";
    }

    public function letter()
    {
        return $this->belongsTo(ReportLetterSetup::class, 'letter_id');
    }

    protected $fillable = [
        'college_id',
        'letter_id',
        'letter_attachment',
        'created_by',
        'updated_by',
    ];
}
