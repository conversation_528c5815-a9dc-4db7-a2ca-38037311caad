<?php

namespace App\Model\v2;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\Contracts\Activity;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class ReportLetterSetup extends Model
{
    use HasFactory;
    use LogsActivity;

    protected $table = 'rto_letter_setup';

    protected $fillable = ['id', 'college_id', 'report_id', 'letter_name', 'recepient', 'category', 'track_letter', 'track_color', 'input_parameter', 'letter_editer', 'created_by', 'updated_by'];

    protected $logAttributes = [
        'college_id',
        'report_id',
        'letter_name',
        'recepient',
        'category',
        'track_letter',
        'track_color',
        'input_parameter',
        'letter_editer',
    ];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logFillable()
            ->logOnlyDirty()
            ->setDescriptionForEvent(fn (string $eventName) => "Report letter setup has been {$eventName}");
    }

    public function tapActivity(Activity $activity, string $eventName)
    {
        $activity->log_name = (new self)->getMorphClass();
        $activity->description = "$this->letter_name letter template $eventName";
    }

    const RECIPIENT_STUDENT = 'Student';

    const RECIPIENT_AGENT = 'Agent';
}
