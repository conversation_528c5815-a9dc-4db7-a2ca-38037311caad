<?php

namespace App\Model\v2;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class Semester extends Model
{
    use HasFactory;

    protected $table = 'rto_semester';

    protected $fillable = ['college_id', 'course_type_id', 'semester_name', 'year', 'calendar_type', 'semester_start', 'semester_finish', 'last_term_date', 'created_by', 'updated_by'];

    public function batches()
    {
        return $this->hasMany(Timetable::class, 'semester_id', 'id');
    }

    public function divisions()
    {
        return $this->hasMany(SemesterDivision::class, 'semester_id', 'id');
    }

    public function courseType()
    {
        return $this->belongsTo(CourseType::class, 'course_type_id', 'id');
    }

    /**
     * Validation before delete process
     * Checks if semester has any timetable entries (batches) before allowing deletion
     */
    public function beforeDeleteProcess()
    {
        $collegeId = Auth::user()->college_id;

        // Check if there are any timetable entries for this semester
        $checkTerm = Timetable::where('college_id', '=', $collegeId)
            ->where('semester_id', '=', $this->id)
            ->groupBy('term')
            ->get(['term']);

        if (! empty($checkTerm[0])) {
            throw new \Exception('Batch is Created for this Semester First Delete it.');
        }
    }

    /**
     * Process after successful deletion
     * Deletes related semester divisions
     */
    public function afterDeleteProcess()
    {
        $collegeId = Auth::user()->college_id;

        // Delete related semester divisions directly using efficient bulk delete
        SemesterDivision::where('college_id', $collegeId)
            ->where('course_type_id', $this->course_type_id)
            ->where('semester_id', $this->id)
            ->whereBetween('week_start', [$this->semester_start, $this->semester_finish])
            ->delete();
    }

    /**
     * Static method for initializing filters (required by CrudBaseController)
     */
    public static function initFilters()
    {
        return static::query()->initFilters()->where('college_id', Auth::user()->college_id);
    }

    /**
     * Scope to filter by college
     */
    public function scopeInitFilters($query)
    {
        return $query->where('college_id', Auth::user()->college_id);
    }

    /**
     * Filter by course type ID
     */
    public function scopeFilterCourseTypeId($query, $value)
    {
        if (empty($value)) {
            return $query;
        }

        return $query->where('course_type_id', $value);
    }

    /**
     * Filter by year
     */
    public function scopeFilterYear($query, $value)
    {
        if (empty($value)) {
            return $query;
        }

        return $query->where('year', $value);
    }

    /**
     * Filter by calendar type
     */
    public function scopeFilterCalendarType($query, $value)
    {
        if (empty($value)) {
            return $query;
        }

        return $query->where('calendar_type', $value);
    }

    /**
     * Scope to filter by college ID
     */
    public function scopeCollegeId($query, $value)
    {
        if (empty($value)) {
            return $query;
        }

        return $query->where('college_id', $value);
    }

    /**
     * Scope to filter by search query (for search functionality)
     */
    public function scopeFilterQuery($query, $value)
    {
        if (empty($value)) {
            return $query;
        }

        $searchTerm = '%'.trim($value).'%';

        return $query->where(function ($q) use ($searchTerm) {
            $q->where('semester_name', 'like', $searchTerm)
                ->orWhere('year', 'like', $searchTerm)
                ->orWhere('calendar_type', 'like', $searchTerm)
                ->orWhereHas('courseType', function ($query) use ($searchTerm) {
                    $query->where('title', 'like', $searchTerm);
                });
        });
    }

    /**
     * Get semester data for dropdowns
     */
    public function _getSemesterData($courseTypeId, $calendarType, $startYear = '')
    {
        $sqlData = Semester::where('college_id', '=', Auth::user()->college_id)
            ->where('course_type_id', '=', $courseTypeId)
            ->whereIn('calendar_type', $calendarType)
            ->select('id', 'semester_name')
            ->groupBy('semester_name');

        if (! empty($startYear)) {
            $sqlData->where('year', '=', $startYear);
        }

        return $sqlData->get();
    }
}
