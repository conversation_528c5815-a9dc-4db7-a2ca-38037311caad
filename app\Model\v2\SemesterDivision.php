<?php

namespace App\Model\v2;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class SemesterDivision extends Model
{
    use HasFactory;

    protected $table = 'rto_semester_division';

    public function _getTermBySemester($semesterId, $collegeId)
    {
        return SemesterDivision::where('college_id', '=', $collegeId)
            ->where('semester_id', '=', $semesterId)
            ->groupBy('term')
            ->get(['term']);
    }
}
