<?php

namespace App\Model\v2;

use Auth;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\Contracts\Activity;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class SmsTemplate extends Model
{
    use LogsActivity;

    protected $table = 'rto_sms_template';

    protected $fillable = ['college_id', 'template_name', 'recipient', 'content', 'status', 'created_by', 'updated_by'];

    protected $logAttributes = [
        'college_id',
        'template_name',
        'recipient',
        'content',
        'status',
    ];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logFillable()
            ->logOnlyDirty()
            ->setDescriptionForEvent(fn (string $eventName) => "Sms template has been {$eventName}");
    }

    public function tapActivity(Activity $activity, string $eventName)
    {
        $activity->log_name = (new self)->getMorphClass();
        $activity->description = "$this->template_name sms template $eventName";

    }

    public function getSmsTemplateList()
    {
        $collegeId = Auth::user()->college_id;

        return SmsTemplate::select('id', 'template_name as name')
            ->where(['college_id' => $collegeId, 'status' => '1', 'recipient' => '1'])
            ->get()
            ->toArray();
    }

    public function getSmsTemplateContain($templeteId)
    {
        return SmsTemplate::find($templeteId)->content;
    }
}
