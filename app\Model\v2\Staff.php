<?php

namespace App\Model\v2;

use DB;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;

class Staff extends Model
{
    use HasFactory;

    const POSITION_TEACHER = 7;

    const POSITION_ADMIN = 2;

    protected $table = 'rto_staff_and_teacher';

    protected $fillable = [
        'college_id',
        'user_id',
        'name_title',
        'first_name',
        'last_name',
        'country',
        'address',
        'city_town',
        'state',
        'postcode',
        'phone',
        'mobile',
        'email',
        'personal_email',
        'birth_date',
        'gender',
        'atsi_code',
        'joining_date',
        'highest_qualification_code',
        'highest_qualification_place_code',
        'work_contract_code',
        'staff_work_level_code',
        'organisational_unit_code',
        'work_sector_code',
        'function_code',
        'signatory_text',
        'staff_number',
        'position',
        'staff_type',
        'is_active',
        'updated_by',
        'created_by',
    ];

    public function getStaffEmailContent($staffId)
    {
        $sql = Staff::from('rto_staff_and_teacher as rst')
            ->leftjoin('rto_country as country1', 'country1.id', '=', 'rst.country')
            ->leftjoin('rto_colleges as clg', 'rst.college_id', '=', 'clg.id')
            ->leftjoin('rto_college_details as rcd', 'rcd.college_id', '=', 'clg.id')
            ->where('rst.id', '=', $staffId);

        return $result = $sql->select(
            DB::raw('concat(rst.name_title, rst.first_name, " ", rst.last_name) as staff_name'), 'rst.first_name', 'rst.last_name', 'rst.email as staff_email',
            'rst.phone', 'rst.mobile', 'rst.postcode', 'rst.state', 'rst.city_town', 'rst.address', 'rst.name_title', 'rcd.fax', 'country1.nationality', 'country1.name as country_name',
            'clg.contact_email as college_email', 'clg.college_name as entity_name', 'clg.college_signature', 'clg.dean_name', 'clg.dean_signature', 'clg.RTO_code', 'clg.CRICOS_code', 'clg.legal_name', 'clg.contact_person', 'clg.contact_phone', 'clg.college_url', 'clg.college_logo', 'clg.timezone as college_timezone',
            'rcd.ABN as college_ABN', 'rst.first_name as teacher_first_name', 'rst.last_name as teacher_last_name', 'rst.email as teacher_email', 'rst.mobile as teacher_mobile')
            ->get()->toarray();
    }

    public function getTeacherList($collegeId)
    {
        $teacherList = Staff::where('college_id', $collegeId)
            ->where('staff_type', 'Staff-Teacher')
            ->where('is_active', '1')
            ->selectRaw("id, CONCAT(COALESCE(name_title, ''), ' ', COALESCE(first_name, ''), ' ', COALESCE(last_name, '')) as full_name")
            ->pluck('full_name', 'id')
            ->toArray();

        return $teacherList ?: ['' => '- - No Record Found - -'];
    }

    public static function GetLoggedInTeacherId($user)
    {
        return self::where('user_id', '=', $user->id ?? null)->value('id');
    }

    public function getSupervisorList($collegeId)
    {
        // 8 for Supervisor
        $supervisorList = Staff::where('college_id', $collegeId)
            // ->where('position', '8')
            ->selectRaw("id, CONCAT(COALESCE(name_title, ''), ' ', COALESCE(first_name, ''), ' ', COALESCE(last_name, '')) as full_name")
            ->pluck('full_name', 'id')
            ->toArray();

        return $supervisorList ?: ['' => '- - No Supervisor Found - -'];
    }

    public function getStaffList($collegeId)
    {
        // Get staff list with concatenated names using database-level concatenation
        $staffList = Staff::from('rto_staff_and_teacher as rst')
            ->leftjoin('rto_user_role_type as rurt', 'rurt.staff_id', '=', 'rst.id')
            ->where('rst.college_id', $collegeId)
            ->where('rurt.role_type', '2')      // 2 for Admin
            ->selectRaw("rst.id, CONCAT(COALESCE(rst.name_title, ''), ' ', COALESCE(rst.first_name, ''), ' ', COALESCE(rst.last_name, '')) as full_name")
            ->pluck('full_name', 'rst.id')
            ->toArray();

        // Add default option and return combined array
        return ['0' => '- - Select Account Manager - -'] + $staffList;
    }
}
