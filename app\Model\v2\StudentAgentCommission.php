<?php

namespace App\Model\v2;

use Domains\Xero\Traits\HasXeroData;
use Domains\Xero\Traits\XeroableInvoice;
use Domains\Xero\Traits\XeroablePurchaseOrder;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\Contracts\Activity;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;
use Support\Traits\HasJsonFields;

class StudentAgentCommission extends Model
{
    use HasFactory;
    use HasJsonFields;
    use HasXeroData;
    use LogsActivity;
    use XeroableInvoice;
    use XeroablePurchaseOrder;

    protected $table = 'rto_student_agent_commission';

    const STATUS_APPROVED = 1;

    const STATUS_NOT_APPROVED = 0;

    protected $fillable = [
        'college_id',
        'student_id',
        'course_id',
        'student_course_id',
        'transaction_no',
        'invoice_no',
        'xero_invoice_id',
        'xero_payment_id',
        'agent_id',
        'commission_payable',
        'GST',
        'gst_amount',
        'commission_paid',
        'mode',
        'CHQ_NO',
        'paid_date',
        'due_date',
        'comm_to_refund',
        'GST_to_refund',
        'refund_amount',
        'remarks',
        'is_commission_deducted',
        'bonus_type',
        'bonus_amount',
        'bonus_gst',
        'is_reversed',
        'is_approved',
        'is_process',
        'miscellaneous_payment_id',
        'is_agent_rebate',
        'rebate_amount',
        'rebate_date',
        'created_by',
        'updated_by',
    ];

    protected $casts = [
        'xero_data' => 'json',
    ];

    protected static $logAttributes = [
        'course_id',
        'student_course_id',
        'transaction_no',
        'invoice_no',
        'xero_invoice_id',
        'xero_payment_id',
        'agent_id',
        'commission_payable',
        'GST',
        'gst_amount',
        'commission_paid',
        'mode',
        'CHQ_NO',
        'paid_date',
        'comm_to_refund',
        'GST_to_refund',
        'refund_amount',
        'remarks',
        'is_commission_deducted',
        'bonus_type',
        'bonus_amount',
        'bonus_gst',
        'is_reversed',
        'is_approved',
        'is_process',
        'miscellaneous_payment_id',
        'is_agent_rebate',
        'rebate_amount',
        'rebate_date',
    ];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logFillable()
            ->logOnlyDirty()
            ->setDescriptionForEvent(fn (string $eventName) => "Agent Commission has been {$eventName}");
        // Chain fluent methods for configuration options
    }

    /* This method is fired before saving the log to db. */
    public function tapActivity(Activity $activity, string $eventName)
    {
        $activity->log_name = $this->xeroLogKey();
        if (! empty($eventName)) {
            $identifier = $this->formatted_invoice_number;
            if ($eventName == 'deleted') {
                $activity->description = "Agent Commission $identifier has been $eventName";
                if (request('reason')) {
                    $activity->description .= ' <br><strong>REASON:</strong> '.request('reason');
                }
            } elseif ($eventName == 'updated') {
                if (isset($activity->properties['attributes']['is_approved']) && $activity->properties['attributes']['is_approved'] == '1') {
                    $activity->description = "Agent Commission $identifier has been Approved.";
                    $this->handleCustomProperties($activity);
                } elseif (isset($activity->properties['attributes']['commission_paid'])) {
                    $activity->description = "Agent Commission $identifier has been Paid";
                    /*if ($this->commission_payable >= $activity->properties['attributes']['commission_paid']){
                        $activity->description = "Agent Commission $identifier has been Paid";
                    } elseif ($activity->properties['attributes']['commission_paid'] > 0){
                        $activity->description = "Agent Commission $identifier has been Partially Paid";
                    }*/
                }
            }
        }
    }

    protected function handleCustomProperties(Activity $activity)
    {
        /*$activity->properties = $activity->properties
            ->put('attributes', ["is_approved" => "Approved"])
            ->put('old', ["is_approved" => "Not Approve"]);*/

        $newRemarks = $activity->properties['attributes']['remarks'] ?? null;
        $oldRemarks = $activity->properties['old']['remarks'] ?? 'NA';
        $attributes = ['is_approved' => 'Approved'];
        if (! is_null($newRemarks)) {
            $attributes['remarks'] = $newRemarks;
        }

        $old = ['is_approved' => 'Not Approve', 'remarks' => $oldRemarks];

        $activity->properties = $activity->properties
            ->put('attributes', $attributes)
            ->put('old', $old);
    }

    public function getDescriptionForEvent(string $eventName): string
    {
        return "Agent Commission has been {$eventName}";
    }

    // public function getXeroId()
    // {
    //     return $this->xero_invoice_id;
    // }

    public function course()
    {
        return $this->belongsTo(Courses::class, 'course_id');
    }

    public function student()
    {
        return $this->belongsTo(Student::class, 'student_id');
    }

    public function agent()
    {
        return $this->belongsTo(Agent::class, 'agent_id');
    }

    public function paymentSyncedWithXero($xeroPaymentID)
    {
        $this->xero_payment_id = $xeroPaymentID;
        $this->updateJsonField([
            'xero_synced_at' => now(),
        ], $this->xeroDataField());

        $this->save();

        return $this;
    }

    public function scopePaymentNotSyncedWithXero($q)
    {
        return $q->whereNull('xero_payment_id');
    }

    public function xeroDataField()
    {
        return 'xero_data';
    }

    public function xeroLogKey()
    {
        return (new self)->getMorphClass().'_'.$this->student_id.'_'.$this->course_id.'_'.$this->agent_id;
    }

    protected $appends = ['formatted_invoice_number'];

    public function getFormattedInvoiceNumberAttribute()
    {
        // return 'GAL-AGN-' . $this->invoice_no . '-' . $this->id;
        $keys = [
            'GAL',
            ! empty($this->student->generated_stud_id) ? $this->student->generated_stud_id : null,
            ! empty($this->course->course_code) ? $this->course->course_code : null,
            ! empty($this->agent_id) ? 'AG'.$this->agent_id : null,
            $this->invoice_no,
            $this->id,
        ];
        $keys = array_filter($keys, fn ($value) => ! is_null($value) && $value !== '');

        return implode('-', $keys);
    }

    public function transaction()
    {
        return $this->hasOne(StudentInitialPaymentTransaction::class, 'transection_no', 'transaction_no');
    }

    public function scopeFilterAgentId($query, $value)
    {
        if (empty($value)) {
            return $query;
        }

        return $query->where('agent_id', $value);
    }
}
