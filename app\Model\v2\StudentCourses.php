<?php

namespace App\Model\v2;

use App\Classes\SiteConstants;
use App\Helpers\Helpers;
use App\Traits\CommonTrait;
use Carbon\Carbon;
use Domains\Customers\Billing\Traits\HasGalaxyStudentInvoices;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;
use Support\Traits\CreaterUpdaterTrait;

class StudentCourses extends Model
{
    use CommonTrait;
    use CreaterUpdaterTrait;
    use HasFactory;
    use HasGalaxyStudentInvoices;
    use LogsActivity;

    protected $table = 'rto_student_courses';

    const STATUS_DEFERRED = 'Deferred';

    const STATUS_CANCELLED = 'Cancelled';

    const STATUS_WITHDRAWN = 'Withdrawn';

    const STATUS_SUSPENDED = 'Suspended';

    const STATUS_CURRENT = 'Current Student';

    const STATUS_COMPLETED = 'Completed';

    const STATUS_ENROLLED = 'Enrolled';

    const STATUS_FINISHED = 'Finished';

    const STATUS_CURRENT_STUDENT = 'Current Student';

    protected $fillable = [
        'student_id',
        'agent_id',
        'campus_id',
        'palacement_manager_id',
        'course_manager_id',
        'res_cal_method',
        'course_type_id',
        'intake_id',
        'intake_year',
        'intake_date',
        'issued_date',
        'course_id',
        'start_date',
        'finish_date',
        'total_weeks',
        'course_duration_type',
        'enroll_fee',
        'study_reason_id',
        'course_fee',
        'course_upfront_fee',
        'course_material_fee',
        'application_request',
        'advance_standing_credit',
        'credit_transfer_request',
        'special_instructions',
        'employer_id',
        'contract_schedule_id',
        'purchasing_contract_ode',
        'schedule_code',
        'auto_update_to_sru',
        'census_date1',
        'census_date2',
        'census_date3',
        'mode_of_delivery',
        'is_material_fee_inc_initail_payment',
        'internal',
        'external',
        'workplace_based_delivery',
        'status',
        'offer_status',
        'orientation_comment',
        'offer_id',
        'coe_applicable',
        'coe_name',
        'coe_image',
        'coe_material_id',
        'course_template',
        'default_unit_fee',
        'is_finish_dt',
        'is_orientation',
        'is_orientation_sms_send',
        'is_orientation_email_send',
        'is_claim',
        'is_qualification',
        'is_certificate',
        'group_id',
        'course_attempt',
        'date_approved',
        'survey_contact_status',
        'purchasing_contract_identifier',
        'purchasing_contract_schedule_identifier',
        'associated_course_identifier',
        'is_fulltimelearing',
        'enrolled_host_url',
        'subject_clming_for_credit',
        'is_receiving_any_scholarship',
        'scholarship_percentage',
        'wil_requirements',
        'third_party_providers',
        'created_by',
        'updated_by',
    ];

    protected $logAttributes = [
        'student_id',
        'agent_id',
        'campus_id',
        'palacement_manager_id',
        'course_manager_id',
        'res_cal_method',
        'course_type_id',
        'intake_year',
        'intake_date',
        'issued_date',
        'course_id',
        'start_date',
        'finish_date',
        'total_weeks',
        'enroll_fee',
        'study_reason_id',
        'course_fee',
        'course_upfront_fee',
        'course_material_fee',
        'application_request',
        'advance_standing_credit',
        'credit_transfer_request',
        'special_instructions',
        'employer_id',
        'contract_schedule_id',
        'purchasing_contract_ode',
        'schedule_code',
        'auto_update_to_sru',
        'census_date1',
        'census_date2',
        'census_date3',
        'mode_of_delivery',
        'is_material_fee_inc_initail_payment',
        'internal',
        'external',
        'workplace_based_delivery',
        'status',
        'offer_status',
        'orientation_comment',
        'offer_id',
        'coe_applicable',
        'coe_name',
        'coe_image',
        'coe_material_id',
        'course_template',
        'default_unit_fee',
        'is_finish_dt',
        'is_orientation',
        'is_orientation_sms_send',
        'is_orientation_email_send',
        'is_claim',
        'is_qualification',
        'is_certificate',
        'group_id',
        'course_attempt',
        'date_approved',
        'survey_contact_status',
        'purchasing_contract_identifier',
        'purchasing_contract_schedule_identifier',
        'associated_course_identifier',
        'is_fulltimelearing',
    ];

    public function getGalaxyLogNameAttribute(): string
    {
        return implode('-', [@$this->student->generated_stud_id, @$this->course->course_code]);
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logFillable()
            ->logOnlyDirty()
            ->setDescriptionForEvent(fn (string $eventName) => ":subject.galaxy_log_name Student Course has been {$eventName}");
    }

    public function student()
    {
        return $this->hasOne(Student::class, 'id', 'student_id');
    }

    public function course()
    {
        return $this->hasOne(Courses::class, 'id', 'course_id');
    }

    public function intake()
    {
        return $this->hasOne(CoursesIntakeDate::class, 'id', 'intake_id');
    }

    public function campus()
    {
        return $this->hasOne(CollegeCampus::class, 'id', 'campus_id');
    }

    public function newmessages()
    {
        $newRecordThreshold = SiteConstants::COMMUNICATION_RECORD_NEW_STATUS_FOR;
        $createdDate = Carbon::now()->subDays($newRecordThreshold);

        return $this->hasMany(StudentCommunicationLog::class, 'student_course_id', 'id')
            ->whereColumn('created_at', '=', 'updated_at')
            ->where('created_at', '>', $createdDate);
    }

    public function _checkCourseExist($courseId)
    {
        return StudentCourses::where('course_id', '=', $courseId)->count();
    }

    public function agent()
    {
        return $this->hasOne(Agent::class, 'id', 'agent_id');
    }

    public function getTotalPayableFee($type = null)
    {
        if ($this->course_type_id == SiteConstants::SHORT_COURSE_TYPE_ID) {
            $fee = $this->course_fee;
        } else {

            $fee = ($type == 'upfront') ? $this->course_upfront_fee : $this->course_fee;
            $fee += ($this->enroll_fee + $this->course_material_fee);
        }

        return $fee;
    }

    public function getStudentAppliedCourseName($studentId)
    {

        $arrStudentCourse = StudentCourses::join('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->where('rto_student_courses.student_id', '=', $studentId)
            // ->pluck('rto_courses.course_name', 'rto_courses.id')
            ->get(['rto_courses.course_name', 'rto_courses.id', 'rto_courses.course_code'])->toArray();

        $nullRecord[''] = 'No Course Found';
        $arrStudentCourseCode = [];
        for ($i = 0; $i < count($arrStudentCourse); $i++) {
            $arrStudentCourseCode[$arrStudentCourse[$i]['id']] = $arrStudentCourse[$i]['course_code'].' : '.$arrStudentCourse[$i]['course_name'];
        }

        return ! empty($arrStudentCourseCode) ? $arrStudentCourseCode : $nullRecord;
    }

    public static function getStudentAppliedCourseNameWithStudentCourseId($studentId)
    {
        $courses = self::select([
            'rto_student_courses.id as student_course_id',
            'rto_courses.course_name',
            'rto_courses.id as course_id',
            'rto_courses.course_code',
            'rto_student_courses.status',
        ])
            ->join('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->where('rto_student_courses.student_id', $studentId)
            ->get()
            ->mapWithKeys(function ($course) {
                return [
                    $course->student_course_id => sprintf(
                        '%s : %s (%s)',
                        $course->course_code,
                        $course->course_name,
                        $course->status
                    ),
                ];
            })
            ->toArray();

        return $courses ?: ['' => 'No Course Found'];
    }

    public function getStudentCourseInfo($collegeId, $studCourseId)
    {
        return StudentCourses::from('rto_student_courses as rsc')
            ->leftjoin('rto_students as rs', 'rs.id', '=', 'rsc.student_id')
            ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->leftjoin('rto_colleges', 'rto_colleges.id', '=', 'rs.college_id')
            ->where('rs.college_id', '=', $collegeId)
            ->where('rsc.id', '=', $studCourseId)
            ->get([
                'rs.generated_stud_id as generated_id',
                'rs.name_title',
                'rs.first_name',
                'rs.middel_name',
                'rs.family_name',
                'rs.DOB',
                'rc.course_code',
                'rc.course_name',
                'rc.cricos_code',
                'rc.national_code',
                'rsc.start_date',
                'rsc.finish_date',
                'rsc.status',
                'rto_colleges.college_name',
                'rs.id as studentId',
                'rc.id as courseId',
            ]);
    }

    public function getResult($post = [], $countOnly = false)
    {

        // $operations = Helpers::getOperators();
        $operations = $this->getOperators();
        $filterParts = (isset($post['filter']) && isset($post['filter']['filters'])) ? $post['filter'] : [];
        $sorts = (isset($post['sort'])) ? $post['sort'] : [];
        $customFilterParts = (isset($post['filter']) && isset($post['filter']['filters'])) ? $post['filter']['filters'] : [];

        $columnArr = [
            'rsc.id',
            'rs.generated_stud_id',
            DB::raw("CONCAT(COALESCE(rs.first_name, ''), ' ', COALESCE(rs.family_name, '')) as student_name"),
            DB::raw("CONCAT(rc.course_code,' : ',rc.course_name) as course"),
            'rsc.status',
            'rsc.start_date as course_start_date',
            DB::raw("(CASE WHEN rsc.is_orientation='1' THEN 'Yes' ELSE 'No' END) as orientation"),
            'rs.profile_picture',
            'rs.id as student_id',
            'rs.current_mobile_phone as mobile',
            'rsc.is_orientation_sms_send',
            'rsc.is_orientation_email_send',
        ];

        $columns = [
            'student_name' => "CONCAT(COALESCE(rs.first_name, ''), ' ', COALESCE(rs.family_name, '')) as student_name",
            'course' => "CONCAT(rc.course_code,' : ',rc.course_name) as course",
            'status' => 'rsc.status',
            'generated_stud_id' => 'rs.generated_stud_id',
            'course_start_date' => 'rsc.start_date as course_start_date',
            'orientation' => "(CASE WHEN rsc.is_orientation='1' THEN 'Yes' ELSE 'No' END) as orientation",
        ];

        $query = StudentCourses::from('rto_student_courses as rsc')
            ->join('rto_students as rs', 'rs.id', '=', 'rsc.student_id')
            ->join('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->whereIn('rsc.offer_status', ['Enrolled'])
            ->where(function ($query) {
                $query->where('rsc.status', 'Enrolled')
                    ->orWhere('rsc.is_orientation', '=', 0);
            })
            // ->whereIn('rsc.status', ['Enrolled', 'Current Student'])
            ->select($columnArr);

        $this->gridDataFilter($query, $post, $columns);

        // apply sidebar filter
        foreach ($customFilterParts as $filter) {
            if ($filter['field'] == 'extra' && isset($filter['value'])) {
                foreach ($filter['value'] as $fieldName => $fieldvalue) {
                    if (count($fieldvalue) > 0) {
                        if ($fieldName == 'campus') {
                            $query->whereIn('rsc.campus_id', $fieldvalue);
                        } elseif ($fieldName == 'generated_stud_id') {
                            $query->whereIn('rs.generated_stud_id', $fieldvalue);
                        } elseif ($fieldName == 'date') {
                            $query->whereIn('rsc.start_date', $fieldvalue);
                        } elseif ($fieldName == 'type') {
                            $query->whereIn('rc.course_type_id', $fieldvalue);
                        } elseif ($fieldName == 'course') {
                            $query->whereIn('rc.id', $fieldvalue);
                        } elseif ($fieldName == 'year') {
                            $query->whereIn('rsc.intake_year', $fieldvalue);
                        } elseif ($fieldName == 'invitation') {
                            if (count($fieldvalue) == 1) {
                                if (in_array('send', $fieldvalue)) {
                                    $query->where(function ($subChildQuery) {
                                        $subChildQuery->where('rsc.is_orientation_email_send', '1');
                                        $subChildQuery->orWhere('rsc.is_orientation_sms_send', '1');
                                    });
                                } else {
                                    $query->where('rsc.is_orientation_email_send', '0');
                                    $query->where('rsc.is_orientation_sms_send', '0');
                                }
                            }
                        } elseif ($fieldName == 'orientation') {
                            $query->whereIn('rsc.is_orientation', $fieldvalue);
                        }
                    }
                }
            }
        }

        // handle the sorting request
        foreach ($sorts as $sort) {
            $query->orderBy($sort['field'], $sort['dir']);
        }

        if ($countOnly) {
            $result = $query->get()->count();
        } else {
            if (isset($post['take'])) {
                $query->skip($post['skip'])->take($post['take']);
            }
            $result = $query->get()->toArray();
        }

        return $result;
    }

    public function getSidebarResult($categoryId)
    {

        $query = StudentCourses::from('rto_student_courses as rsc')
            ->join('rto_students as rs', 'rs.id', '=', 'rsc.student_id')
            ->join('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->whereIn('rsc.offer_status', ['Enrolled'])
            ->where(function ($query) {
                $query->where('rsc.status', 'Enrolled')
                    ->orWhere('rsc.is_orientation', '=', 0);
            });

        if ($categoryId == 1) {
            $total = $query->select('rsc.id')->get()->count();

            return [
                ['id' => 1, 'category_id' => $categoryId, 'type' => 'radio', 'hasChild' => false, 'field' => 'student_by', 'value' => '1', 'original' => 'Course Start Date', 'subtext' => 'Course Start Date ('.$total.')'],
            ];
        }

        if ($categoryId == 2) {
            // DB::raw('DATE_FORMAT("course_start_date", "%d %M, %Y")')
            $query->select(['rsc.id', DB::raw('count(rsc.id) as total'), 'rsc.start_date as course_start_date'])
                ->orderBy('rsc.start_date', 'DESC')
                ->groupBy('rsc.start_date');

            $resultArr = $query->get();
            $result = [];
            foreach ($resultArr as $row) {
                $result[] = [
                    'id' => $row->id,
                    'category_id' => $categoryId,
                    'type' => 'checkbox',
                    'hasChild' => false,
                    'field' => 'date',
                    'value' => $row->course_start_date,
                    'original' => date('d M Y', strtotime($row->course_start_date)),
                    'subtext' => date('d M Y', strtotime($row->course_start_date))." ($row->total)",
                    'isDefaultShow' => (strtotime($row->course_start_date) >= strtotime(date('Y-m-d'))),
                ];
            }

            return $result;
        }

        if ($categoryId == 3) {
            $query->join('rto_college_course_type as rcct', 'rcct.id', '=', 'rc.course_type_id', 'right')
                ->select(['rcct.id', DB::raw('count(rsc.id) as total'), 'rcct.title'])
                ->orderBy('rcct.title', 'ASC')
                ->groupBy('rcct.id');

            $resultArr = $query->get();
            $result = [];
            foreach ($resultArr as $row) {
                $result[] = [
                    'id' => $row->id,
                    'category_id' => $categoryId,
                    'type' => 'radio',
                    'hasChild' => false,
                    'field' => 'type',
                    'value' => $row->id,
                    'original' => $row->title,
                    'subtext' => $row->title." ($row->total)",
                ];
            }

            return $result;
        }

        if ($categoryId == 4) {
            $query->select(['rc.id', DB::raw('count(rc.id) as total'), 'rc.course_code', 'rc.course_name'])
                ->orderBy('total', 'desc')
                ->groupBy('rc.course_code');

            $resultArr = $query->get();
            $result = [];
            foreach ($resultArr as $row) {
                $fullText = $row->course_code.': '.$row->course_name;
                $subText = (strlen($fullText) > 23) ? (substr($fullText, 0, 23).'...') : $fullText;
                $result[] = [
                    'id' => $row->id,
                    'category_id' => $categoryId,
                    'type' => 'checkbox',
                    'hasChild' => false,
                    'field' => 'course',
                    'value' => $row->id,
                    'original' => $fullText,
                    'subtext' => $subText." ($row->total)",
                    'isDefaultShow' => true,
                ];
            }

            return $result;
        }

        if ($categoryId == 5) {
            $query->select(['rsc.id', DB::raw('count(rsc.id) as total'), 'rsc.intake_year'])
                ->orderBy('rsc.intake_year', 'DESC')
                ->groupBy('rsc.intake_year');

            $resultArr = $query->get();
            $result = [];
            foreach ($resultArr as $row) {
                $result[] = [
                    'id' => $row->id,
                    'category_id' => $categoryId,
                    'type' => 'checkbox',
                    'hasChild' => false,
                    'field' => 'year',
                    'value' => $row->intake_year,
                    'original' => $row->intake_year,
                    'subtext' => $row->intake_year." ($row->total)",
                    'isDefaultShow' => true,
                ];
            }

            return $result;
        }

        if ($categoryId == 6) {
            $query->select([
                DB::raw('count(CASE WHEN rsc.is_orientation_email_send="1" OR rsc.is_orientation_sms_send="1" THEN 1 END) as send'),
                DB::raw('count(CASE WHEN rsc.is_orientation_email_send="0" AND rsc.is_orientation_sms_send="0" THEN 1 END) as not_send'),
            ]);
            $resultArr = $query->first();

            return [
                ['id' => 1, 'category_id' => $categoryId, 'type' => 'checkbox', 'hasChild' => false, 'field' => 'invitation', 'value' => 'send',     'original' => 'Invitation sent',     'subtext' => 'Invitation sent ('.$resultArr->send.')',         'isDefaultShow' => true],
                ['id' => 2, 'category_id' => $categoryId, 'type' => 'checkbox', 'hasChild' => false, 'field' => 'invitation', 'value' => 'not_send', 'original' => 'Invitation not sent', 'subtext' => 'Invitation not sent ('.$resultArr->not_send.')', 'isDefaultShow' => true],
            ];
        }
        if ($categoryId == 7) {
            return [
                ['id' => 1, 'is_orientation' => 1, 'type' => 'radio', 'hasChild' => false, 'field' => 'orientation', 'value' => '1', 'original' => 'Orientation Yes', 'subtext' => 'Yes'],
                ['id' => 2, 'is_orientation' => 0, 'type' => 'radio', 'hasChild' => false, 'field' => 'orientation', 'value' => '0', 'original' => 'Orientation No', 'subtext' => 'No'],
            ];
        }
    }

    public static function getStudentCoursesEmailContent($studentId, $courseId)
    {
        $sql = StudentCourses::from('rto_student_courses as rsc')
            ->leftjoin('rto_campus as campus', 'campus.id', '=', 'rsc.campus_id')
            ->leftjoin('rto_student_details as rsd', 'rsd.student_id', '=', 'rsc.student_id')
            ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->leftjoin('rto_course_template as rct', 'rct.id', '=', 'rsc.course_template')
            ->leftjoin('rto_students as rs', 'rs.id', '=', 'rsc.student_id')
            ->leftjoin('rto_country as country1', 'country1.id', '=', 'rs.birth_country')
            ->leftjoin('rto_country as country2', 'country2.id', '=', 'rs.nationality')
            ->leftjoin('rto_country as country3', 'country3.id', '=', 'rs.current_country')
            ->leftjoin('rto_colleges as clg', 'rs.college_id', '=', 'clg.id')
            ->leftjoin('rto_college_details as rcd', 'rcd.college_id', '=', 'clg.id')
            ->leftjoin('rto_college_course_type as rcct', 'rcct.id', '=', 'rsc.course_type_id')
            ->leftjoin('rto_staff_and_teacher as rst', 'rst.id', '=', 'rsd.account_manager_id')
            ->leftjoin('rto_agents as agent', 'agent.id', '=', 'rsc.agent_id')
            ->leftjoin('rto_setup_section as rss', 'rss.id', '=', 'rs.visa_status')
            ->where('rsc.student_id', '=', $studentId);
        if ($courseId != '') {
            $sql->where('rsc.course_id', '=', $courseId);
        }

        return $sql->select(
            'rc.results_calculation_methods',
            'rc.cricos_code',
            'rc.course_code',
            'rc.course_name',
            'campus.name as campus_name',
            'rct.template_name as courseTemplate',
            DB::raw('concat(rs.name_title, rs.first_name, " ", rs.family_name) as student_name'),
            DB::raw('concat(rst.name_title, rst.first_name, " ", rst.last_name) as account_manager'),
            'rs.first_name',
            'rs.middel_name',
            'rs.family_name',
            'rs.nickname',
            'rs.current_postcode',
            'rs.current_state',
            'rs.current_country',
            'rs.email as student_email',
            'rs.DOB as birth_date',
            'rs.visa_expiry_date',
            'rss.value as visa_type',
            'rs.generated_stud_id',
            'rs.current_mobile_phone',
            'rs.current_postcode',
            'rs.current_state',
            'rs.current_street_name',
            'rs.current_city',
            'rs.name_title',
            'rs.gender',
            'rs.passport_no',
            'rs.visa_status',
            'rs.visa_expiry_date',
            'rcd.fax',
            'country1.name as birth_country',
            'country2.nationality',
            'country3.name as country_name',
            'rsd.emergency_email',
            'clg.contact_email as college_email',
            'clg.college_name as entity_name',
            'rcct.title as course_type',
            'agent.id as agent_id',
            'agent.agency_name as agent_name',
            'rsc.*',
            'clg.RTO_code',
            'clg.CRICOS_code',
            'clg.legal_name',
            'clg.contact_person',
            'clg.contact_phone',
            'clg.college_url',
            'clg.college_logo',
            'clg.college_signature',
            'clg.dean_name',
            'clg.dean_signature',
            'clg.timezone as college_timezone',
            'rcd.ABN as college_ABN',
            'rs.student_type',
            'rst.first_name as teacher_first_name',
            'rst.last_name as teacher_last_name',
            'rst.email as teacher_email',
            'rst.mobile as teacher_mobile',
            'agent.agency_name',
            'agent.contact_person as agent_name',
            'agent.primary_email as agent_email',
            'agent.telephone as agent_telephone',
            'rs.current_street_no',
            'rs.current_unit_detail',
            'rs.current_building_name'
        )
            ->get()
            ->toarray();
    }

    public static function getArrayStudentEnrolledCourseName($studentId)
    {

        return StudentCourses::join('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->where('rto_student_courses.student_id', '=', $studentId)
            ->where('rto_student_courses.offer_status', '=', 'Enrolled')
            ->get(['rto_courses.course_name', 'rto_courses.id', 'rto_courses.course_code', 'rto_student_courses.start_date', 'rto_student_courses.finish_date'])->toArray();
    }

    public static function getArrayStudentOfferedCourseName($studentId)
    {

        return StudentCourses::join('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->where('rto_student_courses.student_id', '=', $studentId)
            ->where('rto_student_courses.offer_status', '!=', 'Enrolled')
            ->get(['rto_courses.course_name', 'rto_courses.id', 'rto_courses.course_code', 'rto_student_courses.start_date', 'rto_student_courses.finish_date'])->toArray();
    }

    // Helper Function
    public function getOperators()
    {
        return [
            'eq' => '=',
            'gt' => '>',
            'gte' => '>=',
            'lt' => '<',
            'lte' => '<=',
            'neq' => '!=',
            'startswith' => 'like',
            'contains' => 'like',
            'doesnotcontain' => 'not like',
            'endswith' => 'like',
            'isnull' => '=',
            'isnotnull' => '!=',
            'isempty' => '=',
            'isnotempty' => '!=',
        ];
    }

    public function convertFilterValue($filterOperator, $filterValue)
    {
        // Do some value manupulations for some kendo operators
        switch ($filterOperator) {
            case 'startswith':
                $value = $filterValue.'%';
                break;
            case 'endswith':
                $value = '%'.$filterValue;
                break;
            case 'contains':
            case 'doesnotcontain':
                $value = '%'.$filterValue.'%';
                break;
            case 'isnull':
            case 'isnotnull':
                $value = null;
                break;
            case 'isempty':
            case 'isnotempty':
                $value = '';
                break;
            default:
                $value = $filterValue;
                break;
        }

        return $value;
    }

    public function getStudentName()
    {

        return StudentCourses::from('rto_student_courses as rsc')
            ->join('rto_students as rs', 'rs.id', '=', 'rsc.student_id')
            ->whereIn('rsc.status', ['Enrolled', 'Current Student'])
            ->select(DB::raw("CONCAT(COALESCE(rs.first_name, ''), ' ', COALESCE(rs.family_name, '')) as student_name"));

    }

    public function getStudentId()
    {

        return StudentCourses::from('rto_student_courses as rsc')
            ->join('rto_students as rs', 'rs.id', '=', 'rsc.student_id')
            ->whereIn('rsc.status', ['Enrolled', 'Current Student'])
            ->select('generated_stud_id');

    }

    public function getCourseName()
    {

        return StudentCourses::from('rto_student_courses as rsc')
            ->join('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->whereIn('rsc.status', ['Enrolled', 'Current Student'])
            ->select(DB::raw("CONCAT(rc.course_code,' : ',rc.course_name) as course"))
            ->groupBy('rc.id');

    }

    public function getCourseCampusAndDateRange($studentId, $courseId)
    {
        $result = [];
        $result = StudentCourses::from('rto_student_courses as rsc')
            ->leftjoin('rto_campus as rc', 'rc.id', '=', 'rsc.campus_id')
            ->where('rsc.course_id', '=', $courseId)
            ->where('rsc.student_id', '=', $studentId)
            ->select('rsc.start_date', 'rsc.finish_date', 'rc.name')
            ->get();
        if (! empty($result)) {
            $result[0]->start_date = date('d-m-Y', strtotime($result[0]->start_date));
            $result[0]->finish_date = date('d-m-Y', strtotime($result[0]->finish_date));
            $result[0]->duration = date('d/m/Y', strtotime($result[0]->start_date)).' - '.date('d/m/Y', strtotime($result[0]->finish_date));

            return $result[0]->toArray();
        }

        return $result;
    }

    public function completeStudentCourseInfo($studCourseId)
    {
        $objStudentCourse = StudentCourses::find($studCourseId);
        $objStudentCourse->status = 'Completed';
        $objStudentCourse->updated_by = Auth::user()->id;
        $objStudentCourse->save();

        return true;
    }

    /**
     * Get the indexable data array for the model.
     *
     * @return array<string, mixed>
     */
    public function toSearchableArray($context = null): array
    {
        $this->loadMissing(['course']);

        /* Limit indexing data for student list */
        return [
            // 'intake_year' => $this->intake_year,
            'campus_id' => $this->campus_id,
            'status' => $this->status,
            'offer_status' => $this->offer_status,
            'course' => $this->course ? $this->course->toSearchableArray($context) : [],
            'intake_year' => (int) ($this->intake_date ? date('Y', strtotime($this->intake_date)) : 0),
            'intake_date' => $this->intake_date,
            'id_with_status' => $this->course_id.'_'.$this->status,
            'id_with_intake_date' => $this->course_id.'_'.$this->intake_date,
            'id_with_status_with_intake_date' => $this->course_id.'_'.$this->status.'_'.$this->intake_date,
            'status_with_intake_date' => $this->status.'_'.$this->intake_date,
        ];
    }

    public static function checkCourseIsHigherEd($studentCourseId)
    {
        $courseData = StudentCourses::where('id', $studentCourseId)->get(['course_type_id'])->first()->toArray();
        $course_type_id = $courseData['course_type_id'];

        return (new CourseType)->checkHigherEdGradingType($course_type_id);
    }

    public function scopeWithStudentDetails($query, $studentId)
    {
        $sqlDateFormat = Helpers::toMysqlDateFormat();

        return $query->from('rto_student_courses as t1')
            ->leftJoin('rto_courses as t2', 't2.id', '=', 't1.course_id')
            ->leftJoin('rto_campus as t3', 't3.id', '=', 't1.campus_id')
            ->where('t1.student_id', $studentId)
            ->select(
                't1.id', 't2.college_id', 't1.student_id', 't1.course_id', 't2.course_code', 't2.course_name', 't1.offer_status', 't1.status', 't1.offer_id', 't3.name as campus',
                DB::raw("DATE_FORMAT(t1.start_date, '$sqlDateFormat') as start_date"),
                DB::raw("DATE_FORMAT(t1.finish_date, '$sqlDateFormat') as finish_date"),
                DB::raw('(CASE WHEN (t1.finish_date > now() AND t1.start_date > now()) THEN 0 WHEN t1.finish_date > now() THEN DATEDIFF(now(), t1.start_date) ELSE DATEDIFF(t1.finish_date, t1.start_date) END) as days'),
                DB::raw('DATEDIFF(t1.finish_date, t1.start_date) as diff_days'),
                DB::raw("CONCAT(t2.course_code,' : ',t2.course_name, ' (', t1.status, ')') as course_title")
            );
    }

    public function modifyChangedProperties()
    {
        return [
            // 'status' => fn($model, $currentValue) => new \Support\DTO\LabelValue('Status Label', 'Hello'),
            // 'offer_status' => function($model, $currentValue){
            //     return new \Support\DTO\LabelValue('Offer Label', $currentValue.' world');
            // }
        ];
    }

    public function getAppliedStudentCourse($courseId, $studentId)
    {
        if (! empty($courseId)) {

            return StudentCourses::join('rto_campus', 'rto_campus.id', '=', 'rto_student_courses.campus_id')
                ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
                ->leftjoin('rto_agents', 'rto_agents.id', '=', 'rto_student_courses.agent_id')
                ->where('rto_student_courses.student_id', '=', $studentId)
                ->where('rto_student_courses.course_id', '=', $courseId)
                ->select('rto_campus.name as campus_name', 'rto_courses.cricos_code', 'rto_courses.course_code', 'rto_courses.work_placement_hour', 'rto_courses.work_placement', 'rto_courses.course_name', 'rto_student_courses.*', 'rto_agents.agency_name', 'rto_agents.office_address', 'rto_agents.office_city', 'rto_agents.office_state', 'rto_agents.office_postcode')
                ->get();
        } else {

            return StudentCourses::join('rto_campus', 'rto_campus.id', '=', 'rto_student_courses.campus_id')
                ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
                ->join('rto_agents', 'rto_agents.id', '=', 'rto_student_courses.agent_id')
                ->leftjoin('rto_college_enrollment_fees as ref', 'ref.id', '=', 'rto_student_courses.enroll_fee')
                ->select('rto_student_courses.*', 'rto_agents.*', 'rto_campus.name as campus_name', 'rto_courses.cricos_code', 'rto_courses.work_placement_hour', 'rto_courses.work_placement', 'rto_campus.*', 'ref.fee_amount as feeAmount', 'rto_courses.course_code', 'rto_courses.course_name', 'rto_student_courses.id as studentCourseId')
                ->where('rto_student_courses.student_id', '=', $studentId)
                ->orderBy('rto_student_courses.id', 'desc')
                ->get();
        }

    }

    public function getAgentId($collegeId = null, $userId = null)
    {
        return Agent::where('college_id', $collegeId)->where('user_id', $userId)->value('id');
    }

    public function getStudentConversationReport($collegeId, $userId, $course_type, $fromDate, $toDate)
    {

        $agentId = $this->getAgentId($collegeId, $userId);

        $fromDate = (! empty($fromDate)) ? date('Y-m-d H:i:s', strtotime($fromDate)) : null;
        $toDate = (! empty($toDate)) ? date('Y-m-d H:i:s', strtotime($toDate)) : null;

        $sql = self::leftjoin('rto_students', 'rto_students.id', '=', 'rto_student_courses.student_id')
            ->where('rto_students.college_id', Auth::user()->college_id)
            ->where('course_type_id', $course_type)
            ->where('agent_id', $agentId);
        if (! empty($fromDate) && ! empty($toDate)) {
            $sql->whereBetween('rto_student_courses.created_at', [$fromDate, $toDate]);
        }
        $res1 = $sql->whereIn('rto_student_courses.offer_status', ['Enrolled', 'Offered'])->count();
        $res2 = $sql->where('rto_student_courses.offer_status', 'Enrolled')->count();
        $res3 = $sql->where('rto_student_courses.offer_status', 'Enrolled')->where('rto_student_courses.status', 'Current Student')->count();

        return [$res1, $res2, $res3];
    }

    public function getAgentWiseStudentConversationReport($collegeId, $userId, $course_type, $fromDate, $toDate)
    {

        $agentId = $this->getAgentId($collegeId, $userId);

        $fromDate = (! empty($fromDate)) ? date('Y-m-d H:i:s', strtotime($fromDate)) : null;
        $toDate = (! empty($toDate)) ? date('Y-m-d H:i:s', strtotime($toDate)) : null;

        $sql = self::select(
            'rto_users.id as user_id',
            'rto_users.email',
            'rto_users.name',
            DB::raw('COUNT(*) AS offered_students'),
            DB::raw('SUM(CASE WHEN rto_student_courses.status = "enrolled" THEN 1 ELSE 0 END) AS enrolled_students')
        )
            ->leftJoin('rto_students', 'rto_students.id', '=', 'rto_student_courses.student_id')
            ->leftJoin('rto_users', 'rto_users.id', '=', 'rto_student_courses.created_by')
            ->where('rto_students.college_id', Auth::user()->college_id)
            ->where('course_type_id', $course_type)
            ->where('agent_id', $agentId)
            ->whereNotNull('rto_student_courses.created_by');
        if (! empty($fromDate) && ! empty($toDate)) {
            $sql->whereBetween('rto_student_courses.created_at', [$fromDate, $toDate]);
        }
        $sql->groupBy('rto_student_courses.created_by');

        return $sql->get();

    }

    public function getCurrentStudentList($collegeId, $userId, $course_type, $fromDate, $toDate, $typeOfReport)
    {
        /*
        select * from `rto_student_courses` as `rsc` left join `rto_students` as `rs` on `rs`.`id` = `rsc`.`student_id` left join `rto_courses` as `rc` on `rc`.`id` = `rsc`.`course_id` where `rsc`.`agent_id` = 142 and `rsc`.`course_type_id` =  "2"  and `rsc`.`status` =  "Current Student"  and `rsc`.`created_at` between  "2021-10-07"  and  "2024-11-07"
        */

        $agentId = $this->getAgentId($collegeId, $userId);

        $fromDate = (! empty($fromDate)) ? date('Y-m-d', strtotime($fromDate)) : null;
        $toDate = (! empty($toDate)) ? date('Y-m-d', strtotime($toDate)) : null;

        $sql = self::from('rto_student_courses as rsc')
            ->leftjoin('rto_students as rs', 'rs.id', '=', 'rsc.student_id')
            ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->where('rsc.agent_id', $agentId);

        if ($typeOfReport == 4) {
            $sql->where('rsc.course_type_id', $course_type);
            $sql->where('rsc.status', 'Current Student');
            $sql->whereBetween('rsc.created_at', [$fromDate, $toDate]);
        }
        if ($typeOfReport == 5) {
            $sql->where('rsc.status', 'Inactive');
        }

        return $sql->get(columns: [
            'rs.name_title',
            'rs.first_name',
            'rs.middel_name',
            'rs.first_name',
            'rs.family_name',
            DB::raw("CONCAT_WS(' ', rs.first_name, rs.middel_name, rs.family_name) AS full_name"),
            'rs.generated_stud_id',
            'rsc.status',
            'rc.course_code',
            'rc.course_name',
            DB::raw("CONCAT_WS(' - ', rc.course_code, rc.course_name) AS course"),
            'rc.course_duration',
            DB::raw("CONCAT_WS(' ', rc.course_duration, 'Weeks') AS courseduration_full"),
            'rsc.course_fee',
            'rc.tuition_fee',
        ]);

    }

    public function getVisaRejectionReport($collegeId, $userId, $course_type, $fromDate, $toDate, $getby = 'time')
    {

        if ($fromDate == null || $toDate == null) {
            $toDate = Carbon::now();
            $fromDate = $toDate->copy()->subMonth(12);
        }
        $agentId = $this->getAgentId($collegeId, $userId);

        $sql = self::select(
            'rto_users.id as user_id',
            'rto_users.name as agent_name',
            'rto_users.email as agent_email',
            'rto_country.id as country_id',
            'rto_country.name as student_country',
            'rto_country.nationality as student_nationality',
            'rto_country.countrycode as student_countrycode',
            DB::raw("DATE_FORMAT(rto_student_courses.intake_date, '%Y-%m') as yearmonth"),
            DB::raw("DATE_FORMAT(rto_student_courses.intake_date, '%Y') as year"),
            DB::raw("DATE_FORMAT(rto_student_courses.intake_date, '%m') as month"),
            DB::raw('COUNT(*) AS offered_students'),
            DB::raw('SUM(CASE WHEN rto_student_courses.status = "rejected" THEN 1 ELSE 0 END) AS rejected_students')
        )
            ->leftJoin('rto_students', 'rto_students.id', '=', 'rto_student_courses.student_id')
            ->leftJoin('rto_users', 'rto_users.id', '=', 'rto_student_courses.created_by')
            ->leftjoin('rto_country', 'rto_students.current_country', '=', 'rto_country.id')
            ->where('rto_students.college_id', Auth::user()->college_id)
            ->where('course_type_id', $course_type)
            ->where('agent_id', $agentId);
        if (! empty($fromDate) && ! empty($toDate)) {
            $sql->whereBetween('rto_student_courses.intake_date', [$fromDate, $toDate]);
        }
        if ($getby == 'nationality') {
            $data = $sql
                ->whereNotNull('rto_country.id')
                ->groupBy(['rto_country.id'])
                ->orderBy('rto_country.name')
                ->get();

            return $this->formatByNationality($data);
        } elseif ($getby == 'agent') {
            $data = $sql
                ->whereNotNull('rto_student_courses.created_by')
                ->groupBy(['rto_users.id'])
                ->orderBy('rto_users.name')
                ->get();

            return $this->formatByAgent($data);
        } else {
            $sql->groupBy(DB::raw("DATE_FORMAT(rto_student_courses.intake_date, '%Y-%m')"));
            $sql->orderByDesc('rto_student_courses.intake_date');
            $data = $sql->get();

            return $this->formatByTimePeriod($data, $fromDate);
        }
    }

    private function formatByTimePeriod($data, $fromDate)
    {
        $startMonth = Carbon::parse($fromDate)->month;
        $startYear = Carbon::parse($fromDate)->year;
        $dataArray = [];
        for ($i = 1; $i <= 12; $i++) {
            if ($startMonth == 12) {
                $startYear++;
            }
            $month = ($startMonth % 12) + 1;
            $monthName = Carbon::createFromFormat('m', $month)->format('M');
            $dataArray[$startYear.$monthName] = [
                'category' => "{$monthName}, {$startYear}",
                'value' => 0,
                'border' => SiteConstants::AGENT_PORTAL_COLORCODES['bar'],
                'labelColor' => '#FFF',
            ];
            if ($month == 12) {
                // $startYear++;
            }
            $startMonth++;
        }
        foreach ($data as $ind => $val) {
            $month = (int) $val->month;
            $year = (int) $val->year;
            $monthName = Carbon::createFromFormat('m', $month)->format('M');
            $rejected = (int) $val->rejected_students ?? 0;
            $offered = (int) $val->offered_students ?? 0;
            $dataArray[$year.$monthName] = [
                'category' => "{$monthName}, {$year}",
                'value' => getPercentage($rejected, $offered, true, false),
                'border' => SiteConstants::AGENT_PORTAL_COLORCODES['bar'],
                'labelColor' => '#FFF',
            ];
        }
        $data = array_values($dataArray);

        return $data;
    }

    private function formatByNationality($data)
    {
        return $data->map(function ($item) {
            $rejected = (int) ($item->rejected_students ?? 0);
            $offered = (int) ($item->offered_students ?? 0);

            return [
                'category' => $item->student_country,
                'value' => getPercentage($rejected, $offered, true, false),
                'color' => SiteConstants::AGENT_PORTAL_COLORCODES['bar'],
                'labelColor' => '#FFF',
            ];
        });
    }

    private function formatByAgent($data)
    {
        return $data->map(function ($item) {
            $rejected = (int) ($item->rejected_students ?? 0);
            $offered = (int) ($item->offered_students ?? 0);

            return [
                'category' => $item->agent_name,
                'value' => getPercentage($rejected, $offered, true, false),
                'color' => SiteConstants::AGENT_PORTAL_COLORCODES['bar'],
                'labelColor' => '#FFF',
            ];
        });
    }
}
