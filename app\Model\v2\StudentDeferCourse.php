<?php

namespace App\Model\v2;

use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\Contracts\Activity;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;

class StudentDeferCourse extends Model
{
    use LogsActivity;

    protected $table = 'rto_student_defer';

    // protected $fillable = ['id','college_id','student_id','course_id','exit_type','reason','request_date','request_date','start_date','from_date','to_date','approval_status','approval_date','is_finish_dt','notify_date','comments','is_attempt','created_at','updated_at','created_by','updated_by'];

    protected $fillable = [
        // Common fields
        'college_id',
        'student_id',
        'course_id',
        'student_course_id',
        'course_status',
        'previous_course_status',
        'created_by',
        'updated_by',

        // Defer/Suspend fields
        'defer_from_date',
        'defer_to_date',
        'original_course_end_date',
        'is_course_end_date_impact',
        'new_course_end_date',
        'student_location',
        'next_class_start_date',

        // Withdrawal/Cancellation fields
        'withdrawal_date',
        'reason_for_cancellation',
        'cancellation_comment',
        'is_notice_period_completed',
        'is_appeal_internally',
        'last_attended_date',
        'cancellation_date',
        'is_approve',
        'approved_by',
        'approved_date',

        // Student release fields
        'is_student_release',
        'is_study_completed',
        'is_granting_release',
        'release_approved_by',
        'release_comment',

        // File upload fields
        'new_coe_file',
        'new_coe_code',
        'approval_evidence_file',
    ];

    protected $logAttributes = [
        'college_id',
        'student_id',
        'course_id',
        'student_course_id',
        'course_status',
        'previous_course_status',
        'defer_from_date',
        'defer_to_date',
        'original_course_end_date',
        'is_course_end_date_impact',
        'new_course_end_date',
        'student_location',
        'next_class_start_date',
        'withdrawal_date',
        'reason_for_cancellation',
        'cancellation_comment',
        'is_notice_period_completed',
        'is_appeal_internally',
        'last_attended_date',
        'cancellation_date',
        'is_approve',
        'approved_by',
        'approved_date',
        'is_student_release',
        'is_study_completed',
        'is_granting_release',
        'release_approved_by',
        'release_comment',
        'new_coe_file',
        'new_coe_code',
        'approval_evidence_file',
        'created_by',
        'updated_by',
    ];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logFillable()
            ->logOnlyDirty()
            ->setDescriptionForEvent(fn (string $eventName) => "Course Variant has been {$eventName}");
    }

    public function tapActivity(Activity $activity, string $eventName)
    {
        $activity->log_name = $this->customLogKey();
    }

    public function customLogKey()
    {
        return (new self)->getMorphClass().'_'.$this->student_id.'_'.$this->course_id;
    }
}
