<?php

namespace App\Model\v2;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\Contracts\Activity;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;
use Support\Traits\CreaterUpdaterTrait;

class StudentDetails extends Model
{
    use CreaterUpdaterTrait;
    use HasFactory;
    use LogsActivity;

    protected $table = 'rto_student_details';

    protected $primaryKey = 'student_id';

    // If the primary key is not an incrementing integer
    public $incrementing = false;

    // protected $fillable = ['offer_id','emergency_contact_person','emergency_relationship','emergency_address','emergency_phone','emergency_email'];

    protected $fillable = [
        'student_id',
        'is_aus_aboriginal_or_islander',
        'is_english_main_lang',
        'main_lang',
        'english_speak_level',
        'was_english_previous_study',
        'is_EPL_test',
        'EPL_test_name',
        'EPL_test_date',
        'EPL_istening_score',
        'EPL_reading_score',
        'EPL_writing_score',
        'EPL_speaking_score',
        'EPL_overall_score',
        'EPL_other_name',
        'highest_completed_school_level',
        'completed_school_year',
        'attending_secondary_school',
        'school_type',
        'funding_source_state',
        'school_location_id',
        'vet_in_flag',
        'school_type_identifier',
        'current_employment_status',
        'offer_id',
        'hear_about_id',
        'aboutus_specify',
        'airport_pickup',
        'arrival_flight_no',
        'arrival_date',
        'arrival_time',
        'no_of_bags',
        'home_stay',
        'accomodation_start_date',
        'length_of_stay',
        'allergies',
        'smoke',
        'picup_fee',
        'service_fee',
        'placement_fee',
        'accommodation_fee',
        'arrange_OSHC',
        'OSHC_course_id',
        'OSHC_provider',
        'OSHC_type',
        'OSHC_duration',
        'OSHC_fee',
        'applied_oshc_fee',
        'OSHC_start_date',
        'OSHC_end_date',
        'disability',
        'area_of_disability',
        'area_other_note',
        'emergency_contact_person',
        'emergency_relationship',
        'emergency_address',
        'emergency_phone',
        'emergency_email',
        'account_manager_id',
        'previous_qualifications',
        'scs',
        'privacy_note',
        'is_declare',
        'declaration_file',
        'dec_file_org_name',
        'consent_fullname',
        'consent_email',
        'consent_address',
        'consent_telephone',
        'consent_alt_telephone',
        'is_consent_confirm',
        'consent_file',
        'con_file_org_name',
        'file_path',
        'application_form_file',
        'created_by',
        'updated_by',
    ];

    protected $logAttributes = [
        'student_id',
        'is_aus_aboriginal_or_islander',
        'is_english_main_lang',
        'main_lang',
        'english_speak_level',
        'was_english_previous_study',
        'is_EPL_test',
        'EPL_test_name',
        'EPL_test_date',
        'EPL_istening_score',
        'EPL_reading_score',
        'EPL_writing_score',
        'EPL_speaking_score',
        'EPL_overall_score',
        'EPL_other_name',
        'highest_completed_school_level',
        'completed_school_year',
        'attending_secondary_school',
        'school_type',
        'funding_source_state',
        'school_location_id',
        'vet_in_flag',
        'school_type_identifier',
        'current_employment_status',
        'offer_id',
        'hear_about_id',
        'aboutus_specify',
        'airport_pickup',
        'arrival_flight_no',
        'arrival_date',
        'arrival_time',
        'no_of_bags',
        'home_stay',
        'accomodation_start_date',
        'length_of_stay',
        'allergies',
        'smoke',
        'picup_fee',
        'service_fee',
        'placement_fee',
        'accommodation_fee',
        'arrange_OSHC',
        'OSHC_course_id',
        'OSHC_provider',
        'OSHC_type',
        'OSHC_duration',
        'OSHC_fee',
        'applied_oshc_fee',
        'OSHC_start_date',
        'OSHC_end_date',
        'disability',
        'area_of_disability',
        'area_other_note',
        'emergency_contact_person',
        'emergency_relationship',
        'emergency_address',
        'emergency_phone',
        'emergency_email',
        'account_manager_id',
        'previous_qualifications',
        'scs',
        'privacy_note',
        'is_declare',
        'declaration_file',
        'dec_file_org_name',
        'consent_fullname',
        'consent_email',
        'consent_address',
        'consent_telephone',
        'consent_alt_telephone',
        'is_consent_confirm',
        'consent_file',
        'con_file_org_name',
        'file_path',
        'application_form_file',
    ];

    public function gettestname()
    {
        return $this->hasOne(SetupSection::class, 'id', 'EPL_test_name');
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logFillable()
            ->logOnlyDirty()
            ->setDescriptionForEvent(fn (string $eventName) => "Student details has been {$eventName}");
    }

    public function tapActivity(Activity $activity, string $eventName)
    {
        $activity->log_name = (new self)->getMorphClass();
        $properties = json_decode($activity->properties, true);
        $changedAttributes = $properties['attributes'] ?? [];
        $allOldAttributes = $this->getOldAttributes();
        if ($eventName != 'deleted') {
            if (! empty($changedAttributes)) {
                $activity->log_name = 'student_emergency_details_'.$this->student_id;
                $activity->description = $this->getEmergencyDetails($properties['attributes'], $allOldAttributes);
            } else {
                $activity->description = "Student details $eventName";
            }
        }

    }

    private function getOldAttributes()
    {
        return $this->getOriginal(); // Assuming this method retrieves the model's original state
    }

    public function saveStudentDetailsStep3($studentId, $studentOfferId)
    {
        $studentDetails = StudentDetails::find($studentId);
        $studentDetails->offer_id = ($studentOfferId != '') ? $studentOfferId : null;
        $studentDetails->save();
    }

    public function getEmergencyDetails($attributes, $old): string
    {
        $components = [];
        if (! empty($attributes['emergency_contact_person'])) {
            $components[] = 'Contact Person::'.$attributes['emergency_contact_person'];
        }
        if (! empty($attributes['emergency_relationship'])) {
            $components[] = 'Relationship::'.$attributes['emergency_relationship'];
        }
        if (! empty($attributes['emergency_phone'])) {
            $components[] = 'Phone::'.$attributes['emergency_phone'];
        }
        if (! empty($attributes['emergency_email'])) {
            $components[] = 'Email::'.$attributes['emergency_email'];
        }
        if (! empty($attributes['emergency_address'])) {
            $components[] = 'Address::'.$attributes['emergency_address'];
        }

        return implode(',', $components);
    }

    private function getValue(array $new, string $newKey, array $old = [], string $oldKey = ''): ?string
    {
        return ! empty($new[$newKey]) ? $new[$newKey] : ($old[$oldKey] ?? null);
    }
}
