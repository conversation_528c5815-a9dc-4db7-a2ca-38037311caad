<?php

namespace App\Model\v2;

use App\Observers\StudentsObserver;
use Domains\Xero\Traits\HasXeroData;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Spatie\Activitylog\Contracts\Activity;
use Spatie\Activitylog\LogOptions;
use Spatie\Activitylog\Traits\LogsActivity;
use Support\Traits\HasJsonFields;

class StudentInitialPaymentTransaction extends Model
{
    use HasFactory;
    use HasJsonFields;
    use HasXeroData;
    use LogsActivity;

    protected $table = 'rto_student_initial_payment_transaction';

    protected $fillable = [
        'id ',
        'college_id',
        'student_id',
        'course_id',
        'student_course_id',
        'initial_payment_detail_id',
        'receipt_no',
        'transection_no',
        'payment_date',
        'paid_amount',
        'deposited_amount',
        'agent_commission_deduct',
        'payment_mode',
        'reference_no',
        'bank_deposit_date',
        'remarks',
        'scholarship',
        'amount_refund',
        'agent_bonus',
        'bonus_gst',
        'agent_bonus',
        'bonus_gst',
        'bonus_paid_date',
        'bad_debt',
        'reversed',
        'reverse_comment',
        'receipt_sent',
        'student_administration_cost',
        'student_refund_mode',
        'student_refund_date',
        'agent_commission_refund_amount',
        'agent_refunded',
        'agent_refund_mode',
        'agent_refund_date',
        'refund_remarks',
        'student_net_receive',
        'student_credit',
        'is_delete',
        'reason_to_delete',
        'receipt_sent_date',
        'created_by',
        'updated_by',
        'xero_payment_id',
        'xero_transaction_type',
    ];

    protected $casts = [
        'xero_data' => 'json',
    ];

    protected static $logAttributes = [
        'course_id',
        'student_course_id',
        'initial_payment_detail_id',
        'receipt_no',
        'transection_no',
        'payment_date',
        'paid_amount',
        'deposited_amount',
        'agent_commission_deduct',
        'payment_mode',
        'reference_no',
        'bank_deposit_date',
        'remarks',
        'scholarship',
        'amount_refund',
        'agent_bonus',
        'bonus_gst',
        'agent_bonus',
        'bonus_gst',
        'bonus_paid_date',
        'bad_debt',
        'reversed',
        'reverse_comment',
        'receipt_sent',
        'student_administration_cost',
        'student_refund_mode',
        'student_refund_date',
        'agent_commission_refund_amount',
        'agent_refunded',
        'agent_refund_mode',
        'agent_refund_date',
        'refund_remarks',
        'student_net_receive',
        'student_credit',
        'is_delete',
        'reason_to_delete',
        'receipt_sent_date',
        'xero_payment_id',
        'xero_transaction_type',
    ];

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logFillable()
            ->logOnlyDirty()
            ->setDescriptionForEvent(fn (string $eventName) => "Payment Schedule transaction has been {$eventName}");
        // Chain fluent methods for configuration options
    }

    /* This method is fired before saving the log to db. */
    public function tapActivity(Activity $activity, string $eventName)
    {
        $activity->log_name = (new self)->getMorphClass().'_'.$this->student_id.'_'.$this->course_id;
        if (! empty($eventName)) {
            $identifier = $this->formatted_invoice_number;
            $activity->description = "Payment Schedule transaction $identifier has been $eventName";
            if ($eventName == 'deleted') {
                if (request('reason')) {
                    $activity->description .= '<br> <strong>REASON:</strong> '.request('reason');
                }
            }
        }
    }

    public function getDescriptionForEvent(string $eventName): string
    {
        return "Payment Schedule Transaction has been {$eventName}";
    }

    public function invoice()
    {
        return $this->belongsTo(StudentInitialPaymentDetails::class, 'initial_payment_detail_id');
    }

    public function agentCommission()
    {
        return $this->hasOne(StudentAgentCommission::class, 'transaction_no', 'transection_no');
    }

    public function isSyncedToXero()
    {
        return ! is_null($this->xero_payment_id);
    }

    public function paymentSyncedWithXero($xeroPaymentID)
    {
        $this->xero_payment_id = $xeroPaymentID;
        $this->updateJsonField([
            'xero_synced_at' => now(),
        ], $this->xeroDataField());

        $this->save();

        return $this;
    }

    public function scopeNotSyncedWithXero($q)
    {
        return $q->whereNull('xero_payment_id');
    }

    public function xeroDataField()
    {
        return 'xero_data';
    }

    public function course()
    {
        return $this->belongsTo(Courses::class, 'course_id');
    }

    public function student()
    {
        return $this->belongsTo(Student::class, 'student_id');
    }

    public function studentCourses()
    {
        return $this->belongsTo(StudentCourses::class, 'student_id', 'student_id');
    }

    // protected static function boot()
    // {
    //     parent::boot();
    //     self::observe(StudentsObserver::class);
    // }

}
