<?php

namespace App\Model\v2;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;

class StudentIntervention extends Model
{
    protected $table = 'rto_student_intervention';

    protected $fillable = ['id', 'college_id', 'student_id', 'recorded_date', 'intervention_for', 'course_id', 'semester', 'is_catchUp', 'is_course', 'is_semester', 'case_detail', 'action_taken', 'reoccurance', 'case_status', 'due_date', 'outcome_date', 'teacher_id', 'created_at', 'updated_at', 'created_by', 'updated_by'];

    public function collage()
    {
        return $this->belongsTo(Colleges::class, 'college_id', 'id');
    }

    public function student()
    {
        return $this->belongsTo(Student::class, 'student_id', 'id');
    }

    public function course()
    {
        return $this->belongsTo(Courses::class, 'course_id', 'id');
    }

    public function studentSemester()
    {
        return $this->belongsTo(Semester::class, 'semester', 'id');
    }

    public function teacher()
    {
        return $this->belongsTo(Staff::class, 'teacher_id', 'id');
    }

    public function studentInterventionStrategies()
    {
        return $this->hasMany(StudentInterventionStrategy::class, 'intervention_id', 'id');
    }

    public function afterCreateProcess()
    {
        $request = request();
        $this->onSave($request);
    }

    public function afterUpdateProcess()
    {
        $request = request();
        $this->onSave($request);
    }

    public function onSave(
        $request,
    ) {
        $interventionTypeIds = $request->get('intervention_type_ids');
        $interventionStrategiesIds = $request->get('intervention_strategy_ids');
        $records = [];
        foreach ($interventionTypeIds as $interventionTypeId) {
            $includedStrategies = \App\Model\StudentInterventionStrategy::query()
                ->where('intervention_type_id', $interventionTypeId)
                ->whereIn('intervention_strategy_id', $interventionStrategiesIds)
                ->get()
                ->pluck('id')
                ->toArray();
            $excludedStrategies = array_diff($interventionStrategiesIds, $includedStrategies);
            foreach ($excludedStrategies as $excludedStrategy) {
                $records[] = [
                    'intervention_id' => $this->id,
                    'intervention_type_id' => $interventionTypeId,
                    'intervention_strategy_id' => $excludedStrategy,
                    'created_by' => Auth::user()?->id,
                    'updated_by' => Auth::user()?->id,
                ];
            }
        }
        if (count($records) > 0) {
            \App\Model\StudentInterventionStrategy::insert($records);
        }
    }
}
