<?php

namespace App\Model\v2;

use Auth;
use DB;
use Illuminate\Database\Eloquent\Model;

class StudentInterventionStrategy extends Model
{
    protected $table = 'rto_student_intervention_strategy';

    protected $fillable = ['id', 'intervention_id', 'intervention_type_id', 'intervention_strategy_id', 'created_at', 'updated_at', 'created_by', 'updated_by'];

    public function interventionType()
    {
        return $this->belongsTo(InterventionType::class, 'intervention_type_id', 'id');
    }

    public function interventionStrategy()
    {
        return $this->belongsTo(InterventionStrategy::class, 'intervention_strategy_id', 'id');
    }

    public function saveInterventionStrategy($interventionId, $request)
    {

        $userId = Auth::user()->id;
        $category = $request->input('intervention_type');
        $subCategory = $request->input('subCategory');

        foreach ($category as $i => $value) {
            if (! empty($subCategory[$value])) {
                foreach ($subCategory[$value] as $k => $subValue) {
                    if (! empty($value) && ! empty($subValue)) {
                        $objStrategy = new StudentInterventionStrategy;
                        $objStrategy->intervention_id = $interventionId;
                        $objStrategy->intervention_type_id = $value;
                        $objStrategy->intervention_strategy_id = $subValue;
                        $objStrategy->created_by = $userId;
                        $objStrategy->updated_by = $userId;
                        $objStrategy->save();
                        $objStrategy = '';
                    }
                }
            }
        }
    }

    public function editInterventionStrategy($interventionId, $request)
    {
        $userId = Auth::user()->id;
        $category = $request->input('intervention_type');
        $subCategory = $request->input('subCategory');

        $objStrategyDelete = new StudentInterventionStrategy;
        $objStrategyDelete::where('intervention_id', $interventionId)->delete();

        foreach ($category as $i => $value) {
            if (! empty($subCategory[$value])) {
                foreach ($subCategory[$value] as $k => $subValue) {
                    if (! empty($value) && (! empty($subValue))) {
                        $objStrategyEdit = new StudentInterventionStrategy;
                        $objStrategyEdit->intervention_id = $interventionId;
                        $objStrategyEdit->intervention_type_id = $value;
                        $objStrategyEdit->intervention_strategy_id = $subValue;
                        $objStrategyEdit->created_by = $userId;
                        $objStrategyEdit->updated_by = $userId;
                        $objStrategyEdit->save();
                        $objStrategyEdit = '';
                    }
                }
            }
        }
    }

    public function editInterventionStrategy_old($interventionId, $request)
    {

        $userId = Auth::user()->id;
        $arrStrategy = StudentInterventionStrategy::where('intervention_id', $interventionId)->get();
        $objStrategy = $arrStrategy[0];

        $objStrategy->is_catchUp1 = ($request->input('is_catchUp') && $request->input('is_catchUp1')) ? $request->input('is_catchUp1') : '0';
        $objStrategy->is_catchUp2 = ($request->input('is_catchUp') && $request->input('is_catchUp2')) ? $request->input('is_catchUp2') : '0';

        $objStrategy->is_course1 = ($request->input('is_course') && $request->input('is_course1')) ? $request->input('is_course1') : '0';
        $objStrategy->is_course2 = ($request->input('is_course') && $request->input('is_course2')) ? $request->input('is_course2') : '0';

        $objStrategy->is_semester1 = ($request->input('is_semester') && $request->input('is_semester1')) ? $request->input('is_semester1') : '0';

        $objStrategy->is_subject1 = ($request->input('is_subject') && $request->input('is_subject1')) ? $request->input('is_subject1') : '0';
        $objStrategy->is_subject2 = ($request->input('is_subject') && $request->input('is_subject2')) ? $request->input('is_subject2') : '0';
        $objStrategy->is_subject3 = ($request->input('is_subject') && $request->input('is_subject3')) ? $request->input('is_subject3') : '0';
        $objStrategy->is_subject4 = ($request->input('is_subject') && $request->input('is_subject4')) ? $request->input('is_subject4') : '0';
        $objStrategy->is_subject5 = ($request->input('is_subject') && $request->input('is_subject5')) ? $request->input('is_subject5') : '0';

        $objStrategy->created_by = $userId;
        $objStrategy->updated_by = $userId;

        $objStrategy->save();
    }

    public function getInterventionSubCategoty($collegeId, $studentId, $interventionId)
    {
        $return = StudentInterventionStrategy::where('intervention_id', $interventionId)->get()->toArray();
        $cat = [];
        $catVal = [];
        foreach ($return as $returnVal) {
            if (! in_array($returnVal['intervention_type_id'], $cat)) {
                $cat[] = $returnVal['intervention_type_id'];
            }
            if (! in_array($returnVal['intervention_strategy_id'], $catVal)) {
                $catVal[] = $returnVal['intervention_strategy_id'];
            }
        }

        return ['cat' => $cat, 'catVal' => $catVal];
    }

    // Delete Invervention Strategy By ID
    public function getInterventionDelete($interventionId)
    {
        $returnInterventionDelete = StudentInterventionStrategy::Where('intervention_id', $interventionId)->delete();

        return $returnInterventionDelete;
    }

    public function getStrategyList($interventionId)
    {

        $nullResult = 'No Strategy Found';
        $resultArr = StudentInterventionStrategy::from('rto_student_intervention_strategy as rsis')
            ->leftjoin('rto_intervention_strategy as ris', 'ris.id', '=', 'rsis.intervention_strategy_id')
            ->where('rsis.intervention_id', $interventionId)
            ->select(DB::raw('GROUP_CONCAT(ris.strategy SEPARATOR ", ") AS strategy'))
            ->get();

        return $result = (count($resultArr) > 0) ? $resultArr[0]->strategy : $nullResult;
    }

    public function getInterventionTypeList($interventionId)
    {

        $nullResult = 'No Intervention Type Found';
        $resultArr = StudentInterventionStrategy::from('rto_student_intervention_strategy as rsis')
            ->leftjoin('rto_intervention_type as rit', 'rit.id', '=', 'rsis.intervention_type_id')
            ->where('rsis.intervention_id', $interventionId)
            ->select(DB::raw('GROUP_CONCAT(DISTINCT  rit.intervention_type SEPARATOR", ") AS type'))
            ->get();

        return $result = (count($resultArr) > 0) ? $resultArr[0]->type : $nullResult;
    }
}
