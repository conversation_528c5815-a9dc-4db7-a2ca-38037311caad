<?php

namespace App\Model\v2;

use Illuminate\Database\Eloquent\Model;

class StudentPaymentAgentCredit extends Model
{
    protected $table = 'rto_payment_agent_credit';

    protected $fillable = [
        'id',
        'college_id',
        'payment_transaction_id',
        'credit_bonus_id',
        'amount',
        'created_by',
        'updated_by',
    ];

    public function creditBonusAllocation()
    {
        return $this->belongsTo(CreditBonusAllocation::class, 'credit_bonus_id');
    }

    public function studentInitialPayment()
    {
        return $this->belongsTo(StudentInitialPaymentTransaction::class, 'payment_transaction_id');
    }

    public function scopeFilterBonusId($query, $value)
    {
        if (empty($value)) {
            return $query;
        }

        return $query->where('credit_bonus_id', $value);
    }
}
