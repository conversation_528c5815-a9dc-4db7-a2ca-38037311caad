<?php

namespace App\Model\v2;

use App\Model\v2\CourseType;
use Domains\Moodle\Facades\Moodle;
use Illuminate\Support\Collection;
use Illuminate\Support\Facades\DB;
use Spatie\Activitylog\LogOptions;
use Domains\Moodle\Entities\Entity;
use Illuminate\Support\Facades\Auth;
use App\Model\v2\StudentUnitEnrollment;
use Illuminate\Database\Eloquent\Model;
use Support\Traits\CreaterUpdaterTrait;
use Spatie\Activitylog\Contracts\Activity;
use Spatie\Activitylog\Traits\LogsActivity;
use Domains\Moodle\Traits\MoodleSyncableItem;
use Domains\Moodle\Contracts\CanConnectToMoodle;
use App\Traits\ActivityLog\SaveResultActivityLog;
use Domains\Moodle\Repositories\MoodleRepository;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Zoha\Metable;

class StudentSubjectEnrolment extends Model
{
    use HasFactory;
    use LogsActivity;
    use SaveResultActivityLog;
    use CreaterUpdaterTrait;
    use Metable;

    const MOODLE_META_KEY = 'moodle_enrollment';

    protected $table = 'rto_student_subject_enrolment';

    protected $fillable = [
        'college_id',
        'student_id',
        'course_id',
        'student_course_id',
        'semester_id',
        'term',
        'enroll_type',
        'course_stage',
        'subject_id',
        'unit_id',
        'batch',
        'activity_start_date',
        'activity_finish_date',
        'last_assessment_approved_date',
        'study_reason',
        'course_commencing_id',
        'training_contract_id',
        'apprenticeship_client_id',
        'fee_exemption_id',
        'con_schedule_id',
        'purchase_contract_id',
        'booking_id',
        'course_site_id',
        'vanue_location',
        'funding_source_state',
        'funding_source_nat',
        'funding_identifier',
        'subject_attempt',
        'predominant_delivery_mode',
        'old_predominant_delivery_mode',
        'final_outcome',
        'mark_outcome',
        'grade',
        'marks',
        'delivery_mode',
        'vet_in_school',
        'outcome_identifier',
        'census_date',
        'mode_of_attendance',
        'subject_tution_fee',
        'fee_paid_upfront',
        'eftsl_study_load',
        'is_result_lock',
        'comment',
        'created_by',
        'updated_by'
    ];

    protected $logAttributes = [
        'college_id',
        'student_id',
        'course_id',
        'semester_id',
        'term',
        'student_course_id',
        'enroll_type',
        'course_stage',
        'subject_id',
        'unit_id',
        'batch',
        'activity_start_date',
        'activity_finish_date',
        'study_reason',
        'course_commencing_id',
        'training_contract_id',
        'apprenticeship_client_id',
        'fee_exemption_id',
        'con_schedule_id',
        'purchase_contract_id',
        'booking_id',
        'course_site_id',
        'vanue_location',
        'funding_source_state',
        'funding_source_nat',
        'funding_identifier',
        'subject_attempt',
        'predominant_delivery_mode',
        'old_predominant_delivery_mode',
        'final_outcome',
        'mark_outcome',
        'grade',
        'marks',
        'delivery_mode',
        'vet_in_school',
        'outcome_identifier',
        'census_date',
        'is_result_lock',
        'mode_of_attendance',
        'subject_tution_fee',
        'fee_paid_upfront',
        'eftsl_study_load',
        'comment',
    ];


    public static function booted(): void
    {
        static::created(function (StudentSubjectEnrolment $model) {
            if($model->unit_id != '' && $model->batch != '')
            {
                CourseBatch::firstOrCreate([
                    'unit_id' => $model->unit_id,
                    'batch' => $model->batch
                ]);
            }
        });
        static::updated(function (StudentSubjectEnrolment $model) {
            if($model->unit_id != '' && $model->batch != '')
            {
                CourseBatch::firstOrCreate([
                    'unit_id' => $model->unit_id,
                    'batch' => $model->batch
                ]);
            }
        });
    }

    public function getActivitylogOptions(): LogOptions
    {
        return LogOptions::defaults()
            ->logFillable()
            ->logOnlyDirty()
            ->setDescriptionForEvent(fn(string $eventName) => "Student Subject Enrollment has been {$eventName}");
    }

    public function tapActivity(Activity $activity, string $eventName)
    {
        $activity->log_name = (new self)->getMorphClass() . '_' . $this->student_id . '_' . $this->course_id;
        $activity->subject_type = self::class;
        $studentName = $this->student->first_name . ' ' . $this->student->family_name; // Adjust based on your relationship
        $unitName = $this->unit->unit_code . ' :' . $this->unit->unit_name; // Adjust based on your relationship
        $detailData['studentName'] = $studentName;
        $detailData['unitName'] = $unitName;
        $detailData['taskName'] = '';
        $detailData['old_group_id'] = '';
        $detailData['studentId'] = $this->student->generated_stud_id;

        $activity->description = $this->getCustomDescriptionForEvent($eventName, $detailData);
    }
    public function student()
    {
        return $this->hasOne(Student::class, 'id', 'student_id');
    }
    public function course()
    {
        return $this->hasOne(Courses::class, 'id', 'course_id');
    }

    public function timeTables()
    {
        return $this->hasMany(Timetable::class, 'batch', 'batch');
    }

    public function unit()
    {
        return $this->hasOne(UnitModule::class, "id", "unit_id");
    }
    public function semester()
    {
        return $this->hasOne(Semester::class, "id", "semester_id");
    }

     /* Moodle Course */
     public function subjectUnitRevised()
     {
         return $this->hasMany(SubjectUnits::class, 'synced_for', 'unit_id');
     }

    public function subject()
    {
        return $this->hasOne(Subject::class, "id", "subject_id");
    }

    public function getStudentCourseListForPdf($studentId, $courseId, $postData)
    {

        $courseType = Courses::where('id', $courseId)->get(['course_type_id'])->first();
        $isHigherEd = (new CourseType)->checkHigherEdGradingType($courseType->course_type_id);
        if (isset($postData['transcriptType']) && isset($postData['include_failed_subject']) && ($postData['transcriptType'] == "TOCA") && ($postData['include_failed_subject'])) {
            $resultArr = ['C', 'RPL', 'RCC', 'CT', 'WD', 'CE', 'NYC'];
        } elseif ($postData['transcriptType'] == "SOA") {
            $resultArr = ['C', 'RPL', 'CT'];
        } elseif ($postData['transcriptType'] == "TOCA") {
            $resultArr = ['C', 'RPL', 'RCC', 'CT', 'WD', 'NYC'];
        } else {
            $resultArr = ['C', 'RPL', 'RCC', 'CT', 'WD'];
        }

        $query = StudentSubjectEnrolment::select('rto_student_subject_enrolment.id', 'sem.semester_name', 'rsu.unit_code', 'rsu.unit_name', 'rsu.description', 'rto_student_subject_enrolment.activity_finish_date as year', 'rsue.competency_date as unitcomyear', 'rto_student_subject_enrolment.final_outcome as final_outcome', 'rto_student_subject_enrolment.grade', 'rto_student_subject_enrolment.marks', 'rto_student_subject_enrolment.mark_outcome', 'rs.credit_point')
            ->leftjoin('rto_subject_unit as rsu', 'rsu.id', '=', 'rto_student_subject_enrolment.unit_id')
            ->leftjoin('rto_subject as rs', 'rs.id', '=', 'rto_student_subject_enrolment.subject_id')
            ->leftjoin('rto_student_unit_enrollment as rsue', 'rsue.student_subject_enrollment_id', '=', 'rto_student_subject_enrolment.id', 'AND', 'rsue.unit_id', '=', 'rsu.id')
            ->leftjoin('rto_semester as sem', 'sem.id', '=', 'rto_student_subject_enrolment.semester_id')
            ->where('rto_student_subject_enrolment.college_id', '=', Auth::user()->college_id)
            ->where('rto_student_subject_enrolment.course_id', '=', $courseId)
            ->where('rto_student_subject_enrolment.student_id', '=', $studentId);

        if (isset($postData['subject_enrollment_id']) && $postData['subject_enrollment_id'] != '') {
            $query->where('rto_student_subject_enrolment.id', '=', $postData['subject_enrollment_id']);
        }

        // Add scopeWithStudentCourseId if student_course_id is provided in postData
        if (isset($postData['student_course_id']) && $postData['student_course_id'] != '') {
            $query->withStudentCourseId($postData['student_course_id']);
        }

        // temp condition for generate certificate for student 1810655
        if(tempCertificateCondition($studentId,$courseId)){
            $query->where('rto_student_subject_enrolment.subject_attempt', '=', 2);
        }
        
        if(!$isHigherEd){
            $query->whereIn('rto_student_subject_enrolment.final_outcome', $resultArr);
        }else{
            if($postData['transcriptType'] == "TOGA" || $postData['transcriptType'] == "UTOGA")
            {
                $whereArr = [
                    'college_id' => Auth::user()->college_id,
                    'grade' => "Enrolled"
                ];
                $gradeDatas = ResultGrade::where($whereArr)->select('*')->first();
                $query->where('rto_student_subject_enrolment.mark_outcome','!=', $gradeDatas->id);

                // $query->where('rto_student_subject_enrolment.grade','!=', 'Enrolled');
            }
        }

        $result = $query->groupby('rto_student_subject_enrolment.id')->get()->toArray();

        return $result;
    }


    public function toSearchableArray($context = null): array
    {
        return [
            // 'id' => (int) $this->id,
            'batch' => $this->batch,
            'enroll_type' => $this->enroll_type,
            'course_stage' => $this->course_stage,
            'activity_start_date' => $this->activity_start_date,
            'activity_finish_date' => $this->activity_finish_date,
            'teacher_id' => $this->timeTables->map(fn($item) => $item->toSearchableArray($context))->pluck('teacher_id')
                ->filter(function ($item) {
                    return $item;
                })->first()
            // 'timetables' => $this->timeTables->map(fn ($item) => $item->toSearchableArray($context)),
            // 'batch' => $this->batch,

        ];
    }

    public static function ListStudentSubjectEnrolment($collegeId = null, $studentId = null, $course = null)
    {
        $qry = StudentSubjectEnrolment::leftjoin('rto_semester', 'rto_semester.id', '=', 'rto_student_subject_enrolment.semester_id')
            ->leftjoin('rto_subject', 'rto_subject.id', '=', 'rto_student_subject_enrolment.subject_id')
            ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_subject_enrolment.course_id')
            //->leftjoin('rto_subject_unit', 'rto_subject_unit.id', '=', 'rto_student_subject_enrolment.unit_id')
            ->leftjoin('rto_student_courses', 'rto_student_courses.course_id', '=', 'rto_student_subject_enrolment.course_id');
        $qry->join("rto_timetable", function ($query) {
            $query->on("rto_timetable.batch", "=", "rto_student_subject_enrolment.batch")
                ->on("rto_timetable.subject_id", "=", "rto_student_subject_enrolment.subject_id");
        });
        if ($collegeId) {
            $qry->where('rto_student_subject_enrolment.college_id', '=', $collegeId);
        }
        if ($course instanceof Collection) {
            $qry->whereIn('rto_student_subject_enrolment.course_id', $course);
        } else if (!empty($course)) {
            $qry->where('rto_student_subject_enrolment.course_id', $course);
        }
        $qry->groupBy('rto_student_subject_enrolment.subject_id')
            ->groupBy('rto_student_subject_enrolment.semester_id')
            ->orderBy('rto_student_subject_enrolment.id')
            ->select(['rto_student_subject_enrolment.*', 'rto_semester.semester_name', 'rto_subject.subject_code', 'rto_subject.subject_name', 'rto_student_courses.campus_id', 'rto_courses.flexible_attendance']);
        $resultArr = $qry->get();
        return $resultArr;
    }

    public static function getCourseProgressSummaryByUnitV2($collegeId, $studentId, $courseId) {

        return StudentSubjectEnrolment::from('rto_student_subject_enrolment as rsse')
                        ->leftjoin('rto_students', 'rto_students.id', '=', 'rsse.student_id')
                        ->leftjoin('rto_student_unit_enrollment as rsue', 'rsue.student_subject_enrollment_id', '=', 'rsse.id')
                        ->leftjoin('rto_subject_unit as rsu', 'rsu.id', '=', 'rsse.unit_id')
                        ->leftjoin('rto_subject as rs', 'rs.id', '=', 'rsse.subject_id')
                        ->leftjoin('rto_venue as rv', 'rv.id', '=', 'rsse.vanue_location')
                        ->where('rsse.college_id', '=', $collegeId)
                        ->where('rsse.student_id', '=', $studentId)
                        ->where('rsse.course_id', '=', $courseId)
                        ->get(['rsu.id',
                            'rsu.unit_code',
                            'rsu.vet_unit_code',
                            'rsu.unit_name',
                            'rsue.compentency',
                            'rs.subject_code',
                            'rv.postcode',
                            'rto_students.birth_country',
                            'rsse.*']);
    }

    /**
     * Scope to filter by student_course_id being null or a specific value.
     *
     * @param \Illuminate\Database\Eloquent\Builder $query
     * @param int|null $studentCourseId
     * @return \Illuminate\Database\Eloquent\Builder
     */
    public function scopeWithStudentCourseId($query, $studentCourseId)
    {
        return $query->where(function ($query) use ($studentCourseId) {
            $query->whereNull('student_course_id')
                  ->orWhere('student_course_id', $studentCourseId);
        });
    }
}
