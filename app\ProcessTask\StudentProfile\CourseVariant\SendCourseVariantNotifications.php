<?php

namespace App\ProcessTask\StudentProfile\CourseVariant;

use App\Model\v2\CollegeDetails;
use App\Model\v2\StudentCourses;
use App\Services\StudentProfileMoreActionServicess;
use App\Traits\SendNotificationTrait;
use Closure;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\Log;
use Notifications\Types\DTOs\CourseCancellationDTO;
use Notifications\Types\DTOs\StudentWithdrawalNotificationDTO;
use Notifications\Types\NotificationType;

class SendCourseVariantNotifications
{
    use SendNotificationTrait;

    private $studentProfileService;

    public function __construct()
    {
        // $this->studentProfileService = app(StudentProfileMoreActionServicess::class);
    }

    public function handle($data, Closure $next)
    {
        $studCourseId = $data['studCourseId'];
        $newStatus = $data['newStatus'] ?? $data['courseVariantData']['course_status'] ?? null;
        $oldStatus = $data['oldStatus'] ?? '';
        $courseVariantData = $data['courseVariantData'] ?? [];
        $isUpdate = $data['is_update'] ?? false;

        try {
            if ($isUpdate && $oldStatus === $newStatus) {
                return ['status' => 'success', 'message' => 'Course Variant details saved successfully.'];
            }

            // Handle withdrawal or cancellation notifications
            if (in_array($newStatus, ['Withdrawn', 'Cancelled'])) {
                $this->notifyIfWithdrawOrCancel($studCourseId, $courseVariantData);
            }

            return ['status' => 'success', 'message' => 'Course Variant details saved successfully.'];
        } catch (\Exception $e) {
            Log::error('Failed to send course variant notifications: ', ['error' => $e->getMessage()]);

            return ['status' => 'error', 'message' => 'Failed to save course variant. Please try again.'];
        }

        return $next($data);
    }

    public function notifyIfWithdrawOrCancel($studentCourseId, $courseVariantData)
    {
        $resData = StudentCourses::with('course')->where('id', $studentCourseId)->first();

        // $loginUser = auth()->user();
        $currentDate = now()->format('d M Y');
        $collage = CollegeDetails::find(auth()->user()->college_id);
        $itEmail = $collage->it_email;
        $loginUser = clone auth()->user();
        $loginUser->email = $itEmail;
        if ($resData->status == 'Withdrawn') {
            $notificationData = StudentWithdrawalNotificationDTO::LazyFromArray([
                'courseId' => $resData->course_id,
                'courseName' => $resData->course->course_name,
                'withdrawnAt' => $currentDate,
            ]);
            $this->sendNotification($loginUser, NotificationType::STUDENT_WITHDRAWAL, $notificationData);
        }

        if ($resData->status == 'Cancelled') {
            $reasonText = '';
            if (! empty($courseVariantData['reason_for_cancellation'])) {
                $cancelReasons = Config::get('constants.arrCourseCancelReason');
                $reasonText = $cancelReasons[$courseVariantData['reason_for_cancellation']] ?? $courseVariantData['reason_for_cancellation'];
            }

            $notificationData = CourseCancellationDTO::LazyFromArray([
                'studentCourse' => $resData,
                'reason' => $reasonText,
                'cancelledAt' => $currentDate,
            ]);
            $this->sendNotification($loginUser, NotificationType::COURSE_CANCELLED, $notificationData);
        }
    }
}
