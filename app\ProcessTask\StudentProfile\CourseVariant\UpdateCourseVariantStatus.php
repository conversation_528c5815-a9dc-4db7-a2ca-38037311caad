<?php

namespace App\ProcessTask\StudentProfile\CourseVariant;

use App\Exceptions\ApplicationException;
use App\Model\v2\StudentCourseExtensionHistory;
use App\Model\v2\StudentCourses;
use Closure;

class UpdateCourseVariantStatus
{
    public function handle($data, Closure $next)
    {
        $studCourseId = $data['studCourseId'];
        $newStatus = $data['newStatus'] ?? $data['courseVariantData']['course_status'] ?? null;
        $isUpdate = $data['is_update'] ?? false;

        try {
            $resData = StudentCourses::find($studCourseId);

            if ($resData) {
                $oldStatus = $resData->status;

                // Only update status if it's different or if it's a new course variant
                $shouldUpdateStatus = ! $isUpdate || ($oldStatus !== $newStatus);
                if ($shouldUpdateStatus) {
                    $resData->status = $newStatus;
                }
                $resData->updated_by = auth()->user()->id;

                // Handle additional course data updates from course variant operations
                if (isset($data['updateCourseData'])) {
                    $updateData = $data['updateCourseData'];

                    if (isset($updateData['is_course_end_date_impact'])) {
                        // Save extension history
                        $dateExtensionHistoryRecord = $this->saveExtensionHistory($resData, $updateData);
                        if ($dateExtensionHistoryRecord) {
                            $data['dateExtendHistory'] = $dateExtensionHistoryRecord;
                        }
                    }
                    if (isset($updateData['finish_date'])) {
                        $resData->finish_date = $updateData['finish_date'];
                    }
                    if (isset($updateData['total_weeks'])) {
                        $resData->total_weeks = $updateData['total_weeks'];
                    }
                }

                $isUpdate = $resData->save();
                if ($shouldUpdateStatus && galaxy_feature('galaxy_webhooks')) {
                    $eventMap = [
                        StudentCourses::STATUS_CURRENT_STUDENT => \Webhooks\Events\Student\StudentCurrentEvent::class,
                        StudentCourses::STATUS_CANCELLED => \Webhooks\Events\Student\StudentCancelledEvent::class,
                        StudentCourses::STATUS_DEFERRED => \Webhooks\Events\Student\StudentDeferredEvent::class,
                        StudentCourses::STATUS_DID_NOT_COMMENCE => \Webhooks\Events\Student\StudentDidNotCommenceEvent::class,
                        StudentCourses::STATUS_PLACED => \Webhooks\Events\Student\StudentPlacedEvent::class,
                        StudentCourses::STATUS_GRADUATED => \Webhooks\Events\Student\StudentGraduatedEvent::class,
                        StudentCourses::STATUS_FINISHED => \Webhooks\Events\Student\StudentFinishedEvent::class,
                        StudentCourses::STATUS_EXPIRED_OFFER => \Webhooks\Events\Student\StudentExpiredOfferEvent::class,
                        StudentCourses::STATUS_TRANSFERRED => \Webhooks\Events\Student\StudentTransferredEvent::class,
                        StudentCourses::STATUS_TRANSITIONED => \Webhooks\Events\Student\StudentTransitionedEvent::class,
                        StudentCourses::STATUS_COMPLETED => \Webhooks\Events\Student\StudentCompletedEvent::class,
                        StudentCourses::STATUS_WITHDRAWN => \Webhooks\Events\Student\StudentWithdrawnEvent::class,
                        StudentCourses::STATUS_AGENT_APPLIED => \Webhooks\Events\Student\StudentAgentAppliedEvent::class,
                        StudentCourses::STATUS_ENROLLED => \Webhooks\Events\Student\StudentEnrolledEvent::class,
                    ];

                    if (isset($eventMap[$newStatus])) {
                        \Webhooks\Facades\Webhook::dispatch(new $eventMap[$newStatus]($resData));
                    }
                }

                if ($isUpdate) {
                    $data['oldStatus'] = $oldStatus;
                    $data['updatedCourse'] = $resData;
                } else {
                    throw new ApplicationException('Failed to update course variant status.');
                }
            } else {
                throw new ApplicationException('Student course not found.');
            }
        } catch (\Exception $e) {
            throw new ApplicationException('Error updating course variant status: '.$e->getMessage());
        }

        return $next($data);
    }

    public function saveExtensionHistory($studentCourse, $updateData)
    {
        if (isset($updateData['finish_date']) || isset($updateData['total_weeks'])) {
            $loginUser = auth()->user();
            $extensionHistoryData = [
                'college_id' => $loginUser->college_id,
                'student_id' => $studentCourse->student_id,
                'course_id' => $studentCourse->course_id,
                'student_course_id' => $studentCourse->id,
                'start_date' => $studentCourse->start_date,
                'new_start_date' => $studentCourse->start_date,
                'finish_date' => $studentCourse->finish_date,
                'new_finish_date' => $updateData['finish_date'],
                'total_weeks' => $studentCourse->total_weeks,
                'new_total_weeks' => $updateData['total_weeks'],
                'extension_reason' => 'Update from course variant module',
                'created_by' => $loginUser->id,
                'updated_by' => $loginUser->id,
            ];

            return StudentCourseExtensionHistory::create($extensionHistoryData);
        }

        return true;
    }
}
