<?php

namespace App\Providers;

use App\Model\StudentAttendance as StudentAttendance_OLD;
use App\Model\StudentUnitEnrollment as StudentUnitEnrollment_OLD;
use App\Model\v2\Courses;
use App\Model\v2\Student;
use App\Model\v2\StudentAttendance;
use App\Model\v2\StudentCourses;
use App\Model\v2\StudentInitialPayment;
use App\Model\v2\StudentInitialPaymentDetails;
use App\Model\v2\StudentSubjectEnrolment;
use App\Model\v2\StudentUnitEnrollment;
use App\Model\v2\UnitModule;
use App\Observers\StudentsObserver;
use Dedoc\Scramble\Scramble;
use Dedoc\Scramble\Support\Generator\OpenApi;
use Dedoc\Scramble\Support\Generator\SecurityScheme;
use Domains\Customers\Models\CentralUser;
use Domains\Customers\Models\Subscription;
use Domains\Xero\Models\XeroConfig;
use Illuminate\Database\Eloquent\Relations\Relation;
use Illuminate\Notifications\DatabaseNotification;
use Illuminate\Pagination\Paginator;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Gate;
use Illuminate\Support\Facades\URL;
use Illuminate\Support\Facades\Vite;
use Illuminate\Support\ServiceProvider;
use Laravel\Cashier\Cashier;
use Livewire\Livewire;
use Notifications\Models\Notification;

class AppServiceProvider extends ServiceProvider
{
    /**
     * Bootstrap any application services.
     *
     * @return void
     */
    public function boot()
    {
        // parent::boot();
        /* if app is running in concole and in case log driver is daily we don't want to use the normal log files
        but create cli files so we won't  have any issue even though new log is created with wrong owner. */
        // if ($this->app->runningInConsole()) {
        //     config(['logging.channels.daily.path' => storage_path() . '/logs/laravel-cli-' . Carbon::now()->format('Y-m-d') . '.log']);
        // }
        Vite::useBuildDirectory('build/dist');
        // $this->app->instance(DatabaseNotification::class, new Notification());
        // Attach the observer to each model you want to track
        Student::observe(StudentsObserver::class);
        Courses::observe(StudentsObserver::class);
        UnitModule::observe(StudentsObserver::class);
        StudentCourses::observe(StudentsObserver::class);
        StudentSubjectEnrolment::observe(StudentsObserver::class);
        StudentUnitEnrollment::observe(StudentsObserver::class);
        if (! config('features.scout') && ! config('features.scout_beta_search')) {

            StudentAttendance::observe(StudentsObserver::class);
            StudentInitialPayment::observe(StudentsObserver::class);
            StudentInitialPaymentDetails::observe(StudentsObserver::class);

            StudentAttendance_OLD::observe(StudentsObserver::class);
            StudentUnitEnrollment_OLD::observe(StudentsObserver::class);
        }

        // https on production
        if (app()->environment('production')) {
            URL::forceScheme('https');
        }
        Paginator::useBootstrap();

        Cashier::useSubscriptionModel(Subscription::class);
        // Cashier::useCustomerModel(CentralUser::class);

        Relation::morphMap([
            'rsipdt' => \App\Model\v2\StudentInitialPaymentTransaction::class,
            'rsmp' => \App\Model\v2\StudentMiscellaneousPayment::class,
            'rsipd' => \App\Model\v2\StudentInitialPaymentDetails::class,
            'rsac' => \App\Model\v2\StudentAgentCommission::class,
            'rssrvp' => \App\Model\v2\StudentServicePayment::class,
            'rsscho' => \App\Model\v2\StudentScholarship::class,
            'course' => \App\Model\v2\Courses::class,
            'student' => \App\Model\v2\Student::class,
            'app_users' => \App\Users::class,
            'agent' => \App\Model\v2\Agent::class,
            'rsaatv2' => \App\Model\v2\StudentAssignedAssessmentTask::class,
            'rsaat' => \App\Model\StudentAssignedAssessmentTask::class,
            'rsc' => \App\Model\StudentCourse::class,
            'rsse' => \App\Model\StudentSubjectEnrolment::class,
            'rsvpr' => \App\Model\StudentVocationalPlacementResult::class,
            'rssev2' => \App\Model\v2\StudentSubjectEnrolment::class,
            'rtsue' => \App\Model\v2\TcsiStudentUnitEnrollment::class,
            'rsuf' => \App\Model\v2\StudentUploadFile::class,
            'rsue' => \App\Model\StudentUnitEnrollment::class,
            'rsuev2' => \App\Model\v2\StudentUnitEnrollment::class,
            'rsic' => \App\Model\v2\StudentInvoiceCredit::class,
            'rsd' => \App\Model\v2\StudentDeferCourse::class,

            //            'rsu'       => \App\Model\v2\SubjectUnits::class,
            //            'ratu'      => \App\Model\v2\AssessmentTaskUnit::class,
            //            'rcs'       => \App\Model\v2\CourseSubject::class,
            //            'rtd'       => \App\Model\v2\TimetableDetail::class,
        ]);

        // $this->app->instance(\App\Providers\FortifyServiceProvider::class, null);
        // $this->app->instance(\App\Providers\JetstreamServiceProvider::class, null);

        Scramble::extendOpenApi(function (OpenApi $openApi) {
            $openApi->secure(
                SecurityScheme::http('bearer')
            );
        });
        Gate::define('viewApiDocs', function () {
            return true;
        });

        // Register Livewire components
        if (class_exists(Livewire::class)) {
            Livewire::component('student.integration-connections', \App\Http\Livewire\Student\IntegrationConnections::class);
        }
    }

    /**
     * Register any application services.
     *
     * @return void
     */
    public function register()
    {
        Cashier::ignoreRoutes();

        require_once app_path('Helpers/utilities.php');
        require_once app_path('Helpers/view-helpers.php');

        if (galaxy_feature('sso') && class_exists(\SSO\SSOServiceProvider::class)) {
            $this->app->register(\SSO\SSOServiceProvider::class);
        }
        if (galaxy_feature('third_party_integrations') && class_exists(\Integrations\Base\IntegrationServiceProvider::class)) {
            $this->app->register(\Integrations\Base\IntegrationServiceProvider::class);
        }

        if (\galaxy_feature('moodle') && class_exists(\Domains\Moodle\LaravelMoodleServiceProvider::class)) {
            $this->app->register(\Domains\Moodle\LaravelMoodleServiceProvider::class);
        }

        if (galaxy_feature('galaxy_webhooks') && class_exists(\Webhooks\Providers\WebhookServiceProvider::class)) {
            $this->app->register(\Webhooks\Providers\WebhookServiceProvider::class);
        }

        // if(config('logging.alarm.enabled') && class_exists(\Support\LogAlarm\LogAlarmServiceProvider::class)){
        //     $this->app->register(\Support\LogAlarm\LogAlarmServiceProvider::class);
        // }
        // $this->app->register(\App\Providers\FortifyServiceProvider::class);
        // $this->app->register(\App\Providers\JetstreamServiceProvider::class);
        // $this->app->singleton('xeroAppConfig', function(){
        //     return XeroConfig::Init();
        // });
    }
}
