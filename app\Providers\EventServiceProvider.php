<?php

namespace App\Providers;

use App\Events\EnrollStudentAttendanceEvent;
use App\Events\GlobalSearchEvent;
use App\Events\NotificationEvent;
use App\Events\SendAttendanceEmails;
use App\Events\SendStudentMailEvent;
use App\Events\ShortCourseEnrolledEvent;
use App\Events\TenantCreationFailed;
use App\Events\TimeTableDashboardEvent;
use App\Listeners\EnrollStudentAttendanceListener;
use App\Listeners\GlobalSearchListener;
use App\Listeners\NotificationListener;
use App\Listeners\SendAttendanceEmailsListner;
use App\Listeners\SendStudentMailListener;
use App\Listeners\ShortCourseEnrolledListener;
use App\Listeners\TimetableDashboardListener;
use App\Listeners\Users\Clear2FACookiesForUser;
use Domains\Customers\Events\TenantWebhookReceived;
use Domains\Customers\Listeners\Tenants\SendTenantFailedEmail;
use Domains\Customers\Listeners\TenantStripeEventListener;
use Illuminate\Foundation\Support\Providers\EventServiceProvider as ServiceProvider;
use Illuminate\Support\Facades\Event;
use Laravel\Fortify\Events\TwoFactorAuthenticationDisabled;

class EventServiceProvider extends ServiceProvider
{
    /**
     * The event listener mappings for the application.
     *
     * @var array
     */
    protected $listen = [
        TenantWebhookReceived::class => [
            TenantStripeEventListener::class,
        ],
        TenantCreationFailed::class => [
            SendTenantFailedEmail::class,
        ],
        NotificationEvent::class => [
            NotificationListener::class,
        ],
        GlobalSearchEvent::class => [
            GlobalSearchListener::class,
        ],
        TimeTableDashboardEvent::class => [
            TimetableDashboardListener::class,
        ],
        SendStudentMailEvent::class => [
            SendStudentMailListener::class,
        ],
        EnrollStudentAttendanceEvent::class => [
            EnrollStudentAttendanceListener::class,
        ],
        ShortCourseEnrolledEvent::class => [
            ShortCourseEnrolledListener::class,
        ],
        'Illuminate\Auth\Events\Failed' => [
            \App\Listeners\LogFailedLoginAttempt::class,
        ],
        'Illuminate\Auth\Events\Authenticated' => [

        ],
        'Illuminate\Auth\Events\Logout' => [
            \App\Listeners\OnLogout::class,
        ],
        'Illuminate\Auth\Events\Login' => [
            \App\Listeners\OnSuccessfulLogin::class,
        ],
        \App\Events\User\NewDeviceLoginDetected::class => [
            \App\Listeners\Users\SendNewDeviceLoginEmail::class,
        ],
        'Illuminate\Auth\Events\Verified' => [
            \App\Listeners\OnEmailVerified::class,
        ],
        'Illuminate\Auth\Events\PasswordReset' => [
            /* In case we need to enable locked account on password reset in the future. */
        ],
        TwoFactorAuthenticationDisabled::class => [
            Clear2FACookiesForUser::class,
        ],
        SendAttendanceEmails::class => [
            SendAttendanceEmailsListner::class,
        ],
    ];

    /**
     * Register any events for your application.
     *
     * @return void
     */
    public function boot()
    {
        parent::boot();

        //
    }
}
