<?php

namespace App\Repositories;

use App\Helpers\Helpers;
use App\Model\CommonModel;
use App\Model\v2\Agent;
use App\Model\v2\AgentCommunication;
use App\Model\v2\AgentEmail;
use App\Model\v2\EmailTemplate;
use App\Model\v2\SetupSection;
use App\Traits\CommonTrait;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Support\Services\UploadService;

class AgentRepository
{
    use CommonTrait;

    public function rtoAgentCommunication($request, $savedEmails)
    {

        $totalAgentSelect = $request->input('agent_select');
        //        $agent_email = $request->input('agent_email');
        $loginUserId = Auth::user()->id;
        $collegeId = Auth::user()->college_id;
        $templateId = (int) $request->input('template');
        $templateData = EmailTemplate::with('attachments')->find($templateId);
        $templateAttachments = $templateData->attachments;
        $filePath = Config::get('constants.uploadFilePath.Templates');
        $templateAttachmentFiles = [];
        foreach ($templateAttachments as $templateAttachment) {
            $destinationPath = Helpers::changeRootPath($filePath, $templateId);
            $fileSavedPath = $destinationPath['view'];
            $fileFullPath = $fileSavedPath.$templateAttachment->file;
            $fileFullPath = UploadService::preview($fileFullPath);
            $templateAttachmentFiles[] = '<a href="'.$fileFullPath.'" title="'.$templateAttachment->file.'">'.$templateAttachment->file.'</a>';
        }
        // Start Default Status & Type/Category ID
        $sectionId = 3;                     // 3 for Diary-Agent as per DB:rto_setup_section_option
        $typeId = 2;                        // 6 for type_name="Type" as per DB:rto_setup_section_type
        $statusId = 3;                      // 7 for type_name="Status" as per DB:rto_setup_section_type
        $type_name = 'General';             // as per DB:rto_default_setup_section
        $status_name = 'Email Sent';        // as per DB:rto_default_setup_section
        $objSetupSection = new SetupSection;
        $defaultType = $objSetupSection->getSetupSectionId($collegeId, $sectionId, $typeId, $type_name);           // replace by 31
        $defaultStatus = $objSetupSection->getSetupSectionId($collegeId, $sectionId, $statusId, $status_name);     // replace by 34
        // End Default Status & Type/Category ID
        $savedEmails = collect($savedEmails);
        $rootFolder = config('constants.arrCollegeRootFolder');
        foreach ($totalAgentSelect as $key => $value) {
            $agentEmailSaved = $savedEmails->where('agent_id', $value)->first();
            $userId = $agentEmailSaved->crated_by;
            $fileName = $agentEmailSaved->attachment;
            $filePath = config('constants.uploadFilePath.AgentMailAttach');
            $destinationPath = Helpers::changeRootPath($filePath, $value);
            $fileSavedPath = $destinationPath['view'];
            $fileFullPath = $fileSavedPath.$fileName;
            $to = $request->input('agent_email'.$value) ?? null;
            $cc = $request->input('emial_CC') ?? null;
            $bcc = $request->input('emial_BCC') ?? null;
            $from = $request->input('email_from') ?? null;
            $subject = $request->input('subject') ?? null;
            $body = $request->input('email_body') ?? null;
            // $attachments = ($fileFullPath) ? url($fileFullPath) : null;
            $attachments = UploadService::preview($fileFullPath);
            $attachments = implode('', [...$templateAttachmentFiles, '<a href="'.$attachments.'" title="'.$fileName.'">'.$fileName.'</a>']);

            $arrAgentDetail = $this->getAgentEmailContent($value);
            if (! empty($arrAgentDetail)) {
                $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
                $destinationPath = Helpers::changeRootPath($filePath);
                $row = $arrAgentDetail[0];
                $domain = url('/');
                $basePath = $destinationPath['view'];

                $college_logo_url = $domain.str_replace('\\', '/', $basePath).$row['college_logo'];
                $college_signature_url = $domain.str_replace('\\', '/', $basePath).$row['college_signature'];
                $dean_signature_url = $domain.str_replace('\\', '/', $basePath).$row['dean_signature'];

                $college_logo = '<img src="'.$college_logo_url.'" alt="college logo" style="height: auto; width: 150px; padding-left: 49px; padding-top: 20px;"  />';
                $college_signature = '<img src="'.$college_signature_url.'" alt="College Signature" style="height: auto; width: 150px; padding-left: 49px; padding-top: 20px;"  />';
                $dean_signature = '<img src="'.$dean_signature_url.'" alt="Dean/CEO Signature" style="height: auto; width: 150px; padding-left: 49px; padding-top: 20px;"  />';

                $imgArr = [
                    'college_logo' => $college_logo,
                    'college_signature' => $college_signature,
                    'dean_signature' => $dean_signature,
                ];
                $dataArr = $this->dataBindForAgent($row, $imgArr);
                $replacedContent = $body;
                foreach ($dataArr as $key => $values) {
                    $replacedContent = str_replace($key, $values, $replacedContent);
                    $subject = str_replace($key, $values, $subject);
                }
            }
            $comment = prepareCommunicationLogContent($to, $from, $subject, $replacedContent, $attachments, ['cc' => $cc, 'bcc' => $bcc]);
            // dd($comment);
            $objRtoAgentEmail = new AgentCommunication;

            $objRtoAgentEmail->college_id = $collegeId;
            $objRtoAgentEmail->agent_id = $value;
            $objRtoAgentEmail->comment_by = $loginUserId;
            $objRtoAgentEmail->category = $defaultType;
            $objRtoAgentEmail->status = $defaultStatus;
            $objRtoAgentEmail->comment = $comment;
            $objRtoAgentEmail->created_by = $loginUserId;
            $objRtoAgentEmail->updated_by = $loginUserId;
            $objRtoAgentEmail->save();
        }
    }

    public function getAgentEmailContent($agentId)
    {
        $columnArr = [
            'ra.agency_name',
            'ra.contact_person as agent_name',
            'ra.primary_email as agent_email',
            'ra.telephone as agent_telephone',
            'clg.contact_email as college_email',
            'clg.college_name as entity_name',
            'clg.RTO_code',
            'clg.CRICOS_code',
            'clg.legal_name',
            'clg.contact_person',
            'clg.contact_phone',
            'clg.college_url',
            'clg.college_logo',
            'clg.college_signature',
            'clg.dean_name',
            'clg.dean_signature',
            'clg.timezone as college_timezone',
            'rcd.ABN as college_ABN',
        ];
        $sql = Agent::from('rto_agents as ra')
            ->leftjoin('rto_colleges as clg', 'ra.college_id', '=', 'clg.id')
            ->leftjoin('rto_college_details as rcd', 'rcd.college_id', '=', 'clg.id')
            ->where('ra.id', '=', $agentId);

        $result = $sql->select($columnArr)->get()->toarray();

        return $result;
    }

    private function dataBindForAgent($row, $imgArr)
    {
        return [

            '{CollegeLogo}' => $imgArr['college_logo'],
            '{CollegeEmail}' => $row['college_email'],
            '{CurrentDate}' => $this->getCurrentDateTimeWithTimeZone($row['college_timezone'], 'd-m-Y'), // TODO::GN-2333
            '{CollegeRtoCode}' => $row['RTO_code'],
            '{CollegeCircosCode}' => $row['CRICOS_code'],
            '{CollegeLegalName}' => $row['legal_name'],
            '{CollegeName}' => $row['entity_name'],
            '{CollegeSignature}' => $imgArr['college_signature'],
            '{DeanName}' => $row['dean_name'],
            '{DeanSignature}' => $imgArr['dean_signature'],
            '{CollegeContactPerson}' => $row['contact_person'],
            '{CollegeContactPhone}' => $row['contact_phone'],
            '{CollegeURL}' => $row['college_url'],
            '{CollegeABN}' => $row['college_ABN'],
            '{AgencyName}' => $row['agency_name'] ?? '',
            '{AgentName}' => $row['agent_name'] ?? '',
            '{AgentEmail}' => $row['agent_email'] ?? '',
            '{AgentTelephone}' => $row['agent_telephone'] ?? '',
        ];
    }

    public function getCurrentDateTimeWithTimeZone($timezone = 'UTC', $format = 'Y-m-d H:i:s')
    {
        $currentDateTime = Carbon::now($timezone);

        return $currentDateTime->format($format);
    }

    public function getAgentData($request, $countOnly = false)
    {
        $post = ($request->input()) ? $request->input() : [];

        $columns = [
            'agency_code' => 'agency_code',
            'agency_name' => 'agency_name',
            'email' => 'email',
            'mobile' => 'mobile',
            'status' => 'status',
            'director_name' => 'director_name',
            'super_agency_name' => 'super_agency_name',
            'country' => 'country',
            'provider_type' => 'provider_type',
            'legal_name' => 'legal_name',
        ];
        $query = Agent::from('rto_agents as ra')
            ->leftjoin('rto_agent_status as ras', 'ras.id', '=', 'ra.status')
            ->leftjoin('rto_agents as ra1', 'ra1.id', '=', 'ra.super_agent_id')
            ->leftjoin('rto_country as country1', 'country1.id', '=', 'ra.office_country')
            ->where('ra.college_id', Auth::user()->college_id)
            ->select('ra.*', 'ras.status_type', 'ra1.agency_name as super_agency_name', 'country1.name as country');

        $this->addSearchToQuery($query, $request, $columns);
        if (! empty($request->accountmanager)) {
            $query->where('ra.account_manager_id', $request->accountmanager);
        }
        if (! empty($request->country)) {
            $query->where('ra.office_country', $request->country);
        }
        if (! empty($request->agentstatus)) {
            $query->where('ra.status', $request->agentstatus);
        }
        if ($request->sort) {
            $query->orderBy($request->sort, $request->dir ?? 'asc');
        } else {
            $query->orderBy('id', 'desc');
        }
        $result = $query->paginate($request->take ?? 10);

        return $result;
    }

    public function rtoAgentEmailSave($request, $collegeId, $userId)
    {
        $rootFolder = Config::get('constants.arrCollegeRootFolder');

        $file = $request->file('files');
        $totalAgentSelect = json_decode($request->input('to_email'), true);
        $flag = true;
        $copyDataArr = [];
        $savedEmails = [];
        foreach ($totalAgentSelect as $agents) {
            $agentId = $agents['id'];
            $filePath = Config::get('constants.uploadFilePath.AgentMailAttach');
            $destinationPath = Helpers::changeRootPath($filePath, $agentId);
            $dataArr = [
                'college_id' => $collegeId,
                'folder_name' => $rootFolder['AgentMailAttach'],
                'sub_folder_name' => $agentId,
                'user_id' => $userId,
            ];
            $objCommonModel = new CommonModel;
            $clgMaterialParentId = $objCommonModel->getSubParentId($dataArr);

            $objRtoAgentEmail = new AgentEmail;
            $objRtoAgentEmail->college_id = $collegeId;
            $objRtoAgentEmail->agent_id = $agentId;

            if (! empty($file)) {

                if ($flag) {
                    $originalName = $file->getClientOriginalName();
                    $fileSize = $file->getSize();
                    $filename = time().$originalName;
                    $file->move($destinationPath['default'], $filename);
                    $agentDataArr = [
                        'college_id' => $collegeId,
                        'original_name' => $originalName,
                        'file_name' => $filename,
                        'size' => $fileSize,
                        'type' => 'File',
                        'parent_id' => $clgMaterialParentId,
                        'file_path' => $destinationPath['view'],
                        'user_id' => $userId,
                        'defaultPath' => $destinationPath['default'],
                    ];

                    $res = $objCommonModel->addCollegeMaterialInfo($agentDataArr);
                    if ($res) {
                        $objRtoAgentEmail->attachment = $filename;
                        $copyDataArr[] = $agentDataArr;
                        $flag = false;
                    }
                } else {
                    foreach ($copyDataArr as $row) {
                        $oldPath = $row['defaultPath'].$row['file_name'];
                        $newPath = $destinationPath['default'].$row['file_name'];
                        $objCommonModel->mycopy($oldPath, $newPath);

                        $objRtoAgentEmail->attachment = $row['file_name'];

                        $newStaffDataArr = [
                            'college_id' => $collegeId,
                            'original_name' => $row['original_name'],
                            'file_name' => $row['file_name'],
                            'size' => $row['size'],
                            'type' => 'File',
                            'parent_id' => $clgMaterialParentId,
                            'file_path' => $destinationPath['view'],
                            'user_id' => $userId,
                        ];
                        $objCommonModel->addCollegeMaterialInfo($newStaffDataArr);
                    }
                }
            }
            $agentEmail = Agent::where('id', $agentId)->pluck('primary_email')->first();
            $objRtoAgentEmail->email = $agentEmail;
            $objRtoAgentEmail->email_from = $request->input('from_email');
            $objRtoAgentEmail->emial_CC = $request->input('email_CC') ?? '';
            $objRtoAgentEmail->subject = $request->input('request_subject');
            $objRtoAgentEmail->email_body = $request->input('request_content');
            $objRtoAgentEmail->created_by = $userId;
            $objRtoAgentEmail->updated_by = $userId;
            $saved = $objRtoAgentEmail->save();
            if ($saved) {
                $savedEmails[] = $objRtoAgentEmail;
            }

            $postData = $request->all();
            // Email Send.
            $objAgentCommunicationAdd = new AgentCommunication;
            $arrAgent = $objAgentCommunicationAdd->addAgentCommunicationData($postData, true);
        }

        return $savedEmails;

    }
}
