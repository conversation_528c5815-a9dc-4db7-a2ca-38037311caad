<?php

namespace App\Repositories;

use App\Helpers\Helpers;
use App\Model\v2\CollegeDetails;
use App\Model\v2\Country;
use App\Model\v2\CreditProvidersCode;
use App\Model\v2\ElearningLink;
use App\Model\v2\StudentAttendance;
use App\Model\v2\StudentCommunicationLog;
use App\Model\v2\StudentSubjectEnrolment;
use App\Traits\CommonTrait;
use Auth;
use Carbon\Carbon;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;

class CommonRepository
{
    use CommonTrait;

    public function all()
    {
        return $this->model->all();
    }

    public function find($id)
    {
        return $this->model->find($id);
    }

    public function findWithTrashed($id)
    {
        return $this->model->withTrashed()->find($id);
    }

    public function show($id)
    {
        return $this->model->findOrFail($id);
    }

    public function findData($id, $fields = '*')
    {
        return $this->model->select($fields)->find($id)->toArray();
    }

    public function create(array $data)
    {
        return $this->model->create($data);
    }

    public function update(array $data, $id)
    {
        $record = $this->model->find($id);

        return $record->update($data);
    }

    public function whereUpdate(array $data, $whereArr)
    {
        $record = $this->model->where($whereArr);

        return $record->update($data);
    }

    public function whereInUpdate(array $data, $ids)
    {
        $record = $this->model->whereIn('id', $ids);

        return $record->update($data);
    }

    public function delete($id)
    {
        return $this->model->destroy($id);
    }

    public function whereDelete($whereArr)
    {
        return $this->model->where($whereArr)->delete();
    }

    public function getWhere($whereArr, $fields = '*')
    {
        return $this->model->where($whereArr)->select($fields)->get()->toArray();
    }

    public function getWhereRow($whereArr)
    {
        return $this->model->where($whereArr)->get();
    }

    public function getWhereFirst($whereArr, $fields = '*')
    {
        return $this->model->where($whereArr)->select($fields)->first()->toArray();
    }

    public function getWhereVal($whereArr, $field = '')
    {
        return $this->model->where($whereArr)->value($field);
    }

    public function getWhereSum($whereArr, $field)
    {
        return $this->model->where($whereArr)->sum($field);
    }

    public function getPluck($key, $value, $field, $orderType = 'DESC')
    {
        return $this->model->orderBy($field, $orderType)->pluck($key, $value)->toArray();
    }

    public function getAll($fields, $field, $orderType = 'DESC')
    {
        return $this->model->select($fields)->orderBy($field, $orderType)->get()->toArray();
    }

    public function getWhereGroupBy($whereArr, $fields = '*', $groupByColumn = 'updated_at')
    {
        return $this->model->where($whereArr)->select($fields)->groupBy($groupByColumn)->get()->toArray();
    }

    public function selectData($fields)
    {
        return $this->model->select($fields)->get()->toArray();
    }

    /* DB related */
    public function saveActivityNote($logData)
    {
        return StudentCommunicationLog::create($logData);
    }

    public function getCountryData($college_id): array
    {
        return Country::whereIn('college_id', [$college_id, 0])->orderBy('name')->get(['id', 'name', 'nationality'])->toArray();
    }

    public function selectCountryData(): array
    {
        return Country::select(['id as Id', 'name as Name'])->orderBy('name', 'asc')->get()->toArray();
    }

    public function selectNationalityData(): array
    {
        return Country::select(['id as Id', 'nationality as Name'])->get()->toArray();
    }

    public function getCollegeDetails($collegeId)
    {
        return CollegeDetails::from('rto_college_details as rcd')
            ->join('rto_colleges as rc', 'rc.id', '=', 'rcd.college_id')
            ->where(['rcd.college_id' => $collegeId])
            ->get()
            ->first();
    }

    public function getCollegeLogo($cLogoName)
    {
        $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
        $destinationPath = Helpers::changeRootPath($filePath);
        // $path = str_replace("\\", "/", $destinationPath['default']) . $cLogoName;
        $path = str_replace('\\', '/', $destinationPath['view']).$cLogoName;
        if (file_exists(public_path($path)) == true) {
            $newPath = 'data:image/png;base64,'.base64_encode(file_get_contents(public_path($path)));
        } else {
            $newPath = '';
        }

        return $newPath;
    }

    public function getStudentAttendanceFilterWiseData($request, $whereArr, $isFilter = false)
    {
        $attendanceArr = ['total_day' => 0, 'present_day' => 0, 'absent_day' => 0, 'pending_day' => 0];
        /*if($isFilter){
            $filterRes = ['last_month_present' => 0, 'last_month_absent' => 0];
            $attendanceArr = array_merge($attendanceArr, $filterRes);
        }*/
        $total = $projected = $overall = 0;

        $studentId = $whereArr['a1.student_id'];
        $subjectEnrollArr = StudentSubjectEnrolment::from('rto_student_subject_enrolment as a1')
            ->where($whereArr)
            ->groupBy('a1.batch')
            ->get(['a1.batch'])
            ->toArray();
        $batchArr = array_column($subjectEnrollArr, 'batch');

        if (count($batchArr) > 0) {
            $today = Carbon::now()->toDateString();
            $query = StudentAttendance::from('rto_student_attendance as rsa')
                ->join('rto_timetable as rt', 'rt.id', '=', 'rsa.timetable_id')
                ->leftjoin('rto_subject as rs', 'rs.id', '=', 'rt.subject_id')
                ->where('rsa.student_id', $studentId);
            if ($request->batch_id) {
                $query->where('rt.batch', $request->batch_id);
            } else {
                $query->whereIn('rt.batch', $batchArr);
            }
            if ($request->subject_id) {
                $query->where('rs.id', $request->subject_id);
            }
            // ->where('rsa.attendance_date', '<=', $today)
            $attendanceDataArr = $query->select(
                /*DB::raw("COUNT(rsa.attendance_date) as total_day"),
                DB::raw("SUM(CASE WHEN (rsa.attendance_date < '$today' AND rsa.status = 'absent') THEN 1 ELSE 0 END) as absent_day"),
                DB::raw("SUM(CASE WHEN (rsa.attendance_date < '$today' AND rsa.status = 'present') THEN 1 ELSE 0 END) as present_day"),
                DB::raw("SUM(CASE WHEN (rsa.attendance_date > '$today' AND rsa.status = 'absent') THEN 1 ELSE 0 END) as pending_day"),*/

                DB::raw('COUNT(rsa.attendance_date) as total_day'),
                DB::raw("SUM(rsa.status = 'absent' AND rsa.attendance_date < '$today') as absent_day"),
                DB::raw("SUM(rsa.status = 'present' AND rsa.attendance_date < '$today') as present_day"),
                DB::raw("SUM(rsa.status = 'absent' AND rsa.attendance_date > '$today') as pending_day")
            )
                ->get()
                ->first();

            if (isset($attendanceDataArr)) {
                $attendanceArr = [
                    'total_day' => $attendanceDataArr->total_day,
                    'absent_day' => (! empty($attendanceDataArr->absent_day)) ? $attendanceDataArr->absent_day : 0,
                    'present_day' => (! empty($attendanceDataArr->present_day)) ? $attendanceDataArr->present_day : 0,
                    'pending_day' => (! empty($attendanceDataArr->pending_day)) ? $attendanceDataArr->pending_day : 0,
                ];

                $total = $attendanceArr['total_day'];
                $present = $attendanceArr['present_day'];
                $pending = $attendanceArr['pending_day'];

                if ($total > 0) {
                    $overall = ($present > 0) ? ($present * 100 / $total) : 0;
                    $projected = ($present > 0 || $pending > 0) ? (($present + $pending) * 100 / $total) : 0;
                }
            }
        }

        $calculated_attendance = [
            'overall_attd' => (in_array($projected, [0, 100])) ? $projected : number_format($projected, 2),
            'projected_attd' => (in_array($overall, [0, 100])) ? $overall : number_format($overall, 2),
        ];

        $finalRes = [
            'course_attendance' => $attendanceArr,
            'calculated_attendance' => $calculated_attendance,
        ];

        /*if($isFilter){
            $finalRes['last_month_attendance'] = $this->getStudentAttendanceLastMonthData2($studentId, $batchArr, $total);
        }*/

        return $finalRes;
    }

    // Retrieve and return the course attendance based on the provided course detail
    public function getStudentAttendanceData($whereArr, $isFilter = false)
    {
        $attendanceArr = ['total_day' => 0, 'present_day' => 0, 'absent_day' => 0, 'pending_day' => 0];
        /*if($isFilter){
            $filterRes = ['last_month_present' => 0, 'last_month_absent' => 0];
            $attendanceArr = array_merge($attendanceArr, $filterRes);
        }*/
        $total = $projected = $overall = 0;

        $studentId = $whereArr['a1.student_id'];
        $subjectEnrollArr = StudentSubjectEnrolment::from('rto_student_subject_enrolment as a1')
            ->where($whereArr)
            ->groupBy('a1.batch')
            ->get(['a1.batch'])
            ->toArray();
        $batchArr = array_column($subjectEnrollArr, 'batch');

        if (count($batchArr) > 0) {
            $today = Carbon::now()->toDateString();
            $attendanceDataArr = StudentAttendance::from('rto_student_attendance as rsa')
                ->join('rto_timetable as rt', 'rt.id', '=', 'rsa.timetable_id')
                ->whereIn('rt.batch', $batchArr)
                ->where('rsa.student_id', $studentId)
                // ->where('rsa.attendance_date', '<=', $today)
                ->select(
                    /*DB::raw("COUNT(rsa.attendance_date) as total_day"),
                    DB::raw("SUM(CASE WHEN (rsa.attendance_date < '$today' AND rsa.status = 'absent') THEN 1 ELSE 0 END) as absent_day"),
                    DB::raw("SUM(CASE WHEN (rsa.attendance_date < '$today' AND rsa.status = 'present') THEN 1 ELSE 0 END) as present_day"),
                    DB::raw("SUM(CASE WHEN (rsa.attendance_date > '$today' AND rsa.status = 'absent') THEN 1 ELSE 0 END) as pending_day"),*/

                    DB::raw('COUNT(rsa.attendance_date) as total_day'),
                    DB::raw("SUM(rsa.status = 'absent' AND rsa.attendance_date < '$today') as absent_day"),
                    DB::raw("SUM(rsa.status = 'present' AND rsa.attendance_date < '$today') as present_day"),
                    DB::raw("SUM(rsa.status = 'absent' AND rsa.attendance_date > '$today') as pending_day")
                )
                ->get()
                ->first();

            if (isset($attendanceDataArr)) {
                $attendanceArr = [
                    'total_day' => $attendanceDataArr->total_day,
                    'absent_day' => (! empty($attendanceDataArr->absent_day)) ? $attendanceDataArr->absent_day : 0,
                    'present_day' => (! empty($attendanceDataArr->present_day)) ? $attendanceDataArr->present_day : 0,
                    'pending_day' => (! empty($attendanceDataArr->pending_day)) ? $attendanceDataArr->pending_day : 0,
                ];

                $total = $attendanceArr['total_day'];
                $present = $attendanceArr['present_day'];
                $pending = $attendanceArr['pending_day'];

                if ($total > 0) {
                    $overall = ($present > 0) ? ($present * 100 / $total) : 0;
                    $projected = ($present > 0 || $pending > 0) ? (($present + $pending) * 100 / $total) : 0;
                }
            }
        }

        $calculated_attendance = [
            'overall_attd' => (in_array($projected, [0, 100])) ? $projected : number_format($projected, 2),
            'projected_attd' => (in_array($overall, [0, 100])) ? $overall : number_format($overall, 2),
        ];

        $finalRes = [
            'course_attendance' => $attendanceArr,
            'calculated_attendance' => $calculated_attendance,
        ];

        /*if($isFilter){
            $finalRes['last_month_attendance'] = $this->getStudentAttendanceLastMonthData2($studentId, $batchArr, $total);
        }*/

        return $finalRes;
    }

    public function getStudentAttendanceLastMonthData($studentId, $batchArr, $startDate)
    {
        $resArr = ['last_month_present' => 0, 'last_month_absent' => 0];

        if (count($batchArr) > 0) {
            // $today = date("Y-m-d");
            // $firstDayOfCurrentMonth = date("Y-m-01", strtotime($today));
            $firstDayOfCurrentMonth = date('Y-m-01', strtotime($startDate.' +7 day'));
            $lastDayOfLastMonth = date('Y-m-d', strtotime($firstDayOfCurrentMonth.' -1 day'));
            $firstDayOfLastMonth = date('Y-m-01', strtotime($lastDayOfLastMonth));
            // echo "First day of last month: $firstDayOfLastMonth<hr/> Last day of last month: $lastDayOfLastMonth";

            $lastMonthAttendanceDataArr = StudentAttendance::from('rto_student_attendance as rsa')
                ->join('rto_timetable as rt', 'rt.id', '=', 'rsa.timetable_id')
                ->where('rsa.student_id', $studentId)
                ->whereIn('rt.batch', $batchArr)
                ->whereBetween('rsa.attendance_date', [$firstDayOfLastMonth, $lastDayOfLastMonth])
                ->select([
                    DB::raw('COUNT(rsa.attendance_date) as total'),
                    DB::raw("SUM(CASE WHEN rsa.status = 'absent' THEN 1 ELSE 0 END) as absent"),
                    DB::raw("SUM(CASE WHEN rsa.status = 'present' THEN 1 ELSE 0 END) as present"),
                ])
                ->get()
                ->first();

            if (isset($lastMonthAttendanceDataArr)) {
                $total = (! empty($lastMonthAttendanceDataArr->total)) ? $lastMonthAttendanceDataArr->total : 0;
                $absent = (! empty($lastMonthAttendanceDataArr->absent)) ? $lastMonthAttendanceDataArr->absent : 0;
                $present = (! empty($lastMonthAttendanceDataArr->present)) ? $lastMonthAttendanceDataArr->present : 0;

                if ($total > 0) {
                    $lastMonthPresent = ($present > 0) ? (($present * 100) / $total) : 0;
                    $lastMonthAbsent = ($absent > 0) ? (($absent * 100) / $total) : 0;
                    $resArr['last_month_present'] = number_format($lastMonthPresent);
                    $resArr['last_month_absent'] = number_format($lastMonthAbsent);
                    // $resArr['last_month_present'] = (in_array($lastMonthPresent, [0, 100])) ? $lastMonthPresent : number_format($lastMonthPresent, 1);
                    // $resArr['last_month_absent'] = (in_array($lastMonthAbsent, [0, 100])) ? $lastMonthAbsent : number_format($lastMonthAbsent, 1);
                }
            }
        }

        return $resArr;
    }

    public function getCreditProvidersCode($request, $collegeId, $countOnly = false)
    {
        $post = ($request->input()) ? $request->input() : [];

        $columns = [
            'provider_code' => 'provider_code',
            'abbr' => 'abbr',
            'provider_type' => 'provider_type',
            'legal_name' => 'legal_name',
        ];

        $query = CreditProvidersCode::where('college_id', $collegeId);

        $this->addSearchToQuery($query, $request, $columns);

        if ($request->sort) {
            $query->orderBy($request->sort, $request->dir ?? 'asc');
        } else {
            $query->orderBy('id', 'desc');
        }
        $result = $query->paginate($request->take ?? 10);

        return $result;
    }

    public function addSearchToQuery($query, $request, $columns)
    {
        if ($request->search) {
            $query->where(function ($childQuery) use ($request, $columns) {
                foreach ($columns as $column) {
                    if (! is_string($column)) {
                        continue;
                    }
                    $columnFullName = (strpos($column, ' as ') !== false) ? (explode(' as ', $column)[0]) : $column;
                    $childQuery->orWhereRaw("$columnFullName like ?", "%$request->search%");
                }
            });
        }
    }

    public function getElearningLinkData($request, $countOnly = false)
    {

        $college_id = Auth::user()->college_id;
        $post = ($request->input()) ? $request->input() : [];

        $columnArr = [
            'rel.id',
            'ru.name as created_by',
            'rel.title',
            'rel.link',
            'rel.created_at as created_date',
        ];

        $columns = [
            'id' => 'rel.id',
            'created_by' => 'ru.name as created_by',
            'title' => 'rel.title',
            'link' => 'rel.link',
            'created_at' => 'rel.created_at as created_date',
        ];

        $query = ElearningLink::from('rto_elearning_link as rel')
            ->leftjoin('rto_users as ru', 'rel.created_by', '=', 'ru.id')
            ->where('rel.college_id', '=', $college_id)
        // ->orderBy('rel.id', 'desc')
            ->select($columnArr);
        $this->addSearchToQuery($query, $request, $columns);

        if ($request->sort) {
            $query->orderBy($request->sort, $request->dir ?? 'asc');
        } else {
            $query->orderBy('id', 'desc');
        }
        $result = $query->paginate($request->take ?? 10);

        return $result;
    }
}
