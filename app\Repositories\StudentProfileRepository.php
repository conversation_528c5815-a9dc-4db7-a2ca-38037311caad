<?php

namespace App\Repositories;

use App\Helpers\Helpers;
use App\Model\ResultGrade;
use App\Model\Semester;
use App\Model\v2\CollegeMaterials;
use App\Model\v2\Courses;
use App\Model\v2\CourseSubject;
use App\Model\v2\InvoiceNumber;
use App\Model\v2\PaymentMode;
use App\Model\v2\ResceiptNumber;
use App\Model\v2\Student;
use App\Model\v2\StudentAgentCommission;
use App\Model\v2\StudentAttendance;
use App\Model\v2\StudentCommunicationLog;
use App\Model\v2\StudentCourses;
use App\Model\v2\StudentDetails;
use App\Model\v2\StudentInitialPayment;
use App\Model\v2\StudentInitialPaymentDetails;
use App\Model\v2\StudentMiscellaneousPayment;
use App\Model\v2\StudentOfferDocuments;
use App\Model\v2\StudentOffers;
use App\Model\v2\StudentScholarship;
use App\Model\v2\StudentServiceInformation;
use App\Model\v2\StudentServicePayment;
use App\Model\v2\StudentSubjectEnrolment;
use App\Model\v2\StudentUploadFile;
use App\Model\v2\SubjectUnits;
use App\Model\v2\TaskNew;
use App\Model\v2\TcsiStudentCreditOffer;
use App\Model\v2\Timetable;
use App\Model\v2\TimetableDetail;
use App\Model\v2\Users;
use App\Traits\CommonTrait;
use App\Traits\CourseSubjectTrait;
use App\Traits\DocumentTrait;
use App\Traits\ResponseTrait;
use Domains\Xero\Facades\Xero;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Integrations\Base\Flags\SyncStatus;
use Support\Models\Activity;
use Support\Services\UploadService;

class StudentProfileRepository extends CommonRepository
{
    use CommonTrait;
    use CourseSubjectTrait;
    use DocumentTrait;
    use ResponseTrait;

    protected $model;

    public function __construct(Model $model)
    {
        $this->model = $model;
    }

    public function Where($whereArr)
    {
        return $this->model->where($whereArr);
    }

    public function whereIn($column, $dataArr)
    {
        return $this->model->whereIn($column, $dataArr);
    }

    public function getStudentDetail($studentId, $arrRoleType)
    {
        $sqlDateFormat = Helpers::toMysqlDateFormat();

        $studentRoleType = array_search('Student', $arrRoleType);
        $result = Student::from('rto_students as rs')
            ->join('rto_colleges', 'rto_colleges.id', '=', 'rs.college_id')
            ->join('rto_student_details as rsd', 'rsd.student_id', '=', 'rs.id')
            ->leftjoin('rto_users', function ($join) use ($studentRoleType) {
                $join->on('rto_users.username', '=', 'rs.generated_stud_id');
                $join->on('rto_users.role_id', '=', DB::raw($studentRoleType));
            })
            ->leftjoin('rto_student_courses as rsc', 'rs.id', '=', 'rsc.student_id')
            ->leftjoin('rto_agents', 'rto_agents.id', '=', 'rsc.agent_id')
            ->leftjoin('rto_country as sc', 'sc.id', '=', 'rs.current_country')
            ->leftjoin('rto_country as ac', 'ac.id', '=', 'rto_agents.office_country')
            // ->leftjoin('rto_setup_section as rss1', 'rss1.id', '=', 'rs.visa_status')
            // ->leftjoin('rto_setup_section as rss2', 'rss2.id', '=', 'rsd.EPL_test_name')
            ->where('rs.id', '=', $studentId)
            ->select(
                'rs.*',
                DB::raw("DATE_FORMAT(rs.DOB, '$sqlDateFormat') as DOB"),
                'rto_colleges.college_logo',
                'rto_users.username',
                'rsd.emergency_contact_person as emergency_name',
                'rsd.emergency_phone',
                'rsd.picup_fee',
                'rsd.service_fee',
                'rsd.OSHC_fee',
                'rsd.account_manager_id',
                'rsd.EPL_overall_score',
                'rto_agents.id as agent_id',
                'rto_agents.agency_name',
                'rto_agents.primary_email as agency_email',
                'rto_agents.office_address as agent_address',
                'rto_agents.office_city as agent_city',
                'rto_agents.office_state as agent_state',
                'rto_agents.office_postcode as agent_postcode',
                'ac.name as agent_country',
                'sc.nationality',
                'sc.nationality as stud_nationality',
                'sc.name as country_name',
                'rto_users.last_login',
                'rs.status as galaxy_status',
                'rsd.emergency_contact_person',
                'rsd.emergency_relationship',
                'rsd.emergency_address',
                'rsd.emergency_phone',
                'rsd.emergency_email',
                // 'rss1.value as visaStatus',
                // 'rss2.value as EPL_test_name',
                DB::raw("CONCAT_WS(', ', rs.current_street_no, rs.current_street_name, rs.current_city, rs.current_state, rs.current_postcode, sc.name) as address")
            )
            ->get();

        if (count($result)) {
            $result[0]['profile_pic'] = $this->getStudentProfilePicPath($studentId, $result[0]['profile_picture']);
            $result[0]['profile_pic_original'] = $this->getStudentProfilePicPath($studentId, $result[0]['profile_picture'], 'original');
        }

        return $result;
    }

    public function getStudentProfileCourses($studentId, $collegeId, $perPage = '')
    {
        $sql = StudentCourses::leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->leftjoin('rto_agents', 'rto_agents.id', '=', 'rto_student_courses.agent_id')
            ->leftjoin('rto_staff_and_teacher as rstp', 'rstp.id', '=', 'rto_student_courses.palacement_manager_id')
            ->leftjoin('rto_staff_and_teacher as rstc', 'rstc.id', '=', 'rto_student_courses.course_manager_id')
            ->leftjoin('rto_course_template as template', 'template.id', '=', 'rto_student_courses.course_template')
            ->leftjoin('rto_campus', 'rto_campus.id', '=', 'rto_student_courses.campus_id')
            ->where('rto_student_courses.student_id', '=', $studentId)
            // ->where('rto_student_courses.offer_status', '=', 'Enrolled') //19-10-2020 GD-820
            ->where('rto_courses.college_id', '=', $collegeId)
            // ->orderBy('rto_student_courses.finish_date', 'asc')
            ->orderBy(DB::raw('( CASE WHEN rto_student_courses.status = "Current Student" THEN "a1" WHEN rto_student_courses.status = "Enrolled" THEN "a2" ELSE rto_student_courses.status END)'), 'ASC') // TODO::GNG-4058
            ->select(
                'rto_student_courses.id as student_course_id',
                'rto_student_courses.student_id as student_id',
                'rto_student_courses.*',
                'rto_student_courses.offer_id',
                //                        'rto_student_courses.intake_year',
                //                        'rto_student_courses.start_date',
                //                        'rto_student_courses.total_weeks',
                //                        'rto_student_courses.mode_of_delivery',
                //                        'rto_student_courses.finish_date',
                //                        'rto_student_courses.census_date1',
                //                        'rto_student_courses.course_id',
                'rto_student_courses.status',
                'rto_courses.course_duration',
                //                        'rto_student_courses.course_upfront_fee',
                'rto_student_courses.course_fee',
                'rto_student_courses.coe_name',
                'template.template_name',
                'rto_courses.id as course_id',
                'rstp.first_name as placement_manager_name',
                'rstc.first_name as course_manager_name',
                'rto_courses.course_code',
                'rto_courses.course_name',
                'rto_courses.created_at',
                'rto_courses.AVETMISS_Report',
                'rto_campus.name as campus_name',
                'rto_agents.agency_name'
            );

        if (! empty($perPage)) {
            $StudentCourse = $sql->paginate($perPage);
        } else {
            $StudentCourse = $sql->get();
        }

        for ($i = 0; $i < count($StudentCourse); $i++) {
            $rslt = $this->getWeekDifferent($StudentCourse[$i]['start_date'], $StudentCourse[$i]['finish_date']);
            $StudentCourse[$i]['week_out_of_week'] = $rslt['weekDiff'];
            $StudentCourse[$i]['color'] = Helpers::getStudCourseColorCode($StudentCourse[$i]['status']);
        }

        return $StudentCourse;
    }

    public function getWeekDifferent($datefrom, $dateto)
    {

        $diff = strtotime($dateto, 0) - strtotime($datefrom, 0);
        $weekDiff = floor($diff / 604800);
        $ddate = date('Y-m-d');
        $currentDiff = date('W', strtotime($ddate));

        if ($dateto > $ddate) {
            $total_days = ((strtotime($ddate) - strtotime($datefrom)) / 86400);
            $total_week = $total_days / 7;
            $cure_week = ceil($total_week);
        } else {
            $total_days = ((strtotime($dateto) - strtotime($datefrom)) / 86400);
            $total_week = $total_days / 7;
            $cure_week = floor($total_week);
        }
        $arrDate = [
            'MonthCurrentWeek' => $currentDiff,
            'totalWeek' => $weekDiff,
            'currentweek' => $cure_week,
            'weekDiff' => $cure_week.' of '.$weekDiff,
        ];

        return $arrDate;
    }

    public function studentResultData($request, $isMoodleConnect = false)
    {
        $collegeId = Auth::user()->college_id;
        $studentId = $request->student_id;
        $studentCourseId = $request->student_course_id;
        $semesterId = $request->semester_id;
        $termId = $request->term_id;
        $finalOutcomeArr = Config::get('constants.arrSelectFinalOutcome');
        $finalOutcomeArr[''] = 'N/A';
        $finalOutcomeArr[0] = 'N/A';
        $post = ($request->input()) ? $request->input() : [];

        $isHigherEd = StudentCourses::checkCourseIsHigherEd($studentCourseId);

        $arrSelectFinalOutcome = (new ResultGrade)->getGradeResultList($collegeId);

        $columnArr = [
            // DB::raw("CONCAT('', rto_subject.subject_code, ':',rto_subject.subject_name) as subject_name"),
            // 'rsse.activity_start_date as course_activity_date',
            // 'rsse.activity_finish_date as actual_end_date',
            'rsse.final_outcome as final_outcome',
            'rsse.mark_outcome',
            'rsse.unit_id',
            'rsse.is_result_lock',
            'rsu.unit_name',
            'rsu.unit_code',
            'rsse.grade',
            'rsse.final_outcome as compentancy',
            'rsse.activity_start_date as start_date',
            'rsse.activity_finish_date as finish_date',
            'rsse.batch as batch',
            'rsse.marks',
            'rsse.id',
        ];

        $query = StudentSubjectEnrolment::from('rto_student_subject_enrolment as rsse')
            // ->leftjoin('rto_semester', 'rto_semester.id', '=', 'rsse.semester_id')
            // ->leftjoin('rto_subject', 'rto_subject.id', '=', 'rsse.subject_id')
            ->leftjoin('rto_subject_unit as rsu', 'rsu.id', '=', 'rsse.unit_id')
            ->leftjoin('rto_student_courses', 'rto_student_courses.course_id', '=', 'rsse.course_id')
            // ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rsse.course_id')
            // ->leftjoin('rto_campus', 'rto_campus.id', '=', 'rto_student_courses.campus_id')
            // ->leftjoin('rto_timetable', function($join)
            // {
            //     $join->on('rto_timetable.subject_id', '=', 'rsse.subject_id');
            //     $join->on('rto_timetable.batch','=', 'rsse.batch');
            // })
            ->withStudentCourseId($studentCourseId)
            ->where([
                'rsse.college_id' => $collegeId,
                'rsse.student_id' => $studentId,
                'rto_student_courses.student_id' => $studentId,
                'rto_student_courses.id' => $studentCourseId,
                'rsse.semester_id' => $semesterId,
                'rsse.term' => $termId,
            ])
            ->select($columnArr)
            ->GroupBy('rsse.id');

        $this->gridDataSorting($query, $post);

        $result = $this->gridDataPaginationV2($query, $post);

        foreach ($result['data'] as $k => $row) {
            $result['data'][$k]['batch'] = ($row['batch'] != null) ? $row['batch'] : 'No Batch';
            if ($isHigherEd) {
                $finalOutcomeVal = isset($arrSelectFinalOutcome[$row['mark_outcome']]) ? $arrSelectFinalOutcome[$row['mark_outcome']] : '-';
            } else {
                $finalOutcomeVal = $row['final_outcome'];
            }
            $result['data'][$k]['final_outcome'] = $finalOutcomeVal;
            // $result['data'][$k]['final_outcome'] = ($isHigherEd) ? $arrSelectFinalOutcome[$row['mark_outcome']] : $row['final_outcome'];
            $result['data'][$k]['marks'] = ($isHigherEd) ? $row['marks'] : 'N/A';
            // $result[$k]['final_outcome'] = ($isHigherEd)?$row['mark_outcome']:$row['final_outcome'];
            $result['data'][$k]['is_higher_ed'] = $isHigherEd;
            $result['data'][$k]['moodleData'] = $this->getMoodleDataForUnit($row, $isMoodleConnect);
        }

        return $result;
    }

    public function getCurrentCourseSummary($request)
    {

        $collegeId = Auth::user()->college_id;
        $studentId = $request->student_id;
        $studentCourseId = $request->student_course_id;
        $getResultCalculationMethod = Config::get('constants.arrResultsCalculationMethod');

        $currentCourseSummary = StudentSubjectEnrolment::from('rto_student_subject_enrolment as rsse')
            ->leftjoin('rto_semester', 'rto_semester.id', '=', 'rsse.semester_id')
            ->leftjoin('rto_student_courses', 'rto_student_courses.course_id', '=', 'rsse.course_id')
            ->withStudentCourseId($studentCourseId)
            ->where([
                'rsse.college_id' => $collegeId,
                'rsse.student_id' => $studentId,
                'rto_student_courses.id' => $studentCourseId,
            ])
            ->select([
                DB::raw("
                    IF(
                        rto_semester.semester_name IS NOT NULL,
                        CONCAT(rto_semester.semester_name, ' Term ', rsse.term, ' '),
                        'Without Semester'
                    ) as semester_term
                "),
                'rsse.semester_id',
                'rsse.term',
                'rto_student_courses.id as student_course_id',
                'rsse.id',
            ])
            ->groupBy('semester_term')
            ->orderByDesc('rto_semester.id')
            ->orderByDesc('rsse.term')
            ->get()->toArray();
        // dd($currentCourseSummary);

        $studentDetails = StudentCourses::from('rto_student_courses as rsc')
            ->join('rto_students as rs', 'rs.id', '=', 'rsc.student_id')
            ->join('rto_campus', 'rto_campus.id', '=', 'rsc.campus_id')
            ->leftjoin('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->where([
                'rsc.student_id' => $studentId,
                'rsc.id' => $studentCourseId,
            ])
            ->select([
                'rc.results_calculation_methods',
                DB::raw("CONCAT(COALESCE(rs.first_name, ''), ' ', COALESCE(rs.family_name, '')) as full_name"),
                'rs.generated_stud_id',
                'rto_campus.name as campus_name',
                'rc.cricos_code',
                'rc.course_code',
                'rc.course_name',
                'rsc.*',
                'rs.profile_picture',
            ])
            ->get();

        //        $studentDetails->toArray()[0]['profile_pic'] = "";
        //            "profile_pic"   => $this->getStudentProfilePicPath($studRow['id'], $studRow['profile_picture'], 'small'),
        $studentDetails[0]->start_date_format = Helpers::convertDateToReadableFormat($studentDetails[0]->start_date);   // date('d-m-Y', strtotime($studentDetails[0]->start_date));
        $studentDetails[0]->finish_date_format = Helpers::convertDateToReadableFormat($studentDetails[0]->finish_date); // date('d-m-Y', strtotime($studentDetails[0]->finish_date));
        $data = [
            'currentCourseSummary' => $currentCourseSummary,
            'studentDetails' => $this->convertToUTF_1($studentDetails->toArray()),
            'studentProfile' => $this->getStudentProfilePicPath($studentDetails[0]->student_id, $studentDetails[0]->profile_picture, 'small'),
            'getResultCalculationMethod' => $getResultCalculationMethod[$studentDetails[0]->results_calculation_methods],
        ];

        return $data;
    }

    public function getSubjectsForEnroll($post)
    {
        $collegeId = $post['college_id'];
        $courseId = $this->getCourseId($post['student_course_id']);

        return CourseSubject::from('rto_course_subject as rcs')
            ->join('rto_subject as rs', 'rs.id', '=', 'rcs.subject_id')
            ->where([
                'rcs.college_id' => $collegeId,
                'rcs.course_id' => $courseId,
            ])
            ->select([
                'rs.id as value',
                DB::raw("CONCAT(rs.subject_code,' : ',rs.subject_name) as text"),
            ])
            ->groupBy('rs.subject_name')
            ->get()
            ->toArray();
    }

    public function getSubjectUnitForEnroll($post)
    {
        return $this->getSubjectUnitForEnrollTrait($post);
    }

    public function studentUnitData($request, $isMoodleConnect = false)
    {

        $collegeId = Auth::user()->college_id;
        $studentId = $request->student_id;
        $courseId = $this->getCourseId($request->student_course_id);
        $post = ($request->input()) ? $request->input() : [];

        // TODO::GNG-2481
        // $isHigherEd = StudentCourses::checkCourseIsHigherEd($request->student_course_id);
        // $isHigherEd = ($request->is_higher_ed) ? true : false;
        $isHigherEd = ($request->is_higher_ed == 'true') ? true : false;
        if ($isHigherEd) {
            $markOutcomeArr = (new ResultGrade)->getGradeResultList($collegeId);
        }

        $columnArr = [
            'a1.unit_id',
            'a1.final_outcome',
            'a1.mark_outcome',
            'rsu.unit_code',
            'rsue.schedule_hours',
            'rsu.unit_name',
            'a1.batch',
            'a1.id',
        ];

        $query = StudentSubjectEnrolment::from('rto_student_subject_enrolment as a1')
            ->join('rto_student_unit_enrollment as rsue', 'rsue.student_subject_enrollment_id', '=', 'a1.id')
            ->leftjoin('rto_subject_unit as rsu', 'rsu.id', '=', 'rsue.unit_id')
            ->withStudentCourseId($request->student_course_id)
            ->where([
                'a1.college_id' => $collegeId,
                'a1.student_id' => $studentId,
                'a1.course_id' => $courseId,
            ])
            ->select($columnArr);

        $this->gridDataSorting($query, $post);

        $result = $this->gridDataPaginationV2($query, $post);

        foreach ($result['data'] as $k => $row) {

            $result['data'][$k]['batch'] = (! empty($row['batch'])) ? $row['batch'] : 'No Batch';

            if ($isHigherEd) {
                $finalOutcomeVal = isset($markOutcomeArr[$row['mark_outcome']]) ? $markOutcomeArr[$row['mark_outcome']] : '-';
            } else {
                $finalOutcomeVal = $row['final_outcome'];
            }
            // $result['data'][$k]['competency'] = ($isHigherEd) ? $markOutcomeArr[$row['mark_outcome']] : $row['final_outcome'];
            $result['data'][$k]['competency'] = $finalOutcomeVal;
            $result['data'][$k]['moodleData'] = $this->getMoodleDataForUnit($row, $isMoodleConnect);
        }

        return $result;
    }

    public function getMoodleDataForUnit($data, $isMoodleConnect = false)
    {
        if ($isMoodleConnect) {
            // if($data['unit_id'] == 199){
            // dd($data);
            // echo "<pre>"; print_r($data);

            $enrollment = StudentSubjectEnrolment::find($data['id']);
            // $enrollment = $enrollment->fresh();
            // dd($enrollment->subjectUnitRevised()->first()->getMoodleId());

            // $syncUnitId = $enrollment->subjectUnitRevised()->where('course_id', $enrollment->course_id)->first()->getMoodleId();

            $syncUnitId = optional(
                $enrollment->subjectUnitRevised()->where('course_id', $enrollment->course_id)->first()
            )->getMoodleId();

            if (! $syncUnitId) {
                return false;
            }

            // $enrollSyncStatus = $this->checkEnrollDataSyncWithMoodle($enrollment);
            $enrollSyncAt = '';
            $enrollSyncData = $enrollment->getMeta(StudentSubjectEnrolment::MOODLE_META_KEY);
            if ($enrollSyncData && $enrollSyncData['sync_status'] == SyncStatus::STATUS_SYNCED) {
                $enrollSyncAt = $enrollSyncData['synced_at'] ? \Carbon\Carbon::parse($enrollSyncData['synced_at'])->diffForHumans() : true;
                // return true;
            }

            $syncedSubjectUnit = SubjectUnits::havingMoodleId($syncUnitId)->first();
            // dd($syncedSubjectUnit->moodleItem);
            if ($syncedSubjectUnit) {
                $moodleItem = $syncedSubjectUnit->moodleItem;
                $prefix = 'unit';
                $failed = (int) ($moodleItem && ! empty($moodleItem->sync_failed_at));
                $syncStatus = $failed == 1 ? 'Sync Fail' : ($moodleItem && $moodleItem->synced_at ? (! empty($enrollSyncAt) ? 'Synced' : 'Only Unit Synced') : 'Not Sync');
                // $synced = (int) ($moodleItem && empty($moodleItem->sync_failed_at) && !empty($moodleItem->synced_at));

                return [
                    "{$prefix}_moodle_sync_status" => $syncStatus,
                    "{$prefix}_syncable_id" => $moodleItem && $moodleItem->syncable_id ? $moodleItem->syncable_id : null,
                    "{$prefix}_sync_item_id" => $moodleItem && $moodleItem->sync_item_id ? $moodleItem->sync_item_id : null,
                    "{$prefix}_moodle_sync_id" => data_get($moodleItem, 'data.item.id') ?? null,
                    "{$prefix}_moodle_synced_at" => ! empty($enrollSyncAt) ? $enrollSyncAt : ($moodleItem && $moodleItem->synced_at ? \Carbon\Carbon::parse($moodleItem->synced_at)->diffForHumans() : ''),
                    "{$prefix}_moodle_failed_at" => $moodleItem && $moodleItem->sync_failed_at ? \Carbon\Carbon::parse($moodleItem->sync_failed_at)->diffForHumans() : '',
                    "{$prefix}_moodle_failed_message" => $failed == 1 ? ($moodleItem->sync_failed_message ?? 'Something went wrong') : '',
                ];
            }
            // }
        }

        return false;
    }

    private function checkEnrollDataSyncWithMoodle($enrollment)
    {
        $enrollSyncData = $enrollment->getMeta(StudentSubjectEnrolment::MOODLE_META_KEY);
        if ($enrollSyncData && $enrollSyncData['sync_status'] == SyncStatus::STATUS_SYNCED) {
            return $enrollSyncData['synced_at'] ? \Carbon\Carbon::parse($enrollSyncData['synced_at'])->diffForHumans() : true;
            // return true;
        }

        return false;
    }

    public function getAttendanceOverviewData($request, $isFilter = false)
    {
        $sqlDateFormat = Helpers::toMysqlDateFormat();
        $courseData = StudentCourses::from('rto_student_courses as rsc')
            ->join('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->join('rto_students as rs', 'rs.id', '=', 'rsc.student_id')
            ->where('rsc.id', $request->student_course_id)
            ->get([
                'rsc.id',
                'rsc.course_id',
                'rc.course_name',
                'rsc.start_date',
                DB::raw("CONCAT(DATE_FORMAT(rsc.start_date, '$sqlDateFormat'), ' To ', DATE_FORMAT(rsc.finish_date, '$sqlDateFormat')) AS course_duration"),
                DB::raw('(CASE WHEN (rsc.finish_date > now() AND rsc.start_date > now()) THEN 0 WHEN rsc.finish_date > now() THEN '.dbRawL10('DATEDIFF(now(), rsc.start_date)').' ELSE '.dbRawL10('DATEDIFF(rsc.finish_date, rsc.start_date)').' END) as days'),
                DB::raw('DATEDIFF(rsc.finish_date, rsc.start_date) as diff_days'),
                DB::raw("CONCAT(rs.first_name,' ',rs.family_name) as student_name"),
            ])
            ->first()
            ->toArray();

        $whereArr = [
            'a1.college_id' => $request->college_id,
            'a1.student_id' => $request->student_id,
            'a1.course_id' => $courseData['course_id'],
        ];

        $attendanceResult = $this->getStudentAttendanceFilterWiseData($request, $whereArr, $isFilter);

        /* hotfix */
        // $attendanceData = call_user_func_array('array_merge', $attendanceResult);
        $attendanceData = call_user_func_array('array_merge', array_values($attendanceResult));
        $result = array_merge($courseData, $attendanceData);

        $result['warning_count'] = StudentCommunicationLog::where([
            'activity_log' => 'email',
            'log_type' => 'warning',
            'student_id' => $request->student_id,
            'student_course_id' => $request->student_course_id,
        ])->count();

        return $result;
    }

    public function getAttendanceGridData($request, $countOnly = false)
    {
        // dd($request->all());
        $collegeId = $request->college_id;
        $studentId = $request->student_id;
        $studCourseId = $request->student_course_id;
        $post = ($request->input()) ? $request->input() : [];

        $studCourseData = StudentCourses::find($studCourseId);
        $courseTypeId = $studCourseData->course_type_id;
        $courseId = $studCourseData->course_id;
        $batchArrList = $this->getBatchListForAttendance($collegeId, $studentId, $courseId);

        $columnArr = [
            'rsa.id',
            'rsa.status',
            'rsa.total_hours',
            'rsa.attendance_date',
            'sem.semester_name',
            'rs.subject_code',
            'rs.subject_name',
        ];

        $columns = [
            'attendance_date' => DB::raw("DATE_FORMAT(rsa.attendance_date, '%d %b %Y')"),
            'status' => 'rsa.status',
            'subject_code' => 'rs.subject_code',
            'subject_name' => 'rs.subject_name',
            'semester_name' => 'sem.semester_name',
        ];

        $query = StudentAttendance::from('rto_student_attendance as rsa')
            ->leftjoin('rto_timetable as rt', 'rt.id', '=', 'rsa.timetable_id')
            ->leftjoin('rto_subject as rs', 'rs.id', '=', 'rt.subject_id')
            ->leftjoin('rto_semester as sem', 'sem.id', '=', 'rt.semester_id')
            ->where(['rt.college_id' => $collegeId, 'rsa.student_id' => $studentId]);
        // ->whereIn('rt.batch', $batchArrList);
        if ($request->batch_id) {
            $query->where('rt.batch', $request->batch_id);
        } else {
            $query->whereIn('rt.batch', $batchArrList);

        }
        if ($request->subject_id) {
            $query->where('rs.id', $request->subject_id);
        }
        // ->whereIn('rt.semester_id', $semesterIds)
        // ->whereNotNull('rsa.week_period')   //TODO:: GNG-1843
        $query->select($columnArr)
            ->orderBy('rsa.attendance_date', 'DESC')
            ->groupBy('rsa.id');

        $this->gridDataFilter($query, $post, $columns);

        $this->gridDataSorting($query, $post);

        $result = $this->gridDataPagination($query, $post, $countOnly);

        return $result;
    }

    public function getAttendanceScheduleData($request)
    {
        $collegeId = $request->college_id;
        $studentId = $request->student_id;
        $studCourseId = $request->student_course_id;
        $reqStartDate = $request->start_date;
        $reqEndDate = $request->end_date;
        $courseId = $this->getCourseId($studCourseId);

        /*$studCourseData = StudentCourses::find($studCourseId);
        $courseTypeId = $studCourseData->course_type_id;
        $courseId = $studCourseData->course_id;*/

        $batchArrList = $this->getBatchListForAttendance($collegeId, $studentId, $courseId);

        $columnArr = [
            'rsa.id',
            'rsa.student_id',
            'rsa.status',
            'rs.subject_name',
            'rs.subject_code',
            'rsa.attendance_date',
            'rsah.start_time',
            'rsah.end_time',
            'rst.first_name',
            'rst.last_name',
            'rc.room_name',
            'rt.start_time as absent_start_time',
            'rt.finish_time as absent_end_time',

            // DB::raw("CONCAT(rs.subject_name,' : ',rs.subject_code) as title"),
            // DB::raw("CONCAT(rsa.attendance_date,' ',rsah.start_time) as start"),
            // DB::raw("CONCAT(rsa.attendance_date,' ',rsah.end_time) as end"),
            // DB::raw("CONCAT(rst.first_name,' ',rst.last_name) as trainer"),
            // 'rsa.total_hours',
            // 'rsah.timeslot',
            // 'rsah.total_hours as attendance_total_hours'
        ];

        $query = StudentAttendance::from('rto_student_attendance as rsa')
            ->join('rto_timetable as rt', 'rt.id', '=', 'rsa.timetable_id')
            ->leftjoin('rto_student_attendance_hours as rsah', 'rsah.student_attendance_id', '=', 'rsa.id')
            ->leftJoin('rto_staff_and_teacher as rst', 'rst.id', '=', 'rt.teacher_id')
            ->leftJoin('rto_classroom as rc', 'rc.id', '=', 'rt.classroom_id')
            ->leftjoin('rto_subject as rs', 'rs.id', '=', 'rt.subject_id')
            ->where(['rt.college_id' => $collegeId, 'rsa.student_id' => $studentId]);
        if ($request->batch_id) {
            $query->where('rt.batch', $request->batch_id);
        } else {
            $query->whereIn('rt.batch', $batchArrList);
        }
        if ($request->subject_id) {
            $query->where('rs.id', $request->subject_id);
        }
        // ->whereBetween('rsa.attendance_date', [$reqStartDate, $reqEndDate])
        // ->whereIn('rt.semester_id', $semesterIds)
        // ->whereNotNull('rsa.week_period')   //TODO:: GNG-1843
        $result = $query->select($columnArr)
            ->groupBy('rsa.id')
            ->get()
            ->toArray();

        $data = [];
        foreach ($result as $row) {
            $subject = $row['subject_name'].' : '.$row['subject_code'];
            $start = $row['attendance_date'].' '.(($row['status'] == 'absent') ? $row['absent_start_time'] : $row['start_time']);
            $end = $row['attendance_date'].' '.(($row['status'] == 'absent') ? $row['absent_end_time'] : $row['end_time']);
            $data[] = [
                'TaskID' => $row['id'],
                'OwnerID' => $row['student_id'],
                'title' => $subject,
                'description' => $subject,
                'start' => $start,
                'end' => $end,
                'subject_name' => $subject,
                'trainer_name' => $row['first_name'].' '.$row['last_name'],
                'time_duration' => date('h:i A', strtotime($start)).' - '.date('h:i A', strtotime($end)),
                'status' => $row['status'],
                'room_name' => $row['room_name'],
                'IsAllDay' => false,
            ];
        }

        // return $data;
        return [
            'calendarData' => $data,
            'headerData' => $this->getStudentAttendanceLastMonthData($studentId, $batchArrList, $reqStartDate),
        ];
    }

    public function studentTimetableData($data)
    {
        $arrAttendanceType = Config::get('constants.arrAttendanceType');

        $collegeId = Auth::user()->college_id;
        $month = isset($data['month']) ? $data['month'] : date('m');
        $year = isset($data['year']) ? $data['year'] : date('Y');
        $startDate = date($year.'-'.$month.'-01');
        if ($month == '02') {
            $endDate = date($year.'-'.$month.'-28');
        } else {
            $endDate = date($year.'-'.$month.'-30');
        }
        $validRecord = [];

        $enrollStudentData = StudentSubjectEnrolment::where(['college_id' => $collegeId, 'student_id' => $data->student_id, 'course_id' => $data->course_id])->select('batch')->get()->toarray();

        $rowQuery = TimetableDetail::from('rto_timetable_detail as rtd')
            ->leftJoin('rto_timetable as rt', 'rt.id', '=', 'rtd.timetable_id')
            ->leftJoin('rto_subject as rs', 'rs.id', '=', 'rt.subject_id')
            ->leftjoin('rto_semester as sem', 'sem.id', '=', 'rt.semester_id')

            ->leftJoin('rto_classroom as rc', 'rc.id', '=', 'rtd.room_id')
            ->leftJoin('rto_staff_and_teacher as rst', 'rst.id', '=', 'rtd.teacher_id')
            ->where('rt.college_id', $collegeId)
            ->whereBetween('rtd.timetable_date', [$startDate, $endDate])
            ->orderBy('rtd.timetable_date', 'ASC');

        $batchValues = array_column($enrollStudentData, 'batch');
        $rowQuery->whereIn('batch', $batchValues);

        $validRecord = $rowQuery->select(
            DB::raw("CONCAT(rs.subject_code,'-',rs.subject_name) as subject_name"),
            DB::raw("CONCAT(rst.first_name,' ',rst.last_name) as trainer_name"),
            'rtd.timetable_date',
            'rs.subject_code',
            'sem.semester_name',
            'rtd.start_time',
            'rt.attendance_type as attendance_type',
            'rt.class_capacity',
            'rt.break_from',
            'rtd.end_time',
            'rtd.room_id',
            'rtd.id',
            'rt.term',
            'rt.batch',
            'rc.room_name',
            'rtd.teacher_id',
            'rt.subject_id'
        )->get()->toArray();
        $color1 = ['bg-red-50', 'bg-yellow-50', 'bg-green-50', 'bg-blue-50', 'bg-purple-50'];
        $color2 = ['bg-red-500', 'bg-yellow-500', 'bg-green-500', 'bg-blue-500', 'bg-purple-500'];
        $finalArray = [];
        $k = 0;
        foreach ($validRecord as $key => $values) {
            if ($k > 4) {
                $k = 0;
            }
            $finalArray[] = [
                'TaskId' => $values['id'],
                'Title' => $values['subject_name'].' | '.date('h:i A', strtotime($values['start_time'])).' - '.date('h:i A', strtotime($values['end_time'])).' | '.$values['room_name'].' | '.$values['trainer_name'],
                'trainer_name' => $values['trainer_name'],
                'subject_name' => $values['subject_name'],
                'room_name' => $values['room_name'],
                'batch' => $values['batch'],
                'semester_name' => $values['semester_name'],
                'term' => $values['term'],
                'color1' => $color1[$k],
                'color2' => $color2[$k],
                'attendance_type' => $arrAttendanceType[$values['attendance_type']],
                'class_capacity' => ! empty($values['class_capacity']) ? $values['class_capacity'] : '-',
                'break' => (! empty($values['break_from']) ? 'Yes' : 'No'),
                'timetable_time' => date('h:i A', strtotime($values['start_time'])).' - '.date('h:i A', strtotime($values['end_time'])),
                'Start' => $values['timetable_date'].' '.$values['start_time'],
                'End' => $values['timetable_date'].' '.$values['end_time'],
                'IsAllDay' => false,
            ];
            $k++;
        }

        $studentDetails = StudentCourses::from('rto_student_courses as rsc')
            ->join('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->join('rto_students as rs', 'rs.id', '=', 'rsc.student_id')
            ->where('rsc.id', '=', $data->student_course_id)
            ->select(
                'rsc.status as status',
                'rsc.total_weeks as total_weeks',
                'rc.course_name',
                'rs.profile_picture',
                DB::raw("CONCAT(DATE_FORMAT(rsc.start_date, '%d %b %Y'),' TO ',DATE_FORMAT(rsc.finish_date, '%d %b %Y')) as course_duration"),
                DB::raw("CONCAT(rs.first_name,' ',rs.family_name) as student_name")
            )
            ->groupBy('rsc.id')->get()->first();

        $studentDetails['profile_pic'] = $this->getStudentProfilePicPath($data->student_id, $studentDetails['profile_picture']);
        $array['studentDetails'] = $studentDetails;
        $array['timetable'] = $finalArray;

        return $array;
    }

    public function studentTimetableDatav2($request)
    {

        $collegeId = Auth::user()->college_id;
        $studentId = $request->student_id;
        $courseId = $request->course_id;
        $enrollData = StudentSubjectEnrolment::where(['college_id' => $collegeId, 'student_id' => $studentId, 'course_id' => $courseId])->select('semester_id', 'term', 'subject_id')->get()->toarray();

        $columnArr = [
            'rs.semester_name',
            'rsub.subject_code',
            'rsub.subject_name',
            'rt.change_type',
            'rt.day',
            'rt.start_week',
            'rt.end_week',
            'rt.start_time',
            'rt.finish_time',
            'rt.start_week_id',
            'rt.end_week_id',
            'rt.id',
            'rt.term',
            'rt.semester_id',
        ];
        $query = Timetable::from('rto_timetable as rt')
            ->leftJoin('rto_timetable_relief as rtr', function ($join) {
                $join->on('rtr.timetable_id', '=', 'rt.id');
                $join->on('rtr.relief_new_teacher_id', '=', 'rt.relief_new_teacher_id');
            })
            ->leftjoin('rto_semester as rs', 'rs.id', 'rt.semester_id')
            ->leftjoin('rto_subject as rsub', 'rsub.id', 'rt.subject_id');

        $query->where(function ($childQuery) use ($enrollData) {
            foreach ($enrollData as $enr) {
                $childQuery->orWhere(function ($subQuery) use ($enr) {
                    $subQuery->where([
                        'rt.semester_id' => $enr['semester_id'],
                        'rt.term' => $enr['term'],
                        'rt.subject_id' => $enr['subject_id'],
                    ]);
                });
            }
        });

        $timetableDetail = $query->where(['rt.college_id' => $collegeId])->groupBy('rt.id')->get($columnArr);

        $arrDays = ['1' => 'Monday', '2' => 'Tuesday', '3' => 'Wednesday', '4' => 'Thursday', '5' => 'Friday', '6' => 'Saturday', '7' => 'Sunday'];
        $resArr = [];

        foreach ($timetableDetail as $k => $row) {
            $startDate = $row->start_week;
            $endDate = $row->end_week;
            $days = $row->day;
            if (! empty($startDate) && ! empty($endDate) && ! empty($days)) {
                $dayData[] = array_keys($arrDays, $days);
                for ($j = strtotime($startDate); $j <= strtotime($endDate); $j = strtotime('+1 day', $j)) {
                    if (isset($dayData[$k][0]) && date('N', $j) == $dayData[$k][0]) {
                        $date_time = date('Y-m-d', $j);

                        $timetableDetail[$k] = $this->getTimetableReliefReport($row, $date_time);

                        $title = '';
                        $description = '';
                        $startWeekDate = date('Y-m-d', strtotime($timetableDetail[$k]['start_week']));
                        $endWeekDate = date('Y-m-d', strtotime($timetableDetail[$k]['end_week']));

                        if ((($date_time >= $startWeekDate) && ($date_time <= $endWeekDate)) && $timetableDetail[$k]['change_type'] == 'relief') {
                            /* assign relief teacher from current teacher on day */
                            $title = $timetableDetail[$k]['semester_name'].' ('.$timetableDetail[$k]['subject_code'].')';
                            $description = '<b>Time : </b>'.date('h:ia', strtotime($timetableDetail[$k]['start_time'])).' - '.date('h:ia', strtotime($timetableDetail[$k]['finish_time'])).'<br><b>Semester : </b>'.$timetableDetail[$k]['semester_name'].':('.$timetableDetail[$k]['subject_code'].')';
                        } elseif ((($date_time < $startWeekDate) || ($date_time > $endWeekDate)) && $timetableDetail[$k]['change_type'] == 'relief') {
                            /* Not assign relief teacher from current teacher on day */
                            $title = $timetableDetail[$k]['semester_name'].' ('.$timetableDetail[$k]['subject_code'].')';
                            $description = '<b>Time : </b>'.date('h:ia', strtotime($timetableDetail[$k]['start_time'])).' - '.date('h:ia', strtotime($timetableDetail[$k]['finish_time'])).'<br><b>Semester : </b>'.$timetableDetail[$k]['semester_name'].':('.$timetableDetail[$k]['subject_code'].')';
                        }

                        if ($timetableDetail[$k]['change_type'] != 'relief') {
                            /* without relief only current teacher  on day */
                            $title = $timetableDetail[$k]['semester_name'].' ('.$timetableDetail[$k]['subject_code'].')';
                            $description = '<b>Time : </b>'.date('h:ia', strtotime($timetableDetail[$k]['start_time'])).' - '.date('h:ia', strtotime($timetableDetail[$k]['finish_time'])).'<br><b>Semester : </b>'.$timetableDetail[$k]['semester_name'].':('.$timetableDetail[$k]['subject_code'].')';
                        }

                        if (! empty($title)) {
                            $resArr[] = [
                                'OwnerID' => $timetableDetail[$k]['id'],
                                'TaskID' => $timetableDetail[$k]['id'],
                                'Title' => $title,
                                'Description' => $description,
                                'Start' => $date_time.' '.$timetableDetail[$k]['start_time'],
                                'End' => $date_time.' '.$timetableDetail[$k]['finish_time'],
                                'IsAllDay' => false,
                            ];
                            $k++;
                        }
                    }
                }
            }
        }

        return $resArr;
    }

    public function getTimetableReliefReport($row, $timetableDate)
    {

        if ($row['change_type'] == 'relief') {
            $reliefData = Timetable::from('rto_timetable')
                ->leftjoin('rto_timetable_relief as rtr', 'rtr.timetable_id', '=', 'rto_timetable.id')
                ->leftjoin('rto_staff_and_teacher as rst', 'rst.id', '=', 'rtr.relief_new_teacher_id')
                ->where('rtr.timetable_id', '=', $row['id'])
                ->select(
                    'rtr.*',
                    DB::raw('(SELECT rto_semester_division.week_start FROM rto_semester_division WHERE rto_semester_division.semester_id = '.$row['semester_id'].' AND rto_semester_division.term = '.$row['term'].' AND rto_semester_division.week_period = rtr.relief_from_week) as start_week_date'),
                    DB::raw('(SELECT rto_semester_division.week_finish FROM rto_semester_division WHERE rto_semester_division.semester_id = '.$row['semester_id'].' AND rto_semester_division.term = '.$row['term'].' AND rto_semester_division.week_period = rtr.relief_to_week) as end_week_date')
                )->get()
                ->first();

            if ($reliefData) {
                if ((strtotime($row['start_week']) >= strtotime($reliefData->start_week_date)) && (strtotime($row['end_week']) <= strtotime($reliefData->end_week_date))) {
                    $row['start_week'] = $reliefData->start_week_date;
                    $row['end_week'] = $reliefData->end_week_date;
                    $row['start_week_id'] = $reliefData->relief_from_week;
                    $row['end_week_id'] = $reliefData->relief_to_week;
                } elseif ((strtotime($timetableDate) < strtotime($reliefData->start_week_date)) || (strtotime($timetableDate) > strtotime($reliefData->end_week_date))) {
                    $row['start_week'] = $reliefData->start_week_date;
                    $row['end_week'] = $reliefData->end_week_date;
                }
            }
        }

        return $row;
    }

    public function profileCourseDetails($studentCourseId)
    {

        $data = [
            'course_name' => 'rc.course_name',
            'course_code' => 'rc.course_code',
            'course' => DB::raw("CONCAT(rc.course_code,' : ',rc.course_name) as course"),
            'status' => 'rsc.status',
            'offer_id' => 'rsc.offer_id',
            'coe_name' => 'rsc.coe_name',
            'agency_name' => 'rto_agents.agency_name',
            'intake_year' => 'rsc.intake_year',
            'issued_date' => 'rsc.issued_date',
            'start_date' => 'rsc.start_date',
            'finish_date' => 'rsc.finish_date',
            'course_duration' => 'rc.course_duration',
        ];

        $query = StudentCourses::from('rto_student_courses as rsc')
            ->join('rto_students as rs', 'rs.id', '=', 'rsc.student_id')
            ->join('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->leftjoin('rto_agents', 'rto_agents.id', '=', 'rsc.agent_id')
            ->where('rsc.id', '=', $studentCourseId)
            ->select($data)->get()->first();

        return $query;
    }

    private function getBatchListForAttendance($collegeId, $studentId, $courseId)
    {
        $whereArr = [
            'college_id' => $collegeId,
            'student_id' => $studentId,
            'course_id' => $courseId,
        ];
        $subjectEnrollArr = StudentSubjectEnrolment::where($whereArr)->groupBy('batch')->get(['batch'])->toArray();
        $batchArr = array_column($subjectEnrollArr, 'batch');

        return $batchArr;
    }

    private function getSemesterHasCourse($collegeId, $studentId, $courseTypeId, $courseId)
    {
        $result = Semester::from('rto_semester as rs')
            ->leftjoin('rto_course_calendar as rcc', 'rs.calendar_type', '=', 'rcc.calendar_type')
            ->leftjoin('rto_student_subject_enrolment as rsse', 'rsse.semester_id', '=', 'rs.id')
            ->where([
                'rs.college_id' => $collegeId,
                'rs.course_type_id' => $courseTypeId,
                'rsse.student_id' => $studentId,
                'rsse.course_id' => $courseId,
            ])
            ->select('rs.id')
            ->groupBy('rs.id')
            ->get()
            ->toarray();

        return array_column($result, 'id');
    }

    public function studentDetails($studentId)
    {

        $columnArr = [
            'rs.id',
            'rs.student_type',
            DB::raw("CONCAT(rs.first_name,' ',rs.family_name) as full_name"),
            'rs.generated_stud_id',
            'rs.gender',
            'rs.first_name',
            'rs.middel_name',
            'rs.family_name',
            'rs.email',
            'rs.optional_email',
            'rs.current_home_phone',
            'rs.current_work_phone',
            'rs.current_mobile_phone',
            'rs.DOB',
            'rs.birthplace',
            'rs.birth_country',
            'rs.nationality',
            'rs.passport_no',
            'rs.passport_expiry',
            'rs.visa_number',
            'rs.visa_expiry_date',
            'rsd.emergency_contact_person',
            'rsd.emergency_relationship',
            'rsd.emergency_address',
            'rsd.emergency_phone',
            'rsd.emergency_email',
            'rs.USI',
            'rs.is_apply_usi',
            'rs.current_country',
            'rs.current_building_name',
            'rs.current_unit_detail',
            'rs.current_street_no',
            'rs.current_street_name',
            'rs.current_city',
            'rs.current_state',
            'rs.current_postcode',
            'rs.is_postal_address',
            'rs.postal_country',
            'rs.postal_building_name',
            'rs.postal_unit_detail',
            'rs.postal_street_no',
            'rs.postal_street_name',
            'rs.postal_city',
            'rs.permanent_country',
            'rs.permanent_street_name',
            'rs.postal_state',
            'rs.postal_postcode',
            'rs.profile_picture',
            'rs.visa_status',
            'nat.nationality as birth_nationality_name',
            'c1.name as birth_country_name',
            'c2.name as postal_country_name',
            'c3.name as current_country_name',
            'c4.name as permanent_country_name',
            DB::raw("CONCAT_WS(', ',rs.permanent_street_name, rs.permanent_city, rs.permanent_state, rs.permanent_postcode,c4.name) as address"),
        ];

        $query = Student::from('rto_students as rs')
            ->leftjoin('rto_student_details as rsd', 'rsd.student_id', '=', 'rs.id')
            ->leftjoin('rto_country as nat', 'nat.id', '=', 'rs.nationality')
            ->leftjoin('rto_country as c1', 'c1.id', '=', 'rs.birth_country')
            ->leftjoin('rto_country as c2', 'c2.id', '=', 'rs.postal_country')
            ->leftjoin('rto_country as c3', 'c3.id', '=', 'rs.current_country')
            ->leftjoin('rto_country as c4', 'c4.id', '=', 'rs.permanent_country')
            ->where('rs.id', '=', $studentId)
            ->where('rsd.student_id', '=', $studentId)
            ->select($columnArr)
            ->get()->first();

        return $query;
    }

    public function getCourseList($studentId, $collegeId)
    {
        $data = [
            'student_course_id' => 'rsc.id as value',
            'course' => DB::raw("CONCAT(rc.course_code,' : ',rc.course_name) as text"),
        ];

        $query = StudentCourses::from('rto_student_courses as rsc')
            ->join('rto_students as rs', 'rs.id', '=', 'rsc.student_id')
            ->join('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->where('rsc.student_id', '=', $studentId)
            ->select($data)
            ->get()->toArray();

        return $query;
    }

    public function getStudentPaymentDetails($studentId, $studentCourseId)
    {

        // TODO::GNG-2569
        $tempStudentDetails = StudentCourses::from('rto_student_courses as rsc')
            // ->leftjoin('rto_student_miscellaneous_payment as t1', 't1.student_course_id', '=', 'rsc.id')
            // ->leftjoin('rto_student_service_payment as t2', 't2.student_course_id', '=', 'rsc.id')
            ->where('rsc.student_id', $studentId)
            ->select([
                'rsc.id', 'rsc.course_fee', 'rsc.course_id',
                // DB::raw('(SUM(CASE WHEN t1.payment_status = "unpaid" THEN t1.amount ELSE 0 END)) as courseMiscellaneousFeeDue'),
                // DB::raw('(SUM(CASE WHEN t2.payment_status = "unpaid" THEN t1.amount ELSE 0 END)) as courseServiceFeeDue')
            ])
            ->groupBy('rsc.id')
            ->get()
            ->toArray();

        // TODO::GNG-4126 (Remove the overall total value for all courses)
        /*$tcCourseFee = 0;
        $tcFeePaid = 0;
        $tcFeeRemaining = 0;
        $tcOverdueAmount = 0;
        $studCourseIdArr = array_column($tempStudentDetails, 'id');

        foreach ($tempStudentDetails as $row) {
            $tempCourseFee = (isset($row['course_fee'])) ? $row['course_fee'] : 0;

            $query = StudentInitialPaymentDetails::where([
                'student_id' => $studentId,
                'course_id' => $row['course_id'],
            ]);
            $tempPayAmount = $query->sum('upfront_fee_to_pay') ?? 0;
            $tempPaidAmount = $query->sum('upfront_fee_pay') ?? 0;
            // $tempDueAmount = max(0, $tempPayAmount - $tempPaidAmount);
            $tempRemaining = $tempPayAmount - $tempPaidAmount;

            $tempArr = $query->where('due_date', '<', now())
                ->select([
                    DB::raw('(SUM(upfront_fee_to_pay)) as upfront_fee_to_pay'),
                    DB::raw('(SUM(upfront_fee_pay)) as upfront_fee_pay'),
                ])
                ->first();
            $tempOverPayAmount = (isset($tempArr->upfront_fee_to_pay)) ? $tempArr->upfront_fee_to_pay : 0;
            $tempOverPaidAmount = (isset($tempArr->upfront_fee_pay)) ? $tempArr->upfront_fee_pay : 0;
            $tempOverDueAmount = max(0, $tempOverPayAmount - $tempOverPaidAmount);

            $tcCourseFee = $tcCourseFee + $tempCourseFee;
            $tcFeePaid = $tcFeePaid + $tempPaidAmount;
            $tcFeeRemaining = $tcFeeRemaining + $tempRemaining;
            $tcOverdueAmount = $tcOverdueAmount + $tempOverDueAmount;
        }*/

        $columnArr = [
            'rsc.student_id',
            'ra.agency_name',
            'ra.id as agent_id',
            'rsc.status as status',
            'rsc.course_fee as course_fee',
            'rsc.total_weeks as total_weeks',
            'rc.course_name',
            'rs.profile_picture',
            'rs.student_type',
            DB::raw("CONCAT(rs.first_name,' ',rs.family_name) as student_name"),
            DB::raw("CONCAT(rsc.start_date,' TO ',rsc.finish_date) as course_duration,rsc.course_id"),
            DB::raw("CONCAT(DATE_FORMAT(rsc.start_date, '%d %b %Y'),' TO ',DATE_FORMAT(rsc.finish_date, '%d %b %Y')) as week_duration"),
            DB::raw('(SUM(CASE WHEN t1.payment_status != "paid" THEN (t1.amount - t1.paid_amount) ELSE 0 END)) as courseMiscellaneousFeeDue'),
            DB::raw('(SUM(CASE WHEN t2.payment_status != "paid" THEN (t1.amount - t1.paid_amount) ELSE 0 END)) as courseServiceFeeDue'),
        ];

        $studentDetails = StudentCourses::from('rto_student_courses as rsc')
            ->join('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->join('rto_agents as ra', 'ra.id', '=', 'rsc.agent_id')
            ->join('rto_students as rs', 'rs.id', '=', 'rsc.student_id')
            ->leftjoin('rto_student_miscellaneous_payment as t1', 't1.student_course_id', '=', 'rsc.id')
            ->leftjoin('rto_student_service_payment as t2', 't2.student_course_id', '=', 'rsc.id')
            ->where('rsc.id', '=', $studentCourseId)
            ->select($columnArr)
            ->groupBy('rsc.id')
            ->get()
            ->first();
        $courseId = $studentDetails['course_id'];

        $studentCoursesDetail = StudentCourses::from('rto_student_courses as rsc')
            ->join('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->where('rsc.student_id', $studentId)
            ->whereNotIn('rsc.id', [$studentCourseId])
            ->select('rsc.id as Id', DB::raw("CONCAT(rc.course_code,' : ',rc.course_name, ' (', rsc.status, ')') as Name"))->get()->toArray();

        $inActiveStatusList = [
            StudentCourses::STATUS_CANCELLED,
            // StudentCourses::STATUS_DEFERRED,
            // StudentCourses::STATUS_WITHDRAWN,
            // StudentCourses::STATUS_SUSPENDED,
        ];
        $activeStudentCoursesDetail = StudentCourses::from('rto_student_courses as rsc')
            ->join('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
            ->where('rsc.student_id', $studentId)
            ->whereNotIn('rsc.id', [$studentCourseId])
            ->whereNotIn('rsc.status', $inActiveStatusList)
            ->select([
                'rsc.id as Id',
                DB::raw("CONCAT(rc.course_code,' : ',rc.course_name, ' (', rsc.status, ')') as Name"),
            ])->get()
            ->toArray();

        /*$tempArr = StudentInitialPaymentDetails::where('due_date', '<', now())
            ->where(['student_id' => $studentId, 'course_id' => $courseId])
            ->select([
                DB::raw('(SUM(upfront_fee_to_pay)) as upfront_fee_to_pay'),
                DB::raw('(SUM(upfront_fee_pay)) as upfront_fee_pay')
            ])
            ->first();*/

        $tempArr = StudentInitialPaymentDetails::where(['student_id' => $studentId, 'course_id' => $courseId, 'student_course_id' => $studentCourseId])
            ->select([
                DB::raw('(SUM(CASE WHEN due_date < NOW() THEN upfront_fee_to_pay ELSE 0 END)) as upfront_fee_to_pay_due'),
                DB::raw('(SUM(CASE WHEN due_date < NOW() THEN upfront_fee_pay ELSE 0 END)) as upfront_fee_pay_due'),
                DB::raw('(SUM(upfront_fee_to_pay)) as upfront_fee_to_pay'),
                DB::raw('(SUM(upfront_fee_pay)) as upfront_fee_pay'),
            ])
            ->first();

        $invoicedDueAmount = (isset($tempArr->upfront_fee_to_pay)) ? $tempArr->upfront_fee_to_pay : 0;
        $totalCoursePaidAmount = (isset($tempArr->upfront_fee_pay)) ? $tempArr->upfront_fee_pay : 0;

        $invoicedDueAmount2 = (isset($tempArr->upfront_fee_to_pay_due)) ? $tempArr->upfront_fee_to_pay_due : 0;
        $totalCoursePaidAmount2 = (isset($tempArr->upfront_fee_pay_due)) ? $tempArr->upfront_fee_pay_due : 0;

        $studentDetails['invoicedDueAmount'] = $invoicedDueAmount2 - $totalCoursePaidAmount2;
        $studentDetails['totalFeePaid'] = $totalCoursePaidAmount;
        $studentDetails['totalRemainingFee'] = $studentDetails->course_fee - $totalCoursePaidAmount;
        $studentDetails['totalCourseFeeBalanceDue'] = $studentDetails->course_fee - $totalCoursePaidAmount;
        $studentDetails['profile_pic'] = $this->getStudentProfilePicPath($studentId, $studentDetails['profile_picture']);

        // TODO::GNG-2569
        $studInitialPay = StudentInitialPayment::where('student_id', $studentId)->first();
        $studentDetails['studentCredit'] = ($studInitialPay) ? $studInitialPay->student_credit : 0;

        // TODO::GNG-4126 (Remove the overall total value for all courses)
        /*$studentDetails['tcCourseFee'] = $tcCourseFee;
        $studentDetails['tcFeePaid'] = $tcFeePaid;
        $studentDetails['tcFeeRemaining'] = $tcFeeRemaining;
        $studentDetails['tcOverdueAmount'] = $tcOverdueAmount;
        $studentDetails['tcAgentCommissionPaid'] = StudentAgentCommission::where('student_id', $studentId)->whereIn('student_course_id', $studCourseIdArr)->sum('commission_paid');*/

        // TODO::GNG-3086
        $outstandingBalance = 0;
        $unallocatedCredit = 0;
        if (Xero::isConnected()) {
            // $student = Student::with('xeroContact')->findOrFail($studentId);
            $student = Student::find($studentId);
            if ($student->isXeroContactCreatated()) {
                // dispatch_sync(new \Domains\Xero\Jobs\SyncContactFromXero($student->xeroContact));
                $outstandingBalance = $student->xeroContact->fresh()->outstanding_balance ? $student->xeroContact->fresh()->outstanding_balance : 0;
                $unallocatedCredit = $student->xeroContact->fresh()->unallocated_credit ?? 0;
            }
        }
        $studentDetails['outstanding_balance'] = abs($outstandingBalance);
        $studentDetails['unallocated_credit'] = abs($unallocatedCredit);

        // $enrollSubData = StudentUnitEnrollment::where('course_id', '=', $courseId)->where('student_id', '=', $studentId)->count();
        $enrollSubjectData = [];
        if (! in_array($studentDetails['student_type'], ['Offshore', 'Onshore'])) {
            $enrollSubjectData = StudentSubjectEnrolment::from('rto_student_subject_enrolment as rsse')
                ->leftJoin('rto_student_unit_enrollment as rsue', 'rsue.student_subject_enrollment_id', '=', 'rsse.id')
                ->where(['rsse.course_id' => $courseId, 'rsse.student_id' => $studentId])
                ->selectRaw('COUNT(*) as count, SUM(rsue.tution_fee) as total_fee') // tution_fee OR amount_paid
                ->first();
        }

        $whereArr = ['student_id' => $studentId, 'student_course_id' => $studentCourseId];
        $paymentScheduleDataCount = StudentInitialPaymentDetails::where($whereArr)->count();
        $paidScheduleCount = StudentInitialPaymentDetails::where($whereArr)->where('payment_status', 'paid')->count();
        $remainScheduleCount = StudentInitialPaymentDetails::where($whereArr)->where('payment_status', '!=', 'unpaid')->count();
        $totalInvoiceCredit = StudentInitialPaymentDetails::where($whereArr)->sum('invoice_credit');
        $scholarshipData = StudentScholarship::where($whereArr)
            ->selectRaw('
                                            SUM(scholarship_amount + transfer_amount) as totalAmount,
                                            SUM(used_scholarship_amount) as usedAmount,
                                            SUM(transfer_amount) as transferredAmount
                                        ')->first()->toArray();

        $totalMiscellaneousFee = StudentMiscellaneousPayment::where($whereArr)->sum('amount');
        $totalServiceFee = StudentServicePayment::where($whereArr)->sum('amount');
        $paidMiscellaneousFee = StudentMiscellaneousPayment::where($whereArr)->where('payment_status', '!=', 'unpaid')->sum('paid_amount');
        $paidServiceFee = StudentServicePayment::where($whereArr)->where('payment_status', '!=', 'unpaid')->sum('paid_amount');
        $agentCommissionPaid = StudentAgentCommission::where($whereArr)->sum('commission_paid');

        $studentDetails['agentCommissionPaid'] = $agentCommissionPaid;
        $studentDetails['totalSchedule'] = $paymentScheduleDataCount;
        $studentDetails['paidSchedule'] = $paidScheduleCount;
        $studentDetails['remainSchedule'] = $remainScheduleCount;
        $studentDetails['scholarshipData'] = $scholarshipData;
        $studentDetails['invoiceCredit'] = $totalInvoiceCredit;

        $dataArray['studentDetails'] = $studentDetails;
        $dataArray['enrollSubjectData'] = $enrollSubjectData;
        $dataArray['xeroConnect'] = Xero::isConnected() ? 1 : 0;
        $dataArray['paidDurationData']['data'] = $this->convertConstantsFormat(Config::get('constants.arrPaidDuration'));
        $dataArray['paymentModeData']['data'] = PaymentMode::select('id as Id', 'name as Name')->get()->toArray();
        $dataArray['courseDurationTypeData']['data'] = $this->convertConstantsFormat(Config::get('constants.arrCourseDurationType'));
        $dataArray['installmentDurationeData']['data'] = $this->convertConstantsFormat(Config::get('constants.arrInstallmentDuration'));
        $dataArray['studentCoursesData']['data'] = $studentCoursesDetail;
        $dataArray['activeStudCourseList']['data'] = $activeStudentCoursesDetail;
        $dataArray['paymentScheduleDataCount'] = $paymentScheduleDataCount;

        $dataArray['studentPaymentDetail']['totalMiscellaneousFee'] = $totalMiscellaneousFee;
        $dataArray['studentPaymentDetail']['totalServiceFee'] = $totalServiceFee;
        $dataArray['studentPaymentDetail']['paidMiscellaneousFee'] = $paidMiscellaneousFee;
        $dataArray['studentPaymentDetail']['paidServiceFee'] = $paidServiceFee;

        return $dataArray;
    }

    public function getStudentDocumentData($request)
    {
        $request->parent_id = isset($request->parent_id) ? $request->parent_id : 0;
        if (isset($request->target)) {

            $parentId = $this->findParentID($request->target, $request->student_id);
            $whereArr = ['rsup.parent_id' => $parentId];
        } else {
            $whereArr = [
                'rsup.college_id' => $request->college_id,
                'rsup.student_id' => $request->student_id,
                'rsup.parent_id' => $request->parent_id,
            ];
        }
        $dataArr = StudentUploadFile::from('rto_student_uploaded_files as rsup')
            ->leftjoin('rto_users as created', 'created.id', '=', 'rsup.created_by')
            ->leftjoin('rto_users as updated', 'updated.id', '=', 'rsup.updated_by')
            ->where($whereArr)
            ->select(['rsup.*', 'created.name as created_by_name', 'updated.name as updated_by_name'])
            ->get()
            ->toArray();

        foreach ($dataArr as $key => $value) {
            $dataArr[$key]['flag'] = 'fileUpload';
        }
        if ($request->parent_id == 0 && ! isset($request->target)) {
            $getStudentFile = StudentOfferDocuments::from('rto_student_offer_documents as rsod')
                ->leftjoin('rto_college_materials as rcm', 'rcm.id', '=', 'rsod.document_material_id')
                ->leftjoin('rto_users as created', 'created.id', '=', 'rcm.created_by')
                ->leftjoin('rto_users as updated', 'updated.id', '=', 'rcm.updated_by')
                ->where('rsod.rto_student_id', '=', $request->student_id)
                ->get(['rsod.document_material_id', 'rcm.*', 'created.name as created_by_name', 'updated.name as updated_by_name'])
                ->toarray();
            foreach ($getStudentFile as $key => $value) {
                $getStudentFile[$key]['flag'] = 'offerDocument';
            }
            $dataArr = array_merge($dataArr, $getStudentFile);
        }

        $data = [];
        foreach ($dataArr as $row) {
            if ($row['type'] == 'File') {
                $ext = '.'.pathinfo($row['original_name'], PATHINFO_EXTENSION);
                $name = str_replace($ext, '', $row['original_name']);
                // $download_path =  fileUrl($row['file_path'] . $row['folder_or_file']);
                $download_path = UploadService::download($row['file_path'].$row['folder_or_file'], $row['original_name']);
                $preview_path = UploadService::preview($row['file_path'].$row['folder_or_file']);

            } else {
                $ext = '';
                $download_path = '';
                $preview_path = '';
                $preview_path = '';
                $name = $row['folder_or_file'];
            }
            if ($name != null) {
                $data[] = [
                    'id' => $row['id'],
                    'entityid' => $row['id'],
                    'name' => $name,
                    'size' => (! empty($row['size'])) ? $row['size'] : 0,
                    'path' => $this->findPath($row),
                    'extension' => $ext,
                    'isDirectory' => ($row['type'] == 'Dir') ? true : false,
                    'preview_path' => $preview_path,
                    'download_path' => $download_path,
                    'hasDirectories' => false,
                    'created' => '/Date('.strtotime($row['created_at']) * 1000 .')/',
                    'modified' => '/Date('.strtotime($row['updated_at']) * 1000 .')/',
                    'created_by' => $row['created_by_name'],
                    'updated_by' => $row['updated_by_name'],
                    'flag' => $row['flag'],
                ];
            }
        }

        return $data;
    }

    public function findParentID($path, $studentId)
    {
        return StudentUploadFile::getIdFromPath($path, $studentId);
    }

    public function findPath($row)
    {
        $path = '/';
        if ($row['flag'] == 'fileUpload') {

            $file = StudentUploadFile::find($row['id']);

            $path = $file ? $file->getFullPath() : null;

        }

        return $path;
    }

    public function getStudentBreadcrumbData($request)
    { // Initialize the breadcrumb array
        $breadcrumbs = [];

        // Start with the provided parent_id
        $parentId = $request->parent_id ?? 0;

        while ($parentId != 0) {
            // Fetch the parent folder data
            $folder = StudentUploadFile::from('rto_student_uploaded_files as rsup')
                ->where('rsup.id', $parentId)
                ->select(['rsup.id', 'rsup.folder_or_file as name', 'rsup.parent_id'])
                ->first();

            if (! $folder) {
                break; // Stop if parent folder not found
            }

            // Add to the breadcrumb trail
            $breadcrumbs[] = [
                'id' => $folder->id,
                'text' => $folder->name,
                'path' => (string) $folder->id,
            ];

            // Move to the next parent
            $parentId = $folder->parent_id;
        }

        // Add the root folder if applicable
        $breadcrumbs[] = [
            'id' => 0,
            'type' => 'rootitem',
            'path' => '0',
        ];

        // Reverse to start from root to the current folder
        return array_reverse($breadcrumbs);

    }

    public function addStudentDocumentDirectory($request)
    {
        $userData = Auth::user();
        $dataArr = [
            'college_id' => $request->college_id,
            'student_id' => $request->student_id,
            'parent_id' => $request->parentId,
            'folder_or_file' => $request->name,
            'type' => 'Dir',
            'created_by' => $userData->id,
            'updated_by' => $userData->id,
        ];
        $data = StudentUploadFile::create($dataArr);
        if ($data->id) {
            return true;
        }

        return false;
    }

    public function updateStudentDocumentDirectory($request)
    {
        $record = StudentUploadFile::find($request->entityid);
        if ($record) {
            if ($record->type == 'Dir') {
                $record->update(['folder_or_file' => $request->name]);
            }
            if ($record->type == 'File') {
                $filePath = Config::get('constants.uploadFilePath.StudentFiles');
                $destinationPath = $this->changeRootPath($filePath, $record->college_id, $record->student_id);
                $dirPath = $destinationPath['view'].$record->folder_or_file;
                if (UploadService::exists($dirPath)) {
                    $ext = '.'.pathinfo($record->folder_or_file, PATHINFO_EXTENSION);
                    $record->update(['original_name' => $request->name.$ext]);
                } else {
                    return ['status' => 'error', 'message' => 'File not found in Directory.'];
                }
            }
            $isDirectory = ($record->type == 'File') ? false : true;
            $fileOrDirectoryName = ($record->type == 'File') ? substr($record->original_name, 0, strpos($record->original_name, '.')) : $record->folder_or_file;
            $size = ($record->type == 'File') ? $record->size : '';
            $updateBy = Users::where('id', $record->updated_by)->value('name');
            $res = [
                'id' => $record->id,
                'name' => $fileOrDirectoryName,
                'size' => $size,
                'path' => (string) $record->id,
                'extension' => '',
                'parentId' => $record->parent_id,
                'opration' => 'update',
                'isDirectory' => $isDirectory,
                'hasDirectories' => false,
                'created' => '/Date('.strtotime($record->created_at) * 1000 .')/',
                'modified' => '/Date('.strtotime($record->updated_at) * 1000 .')/',
                'created_by' => '',
                'updated_by' => $updateBy,
            ];

            return ['status' => 'success', 'message' => 'File or directory rename successfully.', 'data' => $res];
        } else {
            return ['status' => 'error', 'message' => 'File not found.'];
        }
    }

    public function removeStudentDocumentDirectory($request)
    {
        if ($request->flag == 'fileUpload') {
            $getRecord = StudentUploadFile::find($request->entityid);
            if ($getRecord) {

                $collegeId = $getRecord->college_id;
                $studentId = $getRecord->student_id;
                $fileName = $getRecord->folder_or_file;

                if ($getRecord->type == 'File') {
                    $filePath = Config::get('constants.uploadFilePath.StudentFiles');
                    $destinationPath = $this->changeRootPath($filePath, $collegeId, $studentId);
                    $dirPath = public_path().$destinationPath['view'].$fileName;
                    if (is_dir($dirPath)) {
                        $this->delete_directory($dirPath);
                    } else {
                        $dirPath1 = str_replace('\\', '/', $destinationPath['default']).$fileName;
                        // if (is_file($dirPath1)) {
                        //     unlink($dirPath1);
                        // }
                        UploadService::delete($dirPath1);
                    }
                }

                if ($getRecord->type == 'Dir') {
                    $getSubRecordArr = StudentUploadFile::Where('parent_id', $request->entityid)->select(DB::raw('GROUP_CONCAT(DISTINCT id) as idArr'))->get();
                    if ($getSubRecordArr[0]->idArr) {
                        return false;
                    }
                }

                $collegeMaterial = CollegeMaterials::Where('folder_or_file', $fileName)->first();
                if ($collegeMaterial) {
                    $collegeMaterial->destroy($collegeMaterial->id);
                }
                $studentUploadFile = StudentUploadFile::Where('id', $request->entityid)->first();
                if ($studentUploadFile) {
                    $studentUploadFile->destroy($studentUploadFile->id);
                }
            }
        } else {
            $getRecordFromOfferDocument = CollegeMaterials::find($request->entityid);

            if ($getRecordFromOfferDocument) {

                $fileName = $getRecordFromOfferDocument->folder_or_file;
                $folder_path = $getRecordFromOfferDocument->file_path;

                if ($getRecordFromOfferDocument->type == 'File') {

                    $dirPath = public_path().$folder_path.$fileName;
                    if (is_dir($dirPath)) {
                        $this->delete_directory($dirPath);
                    } else {
                        $dirPath1 = str_replace('\\', '/', $dirPath);
                        if (is_file($dirPath1)) {
                            unlink($dirPath1);
                        }
                    }
                }

                if ($getRecordFromOfferDocument->type == 'Dir') {
                    $getSubRecordArr = StudentUploadFile::Where('parent_id', $request->entityid)->select(DB::raw('GROUP_CONCAT(DISTINCT id) as idArr'))->get();
                    if ($getSubRecordArr[0]->idArr) {
                        return false;
                    }
                }

                $collegeMaterial = CollegeMaterials::Where('id', $request->entityid)->first();
                if ($collegeMaterial) {
                    $collegeMaterial->destroy($collegeMaterial->id);
                }
                $StudentOfferDocuments = StudentOfferDocuments::Where('document_material_id', $request->entityid)->first();
                if ($StudentOfferDocuments) {
                    $StudentOfferDocuments->destroy($StudentOfferDocuments->id);
                }
            }
        }

        return true;
    }

    // Note: File Upload with chunk
    public function uploadStudentDocuments2($userId, $collegeId, $studentId, $request)
    {
        $parentId = $request->input('path', 0);
        $rootFolder = Config::get('constants.arrCollegeRootFolder');
        $filePath = Config::get('constants.uploadFilePath.StudentFiles');
        $destinationPath = $this->changeRootPath($filePath, $collegeId, $studentId);

        if (! is_dir($destinationPath['default'])) {
            mkdir($destinationPath['default'], 0777);
        }
        $clgMaterialParentId = $this->getSubParentId([
            'college_id' => $collegeId,
            'folder_name' => $rootFolder['StudentFiles'],
            'sub_folder_name' => $studentId,
            'user_id' => $userId,
        ]);

        // Extract metadata and fileUid
        $metadata = json_decode($request->input('metadata'), true);

        $fileUid = $metadata['uploadUid']; // Unique identifier for the file
        $originalName = $metadata['fileName'];
        $chunkIndex = $metadata['chunkIndex'];
        $totalChunks = $metadata['totalChunks'];

        $file = $request->file('file');
        $filename = $originalName;
        $chunkPath = $destinationPath['default'].DIRECTORY_SEPARATOR.$originalName.'.part';

        // Append the current chunk to the temporary file
        $chunkData = file_get_contents($file->getRealPath());
        file_put_contents($chunkPath, $chunkData, FILE_APPEND);

        // If it's the last chunk, finalize the file
        if ($chunkIndex + 1 == $totalChunks) {

            $filename = $this->removeSpecialCharacter(hashFileName($originalName));
            $finalPath = $destinationPath['default'].DIRECTORY_SEPARATOR.$filename;
            rename($chunkPath, $finalPath);

            $fileSize = filesize($finalPath);

            $studentUploadData = [
                'college_id' => $collegeId,
                'student_id' => $studentId,
                'parent_id' => $parentId,
                'folder_or_file' => $filename,
                'original_name' => $originalName,
                'file_path' => $destinationPath['view'],
                'type' => 'File',
                'size' => $fileSize,
                'created_by' => $userId,
                'updated_by' => $userId,
            ];

            $collegeMaterialData = [
                'college_id' => $collegeId,
                'parent_id' => $clgMaterialParentId,
                'folder_or_file' => $filename,
                'size' => $fileSize,
                'type' => 'File',
                'original_name' => $originalName,
                'file_path' => $destinationPath['view'],
                'created_by' => $userId,
                'updated_by' => $userId,
            ];

            StudentUploadFile::create($studentUploadData);
            CollegeMaterials::create($collegeMaterialData);

            return [
                'uploaded' => true,
                'fileUid' => $fileUid,
                'name' => $originalName,
                'size' => $fileSize,
                'type' => 'f',
            ];
        }

        // Return for intermediate chunks
        return [
            'uploaded' => false,
            'fileUid' => $fileUid,
            'chunkIndex' => $chunkIndex,
        ];
    }

    // Note: File Upload without chunk
    public function uploadStudentDocuments($userId, $collegeId, $studentId, $request)
    {
        $parentId = $this->findParentID($request->path, $studentId);
        // $parentId = (!empty($request->path)) ? $request->path : 0;
        $rootFolder = Config::get('constants.arrCollegeRootFolder');
        $filePath = Config::get('constants.uploadFilePath.StudentFiles');
        $destinationPath = $this->changeRootPath($filePath, $collegeId, $studentId);
        $dataArr = [
            'college_id' => $collegeId,
            'folder_name' => $rootFolder['StudentFiles'],
            'sub_folder_name' => $studentId,
            'user_id' => $userId,
        ];
        $clgMaterialParentId = $this->getSubParentId($dataArr);

        $studentDoc = $request->file();
        if (isset($studentDoc['file'])) {
            $file = $studentDoc['file'];
            $originalName = $file->getClientOriginalName();
            $filenm = hashFileName($originalName);
            $filename = $this->removeSpecialCharacter($filenm);
            $fileSize = $file->getSize();
            // $file_size = ceil($fileSize / 1024);
            //            $upload_success = $file->move($destinationPath['default'], $filename);
            $upload_success = UploadService::uploadAs($destinationPath['view'], $file, $filename);
            info('file uploaded form student document ', [$upload_success]);
            $studentUploadData = [
                'college_id' => $collegeId,
                'student_id' => $studentId,
                'parent_id' => $parentId,
                'folder_or_file' => $filename,
                'original_name' => $originalName,
                'file_path' => $destinationPath['view'],
                'type' => 'File',
                'size' => $fileSize,
                'created_by' => $userId,
                'updated_by' => $userId,
            ];
            $collegeMaterialData = [
                'college_id' => $collegeId,
                'parent_id' => $clgMaterialParentId,
                'folder_or_file' => $filename,
                'size' => $fileSize,
                'type' => 'File',
                'original_name' => $originalName,
                'file_path' => $destinationPath['view'],
                'created_by' => $userId,
                'updated_by' => $userId,
            ];

            if ($upload_success) {
                StudentUploadFile::create($studentUploadData);
                CollegeMaterials::create($collegeMaterialData);

                return ['name' => $originalName, 'size' => $fileSize, 'type' => 'f'];
            }
        }

        return false;
    }

    // We have move this to tarit
    // public function getSubParentId2($data)
    // {

    //     $mainParentId = $this->getMainParentId($data);
    //     if ($mainParentId > 0) {
    //         $whereArr = [
    //             'college_id'     => $data['college_id'],
    //             'folder_or_file' => $data['sub_folder_name'],
    //             'parent_id'      => $mainParentId,
    //             'type'           => 'Dir'
    //         ];
    //         $primaryId = CollegeMaterials::Where($whereArr)->value('id');
    //         if (isset($primaryId)) {
    //             return $primaryId;
    //         } else {
    //             $dataArr = array(
    //                 'college_id'    => $data['college_id'],
    //                 'parent_id'     => $mainParentId,
    //                 'folder_name'   => $data['sub_folder_name'],
    //                 'type'          => 'Dir',
    //                 'user_id'       => $data['user_id']
    //             );
    //             $primaryId = $this->addClgMaterialParent($dataArr);
    //             return $primaryId;
    //         }
    //     }
    //     return FALSE;
    // }

    // public function getMainParentId($data)
    // {

    //     $whereArr = [
    //         'college_id'     => $data['college_id'],
    //         'folder_or_file' => $data['folder_name'],
    //         'parent_id'      => 0,
    //         'type'           => 'Fix'
    //     ];
    //     $mainParentId = CollegeMaterials::Where($whereArr)->value('id');
    //     if (isset($mainParentId)) {
    //         return $mainParentId;
    //     } else {
    //         $dataArr = array(
    //             'college_id'    => $data['college_id'],
    //             'parent_id'     => '0',
    //             'folder_name'   => $data['folder_name'],
    //             'type'          => 'Fix',
    //             'user_id'       => $data['user_id']
    //         );
    //         $primaryId = $this->addClgMaterialParent($dataArr);
    //         return $primaryId;
    //     }
    // }

    // public function addClgMaterialParent($data)
    // {
    //     $collegeMaterialArr = [
    //         'college_id'    => $data['college_id'],
    //         'parent_id'     => $data['parent_id'],
    //         'folder_or_file' => (@$data['file_name']) ? $data['file_name'] : $data['folder_name'],
    //         'size'          => (@$data['size']) ? $data['size'] : NULL,
    //         'type'          => $data['type'],
    //         'original_name' => (@$data['original_name']) ? $data['original_name'] : NULL,
    //         'file_path'     => (@$data['file_path']) ? $data['file_path'] : NULL,
    //         'created_by'    => $data['user_id'],
    //         'updated_by'    => $data['user_id']
    //     ];
    //     $fileFolder = CollegeMaterials::create($collegeMaterialArr);
    //     return $fileFolder->id();
    // }

    public function getRecentDocuments($studentId, $collegeId)
    {

        $dataArr = StudentUploadFile::where('college_id', '=', $collegeId)
            ->where('student_id', '=', $studentId)
            ->where('type', '=', 'File')
            ->orderBy('created_at', 'desc')
            ->get()->toArray();

        return $dataArr;
    }

    public function getStudentTask($studentId, $collegeId)
    {
        $query = TaskNew::from('rto_task_new')
            ->leftjoin('rto_task_priority', 'rto_task_priority.id', '=', 'rto_task_new.priority_id')
            ->leftjoin('rto_task_status', 'rto_task_status.id', '=', 'rto_task_new.status_id')
            ->where('rto_task_new.college_id', $collegeId)
            ->where('rto_task_new.student_id', $studentId)
            ->where('rto_task_new.subtask_id', 0);

        $query->select(
            'rto_task_new.id as taskid',
            'rto_task_new.title as title',
            'rto_task_new.due_date as due_date',
            'rto_task_priority.priority_name as priority',
            'rto_task_priority.priority_color as priority_color',
            'rto_task_status.status_name as status',
            'rto_task_status.status_color'
        );
        $query->orderBy('taskid', 'DESC');
        $query->limit('5');

        return $query->get()->toArray();
    }

    // THIS FUNCTION IS REMOVE SPECIAL CHARACTER FROM THE FILE NAME.
    private function removeSpecialCharacter($fileName)
    {
        $fileName = str_replace(' ', '-', $fileName); // Replaces all spaces with hyphens.

        return preg_replace('/[^A-Za-z0-9.\-]/', '', $fileName); // Removes special chars.
    }

    private function delete_directory($dirname)
    {
        if (is_dir($dirname)) {
            $dir_handle = opendir($dirname);
        }
        if (! $dir_handle) {
            return false;
        }
        while ($file = readdir($dir_handle)) {
            if ($file != '.' && $file != '..') {
                if (! is_dir($dirname.'/'.$file)) {
                    unlink($dirname.'/'.$file);
                } else {
                    delete_directory($dirname.'/'.$file);
                }
            }
        }
        closedir($dir_handle);
        rmdir($dirname);

        return true;
    }

    public function getStudentCourseAttendanceData($collegeId, $studentId, $courseId)
    {

        /*$batchArr = StudentSubjectEnrolment::from('rto_student_subject_enrolment as rsse')
            ->join('rto_student_courses as rsc', function($join) use($studentId){
                $join->on('rsc.course_id', '=', 'rsse.course_id');
                $join->on('rsc.student_id', '=', DB::raw($studentId));
            })
            //->join('rto_student_courses as rsc', 'rsc.course_id', '=', 'rsse.course_id')
            ->where([
                'rsse.college_id' => $collegeId,
                'rsse.student_id' => $studentId,
                'rsc.id'          => $studentCourseId
            ])
            ->groupBy('rsc.id')
            ->get(['batch'])
            ->toArray();
        $batchList = array_column($batchArr, 'batch');*/
        // dd($batchList);

        $resArr = StudentAttendance::from('rto_student_attendance as rsa')
            ->join('rto_timetable as rt', 'rt.id', '=', 'rsa.timetable_id')
            ->join('rto_student_subject_enrolment as rsse', function ($join) use ($courseId, $studentId) {
                $join->on('rsse.semester_id', '=', 'rt.semester_id')
                    ->on('rsse.term', '=', 'rt.term')
                    ->on('rsse.course_id', '=', DB::raw($courseId))
                    ->on('rsse.student_id', '=', DB::raw($studentId));
            })
            ->where(['rt.college_id' => $collegeId, 'rsa.student_id' => $studentId])
            ->whereNotNull('rsa.week_period')   // TODO:: GNG-1843
            // ->whereIn('rt.batch', $batchList)
            ->groupBy(['rsa.id'])
            ->orderBy('rsa.attendance_date', 'DESC')
            ->select(
                'rsa.id',
                'rsa.week_period',
                'rsa.total_hours'
                // 'rsa.*', DB::raw("SUM(rsa.total_hours) AS totalHours")
            )
            ->get()
            ->toArray();

        $res = [];
        foreach ($resArr as $row) {
            if (isset($res[$row['week_period']])) {
                $res[$row['week_period']] = $res[$row['week_period']] + $row['total_hours'];
            } else {
                $res[$row['week_period']] = $row['total_hours'];
            }
        }
        $res = array_values($res);

        $data = [
            ['Name' => 'Week 1', 'Value' => (isset($res[0]) ? $res[0] : 0), 'valueColor' => '#40A9FF'],
            ['Name' => 'Week 2', 'Value' => (isset($res[1]) ? $res[1] : 0), 'valueColor' => '#6366F1'],
            ['Name' => 'Week 3', 'Value' => (isset($res[2]) ? $res[2] : 0), 'valueColor' => '#F97316'],
            ['Name' => 'Week 4', 'Value' => (isset($res[3]) ? $res[3] : 0), 'valueColor' => '#10B981'],
            ['Name' => 'Week 5', 'Value' => (isset($res[4]) ? $res[4] : 0), 'valueColor' => '#EF4444'],
            ['Name' => 'Week 6', 'Value' => (isset($res[5]) ? $res[5] : 0), 'valueColor' => '#F97316'],
        ];

        return $data;
    }

    public function saveStudentService($studentId, $data)
    {
        // Change according to OLD model
    }

    public function checkDuplicateRecord($studentId, $campusId, $courseId, $offerId = '')
    {
        $sql = StudentCourses::Where([
            'student_id' => $studentId,
            'course_id' => $courseId,
            // 'campus_id' => $campusId
        ]);
        if (! empty($offerId) && $offerId != 'new_offer') {
            $sql->where('offer_id', $offerId);
        }
        $res = $sql->count();

        return $res;
    }

    public function addStudentOffer($userId, $studentId, $data)
    {
        $studentOfferId = null;
        if ($data['offer_id'] == 'new_offer') {
            if (! empty($data['agent_id']) && ! empty($data['campus_id']) && ! empty($data['course_id'])) {
                $studentOfferId = StudentOffers::create(['student_id' => $studentId, 'status' => 'In_process'])->id;
                StudentServiceInformation::create([
                    'student_id' => $studentId,
                    'offer_id' => $studentOfferId,
                    'created_by' => $userId,
                    'updated_by' => $userId,
                ]);
            }
        } else {
            $studentOfferId = $data['offer_id'];
        }
        StudentDetails::where('student_id', $studentId)->update(['offer_id' => $studentOfferId]);

        return $studentOfferId;
    }

    public function getInvoiceNumber()
    {
        return InvoiceNumber::where('college_id', Auth::user()->college_id)->value('invoice_number');
    }

    public function getReceiptNumber()
    {
        return ResceiptNumber::where('college_id', Auth::user()->college_id)->value('resceipt_number');
    }

    public function getViewSchedulePaymentDetails($request, $countOnly = false)
    {

        $collegeId = Auth::user()->college_id;
        $paymentDetailId = $request->id;
        $studentCourseId = $request->student_course_id;
        $post = ($request->input()) ? $request->input() : [];

        $query = StudentInitialPaymentDetails::from('rto_student_initial_payment_details')
            ->join('rto_students as rs', 'rs.id', '=', 'rto_student_initial_payment_details.student_id')
            ->leftjoin('rto_payment_mode as rpm', 'rpm.id', '=', 'rto_student_initial_payment_details.payment_mode')
            ->where('rto_student_initial_payment_details.id', '=', $paymentDetailId)
            ->select(
                'rto_student_initial_payment_details.*',
                DB::raw("CONCAT(rs.first_name,' ',rs.family_name) as full_name"),
                'rpm.name as payment_mode_name',
                DB::raw("CONCAT(DATE_FORMAT(rto_student_initial_payment_details.invoiced_start_date, '%d %b %Y'),' - ',DATE_FORMAT(rto_student_initial_payment_details.due_date, '%d %b %Y')) as payment_duration")
            );

        $this->gridDataSorting($query, $post);
        $result = $this->gridDataPagination($query, $post, $countOnly);

        return $result;
    }

    public function getSubjectListFromCourse($request)
    {

        $course_id = StudentCourses::where('id', $request->student_course_id)->where('student_id', $request->student_id)->value('course_id');

        return CourseSubject::join('rto_courses', 'rto_courses.id', '=', 'rto_course_subject.course_id')
            ->join('rto_subject', 'rto_subject.id', '=', 'rto_course_subject.subject_id')
            ->join('rto_course_campus', 'rto_course_campus.course_id', '=', 'rto_courses.id')
            ->leftjoin('rto_timetable', 'rto_timetable.subject_id', '=', 'rto_subject.id')
            ->where('rto_courses.id', $course_id)
            ->where('rto_course_subject.is_active', 1)
            // $query = CourseSubject::join('rto_subject', 'rto_subject.id', '=', 'rto_course_subject.subject_id');

            // if(is_array($request->campus_id)){
            // $query->whereIn('rto_course_campus.campus_id', $request->campus_id);
            // }else{
            // $query->where('rto_course_campus.campus_id', $request->campus_id);
            // }
            ->orderBy('label')
            ->select(
                'rto_subject.id as value',
                DB::raw('CONCAT(rto_subject.subject_code,":",rto_subject.subject_name) AS label'),
                DB::raw('COUNT(DISTINCT rto_timetable.batch) AS data_count'),
                DB::raw('"Batch" as counted_label'),
            )->groupBy('rto_subject.id')
            // dump(getParsedSQL($query));
            ->get();
    }

    public function getBatchListFromCourse($request)
    {

        $course_id = StudentCourses::where('id', $request->student_course_id)->where('student_id', $request->student_id)->value('course_id');

        $query = Timetable::join('rto_course_subject', 'rto_course_subject.subject_id', '=', 'rto_timetable.subject_id')
            ->join('rto_subject', 'rto_subject.id', '=', 'rto_course_subject.subject_id')
            ->join('rto_course_campus', 'rto_course_subject.course_id', 'rto_course_campus.course_id')
            ->join('rto_courses', 'rto_courses.id', 'rto_course_campus.course_id')
            ->select('rto_timetable.batch as value', 'rto_timetable.batch as label', DB::raw('"Student" as counted_label'))
            ->withCount([
                'enrolledstudents AS data_count' => function ($q) {
                    $q->select(DB::raw('COUNT(DISTINCT rto_student_subject_enrolment.student_id)'));
                },
            ]);
        $query->where('rto_course_subject.course_id', $course_id);
        if ($request->subject_id) {
            $query->where('rto_course_subject.subject_id', $request->subject_id);
        }
        $query->groupBy('rto_timetable.batch');

        $query->orderBy('rto_timetable.batch', 'asc');
        // dd(getParsedSQL($query));
        $result = $query->get();

        return $result;

    }

    public function getAttendanceBatchData($request)
    {
        $course_id = StudentCourses::where('id', $request->student_course_id)->where('student_id', $request->student_id)->value('course_id');
        $query = Timetable::join('rto_course_subject', 'rto_course_subject.subject_id', '=', 'rto_timetable.subject_id')
            ->join('rto_subject', 'rto_subject.id', '=', 'rto_course_subject.subject_id')
            ->join('rto_course_campus', 'rto_course_subject.course_id', 'rto_course_campus.course_id')
            ->join('rto_courses', 'rto_courses.id', 'rto_course_campus.course_id')
            ->select('rto_timetable.id as value', 'rto_timetable.batch as label', DB::raw('"Student" as counted_label'))
            ->withCount([
                'enrolledstudents AS data_count' => function ($q) {
                    $q->select(DB::raw('COUNT(DISTINCT rto_student_subject_enrolment.student_id)'));
                },
            ]);
        $query->where('rto_course_subject.course_id', $course_id);
        if ($request->subject_id) {
            $query->where('rto_course_subject.subject_id', $request->subject_id);
        }
        // $query->where('rto_course_subject.subject_id', $request->subject_id);
        $query->groupBy('rto_timetable.batch');

        $query->orderBy('rto_timetable.batch', 'asc');
        // dd(getParsedSQL($query));
        $result = $query->get();

        return $result;

    }

    /* TCSI Credit Offer */
    public function getTcsiCreditOfferData($request, $countOnly = false)
    {
        $post = ($request->input()) ? $request->input() : [];

        $whereArr = [
            'rto_student_tcsi_credit_offer.college_id' => $request->college_id,
            'rto_student_tcsi_credit_offer.student_id' => $request->student_id,
            // 'rto_student_tcsi_credit_offer.student_course_id' => $request->student_course_id
        ];

        $columnArr = [
            'rto_student_tcsi_credit_offer.id',
            'rto_courses.course_code',
            'rto_student_tcsi_credit_offer.credit_used_value',
            'rto_student_tcsi_credit_offer.credit_basis_code',
            'rto_student_tcsi_credit_offer.credit_provider_code',
        ];
        $query = TcsiStudentCreditOffer::from('rto_student_tcsi_credit_offer')
            ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_tcsi_credit_offer.course_id')
            ->where($whereArr)
            ->select($columnArr);

        $this->gridDataSorting($query, $post);

        return $this->gridDataPaginationV2($query, $post, $countOnly);
    }

    public function getStudentAddressHistoryData($request, $countOnly = false)
    {

        $studentId = $request->student_id;
        $post = ($request->input()) ? $request->input() : [];

        $query = Activity::with(['causer'])->where('log_name', 'LIKE', implode('_', [$post['type'], $studentId]))->orderBy('id', 'desc');
        $this->gridDataSorting($query, $post);

        $result = $this->gridDataPaginationV2($query, $post, $countOnly);
        if (! $countOnly) {
            foreach ($result['data'] as $k => $row) {
                $result['data'][$k]['updated_by'] = $row['causer']['name'];
                $result['data'][$k]['updated_on'] = $row['updated_at'];
                $parts = explode('::', $row['description']);
                $result['data'][$k]['updated_field'] = isset($parts[0]) ? trim($parts[0]) : null;
                $result['data'][$k]['updated_value'] = isset($parts[1]) ? trim($parts[1]) : null;
                $result['data'][$k]['properties'] = $this->setAttributeFiled($row['properties']);
            }
        }

        return $result;
    }

    public function getStudentEmergencyDetailsHistoryData($request, $countOnly = false)
    {

        $studentId = $request->student_id;
        $post = ($request->input()) ? $request->input() : [];

        $query = Activity::with(['causer'])->where('log_name', 'LIKE', implode('_', [$post['type'], $studentId]))->orderBy('id', 'desc');
        $this->gridDataSorting($query, $post);
        $result = $this->gridDataPaginationV2($query, $post, $countOnly);
        if (! $countOnly) {
            foreach ($result['data'] as $k => $row) {
                $result['data'][$k]['updated_by'] = $row['causer']['name'];
                $result['data'][$k]['updated_on'] = $row['updated_at'];
                $parts = explode('::', $row['description']);
                $result['data'][$k]['updated_field'] = isset($parts[0]) ? trim($parts[0]) : null;
                $result['data'][$k]['updated_value'] = isset($parts[1]) ? trim($parts[1]) : null;
                $result['data'][$k]['properties'] = $this->setAttributeFiled($row['properties']);
            }
        }

        return $result;
    }
}
