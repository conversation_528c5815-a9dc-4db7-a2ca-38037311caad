<?php

namespace App\Services;

use App\Exceptions\ApplicationException;
use App\Helpers\Helpers;
use App\Model\v2\BankInfo;
use App\Model\v2\CollegeCampus;
use App\Model\v2\ContractCode;
use App\Model\v2\ContractFundingSource;
use App\Model\v2\Country;
use App\Model\v2\Courses;
use App\Model\v2\CoursesIntakeDate;
use App\Model\v2\CourseSite;
use App\Model\v2\CoursesUpfrontFee;
use App\Model\v2\CourseTemplate;
use App\Model\v2\CourseType;
use App\Model\v2\ElearningLink;
use App\Model\v2\ElicosDiscount;
use App\Model\v2\EmailTemplate;
use App\Model\v2\EmailTemplateDocuments;
use App\Model\v2\FailedEmails;
use App\Model\v2\Holiday;
use App\Model\v2\PromotionPrice;
use App\Model\v2\Report;
use App\Model\v2\ReportLetterFileAttachment;
use App\Model\v2\ReportLetterSetup;
use App\Model\v2\ReportsAccess;
use App\Model\v2\Semester;
use App\Model\v2\SemesterDivision;
use App\Model\v2\SmsTemplate;
use App\Model\v2\Student;
use App\Model\v2\StudentCourses;
use App\Model\v2\StudentLetter;
use App\Model\v2\Timetable;
use App\Model\v2\Users;
use App\Repositories\OnboardSettingRepository;
use App\Repositories\StudentRepository;
use App\Traits\CommonTrait;
use App\Traits\ResponseTrait;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Hash;
use Intervention\Image\Facades\Image;
use Support\Services\UploadService;

class OnboardSettingService
{
    use CommonTrait;
    use ResponseTrait;

    protected $commonRepository;

    protected $contractCode;

    protected $contractFundingSource;

    protected $courseSite;

    protected $elearningLink;

    protected $bankInfo;

    protected $report;

    protected $courses;

    protected $studentLetter;

    protected $holiday;

    protected $courseTemplate;

    protected $smsTemplate;

    protected $reportsAccess;

    protected $emailTemplate;

    protected $reportLetterSetup;

    private $studentModel;

    public function __construct(
        ContractCode $contractCode,
        ContractFundingSource $contractFundingSource,
        CourseSite $courseSite,
        ElearningLink $elearningLink,
        BankInfo $bankInfo,
        Report $report,
        Courses $courses,
        StudentLetter $studentLetter,
        Holiday $holiday,
        CourseTemplate $courseTemplate,
        SmsTemplate $smsTemplate,
        ReportsAccess $reportsAccess,
        EmailTemplate $emailTemplate,
        ReportLetterSetup $reportLetterSetup,
        Student $studentModel
    ) {
        $this->studentModel = new StudentRepository($studentModel);
        $this->contractCode = new OnboardSettingRepository($contractCode);
        $this->contractFundingSource = new OnboardSettingRepository($contractFundingSource);
        $this->courseSite = new OnboardSettingRepository($courseSite);
        $this->elearningLink = new OnboardSettingRepository($elearningLink);
        $this->bankInfo = new OnboardSettingRepository($bankInfo);
        $this->report = new OnboardSettingRepository($report);
        $this->courses = new OnboardSettingRepository($courses);
        $this->studentLetter = new OnboardSettingRepository($studentLetter);
        $this->holiday = new OnboardSettingRepository($holiday);
        $this->courseTemplate = new OnboardSettingRepository($courseTemplate);
        $this->smsTemplate = new OnboardSettingRepository($smsTemplate);
        $this->reportsAccess = new OnboardSettingRepository($reportsAccess);
        $this->emailTemplate = new OnboardSettingRepository($emailTemplate);
        $this->reportLetterSetup = new OnboardSettingRepository($reportLetterSetup);
    }

    public function getContractCodeData($request)
    {
        return [
            'data' => $this->contractCode->getContractCodeData($request),
            'total' => $this->contractCode->getContractCodeData($request, true),
        ];
    }

    public function getContractFundingSourceData($request)
    {
        return [
            'data' => $this->contractCode->getContractFundingSourceData($request),
            'total' => $this->contractCode->getContractFundingSourceData($request, true),
        ];
    }

    public function getCourseSiteData($request)
    {
        return [
            'data' => $this->contractCode->getCourseSiteData($request),
            'total' => $this->contractCode->getCourseSiteData($request, true),
        ];
    }

    public function getElearningLinkData($request)
    {
        return [
            'data' => $this->elearningLink->getElearningLinkData($request),
            'total' => $this->elearningLink->getElearningLinkData($request, true),
        ];
    }

    public function failedEmailData($request)
    {
        return [
            'data' => $this->elearningLink->failedEmailData($request),
            'total' => $this->elearningLink->failedEmailData($request, true),
        ];
    }

    public function failedEmailDetail($request)
    {
        return FailedEmails::find($request['primaryId']);
    }

    public function manageReportsData($request)
    {
        return [
            'data' => $this->elearningLink->manageReportsData($request),
            'total' => $this->elearningLink->manageReportsData($request, true),
        ];
    }

    public function bankInfoData($request)
    {
        return [
            'data' => $this->bankInfo->bankInfoData($request),
            'total' => $this->bankInfo->bankInfoData($request, true),
        ];
    }

    public function studentReportLetterData($request)
    {
        return [
            'data' => $this->studentLetter->studentReportLetterData($request),
            'total' => $this->studentLetter->studentReportLetterData($request, true),
        ];
    }

    public function publicHolidayData($request)
    {
        return [
            'data' => $this->holiday->publicHolidayData($request),
            'total' => $this->holiday->publicHolidayData($request, true),
        ];
    }

    public function courseTemplateData($request)
    {
        return [
            'data' => $this->courseTemplate->courseTemplateData($request),
            'total' => $this->courseTemplate->courseTemplateData($request, true),
        ];
    }

    public function uploadedEmailTemplateDocumentsData($request)
    {
        return [
            'data' => $this->emailTemplate->uploadedEmailTemplateDocumentsData($request),
            'total' => $this->emailTemplate->uploadedEmailTemplateDocumentsData($request, true),
        ];
    }

    public function uploadedLetterTemplateDocumentsData($request)
    {
        return [
            'data' => $this->emailTemplate->uploadedLetterTemplateDocumentsData($request),
            'total' => $this->emailTemplate->uploadedLetterTemplateDocumentsData($request, true),
        ];
    }

    public function saveContractCode($data)
    {
        $data['college_id'] = Auth::user()->college_id;
        $data['created_by'] = $data['updated_by'] = Auth::user()->id;
        DB::beginTransaction();
        try {
            $res = $this->contractCode->create($data);
            DB::commit();

            return ['status' => 'success', 'message' => 'Contract Code Save Successfully.'];
        } catch (\Exception $e) {
            DB::rollBack();

            throw new ApplicationException($e->getMessage());
        }
    }

    public function saveContractFundingSource($data)
    {
        $data['college_id'] = Auth::user()->college_id;
        $data['created_by'] = $data['updated_by'] = Auth::user()->id;
        DB::beginTransaction();
        try {
            $res = $this->contractFundingSource->create($data);
            DB::commit();

            return ['status' => 'success', 'message' => 'Contract Funding Source Save Successfully.'];
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApplicationException($e->getMessage());
        }
    }

    public function updateContractFundingSourceData($data)
    {
        DB::beginTransaction();
        try {
            $res = $this->contractFundingSource->update($data, $data['id']);
            DB::commit();

            return ['status' => 'success', 'message' => 'Contract Funding Source Save Successfully.'];
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApplicationException($e->getMessage());
        }
    }

    public function updateContractCodeData($data)
    {
        $dataItem['contract_code'] = $data['contract_code'];
        $dataItem['description'] = $data['description'];
        DB::beginTransaction();
        try {
            $res = ContractCode::find($data['id'])->update($dataItem);
            DB::commit();

            return ['status' => 'success', 'message' => 'Contract Code Save Successfully.'];
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApplicationException($e->getMessage());
        }
    }

    public function saveCourseSite($data)
    {
        $data['college_id'] = Auth::user()->college_id;
        $data['created_by'] = $data['updated_by'] = Auth::user()->id;
        $data['is_active'] = ($data['is_active'] == 'Yes') ? 1 : 0;
        DB::beginTransaction();
        try {
            $res = $this->courseSite->create($data);
            DB::commit();

            return ['status' => 'success', 'message' => 'Course Site Save Successfully.'];
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApplicationException($e->getMessage());
        }
    }

    public function updateCourseSiteData($data)
    {
        $dataItem['course_site_id'] = $data['course_site_id'];
        $dataItem['course_site_name'] = $data['course_site_name'];
        $dataItem['is_active'] = ($data['is_active'] == 'Yes') ? 1 : 0;
        DB::beginTransaction();
        try {
            $res = CourseSite::find($data['id'])->update($dataItem);
            DB::commit();

            return ['status' => 'success', 'message' => 'Course Site Updated Successfully.'];
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApplicationException($e->getMessage());
        }
    }

    public function saveElearningLink($data)
    {
        $data['college_id'] = Auth::user()->college_id;
        $data['created_by'] = $data['updated_by'] = Auth::user()->id;
        DB::beginTransaction();
        try {
            if (isset($data['id'])) {
                $res = $this->elearningLink->update($data, $data['id']);
            } else {
                $res = $this->elearningLink->create($data);
            }
            DB::commit();

            return ['status' => 'success', 'message' => 'Elearning Link Save Successfully.'];
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApplicationException($e->getMessage());
        }
    }

    public function saveBankInfo($data)
    {
        $data['college_id'] = Auth::user()->college_id;
        $data['created_by'] = $data['updated_by'] = Auth::user()->id;
        DB::beginTransaction();
        try {
            if (isset($data['id'])) {
                $res = $this->bankInfo->update($data, $data['id']);
            } else {
                $res = $this->bankInfo->create($data);
            }
            DB::commit();

            //            return $this->successResponse('', 'data', $data);
            return ['status' => 'success', 'message' => 'Bank Info Save Successfully.'];

        } catch (\Exception $e) {
            DB::rollBack();

            throw new ApplicationException($e->getMessage());
        }
    }

    public function saveSmsTemplate($data)
    {
        $data['college_id'] = Auth::user()->college_id;
        $data['created_by'] = $data['updated_by'] = Auth::user()->id;
        DB::beginTransaction();
        try {
            if (isset($data['id'])) {
                $res = $this->smsTemplate->update($data, $data['id']);
            } else {
                $res = $this->smsTemplate->create($data);
            }
            DB::commit();

            return ['status' => 'success', 'message' => 'SMS Template Save Successfully.'];
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApplicationException($e->getMessage());
        }
    }

    public function saveEmailTemplate($data)
    {
        $data['college_id'] = Auth::user()->college_id;
        $data['status'] = 1;
        $data['created_by'] = $data['updated_by'] = Auth::user()->id;
        DB::beginTransaction();
        try {
            if (isset($data['id'])) {
                $res = $this->emailTemplate->update($data, $data['id']);
            } else {
                $res = $this->emailTemplate->create($data);
            }
            DB::commit();

            return ['status' => 'success', 'message' => 'Email Template Save Successfully.'];
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApplicationException($e->getMessage());
        }
    }

    public function saveLetterTemplate($data)
    {

        $data['college_id'] = Auth::user()->college_id;
        $data['created_by'] = $data['updated_by'] = Auth::user()->id;
        DB::beginTransaction();
        try {
            if (isset($data['id'])) {
                $res = $this->reportLetterSetup->update($data, $data['id']);
            } else {
                $data['track_letter'] = ($data['track_letters'] == 'Yes') ? 1 : 0;
                $res = $this->reportLetterSetup->create($data);
            }
            DB::commit();

            return ['status' => 'success', 'message' => 'Letter Template Save Successfully.'];
        } catch (\Exception $e) {
            DB::rollBack();

            throw new ApplicationException($e->getMessage());
        }
    }

    public function saveHolidayDetails($data)
    {
        $data['college_id'] = Auth::user()->college_id;
        $data['created_by'] = $data['updated_by'] = Auth::user()->id;
        $data['holiday_date'] = date('Y-m-d', strtotime($data['holiday_date']));
        DB::beginTransaction();
        try {
            if (isset($data['id'])) {
                $res = $this->holiday->update($data, $data['id']);
            } else {
                if (is_array($data['state_id'])) {
                    foreach ($data['state_id'] as $key => $value) {
                        $data['state_id'] = $value;
                        $res = $this->holiday->create($data);
                    }
                } else {
                    $res = $this->holiday->create($data);
                }
            }
            DB::commit();

            return ['status' => 'success', 'message' => 'Data Save Successfully Saved.'];

        } catch (\Exception $e) {
            DB::rollBack();

            throw new ApplicationException($e->getMessage());
        }
    }

    public function updateReportData($data)
    {
        $data['college_id'] = Auth::user()->college_id;
        $data['created_by'] = $data['updated_by'] = Auth::user()->id;
        $deleteOldRecords = $this->reportsAccess->whereDelete(['report_id' => $data['report_id']]);
        DB::beginTransaction();
        try {
            if (is_array($data['role_id'])) {
                foreach ($data['role_id'] as $key => $value) {
                    $data['role_id'] = $value;
                    $genrateNew = $this->reportsAccess->create($data);
                }
            } else {
                $genrateNew = $this->reportsAccess->create($data);
            }
            DB::commit();

            //            return $genrateNew;
            return ['status' => 'success', 'message' => 'Data Updated Successfully.'];

        } catch (\Exception $e) {
            DB::rollBack();

            throw new ApplicationException($e->getMessage());
        }
    }

    public function saveCourseTemplate($data)
    {
        $data['set_default'] = (isset($data['set_default']) && $data['set_default'] == 'on') ? 1 : 0;
        $data['set_active'] = (isset($data['set_active']) && $data['set_active'] == 'on') ? 1 : 0;
        $data['college_id'] = Auth::user()->college_id;
        $data['created_by'] = $data['updated_by'] = Auth::user()->id;
        DB::beginTransaction();
        try {
            if (isset($data['id'])) {
                $res = $this->courseTemplate->update($data, $data['id']);
            } else {
                $res = $this->courseTemplate->create($data);
            }
            DB::commit();

            return $this->successResponse('Course Template Saved.', 'data', '');
        } catch (\Exception $e) {
            DB::rollBack();

            throw new ApplicationException($e->getMessage());
        }
    }

    public function updateProfileData($data)
    {
        $data['created_by'] = $data['updated_by'] = Auth::user()->id;
        if (isset($data['password'])) {
            $data['password'] = Hash::make($data['password']);
        }
        $data['first_time_reset_pwd'] = '1';
        $data['last_password_updated_date'] = date('Y-m-d');
        DB::beginTransaction();
        try {
            Users::find($data['user_id'])->update($data);
            DB::commit();

            return ['status' => 'success', 'message' => 'Data Save Successfully Saved.'];
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApplicationException($e->getMessage());
        }
    }

    public function deleteContractCode($data)
    {
        DB::beginTransaction();
        try {
            $res = $this->contractCode->delete($data['id']);
            DB::commit();

            return ['status' => 'success', 'message' => 'Contract Code Delete Successfully.'];
        } catch (\Exception $e) {
            DB::rollBack();

            throw new ApplicationException($e->getMessage());
        }
    }

    public function deleteContractFundingSource($data)
    {
        DB::beginTransaction();
        try {
            $res = $this->contractFundingSource->delete($data['id']);
            DB::commit();

            return ['status' => 'success', 'message' => 'Contract Funding Source Delete Successfully.'];
        } catch (\Exception $e) {
            DB::rollBack();

            throw new ApplicationException($e->getMessage());
        }
    }

    public function deleteCourseSite($data)
    {
        DB::beginTransaction();
        try {
            $res = $this->courseSite->delete($data['id']);
            DB::commit();

            return ['status' => 'success', 'message' => 'Course Site Delete Successfully.'];
        } catch (\Exception $e) {
            DB::rollBack();

            throw new ApplicationException($e->getMessage());
        }
    }

    public function deleteElearningLink($data)
    {
        DB::beginTransaction();
        try {
            $res = $this->elearningLink->delete($data['id']);
            DB::commit();

            return ['status' => 'success', 'message' => 'Elearning Link Delete Successfully.'];
        } catch (\Exception $e) {
            DB::rollBack();

            throw new ApplicationException($e->getMessage());
        }
    }

    public function deleteBankInfo($data)
    {
        DB::beginTransaction();
        try {
            $res = $this->bankInfo->delete($data['id']);
            DB::commit();

            return ['status' => 'success', 'message' => 'Bank Info Delete Successfully.'];
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApplicationException($e->getMessage());
        }
    }

    public function getBankInfo($data)
    {
        DB::beginTransaction();
        try {
            $res = $this->bankInfo->find($data['id']);
            DB::commit();

            return ['status' => 'success', 'message' => 'Bank Info fond Successfully.', 'data' => $res];
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApplicationException($e->getMessage());
        }
    }

    public function deleteHoliday($data)
    {
        DB::beginTransaction();
        try {
            $res = $this->holiday->delete($data['id']);
            DB::commit();

            return ['status' => 'success', 'message' => 'Holiday Delete Successfully.'];
        } catch (\Exception $e) {
            DB::rollBack();

            throw new ApplicationException($e->getMessage());
        }
    }

    public function deleteCourseTemplate($data)
    {
        DB::beginTransaction();
        try {
            $res = $this->courseTemplate->delete($data['id']);
            DB::commit();

            return ['status' => 'success', 'message' => 'Course Template Delete Successfully.'];
        } catch (\Exception $e) {
            DB::rollBack();

            throw new ApplicationException($e->getMessage());
        }
    }

    public function getReportName($data)
    {
        return $this->report->getWhere(['report_category' => $data['category_id']], ['report_name as Name', 'id as Id']);
    }

    public function reportName($Id)
    {
        return $this->report->getWhereVal(['id' => $Id], 'report_name');
    }

    public function getSmsTemplateData($Id)
    {
        // return $this->smsTemplate->getWhere(['college_id' => Auth::user()->college_id], 'rto_sms_template.*');
        $collegeId = Auth::user()->college_id;

        return SmsTemplate::where('college_id', $collegeId)->orderBy('template_name', 'ASC')->get()->toArray();
    }

    public function generateAgentCommissionReport($data)
    {
        return $this->report->generateAgentCommissionReport($data);
    }

    public function generateStudentInitialPaymentReport($data)
    {
        return $this->report->generateStudentInitialPaymentReport($data);
    }

    public function getCourseInvoiceDue($data)
    {
        return $this->report->getCourseInvoiceDue($data);
    }

    public function generateTuitionFeeInvoiceReport($data)
    {
        return $this->report->generateTuitionFeeInvoiceReport($data);
    }

    public function getAgentComissionPaid($data)
    {
        return $this->report->getAgentComissionPaid($data);
    }

    public function generatePaymentRefundReport($data)
    {
        return $this->report->generatePaymentRefundReport($data);
    }

    public function getPaymentModeData($data)
    {
        return $this->report->getPaymentModeData($data);
    }

    public function generateApplicationReport($data)
    {
        return $this->report->generateApplicationReport($data);
    }

    public function generateEnrollmentReport($data)
    {
        return $this->report->generateEnrollmentReport($data);
    }

    public function getAgentCommissionOwingDetails($data)
    {
        return $this->report->getAgentCommissionOwingDetails($data);
    }

    public function getAgentConversionReport($data)
    {
        return $this->report->getAgentConversionReport($data);
    }

    public function getAgentListByStatusAndCountry($data)
    {
        return $this->report->getAgentListByStatusAndCountry($data);
    }

    public function getStudentCountByStatus($data)
    {
        return $this->report->getStudentCountByStatus($data);
    }

    public function getStudentCountByCourse($data)
    {
        return $this->report->getStudentCountByCourse($data);
    }

    public function getStudentOfferList($data)
    {
        return $this->report->getStudentOfferList($data);
    }

    public function getMissingDocuments($data)
    {
        return $this->report->getMissingDocuments($data);
    }

    public function getStudentOfferListNotEnrolled($data)
    {
        return $this->report->getStudentOfferListNotEnrolled($data);
    }

    public function getStudentListByIntakeDate($data)
    {
        return $this->report->getStudentListByIntakeDate($data);
    }

    public function getStudentListCompletedCourse($data)
    {
        return $this->report->getStudentListCompletedCourse($data);
    }

    public function getStudentsFailingConsecutiveSemester($data)
    {
        return $this->report->getStudentsFailingConsecutiveSemester($data);
    }

    public function getStudentsFailingOneSemester($data)
    {
        return $this->report->getStudentsFailingOneSemester($data);
    }

    public function getAttendanceMissingList($data)
    {
        return $this->report->getAttendanceMissingList($data);
    }

    public function getStudentAttendanceByCourseClass($data)
    {
        return $this->report->getStudentAttendanceByCourseClass($data);
    }

    public function getStudentContactUploadReport($data)
    {
        return $this->report->getStudentContactUploadReport($data);
    }

    public function getStudentListByCourseClass($data, $classStart, $classFinish)
    {
        return $this->report->getStudentListByCourseClass($data, $classStart, $classFinish);
    }

    public function getStudentListMissingUSINumbers($data)
    {
        return $this->report->getStudentListMissingUSINumbers($data);
    }

    public function getStudentListForUnitAssessmentGrade($data)
    {
        return $this->report->getStudentListForUnitAssessmentGrade($data);
    }

    public function getCountryList($data)
    {
        return Country::where('college_id', '=', $data['college_id'])->orwhere('college_id', '=', 0)->orderby('name', 'asc')->pluck('name', 'id')->toArray();
    }

    public function getCourseByType($data)
    {
        $courses = $this->courses->getWhere(['course_type_id' => $data['course_type_id'], 'activated_now' => '1', 'college_id' => Auth::user()->college_id], ['id as Id', DB::raw("CONCAT(course_code,':' ,course_name) as Name")]);

        return $courses;
    }

    public function getCourseYear($data)
    {
        $year = StudentCourses::where('course_type_id', '=', $data['course_type'])->select('intake_year as Name', 'intake_year as Id');
        if ($data['course_id'] != 'All') {
            $year->where('course_id', '=', $data['course_id']);
        }

        return $year->groupBy('intake_year')->get()->toArray();
    }

    public function getCourseIntakeDate($data)
    {
        $intakeDate = StudentCourses::where('course_type_id', '=', $data['course_type'])->where('intake_year', '=', $data['year'])->select('intake_date as Name', 'intake_date as Id');
        if ($data['course_id'] != 'All') {
            $intakeDate->where('course_id', '=', $data['course_id']);
        }

        return $intakeDate->groupBy('intake_date')->get()->toArray();
    }

    public function getCourseSemester($data)
    {
        $semester = Semester::from('rto_semester as rs')
            ->leftjoin('rto_course_calendar as rcc', 'rs.calendar_type', '=', 'rcc.calendar_type')
            ->leftjoin('rto_student_subject_enrolment as rsse', 'rsse.semester_id', '=', 'rs.id')
            ->where('rs.college_id', '=', Auth::user()->college_id)
            ->where('rs.course_type_id', '=', $data['course_type'])
            ->select('rs.semester_name as Name', 'rs.id as Id');
        if ($data['course_id'] != 'All') {
            $semester->where('rsse.course_id', '=', $data['course_id']);
        }

        return $semester->groupBy('rs.id')->get()->toArray();
    }

    public function getCourseTerm($data)
    {
        return SemesterDivision::where('college_id', '=', Auth::user()->college_id)
            ->where('semester_id', '=', $data['semester_id'])
            ->select('term as Name', 'term as Id')
            ->groupBy('term')->get()->toArray();
    }

    public function getCourseSubject($data)
    {
        return Timetable::from('rto_timetable as rt')
            ->join('rto_subject as rs', 'rs.id', 'rt.subject_id')
            ->where('rt.college_id', '=', Auth::user()->college_id)
            ->where('rt.semester_id', '=', $data['semester_id'])
            ->where('rt.term', '=', $data['term'])
            ->select(DB::raw("CONCAT(rs.subject_code,':' ,rs.subject_name) as Name"), 'rs.id as Id')
            ->groupBy('rt.subject_id')
            ->get()->toArray();
    }

    public function getCourseBatch($data)
    {
        return Timetable::from('rto_timetable as rt')
            ->leftjoin('rto_subject as rs', 'rs.id', '=', 'rt.subject_id')
            ->where('rt.college_id', '=', Auth::user()->college_id)
            ->where('rt.semester_id', '=', $data['semester_id'])
            ->where('rt.subject_id', '=', $data['subject_id'])
            ->groupBy('rt.batch')
            ->select('rt.batch as Id', 'rt.batch as Name')
            ->get()
            ->toArray();
    }

    public function getCourseClass($data)
    {
        return Timetable::leftjoin('rto_subject as rs', 'rs.id', '=', 'rto_timetable.subject_id')
            ->where('rto_timetable.college_id', '=', Auth::user()->college_id)
            ->where('rto_timetable.semester_id', '=', $data['semester_id'])
            ->where('rto_timetable.subject_id', '=', $data['subject_id'])
            ->where('rto_timetable.batch', '=', $data['batch'])
            ->where('rto_timetable.term', '=', $data['term'])
            ->groupBy('rto_timetable.batch')
            ->select(DB::raw("CONCAT(start_week,':' ,end_week) as Id"), DB::raw("CONCAT(rs.subject_code,': Week(', rto_timetable.start_week_id ,'-', rto_timetable.end_week_id,')') as Name"))
            ->get()
            ->toArray();
    }

    public function getReportLetterList($data)
    {
        return ReportLetterSetup::where('report_id', '=', $data['report_id'])->select('letter_name  as Name', 'id as Id')->orderBy('letter_name', 'ASC')->get()->toarray();
    }

    public function getReportLetterDetails($data)
    {
        return ReportLetterSetup::where('college_id', Auth::user()->college_id)->where('id', $data['report_letter_id'])->value('letter_editer');
    }

    public function saveReportLetter($data)
    {
        $data['college_id'] = Auth::user()->college_id;
        $data['category_id'] = $data['letter_category_id'];
        $data['report_id'] = $data['letter_report_id'];
        $data['course_type'] = $data['letter_course_type'];
        $data['course_id'] = $data['letter_course_id'];
        $data['semester_id'] = $data['letter_semester_id'];
        $data['created_by'] = $data['update_by'] = Auth::user()->id;
        $data['status'] = 'Letter Enail Send';
        $staudentIds = (explode(',', $data['student_id']));
        DB::beginTransaction();
        try {
            foreach ($staudentIds as $staudentId) {
                $data['student_id'] = $staudentId;
                $res = $this->studentLetter->create($data);
            }
            DB::commit();

            return ['status' => 'success', 'message' => 'Data Save Successfully Saved.'];
        } catch (\Exception $e) {
            DB::rollBack();

            throw new ApplicationException($e->getMessage());
        }

    }

    public function getEmailTemplateTitleData($data)
    {
        // dd($data);
        $arrEmailTemplate = EmailTemplate::where('rto_email_template.college_id', Auth::user()->college_id)
            ->where('rto_email_template.template_name', Config::get('constants.'.$data['template_name']))
            ->leftjoin('rto_users as ru', 'ru.id', '=', 'rto_email_template.updated_by')
            ->leftjoin('rto_email_template_documents', 'rto_email_template_documents.email_template_id', '=', 'rto_email_template.id')
            ->select('rto_email_template.*', 'ru.name', 'rto_email_template_documents.id as hasFile', DB::raw('GROUP_CONCAT(rto_email_template_documents.file SEPARATOR "<br>") AS fileNames'))
            ->groupBy('rto_email_template.id')
            ->orderBy('rto_email_template.template_name', 'ASC')
            ->get()
            ->toArray();
        if (! empty($arrEmailTemplate) && isset($data['student_id'])) {
            $arrEmailTemplate[0]['content'] = $this->studentModel->setLetterBodyContent($data['student_id'], '', $arrEmailTemplate[0]['content']);
        }

        return $arrEmailTemplate;
    }

    public function getEmailTemplateData($data)
    {
        $arrEmailTemplate = EmailTemplate::where('rto_email_template.college_id', Auth::user()->college_id)
            ->leftjoin('rto_users as ru', 'ru.id', '=', 'rto_email_template.updated_by')
            ->leftjoin('rto_email_template_documents', 'rto_email_template_documents.email_template_id', '=', 'rto_email_template.id')
            ->select('rto_email_template.*', 'ru.name', 'rto_email_template_documents.id as hasFile', DB::raw('GROUP_CONCAT(rto_email_template_documents.file SEPARATOR "<br>") AS fileNames'))
            ->groupBy('rto_email_template.id')
            ->orderBy('rto_email_template.template_name', 'ASC')
            ->get()
            ->toArray();

        return $arrEmailTemplate;
    }

    public function getLetterTemplateData($data)
    {
        $arrLetterTemplate = ReportLetterSetup::where('rto_letter_setup.college_id', Auth::user()->college_id)
            ->leftjoin('rto_reports', 'rto_reports.id', '=', 'rto_letter_setup.report_id')
            ->leftjoin('rto_colleges', 'rto_colleges.id', '=', 'rto_letter_setup.college_id')
            ->leftjoin('rto_letter_file_attachment', 'rto_letter_file_attachment.letter_id', '=', 'rto_letter_setup.id')
            ->leftjoin('rto_users', 'rto_users.id', '=', 'rto_letter_setup.created_by')
            ->select('rto_letter_setup.*', 'rto_reports.report_name', 'rto_colleges.college_name', 'rto_users.name as modifiedBy', DB::raw('COUNT(rto_letter_file_attachment.id) AS fileCount'))
            ->groupBy('rto_letter_setup.id')
            ->orderBy('rto_letter_setup.letter_name', 'ASC')
            ->get()
            ->toArray();

        return $arrLetterTemplate;
    }

    public function uploadEmailTemplateDocument($request)
    {

        $collegeId = Auth::user()->college_id;
        $req = $request->input('metadata');
        $uploadData = json_decode($req, true);
        $emailTemplate = $request->file();
        $templateId = $request->email_template_id;
        DB::beginTransaction();
        try {
            if (isset($emailTemplate['attachments'])) {
                $file = $emailTemplate['attachments'];
                $filePath = Config::get('constants.uploadFilePath.Templates');
                $destinationPath = Helpers::changeRootPath($filePath, $templateId);
                $filename = hashFileName($uploadData['fileName']);
                // $res = $file->move($destinationPath['default'], $filename);
                $res = UploadService::uploadAs($destinationPath['view'], $file, $filename);
                info('file uploaded form Email default attachment.', [$res]);

                if ($res && ! empty($collegeId)) {
                    EmailTemplateDocuments::create(['file' => $filename, 'email_template_id' => $templateId]);
                    DB::commit();
                    echo json_encode(['uploaded' => true, 'fileUid' => $filename, 'status' => 'success', 'message' => 'Email successfully.']);
                    exit;
                }
            } else {
                echo json_encode(['status' => 'error', 'message' => 'Something will be wrong. Please try again.']);
                exit;
            }
        } catch (\Exception $e) {
            DB::rollBack();

            throw new ApplicationException($e->getMessage());
        }

    }

    public function deleteEmailTemplateDocumentData($request)
    {
        $getRecord = EmailTemplateDocuments::find($request['id']);
        DB::beginTransaction();
        try {
            if ($getRecord) {
                $filePath = Config::get('constants.uploadFilePath.Templates');
                $destinationPath = Helpers::changeRootPath($filePath, $getRecord->email_template_id);
                $dirPath = str_replace('\\', '/', $destinationPath['default']).$getRecord->file;
                if (is_file($dirPath)) {
                    unlink($dirPath);
                }
                EmailTemplateDocuments::destroy($request['id']);
                DB::commit();

                return ['status' => 'success', 'message' => 'Delete successfully.'];
            }
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApplicationException($e->getMessage());
        }

    }

    public function deleteLetterTemplateDocumentData($request)
    {
        $getRecord = ReportLetterFileAttachment::find($request['id']);
        DB::beginTransaction();
        try {
            if ($getRecord) {
                $filePath = Config::get('constants.uploadFilePath.LetterFile');
                $destinationPath = Helpers::changeRootPath($filePath, $getRecord->letter_id);
                $dirPath = str_replace('\\', '/', $destinationPath['default']).$getRecord->letter_attachment;
                if (is_file($dirPath)) {
                    unlink($dirPath);
                }
                ReportLetterFileAttachment::destroy($request['id']);
                DB::commit();

                return ['status' => 'success', 'message' => 'Delete successfully.'];
            }
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApplicationException($e->getMessage());
        }

    }

    public function uploadLetterTemplateDocument($request)
    {

        $collegeId = Auth::user()->college_id;
        $req = $request->input('metadata');
        $uploadData = json_decode($req, true);
        $letterTemplate = $request->file();
        $letterId = $request->letter_id;
        DB::beginTransaction();
        try {
            if (isset($letterTemplate['attachments'])) {
                $file = $letterTemplate['attachments'];
                $filePath = Config::get('constants.uploadFilePath.LetterFile');
                $destinationPath = Helpers::changeRootPath($filePath, $letterId);
                $filename = hashFileName($uploadData['fileName']);
                // $res = $file->move($destinationPath['default'], $filename);
                $res = UploadService::uploadAs($destinationPath['view'], $file, $filename);
                info('file uploaded form Letter default attachment ', [$res]);

                if ($res && ! empty($collegeId)) {
                    ReportLetterFileAttachment::create(['letter_attachment' => $filename, 'letter_id' => $letterId, 'college_id' => $collegeId, 'created_by' => Auth::user()->id, 'updated_by' => Auth::user()->id]);
                    DB::commit();
                    echo json_encode(['uploaded' => true, 'fileUid' => $filename, 'status' => 'success', 'message' => 'Email successfully.']);
                    exit;
                }
            } else {
                echo json_encode(['status' => 'error', 'message' => 'Something will be wrong. Please try again.']);
                exit;
            }
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApplicationException($e->getMessage());
        }

    }

    public function uploadUserProfilePicture($userId, $profilePicture, $originalFileName)
    {

        $collegeId = Auth::user()->college_id;
        $filePath = Config::get('constants.uploadFilePath.Users');
        $destinationPath = Helpers::changeRootPath($filePath, null, $collegeId);
        $userName = str_replace(' ', '_', $originalFileName);
        $filename = time().$userName;

        $this->generateThumbImageS3($profilePicture, $destinationPath, $filename);
        // $res = $profilePicture->move($destinationPath['default'], $filename);

        $res = UploadService::uploadAs($destinationPath['view'], $profilePicture, $filename);
        info('Profile Picture uploaded ', [$res]);

        DB::beginTransaction();
        try {
            if ($res && ! empty($collegeId)) {
                Users::find($userId)->update(['profile_picture' => $filename]);
                $picture_url = asset($destinationPath['view'].$filename);
                echo json_encode(['uploaded' => true, 'fileUid' => $filename, 'url' => $picture_url, 'status' => 'success', 'message' => 'Profile Picture Uploaded Successfully.']);
                DB::commit();
                exit;

                // return ['uploaded' => true, 'fileUid' => $filename, 'status' => 'success', 'message' => 'Profile Picture Uploaded Successfully.'];
            }

            return ['status' => 'error', 'message' => 'Something went wrong. Please try again.'];
        } catch (\Exception $e) {
            throw new ApplicationException($e->getMessage());
        }
    }

    private function generateThumbImage($file, $destinationPath, $fileName)
    {
        if (! is_dir($destinationPath)) {
            mkdir($destinationPath, 0777);
        }

        $thumbText = Config::get('constants.defaultThumbText');
        $thumbSize = Config::get('constants.defaultThumbSize');

        $imgFile = Image::make($file->getRealPath());
        $thumbFileName = $thumbText.$fileName;

        $fullTempFilePath = $destinationPath.$thumbFileName;
        $imgFile->resize($thumbSize, $thumbSize, function ($constraint) {
            $constraint->aspectRatio();
        })->save($fullTempFilePath);

        $upload_success = UploadService::uploadAs($destinationPath, new \Illuminate\Http\File($fullTempFilePath), $thumbFileName);
        info('file uploaded form COE', [$upload_success]);
        unlink($fullTempFilePath);

    }

    public function getCoursesUpfrontFeeList($request)
    {
        return [
            'data' => $this->contractCode->getCoursesUpfrontFeeListData($request),
            'total' => $this->contractCode->getCoursesUpfrontFeeListData($request, true),
        ];

    }

    public function getFullCoursesArrList()
    {
        return Courses::join('rto_course_campus', 'rto_course_campus.course_id', '=', 'rto_courses.id')
            ->select('rto_courses.id', 'rto_courses.course_name', 'rto_courses.course_code')
            ->where('rto_courses.status', 1)
            ->where('rto_courses.activated_now', 1)
            ->where('rto_courses.college_id', Auth::user()->college_id)
            ->select(DB::raw("CONCAT(rto_courses.course_code,' : ',rto_courses.course_name) as Name"), 'rto_courses.id as Id')
            ->groupBy('rto_courses.id')
            ->get()->toArray();
    }

    public function getCourseFeeData($request)
    {

        return Courses::select('id', 'course_name', 'tuition_fee', 'course_duration', 'couse_duration_type', 'course_code')
            ->where('id', $request['courseId'])
            ->Where('college_id', Auth::user()->college_id)
            ->get()
            ->toArray();
    }

    public function saveCourseUpfrontFeeData($data)
    {
        $data['college_id'] = Auth::user()->college_id;
        $data['created_by'] = $data['updated_by'] = Auth::user()->id;
        $courseCount = CoursesUpfrontFee::where('college_id', $data['college_id'])
            ->where('agent_id', '=', $data['agent_id'])
            ->where('course_id', '=', $data['course_id'])
            ->whereIn('student_type', $data['orgin'])
            ->count();
        $totalOrigins = $data['orgin'];

        if ($courseCount != 0) {
            return ['status' => 'error', 'message' => 'Course Upfront Fee is already exists for selected course and agent.'];
        }
        DB::beginTransaction();
        try {
            if ($courseCount == 0) {
                foreach ($totalOrigins as $i => $totalOrigin) {
                    $data['student_type'] = $totalOrigins[$i];
                    $res = CoursesUpfrontFee::create($data);
                }
            }
            DB::commit();

            return ['status' => 'success', 'message' => 'Course Upfront Fee is Saved Successfully.'];
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApplicationException($e->getMessage());
        }
    }

    public function getUpFrontFeeDetailForEditData($request)
    {

        return CoursesUpfrontFee::select('id', 'course_id', 'upfront_fee', 'student_type', 'no_of_installments', 'frequency', 'material_fee')
            ->where('id', $request['id'])
            ->get();
    }

    public function editCourseUpfrontFeeData($data)
    {
        $primaryId = $data['id'];
        unset($data['id']);
        DB::beginTransaction();
        try {
            $res = CoursesUpfrontFee::where('id', $primaryId)->update($data);
            DB::commit();

            return ['status' => 'success', 'message' => 'Course Upfront Fee is Edit Successfully.'];
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApplicationException($e->getMessage());
        }
    }

    public function deleteUpfrontFeeData($data)
    {
        $college_id = Auth::user()->college_id;
        $CoursesUpfrontFee = CoursesUpfrontFee::where('id', $data['id'])->get();
        $coursesCoursesUpfrontFeeId = $CoursesUpfrontFee[0]->college_id;
        DB::beginTransaction();
        try {

            if ($coursesCoursesUpfrontFeeId == $college_id) {
                $res = CoursesUpfrontFee::Where('id', $data['id'])->delete();
            }

            DB::commit();

            return ['status' => 'success', 'message' => 'Course Upfront Fee is Delete Successfully.'];
        } catch (\Exception $e) {
            DB::rollBack();

            throw new ApplicationException($e->getMessage());
        }
    }

    public function getElicosDiscountWeekList($request)
    {
        return [
            'data' => $this->contractCode->getElicosDiscountWeekListData($request),
            'total' => $this->contractCode->getElicosDiscountWeekListData($request, true),
        ];

    }

    public function saveElicosDiscountWeekData($data)
    {
        $data['college_id'] = Auth::user()->college_id;
        $data['created_by'] = $data['updated_by'] = Auth::user()->id;
        $data['from_date'] = (date('Y-m-d', strtotime($data['from_date'])));
        $data['to_date'] = (date('Y-m-d', strtotime($data['to_date'])));

        $ElicosCount = ElicosDiscount::where('course_id', $data['course_id'])->count();
        DB::beginTransaction();
        try {
            $res = '';
            if ($ElicosCount == 0) {
                $res = ElicosDiscount::create($data);
            }
            DB::commit();

            return ['status' => 'success', 'message' => 'Elicos Discount Successfully.'];
        } catch (\Exception $e) {
            DB::rollBack();

            throw new ApplicationException($e->getMessage());
        }
    }

    public function getElicosDiscountWeekData($request)
    {

        return ElicosDiscount::find($request['id']);
    }

    public function editElicosDiscountWeek($data)
    {
        $data['college_id'] = Auth::user()->college_id;
        $data['created_by'] = $data['updated_by'] = Auth::user()->id;
        $data['from_date'] = (date('Y-m-d', strtotime($data['from_date'])));
        $data['to_date'] = (date('Y-m-d', strtotime($data['to_date'])));

        $primaryId = $data['id'];
        unset($data['id']);
        DB::beginTransaction();
        try {
            $res = ElicosDiscount::where('id', $primaryId)->update($data);
            DB::commit();

            return ['status' => 'success', 'message' => 'Elicos Discount Updated Successfully.'];
        } catch (\Exception $e) {
            DB::rollBack();

            throw new ApplicationException($e->getMessage());
        }
    }

    public function deleteElicosDiscountWeekData($data)
    {
        DB::beginTransaction();
        try {

            $res = ElicosDiscount::where('id', $data['id'])->delete();
            DB::commit();

            return ['status' => 'success', 'message' => 'Elicos Discount Deleted Successfully.'];
        } catch (\Exception $e) {
            DB::rollBack();

            throw new ApplicationException($e->getMessage());
        }
    }

    public function getCoursePromotionPriceList($request)
    {
        return [
            'data' => $this->contractCode->getCoursePromotionPriceListData($request),
            'total' => $this->contractCode->getCoursePromotionPriceListData($request, true),
        ];

    }

    public function saveCoursePromotionPriceData($data)
    {

        $data['college_id'] = Auth::user()->college_id;
        $data['created_by'] = $data['updated_by'] = Auth::user()->id;
        $data['from_date'] = (date('Y-m-d', strtotime($data['from_date'])));
        $data['to_date'] = (date('Y-m-d', strtotime($data['to_date'])));

        DB::beginTransaction();
        try {
            $res = PromotionPrice::create($data);
            DB::commit();

            return ['status' => 'success', 'message' => 'Course Promotion Price Save Successfully.'];
        } catch (\Exception $e) {
            DB::rollBack();

            throw new ApplicationException($e->getMessage());
        }
    }

    public function getCoursePromotionPriceData($request)
    {

        return PromotionPrice::find($request['id']);
    }

    public function editCoursePromotionPrice($data)
    {
        $data['college_id'] = Auth::user()->college_id;
        $data['created_by'] = $data['updated_by'] = Auth::user()->id;
        $data['from_date'] = (date('Y-m-d', strtotime($data['from_date'])));
        $data['to_date'] = (date('Y-m-d', strtotime($data['to_date'])));

        $primaryId = $data['id'];
        unset($data['id']);
        DB::beginTransaction();
        try {
            $res = PromotionPrice::where('id', $primaryId)->update($data);
            DB::commit();

            return ['status' => 'success', 'message' => 'Course Promotion Price Update Successfully.'];
        } catch (\Exception $e) {
            DB::rollBack();

            throw new ApplicationException($e->getMessage());
        }
    }

    public function deleteCoursePromotionPriceData($data)
    {
        DB::beginTransaction();
        try {

            $res = PromotionPrice::where('id', $data['id'])->delete();
            DB::commit();

            return ['status' => 'success', 'message' => 'Course Promotion Price is Delete Successfully..'];
        } catch (\Exception $e) {
            DB::rollBack();

            throw new ApplicationException($e->getMessage());
        }
    }

    public function getCampusNameListData($request)
    {
        return CollegeCampus::where('college_id', Auth::user()->college_id)->where('status', 1)->select('name as Name', 'id as Id')->get()->toArray();

    }

    public function getCourseTypeListData($request)
    {
        return CourseType::whereIn('college_id', [0, Auth::user()->college_id])->where('status', 1)->select('title as Name', 'id as Id')->get()->toArray();

    }

    public function getCourseListByTypeData($request)
    {
        return Courses::select('id as value', DB::raw("CONCAT(course_code,' : ',course_name) as label"))
            ->where('college_id', Auth::user()->college_id)
            ->where('course_type_id', $request['courseType'])
            ->where('status', $request['courseActive'])
            ->where('activated_now', 1)
            ->get()
            ->toArray();

    }

    public function getCourseIntakeYearData($request)
    {
        $arrYear = [];
        for ($i = 2016; $i <= 2030; $i++) {
            $arrYear[$i] = $i;
        }
        $s = 0;
        foreach ($arrYear as $row) {
            $result[$s]['Id'] = $row;
            $result[$s]['Name'] = $row;
            $s++;
        }

        return $result;

    }

    public function saveCoursesIntakeDateAddData($request)
    {
        $data['created_by'] = Auth::user()->id;
        $data['college_id'] = Auth::user()->college_id;
        $data['updated_by'] = Auth::user()->id;
        $totalCourses_id = $request['course_id'];
        $totalIntakeStarts = empty($request['intakeYear']) ? $request['intake_start_selected'] : $request['intakeYear'];

        $year = ($request['course_type'] == 2) ? date('Y', strtotime($request['intakeYear'])) : $request['year'];
        $totalIntakeStartsArray = ($request['course_type'] == 2) ? (array) $totalIntakeStarts : (array) $totalIntakeStarts;
        $i = 0;
        $x = 0;
        $successCount = 0;
        $failCount = 0;
        foreach ($totalCourses_id as $totalCourseId) {
            foreach ($totalIntakeStartsArray as $intakeStart) {
                $data['course_id'] = $totalCourseId;
                $data['intake_year'] = $year;
                if (empty($request['intakeYear'])) {
                    $data['intake_end'] = $this->getEndDataByStartDate($totalCourseId, date('Y-m-d', strtotime($intakeStart)));
                    $data['intake_start'] = date('Y-m-d', strtotime($intakeStart));
                } else {
                    $data['intake_end'] = $this->getEndDataByStartDate($totalCourseId, date('Y-m-d', strtotime($request['intakeYear'])));
                    $data['intake_start'] = date('Y-m-d', strtotime($request['intakeYear']));
                }
                $intake_duration = $request['intake_duration'];
                if (empty($intake_duration)) {
                    $intake_duration = null;
                }
                $data['campus_id'] = $request['campus_id'];
                $data['course_type'] = $request['course_type'];
                $data['intake_name'] = $request['intake_name'];
                $data['intake_duration'] = $intake_duration;
                $data['intake_receiver'] = $request['intake_receiver'];
                $data['active'] = isset($request['publish']) ? ($request['publish'] == 'on') ? 1 : 0 : 0;
                try {
                    $checkCoursesIntakeDate = CoursesIntakeDate::where([
                        'course_type' => $request['course_type'],
                        'course_id' => $totalCourseId,
                        'intake_start' => date('Y-m-d', strtotime($intakeStart)),
                    ])->exists();
                    if (! $checkCoursesIntakeDate) {
                        CoursesIntakeDate::create($data);
                        $successCount++;
                        $x++;
                    } else {
                        $failCount++;
                    }
                } catch (\Exception $e) {
                    continue;
                }
            }
            $i++;
        }

        return ['status' => 'success', 'message' => $successCount.' is saved and '.$failCount.' already exists'];

    }

    // get end data by start date based on course duration
    public function getEndDataByStartDate($courseId, $startDate)
    {
        $courseDetails = Courses::find($courseId);
        if (! $courseDetails) {
            return null;
        }

        $typeName = '';
        switch ($courseDetails->couse_duration_type) {
            case Courses::DURATION_TYPE_DAILY:
                $typeName = 'day';
                break;
            case Courses::DURATION_TYPE_WEEKLY:
                $typeName = 'week';
                break;
            case Courses::DURATION_TYPE_MONTHLY:
                $typeName = 'month';
                break;
            case Courses::DURATION_TYPE_YEARLY:
                $typeName = 'year';
                break;
        }

        if (! empty($typeName)) {
            $endDate = date('Y-m-d', strtotime("$startDate +$courseDetails->course_duration $typeName"));

            return date('Y-m-d', strtotime("$endDate -1 day")); // TODO::GNG-2333
        }

        return null;
    }

    public function getCoursesIntakeListData($request)
    {

        $sql = CoursesIntakeDate::leftjoin('rto_courses', 'rto_course_intake_dates.course_id', '=', 'rto_courses.id')
            ->leftjoin('rto_college_course_type', 'rto_course_intake_dates.course_type', '=', 'rto_college_course_type.id')
            ->leftjoin('rto_campus', 'rto_course_intake_dates.campus_id', '=', 'rto_campus.id')
            ->orderBy('rto_course_intake_dates.id', 'desc')
            ->select('rto_course_intake_dates.*', 'rto_courses.course_name', 'rto_courses.course_code', 'rto_college_course_type.title', 'rto_campus.name', DB::raw("CONCAT(rto_courses.course_code,' : ',rto_courses.course_name) as Name"))
            ->where('rto_course_intake_dates.college_id', Auth::user()->college_id);

        if ($request['searchYear'] != 0) {
            $sql->where('rto_course_intake_dates.intake_year', $request['searchYear']);
        }
        if ($request['searchCourseId'] != 0) {
            $sql->where('rto_course_intake_dates.course_id', $request['searchCourseId']);
        }

        $getCoursesIntakeDate = $sql->get()->toArray();

        $res = [];
        foreach ($getCoursesIntakeDate as $key => $ci) {
            $duration = ($ci['intake_duration'] != '') ? $ci['intake_duration'] : 0;
            $ci['censusDate'] = date('Y-m-d', strtotime($ci['intake_start'].' + '.$duration.'days'));
            $ci['intake_start'] = date('d M Y', strtotime($ci['intake_start']));
            $res['course'][$ci['Name']][$ci['intake_year']]['intake_years'][] = $ci;
        }

        $finalArray = [];
        $j = 0;
        if ($res) {
            foreach ($res['course'] as $key => $val) {
                $finalArray[$j]['course_name'] = $key;
                foreach ($val as $l => $va) {
                    $finalArray[$j]['intakes'][] = [
                        'intake_year' => $l,
                        'intake_data' => $va['intake_years'],
                    ];
                }

                usort($finalArray[$j]['intakes'], function ($a, $b) {
                    return $b['intake_year'] <=> $a['intake_year'];
                });
                $j++;
            }
        }

        if (! empty($request['page'])) {
            if ($request['page'] == 1) {
                $array = array_slice($finalArray, 0, 3);
            } else {
                $array = array_slice($finalArray, $request['page'] * 2, 3);
            }
        } else {
            $array = $finalArray;
        }

        return $array;
    }

    public function deleteCoursesIntakeDateData($request)
    {

        $coursesIntakeData = CoursesIntakeDate::find($request['id']);
        $checkDataExist = StudentCourses::where(['course_id' => $coursesIntakeData['course_id'], 'intake_date' => $coursesIntakeData['intake_start']])->get()->toArray();
        if (empty($checkDataExist)) {
            $coursesIntakeData->delete();

            return ['status' => 'success', 'message' => 'Delete Successfully'];
        } else {
            return ['status' => 'error', 'message' => 'This Intake is Used'];
        }
    }

    public function deleteLetterTemplateData($request, $id)
    {
        $getRecord = ReportLetterSetup::findOrFail($id);
        DB::beginTransaction();
        try {
            if ($getRecord) {
                ReportLetterSetup::destroy($id);
                DB::commit();

                return ['status' => 'success', 'message' => 'Delete successfully.'];
            }
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApplicationException($e->getMessage());

            return ['status' => 'error', 'message' => 'Delete failed.'];
        }
    }

    public function deleteEmailTemplateData($request, $id)
    {
        $getRecord = EmailTemplate::findOrFail($id);
        DB::beginTransaction();
        try {
            if ($getRecord) {
                EmailTemplate::destroy($id);
                DB::commit();

                return ['status' => 'success', 'message' => 'Delete successfully.'];
            }
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApplicationException($e->getMessage());

            return ['status' => 'error', 'message' => 'Delete failed.'];
        }
    }

    public function deleteSmsTemplateData($request, $id)
    {
        $getRecord = SmsTemplate::findOrFail($id);
        DB::beginTransaction();
        try {
            if ($getRecord) {
                SmsTemplate::destroy($id);
                DB::commit();

                return ['status' => 'success', 'message' => 'Delete successfully.'];
            }
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApplicationException($e->getMessage());

            return ['status' => 'error', 'message' => 'Delete failed.'];
        }
    }
}
