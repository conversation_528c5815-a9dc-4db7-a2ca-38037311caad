<?php

namespace App\Services;

use <PERSON><PERSON>\Scout\Builder;

class ScoutFilterService
{
    public static function FilterStudentForList(Builder $scoutBuilder, $params = [])
    {

        self::FilterCourseStatusIntakeCombination($scoutBuilder, $params);

        if (count($params['campuses'])) {
            $scoutBuilder->whereIn('courses.campus_id', $params['campuses']);
        }

        if (count($params['student_type'])) {
            $scoutBuilder->whereIn('student_type', $params['student_type']);
        }

        if (count($params['nationality'])) {
            $scoutBuilder->whereIn('nationality', $params['nationality']);
        }
        if (count($params['teachers'])) {
            $scoutBuilder->whereIn('enrollments.teacher_id', $params['teachers']);
        }

        if (count($params['batches'])) {
            $scoutBuilder->whereIn('enrollments.batch', $params['batches']);
        }

        if (@$params['sort_by']) {
            $scoutBuilder->orderBy($params['sort_by'], $params['sort_direction']);
        }

        return $scoutBuilder;
    }

    public static function FilterCourseStatusIntakeCombination(Builder $scoutBuilder, $params)
    {
        // Handle all three filters: courses, status, and intake dates
        if (! empty($params['courses']) && ! empty($params['status']) && ! empty($params['intake_dates'])) {
            // Combine all three: course, status, and intake date
            $combinations = [];
            foreach ($params['courses'] as $course) {
                foreach ($params['status'] as $status) {
                    foreach ($params['intake_dates'] as $intake_date) {
                        $combinations[] = $course.'_'.$status.'_'.$intake_date;
                    }
                }
            }
            // Filter by combined field: id_with_status_with_intake_date
            $scoutBuilder->whereIn('courses.id_with_status_with_intake_date', $combinations);

            // Handle only courses and status
        } elseif (! empty($params['courses']) && ! empty($params['status'])) {
            $combinations = [];
            foreach ($params['courses'] as $course) {
                foreach ($params['status'] as $status) {
                    $combinations[] = $course.'_'.$status;
                }
            }
            // Filter by combined field: id_with_status
            $scoutBuilder->whereIn('courses.id_with_status', $combinations);

            // Handle only courses and intake dates
        } elseif (! empty($params['courses']) && ! empty($params['intake_dates'])) {
            $combinations = [];
            foreach ($params['courses'] as $course) {
                foreach ($params['intake_dates'] as $intake_date) {
                    $combinations[] = $course.'_'.$intake_date;
                }
            }
            // Filter by combined field: id_with_intake_date
            $scoutBuilder->whereIn('courses.id_with_intake_date', $combinations);

            // Handle individual filters if no combinations are needed
        } elseif (! empty($params['status']) && ! empty($params['intake_dates'])) {
            $combinations = [];
            foreach ($params['status'] as $status) {
                foreach ($params['intake_dates'] as $intake_date) {
                    $combinations[] = $status.'_'.$intake_date;
                }
            }
            // Filter by combined field: id_with_intake_date
            $scoutBuilder->whereIn('courses.status_with_intake_date', $combinations);

            // Handle individual filters if no combinations are needed
        } else {
            if (! empty($params['courses'])) {
                $scoutBuilder->whereIn('courses.course.id', $params['courses']);
            }
            if (! empty($params['status'])) {
                $scoutBuilder->whereIn('courses.status', $params['status']);
            }
            if (! empty($params['intake_dates'])) {
                $scoutBuilder->whereIn('courses.intake_date', $params['intake_dates']);
            }
        }

        return $scoutBuilder;
    }

    public static function FilterCourseStatusIntakeCombinationV2(Builder $scoutBuilder, $params)
    {
        if (count($params['courses']) == 1 && count($params['status']) == 1 && count($params['intake_dates']) == 1) {
            $scoutBuilder->where('courses.id_with_status_with_intake_date', $params['courses'][0].'_'.$params['status'][0].'_'.$params['intake_dates'][0]);
        } elseif (count($params['courses']) == 1 && count($params['status']) == 1) {
            $scoutBuilder->where('courses.id_with_status', $params['courses'][0].'_'.$params['status'][0]);
        } elseif (count($params['courses']) == 1 && count($params['intake_dates']) == 1) {
            $scoutBuilder->where('courses.id_with_intake_date', $params['courses'][0].'_'.$params['intake_dates'][0]);
        } else {

            if (count($params['courses'])) {
                $scoutBuilder->whereIn('courses.course.id', $params['courses']);
            }
            if (count($params['status'])) {
                $scoutBuilder->whereIn('courses.status', $params['status']);
            }
            if (count($params['intake_dates'])) {
                $scoutBuilder->whereIn('courses.intake_date', $params['intake_dates']);
            }
        }

        return $scoutBuilder;
    }
}
