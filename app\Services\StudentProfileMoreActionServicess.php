<?php

namespace App\Services;

use App\DTO\studentProfile\EditStudentSanction;
use App\DTO\studentProfile\SaveExitInterview;
use App\DTO\studentProfile\SaveStudentCourseDefer;
use App\DTO\studentProfile\SaveStudentCourseInformationPayload;
use App\DTO\studentProfile\SaveStudentOsHelpPayload;
use App\DTO\studentProfile\SaveStudentSaHelpPayload;
use App\DTO\studentProfile\SaveStudentSanction;
use App\DTO\studentProfile\SaveStudentTcsiDetailsPayload;
use App\Exceptions\ApplicationException;
use App\Helpers\Helpers;
use App\Model\v2\BroadType;
use App\Model\v2\CollegeDetails;
use App\Model\v2\Country;
use App\Model\v2\CreditProvidersCode;
use App\Model\v2\Language;
use App\Model\v2\NarrowType;
use App\Model\v2\Student;
use App\Model\v2\StudentCommunicationLog;
use App\Model\v2\StudentCourseExtensionHistory;
use App\Model\v2\StudentCourses;
use App\Model\v2\StudentCourseStatusHistory;
use App\Model\v2\StudentDeferCourse;
use App\Model\v2\StudentInterview;
use App\Model\v2\StudentSanction;
use App\Model\v2\StudentTcsiDetails;
use App\Model\v2\SubNarrowType;
use App\Model\v2\TcsiStudentCourseInfo;
use App\Model\v2\TcsiStudentCreditOffer;
use App\Model\v2\TcsiStudentDisability;
use App\Model\v2\TcsiStudentOsHelp;
use App\Model\v2\TcsiStudentSAHelp;
use App\Repositories\StudentProfileCommonRepository;
use App\Repositories\StudentProfileRepository;
use App\Repositories\StudentSummaryTabRepository;
use App\Traits\CommonTrait;
use App\Traits\SendNotificationTrait;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Notifications\Types\DTOs\CourseCancellationDTO;
use Notifications\Types\DTOs\StudentWithdrawalNotificationDTO;
use Notifications\Types\NotificationType;
use Support\Models\Activity;
use Support\Services\UploadService;

class StudentProfileMoreActionServicess
{
    use CommonTrait;
    use SendNotificationTrait;

    private $studentProfileCommonRepository;

    private $studentSummaryTabRepository;

    private $studentProfile;

    public function __construct(
        StudentProfileCommonRepository $studentProfileCommonRepository,
        StudentSummaryTabRepository $studentSummaryTabRepository,
        Student $student
    ) {
        $this->studentProfileCommonRepository = $studentProfileCommonRepository;
        $this->studentSummaryTabRepository = $studentSummaryTabRepository;
        $this->studentProfile = new StudentProfileRepository($student);
    }

    public function saveStudentSanctionData(SaveStudentSanction $data)
    {
        $postData = $data->toArray();
        DB::beginTransaction();
        try {
            StudentSanction::create($postData);
            DB::commit();

            return ['status' => 'success', 'message' => 'student sanction saved successfully.'];
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApplicationException($e->getMessage());
        }
    }

    public function getStudentSanctionListData($request, $countOnly = false)
    {
        $post = ($request->input()) ? $request->input() : [];
        $columnArr = [
            'rss.id',
            'rss.type',
            'rss.comment',
            'rss.active_date',
            'rss.is_show_msg',
            'rss.is_active'];
        $query = StudentSanction::from('rto_student_sanction as rss')->where('rss.college_id', $post['college_id'])->where('rss.student_id', $post['student_id'])->select($columnArr);
        $this->gridDataSorting($query, $post);
        $result = $this->gridDataPaginationV2($query, $post, $countOnly);
        $arrAcademic = Config::get('constants.arrSenctionType');
        if (! $countOnly) {
            foreach ($result['data'] as $k => $row) {
                $result['data'][$k]['type'] = $arrAcademic[$row['type']];
                $result['data'][$k]['active_date'] = date('d M Y', strtotime($row['active_date']));
            }
        }

        return $result;
    }

    public function getStudentSanctionDetailData($request)
    {
        return StudentSanction::findOrFail($request['id']);
    }

    public function editStudentSanctionData(EditStudentSanction $data)
    {
        $postData = $data->toArray();
        DB::beginTransaction();
        try {
            $id = $postData['id'];
            unset($postData['id']);
            StudentSanction::where('id', $id)->update($postData);
            DB::commit();

            return ['status' => 'success', 'message' => 'student sanction update successfully.'];
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApplicationException($e->getMessage());
        }
    }

    public function deleteStudentSanctionData($request)
    {
        DB::beginTransaction();
        try {
            $sanctions = StudentSanction::find($request['id']);
            if ($sanctions) {
                $sanctions->delete(); // ✅ Triggers the `deleting` boot method
            }

            DB::commit();

            return ['status' => 'success', 'message' => 'student sanction delete successfully.'];
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApplicationException($e->getMessage());
        }
    }

    public function getStudentExitInterviewListData($request, $countOnly = false)
    {

        $post = ($request->input()) ? $request->input() : [];
        $columnArr = [
            'rto_student_interview.*',
            'rto_courses.course_code',
            'rto_courses.course_name',
            DB::raw("(CASE WHEN rto_student_interview.exit_type='1' THEN 'Course Exit' ELSE 'College Exit' END) as exit_type"),
        ];

        $columns = [
            'id' => 'id',
            'reason' => 'reason',
            'comment' => 'comment',
        ];

        $query = StudentInterview::leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_interview.course_id')
            ->where('rto_student_interview.student_id', '=', $post['student_id'])
            ->orderBy('rto_student_interview.id', 'desc')
            ->select($columnArr);

        $this->gridDataFilter($query, $post, $columns);

        $this->gridDataSorting($query, $post);

        $result = $this->gridDataPaginationV2($query, $post, $countOnly);

        return $result;
    }

    public function saveExitInterviewData(SaveExitInterview $data)
    {
        $postData = $data->toArray();
        DB::beginTransaction();
        try {
            StudentInterview::create($postData);
            if ($postData['is_comm_log']) {
                $postData['type'] = 'added';
                $this->communicationLogForInterview($postData);
            }
            DB::commit();

            return ['status' => 'success', 'message' => 'Student exit interview saved successfully.'];
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApplicationException($e->getMessage());
        }
    }

    public function updateExitInterviewData(SaveExitInterview $data)
    {
        $postData = $data->toArray();
        DB::beginTransaction();
        try {
            StudentInterview::where('id', $postData['id'])->update($postData);
            if ($postData['is_comm_log']) {
                $postData['type'] = 'updated';

                $this->communicationLogForInterview($postData);
            }
            DB::commit();

            return ['status' => 'success', 'message' => 'Student exit interview updated successfully.'];
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApplicationException($e->getMessage());
        }
    }

    public function getExitInterviewData($request)
    {
        return StudentInterview::find($request['id']);

    }

    public function deleteStudentExitInterviewData($request)
    {
        DB::beginTransaction();
        try {
            StudentInterview::destroy($request['id']);
            DB::commit();

            return ['status' => 'success', 'message' => 'student interview delete successfully.'];
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApplicationException($e->getMessage());
        }
    }

    private function communicationLogForInterview($postData)
    {
        $userId = Auth::user()->id;
        $data['college_id'] = Auth::user()->college_id;
        $data['today_date'] = date('l,d F Y');
        $data['student_id'] = $postData['student_id'];
        $data['comment_by'] = $userId;
        $data['type'] = $postData['type'];
        $data['activity_log'] = 'interview';
        $data['log_type'] = 'profile';
        $data['status'] = '';
        $data['log'] = $postData['comment'];
        $data['created_by'] = $userId;
        $data['updated_by'] = $userId;
        StudentCommunicationLog::create($data);

        return true;
    }

    public function getStudentDetail($studentId)
    {

        $arrRoleType = Config::get('constants.arrRoleType');
        $studentRoleType = array_search('Student', $arrRoleType);

        return Student::join('rto_colleges', 'rto_colleges.id', '=', 'rto_students.college_id')
            ->join('rto_student_details as rsd', 'rsd.student_id', '=', 'rto_students.id')
            ->leftjoin('rto_users', function ($join) use ($studentRoleType) {
                $join->on('rto_users.username', '=', 'rto_students.generated_stud_id');
                $join->on('rto_users.role_id', '=', DB::raw($studentRoleType));
            })
            ->leftjoin('rto_student_courses as rsc', 'rto_students.id', '=', 'rsc.student_id')
            ->leftjoin('rto_agents', 'rto_agents.id', '=', 'rsc.agent_id')
            ->leftjoin('rto_country as sc', 'sc.id', '=', 'rto_students.current_country')
            ->leftjoin('rto_country as ac', 'ac.id', '=', 'rto_agents.office_country')
            ->leftjoin('rto_setup_section as rss1', 'rss1.id', '=', 'rto_students.visa_status')
            ->leftjoin('rto_setup_section as rss2', 'rss2.id', '=', 'rsd.EPL_test_name')
            ->where('rto_students.id', '=', $studentId)
            ->select('rto_colleges.college_logo', 'rto_students.*', 'rto_users.username', 'rsd.picup_fee', 'rsd.service_fee', 'rsd.OSHC_fee', 'rsd.account_manager_id', 'rsd.EPL_overall_score', 'rto_agents.agency_name', 'rto_agents.office_address as agent_address', 'rto_agents.office_city as agent_city', 'rto_agents.office_state as agent_state', 'rto_agents.office_postcode as agent_postcode', 'ac.name as agent_country', 'sc.nationality', 'sc.nationality as stud_nationality', 'sc.name as country_name', 'rss1.value as visaStatus', 'rss2.value as EPL_test_name')
            ->get();
    }

    public function getTcsiStudentDetailData($request)
    {
        $collegeId = $request['college_id'];
        $studentId = $request['student_id'];

        $data['arrCitizenshipCode'] = $this->convertConstantsFormat(Config::get('constants.arrCitizenshipCode'));
        $data['arrEmploymentStatus'] = $this->convertConstantsFormat(Config::get('constants.arrEmploymentStatus'));
        $data['arrEducationOfParent1'] = $this->convertConstantsFormat(Config::get('constants.arrEducationOfParent1'));
        $data['arrEducationOfParent2'] = $this->convertConstantsFormat(Config::get('constants.arrEducationOfParent2'));
        $data['arrLevelLeftSchool'] = $this->convertConstantsFormat(Config::get('constants.arrLevelLeftSchool'));
        $data['arrAttendedYear12'] = $this->convertConstantsFormat(Config::get('constants.arrAttendedYear12'));
        $data['arrAttendedHep'] = $this->convertConstantsFormat(Config::get('constants.arrAttendedHep'));
        $data['E561CreditBasisCode'] = $this->convertConstantsFormat(Config::get('constants.E561CreditBasisCode'));
        $data['E566CreditProviderCode'] = CreditProvidersCode::get([DB::raw('concat(provider_code, " - ", legal_name) as Name'), 'provider_code as Id']);
        $data['studentTcsiDetail'] = StudentTcsiDetails::where(['college_id' => $request['college_id'], 'student_id' => $request['student_id']])->first();
        $data['studentCourseList'] = $this->getTcsiStudentCourseListOnly($collegeId, $studentId);

        return $data;
    }

    public function saveStudentTcsiDetailsData(SaveStudentTcsiDetailsPayload $data)
    {
        $postData = $data->toArray();
        DB::beginTransaction();
        try {
            // Use firstOrNew to either find the existing record or create a new instance
            $studTcsiData = StudentTcsiDetails::firstOrNew(['student_id' => $postData['student_id']]);
            $studTcsiData->fill($postData)->save();

            // StudentTcsiDetails::updateOrCreate(['id' => $postData['id']], $postData);
            DB::commit();

            return ['status' => 'success', 'message' => 'TCSI detail saved successfully.'];
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApplicationException($e->getMessage());
        }
    }

    public function getSaHelpFormData($request)
    {
        $data['arrStudStatusCode'] = $this->convertConstantsFormat(Config::get('constants.arrStudStatusCode'));
        $data['arrReportingYear'] = $this->convertConstantsFormat(Config::get('constants.arrReportingYear'));
        $data['arrReportingPeriod'] = $this->convertConstantsFormat(Config::get('constants.arrReportingPeriod'));
        $data['studentSaHelpDetail'] = TcsiStudentSAHelp::where(['college_id' => $request['college_id'], 'student_id' => $request['student_id']])->first();

        return $data;
    }

    public function saveSaHelpDetailData(SaveStudentSaHelpPayload $data)
    {
        $postData = $data->toArray();

        DB::beginTransaction();
        try {
            TcsiStudentSAHelp::updateOrCreate(['id' => $postData['id']], $postData);
            DB::commit();

            return ['status' => 'success', 'message' => 'SA HELP detail saved successfully.'];
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApplicationException($e->getMessage());
        }

    }

    public function getOsHelpFormData($request)
    {
        $collegeId = $request['college_id'];
        $studentId = $request['student_id'];
        $data['arrCourseList'] = $this->getTcsiStudentCourseList($collegeId, $studentId);
        $data['arrLanguageList'] = Language::whereIn('college_id', [$collegeId, 0])->orderBy('name', 'asc')->select('name as  Name', 'id as Id')->get()->toArray();
        $data['arrStudCountry'] = Country::where('college_id', '=', $collegeId)->orwhere('college_id', '=', 0)->orderby('name', 'asc')->select('name as Name', 'absvalue as  Id')->get()->toArray();
        $data['arrStudStatusCode'] = $this->convertConstantsFormat(Config::get('constants.arrStudStatusCode'));
        $data['arrReportingYear'] = $this->convertConstantsFormat(Config::get('constants.arrReportingYear'));
        $data['arrReportingPeriod'] = $this->convertConstantsFormat(Config::get('constants.arrReportingPeriod'));
        $data['studentOsHelpDetail'] = TcsiStudentOsHelp::where(['college_id' => $request['college_id'], 'student_id' => $request['student_id']])->first();

        return $data;
    }

    public function getOShelpInformationFromCourseIdData($request)
    {

        $data['studentOsHelpDetail'] = TcsiStudentOsHelp::where(['course_id' => $request['course_id'], 'college_id' => $request['college_id'], 'student_id' => $request['student_id']])->first();

        return $data;
    }

    public function saveOsHelpDetailData(SaveStudentOsHelpPayload $data)
    {
        $postData = $data->toArray();

        DB::beginTransaction();
        try {
            unset($postData['id']);
            TcsiStudentOsHelp::updateOrCreate(['college_id' => $postData['college_id'], 'student_id' => $postData['student_id'], 'course_id' => $postData['course_id']], $postData);
            DB::commit();

            return ['status' => 'success', 'message' => 'OS HELP detail saved successfully.'];
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApplicationException($e->getMessage());
        }

    }

    public function getTcsiStudentCourseList($collegeId, $studentId)
    {
        //    return StudentCourses::from('rto_student_courses as rsc')
        //             ->join('rto_courses as rc', 'rc.id', '=', 'rsc.course_id')
        //             ->where(['rc.college_id'  => $collegeId,'rsc.student_id' => $studentId])
        //             ->get(['rc.id as Id',DB::raw('concat(rc.course_code, " : ", rc.course_name) as Name')])
        //             ->toArray();

        $resultArr = StudentCourses::from('rto_student_courses as t1')
            ->leftJoin('rto_courses as t2', 't2.id', '=', 't1.course_id')
            ->leftJoin('rto_campus as t3', 't3.id', '=', 't1.campus_id')
            ->where('t1.student_id', $studentId)
            ->select(
                't1.course_id as Id', 't2.college_id', 't1.student_id', 't1.course_id', 't2.course_code', 't2.course_name', 't1.offer_status', 't1.status', 't1.offer_id', 't3.name as campus',
                DB::raw('DATE_FORMAT(t1.start_date, "%d %b, %Y") as start_date'),
                DB::raw('DATE_FORMAT(t1.finish_date, "%d %b, %Y") as finish_date'),
                DB::raw('(CASE WHEN (t1.finish_date > now() AND t1.start_date > now()) THEN 0 WHEN t1.finish_date > now() THEN DATEDIFF(now(), t1.start_date) ELSE DATEDIFF(t1.finish_date, t1.start_date) END) as days'),
                DB::raw('DATEDIFF(t1.finish_date, t1.start_date) as diff_days'),
                DB::raw("CONCAT(t2.course_code,' : ',t2.course_name) as Name")
            )
            ->orderBy(DB::raw('(CASE WHEN t1.status = "Current Student" THEN "a1" WHEN t1.status = "Enrolled" THEN "a2" ELSE t1.status END)'), 'ASC')
            ->get()
            ->toArray();
        foreach ($resultArr as $key => $res) {
            $resultArr[$key]['bg_color'] = $this->getColorCode($res['status']);
        }

        return $resultArr;
    }

    public function getTcsiStudentCourseListOnly($collegeId, $studentId)
    {
        $resultArr = StudentCourses::join('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->where([
                'rto_student_courses.student_id' => $studentId,
                'rto_courses.college_id' => $collegeId,
            ])
            ->select(
                'rto_student_courses.id as Id',
                'rto_student_courses.status as status',
                DB::raw("CONCAT(rto_courses.course_code,' : ',rto_courses.course_name) as Name")
            )
            ->orderByRaw('CASE
                            WHEN rto_student_courses.status = "Current Student" THEN 1
                            WHEN rto_student_courses.status = "Enrolled" THEN 2
                            ELSE 3
                         END')
            // ->orderBy(DB::raw('(CASE WHEN rto_student_courses.status = "Current Student" THEN "a1" WHEN rto_student_courses.status = "Enrolled" THEN "a2" ELSE rto_student_courses.status END)'), 'ASC')
            ->get()
            /*->map(function ($res) {
                return [
                    'Id' => $res->Id,
                    'Name' => $res->Name,
                    'bg_color' => $this->getColorCode($res->status),
                ];
            })*/
            ->toArray();

        foreach ($resultArr as $key => $res) {
            $resultArr[$key]['bg_color'] = $this->getColorCode($res['status']);
        }

        return $resultArr;
    }

    private function getColorCode($status)
    {

        return match ($status) {
            'Current Student' => 'primary-blue',
            'Cancelled', 'Suspended' => 'red',
            'Transitioned' => 'yellow',
            'Completed', 'Finished' => 'green',
            'Withdrawn' => 'pink',
            default => 'gray',
        };

        /*$bgColor = 'gray';
        if ($status == 'Current Student'){
            $bgColor = 'primary-blue';
        } else if ($status == 'Cancelled'){
            $bgColor = 'red';
        } else if ($status == 'Transitioned'){
            $bgColor = 'yellow';
        } else if ($status == 'Completed'){
            $bgColor = 'green';
        } else if ($status == 'Finished'){
            $bgColor = 'green';
        } else if ($status == 'Withdrawn'){
            $bgColor = 'pink';
        }else if( $status == 'Suspended'){
            $bgColor = 'red';
        }
        return $bgColor;*/
    }

    public function getStudentCourseInformationFormData($request)
    {
        $collegeId = $request['college_id'];
        $studentId = $request['student_id'];
        $data['arrBroadTypeArr'] = BroadType::select('id as Id', DB::raw('concat(title, " : ", broad_key) as Name'))->where('college_id', $collegeId)->get()->toArray();
        $data['arrNarrowTypeArr'] = NarrowType::select('id as Id', DB::raw('concat(title, " : ", narrow_key) as Name'))->where('college_id', $collegeId)->get()->toArray();
        $data['arrSubNarrowTypeArr'] = SubNarrowType::select('id as Id', DB::raw('concat(title, " : ", sub_narrow_key) as Name'))->where('college_id', $collegeId)->get()->toArray();
        $data['arrCourseList'] = $this->getTcsiStudentCourseList($collegeId, $studentId);
        $data['arrBasisForAdmissionCode'] = $this->convertConstantsFormat(Config::get('constants.arrBasisForAdmissionCode'));
        $data['arrModeOfAttendanceCode'] = $this->convertConstantsFormat(Config::get('constants.arrModeOfAttendanceCode'));
        $data['arrTypeofAttendanceCode'] = $this->convertConstantsFormat(Config::get('constants.arrTypeofAttendanceCode'));
        $data['arrRemissionReasonCode'] = $this->convertConstantsFormat(Config::get('constants.arrRemissionReasonCode'));
        $data['arrHighestAttainmentCode'] = $this->convertConstantsFormat(Config::get('constants.arrHighestAttainmentCode'));
        $data['arrCourseFeeType'] = $this->convertConstantsFormat(Config::get('constants.arrCourseFeeType'));
        $data['arrCourseOutcomeCode'] = $this->convertConstantsFormat(Config::get('constants.arrCourseOutcomeCode'));
        $data['studentCourseInformationDetail'] = TcsiStudentCourseInfo::where(['college_id' => $request['college_id'], 'student_id' => $request['student_id']])->first();

        return $data;
    }

    public function getStudentCourseInformationFromCourseIdData($request)
    {
        $data['studentCourseInformationDetail'] = TcsiStudentCourseInfo::where(['course_id' => $request['course_id'], 'college_id' => $request['college_id'], 'student_id' => $request['student_id']])->first();

        return $data;
    }

    public function getNarrowTypeListData($request)
    {
        return NarrowType::select('id as Id', DB::raw('concat(title, " : ", narrow_key) as Name'))->where(['college_id' => $request['college_id'], 'broad_id' => $request['broad_id']])->get()->toArray();
    }

    public function getSubNarrowTypeListData($request)
    {
        return SubNarrowType::select('id as Id', DB::raw('concat(title, " : ", sub_narrow_key) as Name'))->where(['college_id' => $request['college_id'], 'narrow_id' => $request['narrow_id']])->get()->toArray();
    }

    public function saveStudentCourseInformationData(SaveStudentCourseInformationPayload $data)
    {
        $postData = $data->toArray();
        DB::beginTransaction();
        try {

            // TcsiStudentCourseInfo::updateOrCreate(['id' => $postData['id']], $postData);
            unset($postData['id']);
            TcsiStudentCourseInfo::updateOrCreate(['college_id' => $postData['college_id'], 'student_id' => $postData['student_id'], 'course_id' => $postData['course_id']], $postData);
            DB::commit();

            return ['status' => 'success', 'message' => 'Student Course information saved successfully.'];
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApplicationException($e->getMessage());
        }

    }

    public function getDisabilityInformationFormData($request)
    {
        $collegeId = $request['college_id'];
        $studentId = $request['student_id'];
        $data['arrStudStatusCode'] = $this->convertConstantsFormat(Config::get('constants.arrDisabilityInformation'));
        $dataArr = TcsiStudentDisability::where(['college_id' => $collegeId, 'student_id' => $studentId])->get(['disability_code as code', 'start_date as start', 'end_date as end'])->toArray();
        $resArr = [];
        foreach ($dataArr as $res) {
            $resArr[$res['code']] = $res;
        }
        $data['studentCourseInformationDetail'] = $resArr;

        return $data;
    }

    public function saveDisabilityInformationDetailsData($postData, $userId)
    {

        TcsiStudentDisability::where(['college_id' => $postData['college_id'], 'student_id' => $postData['student_id']])->delete();
        DB::beginTransaction();
        try {
            if (isset($postData['name'])) {
                foreach ($postData['name'] as $key => $val) {
                    if ($val == 'on') {
                        $start = $postData['start'][$key];
                        $end = $postData['end'][$key];
                        $postData['college_id'] = $postData['college_id'];
                        $postData['student_id'] = $postData['student_id'];
                        $postData['disability_code'] = $key;
                        $postData['start_date'] = (! empty($start) ? date('Y-m-d', strtotime($start)) : null);
                        $postData['end_date'] = (! empty($end) ? date('Y-m-d', strtotime($end)) : null);
                        $postData['created_by'] = $userId;
                        $postData['updated_by'] = $userId;
                        TcsiStudentDisability::create($postData);
                    }
                }
            }
            DB::commit();

            return ['status' => 'success', 'message' => 'Disability information saved successfully.'];
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApplicationException($e->getMessage());
        }

    }

    public function getCourseVariantListData($request)
    {
        // Get pagination parameters from request, with defaults
        $perPage = isset($request['per_page']) ? (int) $request['per_page'] : 5;
        $page = isset($request['page']) ? (int) $request['page'] : 1;

        // Ensure reasonable limits
        $perPage = max(1, min($perPage, 50)); // Between 1 and 50
        $page = max(1, $page); // At least 1

        $result = StudentDeferCourse::from('rto_student_defer as rsd')
            ->join('rto_student_courses as rsc', 'rsc.id', '=', 'rsd.student_course_id')
            ->where([
                'rsd.college_id' => $request['college_id'],
                'rsd.student_id' => $request['student_id'],
                'rsd.student_course_id' => $request['student_course_id'],
            ])
            ->select(['rsd.*', 'rsc.status'])
            ->orderBy('rsd.id', 'DESC')
            ->groupBy('rsd.id')
            ->paginate($perPage, ['*'], 'page', $page);

        $data = @$result->toArray()['data'];

        // Add download URLs for COE and Evidence files
        if (! empty($data)) {
            $filePath = Config::get('constants.uploadFilePath.StudentCOE');
            $destinationPath = Helpers::changeRootPath($filePath, $request['student_id']);

            foreach ($data as $k => $row) {
                // Handle CoE file download URL
                $downloadURL = null;
                if (! empty($row['new_coe_file'])) {
                    $existingFilePath = UploadService::exists($destinationPath['view'].$row['new_coe_file']);
                    if ($existingFilePath) {
                        $downloadURL = UploadService::download($destinationPath['view'].$row['new_coe_file']);
                    }
                }
                $data[$k]['download_url'] = $downloadURL;

                // Handle Evidence file download URL
                $evidenceDownloadURL = null;
                if (! empty($row['approval_evidence_file'])) {
                    $existingEvidenceFilePath = UploadService::exists($destinationPath['view'].$row['approval_evidence_file']);
                    if ($existingEvidenceFilePath) {
                        $evidenceDownloadURL = UploadService::download($destinationPath['view'].$row['approval_evidence_file']);
                    }
                }
                $data[$k]['approval_evidence_download_url'] = $evidenceDownloadURL;
            }
        }

        return [
            'total' => $result->total(),
            'data' => $data,
            'current_page' => $result->currentPage(),
            'last_page' => $result->lastPage(),
            'per_page' => $result->perPage(),
            'from' => $result->firstItem(),
            'to' => $result->lastItem(),
        ];
    }

    public function getCourseVariantDetailData($request)
    {
        return StudentDeferCourse::find($request['id']);
    }

    public function getCourseVariantLogData($request)
    {
        $res = StudentCourses::find($request['student_course_id']);

        $data = Activity::with(['causer'])
            ->logForCourseVariant($res->student_id, $res->course_id)
            ->orderBy('id', 'desc')
            ->paginate(10)
            ->toArray();

        foreach ($data['data'] as $key => $value) {
            $data['data'][$key] = $value;
            $data['data'][$key]['description'] = nl2br($value['description']);
            $data['data'][$key]['properties'] = $this->setAttributeFiled($value['properties']);
            $data['data'][$key]['created_at'] = Helpers::convertDateTimeToReadableFormat($value['created_at']); // $this->convertDisplayDate($value['created_at']);
        }

        return $data;
    }

    public function printCourseVariantData($courseDeferId)
    {
        $whereArr = [
            'rto_student_defer.college_id' => auth()->user()->college_id,
            'rto_student_defer.id' => $courseDeferId,
        ];

        return StudentDeferCourse::join('rto_courses', 'rto_courses.id', '=', 'rto_student_defer.course_id')
            ->leftjoin('rto_colleges', 'rto_colleges.id', '=', 'rto_courses.college_id')
            ->leftjoin('rto_college_details', 'rto_college_details.college_id', '=', 'rto_courses.college_id')
            ->leftjoin('rto_students', 'rto_students.id', '=', 'rto_student_defer.student_id')
            ->leftjoin('rto_student_courses', 'rto_student_courses.course_id', '=', 'rto_courses.id')
            ->where($whereArr)
            ->select(
                'rto_students.generated_stud_id',
                'rto_students.name_title',
                'rto_students.first_name',
                'rto_students.family_name',
                'rto_colleges.contact_phone as phone',
                'rto_college_details.fax',
                'rto_colleges.contact_email as email',
                'rto_colleges.college_logo as logo',
                'rto_student_courses.start_date as dt_start',
                'rto_student_courses.finish_date as dt_finish',
                'rto_student_courses.status as course_status',
                'rto_courses.course_code',
                'rto_courses.course_name',
                'rto_student_defer.*'
            )
            ->groupBy('rto_student_defer.id')
            ->first();
    }

    /* TCSI Credit Offer */
    public function getTcsiCreditOfferData($request)
    {
        $res = $this->studentProfile->getTcsiCreditOfferData($request);

        return [
            'data' => $res['data'],
            'total' => $res['total'],
        ];
    }

    public function getTcsiCreditOfferInfo($primaryId)
    {
        if ($primaryId <= 0) {
            throw new ApplicationException('Invalid Id / Data not found');
        }

        try {
            return TcsiStudentCreditOffer::find($primaryId);
        } catch (\Exception $e) {
            throw new ApplicationException($e->getMessage());
        }
    }

    public function saveTcsiCreditOfferData($request)
    {
        $data = $request->toArray();
        $data['course_id'] = StudentCourses::find($data['student_course_id'])->course_id;
        DB::beginTransaction();
        try {
            TcsiStudentCreditOffer::create($data);
            DB::commit();

            return ['status' => 'success', 'message' => 'Added Successfully.'];
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApplicationException($e->getMessage());
        }
    }

    public function updateTcsiCreditOfferData($request)
    {
        $data = $request->toArray();

        $primaryId = $data['id'];

        if ($primaryId <= 0) {
            throw new ApplicationException('Invalid Id');
        }

        $rowData = TcsiStudentCreditOffer::find($primaryId);

        if (! $rowData) {
            throw new ApplicationException('Data not found');
        }

        DB::beginTransaction();
        try {
            $updateArr = array_filter([
                'credit_used_value' => $data['credit_used_value'],
                'credit_basis_code' => $data['credit_basis_code'],
                'credit_provider_code' => $data['credit_provider_code'],
                'updated_by' => $data['updated_by'],
            ]);

            if ($rowData->student_course_id != $data['student_course_id']) {
                $courseId = StudentCourse::where('id', $data['student_course_id'])->value('course_id');
                $updateArr['course_id'] = $courseId;
                $updateArr['student_course_id'] = $data['student_course_id'];
            }

            $rowData->update($updateArr);

            DB::commit();

            return ['status' => 'success', 'message' => 'Updated Successfully.'];
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApplicationException($e->getMessage());
        }
    }

    public function deleteTcsiCreditOfferData($primaryId)
    {
        DB::beginTransaction();
        try {
            $res = TcsiStudentCreditOffer::destroy($primaryId);
            DB::commit();

            return $res;
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApplicationException($e->getMessage());
        }
    }

    /* TODO:: Once GNG-4838 is completed, please remove the unused functions below—they are related to the Course Variant module. */
    public function saveCourseVariantData($request) // SaveStudentCourseDefer $request
    {
        $postData = $request->toArray();

        $coeFile = $request->file('new_coe_file');
        $evidenceFile = $request->file('approval_evidence_file');

        $coeFileName = $evidenceFileName = $coeFilePath = $evidenceFilePath = null;
        $studentId = $postData['student_id'];

        if (! empty($coeFile)) {
            $coeFileUploadResult = $this->uploadFile($coeFile, $studentId);
            if ($coeFileUploadResult) {
                $coeFileName = $coeFileUploadResult['name'];
                // $coeFilePath = $coeFileUploadResult['path'];
            }
        }
        if (! empty($evidenceFile)) {
            $evidenceFileUploadResult = $this->uploadFile($evidenceFile, $studentId);
            if ($evidenceFileUploadResult) {
                $evidenceFileName = $evidenceFileUploadResult['name'];
                // $evidenceFilePath = $evidenceFileUploadResult['path'];
            }
        }

        $studentCourse = StudentCourses::find($postData['student_course_id']);

        if (! $studentCourse) {
            throw new ApplicationException('Student course not found.');
        }

        $oldStatus = $studentCourse->status;
        $newStatus = $postData['course_status'];

        $postData['course_id'] = $studentCourse->course_id;
        $postData['previous_course_status'] = $oldStatus;
        $postData['new_coe_file'] = $coeFileName;
        $postData['approval_evidence_file'] = $evidenceFileName;
        $postData['original_course_end_date'] = $studentCourse->finish_date;

        $isCourseEndDateImpact = filter_var($postData['is_course_end_date_impact'], FILTER_VALIDATE_BOOLEAN);

        DB::beginTransaction();
        try {
            // Create defer course record
            StudentDeferCourse::create($postData);

            // Update student course status, finish date & total_weeks
            $updateStudCourseArr = [
                'status' => $newStatus,
                'updated_by' => auth()->user()->id,
            ];
            if ($isCourseEndDateImpact) {
                $calculatedTotalWeeks = $this->extendCourseDateRangeData($studentCourse, $postData);
                $updateStudCourseArr['finish_date'] = $postData['new_course_end_date'];
                $updateStudCourseArr['total_weeks'] = $calculatedTotalWeeks;
            }
            $studentCourse::where('id', $studentCourse->id)->update($updateStudCourseArr);

            // Create status history log entry
            $this->addCourseStatusChangeHistory($oldStatus, $newStatus, $studentCourse, $postData);

            // TODO:: GNG-4849 (Automated Email Trigger on Student Withdrawal/Cancel in Galaxy)
            if (in_array($newStatus, ['Withdrawn', 'Cancelled'])) {
                $this->notifyIfWithdrawOrCancel($studentCourse->id, $postData);
            }

            DB::commit();

            return ['status' => 'success', 'message' => 'Course Variant details saved successfully.'];
        } catch (\Exception $e) {
            DB::rollBack();
            throw new ApplicationException($e->getMessage());
        }
    }

    public function addCourseStatusChangeHistory($oldStatus, $newStatus, $studentCourse, $postData)
    {
        $loginUser = auth()->user();

        if ($oldStatus !== $newStatus) {
            $statusHistoryData = [
                'college_id' => $loginUser->college_id,
                'student_id' => $studentCourse->student_id,
                'course_id' => $studentCourse->id, // $studentCourse->course_id,
                'status_from' => empty($oldStatus) ? 'New Application Request' : $oldStatus,
                'status_to' => $newStatus,
                'today_date' => date('d/m/Y h:i:s A'),
                'status' => ($oldStatus == '') ? ("Inserted record with status : $newStatus") : ("Convert from $oldStatus to $newStatus"),
                'created_at' => date('Y-m-d H:i:s'),
                'created_by' => $loginUser->id,
                'updated_by' => $loginUser->id,
            ];

            // Handle cancellation status
            if (in_array($newStatus, ['Cancelled'])) {
                $statusHistoryData['is_cancel'] = 1;
                if (! empty($postData['reason_for_cancellation'])) {
                    $cancelReasons = Config::get('constants.arrCourseCancelReason');
                    $reasonText = $cancelReasons[$postData['reason_for_cancellation']] ?? $postData['reason_for_cancellation'];
                    $statusHistoryData['cancel_reason'] = $reasonText;
                }
            }

            StudentCourseStatusHistory::create($statusHistoryData);
        }
    }

    public function uploadFile($file, $subFolderName)
    {
        // $filePath = Config::get('constants.uploadFilePath.DeferStudentDoc');
        $filePath = Config::get('constants.uploadFilePath.StudentCOE');
        $destinationPath = Helpers::changeRootPath($filePath, $subFolderName);
        $originalName = $file->getClientOriginalName();
        $filenm = time().$originalName;   // hashFileName($originalName);
        $fileName = $this->removeSpecialCharacter($filenm);
        // $upload_success = $file->move($destinationPath['default'], $fileName);
        $upload_success = UploadService::uploadAs($destinationPath['view'], $file, $fileName);

        info('File uploaded for defer student', [$upload_success]);
        if ($upload_success) {
            return [
                'path' => $destinationPath['view'],
                'name' => $fileName,
            ];
        }

        return null;
    }

    private function removeSpecialCharacter($fileName)
    {
        $fileName = str_replace(' ', '-', $fileName); // Replaces all spaces with hyphens.

        return preg_replace('/[^A-Za-z0-9.\-]/', '', $fileName); // Removes special chars.
    }

    public function notifyIfWithdrawOrCancel($studentCourseId, $postData)
    {
        $resData = StudentCourses::with('course')->where('id', $studentCourseId)->first();
        $student = Student::with('associatedUserAccount')->find($resData->student_id);
        $studentUser = $student->associatedUserAccount;

        $loginUser = auth()->user();
        $currentDate = now()->format('d M Y');

        if ($resData->status == 'Withdrawn') {
            $notificationData = StudentWithdrawalNotificationDTO::LazyFromArray([
                'courseId' => $resData->course_id,
                'courseName' => $resData->course->course_name,
                'withdrawnAt' => $currentDate,
            ]);
            // Send notification to student and admin
            $collage = CollegeDetails::find(auth()->user()->college_id);
            $itEmail = $collage->it_email;
            $adminUser = clone auth()->user();
            $adminUser->email = $itEmail;
            $this->sendNotificationsToUsers([
                [
                    'user' => $studentUser,
                    'type' => NotificationType::STUDENT_WITHDRAWAL,
                    'data' => $notificationData,
                ],
                [
                    'user' => $adminUser,
                    'type' => NotificationType::ADMIN_STUDENT_WITHDRAWAL,
                    'data' => $notificationData,
                ],
            ]);
        }

        if ($resData->status == 'Cancelled') {
            $reasonText = '';
            if (! empty($postData['reason_for_cancellation'])) {
                $cancelReasons = Config::get('constants.arrCourseCancelReason');
                $reasonText = $cancelReasons[$postData['reason_for_cancellation']] ?? $postData['reason_for_cancellation'];
            }

            $notificationData = CourseCancellationDTO::LazyFromArray([
                'studentCourse' => $resData,
                'reason' => $reasonText,
                'cancelledAt' => $currentDate,
            ]);
            // $this->sendNotification($loginUser, NotificationType::COURSE_CANCELLED, $notificationData);
            $this->sendNotificationsToUsers([
                [
                    'user' => $studentUser,
                    'type' => NotificationType::COURSE_CANCELLED,
                    'data' => $notificationData,
                ],
                [
                    'user' => $adminUser,
                    'type' => NotificationType::ADMIN_COURSE_CANCELLED,
                    'data' => $notificationData,
                ],
            ]);
        }
    }

    public function extendCourseDateRangeData($studentCourse, $postData)
    {
        $loginUserId = auth()->user()->id;
        $newCourseEndDate = $postData['new_course_end_date'];

        // Calculate total weeks using the same logic as JavaScript calculateDurationFromDates
        $startDate = $studentCourse->start_date;
        $finishDate = $newCourseEndDate;
        $durationType = $studentCourse->course_duration_type ?? '2'; // Default to 'week' (value 2)

        $calculatedTotalWeeks = $this->calculateDurationFromDates($startDate, $finishDate, $durationType);

        $studentCourse::where('id', $studentCourse->id)->update([
            'finish_date' => $newCourseEndDate,
            'total_weeks' => $calculatedTotalWeeks,
            'status' => $postData['course_status'],
            'updated_by' => $loginUserId,
        ]);

        // Save extension history
        $historyData = [
            'college_id' => $postData['college_id'],
            'student_id' => $studentCourse->student_id,
            'course_id' => $studentCourse->course_id,
            'student_course_id' => $studentCourse->id,
            'start_date' => $studentCourse->start_date,
            'new_start_date' => $studentCourse->start_date,
            'finish_date' => $studentCourse->finish_date,
            'new_finish_date' => $newCourseEndDate,
            'total_weeks' => $studentCourse->total_weeks,
            'new_total_weeks' => $calculatedTotalWeeks,
            'extension_reason' => 'Update from course variant module',
            'created_by' => $loginUserId,
            'updated_by' => $loginUserId,
        ];

        StudentCourseExtensionHistory::create($historyData);

        return $calculatedTotalWeeks;
    }

    public function calculateDurationFromDates($startDate, $finishDate, $durationType)
    {
        if (! $startDate || ! $finishDate) {
            return 0;
        }

        $startDateTime = new \DateTime($startDate);
        $finishDateTime = new \DateTime($finishDate);

        // Calculate the difference in days
        $timeDifference = $finishDateTime->getTimestamp() - $startDateTime->getTimestamp();
        $daysDifference = ceil($timeDifference / (24 * 60 * 60)); // Convert seconds to days

        switch ($durationType) {
            case '1': // day
                return $daysDifference;

            case '2': // week (default)
                return ceil($daysDifference / 7);

            case '3': // month
                $monthsDiff = ($finishDateTime->format('Y') - $startDateTime->format('Y')) * 12;
                $monthsDiff += $finishDateTime->format('n') - $startDateTime->format('n');

                return ceil($monthsDiff);

            case '4': // year
                return ceil($finishDateTime->format('Y') - $startDateTime->format('Y'));

            default: // default to weeks
                return ceil($daysDifference / 7);
        }
    }
}
