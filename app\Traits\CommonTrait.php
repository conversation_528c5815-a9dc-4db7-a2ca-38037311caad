<?php

namespace App\Traits;

use App\Helpers\Helpers;
use DateTime;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Cache;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\DB;
use Intervention\Image\Facades\Image;
use Support\Services\UploadService;

trait CommonTrait
{
    public function getOperators()
    {
        return [
            'eq' => '=',
            'gt' => '>',
            'gte' => '>=',
            'lt' => '<',
            'lte' => '<=',
            'neq' => '!=',
            'startswith' => 'like',
            'contains' => 'like',
            'doesnotcontain' => 'not like',
            'endswith' => 'like',
            'isnull' => '=',
            'isnotnull' => '!=',
            'isempty' => '=',
            'isnotempty' => '!=',
        ];
    }

    public function convertFilterValue($filterOperator, $filterValue)
    {
        // Do some value manupulations for some kendo operators
        switch ($filterOperator) {
            case 'startswith':
                $value = $filterValue.'%';
                break;
            case 'endswith':
                $value = '%'.$filterValue;
                break;
            case 'contains':
            case 'doesnotcontain':
                $value = '%'.$filterValue.'%';
                break;
            case 'isnull':
            case 'isnotnull':
                $value = null;
                break;
            case 'isempty':
            case 'isnotempty':
                $value = '';
                break;
            default:
                $value = $filterValue;
                break;

        }

        return $value;
    }

    public function gridDataFilter($query, $post, $columns)
    {

        $operations = $this->getOperators();
        $filterParts = (isset($post['filter']) && isset($post['filter']['filters'])) ? $post['filter'] : [];

        if (isset($filterParts['logic'])) {

            $filterArr = $filterParts['filters'];
            $defaultLogic = $filterParts['logic'];
            $query->where(function ($childQuery) use ($filterArr, $operations, $columns, $defaultLogic) {

                foreach ($filterArr as $filter) {
                    if (isset($filter['logic'])) {
                        $childQuery->where(function ($subQuery) use ($filter, $operations, $columns) {
                            foreach ($filter['filters'] as $subFilter) {
                                $subOperator = $operations[$subFilter['operator']];
                                $subSearchValue = $this->convertFilterValue($subFilter['operator'], $subFilter['value']);
                                if (strpos($columns[$subFilter['field']], ' as ') !== false) {
                                    $fullField = explode(' as ', $columns[$subFilter['field']])[0];
                                    $whereRawCondition = ($filter['logic'] == 'or') ? 'orWhereRaw' : 'whereRaw';
                                    $subQuery->$whereRawCondition("$fullField $subOperator ?", $subSearchValue);
                                } else {
                                    $whereCondition = ($filter['logic'] == 'or') ? 'orWhere' : 'where';
                                    $subQuery->$whereCondition($subFilter['field'], $subOperator, $subSearchValue);
                                }
                            }
                        });
                    } elseif ($filter['field'] == 'extra') {
                        // apply filter on controller
                    } elseif ($filter['field'] == 'searchKey') {
                        $searchText = $filter['value'];
                        if (! empty($searchText)) {
                            $childQuery->where(function ($subQuery) use ($columns, $searchText) {
                                foreach ($columns as $column) {
                                    $columnFullName = (strpos($column, ' as ') !== false) ? (explode(' as ', $column)[0]) : $column;
                                    $subQuery->orWhereRaw("$columnFullName like ?", "%$searchText%");
                                }
                            });
                        }
                    } else {
                        $operator = $operations[$filter['operator']];
                        $searchValue = $this->convertFilterValue($filter['operator'], $filter['value']);
                        if (strpos($columns[$filter['field']], ' as ') !== false) {
                            $fullField = explode(' as ', $columns[$filter['field']])[0];
                            $whereRawCondition = ($defaultLogic == 'or') ? 'orWhereRaw' : 'whereRaw';
                            $childQuery->$whereRawCondition("$fullField $operator ?", $searchValue);
                        } else {
                            $whereCondition = ($defaultLogic == 'or') ? 'orWhere' : 'where';
                            $childQuery->$whereCondition($filter['field'], $operator, $searchValue);
                        }
                    }
                }
            });
        }
    }

    public function gridDataSorting($query, $post)
    {
        // handle the sorting request
        $sorts = (isset($post['sort'])) ? $post['sort'] : [];
        foreach ($sorts as $sort) {
            $query->orderBy($sort['field'], $sort['dir']);
        }
    }

    public function gridDataPagination($query, $post, $countOnly)
    {
        if ($countOnly) {
            // $rows = DB::select("SELECT COUNT(*) as total from (".getParsedSQL($query).") as temp_table");
            // return count($rows) ? @$rows[0]->total : 0;
            return DB::table(DB::raw("({$query->toSql()}) as subquery"))
                ->mergeBindings($query->getQuery()) // Merge original query bindings
                ->count();
            // return $query->get()->count();
        } else {
            if (isset($post['take'])) {
                $query->skip($post['skip'])->take($post['take']);
            }
            $result = $query->get()->toArray();
        }

        return $result;
    }

    public function gridDataPaginationV2($query, $post, $countOnly = false)
    {

        $result = $query->paginate($post['take']);

        return ['total' => $result->total(), 'data' => @$result->toArray()['data']];
    }

    public function getStudentProfilePicPath($studentId, $profilePicture, $imageType = 'thumb')
    {
        $thumbText = Config::get('constants.defaultThumbText');
        $smallThumbText = Config::get('constants.smallThumbText');
        $filePath = Config::get('constants.uploadFilePath.StudentPics');
        $destinationPath = Helpers::changeRootPath($filePath, $studentId);

        $imageName = ($imageType == 'original') ? $profilePicture : (($imageType == 'small') ? $smallThumbText : $thumbText).$profilePicture;

        $profile_pic = $destinationPath['default'].$profilePicture;
        // if (file_exists($profile_pic) && !empty($profilePicture)) {
        //     $profile_pic_url = asset($destinationPath['view'] . $imageName);
        // } else {
        //     $profile_pic_url = '';
        // }
        if (UploadService::exists($destinationPath['view'].$imageName)) {
            $profile_pic_url = $this->getUploadedFileUrl($destinationPath['view'].$imageName);
        } else {
            $profile_pic_url = '';
        }

        return $profile_pic_url;
    }

    public function checkDropdownDataValid($result)
    {

        if (isset($result) && count($result) > 0) {
            return $result;
        } else {
            return [];
        } // return [ 'id' => '', 'text' => 'No Data Found' ];

    }

    public function getCourseStatusColor($status)
    {
        $bgColor = 'gray';
        if ($status == 'Current Student') {
            $bgColor = 'primary-blue';
        } elseif ($status == 'Cancelled') {
            $bgColor = 'red';
        } elseif ($status == 'Transitioned') {
            $bgColor = 'yellow';
        } elseif ($status == 'Completed') {
            $bgColor = 'green';
        } elseif ($status == 'Finished') {
            $bgColor = 'green';
        } elseif ($status == 'Withdrawn') {
            $bgColor = 'pink';
        } elseif ($status == 'Suspended') {
            $bgColor = 'red';
        }

        return $bgColor;
    }

    public function cacheStorageForGrid($cacheKey, $query, $post, $countOnly)
    {
        $cacheTime = 60 * 60 * 24; // 1 Day

        return cache()->remember($cacheKey, $cacheTime, function () use ($query, $post, $countOnly) {
            $this->gridDataSorting($query, $post);

            return $this->gridDataPagination($query, $post, $countOnly);
        });
    }

    public function convertDateFormat($date, $format = '', $newFormat = 'Y-m-d')
    {
        $currentFormat = empty($format) ? Config::get('constants.dateFormat') : $format;

        return Carbon::createFromFormat($currentFormat, $date)->format($newFormat);
    }

    public static function changeRootPath($filePath, $collegeId, $subFolderName = '')
    {

        $key = '{college_id}';
        $value = $collegeId;

        $collgeDirPath = public_path().'/uploads/{college_id}/';

        $checkCollgeDirPath = str_replace("$key", $value, $collgeDirPath);

        if (! is_dir($checkCollgeDirPath)) {
            @mkdir($checkCollgeDirPath, 0777);
        }

        $default = str_replace("$key", $value, $filePath['default']);
        $view = str_replace("$key", $value, $filePath['view']);

        if (! is_dir($default)) {
            @mkdir($default, 0777);
        }

        if (empty($subFolderName)) {
            $resultArr = ['default' => $default, 'view' => $view];
        } else {
            $resultArr = ['default' => $default.$subFolderName.'/', 'view' => $view.$subFolderName.'/'];
        }

        return $resultArr;
    }

    private function convertConstantsFormat($resArr, $param1 = 'Id', $param2 = 'Name', $isSameValue = false)
    {
        $data = [];
        if ($resArr) {
            // unset($resArr['']);
            foreach ($resArr as $key => $value) {
                $data[] = [
                    $param1 => ($isSameValue) ? $value : $key,
                    $param2 => $value,
                ];
            }
        }

        return $data;
    }

    private function generateThumbImage($file, $destinationPath, $fileName)
    {
        if (config('filesystems.upload_disk') != 'uploads_s3' && ! is_dir($destinationPath['default'])) {
            @mkdir($destinationPath['default'], 0777);
        }

        $imgFile = Image::make($file);

        $defaultthumbText = Config::get('constants.defaultThumbText');
        $defaultthumbSize = Config::get('constants.defaultThumbSize');
        $this->generateAndSaveImageThumbnail($imgFile, $destinationPath, $defaultthumbText.$fileName, $defaultthumbSize);

        $smallthumbText = Config::get('constants.smallThumbText');
        $smallthumbSize = Config::get('constants.smallThumbSize');
        $this->generateAndSaveImageThumbnail($imgFile, $destinationPath, $smallthumbText.$fileName, $smallthumbSize);
    }

    private function generateThumbImageS3($file, $destinationPath, $fileName)
    {
        if (config('filesystems.upload_disk') != 'uploads_s3' && ! is_dir($destinationPath['default'])) {
            @mkdir($destinationPath['default'], 0777);
        }

        $imgFile = Image::make($file);

        $thumbText1 = Config::get('constants.defaultThumbText');
        $thumbSize1 = Config::get('constants.defaultThumbSize');
        $this->generateAndSaveImageThumbnail($imgFile, $destinationPath, $thumbText1.$fileName, $thumbSize1);

        $thumbText2 = Config::get('constants.smallThumbText');
        $thumbSize2 = Config::get('constants.smallThumbSize');
        $this->generateAndSaveImageThumbnail($imgFile, $destinationPath, $thumbText2.$fileName, $thumbSize2);
    }

    public function generateAndSaveImageThumbnail(\Intervention\Image\Image $imgFile, $destinationPath, $fileName, $size = null)
    {

        if (! $size) {
            return null;
        }

        $resizedimage = $imgFile->resize($size, $size, function ($constraint) {
            $constraint->aspectRatio();
        });

        // Detect the original image format from the mime type
        $mimeType = $imgFile->mime();
        $format = 'jpg'; // default fallback
        $extension = 'jpg';
        $quality = 90;

        switch ($mimeType) {
            case 'image/png':
                $format = 'png';
                $extension = 'png';
                $quality = null; // PNG doesn't use quality parameter
                break;
            case 'image/jpeg':
            case 'image/jpg':
                $format = 'jpg';
                $extension = 'jpg';
                $quality = 90;
                break;
            case 'image/gif':
                $format = 'gif';
                $extension = 'gif';
                $quality = null; // GIF doesn't use quality parameter
                break;
            case 'image/webp':
                $format = 'webp';
                $extension = 'webp';
                $quality = 90;
                break;
        }

        // Create a temporary file with proper extension
        $tempPath = tempnam(sys_get_temp_dir(), 'img_').'.'.$extension;

        // Save with explicit format to preserve original format
        if ($quality !== null) {
            $resizedimage->encode($format, $quality)->save($tempPath);
        } else {
            $resizedimage->encode($format)->save($tempPath);
        }

        $upload_success = UploadService::uploadAs($destinationPath['view'], $tempPath, $fileName);

        info('Student profile pic update', [$upload_success]);

        // Clean up temporary file
        if (file_exists($tempPath)) {
            unlink($tempPath);
        }

        return $upload_success;
    }

    private function deleteThumbImages($destinationPath, $fileName, $deleteMainFile = false)
    {
        $defaulThumbName = Config::get('constants.defaultThumbText');
        $smallThumbName = Config::get('constants.smallThumbText');
        $defaultThumb = $defaulThumbName.$fileName;
        $smallThumb = $smallThumbName.$fileName;
        if (file_exists($destinationPath.$defaultThumb)) {
            unlink($destinationPath.$defaultThumb);
        }
        if (file_exists($destinationPath.$smallThumb)) {
            unlink($destinationPath.$smallThumb);
        }
        if ($deleteMainFile && file_exists($destinationPath.$fileName)) {
            unlink($destinationPath.$fileName);
        }
    }

    /* Get & Set student list data from cache */
    public function getStudentCacheData()
    {
        $cacheKeySuffixText = Config::get('constants.cacheKeySuffix.studentList');
        $cacheKey = tenant('id').$cacheKeySuffixText;
        $checkStudentCache = Cache::get($cacheKey);
        if ($checkStudentCache === null || empty(json_decode($checkStudentCache))) {
            return false;
        } else {
            $arrStudentList = json_decode($checkStudentCache, true);
            if (empty($arrStudentList['data'])) {
                return false;
            }

            return $arrStudentList;
        }
    }

    public function setStudentCacheData()
    {
        $studentListData = [
            'college_id' => @Auth::user()->college_id,
            'tenant_id' => tenant('id'),
        ];
        event(new \App\Events\StudentListEvent($studentListData));

        return true;
    }

    public function getCurrentDateTimeWithTimeZone($timezone = 'UTC', $format = 'Y-m-d H:i:s')
    {
        $currentDateTime = Carbon::now($timezone);

        return $currentDateTime->format($format);
    }

    public function dateCovertWithTimeZone($givenDate, $timezone = 'UTC', $format = 'Y-m-d H:i:s')
    {
        // $givenDate = '2023-12-27 15:30:00';
        // Create a Carbon instance from the given date and assume it's in a specific timezone (e.g., UTC)
        $carbonDate = Carbon::createFromFormat('Y-m-d H:i:s', $givenDate, 'UTC');

        // Convert the date to the given timezone
        $convertDate = $carbonDate->setTimezone($timezone);

        // Format the date in the desired format
        return $convertDate->format($format);
    }

    public function getInstallmentNumberData()
    {
        $installments = [];
        for ($i = 1; $i <= 50; $i++) {
            $installments[] = ['Id' => $i, 'Name' => $i];
        }

        return $installments;
    }

    public function convertDisplayDate($date)
    {
        return date(Config::get('app.dateFormatFrontSidePHP').' h:i A', strtotime($date));
    }

    public function setAttributeFiled(array $dataItems): array
    {
        // Extract attributes and old values
        $attributes = $dataItems['attributes'] ?? [];
        $oldValues = $dataItems['old'] ?? [];

        // Construct new array
        return array_map(function ($fieldName, $value) use ($oldValues) {
            return [
                'field_name' => $this->convertToReadable($fieldName),
                'attributes' => $this->formatDateIfNeededV2($value),
                'old' => $this->formatDateIfNeededV2($oldValues[$fieldName] ?? '-'),
            ];
        }, array_keys($attributes), $attributes);
    }

    protected function isDate($value): bool
    {
        return $value && (DateTime::createFromFormat('Y-m-d', $value) !== false);
    }

    protected function formatDateIfNeededV2($value)
    {
        // Ensure we only process strings or date-like values
        if (is_array($value)) {
            return json_encode($value);
        }

        // Proceed with date conversion logic for valid date-like strings
        if (is_string($value) && preg_match('/^\/Date\((\d+)([+-]\d+)?\)\/$/', $value, $matches)) {
            $timestamp = $matches[1] / 1000;

            return date('Y-m-d H:i:s', $timestamp);
        }

        // If it's not a date, return the value as is
        return $value;
    }

    protected function formatDateIfNeeded($value): string
    {
        if ($this->isDate($value)) {
            return (new DateTime($value))->format('d-m-Y');
        }

        // Return the original value as a string, or an empty string if it's null
        return (string) $value;
    }

    public function convertToReadable(string $fieldName): string
    {
        return ucwords(str_replace('_', ' ', $fieldName));
    }

    public function convertXeroZohoDate($dateVal = null)
    {
        return (isset($dateVal) && ! empty($dateVal)) ? \Carbon\Carbon::parse($dateVal)->diffForHumans() : '';
    }

    public function addSearchToQuery($query, $request, $columns)
    {
        if ($request->search) {
            $query->where(function ($childQuery) use ($request, $columns) {
                foreach ($columns as $column) {
                    if (! is_string($column)) {
                        continue;
                    }
                    $columnFullName = (strpos($column, ' as ') !== false) ? (explode(' as ', $column)[0]) : $column;
                    $childQuery->orWhereRaw("$columnFullName like ?", "%$request->search%");
                }
            });
        }
    }

    public function getUploadedFileUrl(string $path): string
    {
        return UploadService::exists($path)
            ? UploadService::preview($path)
            : asset('v2/img/user.png');
    }
}
