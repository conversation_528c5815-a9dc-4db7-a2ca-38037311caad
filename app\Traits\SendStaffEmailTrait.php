<?php

namespace App\Traits;

use App\DTO\staff\SendQueueEmailPayload;
use App\Helpers\Helpers;
use App\Model\SendMail;
use App\Model\v2\FailedEmails;
use App\Model\v2\SmtpSetup;
use App\Model\v2\Staff;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Config;
use Illuminate\Support\Facades\File;
use Illuminate\Support\Facades\Mail;

trait SendStaffEmailTrait
{
    use CommonTrait;

    public function sendMailToStaffTrait(SendQueueEmailPayload $dto)
    {
        $payload = $dto->toArray();
        $staffIDs = explode(',', $payload['staff_id']);
        $staffArrInfo = Staff::whereIn('id', $staffIDs)->get();
        [$attachment, $attachmentLogData, $existAttachment, $existAttachmentLogData] = $this->getAttachmentsData($payload);

        $failReason = '';
        $checkMailListener = false;

        $failedStaffId = [];
        $failedStaffIdWithReason = [];
        $successStaffIdWithName = [];

        foreach ($staffArrInfo as $staffRow) {
            if ($staffRow->id) {

                if (! $checkMailListener) {
                    $failReason = $this->checkTestMail($payload);
                    $checkMailListener = true;
                }

                if ($checkMailListener && empty($failReason)) {
                    $mailData = $this->prepareStaffMailData($staffRow, $payload, $existAttachment, $attachment);
                    $mailData['attachmentLogData'] = array_merge($attachmentLogData, $existAttachmentLogData);
                    $mailData['attachFile'] = array_merge($payload['attachment_file'], $existAttachment);
                    $sentEmail = $this->sendStaffEmailEvent($mailData, new Request($payload));

                    if ($sentEmail['status'] == 'fail') {
                        $failedStaffIdWithReason[] = [
                            'type' => 'email-not-valid',
                            'reason' => $sentEmail['message'],
                        ];
                        $failedStaffId[] = $staffRow->id;
                    } else {
                        $successStaffIdWithName[] = $staffRow->first_name.' '.$staffRow->last_name;
                    }
                } else {
                    $failedStaffId[] = $staffRow->id;
                    $failedStaffIdWithReason[] = [
                        'type' => 'mail-provider-not-work',
                        'reason' => $failReason,
                    ];
                }
            }
        }

        return [
            'failed' => $failedStaffId,
            'failed_staff_id_with_reason' => $failedStaffIdWithReason,
            'success_staff_id_With_name' => $successStaffIdWithName,
        ];
    }

    private function getAttachmentsData($payload)
    {
        // Retrieve existing attachments
        $emailExistAttachmentIdArr = ($payload['existing_attachment_id'] != '') ? explode(',', $payload['existing_attachment_id']) : [];

        [$existAttachment, $existAttachmentLogData] = $this->retrieveExistingAttachments($emailExistAttachmentIdArr, $payload['email_template_id'], $payload['college_id']);

        // Retrieve attachment files
        $attachment = $attachmentLogData = [];
        if ($payload['attachment_file']) {
            [$attachment, $attachmentLogData] = $this->mailAttachment($payload, true);
        }

        return [$attachment, $attachmentLogData, $existAttachment, $existAttachmentLogData];
    }

    private function retrieveExistingAttachments(array $emailExistAttachmentIdArr, $emailTemplateId, int $collegeId): array
    {
        if (empty($emailExistAttachmentIdArr)) {
            return [[], []];
        }

        $existFilePath = Config::get('constants.uploadFilePath.Templates');
        $existDestinationPath = Helpers::changeRootPath($existFilePath, $emailTemplateId, $collegeId);

        $docList = $this->studentProfileCommonRepository->emailTemplateDocModel($emailExistAttachmentIdArr);

        $existAttachment = array_map(function ($doc) use ($existDestinationPath) {
            return $existDestinationPath['default'].$doc['file'];
        }, $docList);

        $existAttachmentLogData = array_column($docList, 'file');
        $existAttachmentLogData = array_combine(
            $existAttachmentLogData,
            array_map(fn ($file) => $existDestinationPath['view'].$file, $existAttachmentLogData)
        );

        return [$existAttachment, $existAttachmentLogData];
    }

    private function mailAttachment($payload, $viewFlag = false)
    {
        $emailAttachments = $payload['attachment_file'];

        return [$emailAttachments, []];

        /*$filePath = Config::get('constants.uploadFilePath.TempMailAttachment');
        $destinationPath = Helpers::changeRootPath($filePath);
        $savedFileName = array();
        $mailFileName = array();
        $mailLogFileName = array();
        $counts = 0;
        $imageGet = 0;
        foreach ($emailAttachments as $emailAttachment) {
            $originalName = $emailAttachment->getClientOriginalName();
            $filename = date('YmdHsi') . '-' . $originalName;
            $savedFileName[] = $filename;
            if ($imageGet == 0) {
                if (!is_dir($destinationPath['default'])) {
                    File::makeDirectory($destinationPath['default'], 0777, true, true);
                }
                $emailAttachment->move($destinationPath['default'], $filename);
                $mailFileName[] = $destinationPath['default'] . $savedFileName[$counts];
                if ($viewFlag) {
                    $mailLogFileName[$originalName] = $destinationPath['view'] . $savedFileName[$counts];
                }
                $counts++;
            }
        }

        if ($viewFlag) {
            return [$mailFileName, $mailLogFileName];
        }
        return $mailFileName;*/
    }

    private function getStaffMailContent($staffId, $emailSubject, $emailContent)
    {
        $filePath = Config::get('constants.uploadFilePath.CollegeLogo');
        $destinationPath = Helpers::changeRootPath($filePath);

        $arrStaffData = (new Staff)->getStaffEmailContent($staffId);

        if (! empty($arrStaffData)) {
            $row = $arrStaffData[0];
            // $domain = env('APP_URL');
            $domain = url('/');

            $basePath = $destinationPath['view'];

            $college_logo_url = $domain.str_replace('\\', '/', $basePath).$row['college_logo'];
            $college_signature_url = $domain.str_replace('\\', '/', $basePath).$row['college_signature'];
            $dean_signature_url = $domain.str_replace('\\', '/', $basePath).$row['dean_signature'];

            $college_logo = '<img src="'.$college_logo_url.'" alt="College Logo" style="height: auto; width: 150px; padding-left: 49px; padding-top: 20px;"  />';
            $college_signature = '<img src="'.$college_signature_url.'" alt="College Signature" style="height: auto; width: 150px; padding-left: 49px; padding-top: 20px;"  />';
            $dean_signature = '<img src="'.$dean_signature_url.'" alt="Dean/CEO Signature" style="height: auto; width: 150px; padding-left: 49px; padding-top: 20px;"  />';

            $dataArr = [
                '{AlterEmail1}' => '',
                '{AlterEmail2}' => '',
                '{CollegeLogo}' => $college_logo,
                '{CollegeEmail}' => $row['college_email'],
                '{Country}' => $row['country_name'],
                '{CountryBirth}' => '',
                '{CurrentDate}' => date('d-m-Y'),
                '{DoB}' => '',
                '{DOB}' => '',
                '{DoB Without Stroke}' => '******************',
                '{Email}' => $row['staff_email'],
                '{ExpDate}' => '',
                '{Fax}' => $row['fax'],
                '{StudentId}' => '',
                '{FirstName}' => $row['first_name'],
                '{MiddleName}' => '',
                '{LastName}' => $row['last_name'],
                '{Gender}' => '',
                '{Mobile}' => $row['mobile'],
                '{Nationality}' => $row['nationality'],
                '{NickName}' => '',
                '{PassportNo}' => '',
                '{Phone}' => $row['phone'],
                '{Postcode}' => $row['postcode'],
                '{State}' => $row['state'],
                '{StreetAddress}' => $row['address'],
                '{StreetNumber}' => '',
                '{UnitDetail}' => '',
                '{BuildingName}' => '',
                '{Suburb}' => $row['city_town'],
                '{Title}' => $row['name_title'],
                '{UserName}' => $row['staff_name'],
                '{VisaType}' => '',
                '{CourseCode}' => '',
                '{CourseName}' => '',
                '{CollegeRtoCode}' => $row['RTO_code'],
                '{CollegeCircosCode}' => $row['CRICOS_code'],
                '{CollegeLegalName}' => $row['legal_name'],
                '{CollegeName}' => $row['entity_name'],
                '{CollegeSignature}' => $college_signature,
                '{DeanName}' => $row['dean_name'],
                '{DeanSignature}' => $dean_signature,
                '{CollegeContactPerson}' => $row['contact_person'],
                '{CollegeContactPhone}' => $row['contact_phone'],
                '{CollegeURL}' => $row['college_url'],
                '{CollegeABN}' => $row['college_ABN'],
                '{CollegeFax}' => $row['fax'],
                '{CourseType}' => '',
                '{Campus}' => '',
                '{StudentType}' => '',
                '{TeacherFirstName}' => $row['teacher_first_name'],
                '{TeacherLastName}' => $row['teacher_last_name'],
                '{TeacherEmail}' => $row['teacher_email'],
                '{TeacherMobile}' => $row['teacher_mobile'],
                '{AgencyName}' => '',
                '{AgentName}' => '',
                '{AgentEmail}' => '',
                '{AgentTelephone}' => '',
                '{EnrolledCourseList}' => '',
                '{OfferedCourseList}' => '',
                '{CourseStartDate}' => '',
                '{CourseEndDate}' => '',
                '{CourseDuration}' => '',
                '{StudentContactEmail}' => '',
                '{StudentAlternateEmail}' => '',
                '{StudentEmergencyEmail}' => '',
            ];

            foreach ($dataArr as $key => $value) {
                $emailContent = str_replace("$key", $value, $emailContent);
                $emailSubject = str_replace("$key", $value, $emailSubject);
            }

            return [$emailSubject, $emailContent];
        } else {
            return false;
        }
    }

    private function prepareStaffMailData($staffRow, $payload, $existAttachment, $attachment)
    {
        $emailCC = ! empty($payload['email_cc']) ? explode(',', $payload['email_cc']) : [];
        $emailBCC = ! empty($payload['email_bcc']) ? explode(',', $payload['email_bcc']) : [];
        $replyToEmail = $payload['reply_to_email'] ?? '';

        [$convertedSubject, $convertedData] = $this->getStaffMailContent($staffRow->id, $payload['email_subject'], $payload['email_content']);

        return [
            'id' => $staffRow->id,
            'to' => $staffRow->email,
            'from' => $payload['email_from'],
            'cc' => $emailCC,
            'bcc' => $emailBCC,
            'replyTo' => $replyToEmail,
            'subject' => $convertedSubject,
            'body' => $convertedData,
            'attachFile' => array_merge($existAttachment, $attachment),
        ];
    }

    public function checkTestMail($payload)
    {
        $tempMailData = Config::get('constants.testMailData');
        $tempMailData['college_id'] = $payload['college_id'];
        $res = $this->sendSmtpTestMail($tempMailData);

        return $res['status'] ? '' : $res['message'];
    }

    private function sendSmtpTestMail($mailData)
    {
        $collegeId = $mailData['college_id'];
        $mail = SmtpSetup::getSMTPDetail();

        if (($mail) && ($mail->status)) {
            $this->setSMTP();
        }

        $fromEmail = $mail && $mail->status ? $mail->email : config('mail.from.address');
        $fromEmailName = $mail && $mail->status ? $mail->name : config('mail.from.name');

        $mailData['from'] = $fromEmail;
        $mailData['fromName'] = $fromEmailName;

        $page = $mailData['page'] ?? [];
        $data = $mailData['data'] ?? [];

        try {
            ini_set('memory_limit', '1024M');
            ini_set('max_execution_time', 180); // 3 minutes

            Mail::send($page, $data, function ($message) use ($mailData) {
                $message->from(($mailData['from'] = ! '' ? $mailData['from'] : '<EMAIL>'), $mailData['fromName']);
                $message->to($mailData['to']);
                $message->subject($mailData['subject']);
                $message->html($mailData['body']);
            });

            return ['status' => true, 'message' => 'Email sent successfully'];

        } catch (\Exception  $exception) {
            $errorMessage = $exception->getMessage();
            $contentText = (isset($mailData['data']['content'])) ? $mailData['data']['content'] : (isset($mailData['body']) ? $mailData['body'] : '');
            $arrFailMail = [
                'sender' => ($mailData['from'] != '') ? $mailData['from'] : '<EMAIL>',
                'receiver' => $mailData['to'],
                'subject' => $mailData['subject'],
                'content' => $contentText,
                'error_message' => $errorMessage,
                'error_code' => $exception->getCode(),
            ];

            (new FailedEmails)->saveFailedEmails($collegeId, $arrFailMail);

            return ['status' => false, 'message' => $errorMessage];
        }
    }

    private function setSMTP()
    {
        $config = SmtpSetup::where('status', 1)->first(); // Adjust the query to suit your needs
        if ($config) {
            // Set mail configuration
            Config::set('mail.mailers.smtp.transport', 'smtp');
            Config::set('mail.mailers.smtp.host', $config->host);
            Config::set('mail.mailers.smtp.port', $config->port);
            Config::set('mail.mailers.smtp.username', $config->username);
            Config::set('mail.mailers.smtp.password', $config->password);
            Config::set('mail.mailers.smtp.encryption', $config->encryption);
            Config::set('mail.from.address', $config->email);
            Config::set('mail.from.name', $config->name);
        }
    }

    private function sendStaffEmailEvent($mailData, $requestData)
    {
        $mailData['college_id'] = $requestData['college_id'];
        $mailStatus = (new SendMail)->sendSmtpMailQueue($mailData);
        if ($mailStatus['status'] == 'success' && ! empty($requestData['staff_id']) && $requestData['offer_comm_log'] == 'on') {
            $this->communicationLog->saveStaffCommunicationLog(
                $requestData,
                $mailData['id'],
                $mailData['body'],
                $mailData['to'],
                $mailData['from'],
                $mailData['attachmentLogData'],
                $mailData
            );
        }

        return $mailStatus;
    }
}
