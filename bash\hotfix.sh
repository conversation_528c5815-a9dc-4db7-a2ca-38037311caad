#!/bin/bash

set -euo pipefail

die () {
    echo >&2 "ERROR: $*"
    exit 1
}

# If a branch name is provided
if [ "$#" -ge 1 ]; then
    prefix="hotfix/"
    branch=$1;
    branch=${branch#"$prefix"}
    full_branch="hotfix/$branch"
    [[ "$full_branch" == hotfix/* ]] || die "Expected hotfix branch name starting with 'hotfix/', got: $full_branch"

    commit_message="${2:-"hotfix applied: $branch"}"

    echo "🔥 Running full hotfix workflow for branch: $full_branch"

    # Checkout and pull the hotfix branch
    git checkout "$full_branch"
    # git pull origin "$full_branch"

    echo "Staging and committing changes..."
    git add .
    git diff --cached --quiet || git commit -m "$commit_message"
    git push origin "$full_branch"

    # Merge hotfix into master
    git checkout master
    git pull origin master
    git merge --no-ff "$full_branch" -m "hotfix $branch applied"
    git push origin master

    echo "✅ Hotfix merged to master"

else
    echo "⚠️ No hotfix branch provided — assuming hotfix already merged to master"
    echo "🔄 Pulling latest master to continue with merge..."
    git checkout master
    git pull origin master
fi

# Continue to propagate master → staging → development
for target in staging development; do
    echo "📌 Merging master → $target"
    git checkout "$target"
    git pull origin "$target"
    git merge --no-ff master -m "sync with master"
    git push origin "$target"
done

echo "✅ Done syncing hotfix to staging and development"
