#!/bin/bash
die () {
    echo >&2 "$@"
    exit 1
}
[ "$#" -gt 0 ] || die "release branch version required, $# provided"
set -e
echo $1
prefix="release/"
branch=$1;
branch=${branch#"$prefix"}
commitmessage=${2-"release $branch"}
echo $commitmessage;
echo "readying up the release note"
/bin/bash ./bash/release_note.sh $branch
# Dev
git checkout development
git pull origin development
git add releases
git commit -m 'release doc generated'  || true
git push origin development
# Staging
git checkout staging
git pull origin staging
git merge development
git add .
git commit -m "new release $branch ready" || true
git push origin staging

#release branch
git checkout -b release/$branch
# npm install
# npx kendo-ui-license activate
# npm run prod
git add .
git commit -m "release $branch build" || true
git push origin release/$branch
# master
git checkout master
git pull origin master
git merge release/$branch
git add .
git commit -m "release $branch merged" || true
git tag -a $branch -m "releabse  $branch"
git push origin master --follow-tags
rm -rf .changelog/unreleased/archive


