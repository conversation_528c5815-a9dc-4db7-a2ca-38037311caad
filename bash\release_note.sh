#!/bin/bash

die () {
    echo >&2 "$@"
    exit 1
}

[ "$#" -gt 0 ] || die "release branch version required, $# provided"

set -e

release=$1
date=$(date '+%Y-%m-%d')
version_heading="## VERSION $release ($date)"

echo "Generating release notes for version: $release on $date"

unreleased_dir="./.changelog/unreleased"
release_file="./releases/release.md"
final_file="./releases/notes/$release.md"
stub_file="./releases/stub"

# Initialize grouped content variables
features=""
bugfixes=""
improvements=""
others=""

# Loop through all markdown files in unreleased directory
for file in "$unreleased_dir"/*.md; do
    [ -e "$file" ] || continue # Skip if no files

    content=$(cat "$file")
    
    if [[ "$file" == *-feature.md ]]; then
        features+=$'\n- '"${content}"
    elif [[ "$file" == *-bugfix.md ]]; then
        bugfixes+=$'\n- '"${content}"
    elif [[ "$file" == *-improvement.md ]]; then
        improvements+=$'\n- '"${content}"
    else
        others+=$'\n- '"${content}"
    fi
done

# Build the release content
release_notes="$version_heading"

if [ -n "$bugfixes" ]; then
    release_notes+=$'\n\n#### Bugfixes'"$bugfixes"
fi

if [ -n "$features" ]; then
    release_notes+=$'\n\n#### Features'"$features"
fi

if [ -n "$improvements" ]; then
    release_notes+=$'\n\n#### Improvements'"$improvements"
fi

if [ -n "$others" ]; then
    release_notes+=$'\n\n#### Other'"$others"
fi

# Write to release.md and copy to versioned file
echo "$release_notes" > "$release_file"
cp "$release_file" "$final_file"

# Reset release.md to stub
cp "$stub_file" "$release_file"

# Optional: Move processed files to archive
mkdir -p "$unreleased_dir/archive"
mv "$unreleased_dir"/*.md "$unreleased_dir/archive/" 2>/dev/null || true

echo "Release notes generated and saved to $final_file"
