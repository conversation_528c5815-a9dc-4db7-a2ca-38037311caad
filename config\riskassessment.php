<?php

return [
    'categories' => [
        [
            'category' => \Domains\Students\RiskAssessment\Models\StudentRiskAssessment::RISK_CATEGORY_PAYMENT,
            'key' => 'payment',
            'validation_type' => 'array', // For checkboxes/arrays
            'parameters' => [],
            'handler' => \Domains\Students\RiskAssessment\Handlers\PaymentRiskAssessmentHandler::class,
        ],
        [
            'category' => \Domains\Students\RiskAssessment\Models\StudentRiskAssessment::RISK_CATEGORY_RESULT,
            'key' => 'results',
            'validation_type' => 'percentage', // For 0-100 percentage values
            'parameters' => [],
            'handler' => \Domains\Students\RiskAssessment\Handlers\ResultRiskAssessmentHandler::class,
        ],
        [
            'category' => \Domains\Students\RiskAssessment\Models\StudentRiskAssessment::RISK_CATEGORY_ATTENDANCE,
            'key' => 'attendance',
            'validation_type' => 'percentage', // For 0-100 percentage values
            'parameters' => [],
            'handler' => \Domains\Students\RiskAssessment\Handlers\AttendanceRiskAssessmentHandler::class,
        ],
        [
            'category' => \Domains\Students\RiskAssessment\Models\StudentRiskAssessment::RISK_CATEGORY_MOODLE,
            'key' => 'moodle',
            'validation_type' => 'percentage', // For 0-100 percentage values
            'parameters' => [],
            'handler' => \Domains\Students\RiskAssessment\Handlers\MoodleActivityRiskAssessmentHandler::class,
        ],
        // Example: Adding a new category is now as simple as adding it here
        // [
        //     'category' => \Domains\Students\RiskAssessment\Models\StudentRiskAssessment::RISK_CATEGORY_BEHAVIOR,
        //     'key' => 'behavior',
        //     'validation_type' => 'percentage', // or 'array' for checkbox-based criteria
        //     'parameters' => [
        //         'minimum_behavior_score' => 75,
        //     ],
        //     'handler' => \Domains\Students\RiskAssessment\Handlers\BehaviorRiskAssessmentHandler::class,
        // ],
    ],

    'risk_types' => [
        \Domains\Students\RiskAssessment\Models\StudentRiskAssessment::RISK_TYPE_NONE => 'None',
        \Domains\Students\RiskAssessment\Models\StudentRiskAssessment::RISK_TYPE_LOW => 'Low',
        \Domains\Students\RiskAssessment\Models\StudentRiskAssessment::RISK_TYPE_MEDIUM => 'Medium',
        \Domains\Students\RiskAssessment\Models\StudentRiskAssessment::RISK_TYPE_HIGH => 'High',
    ],

    'assessment_levels' => [
        \Domains\Students\RiskAssessment\Models\StudentRiskAssessmentSemester::LEVEL_FIRST => [
            'name' => 'First Level (Low Risk)',
            'weeks_setting_key' => 'lowRiskWeeks',
            'default_weeks' => 3,
        ],
        \Domains\Students\RiskAssessment\Models\StudentRiskAssessmentSemester::LEVEL_SECOND => [
            'name' => 'Second Level (Medium Risk)',
            'weeks_setting_key' => 'mediumRiskWeeks',
            'default_weeks' => 6,
        ],
        \Domains\Students\RiskAssessment\Models\StudentRiskAssessmentSemester::LEVEL_FINAL => [
            'name' => 'Final Level (High Risk)',
            'weeks_setting_key' => 'highRiskWeeks',
            'default_weeks' => 12,
        ],
    ],
];
