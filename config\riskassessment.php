<?php

use Domains\Students\RiskAssessment\Handlers\AttendanceRiskAssessmentHandler;
use Domains\Students\RiskAssessment\Handlers\MoodleActivityRiskAssessmentHandler;
use Domains\Students\RiskAssessment\Handlers\PaymentRiskAssessmentHandler;
use Domains\Students\RiskAssessment\Handlers\ResultRiskAssessmentHandler;
use Domains\Students\RiskAssessment\Models\StudentRiskAssessment;

return [
    'categories' => [
        [
            'category' => StudentRiskAssessment::RISK_CATEGORY_PAYMENT,
            'key' => 'payment',
            'validation_type' => 'array', // For checkboxes/arrays
            'parameters' => [],
            'handler' => PaymentRiskAssessmentHandler::class,
        ],
        [
            'category' => StudentRiskAssessment::RISK_CATEGORY_RESULT,
            'key' => 'results',
            'validation_type' => 'percentage', // For 0-100 percentage values
            'parameters' => [],
            'handler' => ResultRiskAssessmentHandler::class,
        ],
        [
            'category' => StudentRiskAssessment::RISK_CATEGORY_ATTENDANCE,
            'key' => 'attendance',
            'validation_type' => 'percentage', // For 0-100 percentage values
            'parameters' => [],
            'handler' => AttendanceRiskAssessmentHandler::class,
        ],
        [
            'category' => StudentRiskAssessment::RISK_CATEGORY_MOODLE,
            'key' => 'moodle',
            'validation_type' => 'percentage', // For 0-100 percentage values
            'parameters' => [],
            'handler' => MoodleActivityRiskAssessmentHandler::class,
        ],
        // Example: Adding a new category is now as simple as adding it here
        // [
        //     'category' => StudentRiskAssessment::RISK_CATEGORY_BEHAVIOR,
        //     'key' => 'behavior',
        //     'validation_type' => 'percentage', // or 'array' for checkbox-based criteria
        //     'parameters' => [
        //         'minimum_behavior_score' => 75,
        //     ],
        //     'handler' => \Domains\Students\RiskAssessment\Handlers\BehaviorRiskAssessmentHandler::class,
        // ],
    ],

    'risk_types' => [
        StudentRiskAssessment::RISK_TYPE_NONE => 'None',
        StudentRiskAssessment::RISK_TYPE_LOW => 'Low',
        StudentRiskAssessment::RISK_TYPE_MEDIUM => 'Medium',
        StudentRiskAssessment::RISK_TYPE_HIGH => 'High',
    ],

    'assessment_levels' => [
        StudentRiskAssessment::RISK_TYPE_LOW => [
            'name' => 'First Level (Low Risk)',
            'weeks_setting_key' => 'lowRiskWeeks',
            'default_weeks' => 3,
        ],
        StudentRiskAssessment::RISK_TYPE_MEDIUM => [
            'name' => 'Second Level (Medium Risk)',
            'weeks_setting_key' => 'mediumRiskWeeks',
            'default_weeks' => 6,
        ],
        StudentRiskAssessment::RISK_TYPE_HIGH => [
            'name' => 'Final Level (High Risk)',
            'weeks_setting_key' => 'highRiskWeeks',
            'default_weeks' => 12,
        ],
    ],
];
