<?php

use App\Model\v2\CollegeMaterials;
use App\Model\v2\Courses;
use App\Model\v2\Student;
use App\Model\v2\TaskNew;

return [

    /*
    |--------------------------------------------------------------------------
    | Default Search Engine
    |--------------------------------------------------------------------------
    |
    | This option controls the default search connection that gets used while
    | using Laravel Scout. This connection is used when syncing all models
    | to the search service. You should adjust this based on your needs.
    |
    | Supported: "algolia", "meilisearch", "database", "collection", "null"
    |
    */

    'driver' => env('SCOUT_DRIVER', null),

    /*
    |--------------------------------------------------------------------------
    | Index Prefix
    |--------------------------------------------------------------------------
    |
    | Here you may specify a prefix that will be applied to all search index
    | names used by Scout. This prefix may be useful if you have multiple
    | "tenants" or applications sharing the same search infrastructure.
    |
    */

    'prefix' => env('SCOUT_PREFIX', 'live'),

    /*
    |--------------------------------------------------------------------------
    | Queue Data Syncing
    |--------------------------------------------------------------------------
    |
    | This option allows you to control if the operations that sync your data
    | with your search engines are queued. When this is set to "true" then
    | all automatic data syncing will get queued for better performance.
    |
    */

    'queue' => env('SCOUT_QUEUE', false),

    /*
    |--------------------------------------------------------------------------
    | Database Transactions
    |--------------------------------------------------------------------------
    |
    | This configuration option determines if your data will only be synced
    | with your search indexes after every open database transaction has
    | been committed, thus preventing any discarded data from syncing.
    |
    */

    'after_commit' => false,

    /*
    |--------------------------------------------------------------------------
    | Chunk Sizes
    |--------------------------------------------------------------------------
    |
    | These options allow you to control the maximum chunk size when you are
    | mass importing data into the search engine. This allows you to fine
    | tune each of these chunk sizes based on the power of the servers.
    |
    */

    'chunk' => [
        'searchable' => 500,
        'unsearchable' => 500,
    ],

    /*
    |--------------------------------------------------------------------------
    | Soft Deletes
    |--------------------------------------------------------------------------
    |
    | This option allows to control whether to keep soft deleted records in
    | the search indexes. Maintaining soft deleted records can be useful
    | if your application still needs to search for the records later.
    |
    */

    'soft_delete' => false,

    /*
    |--------------------------------------------------------------------------
    | Identify User
    |--------------------------------------------------------------------------
    |
    | This option allows you to control whether to notify the search engine
    | of the user performing the search. This is sometimes useful if the
    | engine supports any analytics based on this application's users.
    |
    | Supported engines: "algolia"
    |
    */

    'identify' => env('SCOUT_IDENTIFY', false),

    /*
    |--------------------------------------------------------------------------
    | Algolia Configuration
    |--------------------------------------------------------------------------
    |
    | Here you may configure your Algolia settings. Algolia is a cloud hosted
    | search engine which works great with Scout out of the box. Just plug
    | in your application ID and admin API key to get started searching.
    |
    */

    'algolia' => [
        'id' => env('ALGOLIA_APP_ID', ''),
        'secret' => env('ALGOLIA_SECRET', ''),
    ],

    /*
    |--------------------------------------------------------------------------
    | Meilisearch Configuration
    |--------------------------------------------------------------------------
    |
    | Here you may configure your Meilisearch settings. Meilisearch is an open
    | source search engine with minimal configuration. Below, you can state
    | the host and key information for your own Meilisearch installation.
    |
    | See: https://www.meilisearch.com/docs/learn/configuration/instance_options#all-instance-options
    |
    */

    'meilisearch' => [
        'host' => env('MEILISEARCH_HOST', 'http://localhost:7700'),
        'key' => env('MEILISEARCH_KEY'),
        'index-settings' => [
            Student::class => [
                'filterableAttributes' => [/* 'name_title', 'first_name', 'middel_name', 'family_name', 'optional_email', 'email', */
                    'is_student',
                    'generated_stud_id',
                    'nationality',
                    'application_reference_id',
                    'courses.course.id',
                    'courses.id_with_status',
                    'courses.id_with_intake_date',
                    'courses.id_with_status_with_intake_date',
                    'courses.status_with_intake_date',
                    /* 'courses.intake_year', */
                    'courses.status',
                    'courses.campus_id',
                    'student_type',
                    'enrollments.batch',
                    'enrollments.teacher_id',
                    'courses.intake_date',
                    'courses.campus.name',
                ],
                'sortableAttributes' => ['created_at', 'courses.created_at', 'generated_stud_id', 'first_name', 'student_type'],
                'rankingRules' => [
                    'words',
                    'proximity',
                    'sort',
                    'attribute',
                    'exactness',
                ],
                'typoTolerance' => [
                    'enabled' => true,
                    // 'enabled' => false
                    'disableOnAttributes' => ['generated_stud_id', 'application_reference_id', 'courses.course.id'],
                ],
                // "pagination" => [
                //     "maxTotalHits" => 500
                // ],
            ],
            Courses::class => [
                'filterableAttributes' => [/* 'course_code', 'course_name', 'cricos_code', 'qualification_prerequisite_id', 'course_duration', 'tuition_fee', 'domestic_fee' */],
                'sortableAttributes' => ['created_at'],
            ],
            TaskNew::class => [
                'filterableAttributes' => [],
            ],
            CollegeMaterials::class => [
                'filterableAttributes' => [],
            ],
            // 'users' => [
            //     'filterableAttributes'=> ['id', 'name', 'email'],
            // ],
        ],
    ],

];
