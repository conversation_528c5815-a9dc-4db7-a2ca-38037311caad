<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('rto_student_scholarship', function (Blueprint $table) {
            $table->double('transfer_amount')->nullable()->default(0)->after('is_transfer');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('rto_student_scholarship', function (Blueprint $table) {
            $table->double('transfer_amount');
        });
    }
};
