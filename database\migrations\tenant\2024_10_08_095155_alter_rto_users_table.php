<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('rto_users', function (Blueprint $table) {
            $table->date('last_password_updated_date')->nullable()->default(null)->after('first_time_reset_pwd');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('rto_users', function (Blueprint $table) {
            $table->date('last_password_updated_date');
        });
    }
};
