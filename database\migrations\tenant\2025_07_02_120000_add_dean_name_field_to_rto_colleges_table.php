<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('rto_colleges', function (Blueprint $table) {
            $table->string('dean_name')->nullable()->default(null)->after('admission_manager_signature');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('rto_colleges', function (Blueprint $table) {
            $table->dropColumn('dean_name');
        });
    }
};
