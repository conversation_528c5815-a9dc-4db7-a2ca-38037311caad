<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (! Schema::hasTable('student_risk_assessments')) {
            Schema::create('student_risk_assessments', function (Blueprint $table) {
                $table->id();
                $table->unsignedInteger('student_id');
                $table->unsignedInteger('course_id');
                $table->tinyInteger('risk_level')->default(0)->comment('0-3: Low, Medium, High, Critical');
                $table->timestamps();

                $table->index(['student_id', 'course_id'], 'sra_student_course_idx');
                $table->index(['risk_level'], 'sra_risk_level_idx');
            });

            // Note: Foreign key constraints are intentionally omitted to ensure
            // compatibility across all tenants with different database schemas.
            // The indexes above provide performance benefits for joins.
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Disable foreign key checks temporarily
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');

        Schema::dropIfExists('student_risk_assessments');

        // Re-enable foreign key checks
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');
    }
};
