<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        if (! Schema::hasTable('student_risk_assessment_semesters')) {
            Schema::create('student_risk_assessment_semesters', function (Blueprint $table) {
                $table->id();
                $table->unsignedBigInteger('risk_assessment_id');
                // Note: Foreign key constraint omitted for cross-tenant compatibility
                $table->foreignId('semester_id')->nullable()/* ->constrained('semesters')->onDelete('cascade') */;
                $table->enum('risk_category', ['attendance', 'result', 'payment', 'moodle_activity'])->comment('attendance, result, payment, moodle_activity');
                $table->tinyInteger('risk_type')->default(0)->comment('0-3: Low, Medium, High, Critical');
                $table->date('queue_date');
                $table->enum('status', ['pending', 'in_progress', 'completed', 'cancelled'])->default('pending')->comment('pending, in_progress, completed, cancelled');
                $table->text('remarks')->nullable();
                $table->json('data')->nullable();
                $table->json('config')->nullable();
                $table->timestamps();

                $table->index(['risk_assessment_id', 'semester_id'], 'sras_risk_semester_idx');
                $table->index(['risk_category'], 'sras_risk_category_idx');
                $table->index(['queue_date'], 'sras_queue_date_idx');
                $table->index(['risk_type'], 'sras_risk_type_idx');
            });
        }
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        // Disable foreign key checks temporarily
        DB::statement('SET FOREIGN_KEY_CHECKS=0;');

        Schema::dropIfExists('student_risk_assessment_semesters');

        // Re-enable foreign key checks
        DB::statement('SET FOREIGN_KEY_CHECKS=1;');
    }
};
