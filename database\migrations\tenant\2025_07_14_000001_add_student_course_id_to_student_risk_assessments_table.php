<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('student_risk_assessments', function (Blueprint $table) {
            $table->unsignedInteger('student_course_id')->nullable()->after('course_id');

            // Add index for the new column
            $table->index(['student_course_id'], 'sra_student_course_id_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('student_risk_assessments', function (Blueprint $table) {
            $table->dropIndex('sra_student_course_id_idx');
            $table->dropColumn('student_course_id');
        });
    }
};
