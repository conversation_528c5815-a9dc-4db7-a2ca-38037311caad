<?php

use Illuminate\Database\Migrations\Migration;
use Illuminate\Database\Schema\Blueprint;
use Illuminate\Support\Facades\Schema;

return new class extends Migration
{
    /**
     * Run the migrations.
     */
    public function up(): void
    {
        Schema::table('student_risk_assessment_semesters', function (Blueprint $table) {
            $table->tinyInteger('is_notify')->default(0)->after('status')->comment('0: Not notified, 1: Notified');

            // Add index for the new column
            $table->index(['is_notify'], 'sras_is_notify_idx');
        });
    }

    /**
     * Reverse the migrations.
     */
    public function down(): void
    {
        Schema::table('student_risk_assessment_semesters', function (Blueprint $table) {
            $table->dropIndex('sras_is_notify_idx');
            $table->dropColumn('is_notify');
        });
    }
};
