"id","parameter_name","parameter_value","title","created_at","updated_at","created_by","updated_by"
"1","AlterEmail1","{AlterEmail1}","{""type"": ""text"",""value"": ""Alter Email""}","2017-08-28 12:35:00","2017-08-28 07:12:19","78","78"
"2","AlterEmail2","{AlterEmail2}","{""type"": ""text"",""value"": ""Alter Email""}","2017-08-28 12:35:00","2017-08-28 07:12:19","78","78"
"3","CollegeEmail","{CollegeEmail}","{""type"": ""text"",""value"": ""College Email""}","2017-08-28 12:35:00","2017-08-28 07:12:19","78","78"
"4","Country","{Country}","{""type"": ""text"",""value"": ""Country""}","2017-08-28 12:35:00","2017-08-28 07:12:19","78","78"
"5","CountryBirth","{CountryBirth}","{""type"": ""text"",""value"": ""Country Birth""}","2017-08-28 12:35:00","2017-08-28 07:12:19","78","78"
"6","CurrentDate","{CurrentDate}","{""type"": ""text"",""value"": ""Current Date""}","2017-08-28 12:35:00","2017-08-28 07:12:19","78","78"
"7","CurrentDate","{CurrentDate}","{""type"": ""text"",""value"": ""Current Date""}","2017-08-28 12:35:00","2017-08-28 07:12:19","78","78"
"8","DOB","{DOB}","{""type"": ""text"",""value"": ""Date of birth""}","2017-08-28 12:35:00","2017-08-28 07:12:19","78","78"
"9","DoB Without Stroke","{DoB Without Stroke}","{""type"": ""text"",""value"": ""DoB Without Stroke""}","2017-08-28 12:35:00","2017-08-28 07:12:19","78","78"
"10","Email","{Email}","{""type"": ""text"",""value"": ""Email""}","2017-08-28 12:35:00","2017-08-28 07:12:19","78","78"
"11","ExpDate","{ExpDate}","{""type"": ""text"",""value"": ""Expiry Date""}","2017-08-28 12:35:00","2017-08-28 07:12:19","78","78"
"12","Fax","{Fax}","{""type"": ""text"",""value"": ""Fax number""}","2017-08-28 12:35:00","2017-08-28 07:12:19","78","78"
"13","FirstName","{FirstName}","{""type"": ""text"",""value"": ""First Name""}","2017-08-28 12:35:00","2017-08-28 07:12:19","78","78"
"14","Gender","{Gender}","{""type"": ""text"",""value"": ""Gender""}","2017-08-28 12:35:00","2017-08-28 07:12:19","78","78"
"15","LastName","{LastName}","{""type"": ""text"",""value"": ""Last Name""}","2017-08-28 12:35:00","2017-08-28 07:12:19","78","78"
"16","MiddleName","{MiddleName}","{""type"": ""text"",""value"": ""Middle Name""}","2017-08-28 12:35:00","2017-08-28 07:12:19","78","78"
"17","Mobile","{Mobile}","{""type"": ""text"",""value"": ""Mobile""}","2017-08-28 12:35:00","2017-08-28 07:12:19","78","78"
"18","Nationality","{Nationality}","{""type"": ""text"",""value"": ""Nationality""}","2017-08-28 12:35:00","2017-08-28 07:12:19","78","78"
"19","NickName","{NickName}","{""type"": ""text"",""value"": ""Nick Name""}","2017-08-28 12:35:00","2017-08-28 07:12:19","78","78"
"20","PassportNo","{PassportNo}","{""type"": ""text"",""value"": ""Passport Number""}","2017-08-28 12:35:00","2017-08-28 07:12:19","78","78"
"21","Phone","{Phone}","{""type"": ""text"",""value"": ""Phone""}","2017-08-28 12:35:00","2017-08-28 07:12:19","78","78"
"22","Postcode","{Postcode}","{""type"": ""text"",""value"": ""Postcode""}","2017-08-28 12:35:00","2017-08-28 07:12:19","78","78"
"23","State","{State}","{""type"": ""text"",""value"": ""State""}","2017-08-28 12:35:00","2017-08-28 07:12:19","78","78"
"24","StreetAddress","{StreetAddress}","{""type"": ""text"",""value"": ""Street Address""}","2017-08-28 12:35:00","2017-08-28 07:12:19","78","78"
"25","StudentId","{StudentId}","{""type"": ""text"",""value"": ""Student Id""}","2017-08-28 12:35:00","2017-08-28 07:12:19","78","78"
"26","Suburb","{Suburb}","{""type"": ""text"",""value"": ""Suburb""}","2017-08-28 12:35:00","2017-08-28 07:12:19","78","78"
"27","Title","{Title}","{""type"": ""text"",""value"": ""Title""}","2017-08-28 12:35:00","2017-08-28 07:12:19","78","78"
"28","UserName","{UserName}","{""type"": ""text"",""value"": ""User Name""}","2017-08-28 12:35:00","2017-08-28 07:12:19","78","78"
"29","VisaType","{VisaType}","{""type"": ""text"",""value"": ""Visa Type""}","2017-08-28 12:35:00","2017-08-28 07:12:19","78","78"
"30","CollegeRtoCode","{CollegeRtoCode}","{""type"": ""text"",""value"": ""College Rto Code""}","2020-10-26 15:34:59","2020-10-27 21:06:08","1","1"
"31","CollegeCircosCode","{CollegeCircosCode}","{""type"": ""text"",""value"": ""College Circos Code""}","2020-10-26 15:34:59","2020-10-27 21:06:08","1","1"
"32","CollegeLegalName","{CollegeLegalName}","{""type"": ""text"",""value"": ""College Legal Name""}","2020-10-26 15:34:59","2020-10-27 21:06:08","1","1"
"33","CollegeName","{CollegeName}","{""type"": ""text"",""value"": ""College Name""}","2020-10-26 15:34:59","2020-10-27 21:06:08","1","1"
"34","CollegeContactPerson","{CollegeContactPerson}","{""type"": ""text"",""value"": ""College Contact Person""}","2020-10-26 15:34:59","2020-10-27 21:06:08","1","1"
"35","CollegeContactPhone","{CollegeContactPhone}","{""type"": ""text"",""value"": ""College Contact Phone""}","2020-10-26 15:34:59","2020-10-27 21:06:08","1","1"
"36","CollegeURL","{CollegeURL}","{""type"": ""text"",""value"": ""College URL""}","2020-10-26 15:34:59","2020-10-27 21:06:08","1","1"
"37","CollegeABN","{CollegeABN}","{""type"": ""text"",""value"": ""College ABN""}","2020-10-26 15:34:59","2020-10-27 21:06:08","1","1"
"38","CollegeFax","{CollegeFax}","{""type"": ""text"",""value"": ""College Fax""}","2020-10-26 15:34:59","2020-10-27 21:06:08","1","1"
"39","CourseType","{CourseType}","{""type"": ""text"",""value"": ""Course Type""}","2020-10-26 15:34:59","2020-10-27 21:06:08","1","1"
"40","Campus","{Campus}","{""type"": ""text"",""value"": ""Campus""}","2020-10-26 15:34:59","2020-10-27 21:06:08","1","1"
"41","CourseCode","{CourseCode}","{""type"": ""text"",""value"": ""Course Code""}","2020-10-26 15:34:59","2020-10-27 21:06:08","1","1"
"42","CourseName","{CourseName}","{""type"": ""text"",""value"": ""Course Name""}","2020-10-26 15:34:59","2020-10-27 21:06:08","1","1"
"43","StudentType","{StudentType}","{""type"": ""text"",""value"": ""Student Type""}","2020-10-26 15:34:59","2020-10-27 21:06:08","1","1"
"44","TeacherFirstName","{TeacherFirstName}","{""type"": ""text"",""value"": ""Teacher First Name""}","2020-10-26 15:34:59","2020-10-27 21:06:08","1","1"
"45","TeacherLastName","{TeacherLastName}","{""type"": ""text"",""value"": ""Teacher Last Name""}","2020-10-26 15:34:59","2020-10-27 21:06:08","1","1"
"46","TeacherEmail","{TeacherEmail}","{""type"": ""text"",""value"": ""Teacher Email""}","2020-10-26 15:34:59","2020-10-27 21:06:08","1","1"
"47","TeacherMobile","{TeacherMobile}","{""type"": ""text"",""value"": ""Teacher Mobile""}","2020-10-26 15:34:59","2020-10-27 21:06:08","1","1"
"48","AgencyName","{AgencyName}","{""type"": ""text"",""value"": ""Agency Name""}","2020-10-26 15:34:59","2020-10-27 21:06:08","1","1"
"49","AgentName","{AgentName}","{""type"": ""text"",""value"": ""Agent Name""}","2020-10-26 15:34:59","2020-10-27 21:06:08","1","1"
"50","AgentEmail","{AgentEmail}","{""type"": ""text"",""value"": ""Agent Email""}","2020-10-26 15:34:59","2020-10-27 21:06:08","1","1"
"51","AgentTelephone","{AgentTelephone}","{""type"": ""text"",""value"": ""Agent Telephone""}","2020-10-26 15:34:59","2020-10-27 21:06:08","1","1"
"52","CollegeLogo","{CollegeLogo}","{""type"": ""text"",""value"": ""College Logo""}","2020-11-20 15:34:59","2020-11-24 05:02:38","1","1"
"53","EnrolledCourseList","{EnrolledCourseList}","{""type"": ""text"",""value"": ""Enrolled Course List""}","2020-11-20 15:34:59","2020-12-05 00:23:12","1","1"
"54","OfferedCourseList","{OfferedCourseList}","{""type"": ""text"",""value"": ""Offered Course List""}","2020-11-20 15:34:59","2020-12-05 00:23:12","1","1"
"55","StreetNumber","{StreetNumber}","{""type"": ""text"",""value"": ""Street Number""}","2020-12-17 00:00:00","2020-12-17 20:53:24","78","78"
"56","UnitDetail","{UnitDetail}","{""type"": ""text"",""value"": ""Unit Detail""}","2020-12-17 00:00:00","2020-12-17 20:53:24","78","78"
"57","BuildingName","{BuildingName}","{""type"": ""text"",""value"": ""Building Name""}","2020-12-17 00:00:00","2020-12-17 20:53:24","78","78"
"58","CourseStartDate","{CourseStartDate}","{""type"": ""text"",""value"": ""Course Start Date""}","2024-03-29 00:00:00","2020-12-17 20:53:24","1","1"
"59","CourseEndDate","{CourseEndDate}","{""type"": ""text"",""value"": ""Course End Date""}","2024-03-29 00:00:00","2020-12-17 20:53:24","1","1"
"60","CourseDuration","{CourseDuration}","{""type"": ""text"",""value"": ""Course Duration""}","2024-03-29 00:00:00","2020-12-17 20:53:24","1","1"
"61","StudentContactEmail","{StudentContactEmail}","{""type"": ""text"",""value"": ""Student Contact Email""}","2024-03-29 00:00:00","2020-12-17 20:53:24","1","1"
"62","StudentAlternateEmail","{StudentAlternateEmail}","{""type"": ""text"",""value"": ""Student Alternate Email""}","2024-03-29 00:00:00","2020-12-17 20:53:24","1","1"
"63","StudentEmergencyEmail","{StudentEmergencyEmail}","{""type"": ""text"",""value"": ""Student Emergency Email""}","2024-03-29 00:00:00","2020-12-17 20:53:24","1","1"
"64","DeanName","{DeanName}","{""type"": ""text"",""value"": ""Dean Name""}","2025-07-02 00:00:00","2025-07-02 00:00:00","1","1"
"65","DeanSignature","{DeanSignature}","{""type"": ""text"",""value"": ""Dean Signature""}","2025-07-02 00:00:00","2025-07-02 00:00:00","1","1"
"66","CollegeSignature","{CollegeSignature}","{""type"": ""text"",""value"": ""College Signature""}","2025-07-02 00:00:00","2025-07-02 00:00:00","1","1"