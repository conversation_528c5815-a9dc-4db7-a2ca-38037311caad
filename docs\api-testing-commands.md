# API Testing Commands

## Quick API Test

You can test the API directly using these methods:

### 1. Browser Console Test
Open browser console on the Student Risk Assessment page and run:

```javascript
// Test API directly
fetch('/api/v2/tenant/student-risk-assessments?page=1&rowsPerPage=10&filters=%7B%7D')
  .then(response => response.json())
  .then(data => {
    console.log('API Response:', data);
    console.log('Summary Data:', data.summary);
    console.log('Student Data Count:', data.data?.length);
    console.log('Pagination Meta:', data.meta);
  });
```

### 2. Test Card Filtering
```javascript
// Test high-risk filter
fetch('/api/v2/tenant/student-risk-assessments?page=1&rowsPerPage=10&filters=%7B%22riskLevel%22%3A3%7D')
  .then(response => response.json())
  .then(data => {
    console.log('High Risk Filter Results:', data);
    console.log('Filtered Count:', data.data?.length);
    console.log('Should only show HIGH risk students');
  });
```

### 3. Test Store Functionality
```javascript
// Access the store from Vue DevTools or console
const store = window.$nuxt?.$pinia?._s?.get('useStudentRiskAssessmentStore');
if (store) {
  console.log('Store Summary Data:', store.summaryData);
  console.log('Store Filters:', store.filters);
  console.log('Store Data:', store.all);
} else {
  console.log('Store not found - check if component is mounted');
}
```

## Expected API Response

### Successful Response
```json
{
  "data": [
    {
      "id": 1,
      "name": "Student Name",
      "email": "<EMAIL>",
      "course": {
        "code": "CHC50113", 
        "id": 72,
        "name": "Course Name"
      },
      "risk_level": "HIGH",
      "progress": 85,
      "attendance": {
        "present": 92,
        "present_percent": 38.33,
        "absent": 22,
        "total": 240
      },
      "payment_status": "Paid",
      "last_contact": "2 days ago",
      "action": "Urgent Call"
    }
  ],
  "meta": {
    "current_page": 1,
    "last_page": 5,
    "per_page": 25,
    "total": 120,
    "from": 1,
    "to": 25
  },
  "summary": {
    "total": 120,
    "high_risk": 15,
    "medium_risk": 35,
    "low_risk": 70
  },
  "code": 200,
  "success": 1
}
```

### Empty Data Response
```json
{
  "data": [],
  "meta": {
    "current_page": 1,
    "last_page": 1,
    "per_page": 25,
    "total": 0,
    "from": null,
    "to": null
  },
  "summary": {
    "total": 0,
    "high_risk": 0,
    "medium_risk": 0,
    "low_risk": 0
  },
  "code": 200,
  "success": 1
}
```

## Debugging Steps

### 1. Check Database Data
```sql
-- Check if risk assessments exist for your college
SELECT 
    sra.id,
    sra.risk_level,
    s.first_name,
    s.family_name,
    s.college_id
FROM student_risk_assessments sra
JOIN rto_students s ON s.id = sra.student_id
WHERE s.college_id = YOUR_COLLEGE_ID
LIMIT 10;

-- Check risk level distribution
SELECT 
    risk_level,
    COUNT(*) as count
FROM student_risk_assessments sra
JOIN rto_students s ON s.id = sra.student_id  
WHERE s.college_id = YOUR_COLLEGE_ID
GROUP BY risk_level;
```

### 2. Check Network Requests
1. Open browser DevTools → Network tab
2. Navigate to Student Risk Assessment page
3. Look for API calls to `/api/v2/tenant/student-risk-assessments`
4. Check request parameters and response data

### 3. Check Console Errors
1. Open browser DevTools → Console tab
2. Look for any JavaScript errors
3. Common errors to check:
   - Import errors
   - Undefined variables
   - API response format issues

### 4. Vue DevTools
1. Install Vue DevTools browser extension
2. Navigate to Components tab
3. Find StudentRiskAssessmentListComponent
4. Check component data and computed properties
5. Verify store state in Pinia tab

## Troubleshooting

### Cards Show 0 Values
**Possible Causes:**
- No StudentRiskAssessment records in database
- College filtering excluding all data
- API response format mismatch

**Solutions:**
1. Check database for test data
2. Verify college_id in user session
3. Check API response structure

### Filtering Not Working
**Possible Causes:**
- Risk level values don't match constants
- Filter parameter not being sent correctly
- Backend filter logic error

**Solutions:**
1. Verify risk_level values are 1, 2, 3 (not strings)
2. Check network request parameters
3. Test API endpoint directly

### Double API Calls
**Possible Causes:**
- Multiple fetchPaged() calls
- Component mounting issues
- Store watcher conflicts

**Solutions:**
1. Check network tab for duplicate requests
2. Verify only store watcher calls fetchPaged()
3. Check component lifecycle methods

### Pagination Issues
**Possible Causes:**
- Meta object format mismatch
- Page parameter not being sent
- Backend pagination logic error

**Solutions:**
1. Verify Laravel pagination format
2. Check request parameters
3. Test different page numbers
