# Dynamic Cards and Pagination Implementation

## Overview

The Student Risk Assessment module has been enhanced with dynamic card counts and filtering functionality, along with proper pagination support.

## Features Implemented

### 1. Dynamic Card Counts

#### Backend Changes (`StudentRiskAssessmentController.php`)
- **Added `getSummaryStatistics()` method**: Calculates real-time counts for each risk level
- **Enhanced `index()` method**: Returns summary data along with paginated results
- **College-scoped statistics**: All counts are filtered by the current user's college

#### Response Structure
```json
{
  "data": [...], // Paginated student data
  "meta": {
    "current_page": 1,
    "last_page": 5,
    "per_page": 25,
    "total": 120
  },
  "summary": {
    "total": 120,
    "high_risk": 15,
    "medium_risk": 35,
    "low_risk": 70
  }
}
```

### 2. Card-Based Filtering

#### Frontend Changes (`StudentRiskAssessmentListComponent.vue`)
- **Dynamic card values**: Cards now display real counts from the API
- **Click-to-filter**: Clicking a card filters the grid by that risk level
- **Visual feedback**: Active cards show a blue ring and "Active Filter" badge
- **Reset functionality**: Clicking "Total Students" clears all filters

#### Card Behavior
- **Total Students**: Shows all students, clears filters when clicked
- **High Risk Students**: Filters by risk level 3
- **Medium Risk Students**: Filters by risk level 2  
- **Low Risk Students**: Filters by risk level 1

### 3. Enhanced Store (`useStudentRiskAssessmentStore.js`)
- **Custom `fetchPaged()` method**: Handles summary data extraction
- **Summary state management**: Stores and updates card counts
- **Automatic updates**: Summary data refreshes with each API call

### 4. Pagination Support

#### Backend Pagination
- **Page parameter**: `page` (default: 1)
- **Page size**: `rowsPerPage` (default: 25, max: 100)
- **Sorting**: `sortBy` and `descending` parameters
- **Laravel pagination**: Uses standard Laravel paginate() method

#### Frontend Pagination
- **AsyncGrid integration**: Works with existing grid pagination
- **Server-side pagination**: Reduces client-side memory usage
- **Consistent navigation**: Standard pagination controls

## Technical Implementation

### Controller Methods

#### `index()` Method
```php
public function index()
{
    // Handle pagination parameters
    $page = request()->input('page', 1);
    $rowsPerPage = request()->input('rowsPerPage', 25);
    
    // Apply filters
    $filters = json_decode(request()->input('filters', '[]'), true);
    if (!empty($filters['riskLevel'])) {
        $query->where('risk_level', $filters['riskLevel']);
    }
    
    // Get paginated results + summary
    $riskAssessments = $query->paginate($rowsPerPage, ['*'], 'page', $page);
    $summaryStats = $this->getSummaryStatistics($collegeId);
    
    return response()->json([
        'data' => $transformedData,
        'meta' => $paginationMeta,
        'summary' => $summaryStats
    ]);
}
```

#### `getSummaryStatistics()` Method
```php
private function getSummaryStatistics(int $collegeId): array
{
    $allAssessments = StudentRiskAssessment::whereHas('student', function ($query) use ($collegeId) {
        $query->where('college_id', $collegeId);
    })->get();

    return [
        'total' => $allAssessments->count(),
        'high_risk' => $allAssessments->where('risk_level', 3)->count(),
        'medium_risk' => $allAssessments->where('risk_level', 2)->count(),
        'low_risk' => $allAssessments->where('risk_level', 1)->count(),
    ];
}
```

### Vue Component Logic

#### Dynamic Cards
```javascript
const cards = computed(() => [
    {
        enumId: null,
        value: store.summaryData.total,
        title: 'Total Students',
        // styling...
    },
    {
        enumId: 3,
        value: store.summaryData.high_risk,
        title: 'High Risk Students',
        // styling...
    },
    // ... more cards
]);
```

#### Card Click Handler
```javascript
const handleCardClick = (card) => {
    store.filters = {}; // Clear existing filters
    
    if (card?.enumId !== null) {
        store.filters.riskLevel = card.enumId;
    }
    
    store.fetchPaged(); // Refresh data
};
```

#### Active Card Detection
```javascript
const isCardActive = (card) => {
    if (card.enumId === null) {
        return !store.filters.riskLevel; // Total card active when no filter
    }
    return store.filters.riskLevel === card.enumId;
};
```

## Benefits

### Performance
- **Server-side filtering**: Reduces data transfer
- **Efficient pagination**: Only loads current page data
- **Optimized queries**: Uses proper indexes and relationships

### User Experience
- **Real-time counts**: Cards show current data
- **Visual feedback**: Clear indication of active filters
- **Intuitive navigation**: Click cards to filter, easy reset

### Maintainability
- **Separation of concerns**: Backend handles data, frontend handles UI
- **Reusable patterns**: Can be applied to other modules
- **Clean architecture**: Follows existing store patterns

## Testing

### Manual Testing Steps
1. **Load the page**: Verify cards show correct counts
2. **Click High Risk card**: Grid should filter to high-risk students only
3. **Click Total Students**: Filter should clear, showing all students
4. **Test pagination**: Navigate through pages, verify counts remain accurate
5. **Test sorting**: Sort by different columns, verify functionality

### Expected Behavior
- Cards update in real-time with data changes
- Filtering works correctly for each risk level
- Pagination maintains filter state
- Visual feedback shows active filters clearly
- Performance remains smooth with large datasets

## Future Enhancements

- **Additional filters**: Course, semester, payment status cards
- **Drill-down capability**: Click card to see detailed breakdown
- **Export functionality**: Export filtered data
- **Real-time updates**: WebSocket integration for live counts
- **Advanced analytics**: Trend charts and historical data
