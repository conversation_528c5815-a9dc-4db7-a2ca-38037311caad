{"private": true, "scripts": {"dev": "vite", "build": "vite build", "prod": "NODE_OPTIONS='--max-old-space-size=4096' vite build", "prepare": "husky"}, "lint-staged": {"*.{js,vue,ts,json,css,scss,md}": "prettier --write", "*.php": "vendor/bin/pint"}, "devDependencies": {"@vitejs/plugin-vue": "^5.0.4", "@vue/compiler-sfc": "^3.3.4", "autoprefixer": "^10.4.14", "axios": "^0.21", "browser-sync": "^2.29.3", "browser-sync-webpack-plugin": "^2.3.0", "husky": "^9.1.7", "laravel-mix": "^6.0.6", "laravel-vite-plugin": "^1.0.3", "lint-staged": "^15.5.1", "lodash": "^4.17.19", "postcss": "^8.4.24", "prettier": "^3.5.3", "prettier-plugin-blade": "^2.1.21", "prettier-plugin-tailwindcss": "^0.5.9", "prettier-plugin-vue": "^1.1.6", "resolve-url-loader": "^5.0.0", "rollup-plugin-concat": "^1.0.3", "sass": "^1.63.6", "sass-loader": "^12.6.0", "tailwindcss": "^3.3.2", "terser": "^5.37.0", "vite": "^5.2.11", "vue-loader": "^16.1.0"}, "dependencies": {"@googlemaps/js-api-loader": "^1.16.8", "@headlessui/vue": "^1.7.16", "@iconify-prerendered/vue-fluent": "^0.23.1717393783", "@inertiajs/vue3": "^1.0.11", "@popperjs/core": "^2.11.8", "@progress/kendo-buttons-vue-wrapper": "^2024.2.514", "@progress/kendo-data-query": "^1.7.0", "@progress/kendo-date-math": "^1.5.13", "@progress/kendo-drawing": "^1.19.1", "@progress/kendo-gantt-vue-wrapper": "^2024.2.514", "@progress/kendo-layout-vue-wrapper": "^2024.2.514", "@progress/kendo-licensing": "^1.3.5", "@progress/kendo-svg-icons": "^2.0.0", "@progress/kendo-theme-default": "^7.0.1", "@progress/kendo-vue-animation": "^4.0.0", "@progress/kendo-vue-buttons": "^4.3.2", "@progress/kendo-vue-charts": "^4.3.1", "@progress/kendo-vue-common": "^4.0.0", "@progress/kendo-vue-data-tools": "^4.3.3", "@progress/kendo-vue-dateinputs": "^4.0.0", "@progress/kendo-vue-dialogs": "^4.0.0", "@progress/kendo-vue-dropdowns": "^4.0.0", "@progress/kendo-vue-editor": "^4.3.3", "@progress/kendo-vue-form": "^4.0.0", "@progress/kendo-vue-grid": "^4.0.0", "@progress/kendo-vue-indicators": "^4.0.0", "@progress/kendo-vue-inputs": "^4.0.0", "@progress/kendo-vue-intl": "^4.3.1", "@progress/kendo-vue-layout": "^4.3.2", "@progress/kendo-vue-listbox": "^4.1.0", "@progress/kendo-vue-listview": "^4.0.0", "@progress/kendo-vue-notification": "^4.0.0", "@progress/kendo-vue-pdf": "^4.1.0", "@progress/kendo-vue-progressbars": "^4.1.0", "@progress/kendo-vue-scheduler": "^4.3.3", "@progress/kendo-vue-tooltip": "^4.3.3", "@progress/kendo-vue-treeview": "^4.1.0", "@progress/kendo-vue-upload": "^4.3.2", "@vue/compat": "^3.3.4", "@vuelidate/core": "^2.0.3", "@vuelidate/validators": "^2.0.4", "@yaireo/tagify": "^4.18.0", "chart.js": "^3.4.1", "dayjs": "^1.11.10", "fast-glob": "^3.3.2", "filepond": "^4.30.4", "filepond-plugin-file-validate-type": "^1.2.8", "floating-vue": "^5.2.2", "glob": "^10.3.15", "hammerjs": "^2.0.8", "javascript-time-ago": "^2.3.6", "konva": "^9.3.20", "mitt": "^3.0.1", "pinia": "^2.1.7", "qs": "^6.14.0", "simple-code-editor": "^2.0.9", "tailwind-merge": "^2.2.2", "tailwindcss-animate": "^1.0.7", "tiny-emitter": "^2.1.0", "v-calendar": "^3.0.3", "vite-plugin-commonjs": "^0.10.1", "vite-plugin-require": "^1.2.14", "vue": "^3.3.4", "vue-chartjs": "^3.5.1", "vue-draggable": "^2.0.6", "vue-filepond": "^7.0.3", "vue-konva": "^3.2.0", "vue3-editor": "^0.1.1", "vue3-pdf-app": "^1.0.3", "vue3-smooth-dnd": "^0.0.5", "vuedraggable": "^4.1.0", "vuelidate": "^0.7.6", "vuex": "^4.0.2"}, "type": "module"}