var Agent = function() {

    $.validator.addMethod("NotAllowAlpha", function(value, element) {
        return this.optional(element) || /^([^a-zA-Z_])+$/i.test(value);
    }, "Username must contain only letters, numbers.");

    $("#loader").kendoLoader();
    kendo.ui.progress.messages = {
        loading:
            '<div class="vue-simple-spinner animate-spin" style="position:absolute;top:50%;left:50%;transfrom:translate(-50%,-50%); margin: 0px auto; border-radius: 100%; border-color: rgb(50, 157, 243) rgb(238, 238, 238) rgb(238, 238, 238); border-style: solid; border-width: 3px; border-image: none 100% / 1 / 0 stretch; width: 30px; height: 30px;"></div>',
    };

    var agentAdd = function() {

        radioCheckboxClass();
        dateFormate('.dateField');
        setAgencyStatusAndDate();
        checkDateRange('.dateField', '#start_date', '#end_date');

        //        $("#addAgentForm22").validate({
        //            rules: {
        //                agency_name: {required: true},
        //                contact_person: {required: true},
        //                primary_email: {required: true, email: true},
        //                telephone: {required: true},
        //                office_address: {required: true},
        //                office_postcode: {required: true},
        //                office_ABN: {required: true},
        //                postal_city: {required: true},
        //                target_primary_country: {required: true},
        //                status: {required: true},
        //                bank_country: {required: true},
        //                bank_name: {required: true},
        //                bank_branch: {required: true},
        //                bank_account_name: {required: true},
        //                bank_swift_code: {required: true},
        //                bank_account_number: {required: true},
        //                bank_BSB: {required: true},
        //                total_employess: {required: false, number: true},
        //                mobile2: {required: false, number: true},
        //                mobile1: {required: false, number: true},
        //                fax: {required: false, number: true},
        //            },
        //            messages: {},
        //            errorPlacement: function (error, element) {}
        //        });
        var rules = {
            agency_name: { required: true },
            contact_person: { required: true },
            primary_email: { required: true, email: true },
            telephone: { required: true, NotAllowAlpha: true },
            office_address: { required: true },
            office_postcode: { required: true },
            //office_ABN: {required: true},
            postal_city: { required: true },
            target_primary_country: { required: true },
            status: { required: true },
            // bank_country: {required: true},
            // bank_name: {required: true},
            // bank_branch: {required: true},
            // bank_account_name: {required: true},
            // bank_swift_code: {required: true},
            // bank_account_number: {required: true},
            // bank_BSB: {required: true},
            total_employess: { required: false, number: true },
            mobile2: { required: false, number: true },
            mobile1: { required: false, number: true },
            fax: { required: false, number: true }
        };
        handleFormValidateNew($("#addAgentForm"), rules);

        $('#telephone').keydown(function(e) {
            if (e.shiftKey || e.ctrlKey || e.altKey) {
                e.preventDefault();
            } else {
                var key = e.keyCode;
                if (!((key == 8) || (key == 46) || (key == 9) || (key >= 35 && key <= 40) || (key >= 48 && key <= 57) || (key >= 96 && key <= 105))) {
                    e.preventDefault();
                }
            }
        });

        function shaw_div_postal() {
            $("#postalId").css("display", "block");
        }

        function hide_div_postal() {
            $("#postalId").css("display", "none");
        }

        $(".statusChange").on("ifChecked", shaw_div_postal);
        $(".statusChange").on("ifUnchecked", hide_div_postal);

        $('body').on('click', '.deleteImage', function() {
            var imageId = this.id;
            var agentId = $(this).attr('data-value');

            $.ajax({
                type: "POST",
                headers: { 'X-CSRF-TOKEN': $('input[name="_token"]').val() },
                url: site_url + "agent/ajaxAction",
                data: { 'action': 'deleteAgent', 'data': { 'id': imageId, 'agentId': agentId } },
                success: function(data) {
                    var data = JSON.parse(data);
                    $('#editImageList').replaceWith('<div class="form-group" id="editImageList"></div>');
                    for (var i = 0; i < data.length; i++) {
                        $('#editImageList').append('<div>' + data[i]['images'] + '  ' + '<a  class="deleteImage"  id= "' + data[i]['id'] + '"value= "' + data[i]['agent_id'] + '"><i class="fa fa-remove margin-r-5"></i> </a> ' + '</div>');
                    }
                }
            });
        });

        $('body').on('change', '#agency_status', function() {
            setTimeout(function() {
                setAgencyStatusAndDate();
            }, 100);
        });

        $('body').on('focusout', '#start_date', function() {
            setTimeout(function() {
                setAgencyStatusAndDate();
            }, 100);
        });
    };

    function setAgencyStatusAndDate() {
        var statusId = $('#agency_status').val();
        var startDate = $('#start_date').val();
        var dataArr = { 'statusId': statusId, 'startDate': startDate };
        var url = "agent/ajaxAction";
        var action = 'getExpiryDate';

        ajaxAction(url, action, dataArr, function(resultData) {
            $('#end_date').val(resultData);
        });
    }

    var agentView = function() {
        //        agentListArr();
        deleteSingleData(site_url + 'delete-agent/');
        //radioCheckboxClass();
        //        radioCheckboxClass();
        $('body').on('change', '#searchField', function() {
            var fieldName = $(this).val();
            if (fieldName == 'status') {
                $('.statusValue').show();
                $('.searchValue').hide();
            } else {
                $('.statusValue').hide();
                $('.searchValue').show();
            }
        });

        $('body').on('click', '.filterSubmit', function() {
            $("#filterForm").validate({
                rules: { statusValue: { required: true }, searchValue: { required: true } },
                messages: {},
                errorPlacement: function(error, element) {},
                submitHandler: function(form) {
                    agentListArr();
                    //                      $('#agentListArr').DataTable().ajax.reload();
                }
            });
        });

        function agentListArr() {
            $('#agentListArr').DataTable().ajax.reload();
        }

        $('body').on('click', '.createUser', function() {
            var id = $(this).attr('data-id');
            var agencyName = $(this).attr('data-agent-name');
            $(".agentId").val(id);

            $.ajax({
                type: "POST",
                headers: { 'X-CSRF-TOKEN': $('input[name="_token"]').val() },
                url: site_url + "agent/ajaxAction",
                data: { 'action': 'checkUserCreate', 'data': { 'agentId': id } },
                success: function(data) {
                    var obj = jQuery.parseJSON(data);
                    $('#agent_email1').html('');
                    if (obj[0]['user_id'] == null) {

                        $('#agent_email1').html(obj[0]['primary_email']);
                        $('#agent_email').val(obj[0]['primary_email']);
                        //     // $("#securityAnswer").val(agencyName);
                    } else {
                        $('#agent_email1').html(obj[1]['email']);
                        $('#agent_email').val(obj[1]['email']);
                        // $("#securityAnswer").val(obj[1]['security_answer']);
                        // $("#securityQuestion").val(obj[1]['security_question']);
                    }
                    $('#createUserModel').modal('show');
                }
            });
        });

        $('body').on('click', '.addStatus', function() {
            var id = $(this).attr('data-id');
            var agencyName = $(this).attr('data-agent-name');
            $("#agencyAgentId").val(id);
            $("#securityAnswer").val(agencyName);
            $('#userName').val('agent' + id);
            $('#addAgentStatus').modal('show');
            agentStatusArr(id);
        });

        function agentStatusArr(id) {
            var dataArr = { 'agentId': id };
            var url = "agent-email/ajaxAction";
            var action = 'agentStatusGetData';
            $('#agentStatusList tbody').html("");

            ajaxAction(url, action, dataArr, function(resultData) {
                var result = jQuery.parseJSON(resultData);
                var table = "";
                $.each(result, function(i, item) {
                    table += '<tr>' +
                        '<td>' + item.status_name + '<div class="action-overlay">' +
                        '<ul class="icon-actions-set">' +
                        '<li><span data-toggle="modal" class="deleteAgency" data-id="' + item.id + '" data-target="#"><a class="link-black text-sm deleteAgency" data-toggle="tooltip" data-original-title="Delete" style="font-size: 13px;" href="javascript:;"><i class="fa fa-remove margin-r-5"></i></a></span></li>' +
                        '</ul>' +
                        '</div>' + '</td>' +
                        '<td>' + item.start_date + '</td>' +
                        '<td>' + item.end_date + '</td>' +
                        //                            '<td class="action-overlay"><ul class="icon-actions-set"><li>' + '<span data-toggle="modal" class="deleteAgency" data-id="' + item.id + '" data-target="#">' + '<a  class="link-black text-sm deleteAgency" data-toggle="tooltip" data-original-title="Delete" style="font-size: 13px;" href="javascript:;"><i class="fa fa-remove margin-r-5"></i> </a></span>' + '</li></ul></td>' +
                        '</tr>';
                });
                $('#agentStatusList tbody').html(table);
                noRecordFoundNew("#agentStatusList tbody", result.length, 3);
                //                if (result.length > 0) {
                //                    $('#noRecordsAgency').hide();
                //                } else {
                //                    $('#noRecordsAgency').show();
                //                }
            });
        }

        $('body').on('click', '.deleteAgency', function() {
            $('#addAgentStatus').modal('hide');
            $('#deleteModalAgency').modal('show');
            var dataid = $(this).attr('data-id');
            $('.deleteStatus').attr('data-id', dataid);
        });

        $('body').on('click', '.deleteStatus', function() {
            var agentId = $(this).attr('data-id');
            $.ajax({
                type: "POST",
                headers: { 'X-CSRF-TOKEN': $('input[name="_token"]').val() },
                url: site_url + "agent/ajaxAction",
                data: { 'action': 'deleteStatus', 'data': { 'agentId': agentId } },
                success: function(data) {
                    $('#addAgentStatus').modal('show');
                    $('#deleteModalAgency').modal('hide');
                    setTimeout(function() {
                        agentStatusArr(agentId);
                    }, 100);
                }
            });
        });

        $('body').on('click', '.editAgentCode', function() {
            var id = $(this).attr('data-id');
            $('.agentCode').val('');
            $('.agentnames').text('');
            $('.editId').val('');
            $.ajax({
                type: 'POST',
                headers: { 'X-CSRF-TOKEN': $('input[name="_token"]').val() },
                url: site_url + "agent/ajaxAction",
                data: { 'action': 'getAgentCode', 'data': { 'id': id } },
                success: function(data) {
                    var obj = jQuery.parseJSON(data);
                    $('.agentCode').val(obj[0].agent_code);
                    $('.agentnames').text(obj[0].agency_name);
                    $('.editId').val(obj[0].id);
                },
                error: function(err) {}
            });
        });

        $('body').on('click', '.change_status', function() {
            var id = $(this).attr('data-id');
            var status = $(this).attr('data-status');

            $.ajax({
                type: 'POST',
                headers: { 'X-CSRF-TOKEN': $('input[name="_token"]').val() },
                url: site_url + "agent/ajaxAction",
                data: { 'action': 'agentStatusChange', 'data': { 'id': id, 'status': status } },
                success: function(data) {
                    var obj = jQuery.parseJSON(data);
                    //                    agentListArr();
                    setTimeout(function() {
                        sessionDisplayMessage(obj.type, obj.message);
                        $('#agentListArr').DataTable().ajax.reload();
                        //radioCheckboxClass();
                    }, 100);
                },
                error: function(err) {}
            });
        });

        $('body').on('click', '.exportStudentList', function() {
            let agentId = $(this).attr('data-id');
            if(agentId > 0){
                let dataArr = { 'agent_id': agentId };
                $.ajax({
                    type: "POST",
                    url: site_url + "api/export-agent-students",
                    dataType: "json",
                    data: dataArr,
                    success: function(response) {
                        if (response.data.export_url) {
                            //let decodedUrl = decodeURIComponent(response.data.export_url);
                            let decodedUrl = atob(response.data.export_url);

                            const link = document.createElement("a");
                            link.setAttribute("href", decodedUrl);
                            link.setAttribute("download", response.data.file_name);
                            link.click();
                        } else {
                            sessionDisplayMessage('alert-danger', response.message);
                        }
                    },
                    error: function(err) {
                        sessionDisplayMessage('alert-danger', err.message);
                    }
                });
            }
        });

        $('body').on('change', '.check_or_not', function(event) {
            let tableDiv = $('#agentListArr');
            let allCheckboxes = tableDiv.find(':checkbox');
            let parentCheckbox = tableDiv.find('.check_all');

            if ($(this).hasClass('check_all')) {
                allCheckboxes.prop('checked', this.checked);
            } else {
                let allChecked = allCheckboxes.not('.check_all').length === allCheckboxes.not('.check_all').filter(':checked').length;
                parentCheckbox.prop('checked', allChecked);
            }
        });

        $('body').on('click', '#agentExport', function(e) {
            e.preventDefault();
            let agentIds = [];
            $('#agentListArr tbody input.check_all:checked').each(function() {
                agentIds.push($(this).val());
            });

            if (agentIds.length == 0) {
                $('#confirmAgentExportModal').modal('show');
                return;
            }else{
                agentDataExport(agentIds);
            }
        });

        $('body').on('click', '.allExportConfirm', function() {
            agentDataExport();
            $('#confirmAgentExportModal').modal('hide');
        });

        function agentDataExport(agentIds=''){
            let dataArr = {
                'action': 'agent-export',
                'data': {'agentIds': agentIds}
            };

            startAjaxLoader();
            $.ajax({
                url: site_url + "agent/ajaxAction",
                type: "POST",
                data: dataArr,
                xhrFields: { responseType: "blob" }, // Set response type to blob for file download
                success: function (data) {
                    stopAjaxLoader();
                    triggerURLForDownload(data, "Agents.xlsx"); // Create a URL for the file and trigger a download
                },
                error: function (xhr, status, error) {
                    stopAjaxLoader();
                    //console.error("Export failed:", error);
                },
            });
        }

        /* Bulk sync from zoho */
        $('body').on('click', '#bulkAgentSyncFromZoho', function(e) {
            e.preventDefault();
            $('#bulkAgentSyncFromZohoModal').modal('show');
        });

        $('body').on('click', '#bulkSyncConfirmFromZoho', function(e) {
            e.preventDefault();
            bulkSyncFromZoho('#bulkAgentSyncFromZohoModal');
        });

        /* Bulk sync to zoho */
        $('body').on('click', '#bulkAgentSyncToZoho', function(e) {
            e.preventDefault();
            getSelectedComm('#bulkAgentSyncToZohoModal');
        });

        $('body').on('click', '#bulkSyncConfirmToZoho', function(e) {
            e.preventDefault();
            bulkSyncToZoho('#bulkAgentSyncToZohoModal');
        });


        /* Single sync from zoho */
        $('body').on('click', '.syncSingleAgentFromZoho', function(e) {
            e.preventDefault();
            let agentId = $(this).attr('data-agent-id');
            $('#syncAgentFromZohoModal').find('.agentId').val(agentId);
            $('#syncAgentFromZohoModal').modal('show');
        });

        $('body').on('click', '.confirmAgentSyncFrom', function(e) {
            e.preventDefault();
            let agentId = $('#syncAgentFromZohoModal').find('.agentId').val();
            if (!agentId) {
                showToster('error', 'Agent ID is required');
                return;
            }
            syncSingleAgentFromZoho(agentId);
        });

        /* Single sync to zoho */
        $('body').on('click', '.syncSingleAgentToZoho', function(e) {
            e.preventDefault();
            let agentId = $(this).attr('data-agent-id');
            $('#syncAgentToZohoModal').find('.agentId').val(agentId);
            $('#syncAgentToZohoModal').modal('show');
        });

        $('body').on('click', '.confirmAgentSyncTo', function(e) {
            e.preventDefault();
            let agentId = $('#syncAgentToZohoModal').find('.agentId').val();
            if (!agentId) {
                showToster('error', 'Agent ID is required');
                return;
            }
            syncSingleAgentToZoho(agentId);
        });

        $("#editAgentCodeForm").validate({
            rules: { agent_code: { required: true } },
            errorPlacement: function(error, element) {}
        });

        dateFormate('.dateField');
        setTimeout(function() {
            setAgencyStatusAndDate();
        }, 100);

        checkDateRange('.dateField', '#start_date', '#end_date');

        $("#AddAgentStatusForm").validate({
            rules: { status: { required: true }, end_date: { required: true }, start_date: { required: true } },
            errorPlacement: function(error, element) {}
        });

        // get data using ajax datatable
        var dataArr = {};
        var columnWidth = { "width": "15%", "targets": 2 };
        var arrList = {
            'tableID': '#agentListArr',
            'ajaxURL': site_url + "agent/ajaxAction",
            'ajaxAction': 'getAgentList',
            'postData': dataArr,
            'hideColumnList': [],
            'noSearchApply': [0],
            'noSortingApply': [0],
            'defaultSortColumn': 1,
            'defaultSortOrder': 'desc',
            'setColumnWidth': columnWidth
        };
        getDataTable(arrList);
    };

    var agentStaus = function() {
        deleteSingleData(site_url + 'delete-agent-status/');
        radioCheckboxClass();
        //        $('input').iCheck({
        //            checkboxClass: 'icheckbox_flat-green',
        //            radioClass: 'iradio_flat-green'
        //        });

        //        $('.delete').click(function() {
        //            var dataid = $(this).attr('data-id');
        //            $('.yes-sure').attr('data-id', dataid);
        //        });
        //
        //        $('.yes-sure').click(function() {
        //            var dataid = $(this).attr('data-id');
        //            window.location = (site_url + 'delete-agent-status/' + dataid);
        //        });

    };

    var emailListView = function() {
        radioCheckboxClass();
        $('#template').on('change', function() {
            var EmailContentId = $(this).val();
            var that = $(this);
            loadingStart(that);
            $.ajax({
                url: site_url + "offerManage/ajaxAction",
                method: "POST",
                headers: { 'X-CSRF-TOKEN': $('input[name="_token"]').val() },
                data: { 'action': 'getEmailContent', 'data': { 'EmailContentId': EmailContentId } },
                success: function(data) {
                    loadingEnd(that);
                    var obj = jQuery.parseJSON(data);
                    $("#documentDiv").empty();
                    $.each(obj, function(i, item) {
                        $('#subject').val(item.email_subject);
                        CKEDITOR.instances['email_body'].setData(item.content);
                        var document = item.document;

                        $.each(document, function(j, itemDoc) {
                            var docData = '<label class="display-ib" style="width:100%; color:green;">' + itemDoc.file.substring(10) + '</label>' +
                                '<input name="email_attachment[' + j + ']" value="' + itemDoc.file + '" type="hidden">';

                            $("#documentDiv").append(docData);
                        });
                    });
                }
            });
        });

        $('.searchFunction').on('change', function() {
            var arrAccountManager = $('#arrAccountManager').val();
            var countryRecord = $('#countryRecord').val();
            var agentStatus = $('#agentStatus').val();

            var dataArr = { 'arrAccountManager': arrAccountManager, 'countryRecord': countryRecord, 'agentStatus': agentStatus };
            var url = "agent-email/ajaxAction";
            var action = 'getFilterAgentEmail';
            $('#agentList tbody').html("");

            ajaxAction(url, action, dataArr, function(resultData) {
                var result = jQuery.parseJSON(resultData);
                var table = "";
                $.each(result, function(i, item) {
                    table += '<tr>' +
                        '<td>' + item.agency_name + '</td>' +
                        '<td>' + item.contact_person + '</td>' +
                        '<td>' + item.telephone + '</td>' +
                        '<td>' + '<input type="checkbox" class="agent_select" name="agent_select[]" value="' + item.id + '">&nbsp;' + item.primary_email + '</td>' +
                        '<td>' + item.alertnet_email + '</td>' +
                        '<td>' + item.status_type + '</td>' +
                        '</tr>';
                });

                $('#agentList tbody').html(table);
                radioCheckboxClass();
                if (result.length > 0) {
                    $('#noRecords').hide();
                } else {
                    $('#noRecords').show();
                }
            });
        });

        $("#email-list-form").validate({
            'ignore': [],
            rules: {
                email_from: { required: true },
                subject: { required: true },
                email_body: {
                    required: function(textarea) {
                        CKEDITOR.instances[textarea.id].updateElement(); // update textarea
                        var editorcontent = textarea.value.replace(/<[^>]*>/gi, ''); // strip tags
                        var chk = editorcontent.length === 0;
                        $('#cke_email_body').css('border', (chk) ? '1px solid red' : '1px solid #b6b6b6');
                        return chk;
                    }
                },
            },
            errorPlacement: function(error, element) {}
        });

        $(function() {
            CKEDITOR.replace('email_body').config.height = "400px";
        });
    };

    var agentStausAdd = function() {

        radioCheckboxClass();
        restrictNumeric('.numberValid');
        $("#agentStatusForm").validate({
            rules: { status_type: { required: true }, select_duration: { required: true, maxlength: 3 } },
            errorPlacement: function(error, element) {}
        });
    };

    function setDefaultFilter() {
        var filter_name = $('.statuschange :selected').text();
        $('.agentListTitle').text(filter_name);
    }

    var agentListArr = function() {
        var filter_name = $('.statuschange :selected').text();
        var filter_by = $('select[name=searchField]').val();
        var search_str = $('input[name=searchValue]').val();
        var agent_status = $('select[name=agent_status]').val();

        var dataArr = { filter_by: filter_by, search_str: search_str, agent_status: agent_status };
        $.ajax({
            type: "POST",
            url: site_url + "agent/ajaxAction",
            headers: { 'X-CSRF-TOKEN': $('input[name="_token"]').val() },
            data: { 'action': 'searchAgentpaymentData', 'data': dataArr },
            success: function(data) {
                var obj = jQuery.parseJSON(data);
                $('.agentListTitle').text(filter_name);
                $(".paginationView").css('display', 'none');
                $("#agentPaymentList tbody").html(obj.filterData);

                adjust_td_width();
            },
            error: function(err) {}
        });
    };

    var searchAgentPaymentData = function() {

        setDefaultFilter();
        $(".clearData").on("click", function() {
            $('.searchField').val('');
            $(".searchField :selected").remove();
            $("#agent_status").val("7");
            setTimeout(function() {
                agentListArr();
            }, 200);
        });

        function setTitle() {
            var titleText = $('select[name=searchField] option:selected').text();
            var AgentpaymentValue = $('select[name=searchField] option:selected').val();
            if (typeof AgentpaymentValue != 'undefined' && AgentpaymentValue != '' && AgentpaymentValue != null) {
                $('.agentListTitle').text(titleText);
            } else {
                $('.agentListTitle').empty();
            }
        }

        $(".statuschange").on("change", function() {
            $('#searchValue').val('');
            setTitle();
            var filter_by = $('select[name=searchField]').val();

            if (filter_by === 'allAgent') {
                $('.agentStatusArr').css('display', 'none');
                $('.searchString').css('display', 'none');
            } else if (filter_by === 'agent_status') {
                $('.agentStatusArr').css('display', 'block');
                $('.searchString').css('display', 'none');
            } else if (filter_by === '') {
                $('.agentStatusArr').css('display', 'none');
                $('.searchString').css('display', 'none');
            } else {
                $('.agentStatusArr').css('display', 'none');
                $('.searchString').css('display', 'block');
            }
        });

        $("#agentPaymentForm").validate({
            rules: { searchValue: { required: true }, agent_status: { required: true }, searchField: { required: true } },
            messages: {},
            errorPlacement: function(error, element) {},
            submitHandler: function(form) {
                agentListArr();
            }
        });

        $(".searchData").on("click", function() {
            var filter_by = $('select[name=searchField]').val();
            var search_str = $('input[name=searchValue]').val();
            var agent_status = $('select[name=agent_status]').val();
            var list_title = $('.statuschange :selected').text();

            $("#searchValue").css('border', '1px solid #ccc');
            $("#agentPaymentForm").submit();

            $("#agentListTitle").html(" by " + list_title);
            if (($('input[name=searchValue]').val() != '' && $('input[name=searchValue]').val() != null) || $('input[name=searchValue]').val() > 0) {
                $("#agentPaymentForm").submit();
            } else {
                $("#searchValue").css('border', '1px solid #dd4b39');
            }
        });

        $('body').on('keypress', '.searchField', function(e) {
            if (e.which == 13) {
                $('.searchData').trigger('click');
            }
        });

        var dataArr = {};
        var columnWidth = { "width": "15%", "targets": 5 };
        var arrList = {
            'tableID': '#agentPaymentList',
            'ajaxURL': site_url + "agent/ajaxAction",
            'ajaxAction': 'getAgentPaymentData',
            'postData': dataArr,
            'hideColumnList': [5, 6],
            'noSearchApply': [],
            'noSortingApply': [],
            'defaultSortColumn': 1,
            'defaultSortOrder': 'desc',
            'setColumnWidth': columnWidth
        };
        getDataTable(arrList);

    };

    function startBlur(){
        $('body').css('filter', 'blur(3px)');
    }

    function stopBlur(){
        $('body').css('filter', 'blur(0px)');
    }

    function startAjaxLoader() {
        $(document)
            .on("ajaxStart", function () {
                kendo.ui.progress($(document.body), true);
            })
            .on("ajaxStop", function () {
                kendo.ui.progress($(document.body), false);
            });
    }

    function stopAjaxLoader() {
        $(document)
            .on("ajaxStart", function () {
                kendo.ui.progress($(document.body), false);
            })
            .on("ajaxStop", function () {
                kendo.ui.progress($(document.body), false);
            });
    }

    function triggerURLForDownload(data, fileName) {
        const url = window.URL.createObjectURL(new Blob([data]));
        const a = document.createElement("a");
        a.href = url;
        a.download = fileName;
        document.body.append(a);
        a.click();
        a.remove();
        window.URL.revokeObjectURL(url);
    }

    function getSelectedComm(modalId){
        let idArr = $(".check_all:checked").map(function() {
            return $(this).val();
        }).get();

        if(idArr.length == 0){
            showToster('error', 'Please select at least one record', '');
            return false;
        }

        $(modalId).modal('show');
        $(modalId).find('.agentIds').val(idArr);
    }

    function bulkSyncFromZoho(modalId){
        startBlur();
        ajaxAction("agent/ajaxAction", "syncFromZoho", {}, function(response) {
            stopBlur();
            let res = jQuery.parseJSON(response);
            showToster(res.status, res.message);
            $(modalId).modal('hide');
            reloadAgentsList(res.status, 4000);
        });
    }

    function bulkSyncToZoho(modalId){
        let selectedIdStr = $(modalId).find('.agentIds').val();
        let selectedIds = selectedIdStr.split(',');
        if(selectedIds.length > 0){
            startBlur();
            ajaxAction("agent/ajaxAction", "syncToZoho", { 'agentIds': selectedIds }, function(response) {
                stopBlur();
                let res = $.parseJSON(response);
                if(res.status){
                    showToster(res.status, res.message)
                }
                // Show detailed popup for sync results
                showSyncDetails(modalId, res);
            });
        }else{
            showToster('error', 'Select at least one agent.');
        }
    }

    function showSyncDetails(modalId, res){
        if (res.show_popup && res.agent_details) {
            showZohoAgentSyncResults(res.agent_details);
        }

        if (res.success_count > 0) {
            showToster("success", res.success_msg);
            if (res.error_count > 0) {
                setTimeout(() => showToster("error", res.error_msg), 5000);
            }
            agentListArr();
            $('#agentListArr').find('.check_all, .check_or_not').prop('checked', false);
        } else if (res.error_count > 0) {
            showToster("error", res.error_msg)
        }
        $(modalId).modal('hide');
    }

    function showZohoAgentSyncResults(agentDetails) {
        let tableBody = $('#agentSyncResultsTableBody');
        tableBody.empty();

        agentDetails.forEach(function(agent) {
            let statusClass = '';
            let statusIcon = '';

            if (agent.status === 'Success') {
                statusClass = 'text-success';
                statusIcon = '<i class="fa fa-check-circle" style="margin-right: 5px;"></i>';
            } else if (agent.status === 'Failed') {
                statusClass = 'text-danger';
                statusIcon = '<i class="fa fa-times-circle" style="margin-right: 5px;"></i>';
            } else if (agent.status === 'Already Synced') {
                statusClass = 'text-warning';
                statusIcon = '<i class="fa fa-exclamation-circle" style="margin-right: 5px;"></i>';
            }

            let row = `
                    <tr>
                        <td>${agent.id}</td>
                        <td>${agent.name}</td>
                        <td class="${statusClass}">${statusIcon}${agent.status}</td>
                        <td>${agent.sync_datetime}</td>
                        <td class="text-danger">${agent.fail_reason || '-'}</td>
                    </tr>
                `;
            tableBody.append(row);
        });

        $('#zohoAgentSyncResultsModal').modal('show');
    }

    function syncSingleAgentFromZoho(agentId) {
        if (!agentId) {
            showToster('error', 'Agent ID is required');
            return;
        }

        startBlur();
        ajaxAction("agent/ajaxAction", "syncSingleAgentFromZoho", { 'agentId': agentId }, function(response) {
            stopBlur();
            let res = jQuery.parseJSON(response);
            showToster(res.status, res.message);
            $('#syncAgentFromZohoModal').modal('hide');
            reloadAgentsList(res.status);
        });
    }

    function syncSingleAgentToZoho(agentId) {
        if (!agentId) {
            showToster('error', 'Agent ID is required');
            return;
        }

        startBlur();
        ajaxAction("agent/ajaxAction", "syncSingleAgentToZoho", { 'agentId': agentId }, function(response) {
            stopBlur();
            let res = jQuery.parseJSON(response);
            showToster(res.status, res.message);
            $('#syncAgentToZohoModal').modal('hide');
            //reloadAgentsList(res.status);
        });
    }

    function reloadAgentsList(status='', timeOutVal=2000){
        if (status === 'success') {
            setTimeout(function() {
                $('#agentListArr').DataTable().ajax.reload();
            }, timeOutVal);
        }
    }

    return {
        initAddAgent: function() {
            radioCheckboxClass();
            agentAdd();
        },
        initViewAgent: function() {
            agentView();
        },
        initSearchAgent: function() {
            searchAgentPaymentData();
        },
        initAgentStatus: function() {
            agentStaus();
        },
        initAgentStatusAdd: function() {
            agentStausAdd();
        },
        initEmailListView: function() {
            emailListView();
        }
    };
}();