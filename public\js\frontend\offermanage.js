var dateWarningModalId = "#dateWarningModal";

function initializeClearableDatepicker(selector) {
    $(selector).datepicker({
        format: 'dd-mm-yyyy',
        onClose: function(dateText) {
            if (!dateText) {
                $(this).val('');
            }
        }
    });

    $(selector).on('keydown', function(e) {
        if (e.key === 'Backspace' || e.key === 'Delete') {
            $(this).val('');
            $(this).datepicker('setDate', null);
        }
    });
}

// Initialize both date fields with reusable function
initializeClearableDatepicker('#input_intake_date');
initializeClearableDatepicker('#applied_date_from');
initializeClearableDatepicker('#applied_date_to');

$('#applied_date_from').on('change', function() {
    var startDate = $(this).datepicker('getDate');
    if (startDate) {
        $('#applied_date_to').datepicker('setStartDate', startDate);
        var endDate = $('#applied_date_to').datepicker('getDate');
        if (endDate && endDate < startDate) {
            $('#applied_date_to').val('');
            $('#applied_date_to').datepicker('setDate', null);
        }
    } else {
        $('#applied_date_to').datepicker('setStartDate', null);
    }
});
$("#loader").kendoLoader();
kendo.ui.progress.messages = {
    loading:
        '<div class="vue-simple-spinner animate-spin" style="position:absolute;top:50%;left:50%;transfrom:translate(-50%,-50%); margin: 0px auto; border-radius: 100%; border-color: rgb(50, 157, 243) rgb(238, 238, 238) rgb(238, 238, 238); border-style: solid; border-width: 3px; border-image: none 100% / 1 / 0 stretch; width: 30px; height: 30px;"></div>',
};

var OfferManage = function() {

    var offerLetter = function() {

        dateFormate('#created_at');
        dateFormate('.dateinput');
        //Date picker

        $('body').on('click', '[data-load-remote]', function(e) {
            e.preventDefault();
            var studentId = $(this).attr('id');
            var courseId = $(this).attr('data-id');
            var studentCourseId = $(this).attr('data-student-course-id');
            window.open("offer-letter/" + courseId + "/" + studentId + "/" + studentCourseId, 'offer-letter', "width="+screen.availWidth+",height="+screen.availHeight+"scrollbars=1,resizable=1");
        });

        $('body').on('click', '[data-load-remote-domestic]', function(e) {
            e.preventDefault();
            var studentId = $(this).attr('id');
            var courseId = $(this).attr('data-id');
            var studentCourseId = $(this).attr('data-student-course-id');
            window.open("domestic-offer-letter/" + courseId + "/" + studentId + "/" + studentCourseId, 'offer-letter', "width="+screen.availWidth+",height="+screen.availHeight+"scrollbars=1,resizable=1");
        });
        $('body').on('click', '.resetLabelId', function(e) {
            e.preventDefault();
            var studentCourseId = $(this).attr('data-student-course-id');
            $('.resetlabelbutton').attr('data-student-course-id',studentCourseId);
        });
        $('body').on('click', '.resetlabelbutton', function(e) {
            e.preventDefault();
            var studentCourseId = $(this).attr('data-student-course-id');
            var dataArr = {'studentCourseId': studentCourseId };
            var url = "offerManage/ajaxAction";
            var action = 'resetOfferLabel';

            ajaxAction(url, action, dataArr, function(resultData) {
                var result = $.parseJSON(resultData);
                if (result.status) {
                    $('#resetOfferLabel').modal('hide');
                    $('#offerManageStudent').DataTable().ajax.reload();
                    showToster(result.status, result.message);
                }
            });
        });

        deleteSingleData(site_url + 'delete-students-course-offer/');

        $('body').on('click', '.add-coe-offer-manage', function() {

            var studentId = $(this).attr('id');
            var offerId = $(this).attr('data-id');
            var studentCourseId = $(this).attr('data-student-course-id');
            var dataCourseCode = $(this).attr('data-course-code');
            var dataCourseName = $(this).attr('data-course-name');

            var courseTitle = dataCourseCode + '-' + dataCourseName;
            $('#student_id').val(studentId);
            $('.student_id').val(studentId);
            $('#offer_id').val(offerId);
            $('#id').val(studentCourseId);

            var ceoTitle = "Add COE Number to Application: " + offerId + "  and Course : " + courseTitle + "(Attempt:1)";
            $('.coe-title').html(ceoTitle);

            $(document).find('.refreshOutStandingBalanceIcon').attr('data-student-id', studentId);
            $(document).find('.refreshUnallocatedCreditIcon').attr('data-student-id', studentId);

            var dataArr = {courseID: studentCourseId};
            $.ajax({
                type: "POST",
                url: site_url + "offerManage/ajaxAction",
                headers: {
                    'X-CSRF-TOKEN': $('input[name="_token"]').val()
                },
                data: {'action': 'studentCOEdetail', 'data': dataArr},
                success: function(data) {
                    var result = JSON.parse(data);
                    $('.applicable').attr('value', result['coe_applicable']);
                    $("#code_no").val(result['coe_name']);
                    $("#oldFile").val(result['coe_image']);
                    if(result['coe_image'] != ''){
                        $("#downloadFile").html('<a class="downloadFile" href="javascript:;" data-id="'+result['coe_material_id']+'">'+result['coe_image']+'</a> <a class="link-black text-sm deleteCOEImage ml-2" data-filename="'+result['coe_image']+'" data-student-id="'+studentId+'" data-id="'+result['coe_material_id']+'" href="javascript:;"><i class="fa fa-remove"></i></a>');
                    }

                    if (result['coe_applicable'] == '1') {
                        $('#code_no').attr('disabled', true);
                        $(".applicable").attr('checked', 'true');
                    } else {
                        $('#code_no').removeAttr('disabled');
                    }

                    let amount = (result['xeroContact']) ? result['outstanding_balance'] : 0;
                    $(document).find('.outStandingBalance').text(kendo.toString(amount, "c"));

                    let unallocatedAmount = (result['xeroContact']) ? result['unallocatedCredit'] : 0;
                    $(document).find('.outUnallocatedBalanceText').text(kendo.toString(unallocatedAmount, "c"));

                    //TODO::GNG-3182 (Remove option for sync to xero from add COE)
                    /*if(result['xeroContact']){
                        let xeroSyncedAt = kendo.toString(kendo.parseDate(result['xeroContact']['xero_synced_at']), "d MMM yyyy, h:mm tt");
                        $('#is_sync').attr('disabled', true);
                        $('.sync-data').html(
                            `<span>Student already synced at ${xeroSyncedAt}</span>
                            <span>Sync Status :${result['xeroContact']['xero_contact_status']}</span>`
                        );
                    }else {
                        $('#is_sync').removeAttr('disabled');
                        $('.sync-data').html('');
                    }*/
                    //$('#is_sync').attr('checked', false);
                    //$('#is_sync').val(0);
                },
                error: function(err) {
                    //alert("error" + JSON.stringify(err));
                }
            });
        });


        $("body").on('click', '.searchData', function () {
            const that = $(this);
            loadingStart(that);
            updateDataTable();
        });

        $(".clearData").on("click", function () {
            $('#course_id').val('0').trigger('change');
            $("#status").val('All');
            $("#input_intake_date").val('');
            $("#course_type_id").val('All');
            $("#intake_name").val('').trigger('change');
            $("#campus").val('');
            $("#intake_year").val('All');
            $("#applied_date_from").val('');
            $("#applied_date_to").val('');
            setTimeout(updateDataTable, 200);
        });
        $('body').on('click','.deleteCOEImage',function(){
            var dataArr = {id: $(this).data('id'),studentId:$(this).data('student-id'),filename:$(this).data('filename')};
            var that = $(this);
            $.ajax({
                type: "POST",
                url: site_url + "offerManage/ajaxAction",
                headers: {
                    'X-CSRF-TOKEN': $('input[name="_token"]').val()
                },
                data: {'action': 'StudentCOEImageDelete', 'data': dataArr},
                success: function(data) {

                    if (data == 0) {
                        var status = 'alert-danger';
                        var msg = 'Something went wrong.';
                    }
                    else {
                        var status = 'alert-success';
                        var msg = 'file delete successfully';
                        that.closest('#downloadFile').remove();
                    }
                    // sessionDisplayMessage(status, msg);
                },
                error: function(err) {
                    //alert("error" + JSON.stringify(err));
                }
            });
        });

        $('.applicable').on('click', function() {
            if ($('.applicable:checked').val()) {
                $(this).attr('value', '1');
                $('#code_no').val('N/A');
                $('#code_no').attr('disabled', true);
            } else {
                $(this).attr('value', '0');
                $('#code_no').val('');
                $('#code_no').removeAttr('disabled');
            }
        });

        $('#search_by').on('change', function() {
            var searchBy = this.value;
            if (searchBy == 'created_at') {
                $('#by_string_field').hide();
                $('#by_date_applied').show();
                $('#search_string').val('');
                $('#by_course').val("");
                $('#by_course').hide();
            }
            else if (searchBy == 'course_name') {
                $('#by_string_field').hide();
                $('#by_date_applied').hide();
                $('#by_course').show();
                $('#search_string').val('');
            }
            else {
                $('#by_string_field').show();
                $('#by_date_applied').hide();
                $('#by_course').hide();
                $('#search_string').val('');
                $('#by_course').val("");
            }
        });

        $(".showFooter").click(function() {
            $(".offerFooter").show();
        });

        var dataArr = {};
        var columnWidth = {"width": "15%", "targets": 5};
        var arrList = {
            'tableID': '#offerManageStudent',
            'ajaxURL': site_url + "offerManage/ajaxAction",
            'ajaxAction': 'getOfferManageDatatabledata',
            'postData': dataArr,
            'hideColumnList': [12, 13, 14 , 15 , 16 , 17],
            'noSearchApply': [],
            'noSortingApply': [0, 3, 8, 10, 11, 17],
            'defaultSortColumn': 1,
            'defaultSortOrder': 'desc',
            'setColumnWidth': columnWidth,
            'intFilter': function() {
            },
            "drawCallback": function() {

               $("input[name='student_id']").iCheck({
                    checkboxClass: 'icheckbox_flat-green'
                });
             },
        };
        getDataTableSpe(arrList);

        $('body').on('ifChecked ifUnchecked', '.studentId', function () {

            if ($(this).val() === 'all') {
                $("#offerManageStudent tbody .studentId").iCheck(($(this).is(':checked')) ? 'check' : 'uncheck');
            } else {
                var arr = [];

                $("#offerManageStudent thead .studentId").iCheck('destroy');
                $("#offerManageStudent thead .studentId").prop('checked', true);

                $("#offerManageStudent tbody .studentId").each(function () {
                    if ($(this).is(':not(:checked)')) {
                        $("#offerManageStudent thead .studentId").prop('checked', false);
                    } else {
                        arr.push($(this).val());
                    }
                });

                var studentIdArr = JSON.stringify(arr);
                $("#studentIdArr").val(studentIdArr);

                $("#offerManageStudent thead .studentId").iCheck({radioClass: 'iradio_flat-green', checkboxClass: 'icheckbox_flat-green'});
            }
        });

        $("body").on("click", ".save-offer-label", function() {

            const studentJsonString = $('#studentIdArr').val();
            const offerLabelId = $('#offer_label').val();

            let studID = '';
            if (studentJsonString.trim() === "") {
                showToster('error', 'Select at least one student.');
                return false;
            }else if(offerLabelId.trim() === ""){
                showToster('error', 'Select offer label.');
                return false;
            } else {
                try {
                    studID = JSON.parse(studentJsonString);
                    if(studID.length == 0){
                        showToster('error', 'Select at least one student.');
                        return false;
                    }
                } catch (error) {
                    showToster('error', 'Select at least one student.');
                    return false;
                }
            }

            var dataArr = {'studentId': studID,'offerLabel':offerLabelId};
            var url = "offerManage/ajaxAction";
            var action = 'saveStudentsOfferLabel';

            ajaxAction(url, action, dataArr, function(resultData) {
                var result = $.parseJSON(resultData);
                if (result.status) {
                    $('#offerLabel').modal('hide');
                    $('#offerManageStudent').DataTable().ajax.reload();
                    showToster(result.status, result.message);
                }
            });
        });

        $(".campusf , .statusf, .studentId , .offerlabelcheckbox").iCheck({
            checkboxClass: 'icheckbox_flat-green'
        });

        $('.appleyeddatefilter').on('click', function() {

            if ($('.applied-from-date').val().trim() === "" || $('.applied-to-date').val().trim() === "") {
                showToster('error', 'Select both date.');
                return false;
            }

            var dataArr = [{'startdate': $('.applied-from-date').val(),'enddate':$('.applied-to-date').val()}];
            var dataTable = $('#offerManageStudent').DataTable();
            var postStr = JSON.stringify(dataArr);
            dataTable.column(3).search(postStr).draw();
            $('#date-filter').removeClass('in');
        });
        $('.resetfilter').on('click', function() {

            $('.applied-from-date').val('');
            $('.applied-to-date').val('');
            var dataArr = [{'startdate': $('.applied-from-date').val(),'enddate':$('.applied-to-date').val()}];
            var dataTable = $('#offerManageStudent').DataTable();
            var postStr = JSON.stringify(dataArr);
            dataTable.column(3).search(postStr).draw();
            $('#date-filter').removeClass('in');
        });

        $('.offerlabelcheckbox').on('ifChanged', function() {

            var dataTable = $('#offerManageStudent').DataTable();

            var val = [];

            $.each($("input[name='offerlabel']:checked"), function() {
                val.push($(this).val());
            });

            dataTable.column(11).search(val ? val : '', true, false).draw();
        });

        $('.statusf').on('ifChanged', function() {

            var dataTable = $('#offerManageStudent').DataTable();

            var val = [];

            $.each($("input[name='status']:checked"), function() {
                val.push($(this).val());
            });

            dataTable.column(10).search(val ? val : '', true, false).draw();
        });

        $(".campusf").on('ifChanged', function() {

            var dataTable = $('#offerManageStudent').DataTable();

            var val = [];
            $.each($("input[name='campus']:checked"), function() {
                val.push($(this).val());
            });

            dataTable.column(8).search(val ? val : '', true, false).draw();
        });

        $('#search_action').on('click', function() {

            var searchBy = $('#search_by').val();
            var searchString = $('#search_string').val();
            var searchCreatedAt = $('#created_at').val();
            var courseFilter = $('#courseFilter').val();
            var searchCollegeCampus = $('#college_campus').val();
            var applicationStautus = $('#applicationStautus').val();

            if (searchBy && (searchString != '' || searchCreatedAt != '' || searchCollegeCampus != '' || courseFilter != '')) {

                var that = $(this);
                loadingStart(that);
                $('#offerManageStudent tbody tr').remove();
                $.ajax({
                    url: site_url + "offerManage/ajaxAction",
                    method: "POST",
                    headers: {
                        'X-CSRF-TOKEN': $('input[name="_token"]').val(),
                    },
                    data: {'action': 'searchOfferManageStudent', 'data': {'searchBy': searchBy, 'searchCollegeCampus': searchCollegeCampus, 'searchString': searchString, 'searchCreatedAt': searchCreatedAt, 'courseFilter': courseFilter, 'applicationStautus': applicationStautus}},
                    success: function(data) {
                        loadingEnd(that);
                        $("#offerManageStudent tbody").html(data);
                        $('#result_count').text($('#search_count').val());
                        $('#noRecords').hide();
                        $('#offerPagignate').hide();
                    }
                });
            }
        });

        $("#addCOEformOfferManage").validate({
            rules: {
                code_no: {
                    required: true,
                }
            },
            messages: {
            },
            errorPlacement: function(error, element) {
            }
        });

        $(document).mouseup(function(e)
        {
            var container = $(".custom-animation-dropdown .dropdown-menu");

            // if the target of the click isn't the container nor a descendant of the container
            if($('.datepicker').length == 1){
               return false;
            }
            if(!$(e.target).hasClass('openDropdown')){
                if(container.has(e.target).length === 0){
                    $('.custom-animation-dropdown .dropdown-menu').removeClass("in");
                }
            }
        });

        $("input[name=campus]").click(function() {
            filtertitle("input[name=campus]");
        });

        $("input[name=status]").click(function() {
            filtertitle("input[name=status]");
        });

        $('body').on('click', '#offerManageExport', function(e){
            e.preventDefault();
            startAjaxLoader();
            $.ajax({
                url: site_url + "offerManage/ajaxAction",
                type: "POST",
                data: {'action': 'offer-export'},
                xhrFields: { responseType: "blob" }, // Set response type to blob for file download
                success: function (data) {
                    stopAjaxLoader();
                    triggerURLForDownload(data, "Offer_List.xlsx"); // Create a URL for the file and trigger a download
                },
                error: function (xhr, status, error) {
                    stopAjaxLoader();
                    //console.error("Export failed:", error);
                },
            });
        });

    };

    var sendEmail = function() {

        var old_template_type = $('#old_template_type').val();
        if (old_template_type > 0) {
            setOldEmailTemplate();
        }

        $('.sendTo').on('ifClicked', function(event) {
            var item = $(this).attr('data-id');
            var studentEmail = $('.student_email').val();
            var agentEmail = $('.agent_email').val();
            $('#email_to').val('');
            if (item == 'both') {
                $('#email_to').val(studentEmail + ',' + agentEmail);
            } else if (item == 'agent') {
                $('#email_to').val(agentEmail);
            } else {
                $('#email_to').val(studentEmail);
            }
        });

        $('body').on('change', '#course_id', function() {
            $('.error_msg_invoice').text('');
            $('.error_msg_offer').text('');
            setEmailTemplate();
        });

        $('body').on('click', '.student_invoice', function() {
            paymentInvoicePDF();
        });

        $('body').on('click', '.offer_letter', function() {
            offerLetterPDF();
        });

        $('body').on('change', '#template_type', function() {
            setEmailTemplate();
        });

        $('#email_template').on('change', function() {
            setEmailTemplateInfo();
        });

        function paymentInvoicePDF() {
            $('.error_msg_invoice').text('');
            if ($('.student_invoice').is(':checked')) {
                var studentId = $("#student_id").val();
                var courseId = $("#course_id").val();
                var dataArr = {'studentId': studentId, 'courseId': courseId};
                var url = "offerManage/ajaxAction";
                var action = 'getStudentInvoicePdf';

                ajaxAction(url, action, dataArr, function(resultData) {
                    var result = $.parseJSON(resultData);
                    if (result.status == 'false') {
                        $('.error_msg_invoice').text(result.msg);
                        $(".student_invoice:checkbox").removeAttr('checked');
                    }
                });
            }
        }

        function offerLetterPDF() {
            $('.error_msg_offer').text('');
            if ($('.offer_letter').is(':checked')) {
                var studentId = $("#student_id").val();
                var courseId = $("#course_id").val();
                var dataArr = {'studentId': studentId, 'courseId': courseId};
                var url = "offerManage/ajaxAction";
                var action = 'getStudentOfferLetterPdf';

                ajaxAction(url, action, dataArr, function(resultData) {
                    var result = $.parseJSON(resultData);
                    if (result.status == 'false') {
                        $('.error_msg_offer').text(result.msg);
                        $(".offer_letter:checkbox").removeAttr('checked');
                    }
                });
            }
        }


        function setEmailTemplate() {
            var courseId = $('#course_id').val();
            if (courseId > 0) {
                var EmailTemplateType = $('#template_type').val();

                var that = $(this);
                loadingStart(that);
                $('#email_template').find('option').remove();
                $('#email_template').append($('<option>', {
                    value: '', text: '--Select Template--'
                }));

                $.ajax({
                    url: site_url + "offerManage/ajaxAction",
                    method: "POST",
                    headers: {
                        'X-CSRF-TOKEN': $('input[name="_token"]').val(),
                    },
                    data: {'action': 'getEmailTemplate', 'data': {'EmailTemplateType': EmailTemplateType}},
                    success: function(data) {
                        loadingEnd(that);
                        var obj = jQuery.parseJSON(data);
                        $.each(obj, function(i, item) {
                            $('#email_template').append($('<option>', {
                                value: item.id,
                                text: item.template_name
                            }));
                        });
                        $('#email_template').selectric('refresh');
                    }
                });
            } else {
                showToster('error', 'Select cource first.', '');
                $('#email_template').find('option').remove();
                $('#email_template').append($('<option>', {
                    value: '', text: '--Select Template--'
                }));
            }
            setEmailTemplateInfo();
        }

        function setEmailTemplateInfo() {

            var emailTemplate = $('#email_template').val();
            var oldEmailTemplate = $('#old_email_template').val();
            var EmailContentId = (oldEmailTemplate == '') ? emailTemplate : oldEmailTemplate;

            var studentId = $("#student_id").val();
            var courseId = $("#course_id").val();
            var that = $(this);
            if (EmailContentId == '') {
                $('#email_subject').val('');
                $('#editor1').val('');
            } else {
                if (courseId != "") {
                    loadingStart(that);
                    $.ajax({
                        url: site_url + "offerManage/ajaxAction",
                        method: "POST",
                        headers: {
                            'X-CSRF-TOKEN': $('input[name="_token"]').val(),
                        },
                        data: {'action': 'getEmailContentOffer', 'data': {'EmailContentId': EmailContentId, 'studentId': studentId, 'courseId': courseId}},
                        success: function(data) {
                            loadingEnd(that);
                            $("#documentDiv").empty();
                            var obj = jQuery.parseJSON(data);
                            $.each(obj, function(i, item) {
                                $('#email_subject').val(item.email_subject);
                                CKEDITOR.instances['editor1'].setData(item.content);
                                var document = item.document;
                                $.each(document, function(j, itemDoc) {

                                    var docData = '<label class="display-ib" style="width:100%; color:green;">' + itemDoc.file.substring(10) + '</label>'
                                            + '<input name="email_attachment[' + j + ']" value="' + itemDoc.file + '" type="hidden">';

                                    $("#documentDiv").append(docData);
                                });
                            });
                        }
                    });
                }
            }
        }

        function setOldEmailTemplate() {
            var courseId = $('#course_id').val();
            if (courseId > 0) {
                var EmailTemplateType = $('#old_template_type').val();
                var oldEmailTemplate = $('#old_email_template').val();

                var that = $(this);
                loadingStart(that);
                $('#email_template').find('option').remove();
                $('#email_template').append($('<option>', {
                    value: '', text: '--Select Template--'
                }));

                $.ajax({
                    url: site_url + "offerManage/ajaxAction",
                    method: "POST",
                    headers: {
                        'X-CSRF-TOKEN': $('input[name="_token"]').val()
                    },
                    data: {'action': 'getEmailTemplate', 'data': {'EmailTemplateType': EmailTemplateType}},
                    success: function(data) {
                        loadingEnd(that);
                        var obj = jQuery.parseJSON(data);
                        $.each(obj, function(i, item) {
                            $('#email_template').append($('<option>', {
                                value: item.id,
                                text: item.template_name
                            }));

                            if (item.id == oldEmailTemplate) {
                                $('#email_template option[value="' + item.id + '"]').attr("selected", "selected");
                            }
                        });
                        $('#email_template').selectric('refresh');
                    }
                });
            } else {
                $('#email_template').find('option').remove();
                $('#email_template').append($('<option>', {
                    value: '', text: '--Select Template--'
                }));
            }
            setOldEmailTemplateInfo();
        }

        function setOldEmailTemplateInfo() {
            var EmailContentId = $('#old_email_template').val();
            var studentId = $("#student_id").val();
            var courseId = $("#course_id").val();
            var that = $(this);
            if (EmailContentId == '') {
                $('#email_subject').val('');
                $('#editor1').val('');
                //CKEDITOR.instances['editor1'].setData('');
            } else {
                if (courseId != "") {
                    loadingStart(that);
                    $.ajax({
                        url: site_url + "offerManage/ajaxAction",
                        method: "POST",
                        headers: {
                            'X-CSRF-TOKEN': $('input[name="_token"]').val(),
                        },
                        data: {'action': 'getEmailContentOffer', 'data': {'EmailContentId': EmailContentId, 'studentId': studentId, 'courseId': courseId}},
                        success: function(data) {
                            loadingEnd(that);
                            $("#documentDiv").empty();
                            var obj = jQuery.parseJSON(data);
                            $.each(obj, function(i, item) {
                                $('#email_subject').val(item.email_subject);
                                CKEDITOR.instances['editor1'].setData(item.content);
                                var document = item.document;
                                $.each(document, function(j, itemDoc) {

                                    var docData = '<label class="display-ib" style="width:100%;">' + itemDoc.file + '</label>'
                                            + '<input name="email_attachment[' + j + ']" value="' + itemDoc.file + '" type="hidden">';

                                    $("#documentDiv").append(docData);
                                });
                            });
                        }
                    });
                }
            }
        }

        jQuery.validator.addMethod(
                "multiemail",
                function(value, element) {
                    if (this.optional(element)) // return true on optional element
                        return true;
                    var emails = value.split(/[;,]+/); // split element by , and ;
                    var valid = true;
                    for (var i in emails) {
                        value = emails[i];
                        valid = valid && jQuery.validator.methods.email.call(this, $.trim(value), element);
                    }
                    return valid;
                },
                jQuery.validator.messages.email
                );
        $("#emailTemplateAddForm").validate({
            rules: {
                email_from: {
                    required: true,
                },
                email_to: {
                    required: true,
                },
                email_cc: {
                    multiemail: true
                },
                email_bcc: {
                    email: true
                },
                email_subject: {
                    required: true,
                },
                email_content: {

                    required: function()
                    {
                        CKEDITOR.instances.email_content.updateElement();
                    },
                }
            },
            messages: {
                email_content: "Required",
            },
            errorPlacement: function(error, element) {

                if (element.attr("name") == "email_content")
                {
                    error.insertBefore("textarea#editor1");
                }
            }
        });

        $(function() {
            //CKEDITOR.replace('editor1');
            CKEDITOR.replace('editor1').config.height = "400px";
        });
    };

    var offerDocumentChecklist = function() {

        radioCheckboxClass();
        deleteSingleData(site_url + 'delete-offer-document-checklist/');

        var rules = {
            document_name: {
                required: true
            },
            document_type: {
                required: true
            },
            "student_origin[]": {
                required: true,
                minlength: 1
            }
        };
        handleFormValidateNew($("#offerDocumentChecklist"), rules);

        $('body').on('change', '#originFilter', function() {
            documentCheckListFilter();
        });

        $('body').on('change', '#documentFilter', function() {
            documentCheckListFilter();
        });

        $('body').on('change', '#statusFilter', function() {
            documentCheckListFilter();
        });

        function documentCheckListFilter() {
            var originFilter = $("#originFilter").val();
            var documentFilter = $("#documentFilter").val();
            var statusFilter = $("#statusFilter").val();

            $.ajax({
                url: site_url + "setupSection/ajaxAction",
                method: "POST",
                headers: {
                    'X-CSRF-TOKEN': $('input[name="_token"]').val(),
                },
                data: {'action': 'offerDocumentChecklistFilter', 'data': {'originFilter': originFilter,
                        'documentFilter': documentFilter,
                        'statusFilter': statusFilter
                    }},
                success: function(data) {
                    var obj = jQuery.parseJSON(data);
                    $("#noRecord").hide();
                    $("#dataAppend").empty();
                    var compulsory = '-';
                    var is_active = '-';
                    $.each(obj, function(i, item) {
                        if (item.is_compulsory == "1") {
                            compulsory = '<input type="checkbox" checked class = "" disabled>';
                        } else {
                            compulsory = '<input type="checkbox" class = "" disabled>';
                        }

                        if (item.is_active == "1") {
                            is_active = '<input type="checkbox" checked class = "" disabled>';
                        } else {
                            is_active = '<input type="checkbox" class = "" disabled>';
                        }

                        var table = '<tr id="dataAppend">' +
                                '<td>' + item.document_name +
                                '<div class="action-overlay">' +
                                '<ul class="icon-actions-set">' +
                                '<li>' +
                                '<a class="link-black text-sm" data-toggle="tooltip" data-original-title="Edit" style="font-size: 13px;" href="edit-offer-document-checklist/' + item.id + '"><i class="fa fa-edit"></i></a>' +
                                '</li>' +
                                '<li>' +
                                '<span data-toggle="modal" class="delete" data-id="' + (item.id) + '" data-target="#deleteModal">' +
                                '<a class="link-black text-sm delete" data-toggle="tooltip" data-original-title="Delete" style="font-size: 13px;" href="javascript:;"><i class="fa fa-remove"></i> </a>' +
                                '</span>' +
                                '</li>' +
                                '</ul>' +
                                '</div>' +
                                '</td>' +
                                '<td>' + item.documentTypeText + '</td>' +
                                '<td>' + item.student_origin + '</td>' +
                                '<td>' + compulsory + '</td>' +
                                '<td>' + is_active + '</td>' +
                                '</tr>';
                        $("#dataAppend").append(table);
                    });
                    if (obj.length == 0) {
                        $("#noRecord").show();
                    }
                }

            });
        };

    };

    var handleOfferDosumentChecklist = function() {
        var dataArr = {};
        var columnWidth = {};
        var arrList = {
            'tableID': '#offerDocumentChecklist',
            'ajaxURL': site_url + "setupSection/ajaxAction",
            'ajaxAction': 'getOfferDocumentChecklist',
            'postData': dataArr,
            'hideColumnList': [],
            'noSearchApply': [],
            'noSortingApply': [],
            'defaultSortColumn': 0,
            'defaultSortOrder': 'asc',
            'setColumnWidth': columnWidth
        };
        getDataTable(arrList);
    };

    var StudentCourseChecklist  = function() {

        radioCheckboxClass();
        $('input').iCheck('destroy');

        $('body').on('change', '.upload_file', function() {
            var ext = $(this).val().split('.').pop().toLowerCase();
            var size = this.files[0].size;

            if ($.inArray(ext, ['pdf', 'jpg', 'jpeg']) == -1) {
                $(this).css('color', 'red');
                $('.errorMsg').text('The document file type must be jpg, jpeg or pdf');
            } else {
                if ((size > 5242880)) {
                    $(this).css('color', 'red');
                    $('.errorMsg').text('The document file size must be less than 5MB');
                } else {
                    $(this).css('color', 'inherit');
                    $('.errorMsg').text('');
                }
            }
        });

        deleteSingleData(site_url + 'delete-student-course-checklist-document/');
    };

    var offerManageChecklist = function() {

        radioCheckboxClass();
        $('input').iCheck('destroy');

        $('body').on('change', '.upload_file', function() {
            var ext = $(this).val().split('.').pop().toLowerCase();
            var size = this.files[0].size;

            if ($.inArray(ext, ['pdf', 'jpg', 'jpeg']) == -1) {
                $(this).css('color', 'red');
                $('.errorMsg').text('The document file type must be jpg, jpeg or pdf');
            } else {
                if ((size > 5242880)) {
                    $(this).css('color', 'red');
                    $('.errorMsg').text('The document file size must be less than 5MB');
                } else {
                    $(this).css('color', 'inherit');
                    $('.errorMsg').text('');
                }
            }
        });

        deleteSingleData(site_url + 'delete-offer-manage-checklist-document/');

    };

    var general = function() {
        icheckbox('green');
    };

    var generateStudID = function() {
        /* Start Generate Student ID */
        $("body").on("click", ".generateStudID", function() {
            var studID = $(this).attr('data-id');
            var coe = $(this).attr('data-coe');
            var generateID = $(this).attr('data-generate-id');
            var idType = $(this).attr('data-id-type');
            var primaryID = $(this).attr('data-primary-id');

            var applicationID = $(this).attr('data-application-id');
            $('#app_id').text(applicationID);

            if (coe != '') {
                if (generateID != 'false' || idType != '') {
                    if (idType == 'generate') {
                        alert("Student ID already Generated...");
                    } else {
                        var dataArr = {studID: studID, generateID: generateID};
                        $.ajax({
                            type: "POST",
                            url: site_url + "offerManage/ajaxAction",
                            headers: {
                                'X-CSRF-TOKEN': $('input[name="_token"]').val()
                            },
                            data: {'action': 'generateStudentID', 'data': dataArr},
                            success: function(data) {
                                var result = JSON.parse(data);

                                if (idType == 'reserve') {
                                    $("#generated_id").val(generateID);
                                    $("#generated_id").attr('disabled', true);
                                } else {
                                    $("#generated_id").val(result['generated_id']);
                                    $(".check_avail").trigger("click");
                                }
                                $("#idType").val(idType);
                                $("#primaryID").val(primaryID);
                                $("#student_id").val(result['student_id']);
                                $("#id_formate").val(result['generateIDFormate']);
                                $("#auto_no").val(result['auto_no']);
                            },
                            error: function(err) {
                                //alert("error" + JSON.stringify(err));
                            }
                        });
                    }
                } else {
                    alert("Student ID already Generated...");
                }
            } else {
                alert("Please add COE for this student before generating Student ID")
            }
        });

        $("body").on("click", ".check_avail", function() {
            var generateID = $('.generated_id').val();
            var IDtype = $(this).attr('data-id-type');
            var idType = $('#idType').val();

            if (idType == 'reserve') {
                alert("This desired student ID: " + generateID + " has been used. Please try another ID.")
            } else {
                checkAvailable(generateID, IDtype);
            }
        });

        $("body").on("click", ".generateIDNow", function() {

            var IDtype = $('#idType').val();
            var studID = $('#student_id').val();
            var generateID = $('#generated_id').val();
            var primaryID = $('#primaryID').val();


            var dataArr = {generateID: generateID, IDtype: IDtype, studID: studID};
            $.ajax({
                type: "POST",
                url: site_url + "offerManage/ajaxAction",
                headers: {
                    'X-CSRF-TOKEN': $('input[name="_token"]').val()
                },
                data: {'action': 'checkStudentIDAvailability', 'data': dataArr},
                success: function(data) {

                    if (data == 'FALSE') {
                        alert("This ID already exists");
                        return false;
                    } else if (data == 'NULL_ID') {
                        alert("Please Enter ID");
                        return false;
                    } else if (data == 'R2G' && primaryID != "") {
                        window.location = (site_url + 'offerManage/generate-studentID/' + studID + '/R2G' + primaryID + '/' + primaryID);
                    } else {
                        window.location = (site_url + 'offerManage/generate-studentID/' + studID + '/' + generateID + '/' + primaryID);
                    }
                },
                error: function(err) {

                }
            });
        });
        /* End Generate Student ID */
    };

    var addCoe = function() {
        $('#coeModal').on('hidden.bs.modal', function() {
            $('.coeModalBody').find('textarea,input').val('');
        });
    }
    var reserveStudID = function() {
        /* Start Reserve Student ID */
        $("body").on("click", ".reserveStudID", function() {
            var studID = $(this).attr('data-id');
            var reserveID = $(this).attr('data-reserve-id');
            var offerID = $(this).attr('data-offer-id');
            $('.offer_id').html(offerID);

            if (reserveID == 'true') {
                var dataArr = {studID: studID, generateID: 'true'};
                $.ajax({
                    type: "POST",
                    url: site_url + "offerManage/ajaxAction",
                    headers: {
                        'X-CSRF-TOKEN': $('input[name="_token"]').val()
                    },
                    data: {'action': 'generateStudentID', 'data': dataArr},
                    success: function(data) {
                        var result = JSON.parse(data);

                        $("#student_id").val(result['student_id']);
                        $("#reserve_id").val(result['generated_id']);
                        $("#auto_no").val(result['auto_no']);

                    },
                    error: function(err) {

                    }
                });
            } else {
                alert("Student ID already Reserved...");
            }
        });

        $("body").on("click", ".check_available", function() {
            var reserveID = $('#reserve_id').val();
            var IDtype = $(this).attr('data-id-type');
            checkAvailable(reserveID, IDtype);
        });

        $("body").on("click", ".reserveIDNow", function() {
            var studID = $('#student_id').val();
            var reserve_id = $('#reserve_id').val();
            var dataArr = {generateID: reserve_id};
            $.ajax({
                type: "POST",
                url: site_url + "offerManage/ajaxAction",
                headers: {
                    'X-CSRF-TOKEN': $('input[name="_token"]').val()
                },
                data: {'action': 'checkStudentIDAvailability', 'data': dataArr},
                success: function(data) {

                    if (data == 'FALSE') {
                        alert("This ID already exists");
                        return false;
                    } else if (data == 'NULL_ID') {
                        alert("Please Enter ID");
                        return false;
                    } else {
                        window.location = (site_url + 'offerManage/reserve-studentID/' + studID + '/' + reserve_id);
                    }
                },
                error: function(err) {

                }
            });
        });

        /* End Reserve Student ID */
    };

    $(dateWarningModalId).kendoDialog({
        width: "400px",
        title: "Confirm",
        content: "You are making a schedule before the course start date. Do you wish to continue?",
        actions: [
            { text: "No" },
            {
                text: "Yes",
                primary: true,
                action: function () {
                    $(dateWarningModalId).data('form').submit();
                },
            },
        ],
        animation: {
            open: {
                effects: "fade:in"
            }
        },
        open: function(){
            $(dateWarningModalId)
                .parent()
                .find(".k-dialog-titlebar")
                .addClass("gradientbackground");
            $(dateWarningModalId)
                .parent()
                .find("button:first")
                .addClass(
                    "bg-white border rounded-lg border-gray-300 text-gray-700 hover:shadow hover:bg-gray-50 focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-primary-blue-500",
                );
            $(dateWarningModalId)
                .parent()
                .find(".k-primary")
                .addClass(
                    "text-white bg-primary-blue-500 hover:shadow-lg rounded-lg focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-indigo-500",
                );
        },
        visible: false,
    });

    var upfrontFeeSchedule = function() {
        //Date picker
        dateFormate("#installmentStartDate");

        deleteSingleData(site_url + 'delete-upfront-fee-schedule/');

        $("#adoffermanageupfrontfeeschedule").validate({
            'ignore': [],
            rules: {
                installmentStartDate: {required: true},
                no_of_installment: {required: true},
                frequency: {required: true},
                duration: {required: true}
            },
            messages: {
            },
            errorPlacement: function(error, element) {
            },
            submitHandler: function(form) {
                let courseStartDate = new Date($('#course_start_date').val());
                //let scheduleStartDate = new Date($('#installmentStartDate').val());
                let scheduleStartDate = new Date(convertDateFormatDMYToYMD($('#installmentStartDate').val()));
                if (scheduleStartDate < courseStartDate) {
                    $(dateWarningModalId).data("kendoDialog").open();
                } else {
                    form.submit();
                }
                return false;
            }
        });
    };

    function convertDateFormatDMYToYMD(inputDate) {
        // Split the input date string into day, month, and year
        let parts = inputDate.split("-");
        let day = parts[0];
        let month = parts[1];
        let year = parts[2];
        // Swap day and month and format the date as "MM-DD-YYYY"
        let formattedDate = year + "-" + month + "-" + day;
        return formattedDate;
    }

    var offerManageSendMail = function() {
        $("body").on("change", "#course_id", function() {
            studentCourseDataFetch();
        });
        studentCourseDataFetch();

        function studentCourseDataFetch() {
            var studenIdPrimary = $('#studenIdPrimary').val();
            var course_id = $('#course_id').val();
            var dataArr = {studenIdPrimary: studenIdPrimary, course_id: course_id};
            $.ajax({
                type: "POST",
                url: site_url + "offerManage/ajaxAction",
                headers: {
                    'X-CSRF-TOKEN': $('input[name="_token"]').val()
                },
                data: {'action': 'offerManageStudentCourse', 'data': dataArr},
                success: function(data) {
                    var result = JSON.parse(data);
                    $('#agentData').empty();
                    $('#courseLengthData').empty();
                    $('#courseFeeData').empty();
                    $('#agentData').append(result[0]['agency_name']);
                    $('#courseLengthData').append(result[0]['start_date'] + " - " + result[0]['finish_date']);
                    $('#courseFeeData').append(result[0]['course_fee']);

                },
                error: function(err) {

                }
            });
        }

        $("body").on("click", "#findStudent", function() {
            var idValue = $('#idValue').val();
            var dataArr = {idValue: idValue};
            $.ajax({
                type: "POST",
                url: site_url + "offerManage/ajaxAction",
                headers: {
                    'X-CSRF-TOKEN': $('input[name="_token"]').val()
                },
                data: {'action': 'offerManageFindStudent', 'data': dataArr},
                success: function(data) {
                    var result = JSON.parse(data);
                    $('#dataFoundError').empty();
                    if (result.length == 0) {
                        $('#dataFoundError').append("Data not found");
                    }
                    else if (idValue == "") {
                        $('#dataFoundError').append("Please enter student generate id");
                    }
                    else {
                        $('#dataFoundError').empty();
                        window.location = (site_url + 'offer-generate-invoice-view/' + result[0]['id']);
                    }
                },
                error: function(err) {
                    //alert("error" + JSON.stringify(err));
                }
            });
        });

        $('#course_id').on('change', function() {
            var that = $(this);
            paymentGetData(that);
        });

        var that = $(this);
        paymentGetData(that);

        function paymentGetData(that) {
            var student_id = $('#studenIdPrimary').val();
            var course_id = $('#course_id').val();
            var resultArr = {'student_id': student_id, 'course_id': course_id};

            var studenGeneratedId = $('#studenGeneratedId').val();

            $('#scheduleTitlePay').empty();
            $('#scheduleTitle').empty();
            $('#scheduleTitle').append("Payment Miscellaneous For Student: " + studenGeneratedId + "; Course: " + $("#course_id option:selected").text());
            $('#scheduleTitlePay').append("Payment Schedule For Student: " + studenGeneratedId + "; Course: " + $("#course_id option:selected").text());
            $.ajax({
                url: site_url + "offerManage/ajaxAction",
                method: "POST",
                headers: {
                    'X-CSRF-TOKEN': $('input[name="_token"]').val()
                },
                data: {'action': 'studentPayment', 'data': resultArr},
                success: function(data) {

                    loadingEnd(that);
                    var obj = jQuery.parseJSON(data);
                    $('#dataAppend').empty();
                    $('#miscellaneousPayment').empty();

                    if (obj['paymentDetail'].length != 0) {
                        $.each(obj['paymentDetail'], function(i, item) {
                            var table = '<tr id="dataAppend">' +
                                    '<td>' + item.id +
                                    '<div class="action-overlay">' +
                                    '<ul class="icon-actions-set">' +
                                    '<li><a class="link-black text-sm modify-payment-schedule" data-toggle="tooltip" data-original-title="Edit" id="' + item.id + '"> <i class="fa fa-edit"></i> </a></li>' +
                                    '<li><span data-toggle="modal" class="deletePayment" data-id="' + item.id + '" data-target="#deleteModal"><a class="link-black text-sm" data-toggle="tooltip" data-original-title="Delete" href="javascript:;"><i class="fa fa-remove"></i></a></span></li>' +
                                    '</ul>' +
                                    '</div>' +
                                    '</td>' +
                                    '<td>' + item.invoice_number + '</td>' +
                                    '<td>' + replaceDateFormate(item.due_date) + '</td>' +
                                    '<td>' + item.agency_name + '</td>' +
                                    '<td>' + item.upfront_fee_to_pay + '</td>' +
                                    '<td>' + item.upfront_fee_pay + '</td>' +
                                    '<td>' + item.paid_duration_text + " " + item.duration_type + '</td>' +
                                    '</tr>';
                            $("#dataAppend").append(table);
                        });
                    } else {
                        $("#dataAppend").append('<tr><td colspan="16" style="color:red; text-align: center">No Record Found</td></tr>');
                    }
                    if (obj['miscellaneousPayment'].length != 0) {
                        $.each(obj['miscellaneousPayment'], function(i, item) {
                            if(item.paid_on==null){
                                var paid_on=" - ";
                            }else{
                                var paid_on=replaceDateFormate(item.paid_on);
                            }
                            var table = '<tr id="miscellaneousPayment">' +
                                    '<td>' + item.id +
                                    '<div class="action-overlay">' +
                                    '<ul class="icon-actions-set">' +
                                    '<li><span data-toggle="modal" class="deletePaymentMisPay" data-id="' + item.id + '" data-target="#deleteModalPayment"><a class="link-black text-sm" data-toggle="tooltip" data-original-title="Delete" href="javascript:;"><i class="fa fa-remove"></i></a></span></li>' +
                                    '</ul>' +
                                    '</div>' +
                                    '</td>' +
                                    '<td>' + item.invoice_number + '</td>' +
                                    '<td>' + replaceDateFormate(item.due_date) + '</td>' +
                                    '<td>' + paid_on + '</td>' +
                                    '<td>' + item.amount + '</td>' +
                                    '<td>' + item.remarks + '</td>' +
                                    '</tr>';
                            $("#miscellaneousPayment").append(table);
                        });
                    } else {
                        $("#miscellaneousPayment").append('<tr><td colspan="16" style="color:red; text-align: center">No Record Found</td></tr>');
                    }
                }
            });
        }

        dateFormateToday("#dueDateEdit");

        $('body').on('click', '.deletePayment', function() {
            var dataid = $(this).attr('data-id');
            $('.yes-sure').attr('data-id', dataid);
        });

        $('body').on('click', '.deletePaymentMisPay', function() {
            var dataid = $(this).attr('data-id');
            $('.deleteDataMis').attr('data-id', dataid);
        });

        $("body").on("click", ".deleteDataMis", function() {
            var deleteId = $(this).attr('data-id');
            var that = $(this);
            var resultArr = {'deleteId': deleteId};
            $.ajax({
                url: site_url + "offerManage/ajaxAction",
                method: "POST",
                headers: {
                    'X-CSRF-TOKEN': $('input[name="_token"]').val()
                },
                data: {'action': 'deleteStudentPaymentMis', 'data': resultArr},
                success: function(data) {
                    paymentGetData(that);
                    $('#deleteModalPayment').modal('hide');

                    var status = 'alert-success';
                    var msg = 'Student Miscellaneous Payment Delete successfully';
                    sessionDisplayMessage(status, msg);
                }

            });
        });

        $("body").on("click", ".deleteData", function() {
            var deleteId = $(this).attr('data-id');
            var that = $(this);
            var resultArr = {'deleteId': deleteId};
            $.ajax({
                url: site_url + "offerManage/ajaxAction",
                method: "POST",
                headers: {
                    'X-CSRF-TOKEN': $('input[name="_token"]').val()
                },
                data: {'action': 'deleteStudentPayment', 'data': resultArr},
                success: function(data) {
                    paymentGetData(that);
                    $('#deleteModal').modal('hide');
                    if (data == 0) {
                        var status = 'alert-danger';
                        var msg = 'Transaction for this schedule is already generated so you can not delete this transaction.';
                    }
                    else {
                        var status = 'alert-success';
                        var msg = 'Student Paymnet Delete successfully';
                    }
                    sessionDisplayMessage(status, msg);
                }

            });
        });

        $("body").on("click", "#editPayment", function() {
            var editId = $('#editId').val();
            var dueDateEdit = $('#dueDateEdit').val();
            var that = $(this);
            var resultArr = {'editId': editId, 'dueDateEdit': dueDateEdit};
            $.ajax({
                url: site_url + "offerManage/ajaxAction",
                method: "POST",
                headers: {
                    'X-CSRF-TOKEN': $('input[name="_token"]').val()
                },
                data: {'action': 'editStudentPayment', 'data': resultArr},
                success: function(data) {

                    paymentGetData(that);
                    $('#modifyPaymentSchedule').modal('hide');
                    var status = 'alert-success';
                    var msg = 'Student Paymnet Due date update successfully';
                    sessionDisplayMessage(status, msg);
                }

            });
        });

        $("body").on("click", ".modify-payment-schedule", function() {
            $('#modifyPaymentSchedule').modal('show');
            var editId = $(this).attr('id');
            var resultArr = {'editId': editId};
            $.ajax({
                url: site_url + "offerManage/ajaxAction",
                method: "POST",
                headers: {
                    'X-CSRF-TOKEN': $('input[name="_token"]').val()
                },
                data: {'action': 'editDataStudentPayment', 'data': resultArr},
                success: function(data) {
                    $('#editId').val(editId);
                    var obj = jQuery.parseJSON(data);
                    $("#dueDateEdit").datepicker("setDate", obj[0]['due_date']);
                }

            });


        });

        $('.combineLink').on('click', function() {

            if ($('#course_id').val() == "") {
                alert("Please Select Course First")
            } else {
                $(".combineLink").attr("href", "/generate-offer-invoice/" + $('#course_id').val() + "/" + $('#studenIdPrimary').val());
            }
        });
    };

    var checkAvailable = function(generateID, IDtype) {
        var dataArr = {generateID: generateID, IDtype: IDtype};
        $.ajax({
            type: "POST",
            url: site_url + "offerManage/ajaxAction",
            headers: {
                'X-CSRF-TOKEN': $('input[name="_token"]').val()
            },
            data: {'action': 'checkStudentIDAvailability', 'data': dataArr},
            success: function(data) {
                if (data == 'FALSE') {
                    alert("This ID already exists");
                    return false;
                } else if (data == 'NULL_ID') {
                    alert("Please Enter ID");
                    return false;
                }
            },
            error: function(err) {

            }
        });
    };

    var syncStudent = function(){

        $('body').on('click', '.syncToXero', function(e) {
            e.preventDefault();
            let studentId = $(this).attr('data-student-id');
            $('.syncStudent').attr('data-student-id', studentId);
        });

        $('body').on('click', '.syncStudent', function(e) {
            e.preventDefault();
            let dataArr = {'studentId': $(this).attr('data-student-id') };
            startBlur()
            ajaxAction("offerManage/ajaxAction", "syncStudent", dataArr, function(resultData) {
                stopBlur();
                let result = $.parseJSON(resultData);
                if (result.status) {
                    $('#syncToXeroModel').modal('hide');
                    $('#offerManageStudent').DataTable().ajax.reload();
                    showToster(result.status, result.message);
                }
            });
        });

        $('body').on('click', '.syncData', function(e) {
            e.preventDefault();
            let dataArr = { studentID: $(this).attr('data-student-id') };
            ajaxAction("offerManage/ajaxAction", "getSyncData", dataArr, function(response) {
                let xeroContact = JSON.parse(response);
                let successUpdateHtml = '';
                let failUpdateHtml = '';

                if(xeroContact['xero_failed_at']){
                    let xero_failed_at = kendo.toString(kendo.parseDate(xeroContact['xero_failed_at']), "d MMM yyyy, h:mm tt");
                    failUpdateHtml = `<p> Failed Date: ${xero_failed_at}</p>
                                                <p> Reason for Failed: ${xeroContact['xero_failed_message']}</p>
                                                <button type="button" class="btn btn-info reSyncStudent" data-student-id="${xeroContact['contact_id']}">Re-Sync</button>`;
                }else{
                    let xero_synced_at = kendo.toString(kendo.parseDate(xeroContact['xero_synced_at']), "d MMM yyyy, h:mm tt");
                    successUpdateHtml = `<p>Xero ID : ${xeroContact['xero_contact_id']}</p>
                                                    <p>Sync Status : ${xeroContact['xero_contact_status']}</p>
                                                    <p>Sync Time : ${xero_synced_at}</p>`;
                }
                $('#syncDataModel').find('.success-update').html(successUpdateHtml);
                $('#syncDataModel').find('.fail-update').html(failUpdateHtml);
            });
        });

        $('body').on('click', '.reSyncStudent', function(e) {
            e.preventDefault();
            let dataArr = {'studentId': $(this).attr('data-student-id') };
            startBlur()
            ajaxAction("offerManage/ajaxAction", "reSyncStudent", dataArr, function(resultData) {
                stopBlur();
                let result = $.parseJSON(resultData);
                $('#syncDataModel').modal('hide');
                showToster(result.status, result.message);
                if(result.status == 'success'){
                    $('#offerManageStudent').DataTable().ajax.reload();
                }
            });
        });

        function handleSyncClick(service) {
            let studentIdArr = getSelectedStudentIds();
            let modalId = '#bulkSyncStudentTo' + service;

            if (studentIdArr.length > 0) {
                $(document).find(modalId).modal('show');
            } else {
                showToster('error', 'Please select at least one record.');
                $(document).find(modalId).modal('hide');
            }
        }

        function performSync(service) {
            let studentIdArr = getSelectedStudentIds();
            if(studentIdArr.length > 0){
                startBlur();
                let dataArr = {
                    studentIds: studentIdArr,
                    service: service
                };
                ajaxAction("offerManage/ajaxAction", "bulkSyncStudent", dataArr, function(response) {
                    stopBlur();
                    let res = $.parseJSON(response);
                    if(res.status){
                        showToster(res.status, res.message)
                    }

                    // Show detailed popup for sync results
                    if (res.show_popup && res.student_details) {
                        if (service === 'Zoho') {
                            showZohoSyncResults(res.student_details);
                        } else if (service === 'Xero') {
                            showXeroSyncResults(res.student_details);
                        }
                    }

                    if (res.success_count > 0) {

                        showToster("success", res.success_msg);

                        if (res.error_count > 0) {
                            setTimeout(() => showToster("error", res.error_msg), 5000);
                        }

                        //$(document).find(`#bulkSyncStudentTo${service}`).modal('hide');
                        //$('#offerManageStudent').DataTable().ajax.reload();

                    } else if (res.error_count > 0) {
                        showToster("error", res.error_msg)
                    }
                    $('#offerManageStudent').DataTable().ajax.reload();
                    $(document).find(`#bulkSyncStudentTo${service}`).modal('hide');

                    /*showToster(res.status, res.message);
                    $(document).find(`#bulkSyncStudentTo${service}`).modal('hide');
                    if (res.status == 'success') {
                        $('#offerManageStudent').DataTable().ajax.reload();
                    }*/
                });
            }else{
                showToster('error', 'Select at least one student.');
            }
        }

        function showZohoSyncResults(studentDetails) {
            let tableBody = $('#syncResultsTableBody');
            tableBody.empty();

            studentDetails.forEach(function(student) {
                let statusClass = '';
                let statusIcon = '';

                switch(student.status) {
                    case 'Success':
                        statusClass = 'text-success';
                        statusIcon = '<i class="fa fa-check-circle"></i> ';
                        break;
                    case 'Failed':
                        statusClass = 'text-danger';
                        statusIcon = '<i class="fa fa-times-circle"></i> ';
                        break;
                    case 'Already Synced':
                        statusClass = 'text-warning';
                        statusIcon = '<i class="fa fa-exclamation-circle"></i> ';
                        break;
                    case 'Invalid':
                        statusClass = 'text-muted';
                        statusIcon = '<i class="fa fa-question-circle"></i> ';
                        break;
                    default:
                        statusClass = 'text-info';
                        statusIcon = '<i class="fa fa-info-circle"></i> ';
                }

                let row = `
                    <tr>
                        <td>${student.id}</td>
                        <td>${student.name}</td>
                        <td class="${statusClass}">${statusIcon}${student.status}</td>
                        <td>${student.sync_datetime}</td>
                        <td class="text-danger">${student.fail_reason || '-'}</td>
                    </tr>
                `;
                tableBody.append(row);
            });

            $('#zohoSyncResultsModal').modal('show');
        }

        function showXeroSyncResults(studentDetails) {
            let tableBody = $('#xeroSyncResultsTableBody');
            tableBody.empty();

            studentDetails.forEach(function(student) {
                let statusClass = '';
                let statusIcon = '';

                switch(student.status) {
                    case 'Success':
                        statusClass = 'text-success';
                        statusIcon = '<i class="fa fa-check-circle"></i> ';
                        break;
                    case 'Failed':
                        statusClass = 'text-danger';
                        statusIcon = '<i class="fa fa-times-circle"></i> ';
                        break;
                    case 'Already Synced':
                        statusClass = 'text-warning';
                        statusIcon = '<i class="fa fa-exclamation-circle"></i> ';
                        break;
                    case 'Invalid':
                        statusClass = 'text-muted';
                        statusIcon = '<i class="fa fa-question-circle"></i> ';
                        break;
                    default:
                        statusClass = 'text-info';
                        statusIcon = '<i class="fa fa-info-circle"></i> ';
                }

                let row = `
                    <tr>
                        <td>${student.id}</td>
                        <td>${student.name}</td>
                        <td class="${statusClass}">${statusIcon}${student.status}</td>
                        <td>${student.sync_datetime}</td>
                        <td class="text-danger">${student.fail_reason || '-'}</td>
                    </tr>
                `;
                tableBody.append(row);
            });

            $('#xeroSyncResultsModal').modal('show');
        }

        $("body").on("click", ".bulk_sync_to_xero", function(e) {
            e.preventDefault();
            handleSyncClick('Xero');
        });

        $("body").on("click", ".bulk_sync_to_zoho", function(e) {
            e.preventDefault();
            handleSyncClick('Zoho');
        });

        $("body").on("click", ".confirm_sync_xero", function(e) {
            e.preventDefault();
            performSync("Xero");
        });

        $("body").on("click", ".confirm_sync_zoho", function(e) {
            e.preventDefault();
            performSync("Zoho");
        });

        $("body").on("click", ".unallocatedCredit", function (e) {
            e.preventDefault();
            let studentId = $(this).attr('data-student-id');
            let amount = $(this).attr('data-amount');
            $(document).find('.refreshOutStandingBalanceIcon').attr('data-student-id', studentId);
            $(document).find('.outStandingBalance').text(kendo.toString(parseFloat(amount), 'c'));

            let unallocatedAmount = $(this).attr('data-unallocatedamount');
            $(document).find('.refreshUnallocatedCreditIcon').attr('data-student-id', studentId);
            $(document).find('.outUnallocatedBalanceText').text(kendo.toString(parseFloat(unallocatedAmount), "c"));
        });

        $("body").on("click", ".refreshOutStandingBalanceIcon", function (e) {
            e.preventDefault();
            let refreshTag = $(this);
            let refreshIcon = refreshTag.find('img');
            if (refreshTag.hasClass('processing')) {
                return;
            }
            refreshTag.addClass('processing');
            refreshIcon.addClass('animate-spin');

            let dataArr = { 'student_id': $(this).attr('data-student-id') };
            ajaxActionV2("api/get-outstanding-balance-from-xero", "POST", dataArr, function (response) {
                refreshIcon.removeClass('animate-spin');
                refreshTag.removeClass('processing');

                if(response.status == 'success'){
                    showToster(response.status, "Outstanding balance updated from xero");
                    let amount = (response.data.outstanding_balance > 0) ? response.data.outstanding_balance : 0;
                    $(document).find('.outStandingBalance').text(kendo.toString(amount, "c"));
                }else{
                    showToster(response.status, response.message);
                }
            });
        });
        $("body").on("click", ".refreshUnallocatedCreditIcon",function (e) {
            e.preventDefault();
            let refreshTag = $(this);
            let refreshIcon = refreshTag.find('img');
            if (refreshTag.hasClass('processing')) {
                return;
            }
            refreshTag.addClass('processing');
            refreshIcon.addClass('animate-spin');
            let dataArr = { 'student_id': $(this).attr('data-student-id') };
            ajaxActionV2("api/get-unallocated-credit-from-xero", "POST", dataArr, function (response) {
                refreshIcon.removeClass('animate-spin');
                refreshTag.removeClass('processing');

                if(response.status == 'success'){
                    showToster(response.status, "Unallocated balance updated from xero");
                    $(document).find('.outUnallocatedBalanceText').text(kendo.toString(response.data.unallocatedCreditAmount, "c"));
                }else{
                    showToster(response.status, response.message);

                }
            });
        });
            function populateDropdown($dropdown, data, defaultOptionText) {
                $dropdown.empty();
                if ($.isEmptyObject(data)) {
                    $dropdown.append(`<option value="">${defaultOptionText}</option>`);
                } else {
                    $.each(data, function (key, value) {
                        $dropdown.append(`<option value="${key}">${value}</option>`);
                    });
                }
            }

        // Your AJAX handler
                $("body").on("change", "#course_type_id", function () {
                    console.log();

                    let dataArr = { 'course_type_id': $(this).val() };
                    let courseTypeName = $(this).find("option:selected").text();
                    const isShortCourse = $(this).val() === SHORT_COURSE_TYPE_ID
                    $('.intake_name_div').toggleClass('hidden', !isShortCourse);
                    $('.intake_date_div').toggleClass('hidden', isShortCourse);

                    // Perform the AJAX request
                    ajaxActionV2("api/get-courses-list-from-type", "POST", dataArr, function (response) {
                        console.log(response);

                        // Populate Course Select dropdown
                        let courseSelect = $('#course_id');
                        populateDropdown(courseSelect, response.data.courseSelect, '-- Select Course --');

                        // Populate Intake Name dropdown
                        let intakeName = $('#intake_name');
                        populateDropdown(intakeName, response.data.courseIntake, '-- Select Intake --');

                        // Populate Campus
                        let courseCampus = $('#campus');
                        populateDropdown(courseCampus, response.data.courseCampus, '-- Select Campus --');
                    });
                });

                $("body").on("change", "#course_id", function () {
                    console.log();

                    let dataArr = { 'course_id': $(this).val() };

                    ajaxActionV2("api/get-intake-details-list-from-course", "POST", dataArr, function (response) {
                        console.log(response);

                        let intakeName = $('#intake_name');
                        populateDropdown(intakeName, response.data.courseIntake, '-- Select Intake --');

                        let courseCampus = $('#campus');
                        populateDropdown(courseCampus, response.data.courseCampus, '-- Select Campus --');

                        let intakeYear = $('#intake_year');
                        populateDropdown(intakeYear, response.data.intakeYearSelect, '-- Select Year --');
                    });
                });
                $("body").on("change", "#intake_year", function () {
                    console.log();

                    let dataArr = { 'intake_year': $(this).val(), 'course_id': $('#course_id').val() };

                    ajaxActionV2("api/get-intake-data-list-from-year", "POST", dataArr, function (response) {
                        console.log(response);

                        let intakeName = $('#intake_name');
                        populateDropdown(intakeName, response.data.courseIntake, '-- Select Intake --');
                    });
                });
    };

    function getSelectedStudentIds(){
        let studIds = [];
        $("#offerManageStudent tbody .studentId").each(function () {
            if ($(this).is(':checked')) {
                studIds.push($(this).attr('data-student-id'));
            }
        });
        return studIds;
    }

    function startBlur(){
        $('body').css('filter', 'blur(3px)');
    }

    function stopBlur(){
        $('body').css('filter', 'blur(0px)');
    }

    function startAjaxLoader() {
        $(document)
            .on("ajaxStart", function () {
                kendo.ui.progress($(document.body), true);
            })
            .on("ajaxStop", function () {
                kendo.ui.progress($(document.body), false);
            });
    }

    function stopAjaxLoader() {
        $(document)
            .on("ajaxStart", function () {
                kendo.ui.progress($(document.body), false);
            })
            .on("ajaxStop", function () {
                kendo.ui.progress($(document.body), false);
            });
    }

    function triggerURLForDownload(data, fileName) {
        const url = window.URL.createObjectURL(new Blob([data]));
        const a = document.createElement("a");
        a.href = url;
        a.download = fileName;
        document.body.append(a);
        a.click();
        a.remove();
        window.URL.revokeObjectURL(url);
    }
    function updateDataTable() {
        const course_id = $("#course_id").val();
        const status = $("#status").val();
        const intake_date = $("#input_intake_date").val();
        const course_type_id = $("#course_type_id").val();
        const intake_name = $("#intake_name").val();
        const campus = $("#campus").val();
        const applied_date_from  = $("#applied_date_from").val();
        const applied_date_to = $("#applied_date_to").val();
        const newUrl = `${site_url}offerManage/ajaxAction?course_id=${encodeURIComponent(course_id)}&status=${encodeURIComponent(status)}&intake_date=${encodeURIComponent(intake_date)}&course_type_id=${encodeURIComponent(course_type_id)}&intake_name=${encodeURIComponent(intake_name)}&campus=${encodeURIComponent(campus)}&applied_date_to=${encodeURIComponent(applied_date_to)}&applied_date_from=${encodeURIComponent(applied_date_from)}`;

        $('#offerManageStudent').DataTable().ajax.url(newUrl).load();
    }

    return{
        initOfferLetter: function() {
            $('.searchDropdown').select2();
            ['#intake_name', '#campus'].forEach(function (selector) {
                $(selector).select2({
                    width: '500px'
                });
            });
            offerLetter();
            generateStudID();
            reserveStudID();
            addCoe();
            syncStudent();
          //radioCheckboxClass();
        },
        initSendEmail: function() {
            sendEmail();
            radioCheckboxClass();
        },
        initOfferDocumentChecklist: function() {
            offerDocumentChecklist();
        },
        initlist: function() {
            handleOfferDosumentChecklist();
            offerDocumentChecklist();
        },
        initOfferManageChecklist: function() {
            offerManageChecklist();
            radioCheckboxClass();
        },
        initStudentCourseChecklist : function() {
            StudentCourseChecklist();
            radioCheckboxClass();
        },
        initUpfrontFeeSchedule: function() {
            upfrontFeeSchedule();
        },
        initOfferManageSendMail: function() {
            offerManageSendMail();
        }
    };
}();