let myEditorRiskMatrixTab;
var selectedRiskMatrixTabText = "all";
var riskMatrixGrid;
var riskMatrixDataSource;

function initializeRiskMatrixTab() {
    console.log("Initializing Risk Matrix Tab");
    
    // Initialize tooltips
    setTimeout(() => {
        $(".k-timeline-date-wrap span")
            .parent()
            .removeClass("k-timeline-date-wrap");
    }, 5000);
    
    $(".addNewNotes").kendoTooltip({
        filter: "button",
        position: "bottom-right",
        width: 150,
        showAfter: 300,
        showOn: "click",
        show: function (e) {
            e.sender.popup.element.find(".k-callout").remove();
            e.sender.popup.element.addClass("tw-popup--top-right");
            e.sender.popup.wrapper.css({
                right: "48px",
                left: "unset",
            });
        },
        content: function (e) {
            return kendo.template($("#actionTemplateRiskMatrix").html());
        },
    });

    // Initialize risk matrix components
    initializeRiskSummaryCards();
    initializeRiskMatrixGrid();
    initializeRiskMatrixFilters();
    
    // Load initial data
    loadRiskMatrixData();
}

function initializeRiskSummaryCards() {
    // Load summary data from API
    ajaxActionV2('api/v2/tenant/student-risk-assessments/summary', 'GET', {
        student_id: studentId
    }, function(response) {
        if (response.success) {
            renderRiskSummaryCards(response.data);
        }
    });
}

function renderRiskSummaryCards(data) {
    const cardTemplate = kendo.template($("#riskSummaryCardTemplate").html());
    const container = $("#riskSummaryCards");
    
    const cards = [
        {
            title: "Total Assessments",
            count: data.total || 0,
            colorClass: "text-blue-600",
            bgColorClass: "bg-blue-100",
            iconColorClass: "text-blue-600",
            icon: "📊"
        },
        {
            title: "High Risk",
            count: data.high_risk || 0,
            colorClass: "text-red-600",
            bgColorClass: "bg-red-100",
            iconColorClass: "text-red-600",
            icon: "⚠️"
        },
        {
            title: "Medium Risk",
            count: data.medium_risk || 0,
            colorClass: "text-yellow-600",
            bgColorClass: "bg-yellow-100",
            iconColorClass: "text-yellow-600",
            icon: "⚡"
        },
        {
            title: "Low Risk",
            count: data.low_risk || 0,
            colorClass: "text-green-600",
            bgColorClass: "bg-green-100",
            iconColorClass: "text-green-600",
            icon: "✅"
        }
    ];
    
    container.empty();
    cards.forEach(card => {
        container.append(cardTemplate(card));
    });
}

function initializeRiskMatrixGrid() {
    riskMatrixDataSource = new kendo.data.DataSource({
        transport: {
            read: {
                url: "api/v2/tenant/student-risk-assessments",
                type: "GET",
                data: function() {
                    return {
                        student_id: studentId,
                        filters: JSON.stringify(getRiskMatrixFilters())
                    };
                }
            }
        },
        schema: {
            data: "data",
            total: "total",
            model: {
                id: "id",
                fields: {
                    id: { type: "number" },
                    risk_level: { type: "number" },
                    risk_category: { type: "string" },
                    created_at: { type: "date" },
                    updated_at: { type: "date" }
                }
            }
        },
        pageSize: 20,
        serverPaging: true,
        serverSorting: true,
        serverFiltering: true
    });

    riskMatrixGrid = $("#riskMatrixGrid").kendoGrid({
        dataSource: riskMatrixDataSource,
        height: 400,
        sortable: true,
        pageable: {
            refresh: true,
            pageSizes: [10, 20, 50],
            buttonCount: 5
        },
        columns: [
            {
                field: "id",
                title: "ID",
                width: "80px"
            },
            {
                field: "risk_level",
                title: "Risk Level",
                width: "120px",
                template: function(dataItem) {
                    const levels = {
                        1: '<span class="badge badge-success">Low</span>',
                        2: '<span class="badge badge-warning">Medium</span>',
                        3: '<span class="badge badge-danger">High</span>'
                    };
                    return levels[dataItem.risk_level] || '<span class="badge badge-secondary">Unknown</span>';
                }
            },
            {
                field: "risk_category",
                title: "Category",
                width: "120px",
                template: function(dataItem) {
                    return dataItem.risk_category ? dataItem.risk_category.replace('_', ' ').toUpperCase() : '-';
                }
            },
            {
                field: "course.name",
                title: "Course",
                width: "200px",
                template: function(dataItem) {
                    return dataItem.course ? dataItem.course.name : '-';
                }
            },
            {
                field: "created_at",
                title: "Assessment Date",
                width: "150px",
                format: "{0:dd/MM/yyyy}"
            },
            {
                field: "updated_at",
                title: "Last Updated",
                width: "150px",
                format: "{0:dd/MM/yyyy HH:mm}"
            },
            {
                title: "Actions",
                width: "100px",
                template: function(dataItem) {
                    return '<button class="btn btn-sm btn-primary" onclick="viewRiskAssessmentDetails(' + dataItem.id + ')">View</button>';
                }
            }
        ]
    }).data("kendoGrid");
}

function initializeRiskMatrixFilters() {
    // Initialize filter change events
    $("#riskLevelFilter, #categoryFilter").on("change", function() {
        loadRiskMatrixData();
    });
}

function getRiskMatrixFilters() {
    return {
        risk_level: $("#riskLevelFilter").val(),
        risk_category: $("#categoryFilter").val(),
        student_id: studentId
    };
}

function loadRiskMatrixData() {
    if (riskMatrixDataSource) {
        riskMatrixDataSource.read();
    }
    
    // Also refresh summary cards
    initializeRiskSummaryCards();
}

function viewRiskAssessmentDetails(id) {
    // Fetch risk assessment details
    ajaxActionV2(`api/v2/tenant/student-risk-assessments/${id}`, 'GET', {}, function(response) {
        if (response.success) {
            showRiskAssessmentDetails(response.data);
        }
    });
}

function showRiskAssessmentDetails(data) {
    // Create modal content
    let detailsHtml = `
        <div class="space-y-4">
            <div class="grid grid-cols-2 gap-4">
                <div>
                    <p class="text-sm font-medium text-gray-500">Risk Level</p>
                    <p class="text-sm font-bold">${getRiskLevelText(data.risk_level)}</p>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-500">Category</p>
                    <p class="text-sm font-bold">${data.risk_category ? data.risk_category.replace('_', ' ').toUpperCase() : '-'}</p>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-500">Course</p>
                    <p class="text-sm font-bold">${data.course ? data.course.name : '-'}</p>
                </div>
                <div>
                    <p class="text-sm font-medium text-gray-500">Assessment Date</p>
                    <p class="text-sm font-bold">${kendo.toString(new Date(data.created_at), "dd/MM/yyyy")}</p>
                </div>
            </div>
            
            <div>
                <p class="text-sm font-medium text-gray-500">Notes</p>
                <p class="text-sm">${data.notes || 'No notes available'}</p>
            </div>
            
            <div>
                <p class="text-sm font-medium text-gray-500">Assessment Data</p>
                <div class="bg-gray-50 p-3 rounded">
                    <pre class="text-xs">${JSON.stringify(data.data || {}, null, 2)}</pre>
                </div>
            </div>
        </div>
    `;
    
    // Show in modal
    $("#riskAssessmentDetails").html(detailsHtml);
    
    // Open modal
    $("#riskAssessmentModal").kendoWindow({
        width: "600px",
        title: "Risk Assessment Details",
        visible: false,
        modal: true,
        actions: ["Close"]
    }).data("kendoWindow").center().open();
}

function getRiskLevelText(level) {
    const levels = {
        1: '<span class="badge badge-success">Low</span>',
        2: '<span class="badge badge-warning">Medium</span>',
        3: '<span class="badge badge-danger">High</span>'
    };
    return levels[level] || '<span class="badge badge-secondary">Unknown</span>';
}

function refreshRiskMatrix() {
    loadRiskMatrixData();
    notificationDisplay("Risk matrix data refreshed", "", "success");
}

function exportRiskMatrix() {
    if (riskMatrixGrid) {
        riskMatrixGrid.saveAsExcel();
    }
}

function manageRiskMatrixTab() {
    console.log("Managing Risk Matrix Tab", selectedTabText, selectedRiskMatrixTabText);
    if (selectedTabText === "riskmatrix") {
        initializeRiskMatrixTab();
    }
}
