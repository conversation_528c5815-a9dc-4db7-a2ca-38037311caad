kendo.culture().calendar.firstDay = 1;

kendo.ui.Form.fn.options.validatable.validateOnBlur = false;
kendo.ui.Validator.fn.options.validateOnBlur = false;

$(document).ready(function () {
    $(".k-picker-wrap > .k-input[type='text']").on('focus', function () {
        var datePicker = $(this).data('kendoDatePicker');
        datePicker.open();
        $(this).on('blur', function () {
            var inputDate = $(this).val();
            if (!inputDate) {
                return;
            }
            var isValidDate = kendo.parseDate(inputDate, 'dd-MM-yyyy') !== null;
            // If not valid, reset to today's date
            if (!isValidDate) {
                datePicker.value(new Date());
                datePicker.trigger('change');
            }
        });
    });
});

function ajaxcallwithMethod(url, data, method, callback) {
    //  App.startPageLoading();
    var rtrn = $.ajax({
        type: method,
        url: url,
        data: data,
        headers: { 'X-CSRF-TOKEN': $('input[name="_token"]').val() },
        success: function (result) {
            //   App.stopPageLoading();
            callback(result);
        },
        error: function (result) {
            callback(result);
        },
    });
    return rtrn;
}

function ajaxcallwithMethodFileKendo(url, data, method, callback) {
    kendo.ui.progress($(document.body), false);
    var rtrn = $.ajax({
        type: method,
        url: url,
        data: data,
        processData: false,
        contentType: false,
        headers: { 'X-CSRF-TOKEN': $('input[name="_token"]').val() },
        success: function (result) {
            kendo.ui.progress($(document.body), false);
            callback(result);
        },
        error: function (result) {
            kendo.ui.progress($(document.body), false);
            callback(result);
        },
    });
    return rtrn;
}

function ajaxcallwithMethodKendo(url, data, method, callback) {
    //  App.startPageLoading();
    kendo.ui.progress($('.tw-scroll-pagination-loader'), true);
    var rtrn = $.ajax({
        type: method,
        url: url,
        data: data,
        headers: { 'X-CSRF-TOKEN': $('input[name="_token"]').val() },
        success: function (result) {
            //   App.stopPageLoading();
            kendo.ui.progress($('.tw-scroll-pagination-loader'), false);
            callback(result);
        },
        error: function (result) {
            kendo.ui.progress($('.tw-scroll-pagination-loader'), false);
            callback(result);
        },
    });
    return rtrn;
}

function handleAjaxFormSubmitLaravel(form, type) {
    if (typeof type === 'undefined') {
        ajaxcallwithMethod(
            $(form).attr('action'),
            $(form).serialize(),
            $(form).attr('method'),
            function (output) {
                if (typeof output.responseText !== 'undefined') {
                    output = output.responseText;
                }

                handleAjaxResponseNew(output);
            }
        );
    } else if (type === true) {
        // App.startPageLoading();
        var options = {
            resetForm: false, // reset the form after successful submit
            success: function (output) {
                //   App.stopPageLoading();
                handleAjaxResponseNew(output);
            },
        };
        $(form).ajaxSubmit(options);
    }
    return false;
}

function handleAjaxResponseNew(output) {
    if (typeof output == 'string') {
        output = JSON.parse(output);
    }

    if (output.message != '' && typeof output.errors === 'undefined') {
        showToster(output.status, output.message, '');
    }
    if (output.message != '' && typeof output.errors !== 'undefined') {
        for (var key in output.errors) {
            showToster('error', output.errors[key], '');
        }
    }
    if (typeof output.redirect !== 'undefined' && output.redirect != '') {
        setTimeout(function () {
            window.location.href = output.redirect;
        }, 4000);
    }
    if (typeof output.jscode !== 'undefined' && output.jscode != '') {
        eval(output.jscode);
    }
}

function replaceDataItHtml(replaceDataObj) {
    $.each(replaceDataObj, function (key, value) {
        if (typeof value == 'string' || typeof value == 'number') {
            if ($('.' + key).is('input, select')) {
                $('.' + key).val(value);
            } else {
                $('.' + key).text(value);
            }
        }
        if (typeof value == 'object') {
            var tableHtml = '';
            $.each(value, function (objKey, objValue) {
                tableHtml += '<tr><td>' + objValue.comments + '</td></tr>';
                $('.' + key).html(tableHtml);
            });
        }
    });
}

function onLoadSidebarOpen() {
    $('.treeview').each(function () {
        if ($(this).closest('li').hasClass('active')) {
            if (!$(this).next('ul').is(':visible')) {
                $(this).next('ul').show();
            }
        }
    });
}

$('.updateNotificationCount').on('click', function () {
    var id = $(this).attr('data-id');
    var notificationcount = $(this).attr('data-count');
    if (notificationcount > 0) {
        $('.updateNotificationCount').find('g').remove();
        $.ajax({
            url: site_url + 'dashboard/ajaxAction',
            method: 'POST',
            headers: {
                'X-CSRF-TOKEN': $('input[name="_token"]').val(),
            },
            data: { action: 'notificationcountupdate', data: { id: id } },
            success: function (data) {
                if (data == 'success') {
                    $('.updateNotificationCount').attr('data-count', '0');
                }
            },
        });
    }
});

$('.treeview').click(function () {
    // $('.sidebar-menu ul').slideUp('fast');
    if (!$(this).next('ul').is(':visible')) {
        $(this).next('ul').slideDown('fast');
    } else {
        $(this).next('ul').slideUp('fast');
    }
});

$('.menuli').click(function () {
    $('.leftsidebarmenu li').removeClass('bg-geekBlue-500');
    $(this).addClass('bg-geekBlue-500');
    $(this).removeClass('hover:bg-gray-700');
});

$('#menuBtn').click(function () {
    $('.toogleWidth.w-64').toggleClass('d-none');
    $('.main-header .logo').toggleClass('hidden');
});

$('#user-menu').on('click', function (event) {
    $(this).parent().toggleClass('open');
});

$('body').on('click', function (e) {
    if (
        !$('#user-menu').is(e.target) &&
        $('#user-menu').has(e.target).length === 0 &&
        $('.open').has(e.target).length === 0
    ) {
        $('#user-menu').parent().removeClass('open');
        $('.headAction').children('div').removeClass('open');
    }
});

window.onload = function () {
    var selItem = sessionStorage.getItem('SelItem');
    $('#sort-item').val(selItem);
    onLoadSidebarOpen();
};

$('#sort-item').change(function () {
    var selVal = $(this).val();
    sessionStorage.setItem('SelItem', selVal);
});

function customDataSource(apiUrl, fields, dateField = [], extraField = [], pageSize = 10) {
    kendo.ui.progress($(document.body), false);
    return {
        type: 'json',
        transport: {
            read: {
                url: site_url + apiUrl,
                dataType: 'json',
                type: 'POST',
                data: dateField,
            },
            parameterMap: function (data, operation) {
                if (operation == 'read') {
                    if (data.filter && extraField.length > 0) {
                        $.each(data.filter.filters, function (index, value) {
                            if ($.inArray(value.field, extraField) != -1) {
                                data.filter.filters[index].value = kendo.toString(
                                    data.filter.filters[index].value,
                                    'yyyy-MM-dd'
                                );
                            }
                        });
                    }
                    return data;
                }
            },
        },
        schema: defaultSchema(fields),
        pageSize: pageSize,
        serverPaging: true,
        serverFiltering: true,
        serverSorting: true,
    };
}

function customPageableArr() {
    return {
        pageSizes: [10, 25, 50, 100],
        alwaysVisible: false,
        previousNext: true,
        buttonCount: 5,
        messages: {
            display: 'Showing {0} to {1} of {2} results',
            select: 'test',
            empty: 'No Record Found',
            first: '&laquo;',
            last: '&raquo;',
            next: '&rsaquo;',
            previous: '&lsaquo;',
        },
    };
}

function customDataBoundWithActionMenu(gridID, e) {
    let grid = e.sender;
    let rows = grid.tbody.find("[role='row']").not(':last-child');
    rows.unbind('click');
    rows.on('click', onClickForCheckbox);
    setTimeout(function () {
        setFilterIcon(gridID);
    }, 100);
}

function customDataBoundWithCheckbox(gridID, e) {
    let grid = e.sender;
    let rows = grid.tbody.find("[role='row']");
    rows.unbind('click');
    rows.on('click', onClickForCheckbox);
    setTimeout(function () {
        setFilterIcon(gridID);
    }, 100);

    /* Manage pager & no record template */
    manageGridPagerTemplate(gridID);
}

function onClickForCheckbox(e) {
    if ($(e.target).hasClass('k-checkbox')) return;
    let row = $(e.target).closest('tr');
    let checkbox = $(row).find('.k-checkbox');
    checkbox.click();
}

function customGridHtml(gridID) {
    /* Change filter icon from column & apply filter data */
    setFilterIcon(gridID);

    /* Change custom pagination for grid & needs only page load time*/
    customPagination(gridID);
}

function setFilterIcon(gridID) {
    let gridHtml = $(document).find(gridID);
    let activeIcon =
        '<svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M1 2C1 1.44772 1.44772 1 2 1H12C12.5523 1 13 1.44772 13 2V3.25245C13 3.51767 12.8946 3.77202 12.7071 3.95956L8.62623 8.04044C8.43869 8.22798 8.33333 8.48233 8.33333 8.74755V10.3333L5.66667 13V8.74755C5.66667 8.48233 5.56131 8.22798 5.37377 8.04044L1.29289 3.95956C1.10536 3.77202 1 3.51767 1 3.25245V2Z" stroke="#1890FF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>';
    let icon =
        '<svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M1 2C1 1.44772 1.44772 1 2 1H12C12.5523 1 13 1.44772 13 2V3.25245C13 3.51767 12.8946 3.77202 12.7071 3.95956L8.62623 8.04044C8.43869 8.22798 8.33333 8.48233 8.33333 8.74755V10.3333L5.66667 13V8.74755C5.66667 8.48233 5.56131 8.22798 5.37377 8.04044L1.29289 3.95956C1.10536 3.77202 1 3.51767 1 3.25245V2Z" stroke="#9CA3AF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>';
    gridHtml.find('.k-grid-filter').each(function () {
        if ($(this).hasClass('k-state-active')) $(this).html(activeIcon);
        else $(this).html(icon);
    });
}

function customPagination(gridID) {
    /*setTimeout(function(){
        togglePagination(gridID);
    },500);*/
    let gridHtml = $(document).find(gridID);
    gridHtml
        .find('.k-pager-sizes')
        .contents()
        .filter(function () {
            return this.nodeType === 3;
        })
        .remove();
    gridHtml
        .find('.k-grid-pager')
        .append(
            '<div class="flex justify-between items-center w-full"><div class="gridInfo"></div><div class="gridPagination k-pager-wrap k-grid-pager k-widget k-floatwrap"></div></div>'
        );
    gridHtml.find('.k-pager-sizes').appendTo(gridID + ' .k-grid-pager .flex .gridInfo');
    gridHtml
        .find('.k-pager-info')
        .insertAfter(gridID + ' .k-grid-pager .flex .gridInfo .k-pager-sizes');
    gridHtml
        .find('.k-pager-wrap a, .k-pager-wrap .k-pager-numbers-wrap')
        .appendTo(gridID + ' .k-grid-pager .flex .gridPagination');
    gridHtml.find('.k-pager-wrap.k-grid-pager.k-widget.k-floatwrap').removeClass('k-pager-sm');
}

function togglePagination(gridID) {
    /*if($(gridID).data('kendoGrid').dataSource.total() <= 10){
        $(gridID).data("kendoGrid").setOptions({ pageable: false });
    }*/
    var gridHtml = $(document).find(gridID);
    if ($(gridID).data('kendoGrid').dataSource.total() > 10) {
        gridHtml.find('.k-grid-pager').show();
    } else {
        gridHtml.find('.k-grid-pager').hide();
    }
}

function manageNoDataTemplate(gridID, dataDiv = '.dataListDiv', noDataDiv = '.noDataListDiv') {
    if ($(gridID).data('kendoGrid').dataSource.total() > 0) {
        $(document).find(noDataDiv).hide();
        $(document).find(dataDiv).show();
    } else {
        $(document).find(noDataDiv).show();
        $(document).find(dataDiv).hide();
    }
}

function manageGridPagerTemplate(gridID) {
    let cnt = $(gridID).data('kendoGrid').dataSource.total();
    if (cnt == 0) $(gridID).find('.k-pager-wrap').hide();
    else $(gridID).find('.k-pager-wrap').show();
}

function manageListSkeletonTemplate(gridID, skeletonID = '#listSkeleton') {
    $(document).find(skeletonID).hide();
    $(document).find(gridID).show();
}

function manageListPagerTemplate(gridID) {
    let cnt = $(gridID).data('kendoListView').dataSource.total();
    if (cnt == 0) $(gridID).parent('div').find('.no-record-template').html(noRecordTemplateHtml());
    else $(gridID).parent('div').find('.no-record-template').html('');
}

function noRecordTemplate() {
    return { template: noRecordTemplateHtml() };
}

function noRecordTemplateHtml(subHeading = "Sorry, we couldn't find any results.") {
    return (
        "<div class='inline-flex flex-col space-y-4 p-6 items-center justify-center w-full'>" +
        "<span class='k-font-icon k-i-clipboard k-icon-64 icon-color-blue'></span>" +
        "<div class='flex flex-col space-y-2 items-center justify-center'>" +
        "<h3 class='text-xl font-bold leading-7 text-center text-gray-800'>No Results Found</h3>" +
        "<p class='text-sm leading-5 font-normal text-center text-gray-500'>" +
        subHeading +
        '</p>' +
        '</div>' +
        '</div>'
    );
}

function manageFilterOnGrid(gridID, fieldName, searchKeyword) {
    /* apply grid filter with custom filter using single array */

    var grid = $(gridID).data('kendoGrid');
    let tempArr = {
        field: fieldName,
        operator: 'contains',
        value: searchKeyword,
    };

    if (typeof grid.dataSource.filter() != 'undefined' && grid.dataSource.filter() != null) {
        let flag = true;
        let oldFilterArr = grid.dataSource.filter().filters;
        Object.entries(oldFilterArr).forEach((filter) => {
            if (filter[1].field == fieldName) {
                filter[1].value = searchKeyword;
                flag = false;
            }
        });
        if (flag) {
            oldFilterArr.push(tempArr);
        }
        grid.dataSource.read();
        //grid.dataSource.filter().filters.push(tempArr);
    } else {
        grid.dataSource.filter(tempArr);
    }
    setFilterIcon(gridID);
}
function manageFilterOnGridV2(gridID, fieldName, searchKeyword) {
    /* apply grid filter with custom filter using single array */

    var grid = $(gridID).data('kendoGrid');
    let tempArr = {
        field: fieldName,
        operator: 'contains',
        value: searchKeyword,
    };

    grid.dataSource.filter(tempArr);

    setFilterIcon(gridID);
}

function manageProfilePic(id, profile_pic, nameStr) {
    /* Manage user name with profile picture or default 2 characters */
    let html = '';
    if (profile_pic == '' || profile_pic == null) {
        let displayName = 'NA';
        if (typeof nameStr !== undefined && nameStr != null) {
            let name = nameStr.toUpperCase().split(/\s+/);
            displayName = name[0].charAt(0) + name[1].charAt(0);
        } else {
            nameStr = 'N/A';
        }
        html =
            "<div class='flex items-center stud_" +
            id +
            "'><div class='user-profile-pic flex justify-center items-center h-7 w-7 rounded-full bg-blue-500'><span class='text-xs leading-6'>" +
            displayName +
            "</span></div>&nbsp;<div class='student-first-name text-sm leading-4 text-gray-600 action-div ml-2'>" +
            nameStr +
            '</div></div>';
    } else {
        html =
            "<div class='flex items-center stud_" +
            id +
            "'><img class='h-8 w-8 rounded-full' src='" +
            profile_pic +
            "' alt=''>&nbsp;<div class='student-first-name w-5/6 text-sm leading-4 text-gray-600 action-div'>" +
            nameStr +
            '</div></div>';
    }
    return html;
}

function startDateChange(start, end) {
    let startDate = start.value();
    let endDate = end.value();
    if (startDate) {
        startDate = new Date(startDate);
        startDate.setDate(startDate.getDate());
        end.min(startDate);
    } else if (endDate) {
        start.max(new Date(endDate));
    } else {
        endDate = new Date();
        start.max(endDate);
        end.min(endDate);
    }
}

function endDateChange(start, end) {
    let startDate = start.value();
    let endDate = end.value();
    if (endDate) {
        endDate = new Date(endDate);
        endDate.setDate(endDate.getDate());
        start.max(endDate);
    } else if (startDate) {
        end.min(new Date(startDate));
    } else {
        endDate = new Date();
        start.max(endDate);
        end.min(endDate);
    }
}

function notificationDisplay(msg, title, status) {
    let notification = $('#notification')
        .kendoNotification({
            position: {
                pinned: true,
                bottom: 30,
                left: 30,
            },
            autoHideAfter: 0,
            stacking: 'down',
            templates: [
                {
                    type: 'error',
                    template: $('#errorTemplate').html(),
                },
                {
                    type: 'success',
                    template: $('#successTemplate').html(),
                },
            ],
        })
        .data('kendoNotification');

    notification.show(
        {
            title: title,
            message: msg,
        },
        status
    );

    setTimeout(function () {
        notification.hide();
    }, 4000);
}

function notificationDisplayForCreateTimetable(msg, title, status) {
    let notification = $('#notification')
        .kendoNotification({
            position: {
                pinned: true,
                bottom: 30,
                left: 30,
            },
            autoHideAfter: 0,
            stacking: 'down',
            templates: [
                {
                    type: 'error',
                    template: $('#errorTemplatev2').html(),
                },
                {
                    type: 'success',
                    template: $('#successTemplate').html(),
                },
            ],
        })
        .data('kendoNotification');

    notification.show(
        {
            title: title,
            message: msg,
        },
        status
    );

    setTimeout(function () {
        notification.hide();
    }, 2000);
}

function customLoader() {
    $('#loader').kendoLoader();
    kendo.ui.progress.messages = {
        loading:
            '<div class="vue-simple-spinner animate-spin" style="position:absolute;top:50%;left:50%;transfrom:translate(-50%,-50%); margin: 0px auto; border-radius: 100%; border-color: rgb(50, 157, 243) rgb(238, 238, 238) rgb(238, 238, 238); border-style: solid; border-width: 3px; border-image: none 100% / 1 / 0 stretch; width: 30px; height: 30px;"></div>',
    };
}

function animationPropertyForWindow() {
    //return { open: { effects: "zoomIn", duration: 100 }, close: { effects: "zoomIn", reverse: true, duration: 100} };
    return {
        open: {
            effects: 'slideIn:left',
            duration: 300,
        },
        close: {
            effects: 'slideIn:left',
            reverse: true,
            duration: 300,
        },
    };
}

$('body').on('click', '#filterBtn', function () {
    // $('#menuBtn').click();
    $('.closeFilter').toggleClass('closeIcon');
    $('.toggelfilter').toggleClass('widthzero');
    $('.toggelfilter').toggleClass('px-0');

    $('#toggelFilterDiv').toggleClass('w-full');
    $('#toggelFilterDiv').toggleClass('w-9/12');
});

$('body').on('keyup', '.searchInputField', function (e) {
    let gridID = '#' + $(this).attr('data-grid-id');
    let searchKeyword = $(this).val();
    manageFilterOnGrid(gridID, 'searchKey', searchKeyword);
});

function defaultWindowSlideFormat(title, widthVal = 40) {
    return {
        title: title,
        width: widthVal + '%',
        height: '100%',
        actions: ['close'],
        draggable: false,
        resizable: false,
        modal: true,
        position: {
            top: 0,
            left: 100 - widthVal + '%',
        },
        animation: {
            open: {
                effects: 'slideIn:left',
                duration: 450,
            },
            close: {
                effects: 'slideIn:left fade:out',
                reverse: true,
                duration: 200,
            },
        },
        visible: false,
    };
}

function openCenterWindow(titleText, widthVal = 34, topVal = 25, leftVal = 33) {
    return {
        title: titleText,
        width: widthVal + '%',
        // height: "70%",
        actions: ['close'],
        draggable: false,
        resizable: false,
        modal: true,
        position: {
            top: topVal + '%',
            left: leftVal + '%',
        },
        animation: defaultCloseAnimation(),
    };
}

function swapDiv(showDiv, replaceDiv, response = []) {
    $(showDiv).show();
    kendo.fx(showDiv).replace(replaceDiv, 'swap').run();
    if (typeof response.status != 'undefined' && response.status !== null) {
        notificationDisplay(response.message, '', response.status);
    }
}

/*Leftside bar submenu*/
$('.treeviewfirsta').click(function () {
    $('.sidebar-menu ul').slideUp('fast');
    if (!$(this).next().is(':visible')) {
        $(this).next().slideDown('fast');
    }
});
$('.subsubmenu').click(function () {
    if (!$(this).next().is(':visible')) {
        $(this).next().slideDown('fast');
    }
});

// Auto complete function

$('#findStudent').kendoAutoComplete({
    dataTextField: 'findStudent',
    filter: 'contains',
    minLength: 1,
    template: function (dataItem) {
        //return dataItem.first_name;
        return manageAutocomplete(
            dataItem.generated_stud_id,
            dataItem.first_name,
            dataItem.family_name,
            dataItem.profile_pic
        );
    },
    dataSource: autoCompleteDataSource(),
    select: onSelect,
});

function autoCompleteDataSource() {
    return {
        serverFiltering: true,
        type: 'json',
        schema: {
            data: 'data',
        },
        transport: getTransportReadOnly('get-autocomplete'),
    };
}

function manageAutocomplete(id, first_name, family_name, profile_pic) {
    let html = '';
    if (profile_pic == '') {
        let sname = first_name.charAt(0) + family_name.charAt(0);
        html =
            "<div class='flex items-center'><div class='autocomplate-student-header student-profile-pic h-6 w-6 rounded-full bg-blue-500'><span class='text-xs'>" +
            sname.toUpperCase() +
            "</span></div>&nbsp;<span class='k-state-default'><h3 class='text-sm autocomplateremove-margin'>" +
            first_name +
            ' ' +
            family_name +
            "</h3><p class='text-sm'>" +
            id +
            '</p></span></div>';
    } else {
        html =
            "<div class='flex items-center'><img class='h-6 w-6 rounded-full' src='" +
            profile_pic +
            "' alt=''>&nbsp;<span class='k-state-default'><h3 class='text-sm autocomplateremove-margin'>" +
            first_name +
            ' ' +
            family_name +
            "</h3><p class='text-sm'>" +
            id +
            '</p></span></div>';
    }
    return html;
}

function onSelect(e) {
    window.location.href = site_url + 'student-profile/' + e.dataItem.id;
    if ('kendoConsole' in window) {
        kendoConsole.log('event :: select (' + e.dataItem.first_name + ')');
    }
}

function previewDocument(previewId, filepath) {
    console.log('preview');
    $.when(
        $.getScript('https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.2.2/pdf.js'),
        $.getScript('https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.2.2/pdf.worker.js')
    )
        .done(function () {
            window.pdfjsLib.GlobalWorkerOptions.workerSrc =
                'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.2.2/pdf.worker.js';
        })
        .then(function () {
            $('#' + previewId).kendoPDFViewer({
                pdfjsProcessing: {
                    file: filepath,
                },
                width: '100%',
                height: '100%',
            });
        });
}

$(document).on('click', '#findStudent', function () {
    openGlobalSearchWindow();
    searchLoading();
});

$('#globalSearchModal')
    .kendoWindow({
        title: 'Global Search',
        width: '50%',
        height: '70%',
        actions: ['close'],
        draggable: false,
        resizable: false,
        modal: true,
        position: {
            top: '15%',
            left: '25%',
        },
        animation: defaultCloseAnimation(),
        open: function () {
            $('.k-window-actions').addClass('globalsearchbutton');
        },
        visible: false,
    })
    .data('kendoWindow');

addModalClassToWindows(['#globalSearchModal']);

var globalSearch = [];
var recentSearch = [];

$(window).keydown(function (event) {
    if (event.ctrlKey && event.keyCode == 75) {
        event.preventDefault();
        openGlobalSearchWindow();
        event.preventDefault();
    }
});

function openGlobalSearchWindow() {
    //$(document).find('#globalSearchModal_wnd_title').closest('.k-window-titlebar').hide();
    let globalSearchWindow = $(document).find('#globalSearchModal');
    globalSearchWindow.getKendoWindow().open();
    globalSearchWindow.find('#globalSearchText').trigger('click');
    globalSearchFn();
}

function openWindow(title) {
    return {
        title: title,
        width: '40%',
        // height: "70%",
        actions: ['close'],
        draggable: false,
        resizable: false,
        modal: true,
        position: {
            top: '15%',
            left: '30%',
        },
        animation: defaultCloseAnimation(),
    };
}

$('#searchTabStrip').kendoTabStrip({
    //tabPosition: "left",
    animation: defaultOpenAnimation(),
});

$(document).on('focus', '#globalSearchText, #globalSearchInput', function () {
    if ($(this).val() == '') {
        var recent = recentSearch[0];
        if (typeof recent !== 'undefined' && recent.length > 0) {
            let studentHtml =
                "<ul class='max-h-80 scroll-py-2 overflow-y-auto py-2 text-sm text-gray-800 space-y-1' id='students'>";
            let taskHtml =
                "<ul class='max-h-80 scroll-py-2 overflow-y-auto py-2 text-sm text-gray-800 space-y-1' id='tasks'>";
            let allHtml =
                "<ul class='max-h-80 scroll-py-2 overflow-y-auto py-2 text-sm text-gray-800 space-y-1' id='all'><h4 class='text-lg searchtext my-4'>Recent Searches:</h4>";
            let documentHtml =
                "<ul class='max-h-80 scroll-py-2 overflow-y-auto py-2 text-sm text-gray-800 space-y-1' id='document'>";
            let limit = recent.length - 10 > 0 ? 10 : 0;
            let start = recent.length - 1 >= 0 ? recent.length - 1 : 0;

            for (var i = start; i >= limit; i--) {
                if (recent[i].student_id != 0 && recent[i].first_name != null) {
                    let sname = recent[i].first_name.charAt(0) + recent[i].family_name.charAt(0);
                    let genrate_id = recent[i].genrate_id ? recent[i].genrate_id : 'N/A';
                    studentHtml +=
                        "<li data-type='student' data-first=" +
                        recent[i].first_name +
                        ' data-family=' +
                        recent[i].family_name +
                        ' data-genrate=' +
                        genrate_id +
                        ' data-studentid=' +
                        recent[i].student_id +
                        " class='selectedSearch hover:bg-gray-100 cursor-pointer select-none px-4 py-2' role='option' tabindex='-1'><div class='flex items-center space-x-2'><div class='autocomplate-student-header student-profile-pic h-6 w-6 rounded-full bg-blue-500'><span class='text-xs'>" +
                        sname.toUpperCase() +
                        "</span></div>&nbsp;<span class='k-state-default'><h3 class='text-sm autocomplateremove-margin'>" +
                        recent[i].first_name +
                        ' ' +
                        recent[i].family_name +
                        "</h3><span class='text-xs text-gray-400 leading-none'>" +
                        recent[i].genrate_id +
                        '</span></span></div></li>';
                    allHtml +=
                        "<li data-type='student' data-first=" +
                        recent[i].first_name +
                        ' data-family=' +
                        recent[i].family_name +
                        ' data-genrate=' +
                        genrate_id +
                        ' data-studentid=' +
                        recent[i].student_id +
                        " class='selectedSearch hover:bg-gray-100 cursor-pointer select-none px-4 py-2' role='option' tabindex='-1'><div class='flex items-center space-x-2'><div class='autocomplate-student-header student-profile-pic h-6 w-6 rounded-full bg-blue-500'><span class='text-xs'>" +
                        sname.toUpperCase() +
                        "</span></div>&nbsp;<span class='k-state-default'><h3 class='text-sm autocomplateremove-margin'>" +
                        recent[i].first_name +
                        ' ' +
                        recent[i].family_name +
                        "</h3><span class='text-xs text-gray-400 leading-none'>" +
                        recent[i].genrate_id +
                        '</span></span></div></li>';
                }

                if (recent[i].task_id != null) {
                    taskHtml +=
                        "<li data-type='task' data-id=" +
                        recent[i].task_id +
                        " class='selectedSearch hover:bg-gray-100 cursor-pointer select-none px-4 py-2 flex' role='option' tabindex='-1'><div class='flex items-center space-x-2'><div class='flex'><svg class='' width='20' height='20' viewBox='0 0 20 20' fill='none' xmlns='http://www.w3.org/2000/svg'><path d='M10 0C15.5228 0 20 4.47715 20 10C20 15.5228 15.5228 20 10 20C4.47715 20 0 15.5228 0 10C0 4.47715 4.47715 0 10 0ZM10 1.5C5.30558 1.5 1.5 5.30558 1.5 10C1.5 14.6944 5.30558 18.5 10 18.5C14.6944 18.5 18.5 14.6944 18.5 10C18.5 5.30558 14.6944 1.5 10 1.5ZM8.75 11.4393L13.2197 6.96967C13.5126 6.67678 13.9874 6.67678 14.2803 6.96967C14.5466 7.23594 14.5708 7.6526 14.3529 7.94621L14.2803 8.03033L9.28033 13.0303C9.01406 13.2966 8.5974 13.3208 8.30379 13.1029L8.21967 13.0303L5.71967 10.5303C5.42678 10.2374 5.42678 9.76256 5.71967 9.46967C5.98594 9.2034 6.4026 9.1792 6.69621 9.39705L6.78033 9.46967L8.75 11.4393L13.2197 6.96967L8.75 11.4393Z' fill='#9CA3AF'/></svg></div><span class='text-xs leaading-4 text-gray-400'>#" +
                        recent[i].id +
                        "</span><span class='text-sm leaading-5 text-gray-700'>" +
                        recent[i].title +
                        '</span></div></li>';
                    allHtml +=
                        "<li data-type='task' data-id=" +
                        recent[i].task_id +
                        " class='selectedSearch hover:bg-gray-100 cursor-pointer select-none px-4 py-2 flex' role='option' tabindex='-1'><div class='flex items-center space-x-2'><div class='flex'><svg class='' width='20' height='20' viewBox='0 0 20 20' fill='none' xmlns='http://www.w3.org/2000/svg'><path d='M10 0C15.5228 0 20 4.47715 20 10C20 15.5228 15.5228 20 10 20C4.47715 20 0 15.5228 0 10C0 4.47715 4.47715 0 10 0ZM10 1.5C5.30558 1.5 1.5 5.30558 1.5 10C1.5 14.6944 5.30558 18.5 10 18.5C14.6944 18.5 18.5 14.6944 18.5 10C18.5 5.30558 14.6944 1.5 10 1.5ZM8.75 11.4393L13.2197 6.96967C13.5126 6.67678 13.9874 6.67678 14.2803 6.96967C14.5466 7.23594 14.5708 7.6526 14.3529 7.94621L14.2803 8.03033L9.28033 13.0303C9.01406 13.2966 8.5974 13.3208 8.30379 13.1029L8.21967 13.0303L5.71967 10.5303C5.42678 10.2374 5.42678 9.76256 5.71967 9.46967C5.98594 9.2034 6.4026 9.1792 6.69621 9.39705L6.78033 9.46967L8.75 11.4393L13.2197 6.96967L8.75 11.4393Z' fill='#9CA3AF'/></svg></div><span class='text-xs leaading-4 text-gray-400'>#" +
                        recent[i].id +
                        "</span><span class='text-sm leaading-5 text-gray-700'>" +
                        recent[i].title +
                        '</span></div></li>';
                }

                if (recent[i].student_id != 0 && recent[i].document_name != null) {
                    documentHtml +=
                        "<li data-type='document' data-studentid=" +
                        recent[i].student_id +
                        ' data-parentid=' +
                        recent[i].parent_id +
                        " class='selectedSearch hover:bg-gray-100 cursor-pointer select-none px-4 py-2 flex' role='option' tabindex='-1'><svg class='mr-3' width='16' height='21' viewBox='0 0 16 21' fill='none' xmlns='http://www.w3.org/2000/svg'><path opacity='0.64' d='M15.2612 5.39858L10.868 1.00492C10.7365 0.874205 10.5588 0.800596 10.3733 0.800049H1.2332C0.84727 0.800049 0.533203 1.11412 0.533203 1.50005V19.7001C0.533203 20.086 0.84727 20.4001 1.2332 20.4001H14.7665C15.1525 20.4001 15.4665 20.086 15.4665 19.7001V5.89325C15.4665 5.70658 15.3937 5.53065 15.2612 5.39858ZM10.7999 1.59665L14.6699 5.46672H11.0332C10.9714 5.46659 10.9121 5.44197 10.8683 5.39824C10.8246 5.35451 10.8 5.29523 10.7999 5.23338V1.59665ZM14.7665 19.9334H1.2332C1.17136 19.9333 1.11208 19.9086 1.06835 19.8649C1.02462 19.8212 0.999993 19.7619 0.99987 19.7001V1.50005C0.99987 1.37172 1.10487 1.26672 1.2332 1.26672H10.3332V5.23338C10.3332 5.61932 10.6473 5.93338 11.0332 5.93338H14.9999V19.7001C14.9999 19.8284 14.8949 19.9334 14.7665 19.9334Z' fill='#605E5C'/></svg><span class='text-sm leading-5 text-gray-700'>" +
                        recent[i].document_name +
                        '</span></li>';
                    allHtml +=
                        "<li data-type='document' data-studentid=" +
                        recent[i].student_id +
                        ' data-parentid=' +
                        recent[i].parent_id +
                        " class='selectedSearch hover:bg-gray-100 cursor-pointer select-none px-4 py-2 flex' role='option' tabindex='-1'><svg class='mr-3' width='16' height='21' viewBox='0 0 16 21' fill='none' xmlns='http://www.w3.org/2000/svg'><path opacity='0.64' d='M15.2612 5.39858L10.868 1.00492C10.7365 0.874205 10.5588 0.800596 10.3733 0.800049H1.2332C0.84727 0.800049 0.533203 1.11412 0.533203 1.50005V19.7001C0.533203 20.086 0.84727 20.4001 1.2332 20.4001H14.7665C15.1525 20.4001 15.4665 20.086 15.4665 19.7001V5.89325C15.4665 5.70658 15.3937 5.53065 15.2612 5.39858ZM10.7999 1.59665L14.6699 5.46672H11.0332C10.9714 5.46659 10.9121 5.44197 10.8683 5.39824C10.8246 5.35451 10.8 5.29523 10.7999 5.23338V1.59665ZM14.7665 19.9334H1.2332C1.17136 19.9333 1.11208 19.9086 1.06835 19.8649C1.02462 19.8212 0.999993 19.7619 0.99987 19.7001V1.50005C0.99987 1.37172 1.10487 1.26672 1.2332 1.26672H10.3332V5.23338C10.3332 5.61932 10.6473 5.93338 11.0332 5.93338H14.9999V19.7001C14.9999 19.8284 14.8949 19.9334 14.7665 19.9334Z' fill='#605E5C'/></svg><span class='text-sm leading-5 text-gray-700'>" +
                        recent[i].document_name +
                        '</span></li>';
                }
            }

            studentHtml += '</ul>';
            documentHtml += '</ul>';
            taskHtml += '</ul>';
            allHtml += '</ul>';

            $('#searchallList').html(allHtml);
            $('#searchstudentsList').html(studentHtml);
            $('#searchtasksList').html(taskHtml);
            $('#searchdocumentList').html(documentHtml);
        } else {
            $('#searchallList').html('<p class="mt-2">No Recent Search</p>');
            $('#searchstudentsList').html('<p class="mt-2">No Recent Search</p>');
            $('#searchtasksList').html('<p class="mt-2">No Recent Search</p>');
            $('#searchdocumentList').html('<p class="mt-2">No Recent Search</p>');
        }
    }
});

$(document).on('input', '#globalSearchText, #globalSearchInput', function () {
    var txt = $(this).val();

    var type = $.trim($('#searchTabStrip').data('kendoTabStrip').select()[0].innerText);

    let finalArray = [];
    // finalArray = globalSearch[0].filter((item) =>
    //     item.search_string.toLowerCase().includes(txt.toLowerCase()),
    // );
    const searchWords = txt.toLowerCase().split(' ');
    if (globalSearch[0] && globalSearch[0].lenght > 0) {
        finalArray = globalSearch[0].filter((item) =>
            searchWords.every((word) => item.search_string.toLowerCase().includes(word))
        );
    } else {
        finalArray = [];
    }

    if (finalArray.length > 0 && txt != '') {
        let studentHtml =
            "<ul class='max-h-80 scroll-py-2 overflow-y-auto py-2 text-sm text-gray-800 space-y-1' id='students'>";
        let taskHtml =
            "<ul class='max-h-80 scroll-py-2 overflow-y-auto py-2 text-sm text-gray-800 space-y-1' id='tasks'>";
        let allHtml =
            "<ul class='max-h-80 scroll-py-2 overflow-y-auto py-2 text-sm text-gray-800 space-y-1' id='all'>";
        let documentHtml =
            "<ul class='max-h-80 scroll-py-2 overflow-y-auto py-2 text-sm text-gray-800 space-y-1' id='document'>";

        let student = 0;
        let task = 0;
        let document = 0;

        let count = finalArray.length > 20 ? 20 : finalArray.length;

        for (var i = 0; i < count; i++) {
            let genrate_id = finalArray[i].generated_stud_id
                ? finalArray[i].generated_stud_id
                : 'N/A';
            if (type == 'Students' || type == 'All') {
                if (
                    typeof finalArray[i].title === 'undefined' &&
                    typeof finalArray[i].type === 'undefined'
                ) {
                    let sname =
                        finalArray[i].first_name.charAt(0) + finalArray[i].family_name.charAt(0);

                    studentHtml +=
                        "<li data-type='student' data-first=" +
                        finalArray[i].first_name +
                        ' data-family=' +
                        finalArray[i].family_name +
                        ' data-genrate=' +
                        genrate_id +
                        ' data-studentid=' +
                        finalArray[i].id +
                        " class='selectedSearch hover:bg-gray-100 cursor-pointer select-none px-4 py-2' role='option' tabindex='-1'><div class='flex items-center space-x-2'><div class='autocomplate-student-header student-profile-pic h-6 w-6 rounded-full bg-blue-500'><span class='text-xs'>" +
                        sname.toUpperCase() +
                        "</span></div>&nbsp;<span class='k-state-default'><h3 class='text-sm text-gray-700 leading-5 autocomplateremove-margin'>" +
                        finalArray[i].first_name +
                        ' ' +
                        finalArray[i].family_name +
                        "</h3><span class='text-xs text-gray-400 leading-none'>" +
                        genrate_id +
                        '</span></span></div></li>';
                }
            }

            if (type == 'Tasks' || type == 'All') {
                if (typeof finalArray[i].title !== 'undefined') {
                    taskHtml +=
                        "<li data-type='task' data-id=" +
                        finalArray[i].id +
                        " class='selectedSearch hover:bg-gray-100 cursor-pointer select-none px-4 py-2 flex' role='option' tabindex='-1'><div class='flex items-center space-x-2'><div class='flex'><svg width='20' height='20' viewBox='0 0 20 20' fill='none' xmlns='http://www.w3.org/2000/svg'><path d='M10 0C15.5228 0 20 4.47715 20 10C20 15.5228 15.5228 20 10 20C4.47715 20 0 15.5228 0 10C0 4.47715 4.47715 0 10 0ZM10 1.5C5.30558 1.5 1.5 5.30558 1.5 10C1.5 14.6944 5.30558 18.5 10 18.5C14.6944 18.5 18.5 14.6944 18.5 10C18.5 5.30558 14.6944 1.5 10 1.5ZM8.75 11.4393L13.2197 6.96967C13.5126 6.67678 13.9874 6.67678 14.2803 6.96967C14.5466 7.23594 14.5708 7.6526 14.3529 7.94621L14.2803 8.03033L9.28033 13.0303C9.01406 13.2966 8.5974 13.3208 8.30379 13.1029L8.21967 13.0303L5.71967 10.5303C5.42678 10.2374 5.42678 9.76256 5.71967 9.46967C5.98594 9.2034 6.4026 9.1792 6.69621 9.39705L6.78033 9.46967L8.75 11.4393L13.2197 6.96967L8.75 11.4393Z' fill='#9CA3AF'/></svg></div><span class='text-xs leaading-4 text-gray-400'>#" +
                        finalArray[i].id +
                        "</span><span class='text-sm leaading-5 text-gray-700'>" +
                        finalArray[i].title +
                        '</span></div></li>';
                }
            }

            if (type == 'Document' || type == 'All') {
                if (typeof finalArray[i].type !== 'undefined') {
                    documentHtml +=
                        "<li data-type='document' data-studentid=" +
                        finalArray[i].student_id +
                        ' data-parentid=' +
                        finalArray[i].parent_id +
                        " class='selectedSearch hover:bg-gray-100 cursor-pointer select-none px-4 py-2 flex' role='option' tabindex='-1'><svg class='mr-3' width='16' height='21' viewBox='0 0 16 21' fill='none' xmlns='http://www.w3.org/2000/svg'><path opacity='0.64' d='M15.2612 5.39858L10.868 1.00492C10.7365 0.874205 10.5588 0.800596 10.3733 0.800049H1.2332C0.84727 0.800049 0.533203 1.11412 0.533203 1.50005V19.7001C0.533203 20.086 0.84727 20.4001 1.2332 20.4001H14.7665C15.1525 20.4001 15.4665 20.086 15.4665 19.7001V5.89325C15.4665 5.70658 15.3937 5.53065 15.2612 5.39858ZM10.7999 1.59665L14.6699 5.46672H11.0332C10.9714 5.46659 10.9121 5.44197 10.8683 5.39824C10.8246 5.35451 10.8 5.29523 10.7999 5.23338V1.59665ZM14.7665 19.9334H1.2332C1.17136 19.9333 1.11208 19.9086 1.06835 19.8649C1.02462 19.8212 0.999993 19.7619 0.99987 19.7001V1.50005C0.99987 1.37172 1.10487 1.26672 1.2332 1.26672H10.3332V5.23338C10.3332 5.61932 10.6473 5.93338 11.0332 5.93338H14.9999V19.7001C14.9999 19.8284 14.8949 19.9334 14.7665 19.9334Z' fill='#605E5C'/></svg><span class='text-sm leading-5 text-gray-700'>" +
                        finalArray[i].search_string +
                        '</span></li>';
                }
            }

            if (type == 'All') {
                if (typeof finalArray[i].title !== 'undefined') {
                    if (task == 0) {
                        allHtml += '<h4 class="text-lg searchtext my-4">Tasks</h4><hr>';
                    }
                    allHtml +=
                        "<li data-type='task' data-id=" +
                        finalArray[i].id +
                        " class='selectedSearch hover:bg-gray-100 cursor-pointer select-none px-4 py-2 flex' role='option' tabindex='-1'><div class='flex items-center space-x-2'><div class='flex'><svg width='20' height='20' viewBox='0 0 20 20' fill='none' xmlns='http://www.w3.org/2000/svg'><path d='M10 0C15.5228 0 20 4.47715 20 10C20 15.5228 15.5228 20 10 20C4.47715 20 0 15.5228 0 10C0 4.47715 4.47715 0 10 0ZM10 1.5C5.30558 1.5 1.5 5.30558 1.5 10C1.5 14.6944 5.30558 18.5 10 18.5C14.6944 18.5 18.5 14.6944 18.5 10C18.5 5.30558 14.6944 1.5 10 1.5ZM8.75 11.4393L13.2197 6.96967C13.5126 6.67678 13.9874 6.67678 14.2803 6.96967C14.5466 7.23594 14.5708 7.6526 14.3529 7.94621L14.2803 8.03033L9.28033 13.0303C9.01406 13.2966 8.5974 13.3208 8.30379 13.1029L8.21967 13.0303L5.71967 10.5303C5.42678 10.2374 5.42678 9.76256 5.71967 9.46967C5.98594 9.2034 6.4026 9.1792 6.69621 9.39705L6.78033 9.46967L8.75 11.4393L13.2197 6.96967L8.75 11.4393Z' fill='#9CA3AF'/></svg></div><span class='text-xs leaading-4 text-gray-400'>#" +
                        finalArray[i].id +
                        "</span><span class='text-sm leaading-5 text-gray-700'>" +
                        finalArray[i].title +
                        '</span></div></li>';
                    task++;
                } else if (
                    typeof finalArray[i].type !== 'undefined' &&
                    typeof finalArray[i].title === 'undefined'
                ) {
                    if (document == 0) {
                        allHtml += '<h4 class="text-lg searchtext my-4">Documents</h4><hr>';
                    }
                    allHtml +=
                        "<li data-type='document' data-studentid=" +
                        finalArray[i].student_id +
                        ' data-parentid=' +
                        finalArray[i].parent_id +
                        " class='selectedSearch hover:bg-gray-100 cursor-pointer select-none px-4 py-2 flex' role='option' tabindex='-1'><svg class='mr-3' width='16' height='21' viewBox='0 0 16 21' fill='none' xmlns='http://www.w3.org/2000/svg'><path opacity='0.64' d='M15.2612 5.39858L10.868 1.00492C10.7365 0.874205 10.5588 0.800596 10.3733 0.800049H1.2332C0.84727 0.800049 0.533203 1.11412 0.533203 1.50005V19.7001C0.533203 20.086 0.84727 20.4001 1.2332 20.4001H14.7665C15.1525 20.4001 15.4665 20.086 15.4665 19.7001V5.89325C15.4665 5.70658 15.3937 5.53065 15.2612 5.39858ZM10.7999 1.59665L14.6699 5.46672H11.0332C10.9714 5.46659 10.9121 5.44197 10.8683 5.39824C10.8246 5.35451 10.8 5.29523 10.7999 5.23338V1.59665ZM14.7665 19.9334H1.2332C1.17136 19.9333 1.11208 19.9086 1.06835 19.8649C1.02462 19.8212 0.999993 19.7619 0.99987 19.7001V1.50005C0.99987 1.37172 1.10487 1.26672 1.2332 1.26672H10.3332V5.23338C10.3332 5.61932 10.6473 5.93338 11.0332 5.93338H14.9999V19.7001C14.9999 19.8284 14.8949 19.9334 14.7665 19.9334Z' fill='#605E5C'/></svg><span class='text-sm leading-5 text-gray-700'>" +
                        finalArray[i].search_string +
                        '</span></li>';
                    document++;
                } else if (
                    typeof finalArray[i].title === 'undefined' &&
                    typeof finalArray[i].type === 'undefined'
                ) {
                    if (student == 0) {
                        allHtml += '<h4 class="text-lg searchtext my-4">Students</h4><hr>';
                    }
                    let sname =
                        finalArray[i].first_name.charAt(0) + finalArray[i].family_name.charAt(0);
                    allHtml +=
                        "<li data-type='student' data-first=" +
                        finalArray[i].first_name +
                        ' data-family=' +
                        finalArray[i].family_name +
                        ' data-genrate=' +
                        genrate_id +
                        ' data-studentid=' +
                        finalArray[i].id +
                        " class='selectedSearch hover:bg-gray-100 cursor-pointer select-none px-4 py-2' role='option' tabindex='-1'><div class='flex items-center space-x-2'><div class='autocomplate-student-header student-profile-pic h-6 w-6 rounded-full bg-blue-500'><span class='text-xs'>" +
                        sname.toUpperCase() +
                        "</span></div>&nbsp;<span class='k-state-default'><h3 class='text-sm text-gray-700 leading-5 autocomplateremove-margin'>" +
                        finalArray[i].first_name +
                        ' ' +
                        finalArray[i].family_name +
                        "</h3><span class='text-xs leading-none text-gray-400'>" +
                        genrate_id +
                        '</span></span></div></li>';
                    student++;
                }
            }
        }

        studentHtml += '</ul>';
        documentHtml += '</ul>';
        taskHtml += '</ul>';
        allHtml += '</ul>';

        $('#searchallList').html(allHtml);
        $('#searchstudentsList').html(studentHtml);
        $('#searchtasksList').html(taskHtml);
        $('#searchdocumentList').html(documentHtml);
    } else {
        if (txt == '') {
            $('#globalSearchText').trigger('focus');
        } else {
            $('#searchallList').html('Not Found');
            $('#searchstudentsList').html('Not Found');
            $('#searchtasksList').html('Not Found');
            $('#searchdocumentList').html('Not Found');
        }
    }
});

$(document).on('click', '.selectedSearch', function () {
    let first = typeof $(this).data('first') === 'undefined' ? null : $(this).data('first');
    let family = typeof $(this).data('family') === 'undefined' ? null : $(this).data('family');
    let studentId =
        typeof $(this).data('studentid') === 'undefined' ? null : $(this).data('studentid');
    let parentId =
        typeof $(this).data('parentid') === 'undefined' ? null : $(this).data('parentid');
    let taskid = typeof $(this).data('id') === 'undefined' ? null : $(this).data('id');
    let genrateId = typeof $(this).data('genrate') === 'undefined' ? null : $(this).data('genrate');
    let document =
        $(this).data('type') == 'document' ? $(this).find('.text-gray-700').html() : null;
    let title = $(this).data('type') == 'task' ? $(this).find('.text-gray-700').html() : null;
    let type = $(this).data('type');
    let dataArr = {
        first_name: first,
        family_name: family,
        student_id: studentId,
        document_name: document,
        parent_id: parentId,
        title: title,
        task_id: taskid,
        genrate_id: genrateId,
        type: type,
    };

    $.ajax({
        type: 'POST',
        url: site_url + 'api/add-recent-search',
        data: dataArr,
        headers: { 'X-CSRF-TOKEN': $('input[name="_token"]').val() },
        success: function (output) {
            // if(output == 'Success'){
            if (type == 'student') {
                var redirectUrl = site_url + 'student-profile/' + studentId;
            } else if (type == 'task') {
                var redirectUrl = site_url + 'task-detail/' + taskid;
            } else if (type == 'document') {
                var redirectUrl =
                    site_url + 'student-document-upload/' + studentId + '/' + genrateId;
            }
            // }
            window.open(redirectUrl, '_self');
        },
    });
});

// $(document).mouseup(function(e){
//     var globalSearchModal = $("#globalSearchModal");
//     var serachBox = $(".serachBox");
//     if(!globalSearchModal.is(e.target) && globalSearchModal.has(e.target).length === 0 && !serachBox.is(e.target) && serachBox.has(e.target).length === 0){
//         // $(document).find("#globalSearchModal").attr('style','display:none');
//         // $(document).find('#globalSearchModal').getKendoWindow().close();
//         // $(document).find('#searchallList').html('');
//         $(document).find('#searchstudentsList').html('');
//         $(document).find('#searchtasksList').html('');
//         $(document).find('#searchdocumentList').html('');
//         $(document).find('#globalSearchText').val('');
//         // $(document).find("#globalSearchModal").data("kendoWindow").close();
//     }
// });

function globalSearchFn() {
    setTimeout(() => {
        $(document).find('#global-search-input-lw').focus();
    }, 500);
    $.ajax({
        type: 'POST',
        url: site_url + 'api/global-search',
        // data: data,
        headers: { 'X-CSRF-TOKEN': $('input[name="_token"]').val() },
        success: function (output) {
            globalSearch.push(output.data);
        },
    });

    $.ajax({
        type: 'POST',
        url: site_url + 'api/get-recent-search',
        // data: data,
        headers: { 'X-CSRF-TOKEN': $('input[name="_token"]').val() },
        success: function (output) {
            recentSearch.push(output.data);
            $(document).find('#globalSearchText').focus();
        },
    });
}

function searchLoading() {
    kendo.ui.progress.messages = {
        loading:
            '<div class="vue-simple-spinner animate-spin" style="position:absolute;top:50%;left:50%;transfrom:translate(-50%,-50%); margin: 0px auto; border-radius: 100%; border-color: rgb(50, 157, 243) rgb(238, 238, 238) rgb(238, 238, 238); border-style: solid; border-width: 3px; border-image: none 100% / 1 / 0 stretch; width: 50px; height: 50px;"></div>',
    };
    kendo.ui.progress($('#searchallList'), true);
    setTimeout(function () {
        kendo.ui.progress($('#searchallList'), false);
    }, 1000);
}

function reloadGrid(gridID) {
    $(gridID).data('kendoGrid').refresh();
    $(gridID).data('kendoGrid').dataSource.read();
}

function refreshGrid(gridID, dataArr, isClearFilter = false) {
    console.log('gridId', gridID);
    let grid = $(gridID).data('kendoGrid');
    console.log('grid', grid);
    if (grid) {
        grid.dataSource.transport.options.read.data = dataArr;
        if (isClearFilter) {
            grid.dataSource.filter(null);
        } else {
            grid.dataSource.read();
        }
    }
}

function refreshScheduler(schedulerID, dataArr) {
    let scheduler = $(schedulerID).data('kendoScheduler');
    scheduler.dataSource.transport.options.read.data = dataArr;
    scheduler.dataSource.read();
}

function getDropdownDataSource(apiUrl, postArr = []) {
    return {
        schema: { data: 'data' },
        transport: getTransportReadOnly(apiUrl, postArr),
    };
}

function getTransportReadOnly(apiUrl, postArr = []) {
    return {
        read: {
            url: site_url + 'api/' + apiUrl,
            dataType: 'json',
            type: 'POST',
            data: postArr,
        },
    };
}

function customDataSourceForInlineV2(
    apiUrl,
    fields,
    dateField = [],
    extraField = [],
    update_url = '',
    destroy_url = '',
    manageMoodleColumn = false,
    gridIdForMoodleColumn = ''
) {
    return {
        type: 'json',
        transport: {
            read: {
                url: site_url + apiUrl,
                dataType: 'json',
                type: 'POST',
                data: dateField,
            },
            update: {
                url: site_url + update_url,
                dataType: 'json',
                type: 'POST',
            },
            destroy: {
                url: site_url + destroy_url,
                dataType: 'json',
                type: 'POST',
            },
            parameterMap: function (data, operation) {
                if (operation == 'read') {
                    if (data.filter && extraField.length > 0) {
                        $.each(data.filter.filters, function (index, value) {
                            if ($.inArray(value.field, extraField) != -1) {
                                data.filter.filters[index].value = kendo.toString(
                                    data.filter.filters[index].value,
                                    'yyyy-MM-dd'
                                );
                            }
                        });
                    }
                    return data;
                }
                if (operation !== 'read' && data.models) {
                    if (data.models[0].start_date) {
                        data.models[0].start_date = new Date(
                            data.models[0].start_date.getTime() -
                                data.models[0].start_date.getTimezoneOffset() * 60000
                        );
                    }

                    if (data.models[0].finish_date) {
                        data.models[0].finish_date = new Date(
                            data.models[0].finish_date.getTime() -
                                data.models[0].finish_date.getTimezoneOffset() * 60000
                        );
                    }

                    if (data.models[0].last_assessment_approved_date) {
                        data.models[0].last_assessment_approved_date = new Date(
                            data.models[0].last_assessment_approved_date.getTime() -
                                data.models[0].last_assessment_approved_date.getTimezoneOffset() *
                                    60000
                        );
                    }

                    console.log(data.models);
                    return { models: kendo.stringify(data.models) };
                }
            },
        },
        batch: true,
        schema: defaultSchema(fields),
        pageSize: 20,
        serverPaging: true,
        serverFiltering: true,
        serverSorting: true,
        requestEnd: function (e) {
            if (e.type === 'read' && e.response && manageMoodleColumn) {
                //isMoodleConnectVal = e.response.data.isMoodleConnect;
                window.isMoodleConnected = e.response.data.isMoodleConnect;
                manageMoodleColumnsForGrid(gridIdForMoodleColumn, e.response.data.isMoodleConnect);
            }
            if ((e.type === 'update' || e.type === 'destroy') && e.response) {
                notificationDisplay(e.response.message, '', e.response.status);
                e.sender.read();
            }
        },
    };
}

function manageMoodleColumnsForGrid(gridId, isMoodleConnectVal) {
    let currentGrid = $(gridId).data('kendoGrid');

    // if (isMoodleConnectVal == 1) {
    //     currentGrid.showColumn("moodle_status");
    //     currentGrid.showColumn("moodle_synced_at");
    // } else {
    //     currentGrid.hideColumn("moodle_status");
    //     currentGrid.hideColumn("moodle_synced_at");
    // }
    const columnsToToggle = ['moodle_status', 'moodle_synced_at'];

    columnsToToggle.forEach((column) => {
        try {
            const th = currentGrid.thead.find("th[data-field='" + column + "']");
            if (th.length) {
                if (isMoodleConnectVal == 1) {
                    currentGrid.showColumn(column);
                } else {
                    currentGrid.hideColumn(column);
                }
            }
        } catch (e) {
            console.warn(`Column '${column}' not found or failed to toggle.`, e);
        }
    });
}

function customDataSourceForInline(
    apiUrl,
    fields,
    dateField = [],
    extraField = [],
    update_url = '',
    destroy_url = '',
    callbackFunction = null,
    manageMoodleColumn = false,
    gridIdForMoodleColumn = ''
) {
    return {
        type: 'json',
        transport: {
            read: {
                url: site_url + apiUrl,
                dataType: 'json',
                type: 'POST',
                data: dateField,
            },
            update: {
                url: site_url + update_url,
                dataType: 'json',
                type: 'POST',
            },
            destroy: {
                url: site_url + destroy_url,
                dataType: 'json',
                type: 'POST',
            },
            parameterMap: function (data, operation) {
                if (operation == 'read') {
                    if (data.filter && extraField.length > 0) {
                        $.each(data.filter.filters, function (index, value) {
                            if ($.inArray(value.field, extraField) != -1) {
                                data.filter.filters[index].value = kendo.toString(
                                    data.filter.filters[index].value,
                                    'yyyy-MM-dd'
                                );
                            }
                        });
                    }
                    return data;
                }
                if (operation !== 'read' && data.models) {
                    if (data.models[0].due_date) {
                        data.models[0].due_date = new Date(
                            data.models[0].due_date.getTime() -
                                data.models[0].due_date.getTimezoneOffset() * 60000
                        );
                    }

                    return { models: kendo.stringify(data.models) };
                }
            },
        },
        batch: true,
        schema: defaultSchema(fields),
        pageSize: 20,
        serverPaging: true,
        serverFiltering: true,
        serverSorting: true,
        requestEnd: function (e) {
            if (e.type === 'read' && e.response && manageMoodleColumn) {
                window.isMoodleConnected = e.response.data.isMoodleConnect;
                let $gridElement = $(e.target).closest("[data-role='grid']");

                //TODO:: Hide/show sync icon when moodle connect or dis-connect
                /*let currentGrid = $gridElement.data("kendoGrid");
                if (e.response.data.isMoodleConnect == 1) {
                    currentGrid.showColumn("moodle_status");
                    currentGrid.showColumn("moodle_synced_at");
                } else {
                    currentGrid.hideColumn("moodle_status");
                    currentGrid.hideColumn("moodle_synced_at");
                }*/
                //let insideGridId = "#" + $gridElement.attr("id");
                //manageMoodleColumnsForGrid($gridElement, e.response.data.isMoodleConnect);
                //$(document).find(".assessmentSyncToMoodleBtn").toggle(!window.isMoodleConnected);
            }

            if ((e.type === 'update' || e.type === 'destroy') && e.response) {
                notificationDisplay(e.response.message, '', e.response.status);
                e.sender.read();

                if (callbackFunction && typeof callbackFunction === 'function') {
                    callbackFunction();
                }
            }
        },
    };
}

function closeKendoWindow(modalId) {
    $(document).find(modalId).getKendoWindow().close();
    //window.parent.$(modalId).getKendoWindow().close();
}

function defaultCloseAnimation() {
    return {
        open: {
            effects: 'fade:in',
            duration: 300,
        },
        close: {
            effects: 'fade:out',
            duration: 300,
        },
    };
}

function defaultOpenAnimation() {
    return { open: { effects: 'fade:in' } };
    // return { open: { effects: "" } }
}

function gridExportExcelData(gridId, fileName) {
    var grid = $(gridId).data('kendoGrid');
    grid.table.find('td:last-child, th:last-child').remove();
    grid.bind('excelExport', function (e) {
        e.workbook.fileName = fileName + '.xlsx';
    });
    grid.saveAsExcel();
}

/* Start selected rows are export as XLS */
function gridExportExcelData2(gridId, fileName = 'SelectedRows') {
    let grid = $(gridId).data('kendoGrid');
    let selectedRows = grid.select();
    let selectedData = selectedRows.map((row) => grid.dataItem(row)).toArray();

    if (selectedData.length === 0) {
        notificationDisplay('Please select at least one row.', '', 'error');
        return;
    }

    //let columns = grid.columns;
    //let columns = grid.columns.filter(column => column !== undefined);
    let columns = grid.columns.filter((column) => column.field !== 'action');

    exportToExcel(selectedData, columns, fileName);
}

function setTempGridElementData(selectedData, columnsVal, tempGridElement) {
    let exportDataSource = new kendo.data.DataSource({
        data: selectedData,
        schema: {
            model: {
                id: 'id',
            },
        },
    });

    tempGridElement.kendoGrid({
        dataSource: exportDataSource,
        columns: columnsVal,
        excel: {
            filterable: true,
            allPages: true,
        },
    });
}

function exportToExcel(selectedData, columns, fileName = 'SelectedRows') {
    let tempGridElement = $("<div id='tempGrid'></div>").appendTo('body');

    setTempGridElementData(selectedData, columns, tempGridElement);

    let tempGrid = tempGridElement.data('kendoGrid');
    tempGrid.bind('excelExport', function (e) {
        e.workbook.fileName = fileName + '.xlsx';
    });
    tempGrid.saveAsExcel();

    // Destroy the temporary grid and remove it from the DOM
    tempGrid.destroy();
    tempGridElement.remove();
    //$("#tempGrid").remove();
}

function dateFormatForExport(date, format = 'yyyy-MM-dd') {
    return date !== null ? kendo.toString(kendo.parseDate(date), format) : 'NA';
}
/* End selected rows are export as XLS */

function gridExportExcelDataV2(gridId, fileName) {
    var grid = $(gridId).data('kendoGrid');
    var columns = grid.columns;
    var columnsForExport = columns.filter(function (column, index) {
        return index !== columns.length - 1;
    });
    grid.columns = columnsForExport;
    grid.bind('excelExport', function (e) {
        e.workbook.fileName = fileName + '.xlsx';
    });
    grid.saveAsExcel();
    grid.columns = columns;
}

function ajaxActionV2(
    url,
    method,
    postData = {},
    callback,
    loading = false,
    loadingCallback = null
) {
    kendo.ui.progress($(document.body), loading);
    if (loadingCallback && typeof loadingCallback === 'function') {
        loadingCallback(true);
    }
    $.ajax({
        type: method,
        dataType: 'json',
        url: site_url + url,
        /* headers: { "X-CSRF-TOKEN": $('input[name="_token"]').val() }, */
        data: postData,
        async: true,
        success: function (data) {
            if (loadingCallback && typeof loadingCallback === 'function') {
                loadingCallback(false);
            }
            callback(data);
            kendo.ui.progress($(document.body), false);
        },
        error: function (err) {
            callback(err);
            kendo.ui.progress($(document.body), false);
            if (loadingCallback && typeof loadingCallback === 'function') {
                loadingCallback(false);
            }
        },
    });
}

function setInputField(
    field,
    label,
    editor,
    colSpan = 0,
    isValidate = false,
    id = '',
    attributes = ''
) {
    let fieldArr = {
        field: field,
        label: label,
        editor: editor,
        id: id != '' ? id : field,
        validation: isValidate === true ? { required: true } : isValidate,
    };
    if (colSpan > 0) {
        fieldArr['colSpan'] = colSpan;
    }
    if (attributes != '') {
        fieldArr['attributes'] = attributes;
    }
    /*if(isValidate === true){
        fieldArr.push({ key: "validation", value: { required: true } });
    }else{
        fieldArr.push({ key: "validation", value: {isValidate} });
    }*/

    return fieldArr;
}

function setDropdownEditor(label, data, dataValue = 'Id', dataText = 'Name') {
    return {
        optionLabel: label,
        dataSource: {
            schema: { data: 'data' },
            data: data,
        },
        dataValueField: dataValue,
        dataTextField: dataText,
    };
}

function setGridColumn(
    field,
    title,
    templateVal,
    templateClass = '',
    filterable = true,
    width = '',
    minResizableWidth = 100
) {
    return {
        field: field,
        title: title,
        template: `<div class='flex items-center text-sm leading-5 font-normal text-gray-600 ${templateClass}'>${templateVal}</div>`,
        filterable: filterable,
        width: width,
        minResizableWidth: minResizableWidth,
    };
}

function customAlphabetInput(container, options) {
    let maxLength = options.maxLength ? 'data-maxlength=' + options.maxLength : ''; // if need max-lenth thane pass maxLength
    let placeholder =
        options.attributes && options.attributes.placeholder
            ? options.attributes.placeholder
            : `Enter ${options.label}`;
    let validationFlag =
        typeof options.validation !== 'undefined' ? options.validation.required : false;
    let validationRule = validationFlag ? 'required' : '';
    $(
        `<input type="text" id="${options.id}" placeholder="${placeholder}" ${maxLength} class="alphabet-only" name="${options.field}" title="" ${validationRule} />`
    ).appendTo(container);
}

function customNumberInput(container, options) {
    let maxLength = options.maxLength ? 'data-maxlength=' + options.maxLength : ''; // if need max-lenth thane pass maxLength
    let disabled = options.attributes.disabled ? 'disabled=' + options.attributes.disabled : ''; // if need max-lenth thane pass maxLength

    let placeholder =
        options.attributes && options.attributes.placeholder
            ? options.attributes.placeholder
            : `Enter ${options.label}`;
    let validationFlag =
        typeof options.validation !== 'undefined' ? options.validation.required : false;
    let validationRule = validationFlag ? 'required' : '';
    $(
        `<input type="text" id="${options.id}" placeholder="${placeholder}" ${maxLength} ${disabled} class="number-only !px-3 !h-9 !indent-0" name="${options.field}" title="" ${validationRule} />`
    ).appendTo(container);
}

function customFloatNumberInput(container, options) {
    let maxLength = options.maxLength ? 'data-maxlength=' + options.maxLength : ''; // if need max-lenth thane pass maxLength
    let placeholder =
        options.attributes && options.attributes.placeholder
            ? options.attributes.placeholder
            : `Enter ${options.label}`;
    let validationFlag =
        typeof options.validation !== 'undefined' ? options.validation.required : false;
    let validationRule = validationFlag ? 'required' : '';
    $(
        `<input type="text" id="${options.id}" placeholder="${placeholder}"  ${maxLength} class="float-only ${options.className}" name="${options.field}" title="" ${validationRule} />`
    ).appendTo(container);
}
function capitalizeFirstCharacter(str = '') {
    //return str.charAt(0).toUpperCase() + str.slice(1);
    if (str === null) {
        return str;
    }
    return str
        .toLowerCase()
        .split(' ')
        .map(function (word) {
            return word.charAt(0).toUpperCase() + word.slice(1);
        })
        .join(' ');
}

function defaultSchema(fields = {}) {
    return {
        data: 'data.data',
        total: 'data.total',
        model: {
            id: 'id',
            fields: fields,
        },
    };
}

$('body').on('keypress', '.number-only', function (e) {
    let charCode = e.which ? e.which : event.keyCode;
    if (String.fromCharCode(charCode).match(/[^0-9]/g)) {
        return false;
    }
    var value = $(this).val();
    if (value.length > $(this).attr('data-maxlength')) {
        // If the length of the value exceeds 5 characters, prevent further input
        return false;
    }
});

$('body').on('keypress', '.alphabet-only', function (e) {
    let charCode = e.which ? e.which : event.keyCode;
    // maxLength = e.attr('data-maxlength')
    if (!String.fromCharCode(charCode).match(/[a-zA-Z]/)) {
        return false;
    }
    var value = $(this).val();
    if (value.length > $(this).attr('data-maxlength')) {
        // If the length of the value exceeds 5 characters, prevent further input
        return false;
    }
});

$('body').on('keypress', '.float-only', function (e) {
    let charCode = e.which ? e.which : event.keyCode;
    let inputChar = String.fromCharCode(charCode);

    // Allow numbers (0-9), the decimal point (.), and backspace (8)
    if (
        !(charCode >= 48 && charCode <= 57) && // Check for numbers 0-9
        charCode !== 46 && // Check for the decimal point (.)
        charCode !== 8 // Check for backspace
    ) {
        return false; // Prevent input if not a number, decimal point, or backspace
    }

    // Check if decimal point is already present
    if (charCode === 46 && $(this).val().indexOf('.') !== -1) {
        return false; // Prevent input if decimal point already exists
    }

    // Check for leading zero before decimal point
    if ($(this).val() === '0' && charCode !== 46) {
        $(this).val(inputChar); // Replace '0' with the input character
        return false;
    }

    // Determine the maximum allowed digits after the decimal point
    let maxDecimalPlaces = $(this).hasClass('threeDigitInput') ? 3 : 2;

    // Check for more than one digit after decimal point
    let dotIndex = $(this).val().indexOf('.');
    if (dotIndex !== -1 && $(this).val().length - dotIndex > maxDecimalPlaces) {
        return false; // Prevent input if more than one digit after decimal point
    }
});

$(document).on('click', '.closeAndRefreshGrid', function () {
    let type = $(this).attr('data-type');
    $(`#statusForSend${type}Modal`).data('kendoWindow').close();
    $('#studentFailEmailList').data('kendoGrid');
    {
        reloadGrid('#studentFailEmailList');
    }
});

function enableKendoFields(parentSelector) {
    $(document)
        .find(parentSelector + ' input')
        .prop('disabled', false);
    $(document)
        .find(parentSelector)
        .find("input[data-role='dropdownlist']")
        .each(function () {
            $(this).data('kendoDropDownList').enable(true);
        });
    $(document)
        .find(parentSelector)
        .find("input[data-role='switch']")
        .each(function () {
            $(this).data('kendoSwitch').enable(true);
        });
    $(parentSelector)
        .find("input[data-role='maskedtextbox']")
        .each(function () {
            $(this).data('kendoMaskedTextBox').enable(true);
        });
}

function disableKendoFields(parentSelector) {
    //$(document).find(parentSelector + " input:visible").prop("disabled", true);
    $(document)
        .find(parentSelector + ' input')
        .prop('disabled', true);
    $(document)
        .find(parentSelector + " input[type='hidden']")
        .prop('disabled', false);
    $(document)
        .find(parentSelector)
        .find("input[data-role='dropdownlist']")
        .each(function () {
            $(this).data('kendoDropDownList').enable(false);
        });
    $(document)
        .find(parentSelector)
        .find("input[data-role='switch']")
        .each(function () {
            $(this).data('kendoSwitch').enable(false);
        });
    $(parentSelector)
        .find("input[data-role='maskedtextbox']")
        .each(function () {
            $(this).data('kendoMaskedTextBox').enable(false);
        });
}

let handleScroll = true;
function handleTabClickScroll(selector, contentSelector, barSelector, offsetAdjustment) {
    $(selector).on('click', function (e) {
        e.preventDefault();
        $(barSelector + ' li a.active').removeClass('active');
        $(this).addClass('active');
        handleScroll = false;

        var target = $(this).attr('href');
        var targetElement = $(contentSelector).find(target);

        if (targetElement.length > 0) {
            var targetOffset = targetElement.offset().top;
            var scrollTop = $(contentSelector).scrollTop() + targetOffset - offsetAdjustment;

            $(contentSelector).stop().animate(
                {
                    scrollTop: scrollTop,
                },
                450
            );
        }

        return false;
    });
}

function handleScrollTabChange(selector, contentSelector, barSelector) {
    $(selector).on('mouseenter', function () {
        handleScroll = true; // Enable scroll event

        $(this)
            .scroll(function () {
                if (!handleScroll) {
                    return;
                }

                var scrollDistance = $(window).scrollTop();
                $(contentSelector).each(function (i) {
                    if ($(this).position().top <= scrollDistance) {
                        $(barSelector + ' li a.active').removeClass('active');
                        $(barSelector + ' li a')
                            .eq(i)
                            .addClass('active');
                    }
                });
            })
            .scroll();
    });
}

function initializeTooltips() {
    $('.glob-tooltip').each(function () {
        // var content = $(this).data("tooltip");
        var content = $(this).attr('title');

        var position = $(this).data('tooltip-position');
        var wrappedContent = $('<div>')
            .addClass('w-full h-full bg-gray-800 color-white p-1.5 text-xs rounded')
            .html(content);

        $(this).kendoTooltip({
            content: wrappedContent,
            position: position || 'bottom',
            autoHide: true,
            show: function (e) {
                // Remove the arrow element from the tooltip
                // e.sender.popup.element.find(".k-callout").remove();
            },
        });
    });
}

function showDivsWithDelay() {
    var divsWithFont = $('.tw-button-group');
    $('.skeleton-btn-group').hide();
    if (divsWithFont.length > 0) {
        divsWithFont.each(function () {
            $(this).removeClass('hidden').addClass('flex');
        });
    }
}

$(document).ready(function () {
    initializeTooltips();
    // Disable default popup animation
    kendo.ui.Popup.fn.options.animation.open.duration = 0;
    kendo.ui.Popup.fn.options.animation.close.duration = 0;
});

$('body').on('focus', '.activeExpandBtn', function () {
    $(this).removeClass('w-40');
    $(this).attr('style', 'width: 300px !important');
});

$('body').on('blur', '.activeExpandBtn', function () {
    $(this).removeAttr('style', 'width: 300px !important');
    $(this).addClass('w-40');
});

var globalSearchInput = $('#globalSearchInput');
var searchResults = $('#globalSearchResult');
var originalPlaceholder = globalSearchInput.attr('placeholder');
var newPlaceholder = 'Search Student, Document or Task';

$(document).on('focus', '#globalSearchInput', function () {
    $(this).removeClass('w-56');
    $(this).css('width', '600px');
    //   openGlobalSearchWindow();
    globalSearchInput.attr('placeholder', newPlaceholder);
    setTimeout(() => {
        openGlobalSearchResult();
    }, 500);
});

$(document).on('mousedown', function (event) {
    var target = $(event.target);
    if (
        !target.closest('#globalSearchInput').length &&
        !target.closest('#globalSearchResult').length
    ) {
        hideSearchResults();
    }
});

function hideSearchResults() {
    searchResults.fadeOut('fast', function () {
        $(this).slideUp('fast');
    });
    globalSearchInput.css('width', '');
    globalSearchInput.addClass('w-56');
    globalSearchInput.attr('placeholder', originalPlaceholder);
}

function openGlobalSearchResult() {
    let searchResultPanel = $(document).find('#globalSearchResult');
    searchResultPanel.slideDown('fast');
    searchLoading();
    globalSearchFn();
}

$(document).on('keydown', function (event) {
    if (event.ctrlKey && event.key === 'j') {
        event.preventDefault();
        globalSearchInput.focus();
    }
});
// setTimeout(function () {
//     $(".student-profile-sidebar-skeleton").hide();
//     $(".student-profile-left-sidebar").show();
// }, 1000);

function addModalClassToWindows(windowIDs, className = 'k-modal-window') {
    windowIDs.forEach(function (windowID) {
        var winObject = $(windowID).data('kendoWindow');
        if (winObject) {
            winObject.wrapper.addClass(className);
        }
    });
}

function manageActionMenu(e, gridID, templateID) {
    console.log('gridId', gridID);
}

function initializeActionMenu(gridID, templateID, width = 140, offset = '24px') {
    $('#' + gridID).kendoTooltip({
        filter: 'td .tw-action',
        position: 'bottom',
        width: width,
        showOn: 'click',
        show: function (e) {
            e.sender.popup.element.find('.k-callout').remove();
            e.sender.popup.element.addClass('tw-fadein');
            let actionLeftPosition = e.sender.element.find('.tw-action').first().offset().left;
            let documentWidth = $(document).width();
            let actionWidth = e.sender.element.find('.tw-action').first().width();
            let newOffset = documentWidth - (actionLeftPosition + actionWidth);
            e.sender.popup.wrapper.css({
                width: width,
                right: newOffset,
                left: 'unset',
            });
        },
        content: function (e) {
            let dataItem = $('#' + gridID)
                .data('kendoGrid')
                .dataItem(e.target.closest('tr'));
            return kendo.template($('#' + templateID).html())({
                id: dataItem.id,
            });
        },
    });
}

function manageActionColumn(id) {
    return `<div class="tw-action w-fit tw-action--autohide text-right placement-tooltip-target"">
          <a class="tw-btn-action" href="javascript:void(0);" data-id="${id}" aria-label="Expand" tabindex="-1">
            <span class="k-icon k-i-more-horizontal"></span>
            <span class='hidden'>View more action</span>
          </a>
      </div>`;
}

jQuery('#inputString').keyup(function () {
    var searchText = jQuery(this).val();
    searchDataUiLi(searchText, 'insertEmailTemplatePanelBar');
});
jQuery('#searchHeaderLetterTemplate').keyup(function () {
    var searchText = jQuery(this).val();
    searchDataUiLi(searchText, 'insertLetterTemplatePanelBar');
});
jQuery('#searchEmailTemplateStudentOrientation').keyup(function () {
    var searchText = jQuery(this).val();
    searchDataUiLi(searchText, 'insertTemplatePanelBar');
});
jQuery('#searchEmailTemplateStudentProfile').keyup(function () {
    var searchText = jQuery(this).val();
    searchDataUiLi(searchText, 'insertTemplatePanelBar');
});
jQuery('#searchEmailTemplateStudentInvite').keyup(function () {
    var searchText = jQuery(this).val();
    searchDataUiLi(searchText, 'insertStudentInviteTemplatePanelBar');
});

jQuery('#searchTemplateCommunication').keyup(function () {
    var searchText = jQuery(this).val();
    searchDataUiLi(searchText, 'insertTemplatePanelBar');
});

jQuery('#searchEmailTemplatePaymentV2').keyup(function () {
    var searchText = jQuery(this).val();
    searchDataUiLi(searchText, 'insertTemplatePanelBarStudentPayment');
});

function searchDataUiLi(searchText, id) {
    jQuery('ul#' + id + ' li').each(function () {
        if (jQuery(this).text().search(new RegExp(searchText, 'i')) < 0) {
            jQuery(this).hide();
        } else {
            jQuery(this).show();
        }
    });
}

// Master Setting
jQuery('.searchInputEmailTemplateMaster').keyup(function () {
    var filter = jQuery(this).val();
    jQuery('#emailDocumentStepper li').each(function () {
        if (jQuery(this).text().search(new RegExp(filter, 'i')) < 0) {
            jQuery(this).hide();
        } else {
            jQuery(this).show();
        }
    });
});

jQuery('.searchInputLetterTemplateMaster').keyup(function () {
    var filter = jQuery(this).val();
    jQuery('#letterDocumentStepper li').each(function () {
        if (jQuery(this).text().search(new RegExp(filter, 'i')) < 0) {
            jQuery(this).hide();
        } else {
            jQuery(this).show();
        }
    });
});

jQuery('.searchInputSMSTemplateMaster').keyup(function () {
    var filter = jQuery(this).val();
    jQuery('#smsDocumentStepper li').each(function () {
        if (jQuery(this).text().search(new RegExp(filter, 'i')) < 0) {
            jQuery(this).hide();
        } else {
            jQuery(this).show();
        }
    });
});

// Reusable Table - Filter Splitter
function initializeSplitter(selector, sidebarToggle = true) {
    const splitter = $(selector)
        .kendoSplitter({
            draggable: false,
            animation: {
                open: {
                    effects: 'expand',
                    duration: 450,
                },
                close: {
                    effects: 'expand',
                    duration: 450,
                },
            },
            panes: [
                { collapsible: true, size: 250, min: 250 }, // Filter pane
                { collapsible: true }, // Table pane
            ],
            orientation: 'horizontal',
        })
        .data('kendoSplitter');
    resizeSplitter(splitter);
    splitter.collapse('.tw-toggle-filter');
    $(window).resize(() => resizeSplitter(splitter));
    handleSplitbarDragging(selector);
    if (sidebarToggle) {
        handleMenuBtnClick(selector);
        handleFilterBtnClick(selector);
    }

    return splitter;
}

function resizeSplitter(splitter) {
    splitter.wrapper.height($(window).height() - 66); // Subtract height of Gradientbar and Adminbar
    splitter.resize();
}

function handleMenuBtnClick(selector) {
    $('#menuBtn').click(() => setTimeout(() => $(selector).data('kendoSplitter').resize(), 450));
}

function handleFilterBtnClick(selector) {
    $('body').on('click', '#filterBtn', function () {
        const splitter = $(selector).data('kendoSplitter');
        const filterPane = splitter.options.panes[0];
        const sidebarStatus = $.cookie('sidebar') || 'unslide';

        if (filterPane.collapsed) {
            if (sidebarStatus === 'unslide') {
                $('#menuBtn').click();
                setTimeout(() => splitter.resize(), 300);
            }
            splitter.expand('.tw-toggle-filter');
            splitter.size('.tw-toggle-filter', 250);
            const filterPaneWidth = $('.tw-toggle-filter').width();
            $('.tw-resize-panel').width(filterPaneWidth);
        } else {
            splitter.collapse('.tw-toggle-filter');
            if (sidebarStatus === 'slide') {
                $('#menuBtn').click();
                setTimeout(() => splitter.resize(), 300);
            }
        }
    });
}

function handleSplitbarDragging(selector) {
    let isDragging = false;
    $(`${selector} .k-splitbar`).on('mousedown', function () {
        isDragging = true;
    });

    $(document).on('mousemove', function () {
        if (isDragging) {
            setTimeout(() => {
                const filterPaneWidth = $('.tw-toggle-filter').width();
                $('.tw-resize-panel').width(filterPaneWidth);
            }, 200);
        }
    });

    $(document).on('mouseup', function () {
        isDragging = false;
        setTimeout(() => {
            const filterPaneWidth = $('.tw-toggle-filter').width();
            $('.tw-resize-panel').width(filterPaneWidth);
        }, 200);
    });
}

function defaultErrorTemplate() {
    return {
        errorTemplate:
            "<span class='k-form-error k-invalid-msg'><div>This field is required</div></span>",
    };
}

function defaultErrorMessage() {
    return {
        messages: {
            required: function (input) {
                return 'This Field is required';
            },
        },
    };
}

function defaultFormValidator(formID) {
    return $(formID).kendoValidator(defaultErrorMessage()).data('kendoValidator');
}

function displayOrDefault(value) {
    return value !== null && value !== undefined && value !== '' && value !== 'null'
        ? value
        : 'N/A';
}

function validateForm(formID) {
    let validator = defaultFormValidator(formID);
    if (!validator.validate()) {
        notificationDisplay('Please fill in all the required fields.', '', 'error');
        return false;
    }
    return true;
}

$('body').on('keydown', '.onlyLettersAllowed', function (event) {
    var key = event.key;
    if (/[a-z]/i.test(key)) {
        return true;
    } else {
        return false;
    }
});
$('body').on('keydown', '.postalCode', function (event) {
    var key = event.key;
    if (/^[0-9a-zA-Z]*$/.test(key) || event.key === 'Backspace' || event.key === 'Delete') {
        return true;
    } else {
        return false;
    }
});
$('body').on('keydown', '.mobileNumber', function (event) {
    var key = event.key;
    if (/^\d*$/.test(key) || event.key === 'Backspace' || event.key === 'Delete') {
        return true;
    } else {
        return false;
    }
});

function getGridTableHeight(gridID, space = 24) {
    function calculateHeight() {
        let offset = $(gridID).offset() ? $(gridID).offset().top : 200;
        return $(window).height() - offset - space;
    }

    var heightVal = calculateHeight();

    $(window).resize(function () {
        heightVal = calculateHeight();
        $(gridID).data('kendoGrid').wrapper.height(heightVal);
    });

    return heightVal;
}

function handleCalendar(inputIds) {
    if (typeof inputIds === 'string') {
        inputIds = [inputIds];
    }

    $(inputIds.join(',')).on('click', function () {
        var datePicker = $(this).data('kendoDatePicker');

        $(this).on('click', function () {
            datePicker.open();
        });

        $(this).on('blur', function () {
            var inputDate = $(this).val();

            if (!inputDate) {
                return;
            }

            var isValidDate = kendo.parseDate(inputDate, 'dd-MM-yyyy') !== null;

            // If not valid, reset to today's date
            if (!isValidDate) {
                datePicker.value(new Date());
                datePicker.trigger('change');
            }
        });
    });
}

function setMinResizableWidth(gridIDs) {
    if (!Array.isArray(gridIDs)) {
        gridIDs = [gridIDs];
    }

    gridIDs.forEach(function (gridID) {
        var grid = $(gridID).data('kendoGrid');
        console.log('grid', grid);
        if (grid) {
            var options = grid.getOptions();

            options.columns.forEach(function (column) {
                if (column.width && column.width !== '') {
                    if (isNaN(column.width)) {
                        var columnWidth = parseInt(column.width.replace('px', ''), 10);
                    } else {
                        columnWidth = column.width;
                    }
                    $(gridID + " th[data-field='" + column.field + "']").attr(
                        'minResizableWidth',
                        columnWidth < 100 ? columnWidth : 100
                    );
                    column.minResizableWidth = columnWidth < 100 ? columnWidth : 100;
                } else {
                    column.minResizableWidth = 100;
                    $(gridID + " th[data-field='" + column.field + "']").attr(
                        'minResizableWidth',
                        100
                    );
                }
            });

            grid.setOptions(options);
        }
    });
}

function manageTooltipPosition(e, width = '140px', selector = '.tw-btn-action') {
    e.sender.popup.element.find('.k-callout').remove();
    e.sender.popup.element.addClass('tw-fadein');
    let actionLeftPosition = e.sender.element.find('.tw-btn-action').first().offset().left;
    let documentWidth = $(document).width();
    let actionWidth = e.sender.element.find(selector).first().width();
    let newOffset = documentWidth - (actionLeftPosition + actionWidth);
    e.sender.popup.wrapper.css({
        width: width,
        right: newOffset,
        left: 'unset',
    });
}

function getStatusTag(label, variant) {
    let color;
    switch (variant) {
        case 'primary':
            color = 'primary-blue';
            break;
        case 'secondary':
            color = 'gray';
            break;
        case 'success':
            color = 'green';
            break;
        case 'warning':
            color = 'yellow';
            break;
        case 'danger':
            color = 'red';
            break;
        default:
            color = 'primary-blue';
            break;
    }
    return `<div class="inline-flex items-center justify-center rounded-full bg-${color}-200 px-2.5 py-1">
    <span class="text-xs leading-4 text-center text-${color}-700">${label}</span>
    </div>`;
}

function formReset(kendoFormId) {
    /*let kendoForm = $(kendoFormId).data("kendoForm");
    if (kendoForm) {
        kendoForm.reset();
    }*/
    $(`${kendoFormId} input[type='text'], ${kendoFormId} textarea`).val('');
}
function convertDateFormatDMYToMDY(inputDate) {
    // Split the input date string into day, month, and year
    let parts = inputDate.split('-');
    let day = parts[0];
    let month = parts[1];
    let year = parts[2];
    // Swap day and month and format the date as "MM-DD-YYYY"
    let formattedDate = month + '-' + day + '-' + year;
    return formattedDate;
}
function convertDateFormatYMDToDMY(inputDate) {
    // Split the input date string into day, month, and year
    let parts = inputDate.split('-');
    let year = parts[0];
    let month = parts[1];
    let day = parts[2];
    // Swap day and month and format the date as "MM-DD-YYYY"
    let formattedDate = day + '-' + month + '-' + year;
    return formattedDate;
}

function handleButtonTitleAttr(selector, isRowSelected) {
    const defaultTitle = $(selector).data('title');
    if (isRowSelected) {
        $(selector).removeAttr('disabled');
        $(selector).attr('title', defaultTitle);
    } else {
        $(selector).attr('disabled', true);
        $(selector).attr('title', 'Select at least one row');
    }
}

$(document).ready(function () {
    $('.tw-disabled-msg').attr('title', 'Select at least one row');
});

function loadJS(FILE_URL, async = true) {
    let scriptEle = document.createElement('script');

    scriptEle.setAttribute('src', FILE_URL);
    scriptEle.setAttribute('type', 'text/javascript');
    scriptEle.setAttribute('async', async);

    document.body.appendChild(scriptEle);

    // success event
    scriptEle.addEventListener('load', () => {
        console.log('File loaded');
    });
    // error event
    scriptEle.addEventListener('error', (ev) => {
        console.log('Error on loading file', ev);
    });
}

function updateURLParameter(key, value) {
    const url = new URL(window.location.href);
    url.searchParams.set(key, value);
    const newURL = url.href;
    history.pushState({ path: newURL }, '', newURL);
}

function getContentWidth(windowSelector) {
    var documentWidth = $('body').width();
    var sidebarWidth = $('#sidebar').width() + 16;
    var contentWidth = documentWidth - sidebarWidth + 'px';
    console.log('conet', contentWidth, documentWidth, sidebarWidth);
    $(windowSelector)
        .getKendoWindow()
        .setOptions({
            width: contentWidth,
            position: { left: sidebarWidth, top: 0 },
        });
}

var observerInitialized = false;
function initObserver() {
    if (observerInitialized) {
        return;
    }

    var observer = new IntersectionObserver(function (entries) {
        entries.forEach(function (entry) {
            if (entry.isIntersecting) {
                $(entry.target).addClass('tw-slideup');
                observer.unobserve(entry.target);
            }
        });
    });

    // Function to observe elements
    function observeElements() {
        $('.tw-animate').each(function () {
            console.log('$this', $('.tw-animate'));
            observer.observe(this);
        });
    }

    // Observe existing elements
    observeElements();

    // Observe dynamically added elements (e.g., after Kendo templates render)
    $(document).on('DOMNodeInserted', function (e) {
        if ($(e.target).hasClass('tw-animate')) {
            observer.observe(e.target);
        }
    });

    observerInitialized = true;
}

/**
 * Check for required fields and enable after all fields are filled
 * @param {*} selector
 * @returns
 */
function checkFields(selector) {
    var allFilled = true;
    let valid = false;
    $(selector + ' input[required]', selector + ' textarea[required]').each(function () {
        var $this = $(this);
        if (
            ($this.is(':checkbox') && !$this.is(':checked')) || // checkbox
            (($this.is(':text') || $this.is('textarea')) && !$this.val()) || // text
            ($this.is(':radio') && !$('input[name=' + $this.attr('name') + ']:checked').length)
        ) {
            valid = false;
        }
    });
    return allFilled;
}

function setFormButtonsTemplate({
    submitLabel = 'Save',
    cancelLabel = 'Cancel',
    btnType = 'submit',
    btnClass = '',
    isDisabled = false,
}) {
    return `
    <div class="modal-footer w-full inline-flex space-x-4 items-center justify-end px-1 py-1">
        <button type="button" class="btn-secondary cancelBtn">
            <p class="text-sm font-medium leading-4 text-gray-700">${cancelLabel}</p>
        </button>
        <button type="${btnType}" class="${btnClass} btn-primary" ${isDisabled ? 'disabled' : ''}>
            <p class="text-sm font-medium leading-4 text-white">${submitLabel}</p>
        </button>
    </div>
    `;
}

function toggleFormDisableAttr(selector, btnSelector) {
    $(selector).on('input', function () {
        $(btnSelector).attr('disabled', !checkFields(selector));
    });
}

function setFromEmail(modalId) {
    ajaxActionV2('api/get-from-email-id', 'POST', {}, function (response) {
        $(modalId).find('#email_from').val(response.data);
    });
}
function getSerializeFormArray(formId, arr = []) {
    let serializeArr = $(document)
        .find(formId)
        .find(
            'input[name], select[name], textarea[name], input[type=checkbox], input[type=radio], :hidden'
        )
        .serializeArray();
    $(serializeArr).each(function (i, field) {
        if (arr.hasOwnProperty(field.name)) {
            if (!Array.isArray(arr[field.name])) {
                arr[field.name] = [arr[field.name]];
            }
            arr[field.name].push(field.value);
        } else {
            arr[field.name] = field.value;
        }
    });
    return arr;
}

function setColumnMinWidth(gridId, hasPadding = false, container = '.maincontainer') {
    let minGridWidth = 0;
    let setMinWidth = false;
    const COLUMN_PADDING = 16;
    function handleResize() {
        var gridCurrent = $(document).find(container).width() - 40;
        var gridData = $(document).find(gridId).data('kendoGrid');
        if (typeof gridData === 'undefined') {
            return;
        }
        var columns = gridData.getOptions().columns;
        columns.map((item) => {
            return (minGridWidth += item.minWidth);
        });
        console.log('gridData', gridCurrent, minGridWidth);
        setMinWidth = gridCurrent < minGridWidth;
        if (setMinWidth) {
            gridData.columns.forEach((column, index) => {
                column.width = columns[index].minWidth + COLUMN_PADDING;
            });
        } else {
            gridData.columns.forEach((column, index) => {
                if (index === columns.length - 1) {
                    delete column.width;
                } else {
                    column.width = columns[index].width
                        ? columns[index].width + COLUMN_PADDING
                        : '';
                }
            });
        }
        //console.log("gridData", gridData);
        gridData.refresh();
        //console.log("gridData2", gridData);
    }
    handleResize();

    // $(window).resize(function () {
    //     minGridWidth = 0;
    //     setMinWidth = false;
    //     handleResize();
    // });
}

function defaultStudentCourseStatusColor(status) {
    const statusColors = {
        'Current Student': 'primary-blue',
        Cancelled: 'red',
        Transitioned: 'yellow',
        Completed: 'green',
        Finished: 'green',
        Withdrawn: 'pink',
    };
    return statusColors[status] || 'gray';
}

function normalizeDate(date) {
    return new Date(date.getFullYear(), date.getMonth(), date.getDate());
}

function getFileMangerHeight(fileManagerId, space = 24) {
    function calculateHeight() {
        let offset = $(fileManagerId).offset() ? $(fileManagerId).offset().top : 200;
        return $(window).height() - 200 - space;
    }

    var heightVal = calculateHeight();
    console.log('heightVal', heightVal);

    $(window).resize(function () {
        heightVal = calculateHeight();
        console.log('getHeight', heightVal);
        $(fileManagerId).data('kendoFileManager').wrapper.height(heightVal);
    });

    return heightVal;
}

function readOnlyDatePicker(format = 'dd-MM-yyyy') {
    return {
        format: format,
        open: function () {
            // Make the input read-only to prevent manual input
            this.element.attr('readonly', 'readonly');
        },
    };
}

function customDateEditor(container, options) {
    let isRequired = options.validation && options.validation.required ? 'required' : '';

    let inputId = options.id ? options.id : options.field;

    let input = $('<input id="' + inputId + '" name="' + options.field + '" ' + isRequired + '/>')
        .appendTo(container)
        .kendoDatePicker({
            format: options.dateFormat,
            change: function () {
                // Trigger validation on change to ensure it captures the selected date
                let validator = container.closest('form').data('kendoValidator');
                if (validator) {
                    validator.validateInput(this.element);
                }
            },
        });

    // Prevent typing in the input field
    input.on('keydown', function (e) {
        e.preventDefault();
    });

    // Toggle DatePicker on input click
    let datePicker = input.data('kendoDatePicker');
    input.on('click', function () {
        if (datePicker.dateView.popup.visible()) {
            datePicker.close();
        } else {
            datePicker.open();
        }
    });
}

function setReplyToEmailSetup(inputIdName, emailData) {
    let replyToInput = document.querySelector(inputIdName);
    let tagifyReplyTo = new Tagify(replyToInput, {
        delimiters: ',| ',
        trim: false,
        tagTextProp: 'value',
        skipInvalid: true,
        placeholder: 'Search Reply-To Email Here...',
        enforceWhitelist: false,
        maxTags: 1,
        dropdown: {
            maxItems: 1,
            closeOnSelect: true,
            enabled: 1,
            highlightFirst: true,
            fuzzySearch: true,
            position: 'text',
            classname: 'reply-to-emails',
            searchKeys: ['value'],
        },
        whitelist: emailData,
        callbacks: {
            add: function (e) {
                const tag = e.detail.data.value;

                // Check if it's a valid email
                const emailPattern =
                    /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/;
                if (!emailPattern.test(tag)) {
                    console.log(tag);
                    notificationDisplay('Please enter valid email id.', '', 'error'); // Alert the user
                    e.detail.tagify.removeTag(e.detail.tag);
                }
                if (e.detail.tagify.value.length > 1) {
                    $(inputIdName).attr('placeholder', '').prop('disabled', true);
                    e.detail.tagify.removeTag(e.detail.tag); // Remove the newly added tag
                    notificationDisplay('Only one reply-to email can be selected.', '', 'error'); // Alert the user
                }
            },
            remove: function (e) {
                if (e.detail.tagify.value.length == 0) {
                    $(inputIdName)
                        .attr('placeholder', 'Search Reply-To Email Here...')
                        .prop('disabled', false);
                }
            },
        },
        templates: {
            tag: function (e) {
                return `<tag title="${e.value}" contenteditable='false' spellcheck='false' tabIndex="-1" class="tagify__tag bg-gray-200">
                    <x title='' class='tagify__tag__removeBtn' role='button' aria-label='remove tag'></x>
                    <div class='flex items-center tag-div'>&nbsp;
                    <div class='text-xs leading-normal text-gray-900 pl-1'>${e.value}</div>
                    </div>
                </tag>`;
            },
        },
    });

    if (tagifyReplyTo.value.length > 0) {
        $(inputIdName).attr('placeholder', '').prop('disabled', true);
    }
    tagifyReplyTo.removeAllTags();
}

window.cookieStorage = {
    getItem(key) {
        let cookies = document.cookie.split(';');
        for (let i = 0; i < cookies.length; i++) {
            let cookie = cookies[i].split('=');
            if (key == cookie[0].trim()) {
                return decodeURIComponent(cookie[1]);
            }
        }
        return null;
    },
    setItem(key, value) {
        // Check if the cookie already exists and has the same value
        let currentValue = this.getItem(key);
        if (currentValue !== value) {
            // Update the cookie if the value is different
            document.cookie = key + '=' + encodeURIComponent(value) + '; path=/';
        }
    },
};

function showNoMatch(value) {
    if (value && value.includes('NoMatch')) {
        return 'No Match';
    }
    return value || '-';
}
const showCheckIcon = (value) => {};

function getBadgeVariant(value) {
    //if (value === "USI AERTYFSSER is Invalid") { return "error"; }
    if (value.includes('Invalid')) {
        return 'error';
    } else if (value.includes('failed')) {
        return 'error';
    }
    return 'success';
}

function updateFormData(formData, key, value) {
    if (formData.has(key) && formData.get(key)) {
        formData.set(key, value); // Replace the existing key
    } else {
        formData.append(key, value); // Add the new key
    }
}

function kendoWindowOpen(windowID) {
    let kendoWindow = $(document).find(windowID);
    kendoWindow.data('kendoWindow').center().open();
    kendoWindow.data('kendoWindow').wrapper.addClass('k-modal-window');
}

// Date format function
function convertJsDateFormat(dateString, format = displayDateFormatJS) {
    return formatDateString(dateString, format, ['dd', 'MMM', 'yyyy']);
}

// DateTime format function
function convertJsDateTimeFormat(dateString, format = displayDateTimeFormatJS) {
    return formatDateString(dateString, format, ['dd', 'MMM', 'yyyy', 'h', 'i', 'A']);
}

function createDateComponents(date) {
    return {
        dd: String(date.getDate()).padStart(2, '0'),
        MMM: date.toLocaleString('en-GB', { month: 'short' }),
        yyyy: date.getFullYear(),
        h: String(date.getHours() % 12 || 12), // 12-hour format, handling "0" as "12"
        i: String(date.getMinutes()).padStart(2, '0'),
        A: date.getHours() >= 12 ? 'PM' : 'AM',
    };
}

function formatDateString(dateString, format, componentKeys) {
    const date = new Date(dateString);
    if (isNaN(date)) return ''; // Validate date

    const components = createDateComponents(date);
    const pattern = new RegExp(componentKeys.join('|'), 'g');
    return format.replace(pattern, (match) => components[match] || match);
}

function loadPdfJsCdn() {
    return new Promise(function (resolve, reject) {
        // Load pdf.js script from CDN
        var pdfjsScript = document.createElement('script');
        pdfjsScript.src = 'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.min.js';
        document.head.appendChild(pdfjsScript);

        // Wait for the script to load
        pdfjsScript.onload = resolve;
        pdfjsScript.onerror = reject;
    }).then(function () {
        // Set the worker source from the CDN
        pdfjsLib.GlobalWorkerOptions.workerSrc =
            'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/3.11.174/pdf.worker.min.js';
    });
}
// function getPdf(fileUrl) {
//     loadPdfJsCdn().then(function () {
//         var loadingTask = pdfjsLib.getDocument(fileUrl);
//         loadingTask.promise.then(function (pdf) {
//             $('#page_num').text(1);
//             $('#page_count').text(pdf._pdfInfo.numPages);
//             pdf.getPage(1).then(function (page) {
//                 var scale = 1;
//                 var viewport = page.getViewport({ scale: scale });
//                 var canvas = document.getElementById("pageContainer");
//                 var context = canvas.getContext("2d");
//                 canvas.height = viewport.height * 0.9;
//                 canvas.width = viewport.width;

//                 var renderContext = {
//                     canvasContext: context,
//                     viewport: viewport
//                 };

//                 var renderTask = page.render(renderContext);
//                 renderTask.promise.then(function () {
//                     console.log("Page rendered");
//                 });
//             });
//         }).catch(function (reason) {
//             console.log("Error loading PDF:", reason);
//         });
//     }).catch(function (error) {
//         console.log("Error loading PDF.js from CDN:", error);
//     });
// }

let pdfDoc = null;
let pageNum = 1;
const scale = 1;
function getPdf(fileUrl) {
    loadPdfJsCdn()
        .then(function () {
            const file = window.URL.createObjectURL(fileUrl);
            console.log(file);
            const loadingTask = pdfjsLib.getDocument(file);
            loadingTask.promise
                .then(function (pdf) {
                    pdfDoc = pdf;
                    $('#page_count').text(pdf._pdfInfo.numPages);
                    if (pdf._pdfInfo.numPages > 1) {
                        $('#nextPdfContainer').removeClass('hidden');
                        $('#prevPdfContainer').removeClass('hidden');
                    } else {
                        $('#nextPdfContainer').addClass('hidden');
                        $('#prevPdfContainer').addClass('hidden');
                    }
                    renderPage(pageNum);
                })
                .catch(function (reason) {
                    console.log('Error loading PDF:', reason);
                });
        })
        .catch(function (error) {
            console.log('Error loading PDF.js from CDN:', error);
        });
}

function renderPage(num) {
    pdfDoc.getPage(num).then(function (page) {
        const viewport = page.getViewport({ scale: scale });
        const canvas = document.getElementById('pageContainer');
        const context = canvas.getContext('2d');
        canvas.height = viewport.height;
        canvas.width = viewport.width;

        const renderContext = {
            canvasContext: context,
            viewport: viewport,
        };

        const renderTask = page.render(renderContext);
        renderTask.promise.then(function () {
            $('#page_num').text(num);
            console.log(`Page ${num} rendered`);
        });
    });
}

function onNextPage() {
    if (pageNum < pdfDoc.numPages) {
        pageNum++;
        renderPage(pageNum);
    }
}

function onPrevPage() {
    if (pageNum > 1) {
        pageNum--;
        renderPage(pageNum);
    }
}

$('body').on('click', '#nextPdfContainer', function () {
    onNextPage();
});

$('body').on('click', '#prevPdfContainer', function () {
    onPrevPage();
});

// for actionTolltip
function getActionTooltip(targetClassName, width = 110, position = 'top', isDarktheme = true) {
    $(targetClassName).kendoTooltip({
        content: function (e) {
            return e.target.data('title');
        },
        position: position,
        width: width,
        show: function (e) {
            // Dynamically assign styles based on the theme
            $(e.sender.popup.element).css({
                backgroundColor: isDarktheme ? '#374151' : '#fff', // Dark theme background is darker
                color: isDarktheme ? '#fff' : '#000', // Dark theme text is white
                padding: '4px 8px',
                borderRadius: '6px',
                textAlign: 'center',
            });
        },
    });
}

function getQueryParam(name) {
    const match = window.location.search.match(new RegExp('[?&]' + name + '=([^&]*)'));
    return match ? decodeURIComponent(match[1]) : null;
}

function setupTogglePopup(toggleButtonSelector, popupSelector, closable = true) {
    $('body').on('click', toggleButtonSelector, function (event) {
        console.log('Popup');
        event.stopPropagation();
        $(popupSelector).toggleClass('hidden');
    });

    $(document).on('click', function (event) {
        if (!$(event.target).closest(popupSelector, toggleButtonSelector).length) {
            $(popupSelector).addClass('hidden');
        }
    });
    if (closable) {
        $(popupSelector).on('click', function () {
            $(this).addClass('hidden');
        });
    }
}

function toggleTabLoader(tab, show = true) {
    const element = $(`.${tab}-li-detail`);
    const loader = $(`.tw-ajax-loader--${tab}`);
    const gridContent = $(document).find(`.tw-${tab}-grid`);
    if (loader.length) {
        loader.toggleClass('hidden', !show);
    }
    if (!show) {
        $('body').find('.overflowWrapper').css('overflow-y', 'auto'); // Prevent scrolling
        gridContent.removeClass('hidden');
    } else {
        $('body').find('.overflowWrapper').css('overflow-y', 'hidden'); // Restore scrolling
        gridContent.removeClass('hidden');
    }
}

function toggleContentLoader(selector, flag, classes = 'animate-pulse') {
    const element = $(document).find(selector);
    const skeleton = element.find(`.tw-skeleton`);
    const otherContent = element.children().not(skeleton);
    // flag ? element.addClass(classes) : element.removeClass(classes);
    if (flag) {
        $('body').find('.overflowWrapper').css('overflow-y', 'hidden');
        skeleton.show().addClass(classes);
        otherContent.hide();
    } else {
        $('body').find('.overflowWrapper').css('overflow-y', 'auto');
        skeleton.hide().removeClass(classes);
        otherContent.show();
    }
}

// Creates a global object called templateLoader with a single method "loadExtTemplate".
var templateLoader = (function ($, host) {
    // Loads the external templates from the path and injects into the page DOM.
    return {
        // Method: loadExtTemplate.
        // Params: (string) path: the relative path to a file that contains template definitions.
        loadExtTemplate: function (path, callbacks = {}) {
            const { onBefore, onSuccess, onError } = callbacks;

            if (typeof onBefore === 'function') {
                onBefore(path);
            }
            // Use jQuery Ajax to fetch the template file.
            var tmplLoader = $.get(path)
                .done(function (result) {
                    // On success, add templates to the DOM (assumes that only the file has template definitions).
                    $('body').append(result);
                    if (typeof onSuccess === 'function') {
                        onSuccess(result, path);
                    }
                })
                .fail(function (xhr, status, error) {
                    if (typeof onError === 'function') {
                        onError(xhr, status, error, path);
                    } else {
                        console.error('Error Loading Templates: ' + error);
                    }
                });

            tmplLoader.complete(function () {
                // Publish an event that indicates when a template is done loading.
                $(host).trigger('TEMPLATE_LOADED', [path]);
            });
        },
    };
})(jQuery, document);

$(document).on('click', '.tw-dropdown-trigger[data-toggle="dropdown"]', function (e) {
    e.stopPropagation();

    const $button = $(this);
    const $menu = $button.siblings('.tw-dropdown-popup');

    // Hide all other dropdowns
    $('.tw-dropdown-popup').not($menu).addClass('hidden').attr('aria-hidden', 'true');

    $menu.toggleClass('hidden');
    $menu.attr('aria-hidden', $menu.hasClass('hidden') ? 'true' : 'false');
});

$(document).on('click', function (e) {
    if (
        !$(e.target).closest('.tw-dropdown-popup').length &&
        !e.target.classList.contains('tw-dropdown-trigger-prevent')
    ) {
        $('.tw-dropdown-popup').addClass('hidden').attr('aria-hidden', 'true');
    }
    // $(".tw-dropdown-popup").addClass("hidden").attr("aria-hidden", "true");
});

$('.copy-button').each(function () {
    $(this)
        .kendoTooltip({
            filter: '.icon-copy',
            autoHide: true,
            position: 'right',
            animation: {
                open: {
                    effects: 'fade:in',
                    duration: 150,
                },
            },
            show: function (e) {
                e.sender.popup.element.addClass('tw-tooltip tw-tooltip--default');
            },
        })
        .data('kendoTooltip');
});
$(document).on('click', '.copy-button', function () {
    console.log('clicked');
    var checkIcon = $(this).find('.icon-check');
    var copyIcon = $(this).find('.icon-copy');
    console.log('aaa', checkIcon, copyIcon);
    copyIcon.hide();
    checkIcon.show();
    // tooltip.show(checkIcon);
    setTimeout(() => {
        checkIcon.hide();
        copyIcon.show();
    }, 3000);
    var textValue = $(this).attr('data-text');
    var $temp = $("<input name='copy'>");
    $('body').append($temp);
    $temp.val(textValue).select();
    // Copy the text to the clipboard
    document.execCommand('copy');
    $temp.remove();
});

$('body').on('change', '.permission', function () {
    var roleId = $(this).val();
    console.log('roleId', roleId);
    window.location = site_url + 'update-session-value/' + roleId;
    $('.permission').selectric('refresh');
});

$(document).ready(function () {
    $(document).on('click', '.tw-menu-link', function (e) {
        const href = $(this).attr('href');
        console.log('clickedItem', href);

        if (!href || href === '#') {
            e.preventDefault();
            return;
        }

        // Remove active class from all links
        $('.tw-menu-link').removeClass('active');

        // Add active class to the clicked one
        $(this).addClass('active');
    });
});

// Configuration object for different dialog types
const DIALOG_TYPES = {
    error: {
        cssClass: 'k-dialog-error',
        icon: 'k-icon k-i-exclamation-triangle',
        iconColor: '#dc3545',
        titlePrefix: 'Error: ',
    },
    success: {
        cssClass: 'k-dialog-success',
        icon: 'k-icon k-i-check-circle',
        iconColor: '#28a745',
        titlePrefix: 'Success: ',
    },
    warning: {
        cssClass: 'k-dialog-warning',
        icon: 'k-icon k-i-warning',
        iconColor: '#ffc107',
        titlePrefix: 'Warning: ',
    },
    info: {
        cssClass: 'k-dialog-info',
        icon: 'k-icon k-i-info-circle',
        iconColor: '#17a2b8',
        titlePrefix: 'Info: ',
    },
};

// Default animation configuration
// function defaultOpenAnimation() {
//     return {
//         open: {
//             effects: 'fadeIn',
//             duration: 300
//         },
//         close: {
//             effects: 'fadeOut',
//             duration: 200
//         }
//     };
// }

/**
 * Creates a reusable confirmation dialog
 * @param {string} modalId - The ID of the modal element
 * @param {string} title - Dialog title
 * @param {string} content - Dialog content/message
 * @param {string} type - Dialog type: 'error', 'success', 'warning', 'info'
 * @param {Function} callbackFn - Callback function for confirmation action
 * @param {Object} options - Additional options for customization
 */
function confirmDialog(modalId, title, content, type = 'info', callbackFn, options = {}) {
    // Validate type
    if (!DIALOG_TYPES[type]) {
        console.warn(`Invalid dialog type: ${type}. Using 'info' as default.`);
        type = 'info';
    }

    const dialogType = DIALOG_TYPES[type];
    const settings = {
        width: options.width || '400px',
        title: title,
        content: createDialogContent(content, dialogType),
        actions: createDialogActions(callbackFn, options),
        animation: options.animation || defaultOpenAnimation(),
        closable: options.closable !== false,
        modal: options.modal !== false,
        visible: false,
        open: function () {
            // Add type-specific CSS class
            $(this.element).closest('.k-dialog').addClass(dialogType.cssClass);

            // Execute custom open callback if provided
            if (typeof options.onOpen === 'function') {
                options.onOpen.call(this);
            }
        },
        close: function () {
            // Execute custom close callback if provided
            if (typeof options.onClose === 'function') {
                options.onClose.call(this);
            }
        },
    };

    // Create or update the dialog
    const dialog = $(modalId).kendoDialog(settings);

    // Show the dialog
    dialog.data('kendoDialog').open();

    return dialog.data('kendoDialog');
}

/**
 * Creates formatted dialog content with icon
 */
function createDialogContent(content, dialogType) {
    return `
        <div class="confirm-dialog-content flex gap-2">
            <div class="confirm-dialog-icon">
                <span class="${dialogType.icon}" style="color: ${dialogType.iconColor}; font-size: 20px;"></span>
            </div>
            <div class="confirm-dialog-text">
                ${content}
            </div>
        </div>
    `;
}

/**
 * Creates dialog action buttons
 */
function createDialogActions(callbackFn, options) {
    const actions = [];

    // Cancel/Close button
    actions.push({
        text: options.cancelText || 'Cancel',
        action: function (e) {
            if (typeof options.onCancel === 'function') {
                options.onCancel.call(this, e);
            }
        },
    });

    // Confirm button
    actions.push({
        text: options.confirmText || 'Yes',
        primary: true,
        action: function (e) {
            if (typeof callbackFn === 'function') {
                callbackFn.call(this, e);
            }
        },
    });

    return actions;
}
