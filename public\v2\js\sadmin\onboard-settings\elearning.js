$(document).ready(function () {
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
        },
    });

    $('#loader').kendoLoader();
    kendo.ui.progress.messages = {
        loading:
            '<div class="vue-simple-spinner animate-spin" style="position:absolute;top:50%;left:50%;transfrom:translate(-50%,-50%); margin: 0px auto; border-radius: 100%; border-color: rgb(50, 157, 243) rgb(238, 238, 238) rgb(238, 238, 238); border-style: solid; border-width: 3px; border-image: none 100% / 1 / 0 stretch; width: 30px; height: 30px;"></div>',
    };

    $('#elearningLinkList').kendoGrid({
        dataSource: customDataSource('api/elearning-link-data', {
            id: { type: 'integer' },
            title: { type: 'string' },
            link: { type: 'string' },
            created_by: { type: 'string' },
            created_date: { type: 'date' },
        }),
        pageable: customPageableArr(),
        dataBound: function (e) {
            setTimeout(function () {
                setFilterIcon('#elearningLinkList');
            }, 100);
            manageNoDataTemplate('#elearningLinkList', '.dataEformDiv', '.noDataEformDiv');
        },
        filterable: false,
        sortable: true,
        resizable: true,
        columns: [
            {
                template:
                    "<div class='flex items-center text-sm leading-5 font-normal text-gray-600'>#: id #</div>",
                field: 'id',
                title: 'ID',
                width: 80,
            },
            {
                template: function (dataItem) {
                    return manageTitle(dataItem.title, dataItem.link);
                },
                // template: "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: title #</div>",
                field: 'title',
                title: 'FORM NAME',
                width: 150,
            },
            {
                // template:
                //     "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: link #</div>",
                template: function (dataItem) {
                    return manageTitle(dataItem.link, dataItem.link);
                },
                field: 'link',
                title: 'LINK',
                width: 500,
            },
            {
                template:
                    "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: created_by #</div>",
                field: 'created_by',
                title: 'CREATED BY',
                width: 150,
            },
            {
                template:
                    '<div class=\'flex items-center text-sm leading-5 font-normal text-gray-600 action-div\'>#: kendo.toString(created_date, "dd MMM yyyy") #</div>',
                field: 'created_date',
                title: 'CREATED DATE',
                width: 150,
            },
            {
                headerTemplate:
                    "<a class='k-link text-xs font-medium tracking-wide leading-none text-gray-500 uppercase' style='cursor: default !important;'>ACTION</a>",
                template: function (dataItem) {
                    // return manageAction(dataItem.id);
                    return manageActionColumn(dataItem.id);
                },
                field: 'action',
                title: 'ACTIONS',
                width: 80,
                filterable: false,
                sortable: false,
            },
            {},
        ],
        noRecords: noRecordTemplate(),
    });
    initializeActionMenu('elearningLinkList', 'eFormActionMenu', 150, '54px');
    customGridHtml('#elearningLinkList');

    function manageTitle(title, link) {
        return (
            '<div class="flex items-center text-sm leading-5 font-normal text-primary-blue-500 action-div hover:underline truncate">' +
            '<a class="truncate" href="' +
            link +
            '" target="_blank">' +
            title +
            '</a>' +
            '</div>'
        );
    }

    function manageAction(id) {
        return (
            '<div class="action-div flex justify-start items-center space-x-1">' +
            '<div class="editElearningLink action-menu flex-col space-x-3 items-start justify-start p-2 cursor-pointer" data-id = "' +
            id +
            '"><span class="k-icon k-i-edit k-icon-edit"></span></div>' +
            '<div class="deleteElearningLink action-menu flex-col space-x-3 items-start justify-start p-2 cursor-pointer" data-id = "' +
            id +
            '"><span class="k-icon k-i-delete k-icon-delete"></span></div>' +
            '</div>'
        );
    }

    function kendoWindowOpen(windowID) {
        let kendoWindow = $(document).find(windowID);
        kendoWindow.getKendoWindow().open();
        kendoWindow
            .parent('div')
            .find('.k-window-titlebar')
            .addClass('titlebar-sms-modal bg-gradient-to-l from-green-400 to-blue-500')
            .find('.k-window-title')
            .addClass('text-lg font-medium leading-normal text-white');
    }

    function openCenterWindow(titleText, widthVal = 34, topVal = 15, leftVal = 33) {
        return {
            title: titleText,
            width: widthVal + '%',
            // height: "70%",
            actions: ['close'],
            draggable: false,
            resizable: false,
            modal: true,
            position: {
                top: topVal + '%',
                left: leftVal + '%',
            },
            animation: defaultCloseAnimation(),
        };
    }

    $('#addElearningLinkModal').kendoWindow(openCenterWindow('Add E-forms Link'));
    $('#editElearningLinkModal').kendoWindow(openCenterWindow('Edit E-forms Link'));

    addModalClassToWindows(['#addElearningLinkModal', '#editElearningLinkModal']);

    $('body').on('click', '.addElearningLink', function () {
        kendoWindowOpen('#addElearningLinkModal');
        $('#addElearningLinkForm')[0].reset();
    });

    $('body').on('click', '.editElearningLink', function (e) {
        const id = $(this).attr('data-id');
        const dataItem = $('#elearningLinkList').data('kendoGrid').dataSource.get(id);
        kendoWindowOpen('#editElearningLinkModal');
        let editElearningLinkForm = $(document).find('#editElearningLinkForm');
        console.log('data', dataItem);
        editElearningLinkForm.find('#editId').val(dataItem.id);
        editElearningLinkForm.find('#title').val(dataItem.title);
        editElearningLinkForm.find('#link').val(dataItem.link);
    });

    $('#addElearningLinkForm').kendoForm({
        orientation: 'vertical',
        formData: {
            tracking_form: 'applications.elearning_link_list',
        },
        layout: 'grid',
        grid: {
            gutter: 16,
        },
        items: [
            {
                field: 'title',
                label: 'Title',
                validation: { required: true },
                attributes: {
                    class: '!h-9 !px-3 !indent-0',
                },
            },
            {
                field: 'link',
                label: 'Link',
                validation: { required: true },
                attributes: {
                    class: '!h-9 !px-3 !indent-0',
                },
            },
        ],
        buttonsTemplate:
            '<div class="w-full inline-flex space-x-4 items-center justify-end px-1">\n' +
            '<div class="float-right flex space-x-4 items-center justify-end">\n' +
            '<button type="button" class="flex justify-center px-6 py-2 bg-white h-9 border hover:shadow-lg rounded-lg  border-gray-300 focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-gray-400 cancelBtn" type="button">\n' +
            '<p type="button" class="text-sm text-gray-700">Cancel</p>\n' +
            '</button>\n' +
            '<button type="submit" class="flex justify-center h-9 px-3 py-2 bg-primary-blue-500 hover:shadow-lg rounded-lg focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-primary-blue-600">\n' +
            '<p class="text-sm text-white">Save Elearning Link</p>\n' +
            '</button>\n' +
            '</div>\n' +
            '</div>',
        submit: function (ev) {
            saveModalData('save-elearning-link', 'addElearningLinkModal', 'elearningLinkList');
            ev.preventDefault();
            return false;
        },
    });

    $('#editElearningLinkForm').kendoForm({
        orientation: 'vertical',
        layout: 'grid',
        grid: {
            gutter: 16,
        },
        items: [
            {
                field: 'title',
                label: 'Title',
                attributes: {
                    placeholder: 'Enter Title',
                    class: '!h-9 !px-3 !indent-0',
                },
                validation: { required: true },
            },
            {
                field: 'link',
                label: 'Link',
                attributes: {
                    placeholder: 'Enter Link',
                    class: '!h-9 !px-3 !indent-0',
                },
                validation: { required: true },
            },
        ],
        buttonsTemplate:
            '<div class="w-full inline-flex space-x-4 items-center justify-end px-1">\n' +
            '<div class="float-right flex space-x-4 items-center justify-end">\n' +
            '<button type="button" class="flex justify-center px-6 py-2 bg-white h-9 border hover:shadow-lg rounded-lg  border-gray-300 focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-gray-400 cancelBtn" type="button">\n' +
            '<p type="button" class="text-sm text-gray-700">Cancel</p>\n' +
            '</button>\n' +
            '<input type="hidden" id="editId" name="id">\n' +
            '<button type="submit" class="flex justify-center h-9 px-3 py-2 bg-primary-blue-500 hover:shadow-lg rounded-lg focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-primary-blue-600">\n' +
            '<p class="text-sm text-white">Update Elearning Link</p>\n' +
            '</button>\n' +
            '</div>\n' +
            '</div>',
        submit: function (ev) {
            saveModalData('save-elearning-link', 'editElearningLinkModal', 'elearningLinkList');
            ev.preventDefault();
            return false;
        },
    });

    function saveModalData(url, modalId = '', girdId = '') {
        let serializeArr = $(document)
            .find('#' + modalId)
            .find('input[name], select[name], textarea[name]')
            .serializeArray();
        let dataArr = {};
        $(serializeArr).each(function (id, field) {
            dataArr[field.name] = field.value;
        });
        ajaxcallwithMethodKendo(site_url + 'api/' + url, dataArr, 'POST', function (resultData) {
            $(document)
                .find('#' + modalId)
                .getKendoWindow()
                .close();
            $('#' + girdId)
                .data('kendoGrid')
                .refresh();
            $('#' + girdId)
                .data('kendoGrid')
                .dataSource.read();
            notificationDisplay(resultData.message, '', resultData.status);
            window.dispatchEvent(new Event('obformsuccess'), {});
        });
    }

    $('body').on('click', '.deleteElearningLink', function () {
        let primaryID = $(this).attr('data-id');
        $('#deleteElearningLinkModal').data('kendoDialog').open();
        $('#deleteElearningLinkModal').find('#deleteElearningLinkId').val(primaryID);
    });

    $('#deleteElearningLinkModal').kendoDialog({
        width: '400px',
        title: 'Delete',
        content:
            "Are you sure you want to Delete Elearning Link ? <input type='hidden' name='id' id='deleteElearningLinkId' />",
        actions: [
            { text: 'Close' },
            {
                text: 'Yes',
                primary: true,
                action: function () {
                    deleteContractFunction(
                        $('#deleteElearningLinkModal').find('#deleteElearningLinkId').val()
                    );
                },
            },
        ],
        animation: {
            open: {
                effects: 'fade:in',
            },
        },
        visible: false,
    });

    function deleteContractFunction(primaryID) {
        if (primaryID > 0) {
            ajaxcallwithMethodKendo(
                site_url + 'api/delete-elearning-link',
                { id: primaryID },
                'POST',
                function (resultData) {
                    $('#elearningLinkList').data('kendoGrid').refresh();
                    $('#elearningLinkList').data('kendoGrid').dataSource.read();
                    notificationDisplay(resultData.message, '', resultData.status);
                }
            );
        }
    }

    $('body').on('click', '.cancelBtn', function (e) {
        e.preventDefault();
        $(this).closest('.k-window-content').data('kendoWindow').close();
    });
});
