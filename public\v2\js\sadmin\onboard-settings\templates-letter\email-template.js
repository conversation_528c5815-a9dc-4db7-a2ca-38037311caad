let myEditor;
let myEditor1;
$(document).ready(function () {
    $('#viewEmailContentTagParameterModal').kendoWindow(
        openCenterWindowWithHeight('View Parameters', 60, 90, 5, 20)
    );

    $('#emailPreviewFileManagerModal').kendoWindow(
        openCenterWindowWithHeight('Preview Document', 60, 90, 5, 20)
    );
    function openCenterWindowWithHeight(
        titleText,
        widthVal = 34,
        heightVal = 25,
        topVal = 25,
        leftVal = 33
    ) {
        return {
            title: titleText,
            width: widthVal + '%',
            height: heightVal + '%',
            actions: ['close'],
            draggable: false,
            resizable: false,
            modal: true,
            position: {
                top: topVal + '%',
                left: leftVal + '%',
            },
            animation: {
                open: {
                    effects: 'fade:in',
                    duration: 300,
                },
                close: {
                    effects: 'fade:out',
                    duration: 300,
                },
            },
        };
    }
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
        },
    });

    let selectedEmailTemplateId = null;

    $('#loader').kendoLoader();
    kendo.ui.progress.messages = {
        loading:
            '<div class="vue-simple-spinner animate-spin" style="position:absolute;top:50%;left:50%;transfrom:translate(-50%,-50%); margin: 0px auto; border-radius: 100%; border-color: rgb(50, 157, 243) rgb(238, 238, 238) rgb(238, 238, 238); border-style: solid; border-width: 3px; border-image: none 100% / 1 / 0 stretch; width: 30px; height: 30px;"></div>',
    };

    kendo.ui.progress($('.maincontainer'), true);

    manageEmailTemplate();

    function manageEmailTemplate() {
        let dataArr = {};
        $.ajax({
            type: 'POST',
            url: site_url + 'api/get-email-template-data',
            dataType: 'json',
            data: dataArr,
            success: function (response) {
                let stepArr = [];
                let wizarStepArr = [];
                $.each(response.data, function (index, row) {
                    let iconTemplate =
                        row.locked == 1
                            ? "<img src='" +
                              site_url +
                              "/v2/img/Lock.svg' class='h-6 w-6' alt='overseasIcon'><span class='w-full truncate'><strong class='action-div'> #:label# </strong></span>"
                            : "<span class='w-full truncate'><strong class='action-div'> #:label# </strong></span>";
                    stepArr.push({
                        label: row.template_name,
                        id: row.id,
                        iconTemplate: kendo.template($('#listMenuItem').html()),
                    });
                    wizarStepArr.push({
                        title: row.template_name,
                        content: kendo.template($('#commonTemplate').html())(row),
                    });
                });
                let stepper = $('#emailDocumentStepper')
                    .kendoStepper({
                        linear: false,
                        steps: stepArr,
                        // label:false,
                        orientation: 'vertical',
                        select: onSelect,
                        activate: function (e) {
                            let stepID = e.step.options.index;
                            wizard.select(stepID);
                            selectedEmailTemplateId = $(
                                '#emailTemplateWizard-' + stepID + ' #id'
                            ).val();
                            refreshGrid('uploadedDocuments');
                        },
                    })
                    .data('kendoStepper');

                //kendo Wizards
                var templateWizard = $('#emailTemplateWizard');
                let wizard = templateWizard
                    .kendoWizard({
                        validateForms: true,
                        steps: wizarStepArr,
                    })
                    .data('kendoWizard');
                templateWizard.find('.k-stepper').hide();
                templateWizard.find('.k-wizard-buttons').hide();

                // let selectedEmailTemplateId = null;
                // if (stepArr.length > 0) {
                //     selectedEmailTemplateId = $("#emailTemplateWizard-" +     stepper.select().options.index +    " #id").val();
                // }

                // console.log('selectedEmailTemplateId',selectedEmailTemplateId);
                //uploaded documents Grid
                $('#uploadedDocuments').kendoGrid({
                    dataSource: {
                        type: 'json',
                        transport: {
                            read: {
                                url: site_url + 'api/uploaded-email-template-documents',
                                dataType: 'json',
                                type: 'POST',
                                data: {
                                    email_template_id: selectedEmailTemplateId,
                                },
                            },
                            parameterMap: function (data, operation) {
                                if (operation == 'read') {
                                    data.email_template_id = selectedEmailTemplateId;
                                    return data;
                                }
                            },
                        },
                        schema: {
                            data: 'data.data',
                            total: 'data.total',
                            model: {
                                id: 'id',
                                fields: {
                                    file: { type: 'string' },
                                    added_by: { type: 'string' },
                                    date: { type: 'string' },
                                },
                            },
                        },
                        pageSize: 25,
                        serverPaging: true,
                        serverFiltering: true,
                        serverSorting: true,
                    },
                    dataBound: onBoundDocuments,
                    columns: [
                        {
                            template:
                                "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: file #</div>",
                            field: 'file',
                            title: 'NAME',
                        },
                        {
                            template:
                                "<div class='flex items-center text-sm leading-5 font-normal text-gray-600'>#: added_by #</div>",
                            field: 'added_by',
                            title: 'ADDED BY',
                        },
                        {
                            template:
                                '<div class=\'flex items-center text-sm leading-5 font-normal text-gray-600 action-div\'>#: kendo.toString(date, "dd MMM yyyy") #</div>',
                            field: 'date',
                            title: 'DATE ADDED',
                        },
                        {
                            template: function (dataItem) {
                                return actionDocument(dataItem.id, dataItem.file_path);
                            },
                            field: '',
                            title: 'ACTION',
                            width: 180,
                            filterable: false,
                            sortable: false,
                        },
                    ],
                });

                //upload document
                $('#attachments').kendoUpload({
                    async: {
                        chunkSize: 20000000, // bytes
                        saveUrl: site_url + 'api/upload-email-template-document',
                        autoUpload: true,
                    },
                    validation: {
                        allowedExtensions: ['.pdf'],
                        maxFileSize: 20000000,
                    },
                    upload: function (e) {
                        e.data = {
                            email_template_id: $(
                                '#emailTemplateWizard-' + stepper.select().options.index + ' #id'
                            ).val(),
                        };
                    },
                    success: function (e) {
                        if (e.operation == 'upload') {
                            for (var i = 0; i < e.files.length; i++) {
                                var file = e.files[i].rawFile;
                                if (file) {
                                    var reader = new FileReader();
                                    reader.onloadend = function () {
                                        refreshGrid('uploadedDocuments');
                                        notificationDisplay(
                                            'Email Template Document uploaded successfully.',
                                            '',
                                            'success'
                                        );
                                    };
                                    reader.readAsDataURL(file);
                                }
                            }
                        }
                    },
                    error: function (e) {
                        notificationDisplay(
                            'Something will be wrong. Please try again.',
                            '',
                            'error'
                        );
                    },
                });

                //setdata onload
                let editCourseTemplateModal = $(document).find('#editEmailTemplateForm');
                editCourseTemplateModal.find('#editId').val($('#emailTemplateWizard-0 #id').val());
                editCourseTemplateModal
                    .find('#email_subject')
                    .val($('#emailTemplateWizard-0 #email_subject').val());
                editCourseTemplateModal
                    .find('#template_name')
                    .val($('#emailTemplateWizard-0 #template_name').val());
                editCourseTemplateModal
                    .find('#recipient')
                    .data('kendoDropDownList')
                    .value($('#emailTemplateWizard-0 #recipient').val());
                editCourseTemplateModal
                    .find('#content')
                    .val($('#emailTemplateWizard-0 #content').val());
                if ($('#emailTemplateWizard-0 #locked').val() == 1) {
                    $('#editEmailTemplateForm').find('#template_name').attr('disabled', true);
                } else {
                    $('#editEmailTemplateForm').find('#template_name').attr('disabled', false);
                }

                if (stepArr.length > 0) {
                    myEditor1.setData($('#emailTemplateWizard-0 #content').html());
                }
                $('.defaultHide').removeClass('hidden');
                kendo.ui.progress($('.maincontainer'), false);
            },
        });
    }

    function actionDocument(id, file_path) {
        return (
            '<div class="action-div  flex justify-start items-center space-x-1">' +
            '<button type="button" data-path="' +
            file_path +
            '"  class="previewDocument  action-menu flex-col space-x-3 items-start justify-start p-2 cursor-pointer" data-id = "' +
            id +
            '"><span class="k-icon k-i-preview k-icon-preview"></span></button>' +
            '<button  type="button" class="deleteDocumentBtn action-menu flex-col space-x-3 items-start justify-start p-2 cursor-pointer" data-id = "' +
            id +
            '"><span class="k-icon k-i-delete k-icon-delete"></span></button>' +
            '</div>'
        );
    }

    function refreshGrid(gridID) {
        $('#' + gridID)
            .data('kendoGrid')
            .refresh();
        $('#' + gridID)
            .data('kendoGrid')
            .dataSource.read();
    }

    function onBoundDocuments(e) {
        if ($('#uploadedDocuments').data('kendoGrid').dataSource.total() > 0) {
            $(document).find('.showUploadedDocuments').show();
        } else {
            $(document).find('.showUploadedDocuments').hide();
        }
        setTimeout(function () {
            setFilterIcon('#uploadedDocuments');
        }, 100);
    }

    $('#editEmailTemplateForm').kendoForm({
        orientation: 'vertical',
        formData: {},
        items: [
            {
                field: 'email_subject',
                label: 'Email Subject',
                attributes: {
                    placeholder: 'Enter Email Subject',
                    class: '!px-3 !h-9 !indent-0',
                },
                validation: { required: true },
            },
            {
                field: 'template_name',
                label: 'Template Name',
                attributes: {
                    placeholder: 'Enter Template Name',
                    class: '!px-3 !h-9 !indent-0',
                },
                validation: { required: true },
            },
            {
                field: 'recipient',
                label: 'Recipient',
                editor: 'DropDownList',
                editorOptions: {
                    optionLabel: 'Select Recipient',
                    dataSource: getDropdownDataSource('get-constant-data', {
                        action: 'arrTemplateRecipient',
                    }),
                    dataValueField: 'Id',
                    dataTextField: 'Name',
                },
                validation: { required: true },
                attributes: {
                    class: '!h-9',
                },
            },
            {
                field: 'content',
                label: 'Template Editor',
                hint: "Note : Press '{' for tag",
                editor: function (container, options) {
                    $(
                        "<textarea class='k-textarea' id='edit_content' name='" +
                            options.field +
                            "' data-bind='value: " +
                            options.field +
                            "'></textarea>"
                    ).appendTo(container);
                },
            },
            {
                field: 'attachments',
                label: 'Attachments',
                editor: function (container, options) {
                    $(
                        " <input  id='attachments' type='file' name='" + options.field + "' />"
                    ).appendTo(container);
                },
            },
        ],
        buttonsTemplate:
            '<div class="w-full inline-flex space-x-4 items-center justify-end">\n' +
            '<div class="float-right flex space-x-4 items-center justify-end">\n' +
            '<input type="hidden" id="editId" name="id">\n' +
            '<button type="button" class="updateEmailTemplate flex justify-center h-9 px-3 py-2 bg-primary-blue-500 hover:shadow-lg rounded-lg focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-primary-blue-600">\n' +
            '<p class="text-sm text-white">Update</p>\n' +
            '</button>\n' +
            '</div>\n' +
            '</div>',
    });

    $('body').on('click', '.updateEmailTemplate', function (e) {
        $('#edit_content').val(myEditor1.getData());
        let serializeArr = $(document)
            .find('#editEmailTemplateForm')
            .find('input[name], select[name], textarea[name]')
            .serializeArray();
        let dataArr = {};
        $(serializeArr).each(function (id, field) {
            dataArr[field.name] = field.value;
        });
        ajaxcallwithMethodKendo(
            site_url + 'api/save-email-template',
            dataArr,
            'POST',
            function (resultData) {
                manageEmailTemplate();
                notificationDisplay(resultData.message, '', resultData.status);
            }
        );
    });

    function onSelect(e) {
        const isDeleteBtn = e.originalEvent.target.closest('#deleteEmailTemplate');
        if (isDeleteBtn) {
            e.preventDefault();
            return;
        }
        let editCourseTemplateModal = $(document).find('#editEmailTemplateForm');
        editCourseTemplateModal
            .find('#editId')
            .val($('#emailTemplateWizard-' + e.step.options.index + ' #id').val());
        editCourseTemplateModal
            .find('#email_subject')
            .val($('#emailTemplateWizard-' + e.step.options.index + ' #email_subject').val());
        editCourseTemplateModal
            .find('#template_name')
            .val($('#emailTemplateWizard-' + e.step.options.index + ' #template_name').val());
        editCourseTemplateModal
            .find('#recipient')
            .data('kendoDropDownList')
            .value($('#emailTemplateWizard-' + e.step.options.index + ' #recipient').val());
        editCourseTemplateModal
            .find('#content')
            .val($('#emailTemplateWizard-' + e.step.options.index + ' #content').val());
        myEditor1.setData($('#emailTemplateWizard-' + e.step.options.index + ' #content').html());
        if ($('#emailTemplateWizard-' + e.step.options.index + ' #locked').val() == 1) {
            $('#editEmailTemplateForm').find('#template_name').attr('disabled', true);
        } else {
            $('#editEmailTemplateForm').find('#template_name').attr('disabled', false);
        }

        const ff = $('#emailTemplateWizard-' + e.step.options.index + ' #id').val();
        console.log('ff', ff);
    }

    function openCenterWindow(titleText, widthVal = 50, topVal = 5, leftVal = 25) {
        return {
            title: titleText,
            width: widthVal + '%',
            // height: "70%",
            actions: ['close'],
            draggable: false,
            resizable: false,
            modal: true,
            position: {
                top: topVal + '%',
                left: leftVal + '%',
            },
            animation: defaultCloseAnimation(),
        };
    }

    // function kendoWindowOpen(windowID) {
    //     let kendoWindow = $(document).find(windowID);
    //     kendoWindow.getKendoWindow().open();
    //     kendoWindow
    //         .parent("div")
    //         .find(".k-window-titlebar")
    //         .addClass(
    //             "titlebar-sms-modal bg-gradient-to-l from-green-400 to-blue-500",
    //         )
    //         .find(".k-window-title")
    //         .addClass("text-lg font-medium leading-normal text-white");
    // }

    $('#addEmailTemplateModal').kendoWindow(openCenterWindow('Add Email Template'));

    $('body').on('click', '.addEmailTemplate', function () {
        kendoWindowOpen('#addEmailTemplateModal');
        $('#addEmailTemplateForm')[0].reset();
    });

    $('#addEmailTemplateForm').kendoForm({
        orientation: 'vertical',
        layout: 'grid',
        type: 'group',
        grid: { cols: 6, gutter: 10 },
        formData: {},
        items: [
            {
                field: 'email_subject',
                label: 'Email Subject',
                attributes: {
                    placeholder: 'Enter Email Subject',
                    class: '!h-9 !px-3 !indent-0',
                },
                validation: { required: true },
                colSpan: 3,
            },
            {
                field: 'template_name',
                label: 'Template Name',
                attributes: {
                    placeholder: 'Enter Template Name',
                    class: '!h-9 !px-3 !indent-0',
                },
                validation: { required: true },
                colSpan: 3,
            },
            {
                field: 'recipient',
                label: 'Recipient',
                editor: 'DropDownList',
                editorOptions: {
                    optionLabel: 'Select Recipient',
                    dataSource: getDropdownDataSource('get-constant-data', {
                        action: 'arrTemplateRecipient',
                    }),
                    dataValueField: 'Id',
                    dataTextField: 'Name',
                },
                validation: { required: true },
                colSpan: 6,
                attributes: {
                    class: '!h-9',
                },
            },
            {
                field: 'content',
                label: 'Template Editor',
                editor: function (container, options) {
                    $(
                        "<textarea class='k-textarea' id='add_content' name='" +
                            options.field +
                            "' data-bind='value: " +
                            options.field +
                            "'></textarea>"
                    ).appendTo(container);
                },
                colSpan: 6,
            },
        ],
        buttonsTemplate:
            '<div class="w-full inline-flex space-x-4 items-center justify-end">\n' +
            '<div class="float-right flex space-x-4 items-center justify-end">\n' +
            '<button type="button" class="flex justify-center px-6 py-2 bg-white h-9 shadow border hover:shadow-lg rounded-lg  border-gray-300 focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-gray-400 cancelBtn">\n' +
            '<p type="button" class="text-sm text-gray-700">Cancel</p>\n' +
            '</button>\n' +
            '<button type="button" class="createEmailTemplate flex justify-center h-9 px-3 py-2 bg-primary-blue-500 hover:shadow-lg rounded-lg focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-primary-blue-600">\n' +
            '<p class="text-sm text-white">Add Email Template</p>\n' +
            '</button>\n' +
            '</div>\n' +
            '</div>',
    });

    $('body').on('click', '.previewDocument', function (e) {
        let download_path = $(this).attr('data-path');
        kendoWindowOpen('#emailPreviewFileManagerModal');
        let previewDetails = kendo.template($('#emailFilePreviewTemplate').html())({
            previewId: 'notesAttachment',
        });
        $(document).find('#emailPreviewFileManagerModal').html(previewDetails);
        $.when(
            $.getScript('https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.2.2/pdf.js'),
            $.getScript('https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.2.2/pdf.worker.js')
        )
            .done(function () {
                window.pdfjsLib.GlobalWorkerOptions.workerSrc =
                    'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.2.2/pdf.worker.js';
            })
            .then(function () {
                $('#emailPreviewFileManagerModal')
                    .find('#documentId')
                    .kendoPDFViewer({
                        pdfjsProcessing: {
                            file: download_path,
                        },
                        width: '100%',
                        height: '100%',
                    });
            });
    });

    $('body').on('click', '.createEmailTemplate', function (e) {
        $('#add_content').val(myEditor.getData());
        let validator = $('#addEmailTemplateForm').kendoValidator().data('kendoValidator');
        if (!validator.validate()) {
            return false;
        }
        let serializeArr = $(document)
            .find('#addEmailTemplateForm')
            .find('input[name], select[name], textarea[name]')
            .serializeArray();
        let dataArr = {};

        $(serializeArr).each(function (id, field) {
            dataArr[field.name] = field.value;
        });
        if (dataArr) {
            ajaxcallwithMethodKendo(
                site_url + 'api/save-email-template',
                dataArr,
                'POST',
                function (resultData) {
                    $(document).find('#addEmailTemplateModal').getKendoWindow().close();
                    manageEmailTemplate();
                    notificationDisplay(resultData.message, '', resultData.status);
                }
            );
        }
    });

    $('body').on('click', '.cancelBtn', function (e) {
        e.preventDefault();
        $(this).closest('.k-window-content').data('kendoWindow').close();
    });

    if ($(document).find('#add_content').html() == '') {
        CKEDITOR.ClassicEditor.create(document.querySelector('#add_content'), {
            ckfinder: {
                uploadUrl: site_url + 'api/upload-file-email-text-editor',
            },
            mention: {
                feeds: [
                    {
                        marker: '{',
                        feed: window.tagJson,
                        minimumCharacters: 0,
                    },
                ],
            },
            removePlugins: [
                'RealTimeCollaborativeComments',
                'RealTimeCollaborativeTrackChanges',
                'RealTimeCollaborativeRevisionHistory',
                'PresenceList',
                'Comments',
                'TrackChanges',
                'TrackChangesData',
                'RevisionHistory',
                'Pagination',
                'WProofreader',
                'MathType',
            ],
        })
            .then((editor) => {
                editor.ui.view.editable.element.style.height = '200px';
                myEditor = editor;
            })
            .catch((error) => {
                console.error(error);
            });
    }

    if ($(document).find('#edit_content').html() == '') {
        CKEDITOR.ClassicEditor.create(document.querySelector('#edit_content'), {
            ckfinder: {
                uploadUrl: site_url + 'api/upload-file-email-text-editor',
            },
            mention: {
                feeds: [
                    {
                        marker: '{',
                        feed: window.tagJson,
                        minimumCharacters: 0,
                    },
                ],
            },
            removePlugins: [
                'RealTimeCollaborativeComments',
                'RealTimeCollaborativeTrackChanges',
                'RealTimeCollaborativeRevisionHistory',
                'PresenceList',
                'Comments',
                'TrackChanges',
                'TrackChangesData',
                'RevisionHistory',
                'Pagination',
                'WProofreader',
                'MathType',
            ],
        })
            .then((editor1) => {
                editor1.ui.view.editable.element.style.height = '300px';
                myEditor1 = editor1;
            })
            .catch((error) => {
                console.error(error);
            });
    }

    $('body').on('click', '.deleteDocumentBtn', function () {
        let primaryID = $(this).attr('data-id');
        $('#deleteEmailDocumentModal').data('kendoDialog').open();
        $('#deleteEmailDocumentModal').find('#deleteEmailDocumentId').val(primaryID);
    });
    $('#deleteEmailDocumentModal').kendoDialog({
        width: '400px',
        title: 'Delete',
        content:
            "Are you sure you want to Delete File? <input type='hidden' name='id' id='deleteEmailDocumentId' />",
        actions: [
            { text: 'Close' },
            {
                text: 'Yes',
                primary: true,
                action: function () {
                    deleteEmailDocumentFunction(
                        $('#deleteEmailDocumentModal').find('#deleteEmailDocumentId').val()
                    );
                },
            },
        ],
        animation: {
            open: {
                effects: 'fade:in',
            },
        },
        open: onOpenDeleteDialog('#deleteEmailDocumentModal'),
        visible: false,
    });
    function deleteEmailDocumentFunction(primaryID) {
        if (primaryID > 0) {
            kendo.ui.progress($(document.body), true);
            $.ajax({
                type: 'POST',
                url: site_url + 'api/delete-email-template-document',
                dataType: 'json',
                data: { id: primaryID },
                success: function (response) {
                    kendo.ui.progress($(document.body), false);
                    $('#uploadedDocuments').data('kendoGrid').refresh();
                    $('#uploadedDocuments').data('kendoGrid').dataSource.read();
                    notificationDisplay(response.message, '', response.status);
                },
            });
        }
    }
    function onOpenDeleteDialog(gridID) {
        $(gridID)
            .parent()
            .find('.k-dialog-titlebar')
            .addClass('bg-gradient-to-l from-green-400 to-blue-500');
        // $(gridID)
        //     .parent()
        //     .find("button:first")
        //     .addClass(
        //         "bg-white border rounded-lg border-gray-300 text-gray-700 hover:shadow hover:bg-gray-50 focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-primary-blue-500"
        //     );
        $(gridID)
            .parent()
            .find('.k-primary')
            .addClass(
                'text-white bg-primary-blue-500 hover:shadow-lg rounded-lg focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-indigo-500'
            );
    }
    $('body').on('click', '.showEmailContentTags', function () {
        startAjaxLoader();
        $.ajax({
            type: 'POST',
            url: site_url + 'api/get-email-default-parameter',
            dataType: 'json',
            data: {},
            success: function (response) {
                stopAjaxLoader();
                let recordPaymentHtml = kendo.template(
                    $('#viewEmailContentTagParameterTemplate').html()
                )(response);
                $(document).find('#viewEmailContentTagParameterHtml').html(recordPaymentHtml);
                kendoWindowOpen('#viewEmailContentTagParameterModal');
            },
        });
    });
    jQuery('.searchInputEmailTags').keyup(function () {
        var filter = jQuery(this).val();

        jQuery('.viewEmailContentTagParameterSearch div').each(function () {
            if (jQuery(this).find('span').text().search(new RegExp(filter, 'i')) < 0) {
                jQuery(this).find('.searchEmailDiv').hide();
            } else {
                jQuery(this).find('.searchEmailDiv').show();
            }
        });
    });
    $(document).on('click', '.copy_data', function () {
        $(document).find('.copy_data').removeClass('active');
        $(this).addClass('active');
        var textVal = $(this).attr('data-text');
        var $temp = $("<input name='copy'>");
        $('body').append($temp);
        $temp.val(textVal).select();
        document.execCommand('copy');
        $temp.remove();
        notificationDisplay('Copied', '', 'success');
    });
    function startAjaxLoader() {
        $(document)
            .on('ajaxStart', function () {
                kendo.ui.progress($(document.body), true);
            })
            .on('ajaxStop', function () {
                kendo.ui.progress($(document.body), false);
            });
    }

    function stopAjaxLoader() {
        $(document)
            .on('ajaxStart', function () {
                kendo.ui.progress($(document.body), false);
            })
            .on('ajaxStop', function () {
                kendo.ui.progress($(document.body), false);
            });
    }

    $('body').on('click', '.deleteEmailTemplate', function (e) {
        e.preventDefault();
        let primaryID = $(this).attr('data-id');
        confirmDialog(
            '#confirmDeleteModal',
            'Delete Item?',
            'Are you sure you want to delete this item?',
            'warning',
            function (e) {
                console.log('User confirmed deletion');
                ajaxActionV2(
                    'api/email-templates/' + primaryID,
                    'DELETE',
                    {},
                    function (resultData) {
                        manageEmailTemplate();
                        notificationDisplay(resultData.message, '', resultData.status);
                    }
                );
            }
        );
    });
});
