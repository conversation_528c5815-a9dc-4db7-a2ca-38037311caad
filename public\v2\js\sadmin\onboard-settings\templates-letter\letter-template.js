let myEditor;
let myEditor1;
$(document).ready(function () {
    $('#viewContentTagParameterModal').kendoWindow(
        openCenterWindowWithHeight('View Parameters', 80, 90, 5, 10)
    );
    $('#letterPreviewFileManagerModal').kendoWindow(
        openCenterWindowWithHeight('Preview Document', 60, 90, 5, 20)
    );
    addModalClassToWindows(['#viewContentTagParameterModal', '#letterPreviewFileManagerModal']);
    function openCenterWindowWithHeight(
        titleText,
        widthVal = 34,
        heightVal = 25,
        topVal = 25,
        leftVal = 33
    ) {
        return {
            title: titleText,
            width: widthVal + '%',
            height: heightVal + '%',
            actions: ['close'],
            draggable: false,
            resizable: false,
            modal: true,
            position: {
                top: topVal + '%',
                left: leftVal + '%',
            },
            animation: {
                open: {
                    effects: 'fade:in',
                    duration: 300,
                },
                close: {
                    effects: 'fade:out',
                    duration: 300,
                },
            },
        };
    }

    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
        },
    });

    $('#loader').kendoLoader();
    kendo.ui.progress.messages = {
        loading:
            '<div class="vue-simple-spinner animate-spin" style="position:absolute;top:50%;left:50%;transfrom:translate(-50%,-50%); margin: 0px auto; border-radius: 100%; border-color: rgb(50, 157, 243) rgb(238, 238, 238) rgb(238, 238, 238); border-style: solid; border-width: 3px; border-image: none 100% / 1 / 0 stretch; width: 30px; height: 30px;"></div>',
    };

    kendo.ui.progress($('.maincontainer'), true);

    manageLetterTemplate();

    function manageLetterTemplate() {
        let dataArr = {};
        $.ajax({
            type: 'POST',
            url: site_url + 'api/get-letter-template-data',
            dataType: 'json',
            data: dataArr,
            success: function (response) {
                let stepArr = [];
                let wizarStepArr = [];
                console.log(response.data);
                if (response.data.length > 0) {
                    $('.noLetterDataDiv').hide();
                    $('.letterDataDiv').show();
                } else {
                    $('.noLetterDataDiv').show();
                    $('.letterDataDiv').hide();
                }
                $.each(response.data, function (index, row) {
                    console.log('rows', row);
                    stepArr.push({
                        label: row.letter_name,
                        id: row.id,
                        iconTemplate: kendo.template($('#listMenuItem').html()),
                    });
                    wizarStepArr.push({
                        title: row.letter_name,
                        content: kendo.template($('#commonTemplate').html())(row),
                    });
                });
                let stepper = $('#letterDocumentStepper')
                    .kendoStepper({
                        linear: false,
                        steps: stepArr,
                        // label:false,
                        orientation: 'vertical',
                        select: onSelect,
                        activate: function (e) {
                            let stepID = e.step.options.index;
                            wizard.select(stepID);
                            refreshGrid('uploadedDocuments');
                        },
                    })
                    .data('kendoStepper');

                //kendo Wizards
                var templateWizard = $('#letterTemplateWizard');
                let wizard = templateWizard
                    .kendoWizard({
                        validateForms: true,
                        steps: wizarStepArr,
                    })
                    .data('kendoWizard');
                templateWizard.find('.k-stepper').hide();
                templateWizard.find('.k-wizard-buttons').hide();

                let selectedLetterTemplateId = null;
                if (stepArr.length > 0) {
                    selectedLetterTemplateId = $(
                        '#letterTemplateWizard-' + stepper.select().options.index + ' #id'
                    ).val();
                }
                //uploaded documents Grid
                $('#uploadedDocuments').kendoGrid({
                    dataSource: {
                        type: 'json',
                        transport: {
                            read: {
                                url: site_url + 'api/uploaded-letter-template-documents',
                                dataType: 'json',
                                type: 'POST',
                                data: {
                                    letter_id: selectedLetterTemplateId,
                                },
                            },
                            parameterMap: function (data, operation) {
                                if (operation == 'read') {
                                    data.letter_id = selectedLetterTemplateId;
                                    return data;
                                }
                            },
                        },
                        schema: {
                            data: 'data.data',
                            total: 'data.total',
                            model: {
                                id: 'id',
                                fields: {
                                    file: { type: 'string' },
                                    added_by: { type: 'string' },
                                    date: { type: 'string' },
                                },
                            },
                        },
                        pageSize: 25,
                        serverPaging: true,
                        serverFiltering: true,
                        serverSorting: true,
                    },
                    dataBound: onBoundDocuments,
                    columns: [
                        {
                            template:
                                "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: file #</div>",
                            field: 'file',
                            title: 'NAME',
                        },
                        {
                            template:
                                "<div class='flex items-center text-sm leading-5 font-normal text-gray-600'>#: added_by #</div>",
                            field: 'added_by',
                            title: 'ADDED BY',
                        },
                        {
                            template:
                                '<div class=\'flex items-center text-sm leading-5 font-normal text-gray-600 action-div\'>#: kendo.toString(date, "dd MMM yyyy") #</div>',
                            field: 'date',
                            title: 'DATE ADDED',
                        },
                        {
                            template: function (dataItem) {
                                return actionDocument(dataItem.id, dataItem.file_path);
                            },
                            field: '',
                            title: 'ACTION',
                            width: 180,
                            filterable: false,
                            sortable: false,
                        },
                    ],
                });

                //upload document
                $('#attachments').kendoUpload({
                    async: {
                        chunkSize: 20000000, // bytes
                        saveUrl: site_url + 'api/upload-letter-template-document',
                        autoUpload: true,
                    },
                    validation: {
                        allowedExtensions: ['.pdf', '.jpg', '.png', '.jpeg'],
                        maxFileSize: 20000000,
                    },
                    upload: function (e) {
                        e.data = {
                            letter_id: $(
                                '#letterTemplateWizard-' + stepper.select().options.index + ' #id'
                            ).val(),
                        };
                    },
                    success: function (e) {
                        if (e.operation == 'upload') {
                            for (var i = 0; i < e.files.length; i++) {
                                var file = e.files[i].rawFile;
                                if (file) {
                                    var reader = new FileReader();
                                    reader.onloadend = function () {
                                        refreshGrid('uploadedDocuments');
                                        notificationDisplay(
                                            'Letter Template Document uploaded successfully.',
                                            '',
                                            'success'
                                        );
                                    };
                                    reader.readAsDataURL(file);
                                }
                            }
                        }
                    },
                    error: function (e) {
                        notificationDisplay(
                            'Something will be wrong. Please try again.',
                            '',
                            'error'
                        );
                    },
                });

                //setdata onload
                let editCourseTemplateModal = $(document).find('#editLetterTemplateForm');
                editCourseTemplateModal.find('#editId').val($('#letterTemplateWizard-0 #id').val());
                editCourseTemplateModal
                    .find('#recipient')
                    .data('kendoDropDownList')
                    .value($('#letterTemplateWizard-0 #recipient').val());
                editCourseTemplateModal
                    .find('#letter_name')
                    .val($('#letterTemplateWizard-0 #letter_name').val());
                editCourseTemplateModal
                    .find('#category_id')
                    .data('kendoDropDownList')
                    .value($('#letterTemplateWizard-0 #category_id').val());
                let postArr = {
                    category_id: $('#letterTemplateWizard-0 #category_id').val(),
                };
                let report = $('#letterTemplateWizard-0 #report').val();
                $('#report_id').kendoDropDownList({
                    autoWidth: true,
                    dataTextField: 'Name',
                    dataValueField: 'Id',
                    filter: 'contains',
                    value: report,
                    dataSource: getDropdownDataSource('get-report-name', postArr),
                    dataBound: function (e) {
                        this.value(report);
                    },
                });
                editCourseTemplateModal
                    .find(
                        'input[type=radio][name="track_letter"][value="' +
                            $('#letterTemplateWizard-0 #track_letter').val() +
                            '"]'
                    )
                    .trigger('click');
                editCourseTemplateModal.find('#track_letter').getKendoRadioGroup().enable(false, 1);
                editCourseTemplateModal
                    .find('#track_color')
                    .val($('#letterTemplateWizard-0 #track_color').val());
                if (stepArr.length > 0) {
                    myEditor1.setData($('#letterTemplateWizard-0 #content').html());
                }

                $('.defaultHide').removeClass('hidden');
                kendo.ui.progress($('.maincontainer'), false);
            },
        });
    }

    function actionDocument(id, file_path) {
        return (
            '<div class="action-div  flex justify-start items-center space-x-1">' +
            '<div type="button" data-path="' +
            file_path +
            '"   class="previewLetterDocument action-menu flex-col space-x-3 items-start justify-start p-2 cursor-pointer" data-id = "' +
            id +
            '"><span class="k-icon k-i-preview k-icon-preview"></span></div>' +
            '<div type="button" class="deleteLetterDocumentBtn action-menu flex-col space-x-3 items-start justify-start p-2 cursor-pointer" data-id = "' +
            id +
            '"><span class="k-icon k-i-delete k-icon-delete"></span></div>' +
            '</div>'
        );
    }

    function refreshGrid(gridID) {
        if ($('#' + gridID).data('kendoGrid')) {
            $('#' + gridID)
                .data('kendoGrid')
                .refresh();
            $('#' + gridID)
                .data('kendoGrid')
                .dataSource.read();
        }
    }

    function onBoundDocuments(e) {
        if ($('#uploadedDocuments').data('kendoGrid').dataSource.total() > 0) {
            $(document).find('.showUploadedDocuments').show();
        } else {
            $(document).find('.showUploadedDocuments').hide();
        }
        setTimeout(function () {
            setFilterIcon('#uploadedDocuments');
        }, 100);
    }

    function setDropdownList(fieldID, api_url, postArr = {}) {
        $('#' + fieldID).kendoDropDownList({
            autoWidth: true,
            dataTextField: 'Name',
            dataValueField: 'Id',
            filter: 'contains',
            dataSource: getDropdownDataSource(api_url, postArr),
            dataBound: function (e) {
                this.select(0);
                this.trigger('change');
                this.trigger('select');
            },
        });
    }

    let editLetterTemplateForm = $('#editLetterTemplateForm').kendoForm({
        orientation: 'vertical',
        formData: {},
        items: [
            {
                field: 'letter_name',
                label: 'Letter Name',
                // editor: function (container, options) {
                //     $(
                //         '<input type="text" clsss="k-widget k-textbox mr-2 !px-3 !h-9 !indent-0" id="' +
                //             options.field +
                //             '" name="' +
                //             options.field +
                //             '"  />',
                //     )
                //         .appendTo(container)
                //         .kendoTextBox();
                // },
                editor: function (container, options) {
                    $(
                        '<input placeholder="Letter name" class="k-widget k-textbox mr-2 !px-3 !h-9 !indent-0" id="' +
                            options.field +
                            '" name="' +
                            options.field +
                            '"  />'
                    ).appendTo(container);
                },
            },
            {
                // field: "recipient",
                // label: "Recipient",
                // editor: function (container, options) {
                //     $('<input type="text" id="'+ options.field +'" name="' + options.field + '"  />')
                //         .appendTo(container)
                //         .kendoTextBox();
                // }
                field: 'recepient',
                id: 'recipient',
                editor: 'DropDownList',
                label: 'Recipient',
                editorOptions: {
                    optionLabel: 'Select Recipient',
                    dataSource: getDropdownDataSource('get-constant-data', {
                        action: 'arrLetterRecipient',
                    }),
                    dataValueField: 'Id',
                    dataTextField: 'Name',
                },
                validation: { required: true },
                attributes: {
                    class: '!h-9',
                },
            },
            {
                field: 'category',
                id: 'category_id',
                editor: 'DropDownList',
                label: 'Select Category',
                editorOptions: {
                    dataSource: category,
                    dataValueField: 'Id',
                    dataTextField: 'Name',
                    filter: 'contains',
                    filterInput: {
                        width: '100%',
                    },
                    select: function (e) {
                        if (e.dataItem) {
                            let postArr = { category_id: e.dataItem.Id };
                            setDropdownList('report_id', 'get-report-name', postArr);
                        }
                    },
                },
                attributes: {
                    class: '!h-9',
                },
            },
            {
                field: 'report_id',
                label: 'Select Report',
                editor: 'DropDownList',
                editorOptions: {
                    dataSource: report,
                    dataValueField: 'Id',
                    dataTextField: 'Name',
                    filter: 'contains',
                    filterInput: {
                        width: '100%',
                    },
                },
                attributes: {
                    class: '!h-9',
                },
            },
            {
                field: 'track_letter',
                editor: 'RadioGroup',
                label: 'Track Letter',
                editorOptions: {
                    items: ['Yes', 'No'],
                    layout: 'horizontal',
                    labelPosition: 'after',
                    change: function (e) {
                        if (e.newValue == 'Yes') {
                            $(document)
                                .find('#editLetterTemplateForm')
                                .find('#track_color-form-label')
                                .closest('.k-form-field')
                                .show();
                        } else {
                            $(document)
                                .find('#editLetterTemplateForm')
                                .find('#track_color-form-label')
                                .closest('.k-form-field')
                                .hide();
                        }
                    },
                },
            },
            {
                field: 'parameter_insert',
                label: 'Input Parameters',
                editor: function (container, options) {
                    $(
                        " <div class='flex'><input placeholder='Input Parameters' class='k-widget k-textbox mr-2 !px-3 !h-9 !indent-0' id='" +
                            options.field +
                            "' type='input' name='" +
                            options.field +
                            "' /> <button type='button' class='insertParameterInEditor flex justify-center px-3 py-2 h-9 bg-primary-blue-500 hover:shadow-lg rounded-lg focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-primary-blue-600'><p class='text-sm font-medium text-white'>Insert</p></button></div>"
                    ).appendTo(container);
                },
            },
            {
                field: 'track_color',
                label: 'Track Color',
                editor: function (container, options) {
                    $(
                        '<input type="text" id="' +
                            options.field +
                            '" name="' +
                            options.field +
                            '"  disabled />'
                    )
                        .appendTo(container)
                        .kendoTextBox();
                },
            },
            {
                field: 'letter_editer',
                label: 'Template Editor',
                hint: "Note : Press '{' for tag",
                editor: function (container, options) {
                    $(
                        "<textarea class='k-textarea' id='edit_content' name='" +
                            options.field +
                            "' data-bind='value: " +
                            options.field +
                            "'></textarea>"
                    ).appendTo(container);
                },
            },
            {
                field: 'attachments',
                label: 'Attachments',
                editor: function (container, options) {
                    $(
                        " <input  id='attachments' type='file' name='" + options.field + "' />"
                    ).appendTo(container);
                },
            },
        ],
        buttonsTemplate:
            '<div class="w-full inline-flex space-x-4 items-center justify-end">\n' +
            '<div class="float-right flex space-x-4 items-center justify-end">\n' +
            '<input type="hidden" id="editId" name="id">\n' +
            '<button type="button" class="updateLetterTemplate flex justify-center h-full px-3 py-2 h-9 bg-primary-blue-500 hover:shadow-lg rounded-lg focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-primary-blue-600">\n' +
            '<p class="text-sm  text-white">Update</p>\n' +
            '</button>\n' +
            '</div>\n' +
            '</div>',
    });

    $('body').on('click', '.insertParameterInEditor', function (e) {
        let parameterValue = '{#' + $('#parameter_insert').val() + '#}';
        let content = myEditor1.getData();
        myEditor1.setData(content + parameterValue);
    });

    $('body').on('click', '.updateLetterTemplate', function (e) {
        $('#edit_content').val(myEditor1.getData());
        let serializeArr = $(document)
            .find('#editLetterTemplateForm')
            .find('input[name], select[name], textarea[name]')
            .serializeArray();
        let dataArr = {};
        $(serializeArr).each(function (id, field) {
            dataArr[field.name] = field.value;
        });
        ajaxcallwithMethodKendo(
            site_url + 'api/save-letter-template',
            dataArr,
            'POST',
            function (resultData) {
                manageLetterTemplate();
                notificationDisplay(resultData.message, '', resultData.status);
            }
        );
    });

    function onSelect(e) {
        const isDeleteBtn = e.originalEvent.target.closest('#deleteLetterTemplate');
        if (isDeleteBtn) {
            e.preventDefault();
            return;
        }
        let editCourseTemplateModal = $(document).find('#editLetterTemplateForm');
        editCourseTemplateModal.find('#track_letter').getKendoRadioGroup().enable(true, 1);
        editCourseTemplateModal
            .find('#editId')
            .val($('#letterTemplateWizard-' + e.step.options.index + ' #id').val());
        editCourseTemplateModal
            .find('#recipient')
            .data('kendoDropDownList')
            .value($('#letterTemplateWizard-' + e.step.options.index + ' #recipient').val());
        editCourseTemplateModal
            .find('#letter_name')
            .val($('#letterTemplateWizard-' + e.step.options.index + ' #letter_name').val());
        editCourseTemplateModal
            .find('#category_id')
            .data('kendoDropDownList')
            .value($('#letterTemplateWizard-' + e.step.options.index + ' #category_id').val());
        // editCourseTemplateModal.find("#category_id").data('kendoDropDownList').enable(false);
        let postArr = {
            category_id: $('#letterTemplateWizard-' + e.step.options.index + ' #category_id').val(),
        };
        let report = $('#letterTemplateWizard-' + e.step.options.index + ' #report').val();
        $('#report_id').kendoDropDownList({
            autoWidth: true,
            dataTextField: 'Name',
            dataValueField: 'Id',
            filter: 'contains',
            value: $('#letterTemplateWizard-' + e.step.options.index + ' #report').val(),
            dataSource: getDropdownDataSource('get-report-name', postArr),
            dataBound: function (e) {
                this.value(report);
            },
        });
        editCourseTemplateModal
            .find(
                'input[type=radio][name="track_letter"][value="' +
                    $('#letterTemplateWizard-' + e.step.options.index + ' #track_letter').val() +
                    '"]'
            )
            .trigger('click');
        editCourseTemplateModal.find('#track_letter').getKendoRadioGroup().enable(false, 1);
        editCourseTemplateModal
            .find('#track_color')
            .val($('#letterTemplateWizard-' + e.step.options.index + ' #track_color').val());
        myEditor1.setData($('#letterTemplateWizard-' + e.step.options.index + ' #content').html());
    }

    function openCenterWindow(titleText, widthVal = 50, topVal = 5, leftVal = 25) {
        return {
            title: titleText,
            width: widthVal + '%',
            // height: "70%",
            actions: ['close'],
            draggable: false,
            resizable: false,
            modal: true,
            position: {
                top: topVal + '%',
                left: leftVal + '%',
            },
            animation: defaultCloseAnimation(),
        };
    }

    // function kendoWindowOpen(windowID) {
    //     let kendoWindow = $(document).find(windowID);
    //     kendoWindow.getKendoWindow().open();
    // }

    $('#addLetterTemplateModal').kendoWindow(openCenterWindow('Add Letter Template'));

    $('body').on('click', '.addLetterTemplate', function () {
        kendoWindowOpen('#addLetterTemplateModal');
        $('#addLetterTemplateForm')[0].reset();
        if (myEditor) {
            myEditor.setData(''); // Clear the CKEditor content
        }
        let validator = $('#addLetterTemplateForm').data('kendoValidator');
        if (validator) {
            validator.hideMessages(); // Clear any validation messages
            $('.k-invalid').removeClass('k-invalid'); // Remove the red borders
        }
    });

    function setDropdownList(fieldID, api_url, postArr = {}) {
        $('#' + fieldID).kendoDropDownList({
            autoWidth: true,
            dataTextField: 'Name',
            dataValueField: 'Id',
            filter: 'contains',
            dataSource: getDropdownDataSource(api_url, postArr),
            dataBound: function (e) {
                this.select(0);
                this.trigger('change');
                this.trigger('select');
                // onchangeSetDropdownList(fieldID, $("#" + fieldID).data("kendoDropDownList").value());
            },
        });
    }

    function onchangeSetDropdownList(fieldID, value) {}

    $('#addLetterTemplateForm').kendoForm({
        orientation: 'vertical',
        formData: {
            track_letters: 'Yes',
        },
        items: [
            {
                field: 'letter_name',
                label: 'Letter Name',
                attributes: {
                    placeholder: 'Enter Letter Name',
                    class: '!px-3 !h-9 !indent-0',
                },
                validation: { required: true },
            },
            {
                field: 'recepient',
                editor: 'DropDownList',
                label: 'Recipient',
                editorOptions: {
                    optionLabel: 'Select Recipient',
                    dataSource: getDropdownDataSource('get-constant-data', {
                        action: 'arrLetterRecipient',
                    }),
                    dataValueField: 'Id',
                    dataTextField: 'Name',
                },
                validation: { required: true },
                attributes: {
                    class: '!h-9',
                },
            },
            {
                field: 'category',
                editor: 'DropDownList',
                label: 'Select Category',
                colSpan: 6,
                editorOptions: {
                    optionLabel: 'Select Category',
                    dataSource: category,
                    dataValueField: 'Id',
                    dataTextField: 'Name',
                    filter: 'contains',
                    filterInput: {
                        width: '100%',
                    },
                    select: function (e) {
                        if (e.dataItem) {
                            let postArr = { category_id: e.dataItem.Id };
                            setDropdownList('report_id2', 'get-report-name', postArr);
                        }
                    },
                },
                validation: { required: true },
                attributes: {
                    class: '!h-9',
                },
            },
            {
                id: 'report_id2',
                field: 'report_id',
                label: 'Select Report',
                editor: 'DropDownList',
                editorOptions: {
                    optionLabel: 'Select Report',
                },
                validation: { required: true },
                attributes: {
                    class: '!h-9',
                },
            },
            {
                field: 'track_letters',
                editor: 'RadioGroup',
                label: 'Track Letter',
                editorOptions: {
                    items: ['Yes', 'No'],
                    layout: 'horizontal',
                    labelPosition: 'after',
                    change: function (e) {
                        if (e.newValue == 'Yes') {
                            $(document)
                                .find('#addLetterTemplateForm')
                                .find('#track_color-form-label')
                                .closest('.k-form-field')
                                .show();
                        } else {
                            $(document)
                                .find('#addLetterTemplateForm')
                                .find('#track_color-form-label')
                                .closest('.k-form-field')
                                .hide();
                        }
                    },
                },
                validation: { required: true },
                attributes: {
                    class: '!h-9',
                },
            },
            {
                field: 'track_color',
                editor: 'DropDownList',
                label: 'Track Color',
                editorOptions: {
                    optionLabel: 'Select Track Color',
                    dataSource: getDropdownDataSource('get-constant-data', {
                        action: 'arrLetterTrackColor',
                    }),
                    dataValueField: 'Id',
                    dataTextField: 'Name',
                },
                attributes: {
                    class: '!h-9',
                },
            },
            {
                field: 'letter_editer',
                label: 'Template Editor',
                editor: function (container, options) {
                    $(
                        "<textarea class='k-textarea' id='add_content' name='" +
                            options.field +
                            "' data-bind='value: " +
                            options.field +
                            "'></textarea>"
                    ).appendTo(container);
                },
            },
        ],
        buttonsTemplate:
            '<div class="w-full inline-flex space-x-4 items-center justify-end">\n' +
            '<div class="float-right flex space-x-4 items-center justify-end">\n' +
            '<button type="button" class="flex justify-center px-6 py-2 bg-white shadow border hover:shadow-lg rounded-lg h-9 border-gray-300 focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-gray-400 cancelBtn">\n' +
            '<p type="button" class="text-sm text-gray-700">Cancel</p>\n' +
            '</button>\n' +
            '<button type="button" class="createLetterTemplate flex justify-center px-3 py-2 h-9 bg-primary-blue-500 hover:shadow-lg rounded-lg focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-primary-blue-600">\n' +
            '<p class="text-sm text-white">Add Letter Template</p>\n' +
            '</button>\n' +
            '</div>\n' +
            '</div>',
    });

    $('body').on('click', '.createLetterTemplate', function (e) {
        $('#add_content').val(myEditor.getData());
        let validator = $('#addLetterTemplateForm').kendoValidator().data('kendoValidator');
        if (!validator.validate()) {
            return false;
        }
        let serializeArr = $(document)
            .find('#addLetterTemplateForm')
            .find('input[name], select[name], textarea[name]')
            .serializeArray();
        let dataArr = {};
        $(serializeArr).each(function (id, field) {
            dataArr[field.name] = field.value;
        });
        if (dataArr) {
            ajaxcallwithMethodKendo(
                site_url + 'api/save-letter-template',
                dataArr,
                'POST',
                function (resultData) {
                    $(document).find('#addLetterTemplateModal').getKendoWindow().close();
                    manageLetterTemplate();
                    notificationDisplay(resultData.message, '', resultData.status);
                }
            );
        }
    });
    $('body').on('click', '.cancelBtn', function (e) {
        e.preventDefault();
        $(this).closest('.k-window-content').data('kendoWindow').close();
    });

    if ($(document).find('#add_content').html() == '') {
        CKEDITOR.ClassicEditor.create(document.querySelector('#add_content'), {
            ckfinder: {
                uploadUrl: site_url + 'api/upload-file-text-editor',
            },
            mention: {
                feeds: [
                    {
                        marker: '{',
                        feed: window.tagJson,
                        minimumCharacters: 0,
                    },
                ],
            },
            removePlugins: [
                'RealTimeCollaborativeComments',
                'RealTimeCollaborativeTrackChanges',
                'RealTimeCollaborativeRevisionHistory',
                'PresenceList',
                'Comments',
                'TrackChanges',
                'TrackChangesData',
                'RevisionHistory',
                'Pagination',
                'WProofreader',
                'MathType',
            ],
        })
            .then((editor) => {
                editor.ui.view.editable.element.style.height = '200px';
                myEditor = editor;
            })
            .catch((error) => {
                console.error(error);
            });
    }

    if ($(document).find('#edit_content').html() == '') {
        CKEDITOR.ClassicEditor.create(document.querySelector('#edit_content'), {
            ckfinder: {
                uploadUrl: site_url + 'api/upload-file-text-editor',
            },
            mention: {
                feeds: [
                    {
                        marker: '{',
                        feed: window.tagJson,
                        minimumCharacters: 0,
                    },
                ],
            },
            removePlugins: [
                'RealTimeCollaborativeComments',
                'RealTimeCollaborativeTrackChanges',
                'RealTimeCollaborativeRevisionHistory',
                'PresenceList',
                'Comments',
                'TrackChanges',
                'TrackChangesData',
                'RevisionHistory',
                'Pagination',
                'WProofreader',
                'MathType',
            ],
        })
            .then((editor1) => {
                editor1.ui.view.editable.element.style.height = '300px';
                myEditor1 = editor1;
            })
            .catch((error) => {
                console.error(error);
            });
    }

    $('body').on('click', '.previewLetterDocument', function (e) {
        let download_path = $(this).attr('data-path');
        kendoWindowOpen('#letterPreviewFileManagerModal');
        let previewDetails = kendo.template($('#letterFilePreviewTemplate').html())({
            previewId: 'notesAttachment',
        });
        $(document).find('#letterPreviewFileManagerModal').html(previewDetails);
        $.when(
            $.getScript('https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.2.2/pdf.js'),
            $.getScript('https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.2.2/pdf.worker.js')
        )
            .done(function () {
                window.pdfjsLib.GlobalWorkerOptions.workerSrc =
                    'https://cdnjs.cloudflare.com/ajax/libs/pdf.js/2.2.2/pdf.worker.js';
            })
            .then(function () {
                $('#letterPreviewFileManagerModal')
                    .find('#documentId')
                    .kendoPDFViewer({
                        pdfjsProcessing: {
                            file: download_path,
                        },
                        width: '100%',
                        height: '100%',
                    });
            });
    });
    $('body').on('click', '.deleteLetterDocumentBtn', function () {
        let primaryID = $(this).attr('data-id');
        $('#deleteLetterDocumentModal').data('kendoDialog').open();
        $('#deleteLetterDocumentModal').find('#deleteLetterDocumentId').val(primaryID);
    });
    $('body').on('click', '.showContentTags', function () {
        startAjaxLoader();

        $.ajax({
            type: 'POST',
            url: site_url + 'api/get-letter-default-parameter',
            dataType: 'json',
            data: {},
            success: function (response) {
                stopAjaxLoader();
                let recordPaymentHtml = kendo.template(
                    $('#viewContentTagParameterTemplate').html()
                )(response);
                $(document).find('#viewContentTagParameterHtml').html(recordPaymentHtml);
                kendoWindowOpen('#viewContentTagParameterModal');
            },
        });
    });
    $('#deleteLetterDocumentModal').kendoDialog({
        width: '400px',
        title: 'Delete',
        content:
            "Are you sure you want to Delete File? <input type='hidden' name='id' id='deleteLetterDocumentId' />",
        actions: [
            { text: 'Close' },
            {
                text: 'Yes',
                primary: true,
                action: function () {
                    deleteLetterDocumentFunction(
                        $('#deleteLetterDocumentModal').find('#deleteLetterDocumentId').val()
                    );
                },
            },
        ],
        animation: {
            open: {
                effects: 'fade:in',
            },
        },
        open: onOpenDeleteDialog('#deleteLetterDocumentModal'),
        visible: false,
    });
    function deleteLetterDocumentFunction(primaryID) {
        if (primaryID > 0) {
            kendo.ui.progress($(document.body), true);
            $.ajax({
                type: 'POST',
                url: site_url + 'api/delete-letter-template-document',
                dataType: 'json',
                data: { id: primaryID },
                success: function (response) {
                    kendo.ui.progress($(document.body), false);
                    $('#uploadedDocuments').data('kendoGrid').refresh();
                    $('#uploadedDocuments').data('kendoGrid').dataSource.read();
                    notificationDisplay(response.message, '', response.status);
                },
            });
        }
    }
    function onOpenDeleteDialog(gridID) {
        $(gridID)
            .parent()
            .find('.k-dialog-titlebar')
            .addClass('bg-gradient-to-l from-green-400 to-blue-500');
        // $(gridID)
        //     .parent()
        //     .find("button:first")
        //     .addClass(
        //         "bg-white border rounded-lg border-gray-300 text-gray-700 hover:shadow hover:bg-gray-50 focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-primary-blue-500",
        //     );
        $(gridID)
            .parent()
            .find('.k-primary')
            .addClass(
                'text-white bg-primary-blue-500 hover:shadow-lg rounded-lg focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-indigo-500'
            );
    }

    jQuery('.searchInputLetterTags').keyup(function () {
        var filter = jQuery(this).val();

        jQuery('.viewContentTagParameterSearch div').each(function () {
            if (jQuery(this).find('span').text().search(new RegExp(filter, 'i')) < 0) {
                jQuery(this).find('.searchLetterDiv').hide();
            } else {
                jQuery(this).find('.searchLetterDiv').show();
            }
        });
    });
    $(document).on('click', '.copy_data', function () {
        $(document).find('.copy_data').removeClass('active');
        $(this).addClass('active');
        var textVal = $(this).attr('data-text');
        var $temp = $("<input name='copy'>");
        $('body').append($temp);
        $temp.val(textVal).select();
        document.execCommand('copy');
        $temp.remove();
        notificationDisplay('Copied', '', 'success');
    });
    function startAjaxLoader() {
        $(document)
            .on('ajaxStart', function () {
                kendo.ui.progress($(document.body), true);
            })
            .on('ajaxStop', function () {
                kendo.ui.progress($(document.body), false);
            });
    }

    function stopAjaxLoader() {
        $(document)
            .on('ajaxStart', function () {
                kendo.ui.progress($(document.body), false);
            })
            .on('ajaxStop', function () {
                kendo.ui.progress($(document.body), false);
            });
    }

    $('body').on('click', '.deleteLetterTemplate', function (e) {
        console.log('clicked');
        e.preventDefault();
        e.stopPropagation();
        e.stopImmediatePropagation();
        let primaryID = $(this).attr('data-id');
        confirmDialog(
            '#confirmDeleteModal',
            'Delete Item?',
            'Are you sure you want to delete this item?',
            'warning',
            function (e) {
                ajaxActionV2(
                    'api/letter-templates/' + primaryID,
                    'DELETE',
                    {},
                    function (resultData) {
                        manageLetterTemplate();
                        notificationDisplay(resultData.message, '', resultData.status);
                    }
                );
            }
        );
    });
});
