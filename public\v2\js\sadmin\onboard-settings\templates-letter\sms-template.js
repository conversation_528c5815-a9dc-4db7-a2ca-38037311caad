$(document).ready(function () {
    $.ajaxSetup({
        headers: {
            'X-CSRF-TOKEN': $('meta[name="csrf-token"]').attr('content'),
        },
    });

    $('#loader').kendoLoader();
    kendo.ui.progress.messages = {
        loading:
            '<div class="vue-simple-spinner animate-spin" style="position:absolute;top:50%;left:50%;transfrom:translate(-50%,-50%); margin: 0px auto; border-radius: 100%; border-color: rgb(50, 157, 243) rgb(238, 238, 238) rgb(238, 238, 238); border-style: solid; border-width: 3px; border-image: none 100% / 1 / 0 stretch; width: 30px; height: 30px;"></div>',
    };
    kendo.ui.progress($('.maincontainer'), true);

    manageSmsTemplate();

    function manageSmsTemplate() {
        let dataArr = {};
        $.ajax({
            type: 'POST',
            url: site_url + 'api/get-sms-template-data',
            dataType: 'json',
            data: dataArr,
            success: function (response) {
                let stepArr = [];
                let wizarStepArr = [];
                if (response.data.length > 0) {
                    $('.noSmsDataDiv').hide();
                    $('.smsDataDiv').show();
                } else {
                    $('.noSmsDataDiv').show();
                    $('.smsDataDiv').hide();
                }
                $.each(response.data, function (index, row) {
                    stepArr.push({
                        label: row.template_name,
                        id: row.id,
                        iconTemplate: kendo.template($('#listMenuItem').html()),
                    });
                    wizarStepArr.push({
                        title: row.template_name,
                        content: kendo.template($('#commonTemplate').html())(row),
                    });
                });
                let stepper = $('#smsDocumentStepper')
                    .kendoStepper({
                        linear: false,
                        steps: stepArr,
                        // label:false,
                        orientation: 'vertical',
                        select: onSelect,
                        activate: function (e) {
                            let stepID = e.step.options.index;
                            wizard.select(stepID);
                        },
                    })
                    .data('kendoStepper');

                var templateWizard = $('#smsTemplateWizard');
                let wizard = templateWizard
                    .kendoWizard({
                        validateForms: true,
                        steps: wizarStepArr,
                    })
                    .data('kendoWizard');
                templateWizard.find('.k-stepper').hide();
                templateWizard.find('.k-wizard-buttons').hide();

                setTimeout(() => {
                    let editCourseTemplateModal = $(document).find('#editSMSTemplateForm');
                    editCourseTemplateModal
                        .find('#editId')
                        .val($('#smsTemplateWizard-0 #id').val());
                    editCourseTemplateModal
                        .find('#template_name')
                        .val($('#smsTemplateWizard-0 #template_name').val());
                    editCourseTemplateModal
                        .find('#recipient')
                        .data('kendoDropDownList')
                        .value($('#smsTemplateWizard-0 #recipient').val());
                    editCourseTemplateModal
                        .find('#content')
                        .val($('#smsTemplateWizard-0 #content').val());
                    $('.defaultHide').removeClass('hidden');
                    kendo.ui.progress($('.maincontainer'), false);
                }, 1000);
            },
        });
    }

    $('#editSMSTemplateForm').kendoForm({
        orientation: 'vertical',
        formData: {},
        items: [
            {
                field: 'template_name',
                label: 'Template Name',
                attributes: {
                    placeholder: 'Enter Template Name',
                    class: '!indent-0 !px-1.5 !h-9',
                },
                validation: { required: true },
            },
            {
                field: 'recipient',
                label: 'Recipient',
                editor: 'DropDownList',
                editorOptions: {
                    optionLabel: 'Select Recipient',
                    dataSource: getDropdownDataSource('get-constant-data', {
                        action: 'arrTemplateRecipient',
                    }),
                    dataValueField: 'Id',
                    dataTextField: 'Name',
                },
                validation: { required: true },
                attributes: {
                    class: '!h-9',
                },
            },
            {
                field: 'content',
                label: 'SMS Content',
                editor: 'TextArea',
                editorOptions: { rows: 8 },
                attributes: {
                    placeholder: 'Enter SMS Content',
                    class: '!indent-0 !px-1.5',
                },
                validation: { required: true },
            },
        ],
        buttonsTemplate:
            '<div class="w-full inline-flex space-x-4 items-center justify-end">\n' +
            '<div class="float-right flex space-x-4 items-center justify-end">\n' +
            '<input type="hidden" id="editId" name="id">\n' +
            '<button type="button" class="updateSMSTemplate flex justify-center h-9 px-3 py-2 bg-primary-blue-500 hover:shadow-lg rounded-lg focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-primary-blue-600">\n' +
            '<p class="text-sm text-white">Update</p>\n' +
            '</button>\n' +
            '</div>\n' +
            '</div>',
    });

    $('body').on('click', '.updateSMSTemplate', function (e) {
        let serializeArr = $(document)
            .find('#editSMSTemplateForm')
            .find('input[name], select[name], textarea[name]')
            .serializeArray();
        let dataArr = {};
        $(serializeArr).each(function (id, field) {
            dataArr[field.name] = field.value;
        });
        ajaxcallwithMethodKendo(
            site_url + 'api/save-sms-template',
            dataArr,
            'POST',
            function (resultData) {
                manageSmsTemplate();
                notificationDisplay(resultData.message, '', resultData.status);
            }
        );
    });

    function onSelect(e) {
        const isDeleteBtn = e.originalEvent.target.closest('#deleteSMSTemplate');
        if (isDeleteBtn) {
            e.preventDefault();
            return;
        }
        let editCourseTemplateModal = $(document).find('#editSMSTemplateForm');
        editCourseTemplateModal
            .find('#editId')
            .val($('#smsTemplateWizard-' + e.step.options.index + ' #id').val());
        editCourseTemplateModal
            .find('#template_name')
            .val($('#smsTemplateWizard-' + e.step.options.index + ' #template_name').val());
        editCourseTemplateModal
            .find('#recipient')
            .data('kendoDropDownList')
            .value($('#smsTemplateWizard-' + e.step.options.index + ' #recipient').val());
        editCourseTemplateModal
            .find('#content')
            .val($('#smsTemplateWizard-' + e.step.options.index + ' #content').val());
    }

    function openCenterWindow(titleText, widthVal = 32, topVal = 10, leftVal = 34) {
        return {
            title: titleText,
            width: widthVal + '%',
            // height: "70%",
            actions: ['close'],
            draggable: false,
            resizable: false,
            modal: true,
            position: {
                top: topVal + '%',
                left: leftVal + '%',
            },
            animation: defaultCloseAnimation(),
        };
    }

    $('#addSMSTemplateModal').kendoWindow(openCenterWindow('Add SMS Template'));

    $('body').on('click', '.addSMSTemplate', function () {
        kendoWindowOpen('#addSMSTemplateModal');
        $('#addSMSTemplateForm')[0].reset();
    });

    $('#addSMSTemplateForm').kendoForm({
        orientation: 'vertical',
        formData: {},
        items: [
            {
                field: 'template_name',
                label: 'Template Name',
                attributes: {
                    placeholder: 'Enter Template Name',
                    class: '!px-3 !indent-0 !h-9',
                },
                validation: { required: true },
            },
            {
                field: 'recipient',
                label: 'Recipient',
                editor: 'DropDownList',
                editorOptions: {
                    optionLabel: 'Select Recipient',
                    dataSource: getDropdownDataSource('get-constant-data', {
                        action: 'arrTemplateRecipient',
                    }),
                    dataValueField: 'Id',
                    dataTextField: 'Name',
                },
                validation: { required: true },
                attributes: {
                    class: '!h-9',
                },
            },
            {
                field: 'content',
                label: 'SMS Content',
                editor: 'TextArea',
                editorOptions: { rows: 8 },
                attributes: {
                    placeholder: 'Enter SMS Content',
                    class: '!px-1.5 !indent-0',
                },
                validation: { required: true },
            },
        ],
        buttonsTemplate:
            '<div class="w-full inline-flex space-x-4 items-center justify-end">\n' +
            '<div class="float-right flex space-x-4 items-center justify-end">\n' +
            '<button type="button" class="flex justify-center px-6 py-2 bg-white border hover:shadow-lg rounded-lg h-9 border-gray-300 focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-gray-400 cancelBtn">\n' +
            '<p type="button" class="text-sm text-gray-700">Cancel</p>\n' +
            '</button>\n' +
            '<button type="button" class="createSMSTemplate flex justify-center h-9 px-3 py-2 bg-primary-blue-500 hover:shadow-lg rounded-lg focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-primary-blue-600">\n' +
            '<p class="text-sm text-white">Add SMS Template</p>\n' +
            '</button>\n' +
            '</div>\n' +
            '</div>',
    });

    $('body').on('click', '.createSMSTemplate', function (e) {
        let validator = $('#addSMSTemplateForm').kendoValidator().data('kendoValidator');
        if (!validator.validate()) {
            return false;
        }
        let serializeArr = $(document)
            .find('#addSMSTemplateForm')
            .find('input[name], select[name], textarea[name]')
            .serializeArray();
        let dataArr = {};
        $(serializeArr).each(function (id, field) {
            dataArr[field.name] = field.value;
        });
        if (dataArr) {
            ajaxcallwithMethodKendo(
                site_url + 'api/save-sms-template',
                dataArr,
                'POST',
                function (resultData) {
                    $(document).find('#addSMSTemplateModal').getKendoWindow().close();
                    manageSmsTemplate();
                    notificationDisplay(resultData.message, '', resultData.status);
                }
            );
        }
    });

    $('body').on('click', '.cancelBtn', function (e) {
        e.preventDefault();
        $(this).closest('.k-window-content').data('kendoWindow').close();
    });

    $('body').on('click', '.deleteSMSTemplate', function (e) {
        e.preventDefault();
        let primaryID = $(this).attr('data-id');
        confirmDialog(
            '#confirmDeleteModal',
            'Delete Item?',
            'Are you sure you want to delete this item?',
            'warning',
            function (e) {
                ajaxActionV2('api/sms-templates/' + primaryID, 'DELETE', {}, function (resultData) {
                    manageSmsTemplate();
                    notificationDisplay(resultData.message, '', resultData.status);
                });
            }
        );
    });
});
