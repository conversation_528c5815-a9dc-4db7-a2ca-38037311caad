let myEditor;

var emailSubject = '';
var emailContent = '';
var existEmailAttachmentId = [];
var confirmEmail = false;
let attachedFiles = [];

$(document).ready(function () {
    var filterLoad = true;
    var selectedStudent = [];
    var gridID = '#orientationList';
    $(document).find('html').addClass('overflow-hidden');

    $('#loader').kendoLoader();
    kendo.ui.progress.messages = {
        loading:
            '<div class="vue-simple-spinner animate-spin" style="position:absolute;top:50%;left:50%;transfrom:translate(-50%,-50%); margin: 0px auto; border-radius: 100%; border-color: rgb(50, 157, 243) rgb(238, 238, 238) rgb(238, 238, 238); border-style: solid; border-width: 3px; border-image: none 100% / 1 / 0 stretch; width: 30px; height: 30px;"></div>',
    };

    initializeSplitter('#studentOrientationSplitter');

    $.ajaxSetup({
        headers: {
            Authorization: api_token,
        },
    });

    var heightVal = $(window).height() - $(gridID).offset().top;
    $('#orientationList').kendoGrid({
        dataSource: {
            type: 'json',
            transport: {
                read: {
                    url: site_url + 'api/student-orientation-data',
                    dataType: 'json',
                    type: 'POST',
                },
                parameterMap: function (data, operation) {
                    if (operation == 'read') {
                        if (data.filter) {
                            $.each(data.filter.filters, function (index, value) {
                                if (value.field == 'course_start_date') {
                                    data.filter.filters[index].value = kendo.toString(
                                        data.filter.filters[index].value,
                                        'yyyy-MM-dd'
                                    );
                                }
                            });
                        }
                        return data;
                    }
                },
            },
            schema: {
                data: 'data.data',
                total: 'data.total',
                model: {
                    id: 'id',
                    fields: {
                        generated_stud_id: { type: 'string' },
                        profile_picture: { type: 'string' },
                        student_name: { type: 'string' },
                        course: { type: 'string' },
                        course_start_date: { type: 'date' },
                        status: { type: 'string' },
                    },
                },
            },
            pageSize: 25,
            serverPaging: true,
            serverFiltering: true,
            serverSorting: true,
        },
        height: heightVal,
        filterable: false,
        sortable: true,
        resizable: true,
        navigatable: true,
        dataBound: onDataBound,
        change: onChange,
        columnResize: columnResizeAllTimetable,
        pageable: customPageableArr(),
        columns: [
            {
                selectable: true,
                width: '50px',
                //template: '<input type="checkbox" id="#=id#" class="gridCK" />'
            },
            {
                field: 'generated_stud_id',
                title: 'Student Id',
                template:
                    "<div class='course-name text-sm leading-4 text-gray-600 truncate'>#: generated_stud_id #</div>",
                width: 115,
            },
            {
                template: function (dataItem) {
                    return manageProfilePic(
                        dataItem.student_id,
                        dataItem.secure_id,
                        dataItem.profile_pic,
                        dataItem.student_name
                    );
                },
                field: 'student_name',
                title: 'Student Name',
                width: $.cookie('studentorentation-student_name')
                    ? $.cookie('studentorentation-student_name') + 'px'
                    : '',
                attributes: {
                    class: 'border-b border-slate-100 dark:border-slate-700 p-4 pl-8 text-slate-500 dark:text-slate-400 ',
                },
                headerAttributes: {
                    class: 'bg-gray-100 border-b text-sm dark:border-slate-600 font-medium p-4 pl-8 pt-0 pb-3 text-slate-400 dark:text-slate-200 text-left',
                },
            },
            {
                template:
                    "<div class='course-name text-sm text-gray-600 leading-5 truncate glob-tooltip' title='#: course #'>#: course #</div>",
                field: 'course',
                title: 'Course Applied',
                width: $.cookie('studentorentation-course')
                    ? $.cookie('studentorentation-course') + 'px'
                    : '',
                attributes: {
                    style: 'white-space: nowrap;max-width: 200px;',
                    class: 'border-b border-slate-100 dark:border-slate-700 p-4 pl-8 text-slate-500 dark:text-slate-400',
                },
                headerAttributes: {
                    class: 'bg-gray-100 border-b dark:border-slate-600 font-medium p-4 pl-8 pt-0 pb-3 text-slate-400 dark:text-slate-200 text-left',
                },
            },
            {
                template:
                    "<div class='start_date flex items-center text-sm leading-4 text-gray-600'><svg data-v-fd8af9c4='' xmlns='http://www.w3.org/2000/svg' fill='none' viewBox='0 0 24 24' stroke='currentColor' class='h-5 w-5 text-gray-400 mr-2'><path data-v-fd8af9c4='' stroke-linecap='round' stroke-linejoin='round' stroke-width='2' d='M8 7V3m8 4V3m-9 8h10M5 21h14a2 2 0 002-2V7a2 2 0 00-2-2H5a2 2 0 00-2 2v12a2 2 0 002 2z'></path></svg> #: kendo.toString(course_start_date, \"dd MMM yyyy\") # </div>",
                field: 'course_start_date',
                title: 'Course Start Date',
                format: '{0:dd/MM/yyyy}',
                width: $.cookie('studentorentation-course_start_date')
                    ? $.cookie('studentorentation-course_start_date') + 'px'
                    : 150,
                attributes: {
                    class: 'border-b border-slate-100 dark:border-slate-700 p-4 pl-8 text-slate-500 dark:text-slate-400',
                },
                headerAttributes: {
                    class: 'bg-gray-100 border-b dark:border-slate-600 font-medium p-4 pl-8 pt-0 pb-3 text-slate-400 dark:text-slate-200 text-left',
                },
            },
            {
                template: function (dataItem) {
                    return manageStatusWithCss(dataItem.status);
                },
                field: 'status',
                title: 'Status',
                width: $.cookie('studentorentation-status')
                    ? $.cookie('studentorentation-status') + 'px'
                    : 150,
            },
            {
                template: function (dataItem) {
                    return manageInvitationWithCss(
                        dataItem.is_orientation_email_send,
                        dataItem.is_orientation_sms_send
                    );
                },
                field: 'invitation',
                title: 'Invitation',
                width: $.cookie('studentorentation-invitation')
                    ? $.cookie('studentorentation-invitation') + 'px'
                    : 200,
                filterable: false,
                sortable: true,
            },
            {
                template: function (dataItem) {
                    return manageAttendedWithCss(dataItem);
                },
                field: 'orientation',
                title: 'Orientation',
                width: $.cookie('studentorentation-orientation')
                    ? $.cookie('studentorentation-orientation') + 'px'
                    : 150,
            },
        ],
        //toolbar: ["excel"],
        noRecords: noRecordTemplate(),
        excel: {
            fileName: 'Student Orientation.xlsx',
            filterable: true,
            //allPages:true
        },
    });

    function columnResizeAllTimetable(e) {
        $.cookie('studentorentation-' + e.column.field, e.newWidth);
    }

    function createMultiSelectedStudents(element) {
        element.removeAttr('data-bind');
        element.kendoMultiSelect({
            dataSource: {
                transport: {
                    read: {
                        url: site_url + 'api/orientation-student-name',
                        dataType: 'json',
                        type: 'POST',
                        data: {
                            field: 'student_name',
                        },
                    },
                },
                schema: {
                    data: 'data',
                },
            },
            change: function (e) {
                var filter = { logic: 'or', filters: [] };
                var values = this.value();
                console.log(values);
                $.each(values, function (i, v) {
                    filter.filters.push({
                        field: 'student_name',
                        operator: 'eq',
                        value: v,
                    });
                });
                $(gridID).data('kendoGrid').dataSource.filter(filter);
            },
        });
    }
    function createMultiSelectedStudentsId(element) {
        element.removeAttr('data-bind');
        element.kendoMultiSelect({
            dataSource: {
                transport: {
                    read: {
                        url: site_url + 'api/orientation-student-id',
                        dataType: 'json',
                        type: 'POST',
                        data: {
                            field: 'generated_stud_id',
                        },
                    },
                },
                schema: {
                    data: 'data',
                },
            },
            change: function (e) {
                var filter = { logic: 'or', filters: [] };
                var values = this.value();
                console.log(values);
                $.each(values, function (i, v) {
                    filter.filters.push({
                        field: 'generated_stud_id',
                        operator: 'eq',
                        value: v,
                    });
                });
                $(gridID).data('kendoGrid').dataSource.filter(filter);
            },
        });
    }

    var notification = $('#notification')
        .kendoNotification({
            position: {
                pinned: true,
                bottom: 30,
                left: 30,
            },
            autoHideAfter: 0,
            stacking: 'down',
            templates: [
                {
                    type: 'error',
                    template: $('#errorTemplate').html(),
                },
                {
                    type: 'success',
                    template: $('#successTemplate').html(),
                },
            ],
        })
        .data('kendoNotification');

    /* Start design based pagination */
    let gridHtml = $(document).find('#orientationList');
    $('.k-grid-filter').find('span').remove();
    gridHtml
        .find('.k-pager-sizes')
        .contents()
        .filter(function () {
            return this.nodeType === 3;
        })
        .remove();
    gridHtml
        .find('.k-grid-pager')
        .append(
            '<div class="flex justify-between w-full "><div class="gridInfo"></div><div class="gridPagination k-pager-wrap k-grid-pager k-widget k-floatwrap"></div></div>'
        );
    gridHtml.find('.k-pager-sizes').appendTo('#orientationList .k-grid-pager .flex .gridInfo');
    gridHtml
        .find('.k-pager-info')
        .insertAfter('#orientationList .k-grid-pager .flex .gridInfo .k-pager-sizes');
    gridHtml
        .find('.k-pager-wrap a, .k-pager-wrap .k-pager-numbers-wrap')
        .appendTo('#orientationList .k-grid-pager .flex .gridPagination');
    /* End design based pagination */

    function onChange(e) {
        selectedStudent = [];
        let studCourseIds = [];
        $('#mailToUser').html('');
        var rows = e.sender.select();
        rows.each(function (e) {
            var grid = $('#orientationList').data('kendoGrid');
            var dataItem = grid.dataItem(this);
            studCourseIds.push(dataItem.id);
            selectedStudent.push({
                id: dataItem.id,
                student_id: dataItem.student_id,
                name: dataItem.student_name,
                mobile: dataItem.mobile,
            });
        });
        // if (studCourseIds.length > 2) {
        //     $(".studentNameList").addClass("h-20");
        // } else {
        //     $(".studentNameList").removeClass("h-20");
        // }
        $(document).find('.studCourseIds').val(studCourseIds);
        $(document).find('.studentNameList').html(getStudentStrData(selectedStudent));
        let selectedCount = this.selectedKeyNames().length;
        let selectedTitle = 'No student selected';
        if (selectedCount > 0) {
            selectedTitle =
                selectedCount == 1 ? '1 student selected' : selectedCount + ' students selected';
            $('#action').addClass('bottomaction').removeClass('heightzero');
            $('#selectedstudents').prop('checked', true);
        } else {
            $('#action').removeClass('bottomaction').addClass('heightzero');
        }
        $(document).find('#selected_title').text(selectedTitle);
    }

    function onDataBound(e) {
        defaultHideShowColumn();
        let grid = e.sender;
        let rows = grid.tbody.find("[role='row']");
        rows.unbind('click');
        rows.on('click', onClick);
        setTimeout(function () {
            manageFilterBaseTitle();
        }, 100);

        manageGridPagerTemplate('#orientationList');
    }

    function onClick(e) {
        if ($(e.target).hasClass('k-checkbox')) return;

        let row = $(e.target).closest('tr');
        let checkbox = $(row).find('.k-checkbox');
        checkbox.click();
    }

    function manageFilterBaseTitle() {
        setFiltericon();
        $(document)
            .find('.filter_title')
            .html('')
            .html(
                $('#orientationList')
                    .find('span.k-pager-info')
                    .text()
                    .replace('results', 'students')
            );
    }

    function setFiltericon() {
        var activeIcon =
            '<svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M1 2C1 1.44772 1.44772 1 2 1H12C12.5523 1 13 1.44772 13 2V3.25245C13 3.51767 12.8946 3.77202 12.7071 3.95956L8.62623 8.04044C8.43869 8.22798 8.33333 8.48233 8.33333 8.74755V10.3333L5.66667 13V8.74755C5.66667 8.48233 5.56131 8.22798 5.37377 8.04044L1.29289 3.95956C1.10536 3.77202 1 3.51767 1 3.25245V2Z" stroke="#1890FF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>';
        var icon =
            '<svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M1 2C1 1.44772 1.44772 1 2 1H12C12.5523 1 13 1.44772 13 2V3.25245C13 3.51767 12.8946 3.77202 12.7071 3.95956L8.62623 8.04044C8.43869 8.22798 8.33333 8.48233 8.33333 8.74755V10.3333L5.66667 13V8.74755C5.66667 8.48233 5.56131 8.22798 5.37377 8.04044L1.29289 3.95956C1.10536 3.77202 1 3.51767 1 3.25245V2Z" stroke="#9CA3AF" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg>';
        $('.k-grid-filter').each(function () {
            if ($(this).hasClass('k-state-active')) $(this).html(activeIcon);
            else $(this).html(icon);
        });
    }

    function getStudentStrData(selectedStudent) {
        let prev = {};
        let studentNameListStr = '';
        selectedStudent.filter(function (arr) {
            var key = arr['student_id'];
            if (prev[key]) return false;
            if ((prev[key] = true)) {
                studentNameListStr +=
                    '<div class="inline-flex items-center justify-center px-2 py-0.5 m-0.5 bg-gray-200 rounded-full"><span class="text-sm">' +
                    arr['name'] +
                    '</span>&nbsp;<span class="cursor-pointer k-icon k-i-close remove_stud" data-sid="' +
                    arr['student_id'] +
                    '"></span></div>';
            }
        });
        return studentNameListStr;
    }

    function setAppliedFilterData(appliedFilterArr) {
        let filterHtml = '';
        if (appliedFilterArr.length > 0) {
            appliedFilterArr.filter(function (arr) {
                filterHtml +=
                    '<div class="inline-flex items-center justify-center space-x-2 px-2 py-1 m-1 bg-gray-100 rounded-full"><span class="text-xs leading-none text-center text-gray-800">' +
                    arr['value'] +
                    '</span><span class="cursor-pointer k-icon k-i-close clear_applied_filter text-blue-500" data-filter-id="' +
                    arr['id'] +
                    '"></span></div>';
            });
            filterHtml +=
                '<div class="inline-flex items-center justify-center space-x-2"><button class="text-sm leading-5 font-medium text-primary-blue-500 clear_applied_filter" data-filter-id="all">Clear Filters</button></div>';
        }
        return filterHtml;
    }

    function refreshGridData() {
        $('#orientationList').data('kendoGrid').refresh();
        $('#orientationList').data('kendoGrid').dataSource.read();
        $('.k-i-close').trigger('click');
        $('.closeAction').trigger('click');
    }

    ClassicEditor.create(document.querySelector('#comments'))
        .then((editor) => {
            editor.ui.view.editable.element.style.height = 'auto';
            myEditor = editor;
        })
        .catch((error) => {
            console.error(error);
        });

    $('#category').change(function () {
        var value = $(this).val();
        if (value != null) {
            if (value == '4')
                grid.data('kendoGrid').dataSource.pageSize(
                    grid.data('kendoGrid').dataSource.data().length
                );
            else grid.data('kendoGrid').dataSource.pageSize(parseInt(value));
        }
    });

    var filterData = new kendo.data.HierarchicalDataSource({
        transport: {
            read: {
                url: site_url + 'api/student-orientation-filterby-html',
                dataType: 'json',
                type: 'POST',
            },
            parameterMap: function (data, operation) {
                if (operation == 'read') {
                    if (data.id == '4' || data.id == '2') {
                        setTimeout(function () {
                            hideShowMoreCourses(data.id);
                        }, 5000);
                    }
                    return data;
                }
            },
        },
        schema: {
            data: 'data',
            model: {
                id: 'id',
                hasChildren: 'hasChild',
            },
        },
    });

    $('#panelbar').kendoPanelBar({
        template: kendo.template($('#filter-panelbar-template').html()),
        dataSource: filterData,
        dataBound: function (e) {
            if (filterLoad) {
                filterLoad = false;
                $('#panelbar').data('kendoPanelBar').expand($('[class^="k-item"]'));
            }
        },
        //expandMode: "single",
    });

    $('#dropdowncampus').kendoDropDownList({
        autoWidth: true,
        dataTextField: 'name',
        dataValueField: 'id',
        dataSource: {
            schema: {
                data: 'data',
            },
            transport: {
                read: {
                    url: site_url + 'api/student-orientation-campus-list',
                    dataType: 'json',
                    type: 'POST',
                },
            },
        },
    });

    $('#sms_template').kendoDropDownList({
        dataTextField: 'name',
        dataValueField: 'id',
        dataSource: {
            schema: {
                data: 'data',
            },
            transport: {
                read: {
                    url: site_url + 'api/sms-template-list',
                    dataType: 'json',
                    type: 'POST',
                },
            },
        },
    });

    $('#email_attachment').change(function (event) {
        const files = event.target.files;
        const attachedFilesContainer = $('#attachedFilesContainer');

        $.each(files, function (index, file) {
            const fileName = file;
            // Create a new element to display the file name
            const fileElement = $('<div>').addClass(
                'flex items-center border bg-white border-gray-200 p-1 rounded-full max-w-52'
            );
            const fileNameElement = $('<span>')
                .addClass('truncate')
                .attr('title', fileName.name)
                .text(fileName.name);
            const removeButton = $('<button>').addClass('ml-2 text-gray-500 hover:text-red-500');

            const removeIcon = $(
                '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="14" height="14" fill="currentColor"><path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>'
            ).addClass('cursor-pointer');

            // Add a button to remove the file
            removeIcon.click(function () {
                // Remove the file from the list, array, and update UI
                fileElement.remove();
                attachedFiles.splice(attachedFiles.indexOf(fileName), 1);
                $('#email_attachment').val(''); // Reset the file input
            });
            // Append file name and remove button to the container
            removeButton.append(removeIcon);
            fileElement.append(fileNameElement, removeButton);
            attachedFilesContainer.addClass('mt-2').append(fileElement);

            // Add the file name to the array
            attachedFiles.push(fileName);
        });
    });

    $('body').on('click', '.closeAction', function () {
        $('#action').removeClass('bottomaction').addClass('heightzero');
        $('#selectedstudents').trigger('click');
    });

    $('body').on('keyup', '#courseSearch', function (e) {
        let action = $(this).closest('li').siblings('li');
        var value = $(this).val();
        action.each(function () {
            if (value.length > 0) {
                if ($(this).find('label').data('val').toUpperCase().includes(value.toUpperCase()))
                    $(this).fadeIn();
                else $(this).fadeOut();
            } else {
                $(this).fadeIn();
            }
        });
    });

    $('body').on('click change', '.external-filter', function () {
        updateFilterCount();
    });

    function updateFilterCount() {
        let campusCount = $('#dropdowncampus').val() == '' ? 0 : 1;
        let filterCount = $(document).find('.external-filter:checked').length + campusCount;
        let buttonText = '';
        if (filterCount > 0) {
            buttonText = '(' + filterCount + ')';
            // $(document).find(".filterFooterBox").show();
            $(document).find('.filterCountDiv').removeClass('hidden');
            $(document).find('#filterBtn').addClass('border-primary-blue-500 bg-primary-blue-50');
            $(document).find('#filterBtn').removeClass('border-gray-300 bg-white');
            $(document).find('#filterBtn').addClass('bg-primary-blue-50');
        } else {
            // $(document).find(".filterFooterBox").hide();
            $(document).find('.filterCountDiv').addClass('hidden');
            $(document).find('#filterBtn').removeClass('border-primary-blue-500');
            $(document).find('#filterBtn').addClass('border-gray-300');
            $(document).find('#filterBtn').removeClass('bg-primary-blue-50');
        }
        $(document).find('#applyFilter span span').text(buttonText);
        $(document).find('#filterBtn .filterCount').text(filterCount);
        applyOrientationFilter();
    }

    function applyOrientationFilter() {
        let appliedFilterArr = [];
        let campusIdArr =
            $('#dropdowncampus').data('kendoDropDownList').value() != ''
                ? [$('#dropdowncampus').data('kendoDropDownList').value()]
                : [];
        let extFilterArr = {
            campus: campusIdArr,
            student_by: [],
            date: [],
            type: [],
            course: [],
            year: [],
            invitation: [],
            orientation: [],
        };
        $('.external-filter:checked').each(function () {
            let field = $(this).attr('data-category');
            extFilterArr[field].push(this.value);
            appliedFilterArr.push({
                id: this.id,
                value: $(this).attr('data-val'),
            });
        });

        $(document).find('#appliedFilterList').html(setAppliedFilterData(appliedFilterArr));
        manageFilterOnGrid('extra', extFilterArr);
    }

    $('body').on('click', '#applyFilter', function () {
        let appliedFilterArr = [];
        let campusIdArr = $('#dropdowncampus').val() != '' ? [$('#dropdowncampus').val()] : [];
        let extFilterArr = {
            campus: campusIdArr,
            student_by: [],
            date: [],
            type: [],
            course: [],
            year: [],
            invitation: [],
            orientation: [],
        };
        $('.external-filter:checked').each(function () {
            let field = $(this).attr('data-category');
            extFilterArr[field].push(this.value);
            appliedFilterArr.push({
                id: this.id,
                value: $(this).attr('data-val'),
            });
        });
        $(document).find('#appliedFilterList').html(setAppliedFilterData(appliedFilterArr));
        manageFilterOnGrid('extra', extFilterArr);
    });

    $('body').on('keyup', '#searchInputField', function (e) {
        let searchKeyword = $(this).val();
        manageFilterOnGrid('searchKey', searchKeyword);
    });

    $('body').on('click', '#exportData', function (e) {
        $('#orientationList').data('kendoGrid').saveAsExcel();
        //$("#orientationList").getKendoGrid().saveAsExcel();
    });

    $('body').on('click', '#clearFilter', function (e) {
        e.preventDefault();
        $('#orientationList').data('kendoGrid').dataSource.filter([]);
        $('#dropdowncampus').data('kendoDropDownList').select(0);
        $(document).find('.external-filter').prop('checked', false);
        $(document).find('#applyFilter span span').text('');
        // $(document).find(".filterFooterBox").hide();
    });

    function clearOrientationFilter() {
        $(gridID).data('kendoGrid').dataSource.filter([]);
        $(document).find('#apliedFilterList').html('');
        $(document).find('#clearFilter').trigger('click');
        $(document).find('#filterBtn').removeClass('border-primary-blue-500');
        $(document).find('#filterBtn').addClass('border-gray-300');
        $(document).find('#filterBtn').removeClass('bg-primary-blue-50');
        $(document).find('.filterCountDiv').addClass('hidden');
        $(document).find('#filterBtn .filterCount').text('');

        var panelbar = document.getElementById('panelbar');
        var inputs = panelbar.querySelectorAll('input[type="radio"], input[type="checkbox"]');
        inputs.forEach(function (input) {
            input.checked = false;
        });
    }

    $('body').on('click', '.clear_applied_filter', function () {
        let filterId = $(this).attr('data-filter-id');
        if (filterId == 'all') {
            $(document).find('#appliedFilterList').html('');
            // $(document).find("#clearFilter").trigger("click");
            clearOrientationFilter();
        } else {
            $(document)
                .find('#' + filterId)
                .prop('checked', false)
                .trigger('change');
            $(document).find('#applyFilter').trigger('click');
        }
    });

    $('body').on('click', '#manageColumns', function (e) {
        e.preventDefault();
        let checkHtml = $(document).find('.manageColumnBox');
        if (checkHtml.parent().hasClass('active')) {
            checkHtml.removeClass('active');
            checkHtml.parent().removeClass('active');
        } else {
            checkHtml.addClass('active');
            checkHtml.parent().addClass('active');
        }
    });

    $('body').on('click', '.column_filter', function (e) {
        console.log('ub');
        e.preventDefault();
        let columnHtml = $(document).find('.manageColumnBox .k-checkbox');
        if ($(this).hasClass('reset')) columnHtml.prop('checked', true);
        if ($(this).hasClass('clear') || $(this).hasClass('save')) {
            $(document).find('#manageColumns').trigger('click');
        }
        let selectedFields = [];
        $(document)
            .find('.manageColumnBox .k-checkbox')
            .each(function () {
                var fieldObj = {};
                fieldObj[$(this).val()] = $(this).is(':checked').toString();
                selectedFields.push(fieldObj);
            });
        $.cookie('studentOrientationListSelectedFields', JSON.stringify(selectedFields));

        defaultHideShowColumn();
    });

    function defaultHideShowColumn() {
        $(document)
            .find('.manageColumnBox .k-checkbox')
            .each(function () {
                if ($(this).is(':checked')) {
                    $(gridID).data('kendoGrid').showColumn($(this).val());
                } else {
                    $(gridID).data('kendoGrid').hideColumn($(this).val());
                }
            });
    }
    $('body').on('click', '.remove_stud', function () {
        let studId = $(this).attr('data-sid');
        let dataRow = $(document)
            .find('.stud_' + studId)
            .parents('tr');
        dataRow.each(function () {
            if ($(this).hasClass('k-state-selected')) {
                $(this).trigger('click');
            }
        });
    });

    $('body').on('change', '#selectedstudents', function () {
        $('.k-checkbox').each(function () {
            if ($(this).closest('tr').is('.k-state-selected')) {
                $(this).click();
            }
        });
    });

    $('body').on('click', '.ccmail', function () {
        $(document).find('#emailccbox').toggle();
    });

    $('body').on('click', '.bccmail', function () {
        $(document).find('#emailbccbox').toggle();
    });

    $('body').on('click', '.email_template', function () {
        let templateID = $(this).attr('data-id');
        myEditor.setData('');

        $.ajax({
            type: 'POST',
            url: site_url + 'api/get-mail-content',
            dataType: 'json',
            data: { template_id: templateID },
            success: function (response) {
                emailSubject = response.data[0].email_subject;
                emailContent = response.data[0].content;

                let emailAttachment = kendo.template($('#attachmentList').html())({
                    files: response.data[0].files,
                });
                $(document).find('.email_template_id').val(templateID);
                $(document).find('.exist_attachment_div').show();
                $(document).find('.exist_attachment').html('').html(emailAttachment);
                manageExistEmailAttachmentId();

                $(document).find('.email_subject').html('').html(emailSubject);
                $(document).find('.email_content').html('').html(emailContent);
            },
        });
    });

    $('body').on('click', '.existing_attachment', function () {
        $(this).closest('div').remove();
        manageExistEmailAttachmentId();
    });

    $('body').on('click', '.sendmail', function () {
        let modalDiv = $(this).parent('.k-window');
        let formTag = $('#emailTemplateAddForm');
        let formData = new FormData(formTag[0]);
        let totalImageCount = emailTotalAttachmentAllowed;

        if ($('.studCourseIds').val().length == 0) {
            notificationDisplay('Select at least one recipient to send the email.', '', 'error');
            return false;
        }

        if (formTag.find('#email_subject').val().length == 0) {
            notificationDisplay('Email subject is required', '', 'error');
            return false;
        }
        if (myEditor.getData().length == 0) {
            notificationDisplay('Email content is required', '', 'error');
            return false;
        }

        if (attachedFiles.length <= totalImageCount) {
            $.each(attachedFiles, function (index, fileName) {
                formData.append('attachment_file[]', fileName);
            });
        } else {
            notificationDisplay(
                'Please select Only ' + totalImageCount + ' attachments files',
                '',
                'error'
            );
            return false;
        }

        formData.append('email_content', myEditor.getData());
        formData.append('log_type', 'offer');
        formData.append('is_orientation', '1');
        if (formData.get('email_cc')) {
            let tags_cc = JSON.parse(formData.get('email_cc'));
            formData.set('email_cc', tags_cc.map((item) => item.value).toString());
        }
        if (formData.get('email_bcc')) {
            let tags_bccc = JSON.parse(formData.get('email_bcc'));
            formData.set('email_bcc', tags_bccc.map((item) => item.value).toString());
        }
        if (formData.get('reply_to_email')) {
            let parsedRes = JSON.parse(formData.get('reply_to_email'));
            if (parsedRes.length > 0 && parsedRes[0].value) {
                formData.set('reply_to_email', parsedRes[0].value);
            }
        }
        ajaxCallWithMethodFileKendoV2(
            'api/student-send-orientation-email',
            formData,
            'POST',
            function (output) {
                //notificationDisplay(output.message, '', output.status);
                //refreshGridData();
                confirmEmail = true;
            }
        );
    });

    $('body').on('click', '.student_comm_log', function () {
        let ckBox = $(this).prev();
        let ckStatus = !ckBox.is(':checked');
        ckBox.prop('checked', ckStatus);
    });

    $('body').on('click', '.sendMsg', function () {
        let sms_text = $('.sms_text').val();
        let modalDiv = $(this).parent('.k-window');
        let url = site_url + 'api/student-add-sms-details';
        ajaxcallwithMethod(url, { selectedStudent }, 'POST', function (output) {
            notificationDisplay('Send SMS', 'Send SMS', 'success');
            $('#sendSmsStudentModal').data('kendoWindow').close();
            modalDiv.removeClass('blur-modal');
        });
        // TODO:: refresh Grid;
    });

    $('body').on('click', '.resetSMS', function () {
        $(document).find('.sms_text').val('');
    });

    $('body').on('change', '#sms_template', function () {
        let template_id = $(this).val();
        if (template_id != '' && template_id != 'undefined') {
            let dataArr = { template_id: template_id };
            ajaxcallwithMethod('api/get-sms-template-contain', dataArr, 'POST', function (output) {
                $(document).find('#sms_text').val(output.data);
            });
        }
    });

    $('body').on('click', '.selectedStudentSendEmail', function () {
        // let window = $('#sendMailStudentModal').data("kendoWindow");
        // window.element.prev().find(".k-window-titlebar").html('<div className="flex items-start w-full justify-start p-6 bg-gradient-to-l from-green-400 to-blue-500"><div className="flex space-x-6 items-center justify-between w-full"><span className="text-lg font-medium leading-normal text-white" style="width: 570px;">Send Email</span><button type="button" className="rounded-md close" data-dismiss="modal"><svg width="14" height="14" viewBox="0 0 14 14" fill="none" xmlns="http://www.w3.org/2000/svg"><path d="M1 13L13 1M1 1L13 13" stroke="white" stroke-width="2" stroke-linecap="round" stroke-linejoin="round"/></svg></button></div></div>');
        //window.open();
        var input = document.querySelector('input[name=email_bcc]');
        let tagifyEmailBCC = new Tagify(input, {
            pattern:
                /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
            callbacks: {
                invalid: onInvalidTag,
            },
            templates: {
                tag: tagTemplate,
            },
        });
        tagifyEmailBCC.removeAllTags();
        var input = document.querySelector('input[name=email_cc]');
        let tagifyEmailCC = new Tagify(input, {
            pattern:
                /^(([^<>()\[\]\\.,;:\s@"]+(\.[^<>()\[\]\\.,;:\s@"]+)*)|(".+"))@((\[[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\.[0-9]{1,3}\])|(([a-zA-Z\-0-9]+\.)+[a-zA-Z]{2,}))$/,
            callbacks: {
                invalid: onInvalidTag,
            },
            templates: {
                tag: tagTemplate,
            },
        });
        tagifyEmailCC.removeAllTags();
        let sendMail = $(document).find('#sendMailStudentModal');
        sendMail.getKendoWindow().open();
        sendMail
            .parent('div')
            .find('.k-window-titlebar')
            .addClass('titlebar-sms-modal bg-gradient-to-l from-green-400 to-blue-500')
            .find('.k-window-title')
            .addClass('text-lg font-medium leading-normal text-white');

        setReplyToEmailSetup('#reply_to_email', whitelistForReplyToEmail);
    });

    var whitelistForReplyToEmail = {};
    fetch(site_url + 'api/get-college-email-list')
        .then((RES) => RES.json())
        .then(function (newWhitelist) {
            whitelistForReplyToEmail = newWhitelist.data;
        });

    function onInvalidTag(e) {
        console.log('invalid', e.detail);
        notificationDisplay('Enter email only ', '', 'error');
    }

    function tagTemplate(tagData) {
        return `<tag title="${tagData.value}" contenteditable='false' spellcheck='false' tabIndex="-1" class="tagify__tag bg-gray-300">
                    <x title='' class='tagify__tag__removeBtn' role='button' aria-label='remove tag'></x>
                    <div class='flex items-center tag-div'>&nbsp;
                    <div class='text-xs leading-normal text-gray-900 pl-1'>${tagData.value}</div>
                    </div>
                </tag>`;
    }

    $('body').on('click', '#insertTemplate', function () {
        let emailTemplate = $(document).find('#emailTemplatesModal');
        emailTemplate.getKendoWindow().open();
        $('#searchEmailTemplateStudentOrientation').val('').trigger('keyup');
        $(document).find('.email_subject').html('');
        $(document).find('.email_content').html('');
        $(document).find('.exist_attachment').html('');
        // emailTemplate.data("kendoWindow").open();
        emailTemplate
            .parent('div')
            .find('.k-window-titlebar')
            .addClass(
                'titlebar-sms-modal titlebar-template-modal bg-gradient-to-l from-green-400 to-blue-500'
            )
            .find('.k-window-title')
            .addClass('text-lg font-medium leading-normal text-white');
    });

    $('body').on('click', '.selectedStudentSendSms', function () {
        let sendSms = $(document).find('#sendSmsStudentModal');
        sendSms.getKendoWindow().open();
        sendSms
            .parent('div')
            .find('.k-window-titlebar')
            .addClass('titlebar-sms-modal bg-gradient-to-l from-green-400 to-blue-500')
            .find('.k-window-title')
            .addClass('text-lg font-medium leading-normal text-white');
    });

    $('body').on('click', '#useEmailTemplateBtn', function () {
        $(document).find('#email_subject').val(emailSubject);
        $(document).find('.isTemplateSelect').text(emailSubject);
        myEditor.setData(emailContent);
        $(document).find('.existing_attachment_id').val(existEmailAttachmentId);
        $(document).find('#emailTemplatesModal').getKendoWindow().close();
    });

    $('body').on('click', '.updateAttendedOrientation', function () {
        $('#updateAttendedOrientationModal').data('kendoDialog').open();
    });

    $('body').on('click', '.confirmCourseStarted', function () {
        $('#confirmCourseStartedModal').data('kendoDialog').open();
    });

    $('#sendMailStudentModal').kendoWindow(defaultWindowSlideFormat('Send Email', 60));

    $('#statusForSendEmailModal').kendoWindow(openCenterWindow('Send Mail Status', 50, 25, 25));

    $('#statusForSendEmailModal')
        .data('kendoWindow')
        .bind('close', function (e) {
            confirmEmail = true;
            $('.resetEmail').trigger('click');
            $('#sendMailStudentModal').data('kendoWindow').close();
            $('#selectedstudents').trigger('click');
        });

    $('#sendMailStudentModal')
        .data('kendoWindow')
        .bind('close', function (e) {
            if (!confirmEmail) {
                kendowindowOpen('#closeModelConformation');
                confirmEmail = false;
                e.preventDefault();
            }
        });

    $('#closeModelConformation').kendoWindow(openCenterWindow('Discard Email'));

    $('body').on('click', '.discard-yes', function (e) {
        confirmEmail = true;
        $('#closeModelConformation').data('kendoWindow').close();
        $('#sendMailStudentModal').data('kendoWindow').close();
        $('#selectedstudents').trigger('click');
        $('.resetEmail').trigger('click');
    });

    $('body').on('click', '.discard-no', function (e) {
        $('#closeModelConformation').data('kendoWindow').close();
    });

    $('body').on('click', '.resetEmail', function () {
        $(document).find('#comments').text('');
        $('.isTemplateSelect').text('');
        $('#studentCommLogDefault').prop('checked', false);
        $('#email_subject').val('');
        $('.existing_attachment_id').val('');
        $('.email_template_id').val('');
        $('.email_template_id').val('');
        $('.selected_file').text('');
        $('#email_cc').html('');
        $('#email_bcc').html('');
        $(document).find('#emailccbox').hide();
        $(document).find('#emailbccbox').hide();
        myEditor.setData('');
    });

    $('#sendMailStudentModal')
        .data('kendoWindow')
        .bind('open', function (e) {
            attachedFiles = [];
            confirmEmail = false;
        });

    $('#sendSmsStudentModal').kendoWindow(defaultWindowSlideFormat('Send SMS', 55));

    $('#emailTemplatesModal').kendoWindow(openCenterWindow('Email Templates', 65, 15, 17));

    addModalClassToWindows([
        '#emailTemplatesModal',
        '#statusForSendEmailModal',
        '#closeModelConformation',
    ]);

    $('#confirmCourseStartedModal').kendoDialog({
        width: '400px',
        title: 'Confirm Course Started',
        content: 'Are you sure you want to started course?',
        actions: [
            { text: 'Close', action: onCourseStartedCancel },
            { text: 'Yes', primary: true, action: onCourseStartedOK },
        ],
        animation: {
            open: {
                effects: 'fade:in',
            },
        },
        open: onOpenConfirmCourseStarted,
        visible: false,
    });

    function onOpenConfirmCourseStarted(e) {
        $('#confirmCourseStartedModal')
            .parent()
            .find('.k-dialog-titlebar')
            .addClass('bg-gradient-to-l from-green-400 to-blue-500');
    }

    function onCourseStartedCancel() {
        //alert('confirm cancel');
    }

    function validateSelectedCourseData(orientation) {
        let flag = true;
        let studentCourseIdArr = [];
        let gridData = $('#orientationList').data('kendoGrid');
        gridData.tbody
            .find('input')
            .closest('tr')
            .each(function (index, row) {
                if ($(row).hasClass('k-state-selected')) {
                    var dataItem = gridData.dataItem(row);
                    if (dataItem.orientation == orientation) {
                        flag = false;
                        return false;
                    }
                    studentCourseIdArr.push(dataItem.id);
                }
            });
        return flag ? studentCourseIdArr : [];
    }

    function onCourseStartedOK() {
        let studentCourseId = validateSelectedCourseData('No');
        if (studentCourseId.length == 0) {
            notificationDisplay('Selected records should require orientation first', '', 'error');
            return false;
        }
        let dataArr = {
            attended: 'Yes',
            student_course_id: studentCourseId,
            orientation_comment: 'Update comment',
            status: 'Current Student',
        };
        ajaxcallwithMethodKendo('api/confirm-course-started', dataArr, 'POST', function (output) {
            notificationDisplay(output.message, '', output.status);
            // updateStatusColumn();
            refreshGridData();
        });
    }

    $('#updateAttendedOrientationModal').kendoDialog({
        title: 'Update Attended Orientation',
        content: 'Are you sure you want to update attended orientation?',
        actions: [
            { text: 'Close', action: onCancel },
            { text: 'Yes', primary: true, action: onOK },
        ],
        animation: {
            open: {
                effects: 'fade:in',
            },
        },
        open: onOpenUpdateAttended,
        visible: false,
    });

    function onOpenUpdateAttended(e) {
        $('#updateAttendedOrientationModal')
            .parent()
            .find('.k-dialog-titlebar')
            .addClass('bg-gradient-to-l from-green-400 to-blue-500');
        $('#updateAttendedOrientationModal')
            .parent()
            .find('button:first')
            .addClass(
                'bg-white shadow border hover:shadow-lg rounded-lg  border-gray-300 focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-indigo-500'
            );
        $('#updateAttendedOrientationModal')
            .parent()
            .find('.k-primary')
            .addClass(
                'text-white bg-primary-blue-500 hover:shadow-lg rounded-lg focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-indigo-500'
            );
    }

    function onCancel(e) {
        //alert('cancle');
    }

    function onOK(e) {
        let studentCourseId = validateSelectedCourseData('Yes');
        if (studentCourseId.length == 0) {
            notificationDisplay('Selected records already attended orientation', '', 'error');
            return false;
        }
        let dataArr = {
            attended: 'Yes',
            student_course_id: studentCourseId,
            orientation_comment: 'Update comment',
        };
        ajaxcallwithMethodKendo(
            'api/update-attended-orientation',
            dataArr,
            'POST',
            function (output) {
                notificationDisplay(output.message, '', output.status);
                refreshGridData();
            }
        );
    }

    var insertTemplateSidebarMenu = new kendo.data.HierarchicalDataSource({
        transport: {
            read: {
                url: site_url + 'api/get-mail-template-list',
                dataType: 'json',
                type: 'POST',
            },
        },
        schema: {
            data: 'data',
            model: {
                id: 'id',
                hasChildren: 'hasChildren',
                children: 'sub_list',
            },
        },
    });

    $('#insertTemplatePanelBar').kendoPanelBar({
        template: kendo.template($('#email-panelbar-template').html()),
        dataSource: insertTemplateSidebarMenu,
    });

    /* Common function */
    function manageFilterOnGrid(fieldName, searchKeyword) {
        var grid = $('#orientationList').data('kendoGrid');
        let tempArr = {
            field: fieldName,
            operator: 'contains',
            value: searchKeyword,
        };

        if (typeof grid.dataSource.filter() != 'undefined') {
            let flag = true;
            let oldFilterArr = grid.dataSource.filter().filters;
            Object.entries(oldFilterArr).forEach((filter) => {
                if (filter[1].field == fieldName) {
                    filter[1].value = searchKeyword;
                    flag = false;
                }
            });
            if (flag) {
                oldFilterArr.push(tempArr);
            }
            grid.dataSource.read();
            //grid.dataSource.filter().filters.push(tempArr);
        } else {
            grid.dataSource.filter(tempArr);
        }
        manageFilterBaseTitle();
    }

    function manageInvitationWithCss(is_email_send, is_sms_send) {
        let smsHtml = '';
        let emailHtml = '';

        let smscolor = is_sms_send ? 'green' : 'gray';
        let mailcolor = is_email_send ? 'green' : 'gray';

        let smsIcon = is_sms_send
            ? '<span class="k-icon k-i-check" style="color: #34D399;"></span>'
            : '<span class="k-icon k-i-close" style="color: #6b7280;"></span>';
        let mailIcon = is_email_send
            ? '<span class="k-icon k-i-check" style="color: #34D399;"></span>'
            : '<span class="k-icon k-i-close" style="color: #6b7280;"></span>';

        smsHtml =
            "<div class='inline-flex items-center justify-center px-2.5 py-1 bg-" +
            smscolor +
            "-100 rounded-full'>" +
            smsIcon +
            "<span class ='text-xs leading-4 text-center text-" +
            smscolor +
            "-800'>&nbsp;SMS</span></div>";
        emailHtml =
            "<div class='inline-flex items-center justify-center px-2.5 py-1 bg-" +
            mailcolor +
            "-100 rounded-full'>" +
            mailIcon +
            "<span class ='text-xs leading-4 text-center text-" +
            mailcolor +
            "-800'>&nbsp;Email</span></div> ";

        return emailHtml + smsHtml;
    }

    function manageAttendedWithCss(data) {
        let bgcolor = data.orientation == 'Yes' ? 'green' : 'gray';
        let attended = data.orientation == 'Yes' ? 'Yes' : 'No';
        let isOrientation =
            "<div class='inline-flex items-center justify-center px-2.5 py-1  bg-" +
            bgcolor +
            "-100 rounded-full'><span class='text-xs leading-4 text-center text-" +
            bgcolor +
            "-800'>" +
            attended +
            '</span></div>';
        return isOrientation;
    }

    function manageStatusWithCss(status) {
        let statusTitle =
            status == 'In Application'
                ? 'New Application Request'
                : status != ''
                  ? status
                  : 'Incomplete';
        let statusCode = 'gray';

        if (statusTitle == 'Current Student' || statusTitle == 'Enrolled')
            statusCode = 'primary-blue';
        else if (statusTitle == 'Transitioned') statusCode = 'yellow';
        else if (statusTitle == 'Rejected') statusCode = 'orange';
        else if (statusTitle == 'Offered') statusCode = 'green';
        else if (statusTitle == 'Pending') statusCode = 'cyan';
        else if (statusTitle == 'Cancelled') statusCode = 'red';
        else statusCode = 'gray';

        let statusHtml =
            "<div class='inline-flex items-center justify-center px-2.5 py-1.5 bg-" +
            statusCode +
            "-100 rounded-full'><span class='text-xs leading-4 text-center text-" +
            statusCode +
            "-800'>" +
            statusTitle +
            '</span></div>';

        return statusHtml;
    }

    function notificationDisplay(msg, title, status) {
        notification.show(
            {
                title: title,
                message: msg,
            },
            status
        );

        setTimeout(function () {
            notification.hide();
        }, 2000);
    }

    function manageExistEmailAttachmentId() {
        let existDocDiv = $(document).find('.existing_attachment');
        existEmailAttachmentId = [];
        if (existDocDiv.length == 0) {
            let noEmailAttachmentHtml =
                '<p class="text-xs leading-tight text-center text-gray-800"> No attachments available </p>';
            $(document).find('.exist_attachment').html('').html(noEmailAttachmentHtml);
        } else {
            existDocDiv.each(function () {
                existEmailAttachmentId.push($(this).attr('data-file-id'));
            });
        }

        $(document).find('.existing_attachment_id').val(existEmailAttachmentId);

        const attachedFilesContainer = $('#templateFilesContainer');
        attachedFilesContainer.html('');
        $.each(existDocDiv, function () {
            const that = $(this);
            const fileName = $(this).attr('data-file-name');

            // Create a new element to display the file name
            const fileElement = $('<div>').addClass(
                'flex items-center border bg-white border-gray-200 p-1 rounded-full max-w-52'
            );
            const fileNameElement = $('<span>')
                .addClass('truncate')
                .attr('title', fileName)
                .text(fileName);
            const removeButton = $('<button>').addClass('ml-2 text-gray-500 hover:text-red-500');

            const removeIcon = $(
                '<svg xmlns="http://www.w3.org/2000/svg" viewBox="0 0 24 24" width="14" height="14" fill="currentColor"><path d="M19 6.41L17.59 5 12 10.59 6.41 5 5 6.41 10.59 12 5 17.59 6.41 19 12 13.41 17.59 19 19 17.59 13.41 12z"/></svg>'
            ).addClass('cursor-pointer');

            // Add a button to remove the file
            removeIcon.click(function () {
                fileElement.remove();
                that.closest('div').remove();
                manageExistEmailAttachmentId();
                return false;
            });
            // Append file name and remove button to the container
            removeButton.append(removeIcon);
            fileElement.append(fileNameElement, removeButton);
            attachedFilesContainer.addClass('mt-2').append(fileElement);
        });
    }

    function sendSmsUsingApi(phoneNumber, smsContent) {
        //console.log(phoneNumber + ' | ' + smsContent)
        if (
            window.location.hostname == 'www.rtomanager.dev' ||
            window.location.origin == 'http://www.rtomanager.dev'
        ) {
            //console.log('You Send message from local system message not send.');
            notificationDisplay('SMS send successfully.', 'SMS Send', 'success');
            return false;
        } else {
            var xhr = new XMLHttpRequest();
            xhr.open(
                'GET',
                sendSmsUrl +
                    '?apiKey=' +
                    SMSApiKey +
                    '&to=' +
                    phoneNumber +
                    '&content=' +
                    smsContent +
                    '',
                true
            );
            xhr.onreadystatechange = function () {
                if (xhr.readyState == 4 && xhr.status == 200) {
                    console.log('success');
                }
                var data = JSON.parse(xhr.response);
                if (data.messages[0].accepted == false) {
                    var errorMsg =
                        data.messages[0].error == 'null' || data.messages[0].error == ''
                            ? 'Invalid destination address'
                            : data.messages[0].error;
                    notificationDisplay(errorMsg, 'SMS Error', 'error');
                } else {
                    notificationDisplay('SMS send successfully.', 'SMS Send', 'success');
                }
            };
            xhr.send();
            return true;
        }
    }

    function manageProfilePic(normalId, id, profile_pic, student_name) {
        let html = '';

        // Handle null or undefined student_name
        if (!student_name || student_name === null || student_name === undefined) {
            student_name = '--';
        }

        let studProfileUrl = `<a href="${site_url}student-profile-view/${id}" class="hover:text-blue-500" target="_blank">${student_name}</a>`;
        if (profile_pic == '') {
            let sname = student_name.toUpperCase().split(/\s+/);
            // Ensure we have at least one name part
            let firstInitial = sname[0] ? sname[0].charAt(0) : '--';
            let secondInitial = sname[1] ? sname[1].charAt(0) : '';

            html =
                "<div class='flex items-center stud_" +
                normalId +
                "'><div class='student-profile-pic h-8 w-8 rounded-full bg-blue-500 flex items-center justify-center grow-1 shrink-0'><span class='text-xs leading-6 font-medium'>" +
                firstInitial +
                secondInitial +
                "</span></div>&nbsp;<div class='student-first-name text-sm leading-4 text-gray-700 truncate'>" +
                studProfileUrl +
                '</div></div>';
        } else {
            html =
                "<div class='flex items-center stud_" +
                normalId +
                "'><img class='h-8 w-8 rounded-full grow-1 shrink-0' src='" +
                profile_pic +
                "' alt=''>&nbsp;<div class='student-first-name text-sm leading-4 text-gray-700 truncate'>" +
                studProfileUrl +
                '</div></div>';
        }
        return html;
    }

    $(document).click(function (e) {
        if (
            !$('#manageColumns').is(e.target) &&
            $('#manageColumns').has(e.target).length === 0 &&
            $('.active').has(e.target).length === 0
        ) {
            $('#manageColumns').parent().removeClass('active');
        }
    });

    setTimeout(function () {
        $('#sms_template').trigger('change');
    }, 3000);

    function hideShowMoreCourses(tabId) {
        let customUl = $(document)
            .find('#panelbar')
            .find('.filter-panel-title-' + tabId)
            .closest('li')
            .find('ul');
        customUl.addClass('custom-panel-size');
        let isDateTab = tabId == 2 ? true : false;
        let cList = customUl.find('li:gt(4)');
        if (tabId == 2) {
            cList = customUl.find('li').filter(function () {
                return $(this).find('input[data-isdefaultshow="false"]').length > 0;
            });
        }
        /*let cList = isDateTab
            ? customUl.find("li:gt(4)")
            : customUl.find('li').filter((_, el) => $(el).find('input[data-isdefaultshow="false"]').length > 0);*/

        const titles = isDateTab
            ? { more: 'Show Expired Dates', less: 'Hide Expired Dates' }
            : { more: 'See More', less: 'See Less' };

        if (!customUl.hasClass('expanded')) {
            cList.hide();
        } else {
            cList.show();
        }

        if (cList.length > 0) {
            let tempLiHtmlMore =
                '<span class="k-link"><a class="text-sm leading-tight text-blue-500" href="javascript:void(0);">' +
                titles.more +
                '</a></span>';
            let tempLiHtmlLess =
                '<span class="k-link"><a class="text-sm leading-tight text-blue-500" href="javascript:void(0);">' +
                titles.less +
                '</a></span>';
            customUl.append(
                $('<li class="expand">' + tempLiHtmlMore + '</li>').click(function (event) {
                    let expandible = $(this).closest('ul');
                    expandible.toggleClass('expanded');
                    if (!expandible.hasClass('expanded')) {
                        $(this).html(tempLiHtmlMore);
                    } else {
                        $(this).html(tempLiHtmlLess);
                    }
                    cList.toggle();
                    event.preventDefault();
                })
            );
        }
    }

    function ajaxCallWithMethodFileKendoV2(url, data, method, callback) {
        var rtrn = $.ajax({
            type: method,
            url: url,
            data: data,
            processData: false,
            contentType: false,
            headers: { 'X-CSRF-TOKEN': $('input[name="_token"]').val() },
            beforeSend: function () {
                $('#sendEmailStatusList').html('');
                $('#titleEmailSuccessMsg').text('');
                $('#titleEmailFailMsg').text('');
                // $("#sendMailStudentModal").data("kendoWindow").close();
                kendowindowOpen('#statusForSendEmailModal');
                kendo.ui.progress($('#loaderEmail'), true);
            },
            success: function (res) {
                kendo.ui.progress($('#loaderEmail'), false);
                $('#titleEmailSuccessMsg').text(res.success_msg);
                $('#titleEmailFailMsg').text(res.fail_msg);
                manageSendStatusGrid('#sendEmailStatusList', res.statusData);
            },
            error: function (result) {
                // kendo.ui.progress($(document.body), false);
                callback(result);
            },
        });
        return rtrn;
    }

    function kendowindowOpen(modalID) {
        let kendoWindow = $(document).find(modalID);
        kendoWindow.getKendoWindow().open();
        kendoWindow
            .parent('div')
            .find('.k-window-titlebar')
            .addClass('titlebar-sms-modal bg-gradient-to-l from-green-400 to-blue-500')
            .find('.k-window-title')
            .addClass('text-lg font-medium leading-normal text-white');
    }

    function manageSendStatusGrid(sendDataGrid, sendData) {
        $(sendDataGrid).kendoGrid({
            dataSource: sendData,
            groupable: false,
            sortable: true,
            columns: [
                {
                    field: 'name',
                    title: 'Name',
                    template: function (dataItem) {
                        return manageProfilePic(
                            dataItem.id,
                            dataItem.secure_id,
                            dataItem.profile_pic,
                            dataItem.name
                        );
                    },
                },
                {
                    field: 'status',
                    title: 'Status',
                },
                {
                    field: 'description',
                    title: 'Description',
                },
            ],
        });
    }

    // On escape press remove modal with selected data
    document.onkeydown = function (evt) {
        evt = evt || window.event;
        var isEscape = false;
        if ('key' in evt) {
            isEscape = evt.key === 'Escape' || evt.key === 'Esc';
        } else {
            isEscape = evt.keyCode === 27;
        }
        if (isEscape) {
            if ($(document).find('#action').hasClass('bottomaction')) {
                $('#selectedstudents').trigger('click');
            }
        }
    };
});
