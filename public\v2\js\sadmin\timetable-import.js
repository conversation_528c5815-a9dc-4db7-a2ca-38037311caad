// Initialize the Kendo UI uploader
function initKendoUploader() {
    // Check if the element exists
    if ($('#csv_file').length === 0) return;

    // Check if it's already initialized
    if ($('#csv_file').data('kendoUpload')) {
        // Destroy the existing instance
        $('#csv_file').data('kendoUpload').destroy();
        $('#csv_file').closest('.k-upload').replaceWith($('#csv_file'));
    }

    // Initialize the uploader
    $('#csv_file').kendoUpload({
        async: {
            chunkSize: 20000000, // bytes
            saveUrl: site_url + 'api/upload-timetable-file',
            autoUpload: true,
            removeUrl: '#',
        },
        validation: {
            allowedExtensions: ['.csv'],
            maxFileSize: 20000000,
        },
        multiple: false,
        showFileList: true,
        enabled: true,
        localization: {
            select: 'Select CSV File',
        },
        upload: function (e) {},
        success: function (e) {
            console.log('Upload success response:', e);

            if (e.operation == 'upload') {
                try {
                    // Parse the response if it's a string
                    var response =
                        typeof e.response === 'string' ? JSON.parse(e.response) : e.response;

                    console.log('Parsed response:', response);

                    // Get the filename from the response
                    var filename = response && response.filename ? response.filename : null;

                    if (filename) {
                        notificationDisplay(
                            'Timetable document uploaded successfully.',
                            '',
                            'success'
                        );

                        console.log('Starting import with filename:', filename);

                        // Call the Livewire method to start importing data
                        // Using the filename from the response
                        // For Livewire 3.x
                        if (typeof Livewire !== 'undefined' && Livewire.dispatch) {
                            Livewire.dispatch('startImport', { filename: filename });
                        }
                        // For Livewire 2.x
                        else if (typeof window.livewire !== 'undefined') {
                            window.livewire.emit('startImport', filename);
                        }
                    } else {
                        console.error('No filename in response:', response);
                        notificationDisplay(
                            "File uploaded but couldn't process it. Please try again.",
                            '',
                            'warning'
                        );
                    }
                } catch (error) {
                    console.error('Error processing upload response:', error);
                    notificationDisplay(
                        'Error processing upload response. Please try again.',
                        '',
                        'error'
                    );
                }
            }
        },
        error: function (e) {
            console.error('Upload error:', e);

            var errorMessage = 'File upload failed. Please try again.';

            // Try to get more specific error message
            if (e.xhr && e.xhr.responseText) {
                try {
                    var response = JSON.parse(e.xhr.responseText);
                    if (response.message) {
                        errorMessage = response.message;
                    }
                } catch (parseError) {
                    console.error('Error parsing error response:', parseError);
                }
            }

            notificationDisplay(errorMessage, '', 'error');
        },
        remove: function (e) {
            console.log('File removed:', e);
            var upload = $('#csv_file').data('kendoUpload');
            upload.clearFile(function (file) {
                return true;
            });
            e.preventDefault();
        },
    });
}

function formatDateToDMY(dateString) {
    if (!dateString) return '';
    const date = new Date(dateString);
    const day = String(date.getDate()).padStart(2, '0');
    const month = String(date.getMonth() + 1).padStart(2, '0');
    const year = date.getFullYear();
    return `${day}-${month}-${year}`;
}

// Initialize on document ready
$(document).ready(function () {
    initKendoUploader();

    $(document).ready(function () {
        $('#timetable-import-grid').kendoGrid({
            dataSource: {
                data: window.importedData?.data ?? [],
                pageSize: 10,
            },
            pageable: true,
            sortable: {
                allowUnsort: false,
            },
            resizable: true,
            noRecords: noRecordTemplate(),
            columns: [
                {
                    field: 'import_batch_id',
                    title: 'Import Id',
                    width: '180px',
                },
                {
                    field: 'created_at',
                    title: 'Imported At',
                    sortable: true,
                    template: function (dataItem) {
                        if (!dataItem.created_at) return '';
                        const date = new Date(dataItem.created_at);
                        const day = String(date.getDate()).padStart(2, '0');
                        const month = String(date.getMonth() + 1).padStart(2, '0');
                        const year = date.getFullYear();

                        let hours = date.getHours();
                        const minutes = String(date.getMinutes()).padStart(2, '0');
                        const ampm = hours >= 12 ? 'PM' : 'AM';
                        hours = hours % 12 || 12;

                        return `${day}-${month}-${year} ${hours}:${minutes} ${ampm}`;
                    },
                    width: '180px',
                },
                {
                    field: 'imported_by',
                    title: 'Imported By',
                    width: '130px',
                    template: function (dataItem) {
                        const name = dataItem?.creator?.name ?? '-';
                        return bodyCellTemplate(name);
                    },
                },
                {
                    field: 'campus',
                    title: 'Campus',
                    width: '100px',
                    template: function (dataItem) {
                        return bodyCellTemplate(dataItem.campus);
                    },
                },
                {
                    field: 'semester',
                    title: 'Semester',
                    width: '100px',
                    template: function (dataItem) {
                        return bodyCellTemplate(dataItem.semester);
                    },
                },
                {
                    field: 'batch',
                    title: 'Batch',
                    width: '100px',
                    template: function (dataItem) {
                        return bodyCellTemplate(dataItem.batch);
                    },
                },
                {
                    field: 'start_week',
                    title: 'Start Week',
                    width: '100px',
                    template: function (dataItem) {
                        return bodyCellTemplate(formatDateToDMY(dataItem.start_week));
                    },
                },
                {
                    field: 'end_week',
                    title: 'End Week',
                    width: '100px',
                    template: function (dataItem) {
                        return bodyCellTemplate(formatDateToDMY(dataItem.end_week));
                    },
                },
                {
                    field: 'subject',
                    title: 'Subject Code',
                    width: '100px',
                    template: function (dataItem) {
                        return bodyCellTemplate(dataItem.subject);
                    },
                },
                {
                    field: 'teacher',
                    title: 'Teacher Code',
                    width: '100px',
                    template: function (dataItem) {
                        return bodyCellTemplate(dataItem.teacher);
                    },
                },
                {
                    field: 'room_code',
                    title: 'Room Code',
                    width: '100px',
                    template: function (dataItem) {
                        return bodyCellTemplate(dataItem.room_code);
                    },
                },
                {
                    field: 'status',
                    title: 'Status',
                    template: kendo.template($('#status-template').html()),
                    width: '100px',
                },
                {
                    title: 'Actions',
                    headerTemplate: `<a class="k-link" href="javascript:void(0)">Actions</a>`,
                    template: kendo.template($('#action-template').html()),
                    width: '200px',
                },
            ],
            sort: function (e) {
                if (e.sort.field === 'created_at') {
                    const direction = e.sort.dir;
                    if (typeof Livewire !== 'undefined' && Livewire.dispatch) {
                        Livewire.dispatch('toggleSortDirection', { dir: direction });
                    }
                }
            },
        });
    });

    Livewire.on('updateKendoGrid', (payload) => {
        const timetableImportGrid = $('#timetable-import-grid').data('kendoGrid');
        if (timetableImportGrid && payload[0].data) {
            const rows = payload[0].data?.data;
            const newDataSource = new kendo.data.DataSource({
                data: rows,
                pageSize: 10,
            });

            timetableImportGrid.setDataSource(newDataSource);
            timetableImportGrid.refresh();
        }
    });

    // Fix for button clickability on initial load
    setTimeout(function () {
        $('.k-upload-button').css('pointer-events', 'auto');
        $('.k-upload-button').removeClass('k-state-disabled');
    }, 500);

    // Re-initialize when Livewire updates the DOM
    document.addEventListener('livewire:load', function () {
        console.log('Livewire loaded');
        initKendoUploader();

        // Listen for Livewire updates
        Livewire.hook('message.processed', function () {
            setTimeout(function () {
                initKendoUploader();

                // Fix for button clickability
                $('.k-upload-button').css('pointer-events', 'auto');
                $('.k-upload-button').removeClass('k-state-disabled');
            }, 100);
        });

        // Additional fix for button clickability
        setTimeout(function () {
            $('.k-upload-button').css('pointer-events', 'auto');
            $('.k-upload-button').removeClass('k-state-disabled');
        }, 500);
    });
});

function bodyCellTemplate(data) {
    return `<span class="whitespace-nowrap text-sm text-gray-800">${data ? data : 'N/A'}</span>`;
}

$(document).on('click', '.k-dropzone', function (e) {
    if ($(e.target).is('input[type="file"]')) return;

    e.preventDefault();
    e.stopImmediatePropagation();

    $(this).find('input[type="file"]').get(0).click();
});
