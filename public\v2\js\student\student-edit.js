$(document).ready(function () {
    var addressHistoryModal = '#addressHistoryModal';
    var addressHistoryGrid = '#addressHistoryGrid';
    var profileHistoryModal = '#profileHistoryModal';
    var profileHistoryGrid = '#profileHistoryGrid';
    var addressHistoryFlag = true;
    var profileHistoryFlag = true;
    var emergencyDetailsHistoryModal = '#emergencyDetailsHistoryModal';
    var emergencyDetailsHistoryGrid = '#emergencyDetailsHistoryGrid';
    var emergencyDetailsHistoryFlag = true;

    addModalClassToWindows([
        addressHistoryModal,
        profileHistoryModal,
        emergencyDetailsHistoryModal,
    ]);

    $(addressHistoryModal).kendoWindow(openCenterWindow('Address Change History', 50, 15, 30));

    $(profileHistoryModal).kendoWindow(openCenterWindow('Profile Change History', 50, 15, 30));

    $(emergencyDetailsHistoryModal).kendoWindow(
        openCenterWindow('Emergency Details Change History', 60, 15, 25)
    );

    let usiDetailsForm = $('#usiDetailsForm').kendoForm({
        orientation: 'vertical',
        layout: 'grid',
        grid: { cols: 2, gutter: 24 },
        items: [
            // {
            //     field: "first_name",
            //     label: "First Name",
            //     colSpan: 1,
            // },
            // {
            //     field: "family_name",
            //     label: "Last Name",
            //     colSpan: 1,
            // },
            {
                field: 'USI',
                label: 'USI',
                colSpan: 1,
                hint: 'Enter your 10-digit alphanumeric USI',
            },
            // {
            //     field: "DOB",
            //     label: "Date of birth",
            //     editor: "DatePicker",
            //     editorOptions: {
            //         format: dateFormatFrontSideJS,
            //     },
            //     colSpan: 1,
            // },
            // {
            //     field: "isSingleName",
            //     label: "",
            //     editor: "CheckBoxGroup",
            //     editorOptions: {
            //         items: ["Single Name"],
            //         layout: "horizontal",
            //         labelPosition: "after",
            //     },
            //     colSpan: 2,
            // },
        ],
        buttonsTemplate: `
        <div class="mt-4 w-full">
            <div id="usiErrMsgDiv"></div>
            <button type="button" class="verifyUSI relative btn-primary px-4 font-medium min-w-[100px]">Verify</button>
        </div>
        `,
    });
    $('.usiValueFront').text(studentDetail.USI);
    if (usiDetailsForm.data('kendoForm')) {
        usiDetailsForm.data('kendoForm').setOptions({
            formData: {
                // first_name: studentDetail.first_name,
                // family_name: studentDetail.family_name,
                USI: studentDetail.USI,
                // DOB: studentDetail.DOB,
                // isSingleName: studentDetail.isSingleName,
            },
        });
    }
    let residentialAddressForm = $('#residentialAddressForm').kendoForm({
        orientation: 'vertical',
        layout: 'grid',
        grid: { cols: 2, gutter: 24 },
        items: [
            {
                field: 'autocompleteResidentialAddress',
                label: 'Address AutoFill',
                colSpan: 2,
            },
            {
                field: 'current_country',
                label: 'Country',
                colSpan: 1,
                editor: 'DropDownList',
                editorOptions: {
                    optionLabel: 'Select Service',
                    dataSource: getDropdownDataSource('get-country-list', []),
                    dataValueField: 'Id',
                    dataTextField: 'Name',
                    filter: 'contains',
                    filterInput: {
                        width: '100%',
                    },
                },
            },
            {
                field: 'current_building_name',
                label: 'Building/Property Name',
                colSpan: 1,
            },
            {
                field: 'current_unit_detail',
                label: 'Flat/Unit',
                colSpan: 1,
            },
            {
                field: 'current_street_no',
                label: 'Street Number',
                colSpan: 1,
            },
            {
                field: 'current_street_name',
                label: 'Street Name',
                colSpan: 1,
            },
            {
                field: 'current_city',
                label: 'City/Town/suburb',
                colSpan: 1,
            },
            {
                field: 'current_state',
                label: 'State/Province',
                validation: { required: true },
                colSpan: 1,
            },
            {
                field: 'current_postcode',
                label: 'Post Code',
                validation: { required: true },
                colSpan: 1,
            },
        ],
        buttonsTemplate: `
        <div class="flex gap-4 items-center justify-end mt-6">
            <button type="button" class="closeResidentialAddress btn-secondary px-4 font-medium">Cancel</button>
            <button type="button" class="saveResidentialAddressDetailDiv btn-primary px-4 font-medium">Save Changes</button>
        </div>
        `,
        submit: function (e) {
            saveModalData('api/update-student-details', 'residentialAddressDetailDiv');
            e.preventDefault();
            return false;
        },
    });
    residentialAddressForm.data('kendoForm').setOptions({
        formData: {
            current_country: studentDetail.current_country,
            current_building_name: studentDetail.current_building_name,
            current_unit_detail: studentDetail.current_unit_detail,
            current_street_no: studentDetail.current_street_no,
            current_street_name: studentDetail.current_street_name,
            current_city: studentDetail.current_city,
            current_state: studentDetail.current_state,
            current_postcode: studentDetail.current_postcode,
        },
    });
    manageUploadDiv();
    let studentDetailForm = $('#studentDetailForm').kendoForm({
        orientation: 'vertical',
        layout: 'grid',
        grid: { cols: 2, gutter: 24 },
        items: [
            {
                field: 'first_name',
                label: 'First Name',
                attributes: { disabled: true },
                validation: { required: true },
                colSpan: 1,
            },
            {
                field: 'last_name',
                label: 'Last Name',
                attributes: { disabled: true },
                validation: { required: true },
                colSpan: 1,
            },
            {
                field: 'email',
                label: 'Email Address',
                attributes: { disabled: true },
                validation: {
                    required: true,
                    email: true, // Use built-in email validation
                },
                colSpan: 2,
            },
            {
                field: 'optional_email',
                label: 'Optional Email Address',
                validation: {
                    required: true,
                    email: true, // Use built-in email validation
                },
                colSpan: 2,
            },
            {
                field: 'mobile',
                label: 'Mobile No',
                validation: { required: true },
                colSpan: 2,
            },
        ],
        buttonsTemplate: `
        <div class="flex gap-4 items-center justify-end mt-6">

            <button type="button" class="closeStudentBasic btn-secondary px-4 font-medium">Cancel</button>
            <button type="button" class="studentDetailSaveDiv btn-primary px-4 font-medium">Save Changes</button>

        </div>
        `,
    });
    studentDetailForm.data('kendoForm').setOptions({
        formData: {
            first_name: studentDetail.first_name,
            last_name: studentDetail.family_name,
            email: studentDetail.email,
            mobile: studentDetail.current_mobile_phone,
            optional_email: studentDetail.optional_email,
        },
    });
    $('#passwordForm').kendoForm({
        orientation: 'vertical',
        formData: {},
        layout: 'grid',
        grid: { cols: 1, gutter: 16 },
        items: [
            {
                field: 'old_password',
                label: 'Old Password',
                attributes: {
                    placeholder: 'Enter Old Password',
                },
                validation: { required: true },
                editor: function (container, options) {
                    $(getPasswordInputHtml(options, 'Enter Old Password'))
                        .appendTo(container)
                        .kendoTextBox();
                },
            },
            {
                field: 'new_password',
                label: 'New Password',
                attributes: {
                    placeholder: 'Enter New Password',
                },
                validation: { required: true },
                editor: function (container, options) {
                    $(getPasswordInputHtml(options, 'Enter New Password'))
                        .appendTo(container)
                        .kendoTextBox();
                },
            },
            {
                field: 'password',
                label: 'Confirm Password',
                attributes: {
                    placeholder: 'Enter Confirm Password',
                },
                validation: { required: true },
                editor: function (container, options) {
                    $(getPasswordInputHtml(options, 'Enter Confirm Password'))
                        .appendTo(container)
                        .kendoTextBox();
                },
            },
        ],
        buttonsTemplate: `<div class="flex sticky bottom-0 gap-4 items-center justify-end mt-6">
            <input type="hidden" id="user_id" name="user_id">
            <button type="button" class="closeChangePassword btn-secondary px-4 font-medium">Cancel</button>
            <button type="submit" class="btn-primary">
                <p class="text-sm font-medium leading-4 text-white">Change Password</p>
            </button>
        </div>`,
        submit: function (ev) {
            saveModalData('api/update-user-password', 'changePasswordDiv');
            ev.preventDefault();
            return false;
        },
    });

    function getPasswordInputHtml(options, placeholder = 'Enter Old Password') {
        let formHtml = `
            <div class="relative flex w-full items-center mt-1 !overflow-visible border border-gray-[#e5e7eb] invalid-form-input">
                <div class="relative w-full" x-data="{ show: true, error: false }">
                    <input
                        x-bind:type="show ? 'password' : 'text'"
                        x-on:keyup="error=false;"
                        id="${options.field}"
                        class="tw-password-input k-input k-textbox rounded-lg border-0 bg-transparent px-0 py-2 text-sm leading-5 text-gray-700 block w-full !focus:shadow-none !shadow-none"
                        type="password"
                        name="${options.field}"
                        placeholder="${placeholder}"
                        required="required"
                        autocomplete="${options.field}"
                        >
                    <div class="absolute inset-y-0 right-0 flex items-center pr-1 text-sm leading-5 cursor-pointer">
                        <svg class="h-4 text-gray-500 block" fill="none" :class="{ 'hidden': !show, 'block': show }"
                            xmlns="http://www.w3.org/2000/svg" viewBox="0 0 576 512" x-on:click="show = !show;">
                            <path fill="currentColor"
                                d="M572.52 241.4C518.29 135.59 410.93 64 288 64S57.68 135.64 3.48 241.41a32.35 32.35 0 0 0 0 29.19C57.71 376.41 165.07 448 288 448s230.32-71.64 284.52-177.41a32.35 32.35 0 0 0 0-29.19zM288 400a144 144 0 1 1 144-144 143.93 143.93 0 0 1-144 144zm0-240a95.31 95.31 0 0 0-25.31 3.79 47.85 47.85 0 0 1-66.9 66.9A95.78 95.78 0 1 0 288 160z">
                            </path>
                        </svg>
                        <svg class="h-4 text-gray-500 hidden" fill="none" :class="{ 'block': !show, 'hidden': show }"
                            xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512" x-on:click="show = !show">
                            <path fill="currentColor"
                                d="M320 400c-75.85 0-137.25-58.71-142.9-133.11L72.2 185.82c-13.79 17.3-26.48 35.59-36.72 55.59a32.35 32.35 0 0 0 0 29.19C89.71 376.41 197.07 448 320 448c26.91 0 52.87-4 77.89-10.46L346 397.39a144.13 144.13 0 0 1-26 2.61zm313.82 58.1l-110.55-85.44a331.25 331.25 0 0 0 81.25-102.07 32.35 32.35 0 0 0 0-29.19C550.29 135.59 442.93 64 320 64a308.15 308.15 0 0 0-147.32 37.7L45.46 3.37A16 16 0 0 0 23 6.18L3.37 31.45A16 16 0 0 0 6.18 53.9l588.36 454.73a16 16 0 0 0 22.46-2.81l19.64-25.27a16 16 0 0 0-2.82-22.45zm-183.72-142l-39.3-30.38A94.75 94.75 0 0 0 416 256a94.76 94.76 0 0 0-121.31-92.21A47.65 47.65 0 0 1 304 192a46.64 46.64 0 0 1-1.54 10l-73.61-56.89A142.31 142.31 0 0 1 320 112a143.92 143.92 0 0 1 144 144c0 21.63-5.29 41.79-13.9 60.11z">
                            </path>
                        </svg>
                    </div>
                </div>
            </div>
        `;
        return formHtml;
    }

    function dispatchCustomEvent(eventName, detail = {}) {
        const event = new CustomEvent(eventName, {
            detail: detail,
        });
        window.dispatchEvent(event);
    }
    function saveModalData(url = '', formId = '') {
        if (formId.length > 0) {
            let dataArr = {};
            let serializeArr = $(document)
                .find('#' + formId)
                .find('input[name], select[name], textarea[name],hidden')
                .serializeArray();
            $(serializeArr).each(function (i, field) {
                dataArr[field.name] = field.value;
            });
            dataArr['user_id'] = user_id;
            $.ajax({
                type: 'POST',
                url: site_url + url,
                dataType: 'json',
                data: dataArr,
                success: function (response) {
                    notificationDisplay(response.message, '', response.status);
                    if (response.status !== 'error') {
                        dispatchCustomEvent('formSubmitted', {
                            detail: 'changePasswordDiv',
                        });
                        $('#passwordForm')[0].reset();
                    }
                },
                error: function (response) {
                    notificationDisplay(
                        response.responseJSON.message,
                        '',
                        response.responseJSON.status
                    );
                },
            });
        }
    }

    $('body').on('click', '.verifyUSI', function (e) {
        let dataArr = {};
        let serializeArr = $(document)
            .find('#saveUSIDetailsDiv')
            .find('input[name], select[name], textarea[name],hidden')
            .serializeArray();
        $(serializeArr).each(function (i, field) {
            dataArr[field.name] = field.value;
        });
        dataArr['dateOfBirth'] = dataArr.DOB;
        $.ajax({
            type: 'POST',
            url: site_url + 'api/verify-student-usi-number',
            data: dataArr,
            beforeSend: function () {
                isUsiLoading(true);
            },
            success: function (response) {
                $('#saveUSIDetailsDiv').find('.usiNumber').text(dataArr.USI);
                $('.family_name_value').find('span').text(response.data.family_name);
                $('.first_name_value').find('span').text(response.data.first_name);
                isUsiLoading(false);
                if (
                    response.success === 1 &&
                    response.data.Result &&
                    response.data.Result.includes('Valid')
                ) {
                    showUsiValidMsg(dataArr.USI);
                    notificationDisplay('Verified Successfully', '', response.status);
                } else {
                    showUsiErrorMsg(response.data.message);
                }
                ajaxActionV2(
                    'api/update-student-details',
                    'POST',
                    dataArr,
                    function (saveResponse) {
                        dispatchCustomEvent('formSubmitted', { detail: 'usiDiv' });

                        notificationDisplay(saveResponse.message, '', saveResponse.status);
                    }
                );
            },
            error: function () {
                showUsiErrorMsg();
            },
        });
    });
    $('body').on('click', '.studentDetailSaveDiv', function (e) {
        let dataArr = {};
        let serializeArr = $(document)
            .find('#studentDetailDiv')
            .find('input[name], select[name], textarea[name],hidden')
            .serializeArray();
        $(serializeArr).each(function (i, field) {
            dataArr[field.name] = field.value;
        });
        $.ajax({
            type: 'POST',
            url: site_url + 'api/update-student-details',
            dataType: 'json',
            data: dataArr,
            success: function (response) {
                if (response.status == 'error') {
                    notificationDisplay(response.message, '', response.status);
                    return false;
                }
                $('.current_mobile_phone_value')
                    .find('div')
                    .find('span')
                    .text(response.data.mobile);
                $('.optional_email_value')
                    .find('div')
                    .find('span')
                    .text(response.data.optional_email);
                dispatchCustomEvent('formSubmitted', {
                    detail: 'editProfileDiv',
                });
                notificationDisplay(response.message, '', response.status);
            },
        });
    });
    $('body').on('click', '.saveResidentialAddressDetailDiv', function (e) {
        let dataArr = {};
        let serializeArr = $(document)
            .find('#residentialAddressDetailDiv')
            .find('input[name], select[name], textarea[name],hidden')
            .serializeArray();
        $(serializeArr).each(function (i, field) {
            dataArr[field.name] = field.value;
        });
        $.ajax({
            type: 'POST',
            url: site_url + 'api/update-student-details',
            dataType: 'json',
            data: dataArr,
            success: function (response) {
                $('.current_building_name_value')
                    .find('span')
                    .text(response.data.current_building_name);
                $('.current_unit_detail_value')
                    .find('span')
                    .text(response.data.current_unit_detail);
                $('.current_street_no_value').find('span').text(response.data.current_street_no);
                $('.current_street_name_value')
                    .find('span')
                    .text(response.data.current_street_name);
                $('.current_state_value').find('span').text(response.data.current_state);
                $('.current_postcode_value').find('span').text(response.data.current_postcode);
                dispatchCustomEvent('formSubmitted', {
                    detail: 'residentialAddressDiv',
                });
                notificationDisplay(response.message, '', response.status);
            },
        });
    });
    $('body').on('click', '.closeUSIStudent', function (e) {
        dispatchCustomEvent('formSubmitted', { detail: 'usiDiv' });
    });
    $('body').on('click', '.closeStudentBasic', function (e) {
        dispatchCustomEvent('formSubmitted', { detail: 'editProfileDiv' });
    });
    $('body').on('click', '.closeResidentialAddress', function (e) {
        dispatchCustomEvent('formSubmitted', {
            detail: 'residentialAddressDiv',
        });
    });
    $('body').on('click', '.closeEmergencyContact ', function (e) {
        dispatchCustomEvent('formSubmitted', {
            detail: 'emergencyContactDiv',
        });
    });

    $('body').on('click', '.closeChangePassword', function (e) {
        dispatchCustomEvent('formSubmitted', {
            detail: 'changePasswordDiv',
        });
        $('#passwordForm')[0].reset();
    });

    function showUsiErrorMsg(message = 'Invalid USI. Please contact your college.') {
        let usiErrMsgTemplate = kendo.template($('#usiErrorAlert').html())({
            message: message,
        });
        let usiErrMsgDiv = $('#usiErrMsgDiv');

        $(document).find('#saveUSIDetailsDiv').addClass('tw-form-invalid');
        $(document).find(usiErrMsgDiv).html(usiErrMsgTemplate).addClass('mb-4');
    }

    function showUsiValidMsg(usi = '') {
        let usiValidTemplate = kendo.template($('#usiValidDiv').html())({
            usi: usi,
        });
        let usiSuccessDiv = $('#saveUSIDetailsDiv');

        $(document).find(usiSuccessDiv).html(usiValidTemplate);
    }

    function isUsiLoading(isLoading = false) {
        if (!isLoading) {
            $(document).find('.verifyUSI').html(`Verify`);
            return;
        }
        $(document)
            .find('.verifyUSI')
            .html(
                `<div class="vue-simple-spinner animate-spin" style="border-radius: 100%; border-color: rgb(50, 157, 243) rgb(255, 255, 255) rgb(255, 255, 255); border-style: solid; border-width: 3px; border-image: none 100% / 1 / 0 stretch; width: 20px; height: 20px;"></div>`
            );
    }

    function manageUploadDiv() {
        var selectedDataArr = [];
        selectedDataArr = {
            college_id: studentDetail.college_id,
            student_id: studentDetail.id,
        };
        const basePath = window.location.pathname.split('/')[1].includes('kendo')
            ? window.location.pathname.split('/')[1]
            : '';
        const baseUrl = window.location.origin + '/' + basePath;
        $(document)
            .find('#stud_profile_pic')
            .kendoUpload({
                async: {
                    chunkSize: 20000000, // bytes
                    saveUrl: baseUrl + 'api/upload-student-profile-pic',
                    // removeUrl: baseUrl + "api/remove-student-profile-pic",
                    //autoUpload: true,
                },
                upload: function (e) {
                    e.data = selectedDataArr;
                    let fileData = e.files[0];

                    if (typeof fileData.validationErrors !== 'undefined') {
                        let errorData = fileData.validationErrors[0];
                        if (errorData == 'invalidMaxFileSize') {
                            notificationDisplay('File size must be less from 2MB.', '', 'error');
                            e.preventDefault();
                        }
                        if (errorData == 'invalidFileExtension') {
                            notificationDisplay(
                                'Only allow file type jpg, jpeg or png',
                                '',
                                'error'
                            );
                            e.preventDefault();
                        }
                        return false;
                    }
                },
                multiple: false,
                /*validation: {
                    allowedExtensions: ['.jpg', '.JPG', '.png', '.PNG', '.jpeg', '.JPEG'],
                    maxFileSize: 2000000, // 2mb
                },*/
                success: function (e) {
                    if (e.response.status == 'success') {
                        notificationDisplay(e.response.message, '', e.response.status);
                    }
                    if (e.operation == 'upload') {
                        for (var i = 0; i < e.files.length; i++) {
                            let file = e.files[i].rawFile;
                            if (file) {
                                var reader = new FileReader();
                                reader.onloadend = function () {
                                    $('.display_picture').html(
                                        '<img src="' +
                                            this.result +
                                            '" / class="w-16 h-16 rounded-full">'
                                    );
                                    $('#profile_pic').val(this.result);
                                };
                                reader.readAsDataURL(file);
                            }
                        }
                    }
                },
                error: function (e) {
                    let response = null;

                    try {
                        response = JSON.parse(e.XMLHttpRequest.responseText);
                    } catch (err) {
                        response = {
                            message: ['Unexpected error occurred.'],
                            status: 'error',
                        };
                    }
                    const message = Array.isArray(response.message)
                        ? response.message.join('<br>')
                        : response.message;

                    notificationDisplay(message, '', response.status);
                    var upload = $('#stud_profile_pic').data('kendoUpload');
                    upload.clearAllFiles();
                },
            });
    }

    var input = document.getElementById('autocompleteResidentialAddress');
    var autocomplete = new google.maps.places.Autocomplete(input);

    google.maps.event.addListener(autocomplete, 'place_changed', function () {
        var place = autocomplete.getPlace();
        var components_by_type = {};
        for (var i = 0; i < place.address_components.length; i++) {
            var c = place.address_components[i];
            components_by_type[c.types[0]] = c;
        }

        $('#current_building_name').val('');
        var suburb =
            components_by_type['locality'] !== undefined
                ? components_by_type['locality'].long_name
                : '';
        $('#current_city').val(suburb);

        var unitNo =
            components_by_type['subpremise'] !== undefined
                ? components_by_type['subpremise'].long_name
                : '';
        $('#current_unit_detail').val(unitNo);

        var street_no =
            components_by_type['street_number'] !== undefined
                ? components_by_type['street_number'].long_name
                : '';
        $('#current_street_no').val(street_no);
        var street_name =
            components_by_type['route'] !== undefined ? components_by_type['route'].long_name : '';
        $('#current_street_name').val(street_name);
        var postcode =
            components_by_type['postal_code'] !== undefined
                ? components_by_type['postal_code'].long_name
                : '';
        $('#current_postcode').val(postcode);

        if (components_by_type['country'].long_name == 'Australia') {
            var state_short =
                components_by_type['administrative_area_level_1'] !== undefined
                    ? components_by_type['administrative_area_level_1'].short_name
                    : '';
            $('#current_state').val(state_short);
        } else {
            var state =
                components_by_type['administrative_area_level_1'] !== undefined
                    ? components_by_type['administrative_area_level_1'].long_name
                    : '';
            $('#current_state').val(state);
        }
        var country =
            components_by_type['country'] !== undefined
                ? components_by_type['country'].long_name
                : '';
        var dropdownlist = $('#current_country').data('kendoDropDownList');
        dropdownlist.select(function (dataItem) {
            return dataItem.Name === country;
        });
        var unitNo =
            components_by_type['subpremise'] !== undefined
                ? components_by_type['subpremise'].long_name
                : '';
        $('#current_unit_detail').val(unitNo);
        $('#current_building_name').val('');
        //  $("#country option").filter(function(index) { return $(this).text() === country; }).attr('selected', 'selected');
    });

    $('body').on('click', '#addressHistoryBtn', function (e) {
        let dataArr = {
            college_id: studentDetail.college_id,
            student_id: studentDetail.id,
            type: 'student_address',
        };
        if (addressHistoryFlag) {
            addressHistoryFlag = false;
            $(addressHistoryGrid).kendoGrid({
                dataSource: customDataSource(
                    'api/get-student-address-history',
                    {
                        updated_field: { type: 'string' },
                        updated_on: { type: 'date' },
                        updated_by: { type: 'string' },
                    },
                    dataArr
                ),
                columns: [
                    {
                        field: 'updated_field',
                        title: 'Address',
                        minResizableWidth: 100,
                    },
                    {
                        field: 'updated_on',
                        title: 'Updated On',
                        minResizableWidth: 100,
                        template:
                            "<div class=' flex items-center text-13 leading-4 text-gray-600'>#: kendo.toString(updated_on,'" +
                            dateFormatFrontSideJS +
                            "') # </div>",
                    },
                    {
                        field: 'updated_by',
                        title: 'Updated By',
                        minResizableWidth: 100,
                    },
                ],
                noRecords: noRecordTemplate(),
            });
            customGridHtml(addressHistoryGrid);
        } else {
            refreshGrid(addressHistoryGrid, dataArr);
        }
        setTimeout(() => {
            kendowindowOpen(addressHistoryModal);
        }, 500);
    });

    function noRecordTemplate() {
        return { template: noRecordTemplateHtml() };
    }

    function noRecordTemplateHtml(subHeading = "Sorry, we couldn't find any results.") {
        return (
            "<div class='inline-flex flex-col space-y-4 p-6 items-center justify-center w-full'>" +
            "<span class='k-font-icon k-i-clipboard k-icon-64 icon-color-blue'></span>" +
            "<div class='flex flex-col space-y-2 items-center justify-center'>" +
            "<h3 class='text-xl font-bold leading-7 text-center text-gray-800'>No Results Found</h3>" +
            "<p class='text-sm leading-5 font-normal text-center text-gray-500'>" +
            subHeading +
            '</p>' +
            '</div>' +
            '</div>'
        );
    }
    $('body').on('click', '#profileHistoryBtn', function (e) {
        let dataArr = {
            college_id: studentDetail.college_id,
            student_id: studentDetail.id,
            type: 'student_email_phone',
        };
        if (profileHistoryFlag) {
            profileHistoryFlag = false;
            $(profileHistoryGrid).kendoGrid({
                dataSource: customDataSource(
                    'api/get-student-address-history',
                    {
                        updated_field: { type: 'string' },
                        updated_on: { type: 'date' },
                        updated_value: { type: 'string' },
                        updated_by: { type: 'string' },
                    },
                    dataArr
                ),
                columns: [
                    {
                        field: 'updated_value',
                        title: 'Properties',
                        width: 500,
                        template: function (dataItem) {
                            return manageProperties(dataItem.properties);
                        },
                    },
                    {
                        field: 'updated_on',
                        title: 'Updated On',
                        width: 100,
                        template:
                            "<div class=' flex items-center text-13 leading-4 text-gray-600'>#: kendo.toString(updated_on,'" +
                            dateFormatFrontSideJS +
                            "') # </div>",
                    },
                    {
                        field: 'updated_by',
                        title: 'Updated By',
                        width: 100,
                    },
                ],
                noRecords: noRecordTemplate(),
            });
            customGridHtml(profileHistoryGrid);
        } else {
            refreshGrid(profileHistoryGrid, dataArr);
        }
        setTimeout(() => {
            kendowindowOpen(profileHistoryModal);
        }, 500);
    });

    $('body').on('focus', '#usiDetailsForm input', function () {
        let usiErrMsgDiv = $('#usiErrMsgDiv');
        $(document).find(usiErrMsgDiv).html('').removeClass('mb-4');
        $(document).find('#saveUSIDetailsDiv').removeClass('tw-form-invalid');
        // Perform other actions here
    });

    function kendowindowOpen(windowID) {
        let kendoWindow = $(document).find(windowID);
        kendoWindow.data('kendoWindow').center().open();
        kendoWindow.data('kendoWindow').wrapper.addClass('k-modal-window');
    }

    $('body').on('click', '#emergencyDetailsHistoryBtn', function (e) {
        let dataArr = {
            college_id: studentDetail.college_id,
            student_id: studentDetail.id,
            type: 'student_emergency_details',
        };
        if (emergencyDetailsHistoryFlag) {
            emergencyDetailsHistoryFlag = false;
            $(emergencyDetailsHistoryGrid).kendoGrid({
                dataSource: customDataSource(
                    'api/get-student-emergency-details-history',
                    {
                        value: { type: 'string' },
                        updated_on: { type: 'date' },
                        updated_by: { type: 'string' },
                    },
                    dataArr
                ),
                columns: [
                    {
                        field: 'value',
                        title: 'Properties',
                        template: function (dataItem) {
                            return manageProperties(dataItem.properties);
                        },
                        width: 500,
                    },
                    {
                        field: 'updated_on',
                        title: 'Updated On',
                        width: 100,
                        template:
                            "<div class=' flex items-center text-13 leading-4 text-gray-600'>#: kendo.toString(updated_on,'" +
                            dateFormatFrontSideJS +
                            "') # </div>",
                    },
                    {
                        field: 'updated_by',
                        title: 'Updated By',
                        width: 100,
                    },
                ],
                noRecords: noRecordTemplate(),
            });
            customGridHtml(emergencyDetailsHistoryGrid);
        } else {
            refreshGrid(emergencyDetailsHistoryGrid, dataArr);
        }
        setTimeout(() => {
            kendowindowOpen(emergencyDetailsHistoryModal);
        }, 500);
    });

    function manageProperties(dataItem) {
        let result = '';
        result += "<div class='log-grid mb-6'>";
        for (let j = 0; j < dataItem.length; j++) {
            result +=
                "<div class='grid grid-cols-6 gap-4 border-b border-gray-200 py-2 px-2'>" +
                "<div class='col-span-2'>" +
                "<div class='tw-badge inline-flex items-center justify-center px-3 py-1 w-fit text-xs border-none leading-normal bg-primary-blue-500 text-white rounded-md'>" +
                dataItem[j]['field_name'] +
                '</div>' +
                '</div>' +
                "<div class='col-span-4'>" +
                "<div class='flex items-center gap-2'>" +
                "<div class='tw-badge inline-flex items-center justify-center px-3 py-1 w-fit text-xs border-none leading-normal bg-green-50 rounded-md text-gray-600'>" +
                dataItem[j]['old'] +
                '</div>' +
                "<svg width='14' height='14' viewBox='0 0 20 20' fill='none' xmlns='http://www.w3.org/2000/svg'>" +
                "<path fill='currentColor' d='M10.837 3.13a.5.5 0 0 0-.674.74L16.33 9.5H2.5a.5.5 0 0 0 0 1h13.828l-6.165 5.628a.5.5 0 0 0 .674.739l6.916-6.314a.747.747 0 0 0 0-1.108z'></path>" +
                '</svg>' +
                "<div class='tw-badge inline-flex items-center justify-center px-3 py-1 w-fit text-xs border-none leading-normal bg-red-50 rounded-md text-gray-400'>" +
                dataItem[j]['attributes'] +
                '</div>' +
                '</div>' +
                '</div>' +
                '</div>';
        }

        result += '</div>';

        return result;
    }
    let emergencyContactForm = $('#emergencyContactForm').kendoForm({
        orientation: 'vertical',
        layout: 'grid',
        grid: { cols: 2, gutter: 24 },
        items: [
            {
                field: 'emergency_contact_person',
                label: 'Contact Person',
                colSpan: 1,
            },
            {
                field: 'emergency_relationship',
                label: 'Relationship',
                colSpan: 1,
            },
            {
                field: 'emergency_phone',
                label: 'Phone',
                colSpan: 1,
            },
            {
                field: 'emergency_email',
                label: 'Email',
                colSpan: 1,
            },
            {
                field: 'emergency_address',
                label: 'Address',
                colSpan: 2,
            },
        ],
        buttonsTemplate: `
        <div class="flex gap-4 items-center justify-end mt-6">
            <button type="button" class="closeEmergencyContact btn-secondary px-4 font-medium">Cancel</button>
            <button type="submit" class="saveEmergencyContactDetailDiv btn-primary px-4 font-medium">Save Changes</button>
        </div>
        `,
        submit: function (e) {
            saveModalData('api/update-additional-students', 'emergencyContactDiv');
            e.preventDefault();
            return false;
        },
    });

    emergencyContactForm.data('kendoForm').setOptions({
        formData: {
            emergency_contact_person: studentDetail.emergency_contact_person,
            emergency_relationship: studentDetail.emergency_relationship,
            emergency_phone: studentDetail.emergency_phone,
            emergency_email: studentDetail.emergency_email,
            emergency_address: studentDetail.emergency_address,
        },
    });
});
