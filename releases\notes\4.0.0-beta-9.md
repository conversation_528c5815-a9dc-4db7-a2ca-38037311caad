## VERSION 4.0.0-beta-9 (2025-06-27)

<!-- #### Update 3.6.12 (july 18th)
- Fixed character encoding on course template
- Double click bug on timetable fixed -->

#### Bugfixes

- Student Portal
    - Student Portal mobile responsiveness fixes
    - Dashboard profile picture not showing issue fix
- Assign Supervisor
    - Supervisor sorting not working issue fix

#### Features

- Result Import
    - Add new feature for filter by status
    - Result Import page mobile responsiveness
    - Pdf Template page editor replaced with ckeditor
- Manage Report
    - Generate a grade-wise report using the Manage Report module.
- Student Profile
    - When a student's course status is changed to Defer, Suspend, Withdrawn, or Cancelled, the system should automatically open the 'Defer/Withdrawn/Cancelled' form, and upon submission, update the course status accordingly.
    - Generate Report : Course progression report should display both unit code and corresponding unit description.
- Implement webhook when student is offered
- Result Import Fixes
- Add page number to letter pdf
- Convert trainer modules to SPA
