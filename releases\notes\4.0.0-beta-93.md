## VERSION 4.0.0-beta-93 (2025-07-11)

#### Bugfixes

- Resolved issue with the Course tab where the form submission for edit course enrollment and add unit module.
- Resolved issue with the Course tab where the form submission for edit course enrollment and add unit module.
- Fixed issue with generating the invoice schedule under the Payment tab.
- Call webhook on change student course status.
- set notification on student course status change.
- set notification email of student course status change on IT email that we have add on geneal info.
- Fixed cache issue causing incorrect Redirect & Webhook URLs to appear in the Xero Setup Guide.
- Add emergency contact history in student profile.
- Ui fixes - Deletion of letter, email and sms templates - Dataview component for list/grid view card preview - Elearning edit form autofill issue fix - Forms in student portal text truncate removed - Student profile reupload issue fix - Timetable sort issue resolved by removing allow unsort
- Fix Image resize issue when image is upload
- Fix Student List issue on orenatation
- Rotated image due to not correcting the image orientation using EXIF data fix (replicated on samsung galaxy s23 device)
- Remove old Listener code for student.
- update end point in apgent api's
- Add Log for Email, Letter and SMS template.

#### Features

- Display popup showing sync status and failure reason when applying bulk sync to zoho
- Display popup showing sync status and failure reason when applying bulk sync to Zoho.
- Add emergency contact history in student profile
- Added an option in General Info settings to specify "DeanName". Also added three new tags for use in letter and email templates: "DeanName","DeanSignature" and "CollegeSignature"
- Kendo dropdown blade component created
- Async Data View component selection feature added
- Loader and at least 3 character validation added
- Risk Assessment: Risk assesment list and detail page ui scaffolding

#### Improvements

- Edit profile page responsive for button fix

#### Other

- Async Grid column filter and more action menu component created
