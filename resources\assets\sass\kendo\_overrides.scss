// Compatible with @progress/kendo-theme-default v.7.0.0

%tb-typography {
    @include typography-classes($tb-typography);
}

%tb-effects {
    @include effects-classes($tb-effects);
}

.k-button-group .k-button {
    row-gap: $tb-kendo-border-radius;
    width: auto;
}
.k-button.k-button-solid-base {
    border-bottom-color: $kendo-border-color;
    border-left-color: $kendo-border-color;
    border-right-color: $kendo-border-color;
    border-top-color: $kendo-border-color;
}
.k-button {
    outline-offset: inherit;
    outline-style: solid;
    outline-width: inherit;
}
.k-button.k-button-md {
    @extend %tb-typography-kendo-default-typography;
}
.k-dropdown-button {
    padding-bottom: inherit;
    padding-left: inherit;
    padding-right: inherit;
    padding-top: inherit;
    justify-content: center;
    align-items: center;
    border-bottom-left-radius: $tb-kendo-border-radius;
    border-bottom-right-radius: $tb-kendo-border-radius;
    border-top-left-radius: $tb-kendo-border-radius;
    border-top-right-radius: $tb-kendo-border-radius;
}
.k-dropdown-button .k-button.k-hover.k-button-md,
.k-dropdown-button .k-button.k-state-hover.k-button-md,
.k-dropdown-button .k-button.k-state-hovered.k-button-md,
.k-dropdown-button .k-button:hover.k-button-md {
    padding-bottom: calc($kendo-padding-input / 2);
    padding-left: $kendo-padding-input;
    padding-right: $kendo-padding-input;
    padding-top: calc($kendo-padding-input / 2);
}
.k-popup.k-menu-popup .k-group.k-menu-group {
    border-bottom-left-radius: $tb-kendo-border-radius;
    border-bottom-right-radius: $tb-kendo-border-radius;
    border-top-left-radius: $tb-kendo-border-radius;
    border-top-right-radius: $tb-kendo-border-radius;
    margin-inline: 0 !important;
}
.k-popup.k-list-container .k-list.k-list-md .k-list-content .k-list-ul {
    @extend %tb-effects-tb-internal-none-effects;
    border-bottom-left-radius: $tb-kendo-border-radius;
    border-bottom-right-radius: $tb-kendo-border-radius;
    border-top-left-radius: $tb-kendo-border-radius;
    border-top-right-radius: $tb-kendo-border-radius;
}
.k-popup.k-menu-popup {
    border-bottom-left-radius: $tb-kendo-border-radius;
    border-bottom-right-radius: $tb-kendo-border-radius;
    border-top-left-radius: $tb-kendo-border-radius;
    border-top-right-radius: $tb-kendo-border-radius;
    border-bottom-width: 0px;
    border-left-width: 0px;
    border-right-width: 0px;
    border-top-width: 0px;
}
.k-popup.k-list-container {
    border-bottom-width: 0px;
    border-left-width: 0px;
    border-right-width: 0px;
    border-top-width: 0px;
    border-bottom-left-radius: $tb-kendo-border-radius;
    border-bottom-right-radius: $tb-kendo-border-radius;
    border-top-left-radius: $tb-kendo-border-radius;
    border-top-right-radius: $tb-kendo-border-radius;
    // width: 240px;
    border: none;
}

.k-popup {
    &.k-menu-popup {
        .k-menu-group {
            padding: 0.25rem;
            gap: 0.125rem;

            .k-menu-item {
                border-radius: $tb-kendo-border-radius;
                &:is(:hover, :focus) {
                    background-color: var(--color-gray-100);
                }
                & > .k-menu-link {
                    &:hover {
                        color: var(--color-gray-700);
                    }
                }
            }
        }
    }
    .k-context-menu {
        padding: 0.25rem;
        gap: 0.125rem;

        .k-menu-item {
            min-width: 120px;
            border-radius: $tb-kendo-border-radius;
            &:is(:hover, :focus, :active) {
                background-color: var(--color-gray-100);
            }
            & > .k-menu-link {
                padding-inline: 0.25rem;
                border-radius: $tb-kendo-border-radius;
                &:hover {
                    color: var(--color-gray-700);
                }
            }
        }
    }
}

.k-menu-group .k-item:focus > .k-link,
.k-menu-group .k-item.k-focus > .k-link,
.k-menu.k-context-menu .k-item:focus > .k-link,
.k-menu.k-context-menu .k-item.k-focus > .k-link {
    box-shadow: none;
}

.k-list.k-list-md .k-list-content .k-list-ul .k-list-item.k-selected {
    border-bottom-left-radius: $tb-kendo-border-radius;
    border-bottom-right-radius: $tb-kendo-border-radius;
    border-top-left-radius: $tb-kendo-border-radius;
    border-top-right-radius: $tb-kendo-border-radius;
    color: $tb-kendo-heading-text;
    background-color: $tb-kendo-color-primary;
    background-image: none;
}
.k-list.k-list-md .k-list-content .k-list-ul .k-list-item.k-focus,
.k-list.k-list-md .k-list-content .k-list-ul .k-list-item.k-state-focus,
.k-list.k-list-md .k-list-content .k-list-ul .k-list-item.k-state-focused,
.k-list.k-list-md .k-list-content .k-list-ul .k-list-item:focus {
    box-shadow: none;
}
.k-list.k-list-md .k-list-content .k-list-ul .k-list-item.k-focus .k-list-item-text,
.k-list.k-list-md .k-list-content .k-list-ul .k-list-item.k-state-focus .k-list-item-text,
.k-list.k-list-md .k-list-content .k-list-ul .k-list-item.k-state-focused .k-list-item-text,
.k-list.k-list-md .k-list-content .k-list-ul .k-list-item:focus .k-list-item-text {
    background-color: $tb-kendo-base-bg;
    background-image: none;
}
.k-button.k-focus,
.k-button.k-state-focus,
.k-button.k-state-focused,
.k-button:focus {
    outline-offset: 2px;
    outline-color: $tb-kendo-color-primary;
    outline-width: thin;
}

.k-popup {
    color: var(--color-gray-700);
}

.k-checkbox-wrap {
    flex: 1;
}
.k-checkbox-wrap .k-checkbox.k-focus,
.k-checkbox-wrap .k-checkbox.k-state-focus,
.k-checkbox-wrap .k-checkbox.k-state-focused,
.k-checkbox-wrap .k-checkbox:focus {
    outline-offset: 2px;
    outline-style: solid;
    outline-width: thin;
    outline-color: $tb-kendo-color-primary;
}
.k-radio-list.k-list-horizontal .k-radio-item .k-radio.k-radio-md.k-checked.k-focus,
.k-radio-list.k-list-horizontal .k-radio-item .k-radio.k-radio-md:checked.k-focus,
.k-radio-list.k-list-horizontal .k-radio-item .k-radio.k-radio-md.k-checked.k-state-focus,
.k-radio-list.k-list-horizontal .k-radio-item .k-radio.k-radio-md.k-checked.k-state-focused,
.k-radio-list.k-list-horizontal .k-radio-item .k-radio.k-radio-md.k-checked:focus,
.k-radio-list.k-list-horizontal .k-radio-item .k-radio.k-radio-md:checked.k-state-focus,
.k-radio-list.k-list-horizontal .k-radio-item .k-radio.k-radio-md:checked.k-state-focused,
.k-radio-list.k-list-horizontal .k-radio-item .k-radio.k-radio-md:checked:focus {
    outline-offset: 2px;
    outline-style: solid;
    outline-width: thin;
    outline-color: $tb-kendo-color-primary;
    border-bottom-color: $tb-kendo-color-primary;
    border-left-color: $tb-kendo-color-primary;
    border-right-color: $tb-kendo-color-primary;
    border-top-color: $tb-kendo-color-primary;
}
.k-radio-list.k-list-vertical .k-radio-item .k-radio.k-radio-md {
    border-bottom-color: $kendo-border-color;
    border-left-color: $kendo-border-color;
    border-right-color: $kendo-border-color;
    border-top-color: $kendo-border-color;
}
.k-radio-list.k-list-vertical .k-radio-item .k-radio.k-radio-md.k-checked.k-focus,
.k-radio-list.k-list-vertical .k-radio-item .k-radio.k-radio-md:checked.k-focus,
.k-radio-list.k-list-vertical .k-radio-item .k-radio.k-radio-md.k-checked.k-state-focus,
.k-radio-list.k-list-vertical .k-radio-item .k-radio.k-radio-md.k-checked.k-state-focused,
.k-radio-list.k-list-vertical .k-radio-item .k-radio.k-radio-md.k-checked:focus,
.k-radio-list.k-list-vertical .k-radio-item .k-radio.k-radio-md:checked.k-state-focus,
.k-radio-list.k-list-vertical .k-radio-item .k-radio.k-radio-md:checked.k-state-focused,
.k-radio-list.k-list-vertical .k-radio-item .k-radio.k-radio-md:checked:focus {
    border-bottom-width: 1px;
    border-left-width: 1px;
    border-right-width: 1px;
    border-top-width: 1px;
    outline-offset: 2px;
    outline-style: solid;
    outline-width: thin;
    outline-color: $tb-kendo-color-primary;
}
.k-radio-list.k-list-vertical .k-radio-item .k-radio.k-radio-md.k-checked,
.k-radio-list.k-list-vertical .k-radio-item .k-radio.k-radio-md:checked {
    border-bottom-width: 3px;
    border-left-width: 3px;
    border-right-width: 3px;
    border-top-width: 3px;
    border-bottom-color: $tb-kendo-color-primary;
    border-left-color: $tb-kendo-color-primary;
    border-right-color: $tb-kendo-color-primary;
    border-top-color: $tb-kendo-color-primary;
}
.k-radio-list.k-list-horizontal .k-radio-item {
    border-bottom-color: $kendo-border-color;
    border-bottom-style: solid;
    border-bottom-width: 0px;
    border-left-color: $kendo-border-color;
    border-left-style: solid;
    border-left-width: 0px;
    border-right-color: $kendo-border-color;
    border-right-style: solid;
    border-right-width: 0px;
    border-top-color: $kendo-border-color;
    border-top-style: solid;
    border-top-width: 0px;
}
.k-radio-list.k-list-horizontal .k-radio-item .k-radio.k-radio-md {
    border-bottom-color: $kendo-border-color;
    border-left-color: $kendo-border-color;
    border-right-color: $kendo-border-color;
    border-top-color: $kendo-border-color;
}
.k-radio-list.k-list-horizontal .k-radio-item .k-radio.k-radio-md.k-hover,
.k-radio-list.k-list-horizontal .k-radio-item .k-radio.k-radio-md.k-state-hover,
.k-radio-list.k-list-horizontal .k-radio-item .k-radio.k-radio-md.k-state-hovered,
.k-radio-list.k-list-horizontal .k-radio-item .k-radio.k-radio-md:hover {
    border-bottom-width: 1px;
    border-left-width: 1px;
    border-right-width: 1px;
    border-top-width: 1px;
    border-bottom-color: $kendo-border-color;
    border-left-color: $kendo-border-color;
    border-right-color: $kendo-border-color;
    border-top-color: $kendo-border-color;
}
.k-radio-list.k-list-horizontal .k-radio-item .k-radio.k-radio-md.k-checked.k-hover,
.k-radio-list.k-list-horizontal .k-radio-item .k-radio.k-radio-md:checked.k-hover,
.k-radio-list.k-list-horizontal .k-radio-item .k-radio.k-radio-md.k-checked.k-state-hover,
.k-radio-list.k-list-horizontal .k-radio-item .k-radio.k-radio-md.k-checked.k-state-hovered,
.k-radio-list.k-list-horizontal .k-radio-item .k-radio.k-radio-md.k-checked:hover,
.k-radio-list.k-list-horizontal .k-radio-item .k-radio.k-radio-md:checked.k-state-hover,
.k-radio-list.k-list-horizontal .k-radio-item .k-radio.k-radio-md:checked.k-state-hovered,
.k-radio-list.k-list-horizontal .k-radio-item .k-radio.k-radio-md:checked:hover {
    border-bottom-width: 3px;
    border-left-width: 3px;
    border-right-width: 3px;
    border-top-width: 3px;
    border-bottom-color: $tb-kendo-color-primary;
    border-left-color: $tb-kendo-color-primary;
    border-right-color: $tb-kendo-color-primary;
    border-top-color: $tb-kendo-color-primary;
}
.k-radio-list.k-list-horizontal .k-radio-item .k-radio.k-radio-md.k-checked,
.k-radio-list.k-list-horizontal .k-radio-item .k-radio.k-radio-md:checked {
    border-bottom-width: 3px;
    border-left-width: 3px;
    border-right-width: 3px;
    border-top-width: 3px;
    border-bottom-color: $tb-kendo-color-primary;
    border-left-color: $tb-kendo-color-primary;
    border-right-color: $tb-kendo-color-primary;
    border-top-color: $tb-kendo-color-primary;
}
.k-dropdownlist.k-picker {
    border-bottom-width: 1px;
    border-left-width: 1px;
    border-right-width: 1px;
    border-top-width: 1px;
    padding-bottom: 0.125rem;
    padding-top: 0.125rem;
}
.k-dropdownlist.k-picker-solid {
    border-bottom-color: $kendo-border-color;
    border-left-color: $kendo-border-color;
    border-right-color: $kendo-border-color;
    border-top-color: $kendo-border-color;
    background-color: $tb-kendo-button-bg;
    background-image: none;
}
.k-dropdownlist.k-rounded-md {
    border-bottom-left-radius: $tb-kendo-border-radius;
    border-bottom-right-radius: $tb-kendo-border-radius;
    border-top-left-radius: $tb-kendo-border-radius;
    border-top-right-radius: $tb-kendo-border-radius;
    // min-height: 2.25rem;
}
.k-dropdownlist.k-picker.k-picker-md.k-rounded-md.k-picker-solid .k-input-button {
    border-bottom-color: #ffffff;
    border-left-color: #ffffff;
    border-right-color: #ffffff;
    border-top-color: #ffffff;
}
.k-dropdownlist.k-hover.k-picker-solid,
.k-dropdownlist.k-state-hover.k-picker-solid,
.k-dropdownlist.k-state-hovered.k-picker-solid,
.k-dropdownlist:hover.k-picker-solid {
    background-color: $tb-kendo-body-bg;
    background-image: linear-gradient(rgba(0, 0, 0, 0), rgba(0, 0, 0, 0.02));
}
.k-textbox.k-input.k-input-solid .k-input-inner {
    border-bottom-color: $kendo-border-color;
    border-bottom-style: solid;
    border-bottom-width: 0px;
    border-left-color: $kendo-border-color;
    border-left-style: solid;
    border-left-width: 0px;
    border-right-color: $kendo-border-color;
    border-right-style: solid;
    border-right-width: 0px;
    border-top-color: $kendo-border-color;
    border-top-style: solid;
    border-top-width: 0px;
}
.k-textbox.k-input.k-input-solid {
    border-bottom-color: $kendo-border-color;
    border-left-color: $kendo-border-color;
    border-right-color: $kendo-border-color;
    border-top-color: $kendo-border-color;
    // padding-bottom: calc($kendo-padding-input / 2;
    // padding-left: $kendo-padding-input;
    // padding-right: $kendo-padding-input;
    // padding-top: calc($kendo-padding-input / 2;
}
.k-textbox.k-input.k-input-solid.k-rounded-md {
    border-bottom-left-radius: $tb-kendo-border-radius;
    border-bottom-right-radius: $tb-kendo-border-radius;
    border-top-left-radius: $tb-kendo-border-radius;
    border-top-right-radius: $tb-kendo-border-radius;
}
.k-tooltip {
    background-color: $tb-kendo-button-text;
    background-image: none;
}
.k-numerictextbox.k-input {
    padding-bottom: 2px;
    // padding-left: $kendo-padding-input;
    // padding-right: $kendo-padding-input;
    padding-top: 2px;
    outline-color: $tb-kendo-color-primary;
    outline-style: solid;
    outline-width: 0px;
}
.k-numerictextbox.k-rounded-md {
    border-bottom-left-radius: $tb-kendo-border-radius;
    border-bottom-right-radius: $tb-kendo-border-radius;
    border-top-left-radius: $tb-kendo-border-radius;
    border-top-right-radius: $tb-kendo-border-radius;
}
.k-numerictextbox.k-input-solid {
    border-bottom-color: $kendo-border-color;
    border-left-color: $kendo-border-color;
    border-right-color: $kendo-border-color;
    border-top-color: $kendo-border-color;
}
.k-maskedtextbox.k-input {
    padding-bottom: calc($kendo-padding-input / 2);
    padding-left: $kendo-padding-input;
    padding-right: $kendo-padding-input;
    padding-top: calc($kendo-padding-input / 2);
}
.k-maskedtextbox.k-input-solid {
    border-bottom-color: $kendo-border-color;
    border-left-color: $kendo-border-color;
    border-right-color: $kendo-border-color;
    border-top-color: $kendo-border-color;
}
.k-maskedtextbox.k-rounded-md {
    border-bottom-left-radius: $tb-kendo-border-radius;
    border-bottom-right-radius: $tb-kendo-border-radius;
    border-top-left-radius: $tb-kendo-border-radius;
    border-top-right-radius: $tb-kendo-border-radius;
}
.k-maskedtextbox.k-state-focus-within.k-input,
.k-maskedtextbox:focus-within.k-input {
    outline-color: $tb-kendo-color-primary;
    outline-style: solid;
    outline-width: 1px;
}
.k-maskedtextbox.k-state-focus-within.k-rounded-md,
.k-maskedtextbox:focus-within.k-rounded-md {
    border-bottom-left-radius: $tb-kendo-border-radius;
    border-bottom-right-radius: $tb-kendo-border-radius;
    border-top-left-radius: $tb-kendo-border-radius;
    border-top-right-radius: $tb-kendo-border-radius;
}
.k-numerictextbox.k-state-focus-within.k-input,
.k-numerictextbox:focus-within.k-input {
    outline-color: $tb-kendo-color-primary;
    outline-style: solid;
    outline-width: 1px;
}
.k-textbox.k-input.k-input-solid.k-invalid.k-focus,
.k-textbox.k-input.k-input-solid.ng-invalid.ng-touched.k-focus,
.k-textbox.k-input.k-input-solid.ng-invalid.ng-dirty.k-focus,
.k-textbox.k-input.k-input-solid.k-invalid.k-state-focus,
.k-textbox.k-input.k-input-solid.k-invalid.k-state-focused,
.k-textbox.k-input.k-input-solid.k-invalid:focus,
.k-textbox.k-input.k-input-solid.ng-invalid.ng-touched.k-state-focus,
.k-textbox.k-input.k-input-solid.ng-invalid.ng-touched.k-state-focused,
.k-textbox.k-input.k-input-solid.ng-invalid.ng-touched:focus,
.k-textbox.k-input.k-input-solid.ng-invalid.ng-dirty.k-state-focus,
.k-textbox.k-input.k-input-solid.ng-invalid.ng-dirty.k-state-focused,
.k-textbox.k-input.k-input-solid.ng-invalid.ng-dirty:focus {
    outline-color: $tb-kendo-color-primary;
    outline-style: solid;
    outline-width: 1px;
}
.k-textbox.k-input.k-input-outline {
    border-bottom-color: $kendo-border-color;
    border-left-color: $kendo-border-color;
    border-right-color: $kendo-border-color;
    border-top-color: $kendo-border-color;
    padding-bottom: calc($kendo-padding-input / 2);
    padding-left: $kendo-padding-input;
    padding-right: $kendo-padding-input;
    padding-top: calc($kendo-padding-input / 2);
}
.k-textbox.k-input.k-input-outline.k-rounded-md {
    border-bottom-left-radius: $tb-kendo-border-radius;
    border-bottom-right-radius: $tb-kendo-border-radius;
    border-top-left-radius: $tb-kendo-border-radius;
    border-top-right-radius: $tb-kendo-border-radius;
}
.k-textbox.k-input.k-input-solid.k-focus,
.k-textbox.k-input.k-input-solid.k-state-focus,
.k-textbox.k-input.k-input-solid.k-state-focused,
.k-textbox.k-input.k-input-solid:focus {
    outline-color: $tb-kendo-color-primary;
    outline-style: solid;
    outline-width: 1px;
}
input.k-input {
    padding-bottom: calc($kendo-padding-input / 2);
    padding-top: calc($kendo-padding-input / 2);
    padding-left: $kendo-padding-input;
    padding-right: $kendo-padding-input;
    border-radius: $tb-kendo-border-radius;
}
.k-input.k-input-solid {
    border-bottom-color: $kendo-border-color;
    border-left-color: $kendo-border-color;
    border-right-color: $kendo-border-color;
    border-top-color: $kendo-border-color;
}
.k-input.k-rounded-md {
    border-bottom-left-radius: $tb-kendo-border-radius;
    border-bottom-right-radius: $tb-kendo-border-radius;
    border-top-left-radius: $tb-kendo-border-radius;
    border-top-right-radius: $tb-kendo-border-radius;
}
// .k-input.k-focus,
// .k-input.k-state-focus,
// .k-input.k-state-focused,
// .k-input:focus {
//     outline-color: $tb-kendo-color-primary;
//     outline-style: solid;
//     outline-width: 1px;
//     border-radius: 6px;
// }
.k-input.k-disabled.k-input-solid,
.k-input.k-state-disabled.k-input-solid,
.k-input:disabled.k-input-solid {
    background-color: #f9fafb;
}
.k-input.k-disabled,
.k-input.k-state-disabled,
.k-input:disabled {
    background-image: none;
}
.k-input.k-invalid.k-focus,
.k-input.ng-invalid.ng-touched.k-focus,
.k-input.ng-invalid.ng-dirty.k-focus,
.k-input.k-invalid.k-state-focus,
.k-input.k-invalid.k-state-focused,
.k-input.k-invalid:focus,
.k-input.ng-invalid.ng-touched.k-state-focus,
.k-input.ng-invalid.ng-touched.k-state-focused,
.k-input.ng-invalid.ng-touched:focus,
.k-input.ng-invalid.ng-dirty.k-state-focus,
.k-input.ng-invalid.ng-dirty.k-state-focused,
.k-input.ng-invalid.ng-dirty:focus {
    outline-width: thin;
    outline-offset: unset;
}
// .k-textarea.k-state-focus-within .k-input-inner,
// .k-textarea:focus-within .k-input-inner {
//     outline-color: $tb-kendo-color-primary;
//     outline-style: solid;
//     outline-width: inherit;
// }
.k-textarea.k-state-focus-within.k-input,
.k-textarea:focus-within.k-input {
    outline-color: $tb-kendo-color-primary;
    outline-style: solid;
    outline-width: thin;
}
.k-radio.k-checked,
.k-radio:checked {
    border-bottom-width: 3px;
    border-left-width: 3px;
    border-right-width: 3px;
    border-top-width: 3px;
}
.k-radio.k-checked.k-focus,
.k-radio:checked.k-focus,
.k-radio.k-checked.k-state-focus,
.k-radio.k-checked.k-state-focused,
.k-radio.k-checked:focus,
.k-radio:checked.k-state-focus,
.k-radio:checked.k-state-focused,
.k-radio:checked:focus {
    outline-style: solid;
    outline-width: thin;
    outline-offset: 2px;
    outline-color: $tb-kendo-color-primary;
}
.k-radio.k-focus,
.k-radio.k-state-focus,
.k-radio.k-state-focused,
.k-radio:focus {
    outline-color: $tb-kendo-color-primary;
    outline-style: solid;
    outline-width: thin;
    outline-offset: 2px;
}
.k-radio {
    border-bottom-color: $kendo-border-color;
    border-left-color: $kendo-border-color;
    border-right-color: $kendo-border-color;
    border-top-color: $kendo-border-color;
}
.k-dateinput.k-input {
    padding-bottom: calc($kendo-padding-input / 2);
    padding-top: calc($kendo-padding-input / 2);
    input.k-input-inner:focus {
        box-shadow: none;
        border-color: none;
    }
}

.k-input-solid:focus,
.k-input-solid.k-focus {
    outline-color: none;
    outline-style: none;
    outline-width: none;
}
.k-dateinput.k-input.k-state-focus-within,
.k-dateinput.k-input:focus-within {
    border-color: #1890ff;
}
.k-calendar.k-calendar-infinite {
    @extend %tb-effects-kendo-shadow;
    border-bottom-left-radius: $tb-kendo-border-radius;
    border-bottom-right-radius: $tb-kendo-border-radius;
    border-top-left-radius: $tb-kendo-border-radius;
    border-top-right-radius: $tb-kendo-border-radius;
    border-bottom-width: 0px;
    border-left-width: 0px;
    border-right-width: 0px;
    border-top-width: 0px;
}
.k-calendar.k-calendar-range {
    @extend %tb-effects-kendo-shadow;
    border-bottom-width: 0px;
    border-left-width: 0px;
    border-right-width: 0px;
    border-top-width: 0px;
    border-bottom-left-radius: $tb-kendo-border-radius;
    border-bottom-right-radius: $tb-kendo-border-radius;
    border-top-left-radius: $tb-kendo-border-radius;
    border-top-right-radius: $tb-kendo-border-radius;
}
.k-autocomplete.k-input {
    // padding-bottom: $kendo-padding-input;
    // padding-left: $kendo-padding-input;
    // padding-right: $kendo-padding-input;
    // padding-top: $kendo-padding-input;
}
.k-autocomplete.k-input.k-rounded-md {
    border-bottom-left-radius: $tb-kendo-border-radius;
    border-bottom-right-radius: $tb-kendo-border-radius;
    border-top-left-radius: $tb-kendo-border-radius;
    border-top-right-radius: $tb-kendo-border-radius;
}
// .k-autocomplete.k-input.k-state-focus-within,
// .k-autocomplete.k-input:focus-within {
//     outline-color: $tb-kendo-color-primary;
//     outline-style: solid;
//     outline-width: thin;
// }
.k-autocomplete-popup .k-list.k-list-md .k-list-content .k-list-ul .k-list-item.k-selected {
    background-color: $tb-kendo-color-primary;
    background-image: none;
    color: $tb-kendo-component-bg;
}
.k-autocomplete-popup .k-list.k-list-md .k-list-content .k-list-ul .k-list-item.k-hover,
.k-autocomplete-popup .k-list.k-list-md .k-list-content .k-list-ul .k-list-item.k-state-hover,
.k-autocomplete-popup .k-list.k-list-md .k-list-content .k-list-ul .k-list-item.k-state-hovered,
.k-autocomplete-popup .k-list.k-list-md .k-list-content .k-list-ul .k-list-item:hover {
    border-bottom-left-radius: $tb-kendo-border-radius;
    border-bottom-right-radius: $tb-kendo-border-radius;
    border-top-left-radius: $tb-kendo-border-radius;
    border-top-right-radius: $tb-kendo-border-radius;
    color: $tb-kendo-link-text;
}
.k-autocomplete-popup .k-list.k-list-md .k-list-content .k-list-ul .k-list-item.k-active,
.k-autocomplete-popup .k-list.k-list-md .k-list-content .k-list-ul .k-list-item.k-state-active,
.k-autocomplete-popup .k-list.k-list-md .k-list-content .k-list-ul .k-list-item:active {
    color: $tb-kendo-color-primary;
}
.k-dropdowntree.k-picker-solid {
    border-bottom-color: $kendo-border-color;
    border-left-color: $kendo-border-color;
    border-right-color: $kendo-border-color;
    border-top-color: $kendo-border-color;
    background-color: $tb-kendo-button-bg;
    background-image: none;
}
.k-dropdowntree.k-picker {
    padding-bottom: calc($kendo-padding-input / 2);
    padding-left: $kendo-padding-input;
    padding-right: $kendo-padding-input;
    padding-top: calc($kendo-padding-input / 2);
}
.k-dropdowntree.k-hover.k-picker-solid,
.k-dropdowntree.k-state-hover.k-picker-solid,
.k-dropdowntree.k-state-hovered.k-picker-solid,
.k-dropdowntree:hover.k-picker-solid {
    background-color: $tb-kendo-body-bg;
    background-image: none;
}
.k-dropdowntree.k-focus.k-picker,
.k-dropdowntree.k-state-focus.k-picker,
.k-dropdowntree.k-state-focused.k-picker,
.k-dropdowntree:focus.k-picker {
    outline-color: $tb-kendo-color-primary;
    outline-style: solid;
    outline-width: thin;
}
.k-dropdowntree.k-focus .k-input-button.k-button.k-icon-button,
.k-dropdowntree.k-state-focus .k-input-button.k-button.k-icon-button,
.k-dropdowntree.k-state-focused .k-input-button.k-button.k-icon-button,
.k-dropdowntree:focus .k-input-button.k-button.k-icon-button {
    border-bottom-width: 0px;
    border-left-width: 0px;
    border-right-width: 0px;
    border-top-width: 0px;
}
.k-multiselect.k-input {
    padding-bottom: calc($kendo-padding-input / 2);
    padding-left: $kendo-padding-input;
    padding-right: $kendo-padding-input;
    padding-top: calc($kendo-padding-input / 2);
}
.k-multiselect.k-input .k-input-values .k-chip-list.k-chip-list-md .k-chip.k-rounded-md {
    border-bottom-left-radius: 0.75rem;
    border-bottom-right-radius: 0.75rem;
    border-top-left-radius: 0.75rem;
    border-top-right-radius: 0.75rem;
}
.k-multiselect.k-input .k-input-values .k-chip-list.k-chip-list-md .k-chip.k-chip-solid-base {
    background-color: #e5e7eb;
    background-image: none;
}
.k-multiselect.k-input .k-input-values .k-chip-list.k-chip-list-md .k-chip.k-chip-md {
    padding-bottom: $kendo-padding-input;
    padding-left: 4px;
    padding-right: 4px;
    padding-top: $kendo-padding-input;
}
.k-multiselect.k-input .k-input-values .k-chip-list.k-chip-list-md .k-chip {
    border-bottom-width: 0px;
    border-left-width: 0px;
    border-right-width: 0px;
    border-top-width: 0px;
}
.k-multiselect.k-input
    .k-input-values
    .k-chip-list.k-chip-list-md
    .k-chip
    .k-chip-actions
    .k-chip-action.k-chip-remove-action
    .k-icon.k-svg-icon
    > svg {
    background-color: initial;
    background-image: none;
}
.k-multiselecttree.k-input {
    padding-bottom: $kendo-padding-input;
    padding-left: $kendo-padding-input;
    padding-right: $kendo-padding-input;
    padding-top: $kendo-padding-input;
}
.k-multiselecttree.k-input .k-input-values.k-chip-list .k-chip.k-chip-solid-base {
    background-color: $tb-kendo-button-bg;
    background-image: linear-gradient(#e5e7eb, #e5e7eb);
}
.k-multiselecttree.k-input .k-input-values.k-chip-list .k-chip {
    border-bottom-width: 0px;
    border-left-width: 0px;
    border-right-width: 0px;
    border-top-width: 0px;
}
.k-multiselecttree.k-input .k-input-values.k-chip-list .k-chip.k-rounded-md {
    border-bottom-left-radius: $tb-kendo-border-radius;
    border-bottom-right-radius: $tb-kendo-border-radius;
    border-top-left-radius: $tb-kendo-border-radius;
    border-top-right-radius: $tb-kendo-border-radius;
}
.k-form .k-label {
    @extend %tb-typography-kendo-default-typography;
    color: $tb-kendo-heading-text;
}
.k-scheduler .k-event {
    color: $tb-kendo-heading-text;
    background-color: $tb-kendo-body-bg;
    background-image: none;
    border-left-color: inherit;
    border-left-style: solid;
    border-left-width: 2px;
    border-bottom-left-radius: 0px;
    border-bottom-right-radius: 0px;
    border-top-left-radius: 0px;
    border-top-right-radius: 0px;
}
.k-scheduler .k-event.k-selected {
    margin-bottom: 4px;
    box-shadow: none;
}
.k-scheduler .k-event .k-event-template {
    border-left-color: $tb-kendo-color-primary;
}
.k-scheduler {
    border-left-color: #e0e0e0;
    border-left-style: solid;
    border-left-width: 1px;
}
.k-scheduler .k-scheduler-layout-flex.k-scheduler-layout .k-scheduler-cell.k-selected {
    color: $tb-kendo-base-text;
    background-color: #e6f7ff;
    background-image: none;
}
.k-scheduler
    .k-toolbar.k-scheduler-toolbar
    .k-button-group.k-scheduler-views
    .k-button.k-button-md.k-button-solid.k-button-solid-base.k-rounded-md {
    outline-color: $tb-kendo-color-primary;
    outline-width: 0px;
}
.k-scheduler
    .k-toolbar.k-scheduler-toolbar
    .k-button-group.k-scheduler-views
    .k-button.k-button-md.k-button-solid.k-button-solid-base.k-rounded-md.k-selected {
    outline-width: thin;
    outline-offset: 1px;
}
.k-grid.k-grid-md .k-table .k-table-tbody .k-master-row.k-table-row.k-alt.k-table-alt-row {
    background-color: $tb-kendo-body-bg;
    background-image: none;
    border-top-color: $tb-kendo-base-text;
    border-top-style: solid;
    border-top-width: 1px;
    border-bottom-color: $tb-kendo-base-text;
    border-bottom-style: solid;
    border-bottom-width: 1px;
    border-left-width: 1px;
    border-right-width: 1px;
    border-left-color: $tb-kendo-base-text;
    border-right-color: $tb-kendo-base-text;
}
.k-grid.k-grid-md .k-table .k-sorted {
    background-color: $tb-kendo-body-bg;
    background-image: none;
}
.k-grid.k-grid-md .k-table-thead .k-table-row,
.k-grid.k-grid-md .k-grid-header .k-table-row {
    background-color: $tb-kendo-body-bg;
    background-image: none;
}
.k-grid.k-grid-md .k-table .k-table-tbody .k-master-row.k-table-row.k-alt.k-table-alt-row > td {
    border-bottom-width: 1px;
    border-left-width: 0px;
    border-right-width: 0px;
    border-top-width: 1px;
    border-top-color: rgba(0, 0, 0, 0.08);
    border-top-style: solid;
    border-bottom-color: rgba(0, 0, 0, 0.08);
    border-bottom-style: solid;
}
.k-grid.k-grid-md .k-table .k-table-tbody .k-table-row.k-master-row {
    border-bottom-color: inherit;
    border-bottom-style: solid;
    border-bottom-width: 0px;
    border-left-color: inherit;
    border-left-style: solid;
    border-left-width: 0px;
    border-right-color: inherit;
    border-right-style: solid;
    border-right-width: 0px;
    border-top-color: inherit;
    border-top-style: solid;
    border-top-width: 0px;
}
.k-grid.k-grid-md .k-table .k-table-tbody .k-master-row.k-table-row.k-alt.k-table-alt-row.k-hover,
.k-grid.k-grid-md
    .k-table
    .k-table-tbody
    .k-master-row.k-table-row.k-alt.k-table-alt-row.k-state-hover,
.k-grid.k-grid-md
    .k-table
    .k-table-tbody
    .k-master-row.k-table-row.k-alt.k-table-alt-row.k-state-hovered,
.k-grid.k-grid-md .k-table .k-table-tbody .k-master-row.k-table-row.k-alt.k-table-alt-row:hover {
    background-color: #f3f4f6;
    background-image: none;
}
.k-grid.k-grid-md .k-table .k-table-tbody .k-table-row.k-master-row.k-hover,
.k-grid.k-grid-md .k-table .k-table-tbody .k-table-row.k-master-row.k-state-hover,
.k-grid.k-grid-md .k-table .k-table-tbody .k-table-row.k-master-row.k-state-hovered,
.k-grid.k-grid-md .k-table .k-table-tbody .k-table-row.k-master-row:hover {
    background-color: #f3f4f6;
    background-image: none;
}
.k-grid.k-grid-md .k-table-tbody .k-master-row.k-table-row td.k-focus,
.k-grid.k-grid-md .k-table-tbody .k-master-row.k-table-row td.k-state-focus,
.k-grid.k-grid-md .k-table-tbody .k-master-row.k-table-row td.k-state-focused,
.k-grid.k-grid-md .k-table-tbody .k-master-row.k-table-row td:focus {
    box-shadow: none;
}
.k-grid.k-grid-md .k-table-tbody .k-master-row.k-table-row > td {
    border-bottom-width: 0px;
    border-left-width: 0px;
    border-right-width: 0px;
    border-top-width: 0px;
}
.k-grid.k-grid-md .k-table-tbody .k-master-row.k-table-row.k-selected {
    &,
    &.k-table-alt-row {
        & > td {
            background-color: #e6f7ff;
            background-image: none;
        }
    }
}
.k-pager {
    color: $tb-kendo-body-text;
    background-color: $tb-kendo-body-bg;
    background-image: none;
}
.k-pager .k-pager-numbers-wrap .k-pager-numbers .k-button.k-button-flat.k-button-flat-primary {
    border-bottom-color: $kendo-border-color !important;
    border-left-color: $kendo-border-color !important;
    border-right-color: $kendo-border-color !important;
    border-top-color: $kendo-border-color !important;
}
.k-pager
    .k-pager-numbers-wrap
    .k-pager-numbers
    .k-button.k-button-flat.k-button-flat-primary.k-hover,
.k-pager
    .k-pager-numbers-wrap
    .k-pager-numbers
    .k-button.k-button-flat.k-button-flat-primary.k-state-hover,
.k-pager
    .k-pager-numbers-wrap
    .k-pager-numbers
    .k-button.k-button-flat.k-button-flat-primary.k-state-hovered,
.k-pager
    .k-pager-numbers-wrap
    .k-pager-numbers
    .k-button.k-button-flat.k-button-flat-primary:hover {
    background-color: #e6f7ff !important;
    background-image: none !important;
}
.k-pager
    .k-pager-numbers-wrap
    .k-pager-numbers
    .k-button.k-button-flat.k-button-flat-primary.k-selected {
    background-color: #e6f7ff !important;
    background-image: none !important;
    border-bottom-color: $tb-kendo-color-primary !important;
    border-left-color: $tb-kendo-color-primary !important;
    border-right-color: $tb-kendo-color-primary !important;
    border-top-color: $tb-kendo-color-primary !important;
    color: $tb-kendo-color-primary;
}
.k-pager .k-pager-numbers-wrap .k-button.k-pager-nav.k-pager-first.k-button-flat,
.k-pager .k-button-flat {
    border-bottom-color: $kendo-border-color !important;
    border-left-color: $kendo-border-color !important;
    border-right-color: $kendo-border-color !important;
    border-top-color: $kendo-border-color !important;
}
.k-pager .k-pager-numbers-wrap .k-button.k-pager-nav.k-pager-first {
    border-top-left-radius: $tb-kendo-border-radius;
    border-bottom-right-radius: 0px;
    border-bottom-left-radius: $tb-kendo-border-radius;
}
.k-pager .k-pager-numbers-wrap .k-button.k-pager-nav.k-button-flat {
    border-bottom-color: $kendo-border-color !important;
    border-left-color: $kendo-border-color !important;
    border-right-color: $kendo-border-color !important;
    border-top-color: $kendo-border-color !important;
}
.k-pager .k-pager-numbers-wrap .k-button.k-pager-nav.k-pager-last {
    border-top-right-radius: $tb-kendo-border-radius;
    border-bottom-right-radius: $tb-kendo-border-radius;
}
.k-splitter.k-splitter-flex.k-splitter-horizontal .k-splitbar.k-splitbar-horizontal {
    width: 4px;
    background-color: #ffffff;
    background-image: none;
    border-left-style: solid;
    border-left-width: 0px;
    border-bottom-width: 0px;
    border-right-width: 0px;
    border-top-width: 0px;
}
.k-splitter.k-splitter-flex.k-splitter-horizontal .k-splitbar.k-splitbar-horizontal.k-focus,
.k-splitter.k-splitter-flex.k-splitter-horizontal .k-splitbar.k-splitbar-horizontal.k-state-focus,
.k-splitter.k-splitter-flex.k-splitter-horizontal .k-splitbar.k-splitbar-horizontal.k-state-focused,
.k-splitter.k-splitter-flex.k-splitter-horizontal .k-splitbar.k-splitbar-horizontal:focus {
    background-color: #ffffff;
    background-image: none;
}
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal.k-hover
    .k-collapse-prev
    .k-icon.k-svg-icon
    > svg,
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal.k-state-hover
    .k-collapse-prev
    .k-icon.k-svg-icon
    > svg,
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal.k-state-hovered
    .k-collapse-prev
    .k-icon.k-svg-icon
    > svg,
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal:hover
    .k-collapse-prev
    .k-icon.k-svg-icon
    > svg {
    color: $tb-kendo-color-primary;
}
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal.k-hover
    .k-collapse-next
    .k-icon.k-svg-icon
    > svg,
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal.k-state-hover
    .k-collapse-next
    .k-icon.k-svg-icon
    > svg,
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal.k-state-hovered
    .k-collapse-next
    .k-icon.k-svg-icon
    > svg,
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal:hover
    .k-collapse-next
    .k-icon.k-svg-icon
    > svg {
    color: $tb-kendo-color-primary;
}
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal.k-hover
    .k-collapse-prev::before,
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal.k-state-hover
    .k-collapse-prev::before,
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal.k-state-hovered
    .k-collapse-prev::before,
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal:hover
    .k-collapse-prev::before {
    width: 4px;
    height: 4px;
}
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal.k-hover
    .k-collapse-prev::before,
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal.k-state-hover
    .k-collapse-prev::before,
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal.k-state-hovered
    .k-collapse-prev::before,
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal:hover
    .k-collapse-prev::before {
    @extend %tb-effects-kendo-shadow;
    border-bottom-width: 0px;
    border-left-width: 0px;
    border-right-width: 0px;
    border-top-width: 0px;
    border-bottom-color: $tb-kendo-color-primary;
    border-bottom-style: solid;
    border-left-color: $tb-kendo-color-primary;
    border-left-style: solid;
    border-right-color: $tb-kendo-color-primary;
    border-right-style: solid;
    border-top-color: $tb-kendo-color-primary;
    border-top-style: solid;
    border-bottom-left-radius: 50%;
    border-bottom-right-radius: 50%;
    border-top-left-radius: 50%;
    border-top-right-radius: 50%;
}
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal.k-hover
    .k-collapse-next
    .k-icon.k-svg-icon.k-icon-xs,
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal.k-state-hover
    .k-collapse-next
    .k-icon.k-svg-icon.k-icon-xs,
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal.k-state-hovered
    .k-collapse-next
    .k-icon.k-svg-icon.k-icon-xs,
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal:hover
    .k-collapse-next
    .k-icon.k-svg-icon.k-icon-xs {
    width: 4px;
    height: 4px;
}
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal.k-hover
    .k-collapse-next
    .k-icon.k-svg-icon,
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal.k-state-hover
    .k-collapse-next
    .k-icon.k-svg-icon,
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal.k-state-hovered
    .k-collapse-next
    .k-icon.k-svg-icon,
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal:hover
    .k-collapse-next
    .k-icon.k-svg-icon {
    @extend %tb-effects-kendo-shadow;
    border-bottom-width: 0px;
    border-left-width: 0px;
    border-right-width: 0px;
    border-top-width: 0px;
    border-bottom-color: $tb-kendo-color-primary;
    border-bottom-style: solid;
    border-left-color: $tb-kendo-color-primary;
    border-left-style: solid;
    border-right-color: $tb-kendo-color-primary;
    border-right-style: solid;
    border-top-color: $tb-kendo-color-primary;
    border-top-style: solid;
    border-bottom-left-radius: 50%;
    border-bottom-right-radius: 50%;
    border-top-left-radius: 50%;
    border-top-right-radius: 50%;
}
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal
    .k-collapse-prev
    .k-icon.k-svg-icon
    > svg {
    color: $tb-kendo-body-bg;
}
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal
    .k-collapse-next
    .k-icon.k-svg-icon
    > svg {
    color: $tb-kendo-body-bg;
}
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal.k-splitbar-draggable-horizontal
    .k-resize-handle {
    // color: $tb-kendo-body-bg;
    height: 100%;
    background-color: #e5e7eb;
    background-image: none;
    width: 2px;
}
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal.k-focus
    .k-collapse-prev
    .k-icon.k-svg-icon.k-icon-xs,
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal.k-state-focus
    .k-collapse-prev
    .k-icon.k-svg-icon.k-icon-xs,
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal.k-state-focused
    .k-collapse-prev
    .k-icon.k-svg-icon.k-icon-xs,
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal:focus
    .k-collapse-prev
    .k-icon.k-svg-icon.k-icon-xs {
    width: 4px;
    height: 4px;
}
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal.k-focus
    .k-collapse-prev
    .k-icon.k-svg-icon,
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal.k-state-focus
    .k-collapse-prev
    .k-icon.k-svg-icon,
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal.k-state-focused
    .k-collapse-prev
    .k-icon.k-svg-icon,
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal:focus
    .k-collapse-prev
    .k-icon.k-svg-icon {
    border-bottom-width: 0px;
    border-left-width: 0px;
    border-right-width: 0px;
    border-top-width: 0px;
    border-bottom-color: $tb-kendo-color-primary;
    border-bottom-style: solid;
    border-left-color: $tb-kendo-color-primary;
    border-left-style: solid;
    border-right-color: $tb-kendo-color-primary;
    border-right-style: solid;
    border-top-color: $tb-kendo-color-primary;
    border-top-style: solid;
    background-color: initial;
    background-image: none;
    color: $tb-kendo-color-primary;
    border-bottom-left-radius: 50%;
    border-bottom-right-radius: 50%;
    border-top-left-radius: 50%;
    border-top-right-radius: 50%;
}
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal.k-focus
    .k-collapse-prev
    .k-icon.k-svg-icon
    > svg,
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal.k-state-focus
    .k-collapse-prev
    .k-icon.k-svg-icon
    > svg,
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal.k-state-focused
    .k-collapse-prev
    .k-icon.k-svg-icon
    > svg,
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal:focus
    .k-collapse-prev
    .k-icon.k-svg-icon
    > svg {
    color: $tb-kendo-body-bg;
    border-bottom-left-radius: 50%;
    border-bottom-right-radius: 50%;
    border-top-left-radius: 50%;
    border-top-right-radius: 50%;
}
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal.k-splitbar-draggable-horizontal.k-focus
    .k-collapse-prev
    .k-icon.k-svg-icon,
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal.k-splitbar-draggable-horizontal.k-state-focus
    .k-collapse-prev
    .k-icon.k-svg-icon,
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal.k-splitbar-draggable-horizontal.k-state-focused
    .k-collapse-prev
    .k-icon.k-svg-icon,
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal.k-splitbar-draggable-horizontal:focus
    .k-collapse-prev
    .k-icon.k-svg-icon {
    border-bottom-left-radius: 50%;
    border-bottom-right-radius: 50%;
    border-top-left-radius: 50%;
    border-top-right-radius: 50%;
}
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal.k-splitbar-draggable-horizontal.k-hover
    .k-resize-handle,
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal.k-splitbar-draggable-horizontal.k-state-hover
    .k-resize-handle,
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal.k-splitbar-draggable-horizontal.k-state-hovered
    .k-resize-handle,
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal.k-splitbar-draggable-horizontal:hover
    .k-resize-handle {
    color: $tb-kendo-color-primary;
    width: 2px;
    background-color: $tb-kendo-color-primary;
    background-image: none;
}
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal.k-focus
    .k-collapse-next
    .k-icon.k-svg-icon.k-icon-xs,
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal.k-state-focus
    .k-collapse-next
    .k-icon.k-svg-icon.k-icon-xs,
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal.k-state-focused
    .k-collapse-next
    .k-icon.k-svg-icon.k-icon-xs,
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal:focus
    .k-collapse-next
    .k-icon.k-svg-icon.k-icon-xs {
    width: 4px;
    height: 4px;
}
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal.k-focus
    .k-collapse-next
    .k-icon.k-svg-icon
    > svg,
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal.k-state-focus
    .k-collapse-next
    .k-icon.k-svg-icon
    > svg,
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal.k-state-focused
    .k-collapse-next
    .k-icon.k-svg-icon
    > svg,
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal:focus
    .k-collapse-next
    .k-icon.k-svg-icon
    > svg {
    color: $tb-kendo-button-bg;
}
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal.k-splitbar-draggable-horizontal {
    width: 12px;
}
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal.k-splitbar-draggable-horizontal.k-hover
    .k-collapse-prev
    .k-icon.k-svg-icon,
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal.k-splitbar-draggable-horizontal.k-state-hover
    .k-collapse-prev
    .k-icon.k-svg-icon,
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal.k-splitbar-draggable-horizontal.k-state-hovered
    .k-collapse-prev
    .k-icon.k-svg-icon,
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal.k-splitbar-draggable-horizontal:hover
    .k-collapse-prev
    .k-icon.k-svg-icon {
    @extend %tb-effects-kendo-shadow;
    border-bottom-width: 0px;
    border-left-width: 0px;
    border-right-width: 0px;
    border-top-width: 0px;
}
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal.k-splitbar-draggable-horizontal.k-hover
    .k-collapse-next
    .k-icon.k-svg-icon,
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal.k-splitbar-draggable-horizontal.k-state-hover
    .k-collapse-next
    .k-icon.k-svg-icon,
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal.k-splitbar-draggable-horizontal.k-state-hovered
    .k-collapse-next
    .k-icon.k-svg-icon,
.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal.k-splitbar-draggable-horizontal:hover
    .k-collapse-next
    .k-icon.k-svg-icon {
    @extend %tb-effects-kendo-shadow;
    border-bottom-width: 0px;
    border-left-width: 0px;
    border-right-width: 0px;
    border-top-width: 0px;
}
.k-splitter.k-splitter-flex.k-splitter-vertical .k-splitbar.k-splitbar-vertical {
    background-color: transparent;
    background-image: none;
}
.k-splitter.k-splitter-flex.k-splitter-vertical
    .k-splitbar.k-splitbar-vertical
    .k-collapse-prev
    .k-icon.k-svg-icon
    > svg {
    color: transparent;
}
.k-splitter.k-splitter-flex.k-splitter-vertical
    .k-splitbar.k-splitbar-vertical
    .k-collapse-next
    .k-icon.k-svg-icon
    > svg {
    color: transparent;
}
.k-splitter.k-splitter-flex.k-splitter-vertical
    .k-splitbar.k-splitbar-vertical.k-hover
    .k-collapse-prev
    .k-icon.k-svg-icon
    > svg,
.k-splitter.k-splitter-flex.k-splitter-vertical
    .k-splitbar.k-splitbar-vertical.k-state-hover
    .k-collapse-prev
    .k-icon.k-svg-icon
    > svg,
.k-splitter.k-splitter-flex.k-splitter-vertical
    .k-splitbar.k-splitbar-vertical.k-state-hovered
    .k-collapse-prev
    .k-icon.k-svg-icon
    > svg,
.k-splitter.k-splitter-flex.k-splitter-vertical
    .k-splitbar.k-splitbar-vertical:hover
    .k-collapse-prev
    .k-icon.k-svg-icon
    > svg {
    color: $tb-kendo-color-primary;
}
.k-splitter.k-splitter-flex.k-splitter-vertical
    .k-splitbar.k-splitbar-vertical.k-hover
    .k-collapse-prev
    .k-icon.k-svg-icon.k-icon-xs,
.k-splitter.k-splitter-flex.k-splitter-vertical
    .k-splitbar.k-splitbar-vertical.k-state-hover
    .k-collapse-prev
    .k-icon.k-svg-icon.k-icon-xs,
.k-splitter.k-splitter-flex.k-splitter-vertical
    .k-splitbar.k-splitbar-vertical.k-state-hovered
    .k-collapse-prev
    .k-icon.k-svg-icon.k-icon-xs,
.k-splitter.k-splitter-flex.k-splitter-vertical
    .k-splitbar.k-splitbar-vertical:hover
    .k-collapse-prev
    .k-icon.k-svg-icon.k-icon-xs {
    width: 16px;
    height: 16px;
}
.k-splitter.k-splitter-flex.k-splitter-vertical
    .k-splitbar.k-splitbar-vertical.k-hover
    .k-collapse-prev
    .k-icon.k-svg-icon,
.k-splitter.k-splitter-flex.k-splitter-vertical
    .k-splitbar.k-splitbar-vertical.k-state-hover
    .k-collapse-prev
    .k-icon.k-svg-icon,
.k-splitter.k-splitter-flex.k-splitter-vertical
    .k-splitbar.k-splitbar-vertical.k-state-hovered
    .k-collapse-prev
    .k-icon.k-svg-icon,
.k-splitter.k-splitter-flex.k-splitter-vertical
    .k-splitbar.k-splitbar-vertical:hover
    .k-collapse-prev
    .k-icon.k-svg-icon {
    @extend %tb-effects-kendo-shadow;
    border-bottom-left-radius: 50%;
    border-bottom-right-radius: 50%;
    border-top-left-radius: 50%;
    border-top-right-radius: 50%;
}
.k-splitter.k-splitter-flex.k-splitter-vertical
    .k-splitbar.k-splitbar-vertical.k-hover
    .k-collapse-next
    .k-icon.k-svg-icon
    > svg,
.k-splitter.k-splitter-flex.k-splitter-vertical
    .k-splitbar.k-splitbar-vertical.k-state-hover
    .k-collapse-next
    .k-icon.k-svg-icon
    > svg,
.k-splitter.k-splitter-flex.k-splitter-vertical
    .k-splitbar.k-splitbar-vertical.k-state-hovered
    .k-collapse-next
    .k-icon.k-svg-icon
    > svg,
.k-splitter.k-splitter-flex.k-splitter-vertical
    .k-splitbar.k-splitbar-vertical:hover
    .k-collapse-next
    .k-icon.k-svg-icon
    > svg {
    color: $tb-kendo-color-primary;
}
.k-splitter.k-splitter-flex.k-splitter-vertical
    .k-splitbar.k-splitbar-vertical.k-hover
    .k-collapse-next
    .k-icon.k-svg-icon.k-icon-xs,
.k-splitter.k-splitter-flex.k-splitter-vertical
    .k-splitbar.k-splitbar-vertical.k-state-hover
    .k-collapse-next
    .k-icon.k-svg-icon.k-icon-xs,
.k-splitter.k-splitter-flex.k-splitter-vertical
    .k-splitbar.k-splitbar-vertical.k-state-hovered
    .k-collapse-next
    .k-icon.k-svg-icon.k-icon-xs,
.k-splitter.k-splitter-flex.k-splitter-vertical
    .k-splitbar.k-splitbar-vertical:hover
    .k-collapse-next
    .k-icon.k-svg-icon.k-icon-xs {
    width: 16px;
    height: 16px;
}
.k-splitter.k-splitter-flex.k-splitter-vertical
    .k-splitbar.k-splitbar-vertical.k-hover
    .k-collapse-next
    .k-icon.k-svg-icon,
.k-splitter.k-splitter-flex.k-splitter-vertical
    .k-splitbar.k-splitbar-vertical.k-state-hover
    .k-collapse-next
    .k-icon.k-svg-icon,
.k-splitter.k-splitter-flex.k-splitter-vertical
    .k-splitbar.k-splitbar-vertical.k-state-hovered
    .k-collapse-next
    .k-icon.k-svg-icon,
.k-splitter.k-splitter-flex.k-splitter-vertical
    .k-splitbar.k-splitbar-vertical:hover
    .k-collapse-next
    .k-icon.k-svg-icon {
    @extend %tb-effects-kendo-shadow;
    border-bottom-left-radius: 50%;
    border-bottom-right-radius: 50%;
    border-top-left-radius: 50%;
    border-top-right-radius: 50%;
}
.k-splitter.k-splitter-flex.k-splitter-vertical
    .k-splitbar.k-splitbar-vertical.k-splitbar-draggable-vertical
    .k-resize-handle {
    width: 100%;
    background-color: #e5e7eb;
    background-image: none;
}

.k-ghost-splitbar-horizontal,
.k-splitbar-horizontal {
    width: 0px;
}

.k-icon.k-collapse-prev,
.k-icon.k-expand-prev {
    box-shadow: rgba(0, 0, 0, 0.24) 0px 3px 8px;
    border-radius: 50%;
    border: 1px solid #ffffff;
    background-color: #ffffff;
    padding: 10px;
    left: 0;
    opacity: 0;
    visibility: hidden;
    display: none;
    /* border: 1px solid rgb(209 213 219) !important; */
    border-radius: 50% !important;
    /* padding: 6px !important; */
    z-index: 1000 !important;
    position: absolute !important;
    width: 1.25rem;
    height: 1.25rem;
    top: 10%;
    &:is(:hover) {
        background-color: $tb-kendo-color-primary;
        border-color: $tb-kendo-color-primary;
    }
}

.k-splitbar-horizontal.k-splitbar-draggable-horizontal .k-i-arrow-60-left {
    margin-top: 0;
}

.k-splitbar:hover,
.k-splitbar.k-hover,
.k-splitbar-horizontal-hover,
.k-splitbar-vertical-hover {
    .k-icon.k-collapse-prev,
    .k-icon.k-expand-prev {
        // opacity: 1;
        // visibility: visible;
    }

    .k-resize-handle {
        color: $tb-kendo-color-primary;
    }
}

.k-state-collapsed + .k-splitbar-horizontal {
    width: 10px;
    .k-icon.k-collapse-prev,
    .k-icon.k-expand-prev {
        // opacity: 1;
        // visibility: visible;
        // left: -7px;
    }
}

.k-splitbar:focus,
.k-splitbar.k-focus {
    background: transparent;
    color: transparent;
}

.k-dialog-wrapper
    .k-window.k-dialog
    .k-window-titlebar.k-dialog-titlebar
    .k-window-title.k-dialog-title {
    background-color: initial;
    background-image: none;
}
.k-dialog-wrapper .k-window.k-dialog .k-window-titlebar.k-dialog-titlebar {
    background-color: initial;
    background-image: linear-gradient(to left, #4ade80, #3b82f6);
    color: $tb-kendo-body-bg;
    padding-right: 12px;
    //   border-top-left-radius: 8px;
    //   border-top-right-radius: 8px;
}
.k-window .k-window-titlebar {
    color: #ffffff;
    background-color: initial;
    background-image: linear-gradient(to left, #4ade80, #3b82f6);
    //   border-top-left-radius: 8px;
    //   border-top-right-radius: 8px;
}

.k-window-title {
    font-size: 1.125rem;
    padding-block: 1rem;
    font-weight: 500;
}
.k-switch.k-switch-md {
    width: 44px;
    height: 24px;
}
.k-switch.k-switch-md .k-switch-track {
    width: 44px;
    height: 24px;
}
.k-switch .k-switch-thumb-wrap .k-switch-thumb,
.k-switch-md .k-switch-thumb {
    width: 20px;
    height: 20px;
}

.k-switch-off .k-switch-track {
    background-color: var(--color-gray-200);
}

.k-switch-off .k-switch-thumb {
    filter: drop-shadow(0px 1px 2px rgba(0, 0, 0, 0.06)) drop-shadow(0px 1px 3px rgba(0, 0, 0, 0.1));
}
.k-dialog-wrapper
    .k-window.k-dialog
    .k-window-actions.k-dialog-actions.k-actions.k-actions-stretched {
    border-bottom-right-radius: $tb-kendo-border-radius;
    border-bottom-left-radius: $tb-kendo-border-radius;
    border-top: 1px solid var(--color-gray-200);
}
.k-dialog-wrapper .k-window.k-dialog {
    // border-bottom-right-radius: 8px;
    // border-bottom-left-radius: 8px;
}
.k-combobox.k-input {
    padding-bottom: $kendo-padding-input;
    padding-left: $kendo-padding-input;
    padding-right: $kendo-padding-input;
    padding-top: $kendo-padding-input;
}
.k-window {
    //   border-bottom-right-radius: 8px;
    //   border-bottom-left-radius: 8px;
    line-height: 1.5;
}

// .k-tabstrip .k-tabstrip-items-wrapper .k-item {
//     border-bottom: 0.125rem solid white;
//     margin-bottom: -2px;
// }

.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset .k-item.k-active,
.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset .k-item.k-state-active,
.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset .k-item:active {
    //   border-bottom-width: 2px;
    border-left-width: 0px;
    border-right-width: 0px;
    border-top-width: 0px;
    //   border-bottom-color: $tb-kendo-color-primary;
    // border-bottom-style: solid;
    color: $tb-kendo-color-primary;
    margin-bottom: 0px;
    position: relative;
    &::after {
        content: '';
        position: absolute;
        bottom: 0;
        width: 100%;
        height: 3px;
        background-color: var(--color-primary-blue-500);
    }
}

.k-tabstrip-items-wrapper .k-item:active,
.k-tabstrip-items-wrapper .k-item.k-active,
.k-tabstrip-items-wrapper .k-item.k-selected {
    background-color: transparent;
}

#activityTabStripActivityLogTab,
#activityTabStrip {
    &.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset .k-item.k-active,
    &.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset .k-item.k-state-active,
    &.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset .k-item:active {
        &::after {
            display: none;
        }
    }
}

.k-tabstrip .k-tabstrip-items-wrapper .k-tabstrip-items.k-reset .k-item {
    color: $tb-kendo-heading-text;
}
.k-tabstrip .k-tabstrip-items-wrapper.k-hstack .k-tabstrip-items.k-reset {
    //   border-bottom-color: #e5e7eb;
    //   border-bottom-style: solid;
    //   border-bottom-width: 1px;
}
.k-tabstrip .k-tabstrip-content.k-active,
.k-tabstrip .k-tabstrip-content.k-state-active,
.k-tabstrip .k-tabstrip-content:active {
    border-bottom-color: rgba(0, 0, 0, 0.08);
    // border-bottom-style: solid;
    border-bottom-width: 0px;
    border-left-width: 0px;
    border-right-width: 0px;
    border-top-width: 0px;
}
.k-card {
    @extend %tb-effects-kendo-shadow;
    border-bottom-width: 0px;
    border-left-width: 0px;
    border-right-width: 0px;
    border-top-width: 0px;
}
.k-card .k-card-header .k-card-title {
    color: $tb-kendo-heading-text;
}
.k-expander.k-expanded {
    border-bottom-color: #e5e7eb;
    border-left-color: #e5e7eb;
    border-right-color: #e5e7eb;
    border-top-color: #e5e7eb;
    border-bottom-left-radius: $tb-kendo-border-radius;
    border-bottom-right-radius: $tb-kendo-border-radius;
    border-top-left-radius: $tb-kendo-border-radius;
    border-top-right-radius: $tb-kendo-border-radius;
}
.k-expander .k-expander-header .k-expander-title {
    color: $tb-kendo-heading-text;
}
.k-expander .k-expander-header.k-hover,
.k-expander .k-expander-header.k-state-hover,
.k-expander .k-expander-header.k-state-hovered,
.k-expander .k-expander-header:hover {
    background-color: #e6f7ff;
    background-image: none;
}
.k-expander.k-focus.k-expanded,
.k-expander.k-state-focus.k-expanded,
.k-expander.k-state-focused.k-expanded,
.k-expander:focus.k-expanded {
    border-bottom-color: $kendo-border-color;
    border-left-color: $kendo-border-color;
    border-right-color: $kendo-border-color;
    border-top-color: $kendo-border-color;
    outline-offset: 0px;
    outline-style: solid;
}

// Extra override
.k-splitbar:hover,
.k-splitbar.k-hover,
.k-splitbar-horizontal-hover,
.k-splitbar-vertical-hover {
    background-color: transparent;
    color: $tb-kendo-color-primary !important;

    .k-resize-handle {
        height: 100%;
        cursor: col-resize;
    }
}

.k-splitbar {
    color: #e5e7eb;
    align-items: flex-start;
    &.k-splitbar-draggable-horizontal .k-resize-handle {
        height: 100%;
    }
}

.k-splitbar.k-state-hover,
.k-splitbar.k-state-hovered,
.k-splitbar:hover,
.k-splitbar.k-state-focused {
    background: transparent;
    color: $tb-kendo-color-primary;
}

.k-pager-numbers .k-link {
    border-radius: 0;
}

.k-grid .k-grid-header .k-header a {
    color: #6b7280;
    font-size: 0.75rem !important;
    font-weight: 400;
    margin-bottom: 0px;
    // padding-right: $kendo-padding-input;
    text-transform: capitalize;
    cursor: pointer;
}

.k-grid .k-grid-header .k-header {
    background-color: #fff;
    position: relative;
    text-transform: capitalize;
    &.k-grid-header-sticky {
        position: sticky;
    }
}

.k-grid {
    border: none;
    .k-grid-header {
        font-size: 0.75rem;
    }
}

.k-grid-header .k-grid-filter.k-state-active,
.k-grid-header .k-header-column-menu.k-state-active,
.k-grid-header .k-hierarchy-cell .k-icon.k-state-active {
    background-color: transparent !important;
}

/*checkbox */

.k-checkbox {
    border-radius: 4px;
    border-color: var(--color-gray-400);
}

.k-grid td > .k-checkbox {
    vertical-align: inherit;
}

.k-checkbox:checked {
    border-color: #1890ff;
    color: #fff;
    background-color: #1890ff;
}

/* td */

.k-grid td {
    border-width: 0px 0 1px 0px !important;
    background-color: white;
}

.k-grid tr.k-state-selected > td {
    background-color: #e6f7ff;
}

.k-grid tr.k-state-selected:hover > td {
    background-color: var(--color-primary-blue-50);
    .actionButtonHide,
    .tw-btn-action,
    .tw-action-group,
    &.k-command-cell > * {
        pointer-events: none;
        cursor: not-allowed;
        opacity: 0.6;
        max-width: fit-content;
        .k-i-more-horizontal {
            color: var(--color-gray-400);
        }
    }
}

.demo-section .tabstrip-container {
    width: 500px;
}

/* Tab */

.k-tabstrip-items-wrapper,
.k-tabstrip-items-wrapper .k-item.k-state-active,
.k-tabstrip-items-wrapper .k-item.k-state-selected,
.k-tabstrip-items .k-item,
.k-tabstrip > .k-content {
    border: none;
}

.k-tabstrip-items-wrapper .k-item.k-state-active span,
.k-tabstrip-items-wrapper .k-item.k-state-active svg,
.k-tabstrip-items-wrapper .k-item.k-state-selected span {
    color: rgb(59 130 246); //text-blue-500
}

.k-tabstrip-items-wrapper .k-item span {
    color: rgb(107 114 128); //text-gray-500
}

.k-tabstrip {
    padding: 4px 0px;
}

.k-tabstrip-content,
.k-tabstrip > .k-content {
    padding: 4px 0px;
}

/* Pagination */

.k-pager-wrap {
    background-color: white;
    color: #6b7280;
    padding-block: 0.5rem;
}

.k-grid-pager {
    border-top: 1px solid #e5e7eb;
}

.k-pager-numbers .k-link {
    color: #6b7280;
}

.k-pager-numbers .k-link.k-state-selected {
    color: #1890ff;
    background-color: #e6f7ff;
}

.k-pager-numbers .k-link:hover {
    color: #1890ff;
    background-color: #e6f7ff;
}

.k-pager-numbers .k-link,
.k-pager-numbers .k-link:hover,
.k-pager-numbers .k-state-selected {
    border-radius: 0;
    /*Use squares instead of circles*/
    border: 1px solid #bbbbbb;
}

/*Selected page number*/

.k-pager-numbers .k-state-selected {
    border-color: #bbbbbb;
}

/*Pager links*/

.k-pager-wrap > .k-link {
    border-radius: 0;
    border: 1px solid #bbbbbb;
}

.k-pager-wrap > .k-link:hover {
    border-color: #4f4f4f;
}

.k-pager-numbers .k-link,
.k-pager-wrap .k-pager-nav {
    // margin: -1px !important;
}

.k-grid-pager .k-link,
.k-grid-pager .k-pager-numbers {
    float: none;
}

// Popup
.k-popup.k-calendar-container,
.k-popup.k-list-container {
    padding: 0;
    border-width: 0px;
    //   background-color: transparent;
    border-radius: $tb-kendo-border-radius;
    @extend %tb-effects-kendo-shadow;
}

.k-popup.k-list-container {
    padding: 0.25rem;
}

.k-popup {
    border-radius: $tb-kendo-border-radius;
}

.k-grid td,
.k-grid th {
    padding: 8px 12px;
}

.k-daterange-picker .k-dateinput,
.k-daterangepicker .k-dateinput {
    width: auto;
}

.k-window-actions {
    // border-width: 0;

    &:is(:focus, :focus-visible) {
        outline: none;
    }
}

.k-listbox.k-listbox-toolbar-right {
    flex-direction: row-reverse;
}

.k-menu .k-item,
.k-menu-scroll-wrapper .k-item,
.k-menu-scroll-wrapper.horizontal > .k-item,
.k-popups-wrapper .k-item,
.k-popups-wrapper.horizontal > .k-item,
.k-widget.k-menu-horizontal > .k-item {
    border-right-width: 0;
}

.k-pager-nav {
    border-radius: 0;
}

.k-pager-nav + .k-pager-nav,
.k-pager-nav + .k-pager-numbers-wrap,
.k-pager-numbers li + li,
.k-pager-numbers-wrap + .k-pager-nav {
    // margin-left: 0;
}

.k-pager-sizes {
    margin-inline: 0;
}

.k-input-md .k-input-icon,
.k-input-md .k-input-validation-icon,
.k-input-md .k-input-loading-icon,
.k-input-md .k-clear-value,
.k-input-md .k-input-prefix > .k-icon,
.k-input-md .k-input-suffix > .k-icon,
.k-picker-md .k-input-icon,
.k-picker-md .k-input-validation-icon,
.k-picker-md .k-input-loading-icon,
.k-picker-md .k-clear-value,
.k-picker-md .k-input-prefix > .k-icon,
.k-picker-md .k-input-suffix > .k-icon {
    padding-block: 0;
    padding-inline: 0;
}

.k-switch-md.k-switch-on .k-switch-thumb-wrap {
    left: calc(100% - 12px - 0px);
}

.k-switch-md.k-switch-off .k-switch-thumb-wrap {
    left: calc(12px + 0px);
}

.k-input-icon,
.k-input-validation-icon,
.k-input-loading-icon,
.k-input-prefix > .k-icon,
.k-input-suffix > .k-icon {
    display: inline-block;
}

.k-radio::before {
    width: 6px;
    height: 6px;
}

.k-dialog-buttongroup .k-button {
    @apply inline-flex h-8 items-center justify-center space-x-1 rounded-lg border border-gray-300 bg-white pl-2 pr-2 text-gray-700 hover:bg-gray-50 hover:shadow focus:ring-2 focus:ring-primary-blue-500 focus:ring-offset-2 focus:ring-offset-white;
    &.k-primary {
        @apply inline-flex h-8 items-center justify-center space-x-1 rounded-lg border bg-primary-blue-500 pl-2 pr-2.5 text-white shadow hover:border-primary-blue-500 hover:shadow-lg focus:ring-2 focus:ring-primary-blue-500 focus:ring-offset-2 focus:ring-offset-white;
    }
}

.k-dateinput-wrap,
.k-dropdown-wrap,
.k-multiselect-wrap,
.k-numeric-wrap,
.k-picker-wrap {
    border-radius: $tb-kendo-border-radius;
}

.k-calendar .k-header {
    box-shadow: none;
    border-color: transparent;
    color: var(--color-gray-900);
    background-color: white;
    border-top-left-radius: $tb-kendo-border-radius;
    border-top-right-radius: $tb-kendo-border-radius;
}

.k-calendar-footer,
.k-calendar .k-footer {
    border-bottom-left-radius: $tb-kendo-border-radius;
    border-bottom-right-radius: $tb-kendo-border-radius;
    overflow: hidden;
}

.k-calendar {
    .k-link {
        color: var(--color-gray-900);
    }
    .k-other-month {
        .k-link {
            color: var(--color-gray-400);
        }
    }
}

.k-popup.k-calendar-container,
.k-popup.k-list-container {
    border-radius: $tb-kendo-border-radius;
    overflow: hidden;
}

.k-calendar .k-calendar-td.k-state-selected .k-link,
.k-calendar .k-calendar-td.k-state-focused .k-link {
    color: white;
}

// .k-form .k-form-field > .k-label,
// .k-form .k-form-field > kendo-label,
// .k-form .k-form-label {
//   color: var(--color-gray-700) !important;
//   font-size: var(--fs-base-sm) !important;
// }

// .k-checkbox::before {
//   content: "";
// }

.k-checkbox:checked,
.k-checkbox.k-checked {
    background-image: none;
}

.k-checkbox-md::before {
    font-size: 12px;
}

label.k-label,
label.k-radio-label {
    // font-size: var(--fs-base-sm) !important; // Override ADMINLTE CSS
    // color: var(--color-gray-700) !important;
}

.k-textarea > textarea {
    border: 0;
}

.k-textarea {
    border-radius: $tb-kendo-border-radius;
}

.k-dialog {
    .k-dialog-buttongroup {
        padding: 1rem 1.5rem;
        &.k-justify-content-stretch {
            justify-content: flex-end;
            & > * {
                flex: 0 1 auto;
            }
        }
    }
}

.k-scheduler-monthview .k-scheduler-table td {
    text-align: left;
}

.k-pager-sizes .k-dropdown,
.k-pager-sizes > select {
    margin-right: 0;
}

// Filter
form.k-filter-menu .k-textbox {
    border-radius: $tb-kendo-border-radius;
}

.k-filter-menu span.k-filter-and {
    margin-block: 0;
}

.k-filter-menu .k-action-buttons .k-button {
    width: fit-content;
    margin: 0;
}

.k-filter-menu .k-action-buttons {
    justify-content: flex-end;
    padding-block: 0.25rem;
    padding-inline-end: 0.125rem;
}

form.k-filter-menu .k-textbox,
form.k-filter-menu .k-widget.k-multiselect {
    border-radius: $tb-kendo-border-radius;
    border-color: #b6b6b6;
}

.k-grid-header .k-grid-filter:focus,
.k-grid-header .k-grid-filter.k-focus,
.k-grid-header .k-header-column-menu:focus,
.k-grid-header .k-header-column-menu.k-focus,
.k-grid-header .k-grid-header-menu:focus,
.k-grid-header .k-grid-header-menu.k-focus,
.k-grid-header .k-hierarchy-cell .k-icon:focus,
.k-grid-header .k-hierarchy-cell .k-icon.k-focus {
    box-shadow: none;
    background-color: transparent;
    svg {
    }
}

.k-widget.k-textarea {
    width: 100%;
    border-radius: $tb-kendo-border-radius;
    border-color: $kendo-border-color;
    &:focus {
        border: 1px solid #1890ff;
        box-shadow:
            0px -2px 2px 2px rgba(24, 144, 255, 0.1),
            0px 2px 2px 2px rgba(24, 144, 255, 0.1);
    }
}

.k-tabstrip-top > .k-tabstrip-items-wrapper .k-item.k-state-active {
    border-bottom-color: var(--color-primary-blue-500);
}

.k-tabstrip-top > .k-tabstrip-items-wrapper .k-item {
    margin-bottom: 0;
}

// Table
.k-grid-content table,
.k-grid-content-locked > table,
.k-grid-footer table,
.k-grid-header table {
    border-inline: 1px solid var(--color-gray-200);
}

.k-filter-row th,
.k-grid-header th.k-header {
    border-width: 1px 0 1px 1px;
}

#studentList {
    .k-grid-content table,
    .k-grid-content-locked > table,
    .k-grid-footer table,
    .k-grid-header table {
        border-inline: 0;
    }
}

// Pagination
.k-pager-nav + .k-pager-nav,
.k-pager-nav + .k-pager-numbers-wrap,
.k-pager-numbers li + li,
.k-pager-numbers-wrap + .k-pager-nav {
    margin-left: 0;
}

.k-list.k-list-md .k-list-content .k-list-ul .k-list-item.k-selected,
.k-list.k-list-md .k-list-content .k-list-ul .k-selected.k-list-optionlabel {
    // color: white;
    background: var(--color-gray-50);
    display: flex;
    justify-content: space-between;
    align-items: center;

    &::after {
        content: '\e118';
        font-family: WebComponentsIcons, monospace;
        color: var(--color-primary-blue-500);
    }
}

.k-list-item:hover,
.k-list-optionlabel:hover,
.k-list-item.k-hover,
.k-hover.k-list-optionlabel {
    border-radius: $tb-kendo-border-radius;
    background-color: var(--color-gray-50);
}

.k-list {
    padding: 0.25rem;
    gap: 0rem;
}

.k-list-md .k-list-item,
.k-list-md .k-list-optionlabel {
    padding-block: 0.25rem;
    &:not(:last-child) {
        margin-bottom: 0.125rem;
    }
}

// Input foucs
.k-picker-solid:focus,
.k-picker-solid.k-focus,
.k-input-solid:focus,
.k-input-solid.k-focus,
.k-numerictextbox.k-state-focus-within.k-input,
.k-numerictextbox:focus-within.k-input {
    outline: none;
    box-shadow:
        0px -2px 2px 2px rgba(24, 144, 255, 0.1),
        0px 2px 2px 2px rgba(24, 144, 255, 0.1);
    border-color: #1890ff;
}

.k-form .k-form-field > .k-label:not(.k-checkbox-label),
.k-form .k-form-field > kendo-label,
.k-form .k-form-label:not(.k-checkbox-label) {
    margin-bottom: 0.25rem;
}

.k-textbox .k-icon {
    // transform: translateY(-50%);
    margin: 0;
}

.k-list-filter .k-textbox {
    padding-inline: 0.25rem;

    .k-input-inner {
        margin-left: 0;
    }
}

.k-numerictextbox {
    .k-icon::before {
        font-size: 0.75rem;
    }
}

.k-checkbox::before {
    content: '\e118';
    width: 12px;
    height: 12px;
    font-size: 12px;
    font-family: WebComponentsIcons, monospace;
    -webkit-transform: scale(0) translate(-50%, -50%);
    -ms-transform: scale(0) translate(-50%, -50%);
    transform: scale(0) translate(-50%, -50%);
    overflow: hidden;
    position: absolute;
    top: 50%;
    left: 50%;
}

.k-checkbox:checked::before {
    transform: scale(1) translate(-50%, -50%);
}

.k-actions-stretched > * {
    flex: 0 1 auto;
}

.k-actions {
    gap: 0.75rem;
}

.k-dialog-buttongroup .k-button {
    min-width: 80px;
}

.k-multiselect,
.k-picker-wrap {
    [type='text']:focus,
    [type='email']:focus,
    [type='password']:focus {
        box-shadow: none;
        border-color: none;
    }
}

.k-list.k-list-md .k-list-content .k-list-ul .k-list-item.k-focus .k-list-item-text,
.k-list.k-list-md .k-list-content .k-list-ul .k-focus.k-list-optionlabel .k-list-item-text,
.k-list.k-list-md .k-list-content .k-list-ul .k-list-item.k-state-focus .k-list-item-text,
.k-list.k-list-md .k-list-content .k-list-ul .k-state-focus.k-list-optionlabel .k-list-item-text,
.k-list.k-list-md .k-list-content .k-list-ul .k-list-item.k-state-focused .k-list-item-text,
.k-list.k-list-md .k-list-content .k-list-ul .k-state-focused.k-list-optionlabel .k-list-item-text,
.k-list.k-list-md .k-list-content .k-list-ul .k-list-item:focus .k-list-item-text,
.k-list.k-list-md .k-list-content .k-list-ul .k-list-optionlabel:focus .k-list-item-text {
    background-color: transparent;
}

.k-animation-container > .k-popup.k-list-container {
    // width: 100% !important;
}

.k-grid .k-grid-header .k-table-th {
    vertical-align: middle;
}

.k-scheduler .k-event {
    // margin-left: -1px;
    border-left-width: 0px;
    padding-right: 0;
}

.k-icon {
    font-family: WebComponentsIcons, monospace;
}

.k-button {
    outline: 0;
    outline-width: 0;
}

.k-filter-menu.k-popup .k-filter-menu-container,
.k-grid-filter-popup.k-popup .k-filter-menu-container {
    width: auto;
    min-width: 280px;
}

.k-grid-header th.k-header:first-child,
.k-grid-header table {
    border-top-left-radius: $tb-kendo-border-radius;
}

.k-grid table {
    border-collapse: collapse;
}

.k-timeline-horizontal .k-card {
    border: 1px solid var(--color-gray-200);
}

.k-timeline-card {
    .k-card-callout {
        box-shadow: none;
    }

    .k-card {
        box-shadow: none;
    }
}

.k-timeline .k-timeline-circle {
    width: 1.5rem;
    height: 1.5rem;
    position: relative;
    &::after {
        font-family: WebComponentsIcons, monospace;
        content: '\e118';
        position: absolute;
        top: 50%;
        left: 50%;
        transform: translate(-50%, -50%);
        color: white;
        font-size: 0.75rem;
    }
}

.k-timeline-horizontal .k-timeline-track-wrap::after {
    bottom: 26px;
}

.k-grid .k-grid-edit-row td > input:not([type='checkbox']):not([type='radio']) {
    height: 36px;
    border-radius: $tb-kendo-border-radius;
}

.k-grid td,
.k-grid th,
.k-grid-md .k-table-th {
    padding: 0.625rem 0.75rem;
}

.gridInfo {
    display: inline-flex;
    align-items: center;
}

.k-detail-row {
    background-color: var(--color-bluegray-50);
    .k-grid,
    .k-grid td,
    .k-grid-content {
        background-color: transparent;
    }
    table {
        border-inline: 0;
    }
}

.k-scheduler-header,
.k-scheduler-header-wrap {
    font-size: 0.75rem;
    letter-spacing: 0.25px;
    text-transform: uppercase;
    padding-inline-end: 0;
}

.k-tabstrip-top > .k-tabstrip-items-wrapper .k-item + .k-item {
    margin-left: 0;
}

.profile-tablist > .k-tabstrip-items-wrapper {
    position: relative;
    &::after {
        content: '';
        z-index: 1;
        position: absolute;
        bottom: -1rem;
        width: 100%;
        height: 1rem;
        box-shadow: inset 0px 3px 6px -6px rgba(0, 0, 0, 0.5);
        background-color: transparent;
    }
}

.k-checkbox + .k-checkbox-label {
    color: var(--color-gray-700);
}

.k-checkbox + .k-label,
.k-checkbox-wrap + .k-label,
.k-checkbox + .k-checkbox-label,
.k-checkbox-wrap + .k-checkbox-label {
    margin-inline-start: 0.5rem;
}

.k-window-title::before {
    display: none;
}

.k-selected.k-list-optionlabel,
.k-selected.k-list-optionlabel:hover,
.k-selected.k-hover.k-list-optionlabel {
    background-color: white;
    color: var(--color-gray-400);
}

.k-form-md .k-form-field,
.k-form-md .k-form-buttons {
    margin-top: 0;
}

.k-dialog-wrapper .k-dialog.tw-sidebar-drawer {
    position: absolute;
}

.k-tooltip.k-popup {
    color: white;
}

// .k-grid-content.k-virtual-content table.k-table.k-grid-table {
//     width: 100% !important;
// }

.k-rounded-md {
    border-radius: $tb-kendo-border-radius;
}

.k-grid-header .k-table-th.k-grid-header-sticky,
.k-grid-header td.k-grid-header-sticky,
.k-grid-header .k-table-td.k-grid-header-sticky,
.k-grid-header .k-grid-header-sticky.k-sorted {
    border-right-color: rgba(0, 0, 0, 0.08);
    border-left-color: rgba(0, 0, 0, 0.08);
}

.k-loader-container-overlay {
    opacity: 1;
}

.k-button-solid-base:hover,
.k-button-solid-base.k-hover {
    background-color: white;
}

.k-scheduler {
    border-color: var(--color-gray-5);
}

.k-scheduler-layout-flex .k-scheduler-cell.k-heading-cell {
    font-weight: 500;
}

.k-popup {
    box-shadow:
        0px 4px 6px -2px rgba(0, 0, 0, 0.05),
        0px 10px 15px -3px rgba(0, 0, 0, 0.1),
        0px 0px 0px 1px rgba(0, 0, 0, 0.05);
}

.k-form .k-label {
    font-weight: 500;
}

.k-radio + .k-radio-label {
    color: var(--color-gray-700);
}

.k-autocomplete {
    [type='text']:focus,
    [type='email']:focus,
    [type='password']:focus {
        box-shadow: none;
        border-color: transparent;
    }
}

.k-autocomplete > .k-clear-value {
    width: 2rem;
}

.k-form .k-form-fieldset {
    margin: 0;
}

.tw-autocomplete {
    &.tw-autocomplete-empty > .k-list {
        display: none;
    }
}

.k-state-disabled {
    opacity: 0.6;
    background-color: var(--color-bluegray-50);
    cursor: not-allowed;
}

.k-list-horizontal .k-radio-item {
    margin: 0;
}

.k-combobox .k-select,
.k-numeric-wrap .k-select,
.k-picker-wrap .k-select {
    border-width: 0;
}

.k-stepper .k-step-current .k-step-label,
.k-stepper .k-step-hover .k-step-label,
.k-stepper .k-step:hover .k-step-label {
    font-weight: normal;
}

.k-wizard {
    padding-block: 1.5rem;
    padding-inline: 1.5rem;
}

.k-textbox > .k-input {
    height: 100%;
}

.k-list-filter .k-textbox {
    border-radius: $tb-kendo-border-radius;
    min-height: 2.25rem;
    padding-left: 1.125rem;

    & + .k-icon {
        left: 0.75rem;
    }
}

.k-list-filter .k-textbox.k-input {
    padding-left: 0.25rem;
}

.k-fieldselector .k-list .k-item,
.k-list-optionlabel.k-state-focused,
.k-list-optionlabel.k-state-selected,
.k-listbox .k-item,
.k-popup .k-list .k-state-focused,
.k-popup .k-list .k-state-selected,
.k-action-buttons .k-primary {
    background-color: var(--color-gray-100);
    color: var(--color-gray-700);
    border-radius: 0.25rem;
}

.k-form fieldset legend,
.k-form-inline fieldset legend {
    color: var(--color-gray-700);
}

// .k-tooltip.k-popup {
//     box-shadow: none;
// }

.k-scheduler-table td,
.k-scheduler-table th {
    font-weight: 400;
}

.kendo-scheduler .k-event,
.k-event {
    min-height: 32px;
}

.k-scrollbar-h tr + tr .k-scheduler-times {
    border-bottom-width: 0;
}

div.k-grid-header {
    display: block;
}

.splitter .k-splitbar-horizontal.k-splitbar-draggable-horizontal .k-i-arrow-60-left,
.splitter .k-splitbar-horizontal.k-splitbar-draggable-horizontal .k-i-arrow-60-right,
.k-ghost-splitbar .k-collapse-prev,
.k-ghost-splitbar .k-collapse-next,
.k-splitbar .k-collapse-prev,
.k-splitbar .k-collapse-next {
    display: none;
}

.k-pager-wrap {
    padding: 0.5rem 1.5rem;
    &.gridPagination {
        padding-inline: 0;
    }
}

.k-state-collapsed + .k-splitbar-horizontal {
    width: 0;
}

.k-grid .k-grid-norecords + .k-grid-content-expander {
    display: none;
}

.k-animation-container-relative {
    display: block;
}

.k-window-content,
.k-prompt-container {
    padding-inline: 1.5rem;
}

.k-grid-pager.k-pager-md {
    flex-direction: row-reverse;
    justify-content: space-between;
}

.k-pager-md {
    padding-block: 1rem;
    padding-inline: 1.5rem;
}

.k-grid th,
.k-grid-md .k-table-th {
    padding-block: 0.5rem;
}

.tw-editor-input {
    &:focus {
        box-shadow: none;
    }
}

.k-grid-md .k-edit-cell,
.k-grid-md .k-command-cell,
.k-grid-md .k-grid-edit-row td,
.k-grid-md .k-grid-edit-row .k-table-td {
    padding-block: 0.25rem;
}

.k-calendar .k-calendar-decadeview,
.k-calendar .k-calendar-monthview,
.k-calendar .k-calendar-yearview {
    padding: 0 4px;
    min-width: 260px;
}

.k-list-optionlabel.k-state-focused,
.k-list-optionlabel.k-state-selected {
    border-width: 0;
    background-color: transparent;
    color: $kendo-border-color;
    padding: 0 8px;
}

.k-grid-content {
    overflow-y: auto;
}

.k-d-grid {
    // align-items: flex-end;
    .k-form-field:has(> .k-form-field-wrap > .k-dropdown.self-end) {
        align-self: flex-end;
    }
}

.k-splitter.k-splitter-flex.k-splitter-horizontal
    .k-splitbar.k-splitbar-horizontal.k-splitbar-draggable-horizontal,
.k-splitter.k-splitter-flex.k-splitter-horizontal .k-splitbar.k-splitbar-horizontal {
    width: 0;
}

.k-form-buttons {
    margin: 0;
}

.k-popup .k-list .k-state-focused,
.k-popup .k-list .k-state-selected {
    .course_list {
        .k-icon.k-i-check {
            &::before {
                color: var(--color-primary-blue-500);
            }
        }
    }
}

.k-form-hint,
.k-form-error {
    margin: 0;
}

.k-dropdown-wrap {
    border-width: 1px;
    padding-block: 0.25rem;
}

.k-picker-wrap {
    border-width: 1px;
    border-color: $kendo-border-color;
}

.k-pager-sizes .k-dropdown .k-dropdown-wrap {
    border-width: 1px;
    border-color: var(--color-gray-400);
    padding-block: 0;
    .k-select {
        line-height: 2;
    }
}

.k-dropdown-wrap .k-select,
.k-numeric-wrap .k-select,
.k-picker-wrap .k-select {
    line-height: 2rem;
}

.k-listview {
    background-color: transparent;
}

.k-listview-content {
    padding-block: 1rem;
}

.k-grid .k-grid-header .k-grid-filter {
    top: 50%;
    transform: translateY(-30%);
}

div.cusInput input {
    width: 100%;
}

.k-textbox {
    border-width: 1px;
    border-style: solid;
    border-radius: $tb-kendo-border-radius;
    padding-block: 0.5rem;
    height: 2.25rem;
    border-color: $kendo-border-color;
}

.k-calendar .k-state-focused,
.k-calendar .k-state-focused.k-state-selected {
    .k-link {
        background-color: var(--color-primary-blue-500);
    }
    &:hover,
    &.k-state-hover,
    &.k-state-focused {
        .k-link {
            background-color: var(--color-primary-blue-500);
            color: white;
        }
    }
}

.k-textbox .k-icon {
    position: relative;
    top: unset;
}

.k-form-field-wrap .k-textbox:has(> .k-dropdown-wrap) {
    &,
    &:is(:focus, :hover) {
        padding: 0;
        border-width: 0;
        box-shadow: none;
    }
}

.tw-input-borderless:focus {
    box-shadow: none !important;
    border-width: 0 !important;
    border-color: transparent !important;
}

.k-dropdown {
    width: 100%;
}

.k-panelbar .k-group > .k-item > .k-link:hover,
.k-panelbar .k-group > .k-item > .k-link.k-hover,
.k-panelbar .k-group > .k-panelbar-item > .k-link:hover,
.k-panelbar .k-group > .k-panelbar-item > .k-link.k-hover,
.k-panelbar .k-panelbar-group > .k-item > .k-link:hover,
.k-panelbar .k-panelbar-group > .k-item > .k-link.k-hover,
.k-panelbar .k-panelbar-group > .k-panelbar-item > .k-link:hover,
.k-panelbar .k-panelbar-group > .k-panelbar-item > .k-link.k-hover {
    background-color: var(--color-primary-blue-50);
    border-radius: $tb-kendo-border-radius;
}

.k-panelbar .k-group > .k-item.k-level-1 .k-link,
.k-panelbar .k-panelbar-group > .k-panelbar-item.k-level-1 .k-link {
    padding-left: 0.5rem;
}

.k-calendar .k-calendar-view {
    width: 280px;
    height: fit-content;
    // width: fit-content;
}

.k-calendar-view {
    padding-inline: 0;
}

.k-calendar .k-content .k-link {
    margin: auto 0;
}

.k-calendar-centuryview .k-content .k-link {
    width: 64px;
    height: 64px;
    inline-size: var(--INTERNAL--kendo-calendar-cell-size, 64px);
    block-size: var(--INTERNAL--kendo-calendar-cell-size, 64px);
}

.k-calendar .k-calendar-decadeview,
.k-calendar .k-calendar-monthview,
.k-calendar .k-calendar-yearview {
    padding-inline: 0.5rem;
}

.k-multiselect-wrap .k-input {
    min-height: 2.25rem;
}

.k-tabstrip-items .k-link {
    padding-inline: 0;
}

.k-tabstrip,
.k-tabstrip-content,
.k-tabstrip > .k-content {
    padding: 0;
}

.k-picker-wrap.k-state-border-down {
    border-bottom-width: 1px;
    padding-bottom: 0;
}
.k-filemanager-search-tool {
    .k-input-icon {
        width: 1rem;
    }

    .k-input {
        width: 100%;
    }
}

.k-toolbar {
    .k-button-group {
        padding: 2px 0;
    }
}
.k-toggle-selection-group {
    .k-dropdown-wrap {
        padding-block: 0;
    }
    .k-button {
        &:focus {
            outline: none;
        }
    }
    .k-button:hover::before,
    .k-button.k-hover::before,
    .k-button.k-state-active::before,
    .k-button:active::before {
        opacity: 0;
    }
}

.k-grid .k-grid-header .k-header {
    min-height: 40px;
}

.k-filter-row th,
.k-grid-header th.k-header {
    padding: 0.5rem 0.75rem 0.25rem;
    min-height: 40px;
}

.k-scheduler .k-scrollbar-v .k-scheduler-header-wrap {
    border-right-width: 0;
}

.k-scheduler-monthview .k-scheduler-content {
    overflow-y: auto;
}

// .k-grid-header th.k-header .k-checkbox {
//     margin-top: -2px;
// }

.k-dropdown-wrap .k-select .k-icon {
    background-color: white;
}

.k-datepicker {
    width: 100%;
}

.k-callout-w {
    transform: unset;
}

.k-window-actions {
    border-width: 0;
}

.k-grid .k-command-cell > .k-button {
    &.k-button-icontext {
        font-size: 0 !important;
        .k-icon {
            margin-inline-start: 2.5px;
            margin-inline-end: 0;
            // width: 0.875rem;
            // height: 0.875rem;
            flex: 1 0 100%;
            &::before {
                background-size: 0.875rem;
            }
        }
    }
    width: 1.5rem;
    height: 1.5rem;
    display: inline-flex;
    justify-content: center;
    align-items: center;
    min-width: 1.5rem;
    border-width: 0;
    padding: 0;
    flex: 1 0;
    border-radius: $tb-kendo-border-radius;
    &:hover {
        background-color: white;
        box-shadow:
            rgba(0, 0, 0, 0.1) 0px 1px 3px 0px,
            rgba(0, 0, 0, 0.06) 0px 1px 2px 0px;
    }
}

.k-animation-container:has(> .k-tooltip > .k-tooltip-content > .firstTdTooltip) {
    overflow: visible !important;
}

.k-animation-container:has(> .tw-tooltip.tw-popup--top-right) {
    overflow: visible !important;
}

.k-detail-row {
    max-height: 0;
    overflow: hidden;
    transition: max-height 0.3s ease; /* Adjust the duration and easing as needed */
}

/* Apply expanded styles when the row is expanded */
.k-detail-row.k-state-expanded {
    max-height: 500px; /* Adjust the maximum height as needed */
}

.k-form-buttons {
    margin-top: 1.5rem;
}

.k-input {
    border-color: var(--color-gray-200);
    border-radius: $tb-kendo-border-radius;
    border-width: 0;
    &.k-timepicker {
        border-width: 0;
    }
}

// SKELETON
@keyframes k-skeleton-pulse {
    0%,
    100% {
        opacity: 1;
    }
    50% {
        opacity: 0.5;
    }
}
.k-skeleton-pulse .k-skeleton,
.k-skeleton-pulse .k-placeholder-line,
.k-skeleton-pulse.k-skeleton,
.k-skeleton-pulse.k-placeholder-line {
    animation: k-skeleton-pulse 2s cubic-bezier(0.4, 0, 0.6, 1) infinite;
}

.k-skeleton-wave .k-skeleton::after,
.k-skeleton-wave .k-placeholder-line::after,
.k-skeleton-wave.k-skeleton::after,
.k-skeleton-wave.k-placeholder-line::after {
    background-image: linear-gradient(to right, transparent, rgba(0, 0, 0, 0.04), transparent);
}

.k-skeleton,
.k-placeholder-line {
    background-color: var(--color-gray-100);
}

.k-input.k-input-solid {
    color: var(--color-gray-700);
    border-width: 1px;
    height: 2.25rem;
    align-items: center;
    &.k-multiselect,
    &.k-textarea {
        height: auto;
    }
}

.k-form-field-wrap {
    & > .k-dropdownlist.k-rounded-md {
        height: 2.25rem;
    }
}

.k-grid-md .k-table-th > .k-cell-inner > .k-link {
    padding-block: 0.75rem;
}

.k-table,
.k-data-table {
    color: var(--color-gray-600);
}

.k-textbox.k-input.k-input-solid:focus-within {
    box-shadow:
        0px -2px 2px 2px rgba(24, 144, 255, 0.1),
        0px 2px 2px 2px rgba(24, 144, 255, 0.1);
    border-color: #1890ff;
}

.user-profile-pic {
    border-radius: 50%;
    /*background: #337ab7;*/
    color: #fff !important;
    text-align: center;
    flex-shrink: 0;
    font-weight: 500;
}

/* Copy data icon blue */
.copy_data.active span {
    color: #1890ff;
}

.student-profile-pic {
    border-radius: 50%;
    color: #fff !important;
    text-align: center;
}

.k-chip-md {
    .k-chip-action {
        color: var(--color-gray-400);
    }
    &:hover {
        .k-chip-action {
            color: var(--color-gray-600);
        }
    }
}

.k-grid-header th.k-header .k-checkbox {
    vertical-align: baseline;
    margin-bottom: 1.5px;
}

.k-timeline-horizontal .k-timeline-track-wrap .k-timeline-track .k-timeline-scrollable-wrap {
    transition: transform 500ms ease-in-out;
}

.k-timeline-horizontal .k-timeline-track-item {
    justify-content: flex-end;
}

// .k-timeline-horizontal
//     .k-timeline-events-list
//     .k-timeline-scrollable-wrap
//     .k-timeline-event {
//     // opacity: 0;
//     transition:
//         transform 450ms ease,
//         opacity 450ms ease;
//     // &.tw-timeline-active {
//     //     opacity: 1;
//     // }
// }

.k-tabstrip-content,
.k-tabstrip > .k-content {
    background: transparent;
}

.tw-tabstrip .k-tabstrip .k-tabstrip-items-wrapper {
    background-color: white;
    padding-inline: 1.5rem;
}

.k-listview-header {
    border-bottom: 0;
}

.k-calendar .k-content {
    padding: 0.25rem;
}

.k-table-md .k-column-title {
    font-size: 0.8125rem;
}

.k-switch-labelled {
    .k-switch-label-on,
    .k-switch-label-off {
        display: inline;
    }
}

.k-grid .k-table .k-grid-content-sticky {
    border-bottom-color: rgba(0, 0, 0, 0.08);
}

.k-master-row.k-table-alt-row .k-grid-content-sticky,
.k-master-row.k-table-alt-row .k-grid-row-sticky {
    background-color: #ffffff;
}

.k-textarea > .k-input {
    background: white;
}

.k-listbox .k-item {
    min-height: unset;
}

.k-button-solid-base.k-selected:hover,
.k-button-solid-base.k-hover.k-selected {
    background-color: var(--color-primary-blue-500);
}

.k-table-md {
    font-size: 0.875rem;
}

.k-timeline-horizontal .k-timeline-track-wrap {
    padding-block: 0;
}

.k-timeline-mobile {
    margin-block: 0;
    padding-block: 0;
}

.k-grid-header th.k-header > .k-link {
    padding: 0.7em 0.6em 0.4em 0;
}

.k-grid .k-button {
    margin: 0 0.25rem 0 0;
}

.k-list-filter > .k-textbox {
    width: 100% !important;
}

.k-dropdown-wrap.k-state-focus,
.k-dropdown-wrap.k-state-focused {
    box-shadow:
        0px -2px 2px 2px rgba(24, 144, 255, 0.1),
        0px 2px 2px 2px rgba(24, 144, 255, 0.1);
    border-color: #1890ff;
}
.k-widget * {
    box-sizing: inherit;
}

.k-list.k-list-md {
    padding: 0;
}

.k-list-filter {
    padding: 0 0 0.25rem 0;
}

.k-list-optionlabel,
.k-list-optionlabel.k-hover,
.k-list-optionlabel:hover {
    color: var(--color-gray-700);
}

.k-no-data,
.k-nodata {
    min-width: 256px;
}

.k-dropdownlist.k-rounded-md {
    height: 2.375rem;
}

.k-list-content,
.k-list-scroller,
.tw-daterange-popup__custom .k-calendar .k-calendar-view {
    overflow-y: auto;
    max-height: 200px;
    // background:
    //     linear-gradient(white 30%, rgba(255, 255, 255, 0)),
    //     linear-gradient(rgba(255, 255, 255, 0.5), white 70%) 0 100%,
    //         radial-gradient(
    //             50% 0,
    //             farthest-side,
    //             rgba(0, 0, 0, 0.1),
    //             rgba(0, 0, 0, 0)
    //         ),
    //     radial-gradient(
    //             50% 100%,
    //             farthest-side,
    //             rgba(0, 0, 0, 0.1),
    //             rgba(0, 0, 0, 0)
    //         )
    //         0 100%;
    background:
		/* Shadow covers */
        linear-gradient(white 30%, rgba(255, 255, 255, 0.5)),
        linear-gradient(rgba(255, 255, 255, 0.5), white 70%) 0 100%,
        /* Shadows */ radial-gradient(farthest-side at 50% 0, rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0)),
        radial-gradient(farthest-side at 50% 100%, rgba(0, 0, 0, 0.1), rgba(0, 0, 0, 0)) 0 100%;
    background-repeat: no-repeat;
    background-color: white;
    background-size:
        100% 40px,
        100% 40px,
        100% 14px,
        100% 14px;

    /* Opera doesn't support this in the shorthand */
    background-attachment: local, local, scroll, scroll;
}

.k-chip-md {
    font-size: 0.75rem;
}

.k-toolbar-md {
    padding-inline: 1rem;
}

.k-pager-sm .k-pager-numbers-wrap {
    width: fit-content;
    margin-right: 0;
}

.k-pager-md .k-pager-sizes .k-dropdown-list,
.k-pager-md .k-pager-sizes .k-dropdown,
.k-pager-md .k-pager-sizes .k-dropdownlist,
.k-pager-md .k-pager-sizes > select {
    width: 5rem;
}

.k-grid .k-table-th:focus,
.k-grid .k-table-th.k-focus,
.k-grid td:focus,
.k-grid td.k-focus,
.k-grid .k-table-td:focus,
.k-grid .k-table-td.k-focus,
.k-grid .k-master-row > td:focus,
.k-grid .k-master-row > td.k-focus,
.k-grid .k-grouping-row > td:focus,
.k-grid .k-grouping-row > td.k-focus,
.k-grid .k-detail-row > td:focus,
.k-grid .k-detail-row > td.k-focus,
.k-grid .k-group-footer > td:focus,
.k-grid .k-group-footer > td.k-focus,
.k-grid .k-master-row > .k-table-td:focus,
.k-grid .k-master-row > .k-table-td.k-focus,
.k-grid .k-grouping-row > .k-table-td:focus,
.k-grid .k-grouping-row > .k-table-td.k-focus,
.k-grid .k-detail-row > .k-table-td:focus,
.k-grid .k-detail-row > .k-table-td.k-focus,
.k-grid .k-group-footer > .k-table-td:focus,
.k-grid .k-group-footer > .k-table-td.k-focus,
.k-grid .k-grid-pager:focus,
.k-grid .k-grid-pager.k-focus {
    box-shadow: none;
}

.customPreviewFileManagerModal,
#noteAttachment {
    .k-list-scroller {
        max-height: 100%;
    }
}

.custom-pdf {
    .k-list-scroller {
        max-height: 430px;
        overflow-y: auto;
    }
}

.view-document-custom-pdf {
    .k-list-scroller {
        max-height: 80vh;
    }
    .k-pdf-viewer {
        height: auto !important;
    }
}

#passwordForm,
#agentProfileChangePasswordForm {
    position: relative;

    #old_password-form-error,
    #new_password-form-error,
    #password-form-error {
        display: none;
    }

    &:has(.k-invalid) span.invalid-form-input {
        border: 1px solid red;

        &:focus,
        &:focus-within {
            outline-color: currentColor;
            box-shadow:
                0px -2px 2px rgba(255, 24, 24, 0.1),
                0px 2px 2px rgba(255, 24, 24, 0.1) !important;
        }
    }
}

.header-checkbox-custom {
    height: 40px !important;
}

.k-checkbox {
    width: 1rem;
    height: 1rem;
}

.xero-custom-dropdown {
    margin-left: 0 !important;
    margin-right: 0 !important;
    min-width: 150px;
    .k-dropdown-wrap {
        padding: 0.178rem 0.25rem !important;
        border-radius: $tb-kendo-border-radius !important;
        box-shadow: none !important;
        color: rgb(55 65 81) !important;
    }
}

.xero-custom-datepicker {
    .k-daterangepicker {
        gap: 4px !important;
        width: 100% !important;
    }

    .k-textbox {
        border-radius: $tb-kendo-border-radius !important;
        color: rgb(55 65 81) !important;
    }
}

.k-multiselect.tw-input-borderless {
    padding-inline: 0;
    .k-clear-value {
        display: none;
    }
}
.k-form-hint,
.k-form-error {
    font-style: normal;
}

#closeLetterModelConformation {
    padding-bottom: clamp(16px, 12px, 12px) !important;
}

#changePasswordForm,
#agentProfileChangePasswordForm {
    input {
        background-color: transparent !important;
        border-block: none !important;
    }
}

#agentProfileChangePasswordForm div:first-child {
    border-block: none !important;
    background-color: transparent !important;
}

#employmentInformationForm #status li label {
    margin-left: 0.5rem;
}

.k-upload {
    .k-action-buttons {
        justify-content: flex-end;
        .k-upload-selected {
        }
    }
}
#employmentInformationForm #status li label {
    margin-left: 0.5rem;
}

.k-window-content.k-dialog-content {
    a {
        color: #1890ff !important;
        transition: all 0.3s ease-out;
        &:hover {
            text-decoration: underline !important;
        }
    }
}

.release-note-list {
    h2 {
        margin: 0 !important;
        font-size: 12px;
    }
    h4 {
        margin-bottom: 16px;
        font-size: 20px;
    }

    li {
        ul {
            padding-left: 16px !important;
            position: relative;
            // &::before{
            //     content: '';
            //     position: absolute;
            //     top: 0;
            //     left: 4px;
            //     background: #e1e1e1;
            //     height: 100%;
            //     width: 1px;
            // }

            li {
                font-size: 14px;
                font-weight: 500;
                position: relative;
                &:not(:last-child) {
                    margin-bottom: 15px;
                }
                // &::after{
                //     content: '';
                //     position: absolute;
                //     top: 3.5px;
                //     left: -14.5px;
                //     background: #e1e1e1;
                //     height: 8px;
                //     width: 8px;
                //     border-radius: 50%;
                // }
                ul {
                    margin-top: 10px;

                    &::before {
                        display: none;
                    }
                    li {
                        position: relative;
                        padding-left: 15px;
                        font-size: 14px;
                        color: #5a5a5a;
                        font-weight: normal;
                        &:not(:last-child) {
                            margin-bottom: 8px;
                        }
                        &::before {
                            content: '';
                            position: absolute;
                            top: 50%;
                            left: 0;
                            background: #e1e1e1;
                            height: 6px;
                            width: 6px;
                            border-radius: 50%;
                            transform: translateY(-50%);
                        }
                        &::after {
                            display: none;
                        }
                    }
                }
            }
        }
    }
    a {
        color: var(--color-primary-blue-500);
        &:hover {
            text-decoration: underline;
        }
    }
}

.div-separator {
    position: relative;
    &::before {
        content: '';
        position: absolute;
        left: 50%;
        top: 0;
        transform: translateX(-50%);
        height: 100%;
        width: 1px;
        background: rgb(229 231 235);
    }
}

.k-pager {
    .k-pager-info {
        order: 2;
        margin-left: -100px;
    }

    &.k-grid-pager.k-pager-md {
        flex-direction: row;
        justify-content: unset !important;
    }

    .k-pager-numbers-wrap {
        order: 3;
    }

    .k-pager-numbers-wrap:has(.k-pager-numbers) {
        .k-pager-numbers-wrap {
            order: unset;
        }
    }

    .k-pager-sizes {
        visibility: hidden;

        .k-picker {
            visibility: visible;
        }
    }
}

.course-pagination {
    .k-pager-info {
        margin-left: -90px;
    }

    .k-pager .k-pager-numbers-wrap {
        order: unset;
    }
}

.k-callout-s,
.k-callout-n {
    transform: translate(-50%, 0);
    bottom: -12px !important;
    left: 55% !important;
}

.k-callout-e,
.k-callout-w {
    transform: translate(0, 25%);
}

.k-callout {
    color: #374151 !important;
    display: block !important;
    &.k-callout-white {
        color: var(--color-gray-200) !important;
    }
}

.remove-last-child-border:last-child {
    box-shadow: none;
}

.k-grid .k-grid-header .k-table-th {
    text-transform: uppercase;
}

.custom-editor-style iframe {
    height: calc(100vh - 428px) !important;
}

.agent-custom-editor-style iframe {
    height: calc(100vh - 542px) !important;
}

.trainer-custom-editor-style iframe {
    height: calc(100vh - 571px) !important;
}

.feedback-custom-editor-style iframe {
    height: calc(100vh - 451px) !important;
}

// @media (max-width: 600px) {
//     .upload-custom-modal-wrapper {
//         width: 90% !important;
//     }
//     .custom-modal-wrapper,
//     .custom-modal-wrapper .k-window {
//         width: 90% !important;
//     }

//     .student-custom-modal-wrapper {
//         width: 95% !important;
//         left: 5% !important;
//     }

//     .custom-discard-modal {
//         width: 95% !important;
//         left: 50% !important;
//         transform: translateX(-50%);
//     }

//     .course-custom-modal-wrapper {
//         width: 95% !important;
//         left: 5% !important;
//     }
// }

// @media (min-width: 768px) {
//     .upload-custom-modal-wrapper {
//         width: 95% !important;
//         left: 5% !important;
//     }
//     .course-custom-modal-wrapper {
//         width: 95% !important;
//         left: 5% !important;
//     }
//     .custom-modal-wrapper {
//         width: 90% !important;
//     }
// }

// @media (min-width: 992px) {
//     .upload-custom-modal-wrapper {
//         width: 470px !important;
//     }
//     .course-custom-modal-wrapper {
//         width: 50% !important;
//         left: 50% !important;
//     }
//     .custom-modal-wrapper {
//         width: 50% !important;
//     }
// }

.k-grid .k-table-tbody > .k-table-row.k-grid-norecords:hover {
    background-color: transparent !important;
}

#attendanceScheduler {
    .k-event-actions {
        display: none;
    }
    .k-event.k-event-inverse {
        &:hover {
            z-index: 999;
        }
    }

    .k-button.k-more-events {
        margin-top: 1rem;
    }
}

// .timeDropDown {
//     .k-dropdown-wrap {
//         height: 1.875rem;
//         .k-input {
//             padding: 0;
//             // line-height: 1.125rem;
//         }
//         .k-select {
//             line-height: 1.875rem;
//         }
//     }
// }

.k-daterange-picker,
.k-daterangepicker {
    gap: 0.25rem;
    @media (screen(md)) {
        gap: 0.5rem;
    }
}

.k-calendar-view {
    min-height: fit-content;
    @media (screen(md)) {
        min-height: 224px;
    }
}

.blur-xs {
    filter: blur(1px);
}

@media (max-width: 600px) {
    .k-pager .k-pager-info {
        display: none !important;
    }
}

@media (min-width: 768px) {
    .k-pager .k-pager-info {
        display: block !important;
    }
}

.k-checkbox {
    &.k-checkbox-lg {
        width: 1.25rem;
        height: 1.25rem;
        font-size: 1.25rem;
    }
}

.custom-uploader {
    .k-upload-files.k-reset {
        margin-top: 0 !important;
    }
    table {
        tbody {
            tr {
                &:last-child {
                    td {
                        border-bottom: transparent;
                    }
                }
            }
        }
    }
}

.media-file-uploader {
    .k-upload {
        &.k-upload-async {
            padding-inline: 16px;
        }
    }
}

.k-input-md .k-input-inner {
    padding-inline: 0.75rem !important;
}

.custom-date-picker {
    .k-button {
        position: absolute;
        inset: 0;
        top: 0;
        display: flex;
        justify-content: flex-end;
        background: transparent;
        z-index: 1;
        width: 100%;
        .k-i-calendar {
            &::before {
                content: '';
                display: inline-block;
                width: 14px;
                height: 14px;
                background-image: url("data:image/svg+xml,%3Csvg width='12' height='12' viewBox='0 0 12 12' fill='none' xmlns='http://www.w3.org/2000/svg'%3E%3Cpath d='M3.24848 6.99696C3.66186 6.99696 3.99696 6.66186 3.99696 6.24848C3.99696 5.83511 3.66186 5.5 3.24848 5.5C2.83511 5.5 2.5 5.83511 2.5 6.24848C2.5 6.66186 2.83511 6.99696 3.24848 6.99696ZM3.99696 8.74848C3.99696 9.16186 3.66186 9.49696 3.24848 9.49696C2.83511 9.49696 2.5 9.16186 2.5 8.74848C2.5 8.33511 2.83511 8 3.24848 8C3.66186 8 3.99696 8.33511 3.99696 8.74848ZM6.00043 6.99696C6.41381 6.99696 6.74892 6.66186 6.74892 6.24848C6.74892 5.83511 6.41381 5.5 6.00043 5.5C5.58706 5.5 5.25195 5.83511 5.25195 6.24848C5.25195 6.66186 5.58706 6.99696 6.00043 6.99696ZM6.74892 8.74848C6.74892 9.16186 6.41381 9.49696 6.00043 9.49696C5.58706 9.49696 5.25195 9.16186 5.25195 8.74848C5.25195 8.33511 5.58706 8 6.00043 8C6.41381 8 6.74892 8.33511 6.74892 8.74848ZM8.74848 6.99696C9.16186 6.99696 9.49696 6.66186 9.49696 6.24848C9.49696 5.83511 9.16186 5.5 8.74848 5.5C8.33511 5.5 8 5.83511 8 6.24848C8 6.66186 8.33511 6.99696 8.74848 6.99696ZM12 2.5C12 1.11929 10.8807 0 9.5 0H2.5C1.11929 0 0 1.11929 0 2.5V9.5C0 10.8807 1.11929 12 2.5 12H9.5C10.8807 12 12 10.8807 12 9.5V2.5ZM1 4H11V9.5C11 10.3284 10.3284 11 9.5 11H2.5C1.67157 11 1 10.3284 1 9.5V4ZM2.5 1H9.5C10.3284 1 11 1.67157 11 2.5V3H1V2.5C1 1.67157 1.67157 1 2.5 1Z' fill='%239CA3AF'/%3E%3C/svg%3E%0A");
                background-size: cover;
                vertical-align: middle;
            }
        }
    }
}

.tw-old-kendo {
    .k-popup.k-list-container {
        padding: 0.25rem;
        width: auto !important;
        max-width: 22.5rem !important;
    }

    .k-list {
        padding: 0;
    }

    .k-dropdown-wrap .k-select,
    .k-numeric-wrap .k-select,
    .k-picker-wrap .k-select {
        position: relative;
        line-height: 1;
        display: flex;
        justify-content: center;
        align-items: center;
    }

    .k-dropdown-wrap,
    .k-numeric-wrap,
    .k-picker-wrap {
        padding-right: 0;
        align-items: center;
        height: 2.25rem;
    }

    .k-autocomplete .k-input,
    .k-dropdown-wrap .k-input,
    .k-multiselect-wrap .k-input,
    .k-numeric-wrap .k-input,
    .k-picker-wrap .k-input,
    .k-selectbox .k-input,
    .k-textbox > .k-input,
    .k-textbox > input {
        padding: 0;
    }

    .k-popup .k-list .k-state-hover {
        background-color: var(--color-gray-100);
        cursor: pointer;
        border-radius: 4px;
    }

    .k-list-filter .k-textbox + .k-icon {
        left: 4px;
    }
}

.k-splitbar-horizontal {
    z-index: 11;
}

.k-treeview-leaf-text {
    white-space: break-spaces;
}

.prismsfile-uploader .k-upload-files {
    margin-top: 0 !important;
    display: flex !important;
    flex-wrap: nowrap !important;
}

.k-list-optionlabel {
    pointer-events: none;
    background-color: transparent !important;
    color: var(--color-gray-400) !important;
    cursor: not-allowed !important;
}

.k-grid .k-command-cell > .k-button:hover {
    border-width: 1px;
    border-color: var(--color-gray-200);
    background-color: var(--color-gray-50);
    box-shadow: var(--tw-shadow);
    border-radius: 4px;
}

.k-column-list-item:focus,
.k-column-list-item.k-focus,
.k-columnmenu-item:focus,
.k-columnmenu-item.k-focus {
    box-shadow: none;
}

.k-columnmenu-item {
    padding-block: 0.5rem;
}

.k-grid .k-grid-header .k-header a {
    .k-i-more-vertical {
        color: var(--color-gray-600);
        &::before {
            content: '\e129';
        }
    }
}

.k-columnmenu-item > .k-icon.k-i-filter {
    display: none;
}

.k-column-list-item:focus,
.k-column-list-item.k-focus,
.k-columnmenu-item:focus,
.k-columnmenu-item.k-focus {
    box-shadow: none;
}

.k-grid .k-grid-header .k-header a {
    font-size: inherit !important;
    .k-i-more-vertical {
        color: var(--color-gray-600);
        &::before {
            content: '\e129';
        }
    }
}

.k-columnmenu-item > .k-icon.k-i-filter {
    display: none;
}

.k-grid-header .k-link .k-icon.k-i-sort-desc-sm {
    margin-top: 0;
}

.tw-sidebar-drawer .k-editor-content {
    max-height: 400px;
}

.k-dialog .k-dialog-titlebar {
    height: 4rem;
}

.k-label.k-text-error {
    & + .k-form-field-wrap {
        .k-dropdownlist.k-picker-solid:not(.k-focus) {
            border-color: var(--color-red-500);
        }
    }
}
.k-form-error {
    padding-block: 5px;
}

.k-grid-norecords {
    .k-table-td {
        border-bottom-width: 0 !important;
    }
}
.k-dialog-wrapper {
    @media (max-width: 991px) {
        padding-inline: 1.25rem;
    }
}

.k-d-grid {
    @media (max-width: 768px) {
        gap: 1rem !important;
    }
}
.k-grid-cols-2 .k-colspan-1 {
    @media (max-width: 768px) {
        grid-column: span 2 / span 2;
    }
}

.k-notification {
    padding: 0;
    .k-notification-success {
        background-color: var(--color-green-50);
    }

    .k-notification-error {
        background-color: var(--color-red-50);
    }
}

.k-table-md .k-column-title {
    width: 100%;
}

.k-animation-container.k-animation-container-shown {
    z-index: 1000 !important;
}
