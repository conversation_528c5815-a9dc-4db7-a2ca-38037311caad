// Compatible with @progress/kendo-theme-default v.7.0.0

$tb-kendo-is-dark-theme: false;
$tb-kendo-border-radius: 8px;
$tb-kendo-border-color: #d1d5db;
$tb-kendo-color-primary: #1890ff;
$tb-kendo-color-secondary: #6b7280;
$tb-kendo-color-info: #0058e9;
$tb-kendo-color-success: #10b981;
$tb-kendo-color-warning: #f97316;
$tb-kendo-color-error: #ef4444;
$tb-kendo-body-bg: #ffffff;
$tb-kendo-body-text: #6b7280;
$tb-kendo-heading-text: #374151;
$tb-kendo-subtle-text: #9ca3af;
$tb-kendo-disabled-text: #9ca3af;
$tb-kendo-base-bg: #fafafa;
$tb-kendo-hover-bg: #f0f0f0;
$tb-kendo-selected-text: #ffffff;
$tb-kendo-button-bg: #ffffffff;
$tb-kendo-button-text: #374151;
$tb-kendo-link-hover-text: #0050b3;
$tb-kendo-series-a: #ef4444;
$tb-kendo-series-b: #f97316;
$tb-kendo-series-c: #10b981;
$tb-kendo-series-d: #40a9ff;
$tb-kendo-series-e: #6366f1;
$tb-kendo-series-f: #aa46be;
$tb-kendo-component-bg: $tb-kendo-body-bg;
$tb-kendo-component-text: $tb-kendo-body-text;
$tb-kendo-base-text: $tb-kendo-body-text;
$tb-kendo-hover-text: $tb-kendo-base-text;
$tb-kendo-selected-bg: $tb-kendo-color-primary;
$tb-kendo-link-text: $tb-kendo-color-primary;
$tb-kendo-padding-input: 0.5rem;

$kendo-is-dark-theme: $tb-kendo-is-dark-theme;
$kendo-border-radius: $tb-kendo-border-radius;
$kendo-color-primary: $tb-kendo-color-primary;
$kendo-color-secondary: $tb-kendo-color-secondary;
$kendo-color-info: $tb-kendo-color-info;
$kendo-color-success: $tb-kendo-color-success;
$kendo-color-warning: $tb-kendo-color-warning;
$kendo-color-error: $tb-kendo-color-error;
$kendo-body-bg: $tb-kendo-body-bg;
$kendo-body-text: $tb-kendo-body-text;
$kendo-heading-text: $tb-kendo-heading-text;
$kendo-subtle-text: $tb-kendo-subtle-text;
$kendo-disabled-text: $tb-kendo-disabled-text;
$kendo-base-bg: $tb-kendo-base-bg;
$kendo-hover-bg: $tb-kendo-hover-bg;
$kendo-selected-text: $tb-kendo-selected-text;
$kendo-button-bg: $tb-kendo-button-bg;
$kendo-button-text: $tb-kendo-button-text;
$kendo-link-hover-text: $tb-kendo-link-hover-text;
$kendo-series-a: $tb-kendo-series-a;
$kendo-series-b: $tb-kendo-series-b;
$kendo-series-c: $tb-kendo-series-c;
$kendo-series-d: $tb-kendo-series-d;
$kendo-series-e: $tb-kendo-series-e;
$kendo-series-f: $tb-kendo-series-f;
$kendo-component-bg: $tb-kendo-component-bg;
$kendo-component-text: $tb-kendo-component-text;
$kendo-base-text: $tb-kendo-base-text;
$kendo-hover-text: $tb-kendo-hover-text;
$kendo-selected-bg: $tb-kendo-selected-bg;
$kendo-link-text: $tb-kendo-link-text;
$kendo-font-family: inherit;
$kendo-font-size: 14px;
$kendo-font-weight-normal: 400;
$kendo-line-height: 1.4285714286;
$kendo-padding-input: $tb-kendo-padding-input;
$kendo-border-color: $tb-kendo-border-color;

$enable-gradients: true;

$tb-typography: (
    kendo-default-typography: (
        font-family: 'inherit',
        font-size: 14px,
        font-weight: 400,
        line-height: 1.4285714286,
    ),
);

@mixin typography-classes($typography) {
    @each $selector, $property in $typography {
        &-#{$selector} {
            @each $propName, $propValue in $property {
                #{$propName}: #{$propValue};
            }
        }
        &-#{$selector}-important {
            @each $propName, $propValue in $property {
                #{$propName}: $propValue !important;
            }
        }
    }
}

$tb-effects: (
    tb-internal-none-effects: (
        box-shadow: (
            none,
        ),
        filter: (
            none,
        ),
        backdrop-filter: (
            none,
        ),
    ),
    kendo-shadow: (
        box-shadow: (
            0px 1px 2px 0px rgba(0, 0, 0, 0.06),
            0px 1px 3px 0px rgba(0, 0, 0, 0.1),
        ),
    ),
    kendo-shadow-md: (
        box-shadow: (
            0px 2px 4px -1px rgba(0, 0, 0, 0.06),
            0px 4px 6px -1px rgba(0, 0, 0, 0.1),
        ),
    ),
    kendo-shadow-lg: (
        box-shadow: (
            0px 4px 6px -2px rgba(0, 0, 0, 0.05),
            0px 10px 15px -3px rgba(0, 0, 0, 0.1),
        ),
    ),
    kendo-shodow-xl: (
        box-shadow: (
            0px 4px 6px -2px rgba(0, 0, 0, 0.05),
            0px 10px 15px -3px rgba(0, 0, 0, 0.1),
        ),
    ),
);

@mixin effects-classes($effects) {
    @each $selector, $property in $effects {
        &-#{$selector} {
            @each $propName, $propValue in $property {
                #{$propName}: $propValue;
            }
        }
        &-#{$selector}-important {
            @each $propName, $propValue in $property {
                #{$propName}: $propValue !important;
            }
        }
    }
}

// Timeline
$kendo-timeline-bg-flag: #1890ff;
$kendo-timeline-track-size: 2px;
$kendo-timeline-track-bg: var(--gray-200);
$kendo-timeline-flag-min-width: 110px;
$kendo-timeline-track-bottom-calc: 23px;
