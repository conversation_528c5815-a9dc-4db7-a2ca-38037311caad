#fileManager {
    .k-toolbar {
        &::before {
            position: absolute;
        }
        gap: 0.5rem;
        .k-button {
            text-transform: uppercase;
            padding: 0;
            font-size: 0.75rem;
        }
        .k-button-group {
            padding: 0.125rem 0.25rem;
            .k-button {
                padding: 0;
                &:is(:hover, :focus) {
                    background-color: transparent;
                    outline: none;
                    box-shadow: none;
                }
            }
        }

        .k-split-button {
            .k-button {
                border-top-right-radius: 0;
                border-bottom-right-radius: 0;
                &.k-focus,
                &.k-state-focus,
                &.k-state-focused,
                &:focus {
                    outline: none;
                    box-shadow: none;
                }
            }

            &.k-hover,
            &.k-state-hover,
            &.k-state-hovered,
            &.k-hovered {
                outline: none;
                box-shadow: none;
                background-color: var(--color-primary-blue-500);
                color: white;
            }
        }

        .k-filemanager-search-tool {
            flex-direction: row-reverse;
        }

        .k-filemanager-details-toggle {
            gap: 0.25rem;
            .k-switch {
                background-color: var(--color-gray-200);

                .k-switch-container {
                    .k-switch-handle {
                        background-color: white;
                    }
                }
            }
        }
    }

    .k-pager-wrap {
        position: absolute;
        bottom: 0;
        width: 100%;
        height: 54px;
        padding-inline: 1rem;
        &:focus {
            box-shadow: none;
        }
    }

    .k-filemanager-listview .k-file-preview {
        height: 64px;
    }

    .k-filemanager-listview .k-file-icon {
        font-size: 64px;
    }

    .k-listview-content {
        row-gap: 1.5rem;
        column-gap: 1rem;
    }

    .k-filemanager-listview .k-listview-item {
        padding-block: 16px;
        padding-inline: 16px;
        width: 142px;
        height: 142px;
        text-align: center;
        vertical-align: middle;
        display: flex;
        flex-direction: column;
        justify-content: center;
        align-items: center;
    }
    .k-toolbar .k-button.newfolder {
        display: inline;
        & + .k-button {
            display: inline;
        }
    }
}

.k-split-wrapper {
    .k-list-container {
        &.k-split-container {
            gap: 0.125rem;
            .k-button {
                &:is(:hover, :focus) {
                    outline: none;
                    box-shadow: none;
                    // background-color: var(--color-primary-blue-500);
                    // color: white;
                }
            }
        }
    }
}

#studentList .k-grid-pager {
    padding-inline-start: 1rem;

    .k-pager-info {
        margin-left: 0.5rem;
        display: inline;
    }

    .gridInfo .k-pager-sizes {
        height: 2rem;
    }
}

.tw-filemanager {
    &.k-filemanager {
        border-radius: 0.5rem;
        overflow: hidden;
    }
    .k-toolbar {
        padding: 1rem;
        gap: 0.5rem;
        &::before {
            position: absolute;
        }

        .k-split-button {
            display: inline-flex;
            height: 2.125rem;
            justify-content: center;
            align-items: center;
            gap: 0.5rem;
            border-radius: 0.5rem;
            border: 1px solid var(--color-gray-300);
            padding: 0.5rem 0.75rem;
            color: var(--color-gray-700);
            position: relative;
            background-color: white;

            .k-button {
                border: none;
                &:focus {
                    box-shadow: none;
                }
            }
        }

        .k-textbox.k-input.k-input-solid {
            padding-inline: 0.5rem;
        }

        .k-overflow-anchor.k-button {
            display: none;
        }

        .k-button-group {
            padding: 0.125rem;
            border-radius: 0.5rem;
            background-color: var(--color-gray-200);
            gap: 0.5rem;
            height: 2.125rem;
            .k-toggle-button {
                padding: 0.25rem;
                border-radius: 0.5rem;
                &.k-state-active {
                    background-color: white;
                }
                // &:is(:hover, :focus) {
                //     background-color: transparent;
                //     outline: none;
                //     box-shadow: none;
                // }
                .k-icon {
                    width: 1.25rem;
                    height: 1.25rem;
                }
            }

            .k-button {
                padding-inline: 0.25rem !important;

                &.k-icon-button {
                    background-color: transparent;
                    border-width: 0;
                    border-radius: 0.5rem;
                    &.k-selected {
                        background-color: white;
                        color: var(--color-primary-blue-500);
                        &:is(:focus) {
                            background-color: white !important;
                        }
                    }
                }
            }

            &.k-split-button {
                background-color: white;
            }
        }

        .k-filemanager-search-tool {
            flex-direction: row-reverse;
            padding-inline: 0.5rem;
        }

        .k-filemanager-navigation {
            display: none;
        }

        .k-switch {
            // width: 44px;
            // padding: 2px;
            // background-color: var(--color-gray-200);
            // border-radius: 100px;
            margin-left: 0.5rem;

            .k-switch-label-on,
            .k-switch-label-off {
                display: none;
            }

            .k-switch-container {
                width: 44px;
                height: 24px;
                padding: 2px;
                background-color: var(--color-gray-200);
                border-radius: 100px;
            }
            .k-switch-handle {
                width: 20px !important;
                height: 20px !important;
                background-color: white;
                position: absolute;
                border-radius: 100%;
                transform: 0;
            }
        }

        .k-input {
            min-width: 12rem !important;
        }

        .k-button {
            padding: 0.5rem;
        }
    }

    .k-filemanager-navigation,
    .k-filemanager-splitbar-navigation {
        display: none !important;
    }

    .k-grid .k-grid-header .k-header {
        text-align: left;
        font-weight: 400;
        padding-block: 0.625rem;
    }

    .k-grid-content table,
    .k-grid-content-locked > table,
    .k-grid-footer table,
    .k-grid-header table {
        border-inline-width: 0;
        width: 100%;
        border-radius: 0;
        &:focus {
            border: 0;
        }
    }

    .k-grid .k-grid-md .k-table-th > .k-cell-inner,
    .k-grid-md .k-table-th > .k-cell-inner {
        margin-block: -12px;
    }
    &__list-item {
        img {
            width: 64px;
            height: 64px;
        }
    }

    .k-filemanager-listview {
        .k-listview-content {
            gap: 1rem;
            padding: 1.5rem;
        }
        .k-listview-item {
            width: 150px;
            height: fit-content;
        }
    }
}

.k-list-container.k-split-container {
    .k-button {
        border: none;
        width: 100%;
        align-items: flex-start;
        justify-content: flex-start;
        padding: 0.25rem;
        border-radius: 0.5rem;
        &:is(:hover, :focus) {
            background-color: var(--color-gray-200);
        }
    }
}

#previewSlot .k-list-scroller {
    max-height: calc(100vh - 16rem);
}

.tw-media-manager {
    .k-combobox {
        .k-dropdown-wrap {
            display: none;
            // width: 100px;
        }
    }
    .k-spacer {
        display: none;
    }

    .k-button {
        border-color: var(--color-gray-300);
    }

    .k-pager-input {
        .k-textbox {
            width: 50px;
            padding-inline: 0.5rem;
        }
    }

    .k-textbox {
        padding: 0.25rem;
        height: 2rem;
    }

    .k-flat {
        gap: 0.5rem;
    }

    .k-button.k-button-icon {
        border-width: 0;
    }

    &__pagination {
        .k-pager {
            justify-content: space-between;
        }
        .k-pager-wrap {
            padding: 0;
            background-color: transparent;
        }
    }

    .k-button-md.k-icon-button {
        padding: 0.25rem;
    }

    &.tw-filemanager .k-toolbar .k-button {
        padding: 0.25rem;
    }
}

.tw-pager {
    .k-pager {
        justify-content: space-between;
        padding-inline: 0;
        border-width: 0;
    }
    .k-pager-wrap {
        padding: 0;
        background-color: transparent;
    }
}
