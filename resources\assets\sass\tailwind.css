@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
    html {
        font-family: 'Roboto', sans-serif;
    }
}

@layer components {
    .btn-primary,
    .tw-btn-primary,
    .k-filter-menu .k-action-buttons .k-button.k-primary,
    .k-dialog-buttongroup .k-button.k-primary,
    .k-form-buttons .k-button.k-primary,
    .k-wizard-buttons-right .k-button.k-primary,
    #fileManager .k-toolbar .k-button.newfolder + .k-button,
    .k-action-buttons .k-upload-selected.k-primary {
        @apply inline-flex h-[2.125rem] items-center justify-center gap-2 rounded-lg border border-primary-blue-500 bg-primary-blue-500 pl-3 pr-3 font-medium capitalize tracking-wide text-white shadow hover:bg-primary-blue-500 hover:shadow-lg focus:ring-2 focus:ring-primary-blue-500 focus:ring-offset-2 focus:ring-offset-white disabled:opacity-60;
    }

    .btn-secondary,
    .tw-btn-secondary,
    .k-filter-menu .k-action-buttons .k-button,
    .k-dialog-buttongroup .k-button,
    .k-form-buttons .k-button,
    .k-wizard-buttons-right .k-button,
    #fileManager .k-toolbar .k-button.newfolder,
    .k-filemanager-upload-dialog .k-upload .k-upload-button {
        @apply inline-flex h-[2.125rem] items-center justify-center gap-2 rounded-lg border border-gray-300 bg-white pl-3 pr-3 font-medium capitalize leading-[2.125rem] tracking-wide text-gray-700 hover:bg-gray-50 hover:shadow focus:ring-2 focus:ring-primary-blue-500 focus:ring-offset-2 focus:ring-offset-white disabled:opacity-60 disabled:hover:bg-white;
    }

    .btn-tertiary,
    .tw-btn-tertiary {
        @apply inline-flex h-[2.125rem] items-center justify-center gap-2 rounded-lg border-primary-blue-50 bg-primary-blue-50 pl-3 pr-3 font-medium capitalize leading-[2.125rem] tracking-wide text-primary-blue-500 hover:bg-primary-blue-100 focus:ring-2 focus:ring-primary-blue-500 focus:ring-offset-2 focus:ring-offset-white disabled:opacity-60 disabled:hover:bg-primary-blue-50;
    }

    .btn-text,
    .tw-btn-text {
        @apply inline-flex h-[2.125rem] items-center justify-center gap-2 font-medium capitalize leading-[2.125rem] tracking-wide text-primary-blue-500 hover:text-primary-blue-400 hover:underline;
    }

    .btn-danger,
    .k-dialog-buttongroup .k-button.btn-danger,
    .tw-btn-danger {
        @apply inline-flex h-[2.125rem] min-w-[6.25rem] items-center justify-center gap-2 rounded-lg border border-red-500 bg-red-500 pl-3 pr-3 font-medium capitalize leading-[2.125rem] tracking-wide text-white shadow hover:bg-red-500 hover:shadow-lg focus:ring-2 focus:ring-red-500 focus:ring-offset-2 focus:ring-offset-white;
    }

    .btn-ghost,
    .tw-btn-ghost {
        @apply inline-flex items-center justify-center gap-2 text-sm font-medium font-normal capitalize tracking-wide text-primary-blue-400 hover:text-primary-blue-500 hover:underline disabled:cursor-not-allowed disabled:text-primary-blue-300 disabled:no-underline;
    }

    .btn-icon {
        @apply pl-2 pr-2.5;
    }

    .input-radio {
        @apply float-left mr-2 mt-1 h-4 w-4 cursor-pointer appearance-none rounded-full border border-gray-300 bg-white bg-contain bg-center bg-no-repeat align-top transition duration-200 checked:border-blue-500 checked:bg-blue-600 focus:outline-none;
    }

    .tw-input-text {
        @apply flex h-9 w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-400 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-primary-blue-500 disabled:cursor-not-allowed disabled:opacity-50;
    }

    .tw-input-focused {
        @apply flex max-h-24 w-full overflow-auto rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm shadow-sm transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-primary-blue-500 disabled:cursor-not-allowed disabled:opacity-50;
    }

    .tw-input-borderless {
        @apply flex h-9 w-full rounded-lg border border-none bg-transparent px-3 py-2 text-sm !shadow-none transition-colors file:border-0 file:bg-transparent file:text-sm file:font-medium placeholder:text-gray-500 focus:shadow-none focus-visible:outline-none focus-visible:ring-1 focus-visible:ring-primary-blue-500 disabled:cursor-not-allowed disabled:opacity-50;
    }

    .card-wrapper,
    .tw-card-wrapper {
        @apply inline-flex h-full w-full flex-col items-start justify-start rounded-lg border border-gray-200 bg-white p-4 shadow-sm;
    }

    .btn-action,
    .tw-btn-action {
        @apply inline-flex h-6 w-6 max-w-7 cursor-pointer items-center justify-center rounded text-gray-400 hover:border hover:bg-white hover:text-gray-600 hover:shadow focus:bg-gray-50;
    }

    .tw-action-group {
        @apply min-w-24 max-w-28;
    }

    .tw-btn-primary.tw-btn-outline {
        @apply border-primary-blue-500 bg-transparent text-primary-blue-500;
    }

    .tw-btn-danger.tw-btn-outline {
        @apply border-red-500 bg-transparent text-red-500;
    }

    .btn-dropdown,
    .tw-btn-dropdown {
        @apply inline-flex h-[2.125rem] items-center justify-center gap-2 rounded-lg border border-gray-300 bg-white pl-2 pr-2 text-gray-700 hover:bg-gray-50 hover:shadow focus:border-primary-blue-500 focus:shadow-sm focus:shadow-primary-blue-300 disabled:opacity-60 disabled:hover:bg-white;
    }

    .tw-icon-btn,
    .tw-btn-icon {
        @apply inline-flex items-center justify-center rounded bg-transparent p-1 hover:bg-gray-200 disabled:cursor-not-allowed disabled:opacity-70;
    }

    .tw-revamp-table {
        @apply w-full text-left text-sm text-gray-500;
    }

    .tw-revamp-table__th {
        @apply truncate border border-gray-200 px-2 py-3 text-xs font-normal uppercase leading-4 text-gray-600;
    }

    .tw-revamp-table__td {
        @apply px-2 py-3;
    }

    .tw-btn-success {
        @apply inline-flex h-[2.125rem] items-center justify-center gap-2 rounded-lg border border-green-500 bg-green-500 pl-2 pr-2.5 font-medium tracking-wide text-white shadow hover:bg-green-500 hover:shadow-lg focus:ring-2 focus:ring-green-500 focus:ring-offset-2 focus:ring-offset-white disabled:opacity-60;
    }

    .tw-btn-warning {
        @apply inline-flex h-[2.125rem] items-center justify-center gap-2 rounded-lg border border-yellow-400 bg-yellow-400 pl-2 pr-2.5 font-medium tracking-wide text-gray-900 shadow hover:bg-yellow-500 hover:shadow-lg focus:ring-2 focus:ring-yellow-500 focus:ring-offset-2 focus:ring-offset-white disabled:opacity-60;
    }

    .tw-select {
        @apply block w-full rounded-lg border border-gray-300 bg-white p-2.5 text-sm text-gray-900 focus:border-primary-blue-500 focus:ring-primary-blue-500 focus:ring-offset-2 focus:ring-offset-white disabled:opacity-60;
    }

    .tw-btn-link {
        @apply inline-flex items-center font-medium tracking-wide text-primary-blue-500 hover:text-primary-blue-400 hover:underline;
    }
}
