var addCertificateFlag = true;
var subjectEnrollAndCertificateFlag = true;
var manageCertificateFlag = true;
var studEnrollSubjectGridId = '#enrollSubjectList';
var studCertificateIssueGridId = '#studentCertificateIssueList';
var deleteCertificateModalId = '#deleteStudentCertificateModal';

$('#enrollSubjectsModal').kendoWindow(defaultWindowSlideFormat('Enroll Unit', 80));
$('#generateCertificateModal').kendoWindow(defaultWindowSlideFormat('Generate Certificate', 80));
$('#generateCertificateBetaModal').kendoWindow(
    defaultWindowSlideFormat('Generate Certificate', 80)
);
$('#certificateRegisterModal').kendoWindow(defaultWindowSlideFormat('Certificate Register', 65));
$('#addCertificateModal').kendoWindow(openCenterWindow('Manually Document Update'));
$('#addUnitSubjectModal').kendoWindow(defaultWindowSlideFormat('Add Unit', 65));
$('#studentTrainingPlanModal').kendoWindow(defaultWindowSlideFormat('Training plan', 80));
$('#uploadStudentCertificateModal').kendoWindow(openCenterWindow('File Upload'));

addModalClassToWindows(['#addCertificateModal', '#uploadStudentCertificateModal']);

$(deleteCertificateModalId).kendoDialog({
    width: '400px',
    title: 'Delete Certificate',
    content:
        "Are you sure you want to delete this Certificate ? <input type='hidden' name='id' id='deleteStudCertificateId' />",
    actions: [
        { text: 'Close' },
        {
            text: 'Yes',
            primary: true,
            action: function () {
                deleteStudentCertificate(
                    $(deleteCertificateModalId).find('#deleteStudCertificateId').val()
                );
            },
        },
    ],
    animation: defaultOpenAnimation(),
    open: onOpenDeleteDialog(deleteCertificateModalId),
    visible: false,
});

function loadEnrollSubject() {
    $('#addCertificateForm')
        .html('')
        .kendoForm({
            orientation: 'vertical',
            formData: {
                date_generated: new Date(),
            },
            items: [
                {
                    field: 'certificate_type',
                    editor: 'DropDownList',
                    label: 'Type',
                    editorOptions: {
                        optionLabel: 'Select Type',
                        filter: 'contains',
                        dataSource: getDropdownDataSource('get-constant-data', {
                            action: 'certificateType',
                        }),
                        dataTextField: 'Name',
                        dataValueField: 'Id',
                    },
                    validation: { required: true },
                },
                {
                    field: 'date_generated',
                    editor: 'DatePicker',
                    label: 'Date Issued',
                    validation: { required: true },
                },
                {
                    field: 'manually_certificate_no',
                    label: 'Certificate No',
                    validation: { required: true },
                },
            ],
            buttonsTemplate: setModalFooterTemplate('Add Certificate No'),
            submit: function (ev) {
                ev.preventDefault();
                let dataArr = formValidateAndReturnFormData('#addCertificateForm');
                if (dataArr) {
                    ajaxActionV2(
                        'api/save-student-certificate-register',
                        'POST',
                        dataArr,
                        function (response) {
                            notificationDisplay(response.message, '', response.status);
                            if (response.status == 'success') {
                                closeKendoWindow('#addCertificateModal');
                                reloadGrid(studCertificateIssueGridId);
                            }
                        }
                    );
                }
                return false;
            },
        });

    $('#generateCertificateModal :input[type="checkbox"]').each(function (index) {
        let tempId = $(this).attr('id');
        $('#' + tempId).kendoSwitch({
            change: function (e) {
                let switchTextVal = e.checked ? 'Yes' : 'No';
                $('#' + tempId)
                    .closest('.customSwitchButton')
                    .siblings('div')
                    .find('.switchText')
                    .text(switchTextVal);
            },
        });
    });
}

function manageCertificateData() {
    if (manageCertificateFlag) {
        manageCertificateFlag = false;

        $(studCertificateIssueGridId).kendoGrid({
            dataSource: customDataSource(
                'api/get-certificate-issue-data',
                {
                    certificate_no: { type: 'string' },
                    certificate_name: { type: 'string' },
                    course_name: { type: 'string' },
                    generated_date: { type: 'string' },
                    generated_by: { type: 'string' },
                },
                selectedDataArr
            ),
            pageable: customPageableArr(),
            sortable: true,
            resizable: true,
            columns: [
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: certificate_no #</div>",
                    field: 'certificate_no',
                    title: 'CERTIFICATE NO',
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: certificate_name #</div>",
                    field: 'certificate_name',
                    title: 'CERTIFICATE NAME',
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: course_name #</div>",
                    field: 'course_name',
                    title: 'COURSE',
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: generated_date #</div>",
                    field: 'generated_date',
                    title: 'GENERATED DATE',
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: generated_by #</div>",
                    field: 'generated_by',
                    title: 'GENERATED BY',
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>sign</div>",
                    field: 'signatory',
                    title: 'SIGNATORY',
                },
                {
                    width: 80,
                    headerTemplate:
                        "<a class='k-link text-xs font-medium tracking-wide leading-none text-gray-500 uppercase' style='cursor: default !important;'>ACTION</a>",
                    field: 'action',
                    title: 'ACTION',
                    filterable: false,
                    sortable: false,
                    template: function (dataItem) {
                        return `<div class="action-div tw-action tw-action--autohide flex justify-start items-center space-x-1">
                                    <div class="action-only tw-btn-action inline-flex flex-col items-center justify-center w-6 h-6 p-0.5" data-id="${dataItem.id}">
                                        <span class="k-icon k-i-more-horizontal" style="color: #9CA3AF;"></span>
                                    </div>
                                </div>`;
                    },
                },
            ],
            noRecords: noRecordTemplate(),
            dataBound: function (e) {
                togglePagination(studCertificateIssueGridId);
            },
        });
        customGridHtml(studCertificateIssueGridId);
        //$('#studentCertificateIssueList tr td:nth-child(1)').hide();

        $(studCertificateIssueGridId).kendoTooltip({
            filter: 'td .action-only',
            position: 'bottom-left',
            showOn: 'click',
            content: function (e) {
                let dataItem = $(studCertificateIssueGridId)
                    .data('kendoGrid')
                    .dataItem(e.target.closest('tr'));
                return kendo.template($('#studCertificateActionTemplate').html())({
                    id: dataItem.id,
                    file_name: dataItem.is_file_name,
                    crNo: dataItem.manually_certificate_no,
                    crType: dataItem.certificate_type,
                    crName: dataItem.certificate_name,
                });
            },
            show: function (e) {
                manageTooltipPosition(e);
            },
        });
    } else {
        refreshGrid(studCertificateIssueGridId, selectedDataArr);
    }
}

function manageSubjectEnrollmentData() {
    if (subjectEnrollAndCertificateFlag) {
        subjectEnrollAndCertificateFlag = false;

        $(studEnrollSubjectGridId).kendoGrid({
            dataSource: customDataSource(
                'api/get-enroll-subject-data',
                {
                    unit_code: { type: 'string' },
                    unit_name: { type: 'string' },
                    batch: { type: 'string' },
                    activity_date: { type: 'string' },
                    created_at: { type: 'date' },
                },
                selectedDataArr
            ),
            pageable: customPageableArr(),
            sortable: true,
            resizable: true,
            columns: [
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: unit_code #</div>",
                    field: 'unit_code',
                    title: 'UNIT CODE',
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: unit_name #</div>",
                    field: 'unit_name',
                    title: 'UNIT NAME',
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: subject_attempt #</div>",
                    field: 'subject_attempt',
                    title: 'ATTEMPT',
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: batch #</div>",
                    field: 'batch',
                    title: 'BATCH',
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: convertJsDateFormat(created_at) #</div>",
                    field: 'created_at',
                    title: 'CREATED AT',
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: activity_date #</div>",
                    field: 'activity_date',
                    title: 'ACTIVITY DATE',
                },
            ],
            noRecords: noRecordTemplate(),
            dataBound: function (e) {
                togglePagination(studEnrollSubjectGridId);
            },
        });
        customGridHtml(studEnrollSubjectGridId);
        //$('#enrollSubjectList tr td:nth-child(1)').hide();
    } else {
        refreshGrid(studEnrollSubjectGridId, selectedDataArr);
    }
}

function setDropdownListForCourseTab(fieldID, apiURL, postArr = {}) {
    $('#' + fieldID).kendoDropDownList({
        autoWidth: true,
        dataTextField: 'Name',
        dataValueField: 'Id',
        filter: 'contains',
        dataSource: getDropdownDataSource(apiURL, postArr),
        dataBound: function (e) {
            this.select(0);
            this.trigger('change');
            this.trigger('select');
            onchangeSetDropdownListForCourse(
                fieldID,
                $('#' + fieldID)
                    .data('kendoDropDownList')
                    .value()
            );
        },
        select: function (e) {
            if (e.dataItem) {
                onchangeSetDropdownListForCourse(fieldID, e.dataItem.Id);
            }
        },
    });
}

function onchangeSetDropdownListForCourse(fieldID, value) {
    if (fieldID == 'subject_enroll_semester') {
        setDropdownListForCourseTab('subject_enroll_term', 'get-term-data', {
            semester_id: value,
        });
        setTimeout(function () {
            let primaryID = $('#subject_enroll_term').data('kendoDropDownList').value();
            if (primaryID) {
                ajaxActionV2(
                    'api/get-term-detail',
                    'POST',
                    { term: primaryID, semester_id: value },
                    function (response) {
                        $('#addUnitSubjectForm')
                            .find('#activity_start_date')
                            .data('kendoDatePicker')
                            .value(response.data.week_start);
                        $('#addUnitSubjectForm')
                            .find('#activity_finish_date')
                            .data('kendoDatePicker')
                            .value(response.data.week_finish);
                    }
                );
            }
        }, 1000);
    }
}
$('body').on('change', '#subject_enroll_term', function (e) {
    var primaryID = $('#subject_enroll_term').data('kendoDropDownList').value();
    const semesterId = $('#subject_enroll_semester').data('kendoDropDownList').value();
    ajaxActionV2(
        'api/get-term-detail',
        'POST',
        { term: primaryID, semester_id: semesterId },
        function (response) {
            $('#addUnitSubjectForm')
                .find('#activity_start_date')
                .data('kendoDatePicker')
                .value(response.data.week_start);
            $('#addUnitSubjectForm')
                .find('#activity_finish_date')
                .data('kendoDatePicker')
                .value(response.data.week_finish);
        }
    );
});

function deleteStudentCertificate(primaryID) {
    if (primaryID > 0) {
        ajaxActionV2(
            'api/delete-student-certificate',
            'POST',
            { id: primaryID },
            function (response) {
                notificationDisplay(response.message, '', response.status);
                reloadGrid(studCertificateIssueGridId);
            }
        );
    }
}

function setKendoCertificateUpload(studCertificateID) {
    $('#certificateFile')
        .html('')
        .kendoUpload({
            async: {
                chunkSize: 20000000, // bytes
                autoUpload: false,
                saveUrl: site_url + 'api/upload-student-certificate',
            },
            upload: function (e) {
                e.data = { student_certificate_id: studCertificateID };
            },
            multiple: false,
            validation: {
                allowedExtensions: ['.pdf'],
                maxFileSize: 20000000,
            },
            success: function (e) {
                notificationDisplay(e.response.message, '', e.response.status);
                if (e.response.status == 'success') {
                    closeKendoWindow('#uploadStudentCertificateModal');
                    refreshGrid2('#studentCertificateIssueList');
                    $('#certificateFile').data('kendoUpload').clearAllFiles();
                }
            },
            error: function () {
                $('#certificateFile').data('kendoUpload').clearAllFiles();
                notificationDisplay('Something will be wrong. Please try again.', '', 'error');
            },
        });
}

$('body').on('click', '.enrollSubjectBtn', function (e) {
    e.preventDefault();
    kendoWindowOpen('#enrollSubjectsModal');
    manageSubjectEnrollmentData();
});

// $('body').on('click', 'input[name=type_of_enrollment]:radio', function(e){
//     e.preventDefault();
//     $('.student_type_class').removeClass('border-primary-blue-500 bg-primary-blue-50 z-10');
//     if ($(this).val() == "subject") {
//         $(this).parent('label').addClass('border-primary-blue-500 bg-primary-blue-50 z-10');
//         $('.courseStageDiv').removeClass('hidden');
//         $('.selectSubjectDiv').removeClass('hidden');
//         $('.selectUnitDiv').addClass('hidden');
//     } else if ($(this).val() == "unit") {
//         $(this).parent('label').addClass('border-primary-blue-500 bg-primary-blue-50 z-10');
//         $('.courseStageDiv').addClass('hidden');
//         $('.selectSubjectDiv').addClass('hidden');
//         $('.selectUnitDiv').removeClass('hidden');
//     }
// });

$('body').on('change', 'input[name=type_of_enrollment]:radio', function () {
    $('.student_type_class').removeClass('border-primary-blue-500 bg-primary-blue-50 z-10');
    if ($(this).val() == 'subject') {
        $(this)
            .parent('.student_type_class')
            .addClass('border-primary-blue-500 bg-primary-blue-50 z-10');
        $('.courseStageDiv').removeClass('hidden');
        $('.selectSubjectDiv').removeClass('hidden');
        $('.selectUnitDiv').addClass('hidden');
    } else if ($(this).val() == 'unit') {
        $(this)
            .parent('.student_type_class')
            .addClass('border-primary-blue-500 bg-primary-blue-50 z-10');
        $('.courseStageDiv').addClass('hidden');
        $('.selectSubjectDiv').addClass('hidden');
        $('.selectUnitDiv').removeClass('hidden');
    }
});

$('body').on('click', '.EnrollSubjectsExcelExport', function (e) {
    e.preventDefault();
    gridExportExcelData('#enrollSubjectList', 'EnrollSubjectsExcelExport');
});

toggleFormDisableAttr('#addUnitSubjectForm', '.addUnitSubjectsBtn');

$('body').on('click', '.addUnitSubjectsBtn', function (e) {
    e.preventDefault();
    let dataArr = formValidateAndReturnFormData('#addUnitSubjectForm');
    if (dataArr) {
        dataArr.is_higher_ed = isHigherEd;
        ajaxActionV2('api/save-student-subject-enrollment', 'POST', dataArr, function (response) {
            notificationDisplay(response.message, '', response.status);
            if (response.status == 'success') {
                reloadGrid('#enrollSubjectList');
                closeKendoWindow('#addUnitSubjectModal');
            }
        });
    }
});

$('body').on('click', '.certificateRegisterBtn', function (e) {
    e.preventDefault();
    kendoWindowOpen('#certificateRegisterModal');
    manageCertificateData();
});

$('body').on('click', '.generateCertificateBtn', function (e) {
    e.preventDefault();
    ajaxActionV2('api/get-course-finish-date', 'POST', selectedDataArr, function (response) {
        $('.embed-content').hide();
        $('.download-certificate-btngroup').addClass('hidden');
        $('.enroll-certificate-preview-placeholder').show();
        $('.studentEnrollDocumentsPreview').attr('src', '');
        $('#generate_certificate_list').data('kendoDropDownList').value('');
        $('#generate_certificate_list').data('kendoDropDownList').enable(true);
        $('#subject_enrollment_id').val('');
        $('#completion_date').val(response.data);
        kendoWindowOpen('#generateCertificateModal');
        manageCertificateData();
    });
});

$('body').on('click', '.generateCertificateBetaBtn', function (e) {
    e.preventDefault();
    ajaxActionV2('api/get-course-finish-date', 'POST', selectedDataArr, function (response) {
        $('.embed-content').hide();
        $('.download-certificate-btngroup').addClass('hidden');
        $('.enroll-certificate-preview-placeholder').show();
        $('.studentEnrollDocumentsPreview').attr('src', '');
        $('#generate_certificate_list_beta').data('kendoDropDownList').value('');
        $('#generate_certificate_list_beta').data('kendoDropDownList').enable(true);
        $('#subject_enrollment_id').val('');
        $('#completion_date').val(response.data);
        kendoWindowOpen('#generateCertificateBetaModal');
        manageCertificateData();
    });
});

$('body').on('click', '.addCertificateBtn', function (e) {
    e.preventDefault();
    kendoWindowOpen('#addCertificateModal');
    if (addCertificateFlag) {
        loadEnrollSubject();
        addCertificateFlag = false;
    }
});

$('body').on('click', '.addUnitSubjectBtn', function (e) {
    e.preventDefault();
    ajaxActionV2('api/get-sub-units-for-enroll', 'POST', selectedDataArr, function (response) {
        let responseData = response.data;
        if ($('#selectUnit').data('kendoListBox') == undefined) {
            $('#selectUnit')
                .html('')
                .kendoListBox({
                    connectWith: 'selectedUnit',
                    draggable: true,
                    dropSources: ['selectedUnit'],
                    dataBound: onBoundselectedUnit,
                    toolbar: {
                        position: 'right',
                        tools: ['moveUp', 'moveDown', 'transferTo', 'transferFrom'],
                    },
                    selectable: 'multiple',
                    dataSource: {
                        data: responseData.enrolledUnit,
                    },
                    dataTextField: 'text',
                    dataValueField: 'value',
                    template: kendo.template($('#enrollSubjectOptionTemplate').html()),
                    add: dynamicCountUnit,
                    remove: dynamicCountUnit,
                });
        } else {
            $('#selectUnit').data('kendoListBox').setDataSource(responseData.enrolledUnit);
        }
        if ($('#selectedUnit').data('kendoListBox') == undefined) {
            $('#selectedUnit')
                .html('')
                .kendoListBox({
                    connectWith: 'selectUnit',
                    draggable: {
                        placeholder: function (element) {
                            return element.clone().css({
                                opacity: 0.3,
                                border: '1px dashed #000000',
                            });
                        },
                    },
                    dropSources: ['selectUnit'],
                    selectable: 'multiple',
                    template: kendo.template($('#enrollSubjectOptionTemplate').html()),
                    dataTextField: 'text',
                    dataValueField: 'value',
                    add: dynamicCountUnit,
                    remove: dynamicCountUnit,
                });
        } else {
            var selectedUnit = $('#selectedUnit').data('kendoListBox');
            selectedUnit.remove(selectedUnit.items());
            dynamicCountUnit();
        }
    });

    setDropdownListForCourseTab('subject_enroll_semester', 'get-enroll-subject-semester', {
        college_id: collegeId,
        student_course_id: selectedStudCourseID,
        start_date: $('#sub_enroll_course_start_date').val(),
    });
    $('#activity_start_date').kendoDatePicker({
        format: dateFormatFrontSideJS,
    });
    $('#activity_finish_date').kendoDatePicker({
        format: dateFormatFrontSideJS,
    });
    ajaxActionV2('api/get-enroll-venue', 'POST', selectedDataArr, function (response) {
        let responseData = response.data;

        setDropdownWithoutAPI('#venue_location', 'Name', 'Id', responseData.venueData);
        setDropdownWithoutAPI('#study_reason', 'Name', 'Id', responseData.studyReasonData);

        setDropdownWithoutAPI('#funding_source_state', 'Name', 'Id', responseData.arrFundingSource);

        setDropdownWithoutAPI(
            '#funding_source_state_nat',
            'Name',
            'Id',
            responseData.arrFundingSourceNat
        );
        setDropdownWithoutAPI(
            '#delivery_mode',
            'Name',
            'Id',
            responseData.arrDeliveryMode,
            responseData.defaultSelected.delivery_mode
        );
        setDropdownWithoutAPI(
            '#final_outcome',
            'Name',
            'Id',
            responseData.arrSelectFinalOutcomeNew,
            isHigherEd ? '' : 'CE'
        );
        setDropdownWithoutAPI('#mark_outcome', 'Name', 'Id', responseData.markOutcomeData);
        $('#funding_source_state').data('kendoDropDownList').list.width(500);
        // console.log(responseData.studentDetail.student_type);
        if (responseData.studentDetail.student_type != 'Domestic') {
            $('.outcome_identifier').hide().find('input').prop('required', false);
        } else {
            $('.outcome_identifier').show().find('input').prop('required', true);
        }
    });

    kendoWindowOpen('#addUnitSubjectModal');

    if (isHigherEd) {
        $('#addUnitSubjectModal').find('.markOutcomeDiv').show();
        $('#addUnitSubjectModal').find('.finalOutcomeDiv').hide();
    } else {
        $('#addUnitSubjectModal').find('.markOutcomeDiv').hide();
        $('#addUnitSubjectModal').find('.finalOutcomeDiv').show();
    }
});

$('.selectUnitSearch').on('input', function (e) {
    var listBox = $('#selectUnit').getKendoListBox();
    var sarchString = $(this).val();
    listBox.dataSource.filter({ field: 'searchColumn', operator: 'contains', value: sarchString });
});

function onBoundselectedUnit() {
    let dataSource = $('#selectUnit').data('kendoListBox').dataSource.data();
    if (dataSource.length > 0 && dataSource[0].value == '') {
        var listBox = $('#selectUnit').data('kendoListBox');
        listBox.enable($("[data-uid='" + listBox.dataSource.data()[0].uid + "']"), false);
    }
}
$('body').on('click', '.deleteCertificateBtn', function (e) {
    e.preventDefault();
    $(studCertificateIssueGridId).data('kendoTooltip').hide();
    let primaryID = $(this).attr('data-id');
    $(deleteCertificateModalId).data('kendoDialog').open();
    $(deleteCertificateModalId).find('#deleteStudCertificateId').val(primaryID);
});

$('body').on('click', '.downloadCertificateBtn', function (e) {
    e.preventDefault();
    $(studCertificateIssueGridId).data('kendoTooltip').hide();
    let primaryID = $(this).attr('data-id');
    if (primaryID > 0) {
        ajaxActionV2(
            'api/download-student-certificate',
            'POST',
            { id: primaryID },
            function (response) {
                if (response.status == 'success') {
                    const link = document.createElement('a');
                    console.log(response);
                    link.href = response.data; // example: "/download/files/student1/report.pdf"
                    link.download = ''; // Optional – lets browser infer filename
                    link.target = '_blank'; // Optional – opens in new tab if direct download fails
                    document.body.appendChild(link);
                    link.click();
                    document.body.removeChild(link);
                    // const link = document.createElement("a");
                    // link.setAttribute("href", response.data.path);
                    // link.setAttribute("download", response.data.file_name);
                    // link.click();
                } else {
                    notificationDisplay(response.message, '', response.status);
                }
            }
        );
    }
});

$('body').on('click', '.uploadCertificateBtn', function (e) {
    e.preventDefault();
    $(studCertificateIssueGridId).data('kendoTooltip').hide();
    let primaryID = $(this).attr('data-id');
    $('#uploadStudentCertificateModal')
        .find('.studentCertificateNo')
        .text($(this).attr('data-certificate-number'));
    $('#uploadStudentCertificateModal')
        .find('.studentCertificateType')
        .text($(this).attr('data-certificate-name'));
    kendoWindowOpen('#uploadStudentCertificateModal');
    setKendoCertificateUpload(primaryID);
});

$('body').on('click', '.certificateSendEmailBtn', function (e) {
    e.preventDefault();
    $(studCertificateIssueGridId).data('kendoTooltip').hide();
    let primaryID = $(this).attr('data-id');
    let sendMailWithCertificateModalId = '#miscellaneousSendEmailModal';
    kendoWindowOpen(sendMailWithCertificateModalId);
    $(sendMailWithCertificateModalId).find('.certificateDiv').show();
    $(sendMailWithCertificateModalId).find('.noCertificateDiv').hide();
    $(sendMailWithCertificateModalId).find('#studCertificateId').val(primaryID);
});
