var viewCourseVariantModalId = '#viewCourseVariantModal';
var addCourseVariantModalId = '#addCourseVariantModal';
var addCourseVariantFormId = '#addCourseVariantForm';
var editCourseVariantModalId = '#editCourseVariantModal';
var editCourseVariantFormId = '#editCourseVariantForm';
var courseVariantLogModalId = '#courseVariantLogModal';

$(viewCourseVariantModalId).kendoWindow(
    defaultWindowSlideFormat('Manage Student Course Variants', 60)
);
$(addCourseVariantModalId).kendoWindow(defaultWindowSlideFormat('Add Course Variant', 50));
$(editCourseVariantModalId).kendoWindow(defaultWindowSlideFormat('Edit Course Variant', 50));
$(courseVariantLogModalId).kendoWindow(defaultWindowSlideFormat('Course Variant Logs', 40));

// Pagination state
var courseVariantPagination = {
    currentPage: 1,
    perPage: 5, // Reduced from 15 to 5 for better UX in modal
    total: 0,
    lastPage: 1,
};

function loadStudentStatusCards(page = 1) {
    // Update current page
    courseVariantPagination.currentPage = page;

    // Show loading state (similar to GlobalContextLoader)
    showCourseVariantDataLoader(true);

    // Add pagination parameters to request
    let requestData = Object.assign({}, selectedDataArr, {
        page: page,
        per_page: courseVariantPagination.perPage,
    });

    ajaxActionV2('api/get-course-variant-list', 'POST', requestData, function (response) {
        let container = $('#studentStatusCardsContainer');
        container.empty();

        let resData = response.data;
        showCourseVariantDataLoader(false);

        if (resData && resData.total > 0) {
            // Update pagination state
            courseVariantPagination.total = resData.total;
            courseVariantPagination.lastPage = Math.ceil(
                resData.total / courseVariantPagination.perPage
            );

            $(document)
                .find(viewCourseVariantModalId)
                .find('.noDataCourseVariant')
                .addClass('hidden');
            $(document)
                .find(viewCourseVariantModalId)
                .find('.courseVariantDetails')
                .removeClass('hidden');

            // Render each status record as a card
            if (Array.isArray(resData.data)) {
                resData.data.forEach(function (item) {
                    container.append(kendo.template($('#studentStatusCardTemplate').html())(item));
                });
            }

            // Show and update pagination controls
            updatePaginationControls();
        } else {
            $(document)
                .find(viewCourseVariantModalId)
                .find('.noDataCourseVariant')
                .removeClass('hidden');
            $(document)
                .find(viewCourseVariantModalId)
                .find('.courseVariantDetails')
                .addClass('hidden');

            // Hide pagination controls
            $('#courseVariantPagination').addClass('hidden');
        }
    });
}

$('body').on('click', '.manageCourseVariantBtn', function (e) {
    e.preventDefault();
    ajaxActionV2('api/get-course-tab-data', 'POST', selectedDataArr, function (response) {
        let responseData = response.data;
        let responseArr = {
            data: responseData.courseSummary.currentCourseSummary,
            getResultCalculationMethod: responseData.courseSummary.getResultCalculationMethod,
            studentDetails: responseData.courseSummary.studentDetails,
        };
        let profile_pic = '';
        if (responseData.courseSummary.studentProfile == '') {
            let name = responseData.courseSummary.studentDetails[0].full_name
                .toUpperCase()
                .split(/\s+/);
            let shortName =
                name.length >= 2 ? name[0].charAt(0) + name[1].charAt(0) : name[0].substring(0, 2);
            profile_pic =
                '<div class="rounded-full"><div class="flex user-profile-pic w-16 h-16 rounded-full bg-blue-500 items-center"><span class="text-2xl flex justify-center items-center leading-6 px-1 w-full">' +
                shortName +
                '</span></div></div>';
        } else {
            profile_pic =
                '<div class="w-16 h-16 rounded-full"><img class="w-16 h-16 rounded-full" src="' +
                responseData.courseSummary.studentProfile +
                '"/></div>';
        }
        $('.studentProfilePic').html(profile_pic);
        $('.studentProfilePicName').html(responseData.courseSummary.studentDetails[0].full_name);
        $('.studentStudId').html(responseData.courseSummary.studentDetails[0].generated_stud_id);
        $('#viewExitInterviewModal_wnd_title').text(
            'Exit Interview for ' + responseData.courseSummary.studentDetails[0].full_name
        );
        $(document)
            .find('.studentCourseDetailHeader')
            .html(kendo.template($('#studentCourseDetailHeaderTemplate').html())(responseArr));
    });
    // Load student status cards instead of grid
    loadStudentStatusCards();

    kendoWindowOpen(viewCourseVariantModalId);
});
$('body').on('click', '.addCourseVariantBtn', function (e) {
    e.preventDefault();
    ajaxActionV2(
        'api/student-course-detail',
        'POST',
        selectedDataArr,
        function (courseDetailResponse) {
            showHeaderPartForAddCourseVariantModal(courseDetailResponse.data[0]);

            let addCourseVariantForm = InitializeAddCourseVariantForm();
            addCourseVariantForm.data('kendoForm').setOptions({
                formData: {
                    course_status: '', // Set default type here
                    original_course_end_date: courseDetailResponse.data[0].finish_date,
                },
            });

            // Clear edit mode flag and course variant ID for add form
            window.isEditMode = false;
            window.currentCourseVariantId = null;

            // Initialize with all fields hidden (only course status dropdown visible)
            toggleFieldsByType(null); //'deferred'

            $(document).find('.k-form-field-wrap').addClass('customSwitchButton');
            $(document).find('span.tw-switch-group').parent().parent().addClass('tw-switch-field');

            // Initialize switch-dependent fields on form load
            setTimeout(() => {
                initializeSwitchDependentFields();
            }, 200);
        }
    );

    kendoWindowOpen(addCourseVariantModalId);
    toggleFormDisableAttr(addCourseVariantFormId, '.add-course-variant-submit');
});
$('body').on('click', '.editCourseVariantBtn', function (e) {
    e.preventDefault();

    // Get the course variant ID from the button
    let primaryID = $(this).attr('data-id');
    selectedDataArr.id = primaryID;

    // Store the ID globally for use in form submission
    window.currentCourseVariantId = primaryID;

    ajaxActionV2(
        'api/student-course-detail',
        'POST',
        selectedDataArr,
        function (courseDetailResponse) {
            showHeaderPartForEditCourseVariantModal(courseDetailResponse.data[0]);

            window.headerDetail.headerDropDownCourseData.shift();

            ajaxActionV2(
                'api/get-course-variant-detail',
                'POST',
                selectedDataArr,
                function (courseVariantResponse) {
                    let editCourseVariantForm = InitializeModifyCourseVariantForm();
                    let courseVariantData = courseVariantResponse.data;

                    // Prepare form data with all the course variant data
                    let formData = {
                        ...courseVariantData,
                        original_course_end_date: courseDetailResponse.data[0].finish_date,
                    };

                    // Set the form data
                    editCourseVariantForm.data('kendoForm').setOptions({
                        formData: formData,
                    });

                    $(document).find('.k-form-field-wrap').addClass('customSwitchButton');
                    $(document)
                        .find('span.tw-switch-group')
                        .parent()
                        .parent()
                        .addClass('tw-switch-field');

                    // Set edit mode flag to prevent switch reset
                    window.isEditMode = true;

                    // Show fields based on the loaded course status and populate switches
                    setTimeout(() => {
                        populateEditFormWithData(courseVariantData);
                    }, 300);

                    kendoWindowOpen(editCourseVariantModalId);
                }
            );
        }.bind(this)
    );
});
$('body').on('click', '.printCourseVariantBtn', function (e) {
    e.preventDefault();
    let primaryID = $(this).attr('data-id');
    window.location = site_url + 'api/print-course-variant/' + primaryID;
});
$('body').on('click', '.courseVariantLog', function (e) {
    e.preventDefault();
    let currentPage = $(this).attr('data-page');
    let apiDataArr = {
        student_course_id: selectedStudCourseID,
        page: currentPage,
    };

    kendoWindowOpen(courseVariantLogModalId);
    ajaxActionV2('api/get-course-variant-log-data', 'POST', apiDataArr, function (response) {
        //console.log(response.data);
        //console.log(response.data.data);
        //console.log(response.data.length);

        if (response.data && response.data.data && response.data.data.length > 0) {
            //
            let syncLogsTemplate = kendo.template($(document).find('#syncLogsTemplate').html())(
                response.data
            );

            $(courseVariantLogModalId).find('#courseVariantLogBlankDiv').hide();
            $(courseVariantLogModalId).find('#courseVariantLogInfo').show();
            $(courseVariantLogModalId).find('#courseVariantLogInfo').html(syncLogsTemplate);
            $(courseVariantLogModalId)
                .find('#cvLogTabPage')
                .val(parseInt(currentPage) + 1);
        } else {
            $(courseVariantLogModalId).find('#courseVariantLogBlankDiv').show();
            $(courseVariantLogModalId).find('#courseVariantLogInfo').hide();
        }
    });
});

// Pagination event handlers
$('body').on('click', '#prevPageBtn', function (e) {
    e.preventDefault();
    if (courseVariantPagination.currentPage > 1) {
        loadStudentStatusCards(courseVariantPagination.currentPage - 1);
    }
});
$('body').on('click', '#nextPageBtn', function (e) {
    e.preventDefault();
    if (courseVariantPagination.currentPage < courseVariantPagination.lastPage) {
        loadStudentStatusCards(courseVariantPagination.currentPage + 1);
    }
});
$('body').on('click', '.page-btn', function (e) {
    e.preventDefault();
    let page = parseInt($(this).attr('data-page'));
    if (page !== courseVariantPagination.currentPage) {
        loadStudentStatusCards(page);
    }
});

$(courseVariantLogModalId)
    .find('#courseVariantLogInfo')
    .on('scroll', function () {
        var scrollTop = this.scrollTop;
        var scrollHeight = this.scrollHeight;
        var divHeight = $(this).height();
        let currentPage = $(courseVariantLogModalId).find('#cvLogTabPage').val();
        let apiDataArr = {
            student_course_id: selectedStudCourseID,
            page: currentPage,
        };

        if (scrollTop + divHeight + 1 > scrollHeight) {
            ajaxActionV2(
                'api/get-course-variant-log-data',
                'POST',
                apiDataArr,
                function (response) {
                    if (response.data && response.data.data && response.data.data.length > 0) {
                        let syncLogsTemplate = kendo.template($('#syncLogsTemplate').html())(
                            response.data
                        );
                        $(courseVariantLogModalId)
                            .find('#courseVariantLogInfo')
                            .append(syncLogsTemplate);
                        $(courseVariantLogModalId)
                            .find('#cvLogTabPage')
                            .val(parseInt(currentPage) + 1);
                    }
                }
            );
        }
    });

function showHeaderPartForAddCourseVariantModal(responseData) {
    $(document)
        .find('#studentCourseStatusHtml')
        .html(kendo.template($('#studentCourseStatusTemplate').html())({ arr: responseData }));
}

function showHeaderPartForEditCourseVariantModal(responseData) {
    $(document)
        .find(editCourseVariantModalId)
        .find('#studentCourseStatusHtml')
        .html(
            kendo.template($('#studentCourseStatusTemplate').html())({
                arr: responseData,
            })
        );
}

function showCourseVariantDataLoader(show) {
    let loader = $('#courseVariantDataLoader');
    let container = $('#studentStatusCardsContainer');

    if (show) {
        loader.removeClass('hidden');
        container.addClass('hidden');
    } else {
        loader.addClass('hidden');
        container.removeClass('hidden');
    }
}

function updatePaginationControls() {
    let pagination = courseVariantPagination;
    let paginationContainer = $('#courseVariantPagination');

    // Show pagination if more than one page
    if (pagination.lastPage > 1) {
        paginationContainer.removeClass('hidden');

        // Update pagination info
        let startItem = (pagination.currentPage - 1) * pagination.perPage + 1;
        let endItem = Math.min(pagination.currentPage * pagination.perPage, pagination.total);
        $('#paginationInfo').text(
            `Showing ${startItem} to ${endItem} of ${pagination.total} results`
        );

        // Update previous button
        let prevBtn = $('#prevPageBtn');
        if (pagination.currentPage <= 1) {
            prevBtn.prop('disabled', true).addClass('opacity-50 cursor-not-allowed');
        } else {
            prevBtn.prop('disabled', false).removeClass('opacity-50 cursor-not-allowed');
        }

        // Update next button
        let nextBtn = $('#nextPageBtn');
        if (pagination.currentPage >= pagination.lastPage) {
            nextBtn.prop('disabled', true).addClass('opacity-50 cursor-not-allowed');
        } else {
            nextBtn.prop('disabled', false).removeClass('opacity-50 cursor-not-allowed');
        }

        // Generate page numbers
        generatePageNumbers();
    } else {
        paginationContainer.addClass('hidden');
    }
}

function generatePageNumbers() {
    let pagination = courseVariantPagination;
    let pageNumbersContainer = $('#pageNumbers');
    pageNumbersContainer.empty();

    // Calculate which pages to show
    let startPage = Math.max(1, pagination.currentPage - 2);
    let endPage = Math.min(pagination.lastPage, pagination.currentPage + 2);

    // Adjust if we're near the beginning or end
    if (endPage - startPage < 4) {
        if (startPage === 1) {
            endPage = Math.min(pagination.lastPage, startPage + 4);
        } else if (endPage === pagination.lastPage) {
            startPage = Math.max(1, endPage - 4);
        }
    }

    // Add first page and ellipsis if needed
    if (startPage > 1) {
        pageNumbersContainer.append(createPageButton(1));
        if (startPage > 2) {
            pageNumbersContainer.append('<span class="px-2 text-gray-500">...</span>');
        }
    }

    // Add page numbers
    for (let i = startPage; i <= endPage; i++) {
        pageNumbersContainer.append(createPageButton(i));
    }

    // Add last page and ellipsis if needed
    if (endPage < pagination.lastPage) {
        if (endPage < pagination.lastPage - 1) {
            pageNumbersContainer.append('<span class="px-2 text-gray-500">...</span>');
        }
        pageNumbersContainer.append(createPageButton(pagination.lastPage));
    }
}

function createPageButton(pageNumber) {
    let isActive = pageNumber === courseVariantPagination.currentPage;
    let buttonClass = isActive
        ? 'px-3 py-1 text-sm bg-blue-500 text-white border border-blue-500 rounded-md'
        : 'px-3 py-1 text-sm bg-white text-gray-700 border border-gray-300 rounded-md hover:bg-gray-50';

    return `<button class="page-btn ${buttonClass}" data-page="${pageNumber}">${pageNumber}</button>`;
}

function InitializeAddCourseVariantForm() {
    return $(addCourseVariantFormId)
        .html('')
        .kendoForm({
            validatable: defaultErrorTemplate(),
            orientation: 'vertical',
            layout: 'grid',
            type: 'group',
            grid: { cols: 6, gutter: 16 },
            items: getAllFormFields(),
            //validateHiddenFields: false,
            buttonsTemplate: setWindowFooterTemplate(
                'SAVE',
                'submit',
                'add-course-variant-submit',
                false
            ),
            submit: function (ev) {
                ev.preventDefault();
                saveCourseVariantForm();
                return false;
            },
        });
}

function InitializeModifyCourseVariantForm() {
    return $(editCourseVariantFormId)
        .html('')
        .kendoForm({
            validatable: defaultErrorTemplate(),
            orientation: 'vertical',
            layout: 'grid',
            type: 'group',
            grid: { cols: 6, gutter: 16 },
            items: getAllFormFields(),
            //validateHiddenFields: false,
            buttonsTemplate: setWindowFooterTemplate(
                'UPDATE',
                'Update',
                'edit-course-variant-submit',
                false
            ),
            submit: function (ev) {
                ev.preventDefault();
                updateCourseVariantForm();
                return false;
            },
        });
}

// Combined form schema with all fields - show/hide based on status
function getAllFormFields() {
    return [
        {
            field: 'course_status',
            label: 'Course Status',
            editor: 'DropDownList',
            colSpan: 6,
            editorOptions: {
                dataTextField: 'Name',
                dataValueField: 'Id',
                optionLabel: 'Select Course Status',
                dataSource: [
                    { Id: 'Deferred', Name: 'Deferred' },
                    { Id: 'Suspended', Name: 'Suspended' },
                    { Id: 'Withdrawn', Name: 'Withdrawn' },
                    { Id: 'Cancelled', Name: 'Cancelled' },
                ],
                change: function () {
                    const selectedType = this.value();
                    // If user manually changes status in edit mode, clear edit mode to allow reset
                    if (window.isEditMode) {
                        window.isEditMode = false;
                    }
                    toggleFieldsByType(selectedType);
                },
            },
            validation: { required: true },
        },

        // Cancellation specific fields
        {
            field: 'reason_for_cancellation',
            label: 'Reason for Cancellation',
            editor: 'DropDownList',
            colSpan: 3,
            editorOptions: {
                dataTextField: 'Name',
                dataValueField: 'Id',
                optionLabel: 'Select Reason',
                dataSource: arrCourseCancelReason,
            },
            //validation: { required: { message: 'Please select a reason for cancellation' } },
        },
        {
            field: 'cancellation_comment',
            label: 'Comment',
            editor: 'TextArea',
            colSpan: 6,
            editorOptions: {
                placeholder: 'Provide further details...',
                rows: 4,
            },
            //validation: { required: { message: 'Please enter a comment for cancellation' } },
        },
        {
            field: 'is_notice_period_completed',
            editor: 'Switch',
            label: 'Has the 20-Day Notice Period Been Completed?',
            colSpan: 3,
            //validation: { required: false },
        },
        {
            field: 'is_appeal_internally',
            editor: 'Switch',
            label: 'Did the Student Choose to Appeal Internally?',
            colSpan: 3,
            //validation: { required: false },
        },
        {
            field: 'last_attended_date',
            editor: 'DatePicker',
            label: 'Last Date Student Attended Class',
            attributes: { placeholder: 'DD-MM-YYYY' },
            editorOptions: {
                format: dateFormatFrontSideJS,
            },
            colSpan: 3,
            //validation: { required: { message: 'Please select the last attended date' } },
        },
        {
            field: 'cancellation_date',
            editor: 'DatePicker',
            label: 'Cancellation Date',
            attributes: { placeholder: 'DD-MM-YYYY' },
            editorOptions: {
                format: dateFormatFrontSideJS,
            },
            colSpan: 3,
            //validation: { required: { message: 'Please select the cancellation date' } },
        },

        // Withdrawal specific fields
        {
            field: 'withdrawal_date',
            editor: 'DatePicker',
            label: 'Withdrawal Date',
            attributes: { placeholder: 'DD-MM-YYYY' },
            editorOptions: {
                format: dateFormatFrontSideJS,
            },
            colSpan: 3,
            //validation: { required: { message: 'Please select the withdrawal date' } },
        },
        {
            field: 'is_approve',
            editor: 'Switch',
            label: 'Has the Withdrawal/Cancellation Been Approved?',
            editorOptions: {
                change: createSwitchChangeHandler(['approved_by', 'approved_date']),
            },
            colSpan: 6,
            //validation: { required: false },
        },
        {
            field: 'approved_by',
            editor: 'TextBox',
            label: 'Approved By (User)',
            editorOptions: {
                placeholder: "Enter approver's name",
            },
            colSpan: 3,
            //validation: { required: { message: 'Please enter the approver name' } },
        },
        {
            field: 'approved_date',
            editor: 'DatePicker',
            label: 'Date Approved',
            attributes: { placeholder: 'DD-MM-YYYY' },
            editorOptions: {
                format: dateFormatFrontSideJS,
            },
            colSpan: 3,
            //validation: { required: { message: 'Please select the approval date' } },
        },
        {
            field: 'is_student_release',
            editor: 'Switch',
            label: 'Does the Student Require a Release?',
            editorOptions: {
                change: createSwitchChangeHandler(['is_study_completed', 'is_granting_release']),
            },
            colSpan: 6,
            //validation: { required: false },
        },
        {
            field: 'is_study_completed',
            editor: 'Switch',
            label: 'Has the Student Completed 6 Months of Study With Us?',
            editorOptions: {
                change: createSwitchChangeHandler(['is_granting_release'], 'hide'),
            },
            colSpan: 6,
            //validation: { required: false },
        },
        {
            field: 'is_granting_release',
            editor: 'Switch',
            label: 'Are You Granting Release?',
            editorOptions: {
                change: createSwitchChangeHandler(['release_approved_by', 'release_comment']),
            },
            colSpan: 6,
            //validation: { required: false },
        },
        {
            field: 'release_approved_by',
            label: 'Release Approved By',
            editor: 'TextBox',
            editorOptions: {
                placeholder: "Enter approver's name",
            },
            colSpan: 3,
            //validation: { required: { message: 'Please enter the release approver name' } },
        },
        {
            field: 'release_comment',
            label: 'Comment Why Release Was Provided',
            editor: 'TextArea',
            colSpan: 6,
            editorOptions: {
                placeholder: 'Enter reason for granting release',
                rows: 4,
            },
            //validation: { required: { message: 'Please enter reason for granting release' } },
        },

        // Defer/Suspend specific fields
        {
            field: 'defer_from_date',
            editor: 'DatePicker',
            label: 'Defer/Suspension From Date',
            attributes: { placeholder: 'DD-MM-YYYY' },
            editorOptions: {
                format: dateFormatFrontSideJS,
                change: function () {
                    let startDate = new Date(this.value());
                    let endDate = $('#defer_to_date').data('kendoDatePicker').value();
                    if (startDate && endDate && startDate > endDate) {
                        notificationDisplay('From Date must be earlier than To Date', '', 'error');
                        $('#defer_from_date').data('kendoDatePicker').value('');
                    }
                },
            },
            colSpan: 3,
            //validation: { required: { message: 'Please select the defer/suspension from date' } },
        },
        {
            field: 'defer_to_date',
            editor: 'DatePicker',
            label: 'Defer/Suspension To Date',
            attributes: { placeholder: 'DD-MM-YYYY' },
            editorOptions: {
                format: dateFormatFrontSideJS,
                change: function () {
                    let startDate = $('#defer_from_date').data('kendoDatePicker').value();
                    let endDate = new Date(this.value());
                    if (startDate && endDate && startDate > endDate) {
                        notificationDisplay(
                            'To Date must be the same as or later than From Date',
                            '',
                            'error'
                        );
                        $('#defer_to_date').data('kendoDatePicker').value('');
                    }
                },
            },
            colSpan: 3,
            //validation: { required: { message: 'Please select the defer/suspension to date' } },
        },
        {
            field: 'original_course_end_date',
            editor: 'DatePicker',
            label: 'Original Course End Date',
            attributes: { placeholder: 'DD-MM-YYYY', disabled: true },
            editorOptions: {
                format: dateFormatFrontSideJS,
            },
            colSpan: 2,
            //validation: { required: { message: 'Please select the original course end date' } },
        },
        {
            field: 'is_course_end_date_impact',
            editor: 'Switch',
            label: 'Is Course End Date Impacted?',
            editorOptions: {
                change: createSwitchChangeHandler(['new_course_end_date']),
            },
            colSpan: 2,
            //validation: { required: false },
        },
        {
            field: 'new_course_end_date',
            editor: 'DatePicker',
            label: 'New Course End Date',
            attributes: { placeholder: 'DD-MM-YYYY' },
            editorOptions: {
                format: dateFormatFrontSideJS,
                change: function () {
                    let originalEndDate = $('#original_course_end_date')
                        .data('kendoDatePicker')
                        .value();
                    let newEndDate = new Date(this.value());

                    if (originalEndDate && newEndDate && newEndDate <= originalEndDate) {
                        notificationDisplay(
                            'New Course End Date must be greater than Original Course End Date',
                            '',
                            'error'
                        );
                        $('#new_course_end_date').data('kendoDatePicker').value('');
                    }
                },
            },
            colSpan: 2,
            /*validation: {
                custom: function (input) {
                    if (input.is("[name='new_course_end_date']")) {
                        let isCourseEndDateImpact = $('#is_course_end_date_impact').is(':checked');
                        if (isCourseEndDateImpact && (!input.val() || input.val() === '')) {
                            input.attr('data-custom-msg', 'New Course End Date is required when Course End Date is impacted.');
                            return false;
                        }
                    }
                    return true;
                }
            },*/
        },
        {
            field: 'student_location',
            label: 'Student Location During Suspension',
            editor: 'DropDownList',
            colSpan: 3,
            editorOptions: {
                dataTextField: 'Name',
                dataValueField: 'Id',
                optionLabel: 'Select Student Location',
                dataSource: arrStudentOrigin,
                /*dataSource: [
                    { Id: 'in_australia', Name: 'In Australia' },
                    { Id: 'offshore', Name: 'Offshore' },
                ],*/
            },
            //validation: { required: { message: 'Please select student location during suspension' } },
        },
        {
            field: 'next_class_start_date',
            editor: 'DatePicker',
            label: 'Student Next Class Start Date:',
            attributes: { placeholder: 'DD-MM-YYYY' },
            editorOptions: {
                format: dateFormatFrontSideJS,
            },
            colSpan: 3,
            //validation: { required: { message: 'Please select the next class start date' } },
        },

        {
            field: 'new_coe_file',
            label: 'Upload New CoE Document (PDF)',
            editor: function (container, options) {
                $('<input name="' + options.field + '" type="file" accept=".pdf"/>').appendTo(
                    container
                );
            },
            hint: 'Optional: Upload a new CoE to replace the current one.',
            colSpan: 3,
            /*validation: {
                custom: function (input) {
                    if (input.is("[name='new_coe_file']")) {
                        let coeFile = input.val();
                        let coeCode = $('#new_coe_code').val();

                        // Either file or code must be provided, but not both required
                        if (!coeFile && !coeCode) {
                            input.attr(
                                'data-custom-msg',
                                'Please either upload a CoE document or enter a CoE code'
                            );
                            return false;
                        }

                        // If file is provided, validate file type
                        if (coeFile && !coeFile.toLowerCase().endsWith('.pdf')) {
                            input.attr('data-custom-msg', 'Please upload a PDF file only');
                            return false;
                        }

                        return true;
                    }
                    return true;
                },
            },*/
        },
        {
            field: 'new_coe_code',
            label: 'Or Enter New CoE Code',
            editor: 'TextBox',
            editorOptions: {
                placeholder: 'Enter new COE code',
            },
            hint: 'Either upload the new CoE or manually enter the CoE code.',
            colSpan: 3,
            /*validation: {
                custom: function (input) {
                    if (input.is("[name='new_coe_code']")) {
                        let coeCode = input.val();
                        let coeFile = $('#new_coe_file').val();

                        // Either file or code must be provided
                        if (!coeCode && !coeFile) {
                            input.attr(
                                'data-custom-msg',
                                'Please either enter a CoE code or upload a CoE document'
                            );
                            return false;
                        }

                        // If code is provided, validate format (basic validation)
                        if (coeCode && coeCode.length < 3) {
                            input.attr(
                                'data-custom-msg',
                                'CoE code must be at least 3 characters long'
                            );
                            return false;
                        }

                        return true;
                    }
                    return true;
                },
            },*/
        },
        {
            field: 'approval_evidence_file',
            label: 'Upload Student Approval Evidence (PDF)',
            editor: function (container, options) {
                $('<input name="' + options.field + '" type="file" accept=".pdf"/>').appendTo(
                    container
                );
            },
            hint: 'Optional: Upload a new Evidence to replace the current one.',
            colSpan: 3,
            /*validation: {
                custom: function (input) {
                    if (input.is("[name='approval_evidence_file']")) {
                        let file = input.val();

                        // File validation only if provided
                        if (file && !file.toLowerCase().endsWith('.pdf')) {
                            input.attr('data-custom-msg', 'Please upload a PDF file only');
                            return false;
                        }

                        return true;
                    }
                    return true;
                },
            },*/
        },
    ];
}

// Function to show/hide fields based on selected status
function toggleFieldsByType(selectedType) {
    // Hide all type-specific fields first
    hideAllTypeSpecificFields();

    // If no type selected or null, keep all fields hidden
    if (!selectedType || selectedType === '' || selectedType === null) {
        return;
    }

    // Show fields based on selected Status
    switch (selectedType) {
        case 'Deferred':
        case 'Suspended':
            showDeferSuspendFields();
            break;
        case 'Withdrawn':
            showWithdrawalFields();
            break;
        case 'Cancelled':
            showCancellationFields();
            break;
    }

    // Initialize switch-dependent field visibility after showing type-specific fields
    // Use setTimeout to ensure switches are reset and DOM elements are rendered before checking switch states
    setTimeout(() => {
        initializeSwitchDependentFields();
    }, 200);
}

function hideAllTypeSpecificFields() {
    let fieldsToHide = [
        // Defer/Suspend fields
        'defer_from_date',
        'defer_to_date',
        'original_course_end_date',
        'is_course_end_date_impact',
        'new_course_end_date',
        'student_location',
        'next_class_start_date',

        // Withdrawal fields
        'withdrawal_date',
        'is_approve',

        // Cancellation fields
        'reason_for_cancellation',
        'cancellation_comment',
        'is_notice_period_completed',
        'is_appeal_internally',
        'last_attended_date',
        'cancellation_date',

        // Common fields across status
        'approved_by',
        'approved_date',
        'is_student_release',
        'is_study_completed',
        'is_granting_release',
        'release_approved_by',
        'release_comment',
        'approval_evidence_file',

        // Common CoE fields (always visible but included for completeness)
        'new_coe_file',
        'new_coe_code',
    ];
    hideFormFields(fieldsToHide);

    // Reset all switch states to ensure clean state when switching between statuses
    // Only reset if not in edit mode (to preserve loaded values)
    if (!window.isEditMode) {
        resetAllSwitchStates();
    }
}

function resetAllSwitchStates() {
    let switchFields = [
        'is_course_end_date_impact',
        'is_approve',
        'is_student_release',
        'is_study_completed',
        'is_granting_release',
        'is_notice_period_completed',
        'is_appeal_internally',
    ];

    // Reset each switch to unchecked state
    switchFields.forEach((fieldName) => {
        let switchElement = $(`#${fieldName}`).data('kendoSwitch');
        if (switchElement) {
            switchElement.check(false);
        }
    });

    // Also clear related form field values to ensure clean state
    clearStatusSpecificFormValues();
}

function clearStatusSpecificFormValues() {
    // List of fields that should be cleared when switching status
    let fieldsToClear = [
        'defer_from_date',
        'defer_to_date',
        'original_course_end_date',
        'new_course_end_date',
        'student_location',
        'next_class_start_date',
        'withdrawal_date',
        'reason_for_cancellation',
        'cancellation_comment',
        'last_attended_date',
        'cancellation_date',
        'approved_by',
        'approved_date',
        'release_approved_by',
        'release_comment',
    ];

    // Clear each field value
    fieldsToClear.forEach((fieldName) => {
        let element = $(`#${fieldName}`);
        if (element.length > 0) {
            // Handle different input types
            if (element.data('kendoDatePicker')) {
                element.data('kendoDatePicker').value(null);
            } else if (element.data('kendoDropDownList')) {
                element.data('kendoDropDownList').value('');
            } else if (element.data('kendoTextArea')) {
                element.data('kendoTextArea').value('');
            } else {
                element.val('');
            }
        }
    });
}

// Function to populate edit form with data and show appropriate fields
function populateEditFormWithData(courseVariantData) {
    // First, show fields based on course status
    if (courseVariantData.course_status) {
        toggleFieldsByType(courseVariantData.course_status);
    }

    // Wait a bit for fields to be shown, then set switch values
    setTimeout(() => {
        setSwitchValuesFromData(courseVariantData);

        // After setting switches, initialize dependent fields
        setTimeout(() => {
            initializeSwitchDependentFields();
        }, 100);
    }, 200);
}

// Function to set switch values from loaded data
function setSwitchValuesFromData(data) {
    // List of switch fields and their corresponding data keys
    let switchMappings = [
        { field: 'is_course_end_date_impact', dataKey: 'is_course_end_date_impact' },
        { field: 'is_approve', dataKey: 'is_approve' },
        { field: 'is_student_release', dataKey: 'is_student_release' },
        { field: 'is_study_completed', dataKey: 'is_study_completed' },
        { field: 'is_granting_release', dataKey: 'is_granting_release' },
        { field: 'is_notice_period_completed', dataKey: 'is_notice_period_completed' },
        { field: 'is_appeal_internally', dataKey: 'is_appeal_internally' },
    ];

    // Set each switch based on data
    switchMappings.forEach((mapping) => {
        let switchElement = $(`#${mapping.field}`).data('kendoSwitch');
        if (switchElement && data[mapping.dataKey] !== undefined) {
            // Convert data value to boolean (handle '1', 1, 'on', true, etc.)
            let isChecked =
                data[mapping.dataKey] === '1' ||
                data[mapping.dataKey] === 1 ||
                data[mapping.dataKey] === 'on' ||
                data[mapping.dataKey] === true;
            switchElement.check(isChecked);
        }
    });
}

function showDeferSuspendFields() {
    let deferSuspendFields = [
        'defer_from_date',
        'defer_to_date',
        'original_course_end_date',
        'is_course_end_date_impact',
        'new_course_end_date',
        'student_location',
        'next_class_start_date',
        // Common CoE fields
        'new_coe_file',
        'new_coe_code',
    ];
    showFormFields(deferSuspendFields);
}

function showWithdrawalFields() {
    let withdrawalFields = [
        'withdrawal_date',
        'is_approve',
        'approved_by',
        'approved_date',
        'is_student_release',
        'is_study_completed',
        'is_granting_release',
        'release_approved_by',
        'release_comment',
        'approval_evidence_file',
        // Common CoE fields
        'new_coe_file',
        'new_coe_code',
    ];
    showFormFields(withdrawalFields);
}

function showCancellationFields() {
    let cancellationFields = [
        'reason_for_cancellation',
        'cancellation_comment',
        'is_notice_period_completed',
        'is_appeal_internally',
        'last_attended_date',
        'cancellation_date',
        'is_approve',
        'approved_by',
        'approved_date',
        'is_student_release',
        'is_study_completed',
        'is_granting_release',
        'release_approved_by',
        'release_comment',
        'approval_evidence_file',
        // Common CoE fields
        'new_coe_file',
        'new_coe_code',
    ];
    showFormFields(cancellationFields);
}

// Common utility functions for showing/hiding form fields
function showFormFields(fieldNames) {
    fieldNames.forEach((fieldName) => {
        $(`#${fieldName}-form-label`).closest('.k-form-field').show();
    });
}

function hideFormFields(fieldNames) {
    fieldNames.forEach((fieldName) => {
        $(`#${fieldName}-form-label`).closest('.k-form-field').hide();
    });
}

// Common switch change function to show/hide fields based on switch state
function createSwitchChangeHandler(fieldsToToggle, defaultAction = 'show') {
    return function (e) {
        let isChecked = e.checked == true;
        let shouldShow = defaultAction === 'show' ? isChecked : !isChecked;
        if (shouldShow) {
            showFormFields(fieldsToToggle);
        } else {
            hideFormFields(fieldsToToggle);
        }
    };
}

// Initialize switch-dependent field visibility based on current switch states
function initializeSwitchDependentFields() {
    // Handle simple switch dependencies (non-cascading)
    let simpleSwitchDependencies = [
        {
            switchField: 'is_course_end_date_impact',
            dependentFields: ['new_course_end_date'],
            defaultAction: 'show',
        },
        {
            switchField: 'is_approve',
            dependentFields: ['approved_by', 'approved_date'],
            defaultAction: 'show',
        },
    ];

    // Process simple switch dependencies
    simpleSwitchDependencies.forEach((dependency) => {
        let switchElement = $(`#${dependency.switchField}`).data('kendoSwitch');
        if (switchElement) {
            let isChecked = switchElement.check();
            let shouldShow = dependency.defaultAction === 'show' ? isChecked : !isChecked;
            if (shouldShow) {
                showFormFields(dependency.dependentFields);
            } else {
                hideFormFields(dependency.dependentFields);
            }
        }
    });

    // Handle cascading student release logic separately
    handleStudentReleaseLogic();
}

// Handle cascading student release logic for withdrawal/cancellation
function handleStudentReleaseLogic() {
    let studentReleaseSwitch = $('#is_student_release').data('kendoSwitch');
    let studyCompletedSwitch = $('#is_study_completed').data('kendoSwitch');
    let grantingReleaseSwitch = $('#is_granting_release').data('kendoSwitch');
    if (studentReleaseSwitch) {
        let isStudentReleaseOn = studentReleaseSwitch.check();
        if (isStudentReleaseOn) {
            showFormFields(['is_study_completed']);
            if (studyCompletedSwitch) {
                let isStudyCompletedOn = studyCompletedSwitch.check();
                if (isStudyCompletedOn) {
                    hideFormFields([
                        'is_granting_release',
                        'release_approved_by',
                        'release_comment',
                    ]);
                } else {
                    showFormFields(['is_granting_release']);
                    if (grantingReleaseSwitch) {
                        let isGrantingReleaseOn = grantingReleaseSwitch.check();
                        if (isGrantingReleaseOn) {
                            showFormFields(['release_approved_by', 'release_comment']);
                        } else {
                            hideFormFields(['release_approved_by', 'release_comment']);
                        }
                    }
                }
            }
        } else {
            // Hide all dependent fields when student release is OFF
            hideFormFields([
                'is_study_completed',
                'is_granting_release',
                'release_approved_by',
                'release_comment',
            ]);
        }
    }
}

function saveCourseVariantForm() {
    // Use existing validation and get regular form data
    let regularFormData = formValidateAndReturnFormData(addCourseVariantFormId);

    if (!regularFormData) {
        return false; // Validation failed
    }

    // Additional validation for new course end date
    if (
        regularFormData.is_course_end_date_impact === '1' ||
        regularFormData.is_course_end_date_impact === 'on'
    ) {
        let originalEndDate = $('#original_course_end_date').data('kendoDatePicker').value();
        let newEndDate = $('#new_course_end_date').data('kendoDatePicker').value();

        // Check if new course end date is required but not provided
        /*if (!newEndDate || newEndDate === '') {
            notificationDisplay('New Course End Date is required when Course End Date is impacted.', '', 'error');
            return false;
        }*/

        // Check if new course end date is greater than original course end date
        if (newEndDate && originalEndDate && new Date(newEndDate) <= new Date(originalEndDate)) {
            notificationDisplay(
                'New Course End Date must be greater than Original Course End Date',
                '',
                'error'
            );
            return false;
        }
    }

    // Create FormData to handle file uploads
    let formData = new FormData();

    // Add all regular form data from the existing function
    for (let key in regularFormData) {
        if (regularFormData.hasOwnProperty(key)) {
            formData.append(key, regularFormData[key]);
        }
    }

    // formData.append('college_id', collegeId);
    // formData.append('student_id', studentId);
    // formData.append('student_course_id', selectedStudCourseID);

    // Add file inputs specifically (since they're not handled by getSerializeFormArray)
    $(addCourseVariantFormId)
        .find('input[type="file"]')
        .each(function () {
            let $field = $(this);
            let fieldName = $field.attr('name');

            if (fieldName && $field[0].files && $field[0].files.length > 0) {
                formData.append(fieldName, $field[0].files[0]);
            }
        });

    /*ajaxActionV2('api/save-course-variant-data', 'POST', formData, function (response) {
        handleAjaxResponse(response, function () {
            closeKendoWindow(addCourseVariantModalId);
            loadStudentStatusCards(); // Reload the cards to show the new status
        });
    });*/

    $.ajax({
        type: 'POST',
        url: site_url + 'api/save-course-variant-data',
        data: formData,
        processData: false,
        contentType: false,
        headers: { 'X-CSRF-TOKEN': $('input[name="_token"]').val() },
        success: function (response) {
            handleAjaxResponse(response, function () {
                closeKendoWindow(addCourseVariantModalId);
                loadStudentStatusCards();
            });
        },
        error: function (response) {
            handleAjaxResponse(response, function () {
                closeKendoWindow(addCourseVariantModalId);
                loadStudentStatusCards();
            });
        },
    });
}

function updateCourseVariantForm() {
    // Use existing validation and get regular form data
    let regularFormData = formValidateAndReturnFormData(editCourseVariantFormId);

    if (!regularFormData) {
        return false; // Validation failed
    }

    // Additional validation for new course end date
    if (
        regularFormData.is_course_end_date_impact === '1' ||
        regularFormData.is_course_end_date_impact === 'on'
    ) {
        let originalEndDate = $(editCourseVariantFormId)
            .find('#original_course_end_date')
            .data('kendoDatePicker')
            .value();
        let newEndDate = $(editCourseVariantFormId)
            .find('#new_course_end_date')
            .data('kendoDatePicker')
            .value();

        // Check if new course end date is required but not provided
        if (!newEndDate || newEndDate === '') {
            notificationDisplay(
                'New Course End Date is required when Course End Date is impacted.',
                '',
                'error'
            );
            return false;
        }

        // Check if new course end date is greater than original course end date
        if (newEndDate && originalEndDate && new Date(newEndDate) <= new Date(originalEndDate)) {
            notificationDisplay(
                'New Course End Date must be greater than Original Course End Date',
                '',
                'error'
            );
            return false;
        }
    }

    // Create FormData for file upload (same as save function)
    let formData = new FormData();

    // Add all regular form fields
    Object.keys(regularFormData).forEach((key) => {
        if (regularFormData[key] !== null && regularFormData[key] !== undefined) {
            formData.append(key, regularFormData[key]);
        }
    });

    // Add the course variant ID for update operation
    if (window.currentCourseVariantId) {
        formData.append('id', window.currentCourseVariantId);
    }

    // Add file fields
    $(editCourseVariantFormId)
        .find('input[type="file"]')
        .each(function () {
            let $field = $(this);
            let fieldName = $field.attr('name');

            if (fieldName && $field[0].files && $field[0].files.length > 0) {
                formData.append(fieldName, $field[0].files[0]);
            }
        });
    /*let coeFileInput = document.getElementById('new_coe_file');
    if (coeFileInput && coeFileInput.files.length > 0) {
        formData.append('new_coe_file', coeFileInput.files[0]);
    }

    let evidenceFileInput = document.getElementById('approval_evidence_file');
    if (evidenceFileInput && evidenceFileInput.files.length > 0) {
        formData.append('approval_evidence_file', evidenceFileInput.files[0]);
    }*/

    // Add CSRF token
    formData.append('_token', $('input[name="_token"]').val());

    $.ajax({
        type: 'POST',
        url: site_url + 'api/update-course-variant-data',
        data: formData,
        processData: false,
        contentType: false,
        headers: { 'X-CSRF-TOKEN': $('input[name="_token"]').val() },
        success: function (response) {
            handleAjaxResponse(response, function () {
                window.isEditMode = false; // Clear edit mode flag
                closeKendoWindow(editCourseVariantModalId);
                loadStudentStatusCards();
            });
        },
        error: function (response) {
            handleAjaxResponse(response, function () {
                window.isEditMode = false; // Clear edit mode flag
                closeKendoWindow(editCourseVariantModalId);
                loadStudentStatusCards();
            });
        },
    });
}

// Helper function to handle AJAX responses with proper validation error handling
function handleAjaxResponse(response, successCallback = null) {
    let actualResponse = response;
    if (response.responseJSON) {
        actualResponse = response.responseJSON;
    }
    let message = actualResponse.message;
    let status = actualResponse.status;

    // Handle validation errors - show all validation messages
    if (actualResponse.code === 422 && actualResponse.data) {
        let validationErrors = [];
        for (let field in actualResponse.data) {
            if (actualResponse.data.hasOwnProperty(field)) {
                if (Array.isArray(actualResponse.data[field])) {
                    validationErrors = validationErrors.concat(actualResponse.data[field]);
                } else {
                    validationErrors.push(actualResponse.data[field]);
                }
            }
        }
        if (validationErrors.length > 0) {
            message = validationErrors.join('<br/>');
        }
        status = 'error';
    } else if (Array.isArray(message)) {
        message = message.join('<br/>');
    }

    notificationDisplay(message, '', status);
    if (status == 'success' && successCallback && typeof successCallback === 'function') {
        successCallback();
    }

    return { status, message, data: actualResponse.data };
}
