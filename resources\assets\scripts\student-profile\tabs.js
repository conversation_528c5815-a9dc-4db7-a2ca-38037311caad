var verifyBeforeCancellationModalId = '#verifyBeforeCancellationModal';
var verifyIfPreviousSameCourseFoundModalId = '#verifyIfPreviousSameCourseFoundModal';
var verifyIfPreviousSameUnitFoundModalId = '#verifyIfPreviousSameUnitFoundModal';

// Import risk matrix tab functionality
import './tabs/riskMatrixTab.js';
$(document).ready(function () {
    let selectedUnits = [];
    let selectedUnitsIds = [];
});
$(document).ready(function () {
    /*$("#loader").kendoLoader();
    kendo.ui.progress.messages = {
        loading: '<div class="vue-simple-spinner animate-spin" style="position:absolute;top:50%;left:50%;transfrom:translate(-50%,-50%); margin: 0px auto; border-radius: 100%; border-color: rgb(50, 157, 243) rgb(238, 238, 238) rgb(238, 238, 238); border-style: solid; border-width: 3px; border-image: none 100% / 1 / 0 stretch; width: 30px; height: 30px;"></div>',
    };*/

    $('#studentCourseStatusUpdateModal').kendoWindow(openCenterWindow('Change Course Status'));
    $('#previousEnrolledUnitModal').kendoWindow(openCenterWindow('Previous Enroll Unit', 50));
    $('#studentCourseExtendModal').kendoWindow(openCenterWindow('Extend Course Date Range', 50));

    $('#studentCourseStatusHistoryModal').kendoWindow(
        openCenterWindow('Course Status Change History', 40, 25, 30)
    );
    $('#viewStudentCoursesModal').kendoWindow(defaultWindowSlideFormat('Courses', 50));

    addModalClassToWindows([
        '#studentCourseStatusHistoryModal',
        '#studentCourseStatusUpdateModal',
        '#studentCourseExtendModal',
        '#currentCourseHistoryModal',
        '#previousEnrolledUnitModal',
    ]);

    // $(document).on('click', '.expand-row', function (e){
    //     console.log('in');
    //     let row = $(e.target).parents("tr").first();
    //     let icon = row.find(".k-hierarchy-cell>a.k-icon");
    //     icon.click();
    //     //$('.k-detail-row').hide();
    //     let currentExpandTr = $(e.target).parents("tr").next("tr").find('.student-details');
    //     let expandStatus = $(e.target).parents("tr").first().find(".k-hierarchy-cell").attr('aria-expanded');
    //     $(document).find('.expand-row span').removeClass('k-i-arrow-chevron-up').addClass('k-i-arrow-chevron-down');
    //     if(expandStatus == 'true'){
    //         currentExpandTr.hide();
    //         $(this).find('span').removeClass('k-i-arrow-chevron-down').addClass('k-i-arrow-chevron-up');
    //         currentExpandTr.slideDown("slow", function() {});
    //     }else{
    //         currentExpandTr.closest("tr").css('display','table-row');
    //         currentExpandTr.slideUp("slow", function() {
    //             $(e.target).parents("tr").next("tr").hide();
    //         });
    //     }
    // });

    $('body').on('click', '.enroll_new_course', function () {
        getData(selectedStudCourseID);
    });

    $('body').on('click', '.viewStatusHistoryBtn', function () {
        let tempSelectedDataArr = selectedDataArr;
        if ($(this).hasAttr('data-sc-id')) {
            tempSelectedDataArr = {
                college_id: collegeId,
                student_id: studentId,
                student_course_id: $(this).attr('data-sc-id'),
            };
        }
        ajaxActionV2(
            'api/student-status-history-list',
            'POST',
            tempSelectedDataArr,
            function (response) {
                let historyTemplate = kendo.template(
                    $('#studentCourseStatusHistoryTemplate').html()
                )({ arr: response.data });
                $(document).find('#studentCourseStatusHistoryDiv').html(historyTemplate);
                kendoWindowOpen('#studentCourseStatusHistoryModal');
            }
        );
    });

    $('body').on('click', '.updateCourse', function () {
        // let isXeroConnectVal = false;
        ajaxActionV2('api/student-course-detail', 'POST', selectedDataArr, function (response) {
            if (response.status == 'error') {
                notificationDisplay(response.message, '', response.status);
                return false;
            }
            let res = response.data[0];
            isXeroConnectVal = res.isXeroConnect;
            if (verifyCancelledStatus(res.status)) {
                notificationDisplay(
                    'Course has already been cancelled. Status cannot be changed.',
                    '',
                    'error'
                );
                return false;
            }

            setTimeout(() => {
                setUpStudentCourseStatusDropdown(res.status);
            }, 100);

            $(document)
                .find('#studentCourseStatusUpdateHtml')
                .html(kendo.template($('#studentCourseStatusUpdateTemplate').html())({ arr: res }));

            $(document)
                .find('#studentCourseStatusHistoryHtml')
                .html(
                    kendo.template($('#studentCourseStatusHistoryTemplate').html())({
                        arr: res.statusHistoryData,
                    })
                );
            kendoWindowOpen('#studentCourseStatusUpdateModal');
        });
    });

    $('body').on('click', '.closeModal', function () {
        let modalId = $(this).attr('data-modal-name');
        closeKendoWindow('#' + modalId);
    });

    $('body').on('click', '.submitStudentCourseUpdate', function () {
        //let formData = $("#studentCourseUpdateForm").find("select, textarea, input").serialize();

        let newStatus = $('#course_status_list').val();
        let dataArr = {
            studentId: studentId,
            studCourseId: selectedStudCourseID,
            newStatus: newStatus,
        };

        if (verifyCancelledStatus(newStatus)) {
            let cancelledReason = $(document).find('#cancelled_reason').val();
            if (cancelledReason.trim() == '') {
                notificationDisplay('Please enter reason', '', 'error');
                return false;
            }
            customKendoDialog(newStatus);
            if (isXeroConnectVal == 1) {
                let isRemoveInvoice = $(document)
                    .find('#is_remove_invoice')
                    .data('kendoSwitch')
                    .value();
                notificationDisplay(
                    'This option will remove draft invoice from galaxy and xero.',
                    '',
                    'error'
                );
                $(document).find('.removeDraftInfo').toggleClass('hidden', !isRemoveInvoice);
            }
        } else if (newStatus == 'Completed') {
            completedKendoDialog('Completed');
        } else {
            updateStudentCourseStatus(dataArr);
        }
    });

    $('body').on('click', '.view_courses', function () {
        ajaxActionV2(
            'api/get-student-courses',
            'POST',
            { student_id: studentId },
            function (response) {
                let studCourseListTemplate = kendo.template($('#studCourseListTemplate').html())({
                    course_detail: response.data,
                });
                $(document).find('#student_course_list').html(studCourseListTemplate);
                kendoWindowOpen('#viewStudentCoursesModal');
                $('#viewStudentCoursesModal').parent().addClass('course-custom-modal-wrapper');
                initializeTooltips();
                $('#courseList').kendoTooltip({
                    filter: 'button.courseActionBtn',
                    position: 'bottom',
                    width: 300,
                    showOn: 'click',
                    show: function (e) {
                        e.sender.popup.element.find('.k-callout').remove();
                        e.sender.popup.element.addClass('tw-popup--top-right tw-tooltip');
                        e.sender.popup.wrapper.css({
                            right: '24px',
                            left: 'unset',
                        });
                    },
                    content: function (e) {
                        const btnElement = e.target;
                        const id = btnElement.data('id');

                        console.log('e', e, id);
                        return manageCourseActionColumn(id);
                    },
                });
            }
        );
    });

    $(document).on('mouseenter mouseleave', '.courseHover', function (event) {
        $(this).toggleClass('courseHoverAction');
    });

    /*$(document).on("click", ".courseActionBtn", function(e) {
        let courseId = $(this).attr('data-id');
         $(".courseActionMenu")
             .not(".courseActionMenu" + courseId)
             .addClass("hidden");
        if($('.courseActionMenu'+courseId).hasClass("hidden")){
            $('.courseActionMenu'+courseId).removeClass("hidden");
        } else {
            $('.courseActionMenu'+courseId).addClass("hidden");
        }
    });*/

    $(document).on('click', '.moreActionBtn', function (e) {
        $('.moreActionMenu').toggleClass('hidden');
    });

    function manageCourseActionColumn(id) {
        return kendo.template($('#couseActionMenuItems').html())({
            id: id,
        });
    }

    $(document).on('click', '.toggleSidebarMenu', function () {
        $('.hideSidebar').addClass('active');
        $('.hideSidebar').removeClass('hidden');
    });

    $(document).on('click', '.close-sidebar', function () {
        $('.hideSidebar').addClass('hidden');
        $('.hideSidebar').removeClass('active');
    });
    $(document).on('click', '.NewCourse', function () {
        $('.hideSidebar').addClass('hidden');
        $('.hideSidebar').removeClass('active');
    });

    function setUpStudentCourseStatusDropdown(selectedValue) {
        // Filter out non-active statuses: cancelled, deferred, withdrawn, suspended
        /*let filteredCourseStatus = arrCourseStatus.filter(function (item) {
            return !isNonActiveStatus(item.Name || item);
        });*/

        $('#course_status_list').kendoDropDownList({
            autoWidth: true,
            dataTextField: 'Name',
            dataValueField: 'Id',
            filter: 'contains',
            dataSource: arrCourseStatus, //filteredCourseStatus
            value: selectedValue,
            select: function (e) {
                e.preventDefault();
                if (this.value() !== e.dataItem.Name) {
                    handleStudentCourseStatusChange(e.dataItem.Name);
                }
            },
        });
        $(document)
            .find('#is_remove_invoice')
            .kendoSwitch({
                change: function (e) {
                    const $switchWrapper = $(e.sender.wrapper);
                    if (e.checked) {
                        $switchWrapper.addClass('k-switch-danger');
                        notificationDisplay(
                            'This option will remove draft invoice from galaxy and xero.',
                            '',
                            'error'
                        );
                    } else {
                        $switchWrapper.removeClass('k-switch-danger');
                    }
                    //const $draftInvoiceMsg = $(document).find('#studentCourseUpdateForm').find('.draftInvoiceMsg');
                    //$draftInvoiceMsg.toggleClass('hidden', !e.checked);
                },
            });

        $(document)
            .find('#is_remove_invoice')
            .parents('.forCancelDiv')
            .addClass('customSwitchButton');
    }

    function handleStudentCourseStatusChange(status = '') {
        $('#course_status_list').data('kendoDropDownList').value(status);
        let isCancelStatus = verifyCancelledStatus(status);
        $(document).find('.forCancelDiv').toggleClass('hidden', !isCancelStatus);
    }

    function displayUnitGrid(dublicateType = 'course') {
        if ($('#previouCourseResultGrid').data('kendoGrid')) {
            $('#previouCourseResultGrid').data('kendoGrid').destroy(); // Destroy the grid instance
            $('#previouCourseResultGrid').empty(); // Clear the DOM element to avoid reinitialization issues
        }

        var tempSelectedDataArr = selectedDataArr;
        tempSelectedDataArr.dublicate_type = dublicateType;

        $('#previouCourseResultGrid').kendoGrid({
            dataSource: customDataSourceForInlineV2(
                'api/student-status-unit-data',
                {
                    SN: { type: 'string', editable: false },
                    unit_id: { type: 'string', editable: false },
                    unit_code: { type: 'string', editable: false },
                    unit_name: { type: 'string', editable: false },
                    subject_attempt: { type: 'string', editable: false },
                    course_activity_date: { type: 'date' },
                    final_outcome: { type: 'string', editable: false },
                    marks: { type: 'string', editable: false },
                    start_date: { type: 'date', editable: true },
                    finish_date: { type: 'date', editable: true },
                    last_assessment_approved_date: {
                        type: 'date',
                        editable: true,
                    },
                    actual_end_date: { type: 'date' },
                    compentancy: { type: 'string' },
                },
                tempSelectedDataArr,
                {},
                {}
            ),
            pageable: customPageableArr(),
            sortable: false,
            resizable: true,
            change: onChange,
            columns: [
                {
                    selectable: true,
                    width: 44,
                    minResizableWidth: 44,
                    minWidth: 40,
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div unit_#: id #'>#: SN #</div>",
                    field: 'SN',
                    title: 'SN',
                    minResizableWidth: 80,
                    width: 36,
                    minWidth: 30,
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: unit_code #</div>",
                    field: 'unit_code',
                    title: 'Unit Code',
                    minResizableWidth: 80,
                    width: 120,
                    minwidth: 60,
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: unit_name #</div>",
                    field: 'unit_name',
                    title: 'Unit Name',
                    minResizableWidth: 80,
                    width: 80,
                    minwidth: 60,
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div truncate' title='#: subject_attempt #'>#: subject_attempt #</div>",
                    field: 'subject_attempt',
                    title: 'Current Attempt',
                    minResizableWidth: 80,
                    width: 60,
                    minWidth: 40,
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div truncate' title='#: next_attempt #'>#: next_attempt #</div>",
                    field: 'next_attempt',
                    title: 'New Attempt',
                    minResizableWidth: 80,
                    width: 60,
                    minWidth: 40,
                },

                {
                    // template: "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: kendo.toString(start_date, displayDateFormatJS) # </div>",
                    template: function (dataItem) {
                        return `<input class='date-picker' name='start_date' value='${kendo.toString(dataItem.start_date, displayDateFormatJS)}' />`;
                    },
                    field: 'start_date',
                    title: 'Start Date',
                    minResizableWidth: 130,
                    format: '{0:dd/MM/yyyy}',
                    // editor: datePickerForInlineEdit,
                    // editable: true,
                    width: 160,
                    minWidth: 60,
                },
                {
                    // template: "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: kendo.toString(finish_date, displayDateFormatJS) # </div>",
                    template: function (dataItem) {
                        return `<input class='date-picker' name='finish_date' value='${kendo.toString(dataItem.finish_date, displayDateFormatJS)}' />`;
                    },
                    field: 'finish_date',
                    title: 'Finish Date',
                    minResizableWidth: 130,
                    format: '{0:dd/MM/yyyy}',
                    // editor: datePickerForInlineEdit,
                    width: 160,
                    minWidth: 100,
                },
                {
                    // template: "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: (last_assessment_approved_date !=null)? kendo.toString(last_assessment_approved_date, displayDateFormatJS):'-' # </div>",
                    template: function (dataItem) {
                        return `<input class='date-picker' name='last_assessment_approved_date' value='${kendo.toString(dataItem.last_assessment_approved_date, displayDateFormatJS)}' />`;
                    },
                    field: 'last_assessment_approved_date',
                    title: 'Activity End Date',
                    minResizableWidth: 100,
                    format: '{0:dd/MM/yyyy}',
                    // editor: datePickerForInlineEdit,
                    width: 160,
                    minWidth: 100,
                },

                {
                    template:
                        "<div class='h-6 px-2.5 py-0.5 bg-green-100 rounded justify-center items-center inline-flex'><span class='flex items-center text-xs leading-5 font-normal text-green-800 action-div' title='#: final_outcome #'>#: ((final_outcome == null)?'--':final_outcome) #</span></div>",
                    field: 'final_outcome',
                    title: 'Previous Final Outcome',
                    minResizableWidth: 100,
                    editor: dropDownForFinalOutcome,
                    width: 120,
                    minWidth: 100,
                },
                {
                    template:
                        "<div class='h-6 px-2.5 py-0.5 bg-green-100 rounded justify-center items-center inline-flex'><span class='flex items-center text-xs leading-5 font-normal text-green-800 action-div' title='#: final_outcome #'>#: (final_outcome == 'C' || final_outcome == 'CT' || final_outcome == 'RPL')? 'CT':'Enrolled' # </span></div>",
                    field: 'final_outcome',
                    title: 'New Final Outcome',
                    minResizableWidth: 100,
                    editor: dropDownForFinalOutcome,
                    width: 120,
                    minWidth: 100,
                },
                {
                    template:
                        "<div class='flex items-center text-sm leading-5 font-normal text-gray-600 action-div'>#: ((marks !=null) ? marks :'--') #</div>",
                    field: 'marks',
                    title: 'Marks',
                    minResizableWidth: 80,
                    minWidth: 60,
                    hidden: !isHigherEd,
                },
                // {
                //     // template: '<a class="expand-row" href="javascript:void(0);" aria-label="Expand" tabindex="-1"><span class="k-icon k-i-arrow-chevron-down"></span></a>',
                //     headerTemplate:
                //         "<a class='k-link text-xs font-medium tracking-wide leading-none text-gray-500' style='cursor: default !important;'>Action</a>",
                //     field: "action",
                //     title: "Action",
                //     minResizableWidth: 80,
                //     filterable: false,
                //     sortable: false,
                //     width: 80,
                //     minWidth: 60,
                //     command: [
                //         {
                //             name: "edit",
                //             visible: function (dataItem) {
                //                 return false; // Hide edit button now
                //             }
                //         }
                //     ]

                // },
            ],
            save: function (e) {
                // if (e.model.final_outcome !== undefined && e.model.is_result_lock != '1' && (e.model.final_outcome == 'C' || e.model.final_outcome == 'NYC')) {
                //     e.preventDefault(); // Prevent auto-saving
                //     // Display confirmation popup
                //     if (confirm(resultConformationMessage)) {
                //         // Proceed with saving the data if confirmed
                //         e.sender.saveChanges(); // Manually trigger the save
                //     }
                // }
            },
            editable: {
                // mode: "inline",
                mode: 'incell',
                createAt: 'top',
                update: function (e) {
                    return !e.model.is_course_complete; // Disable editing if course is complete
                },
            },
            noRecords: noRecordTemplate(),
            dataBound: function (e) {
                $('.date-picker').each(function () {
                    let $this = $(this);
                    let fieldName = $this.attr('name'); // Get the field name from the name attribute
                    let rowData = $this.closest('tr').data('uid'); // Get the row's unique ID

                    // Get the data item associated with the row
                    let dataItem = e.sender.dataSource.getByUid(rowData);

                    $this.kendoDatePicker({
                        format: 'dd/MM/yyyy',
                        change: function () {
                            let value = this.value(); // Get the selected value
                            dataItem.set(fieldName, value); // Update the grid's data item
                        },
                    });
                });
            },
        });
    }

    function onChange(e) {
        if (e.sender.editable) {
            e.sender.cancelRow();
        }
        var selectedUnits = [];
        var selectedUnitsIds = [];
        var checkIsCourseComplete = [];
        var rows = e.sender.select();
        rows.each(function (e) {
            var grid = $('#previouCourseResultGrid').data('kendoGrid');
            var dataItem = grid.dataItem(this);

            selectedUnits.push({
                id: dataItem.id,
                unitCode: dataItem.unit_code,
                unitName: dataItem.unit_name,
                start_date: dataItem.start_date,
                finish_date: dataItem.finish_date,
                final_outcome: dataItem.final_outcome,
            });
            selectedUnitsIds.push(dataItem.id);

            if (!dataItem.is_course_complete) {
                checkIsCourseComplete.push(dataItem.id);
            }
        });
    }

    // $("body").on("change", ".date-picker",function () {
    //     alert();
    //     const grid = $("#previouCourseResultGrid").data("kendoGrid");
    //     const row = $(this).closest("tr");
    //     const dataItem = grid.dataItem(row);

    //     // Update the field with the new value
    //     dataItem[this.name] = $(this).val();
    //     console.log('dataItem12',dataItem);
    // });

    $('body').on('click', '.autoEnrolledUnit', function () {
        startAjaxLoader();
        const grid = $('#previouCourseResultGrid').data('kendoGrid');
        const selectedRows = grid.select(); // Get selected rows
        let selectedData = [];

        // Collect data for selected rows
        selectedRows.each(function () {
            const dataItem = grid.dataItem(this); // Get the data item for the row
            selectedData.push({
                id: dataItem.id,
                unit_id: dataItem.unit_id,
                start_date: kendo.toString(dataItem.start_date, 'yyyy-MM-dd'), // Format date if needed
                finish_date: kendo.toString(dataItem.finish_date, 'yyyy-MM-dd'),
                final_outcome: dataItem.final_outcome,
                student_course_id: selectedDataArr.student_course_id,
            });
        });

        if (selectedData.length === 0) {
            alert('No rows selected!');
            stopAjaxLoader();
            return;
        }

        let dataPost = { data: selectedData };
        // Log or send the updated records to your server
        console.log('Updated Records:', selectedData);

        // Example: Send the updated data via AJAX
        ajaxActionV2('api/add-unit-on-course-status-change', 'POST', dataPost, function (response) {
            stopAjaxLoader();
            notificationDisplay(response.message, '', response.status);
            closeKendoWindow('#previousEnrolledUnitModal');
            window.location.reload();
        });
    });

    function isNonActiveStatus(status) {
        let statusList = ['cancelled', 'deferred', 'withdrawn', 'suspended'];
        return statusList.includes(status.toLowerCase());
    }

    function verifyCancelledStatus(status) {
        let statusList = ['cancelled'];
        return statusList.includes(status.toLowerCase());
    }

    function convertCancelledStatusLabel(status) {
        const statusMap = {
            cancelled: 'Cancel',
            deferred: 'Defer',
            withdrawn: 'Withdrawn',
            suspended: 'Suspend',
        };
        return statusMap[status.toLowerCase()] || status;
    }

    function getStatusChangeFormData() {
        return {
            studentId: studentId,
            studCourseId: selectedStudCourseID,
            newStatus: $(document).find('#course_status_list').val(),
            isRemoveInvoice:
                isXeroConnectVal == '1'
                    ? $(document).find('#is_remove_invoice').data('kendoSwitch').value()
                        ? 1
                        : 0
                    : 0,
            cancelledReason: $(document).find('#cancelled_reason').val(),
        };
    }

    function updateStudentCourseStatus(dataArr) {
        startAjaxLoader();
        ajaxActionV2('api/update-student-course-status', 'POST', dataArr, function (response) {
            stopAjaxLoader();
            notificationDisplay(response.message, '', response.status);
            if (response.status == 'success') {
                closeKendoWindow('#studentCourseStatusUpdateModal');
                setCourseListWhenStatusUpdate(response.data);
                isActiveCourse = response.data.isActiveCourse;
                if (dataArr.newStatus == 'Current Student') {
                    openAutoEnrollConfirmationPopup();
                }
            }
        });
    }

    function customKendoDialog(statusVal = 'Cancelled') {
        let kendoDialogForVerify = $(verifyBeforeCancellationModalId).data('kendoDialog');
        let statusLabel = convertCancelledStatusLabel(statusVal);
        kendoDialogForVerify.title('Confirm ' + statusLabel);
        kendoDialogForVerify.content(
            kendo.template($('#cancellationTemplate').html())({
                status: statusLabel,
            })
        );
        kendoDialogForVerify.open();
    }
    function completedKendoDialog(statusVal) {
        let kendoDialogForVerify = $(verifyBeforeCancellationModalId).data('kendoDialog');
        let statusLabel = convertCancelledStatusLabel(statusVal);
        kendoDialogForVerify.title('Confirm ' + statusLabel);
        kendoDialogForVerify.content(
            kendo.template($('#completedTemplate').html())({
                status: statusLabel,
            })
        );
        kendoDialogForVerify.open();
    }

    $(verifyBeforeCancellationModalId).kendoDialog({
        width: '400px',
        title: 'Approve Cancellation',
        //content: function () { return kendo.template($("#cancellationTemplate").html()); },
        actions: [
            { text: 'No' },
            {
                text: 'Yes',
                primary: true,
                action: function () {
                    updateStudentCourseStatus(getStatusChangeFormData());
                },
            },
        ],
        animation: defaultOpenAnimation(),
        open: onOpenDeleteDialog(verifyBeforeCancellationModalId),
        visible: false,
    });

    $(verifyIfPreviousSameCourseFoundModalId).kendoDialog({
        width: '400px',
        title: 'Unit auto enrolled confirmation?',
        content:
            'We have found previouly enrolled same course. Do you want to auto enrolled unit for same course?',
        actions: [
            { text: 'No' },
            {
                text: 'Yes',
                primary: true,
                action: function () {
                    kendoWindowOpen('#previousEnrolledUnitModal');
                    displayUnitGrid();
                },
            },
        ],
        animation: defaultOpenAnimation(),
        // open: onOpenDeleteDialog(verifyIfPreviousSameCourseFoundModalId),
        visible: false,
    });
    $(verifyIfPreviousSameUnitFoundModalId).kendoDialog({
        width: '400px',
        title: 'Unit auto enrolled confirmation?',
        content:
            'The student has completed some units in a previous course, do you want to transfer the same units to this course?',
        actions: [
            { text: 'No' },
            {
                text: 'Yes',
                primary: true,
                action: function () {
                    kendoWindowOpen('#previousEnrolledUnitModal');
                    displayUnitGrid('unit');
                },
            },
        ],
        animation: defaultOpenAnimation(),
        // open: onOpenDeleteDialog(verifyIfPreviousSameCourseFoundModalId),
        visible: false,
    });

    function openAutoEnrollConfirmationPopup() {
        ajaxActionV2(
            'api/check-previous-course-found',
            'POST',
            selectedDataArr,
            function (response) {
                if (response.status == 'error' && response.data.dublicateType == 'course') {
                    let kendoDialogForVerify = $(verifyIfPreviousSameCourseFoundModalId).data(
                        'kendoDialog'
                    );
                    kendoDialogForVerify.open();
                } else if (response.status == 'error' && response.data.dublicateType == 'unit') {
                    let kendoDialogForVerify = $(verifyIfPreviousSameUnitFoundModalId).data(
                        'kendoDialog'
                    );
                    kendoDialogForVerify.open();
                }
            }
        );
    }
    $(document).on('click', '.showHideHistory', function (e) {
        $('.statusHistoryDiv').toggleClass('hidden');

        if ($('.statusHistoryDiv').hasClass('hidden')) {
            $('.historyDivTitle').text('View');
        } else {
            $('.historyDivTitle').text('Hide');
        }
    });
});
