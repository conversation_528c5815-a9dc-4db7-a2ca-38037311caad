let myEditorRiskMatrixTab;
var selectedRiskMatrixTabText = "all";

function initializeRiskMatrixTab() {
    setTimeout(() => {
        $(".k-timeline-date-wrap span")
            .parent()
            .removeClass("k-timeline-date-wrap");
    }, 5000);
    $(".addNewNotes").kendoTooltip({
        filter: "button",
        position: "bottom-right",
        width: 150,
        showAfter: 300,
        showOn: "click",
        show: function (e) {
            e.sender.popup.element.find(".k-callout").remove();
            e.sender.popup.element.addClass("tw-popup--top-right");
            e.sender.popup.wrapper.css({
                right: "48px",
                left: "unset",
            });
        },
        content: function (e) {
            console.log("e", e);
            return kendo.template($("#actionTemplateRiskMatrix").html());
        },
    });
}
function manageRiskMatrixTab() {
    console.log("What's running - Risk Matrix", selectedTabText, selectedRiskMatrixTabText);
    if (selectedTabText === "riskmatrix")
        initializeRiskMatrixTab();

}
