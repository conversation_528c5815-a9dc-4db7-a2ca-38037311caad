var selectedStudCourseID = '';
var isHigherEd = false;
var isActiveCourse = false;
var addressHistoryFlag = true;
var emergencyDetailsHistoryFlag = true;
var profileHistoryFlag = true;
var selectedCourseAgencyName = '-';
var isSyncing = false;
var isMoodleConnectVal = false;
var loadedTabs = {};
var isServerRequest = true;
var isSameTab = true;
var defaultDayFrequencyVal = '3'; // 3 for Month
var hasCourseChange = false;
// let pCommissionFlag = true;
// let pBonusFlag = true;
// let pScheduleFlag = true;
// let pMiscellaneousFlag = true;
// let pServiceFlag = true;
// let pPaymentFlag = true;
// let paidPaymentTransferListFlag = true;
// let pRefundFlag = true;
// let pScholarshipFlag = true;
// let attendanceGridViewFlag = true;
// let attendanceCalViewFlag = true;
// let resultUnitGrid = true;
// let sScheduleFlag = true;
// let unitGridFlag = true;
// let vpmsFlag = true;

var selectedDataArr = {
    college_id: collegeId,
    student_id: studentId,
    student_course_id: selectedStudCourseID,
};
var addressHistoryModal = '#addressHistoryModal';
var emergencyDetailsHistoryModal = '#emergencyDetailsHistoryModal';
var addressHistoryGrid = '#addressHistoryGrid';
var emergencyDetailsHistoryGrid = '#emergencyDetailsHistoryGrid';
var profileHistoryGrid = '#profileHistoryGrid';
var profileHistoryModal = '#profileHistoryModal';

function initView() {
    $(profileHistoryModal).kendoWindow(openCenterWindow('Profile Change History', 60, 15, 25));

    if (studentIsUser == 1) {
        $('.resetPasswordBtn').show();
        $('.reSendActivationBtn ').show();
    } else {
        $('.resetPasswordBtn').hide();
        $('.reSendActivationBtn ').hide();
    }

    // Loaders
    $('#summarySkeleton').kendoSkeletonContainer({
        animation: 'wave',
        height: 200,
        width: 340,
        grid: {
            items: [
                {
                    colStart: 1,
                    colSpan: 1,
                    rowStart: 1,
                    rowSpan: 1,
                    shape: 'rectangle',
                },
            ],
            rows: 1,
            columns: 1,
        },
    });
    $('#loader').kendoLoader();
    kendo.ui.progress.messages = {
        loading:
            '<div class="vue-simple-spinner animate-spin" style="position:absolute;top:50%;left:50%;transfrom:translate(-50%,-50%); margin: 0px auto; border-radius: 100%; border-color: rgb(50, 157, 243) rgb(238, 238, 238) rgb(238, 238, 238); border-style: solid; border-width: 3px; border-image: none 100% / 1 / 0 stretch; width: 30px; height: 30px;"></div>',
    };

    // Modals
    $('#displayProfilePicModal').kendoWindow(openCenterWindow('Profile Picture', 30, 15, 35));
    $('#previewFileManagerModal').kendoWindow(
        openCenterWindowWithHeight('Preview Document', 60, 90, 5, 20)
    );
    $('#editStudentModal').kendoWindow(defaultWindowSlideFormat('View Details', 80));
    $('#addNoteModal').kendoWindow(openCenterWindow('Add Note', 34, 26, 33));
    $('#previewDocumentModalNote').kendoWindow(
        openCenterWindowWithHeight('Preview Document', 60, 90, 5, 20)
    );

    $(addressHistoryModal).kendoWindow(openCenterWindow('Address Change History', 50, 15, 30));
    $(emergencyDetailsHistoryModal).kendoWindow(
        openCenterWindow('Emergency Details Change History', 60, 15, 25)
    );
    addModalClassToWindows([
        addressHistoryModal,
        emergencyDetailsHistoryModal,
        profileHistoryModal,
        '#addNoteModal',
        '#previewDocumentModalNote',
        '#previewFileManagerModal',
        '#displayProfilePicModal',
    ]);
}

// This is test.
function setCookiesForTab() {
    const tabactiveValue = new URLSearchParams(window.location.search).get('activetab');
    if (tabactiveValue) {
        $.cookie('activetab', tabactiveValue);
    }
}
// setCookiesForTab();
$(document).ready(function () {
    handleTabClickScroll('.viewDetailTab', '.editStudentModal-wrap', '.right-bar', 85);

    handleScrollTabChange('.editStudentModal-wrap', '.page-section', '.right-bar');

    // $(".editStudentModal-wrap")
    //     .scroll(function () {
    //         var scrollDistance = $(window).scrollTop();
    //         $(".page-section").each(function (i) {
    //             if ($(this).position().top <= scrollDistance) {
    //                 $(".right-bar li a.active").removeClass("active");
    //                 $(".right-bar li a").eq(i).addClass("active");
    //             }
    //         });
    //     })
    //     .scroll();

    $.ajaxSetup({
        headers: { Authorization: api_token },
    });

    $(document)
        .on('ajaxStart', function () {
            kendo.ui.progress($(document.body), false);
        })
        .on('ajaxStop', function () {
            kendo.ui.progress($(document.body), false);
        });

    // ajaxActionV2("api/get-student-courses", "POST", {'enroll_option': 1, 'student_id': studentId }, function (response) {
    //     setDropdownOnTabs(response.data);
    // });

    $('.Action').click(function () {
        $('#window')
            .show()
            .kendoWindow(openCenterWindow('Payment Details', 40, 10, 30));
    });
});

function setDropdownOnTabs(courseDataArr) {
    if (isServerRequest) {
        setCourseListForTabContent(courseDataArr, '', true);
        setDefaultCourse();
        initView();
        $('body').find('.overflowWrapper').css('overflow-y', 'hidden');
        let initializeMappings = {
            summary: 'initializeSummaryTab',
            course: 'initializeCourseTab',
            // 'documents': 'initializeDocumentsTab',
            // 'timetable': 'initializeTimetableTab',
            result: 'initializeResultTab',
            payments: 'initializePaymentTab',
            placement: 'initializePlacementTab',
            activitylog: 'initializeActivityLogTab',
            riskmatrix: 'initializeRiskMatrixTab',
        };
        if (
            initializeMappings[selectedTabText] &&
            typeof window[initializeMappings[selectedTabText]] === 'function'
        ) {
            window[initializeMappings[selectedTabText]]();
        }
    }
    $('#tabStrip').kendoTabStrip({
        animation: { open: { effects: '' } },
        change: function (e) {
            selectedTabText = e.sender.select().data('value');
            selectedTabValue = e.sender.select().data('value');
            if (hasCourseChange) {
                ajaxActionV2(
                    'api/get-student-courses',
                    'POST',
                    { enroll_option: 1, student_id: studentId },
                    function (response) {
                        courseDataArr = response.data;
                        loadAjaxTabContent(selectedTabValue, courseDataArr);
                        hasCourseChange = false;
                    }
                );
            } else {
                loadAjaxTabContent(selectedTabValue, courseDataArr);
            }
        },
        select: function (e) {
            resetFlags();
            var select = e.item.dataset.value;
            console.log('step 1',select);
            toggleTabLoader(select.toLowerCase(), true);
        },
    });
    // setCourseListForTabContent(courseDataArr);
    // setDefaultCourse();
}

function loadAjaxTabContent(value, courseDataArr) {
    const url = new URL(window.location.href);
    url.searchParams.set('activetab', value);
    const newURL = url.href;
    // if (loadedTabs[value]) {
    //     // $("." + value.toLowerCase() + "-li-detail").html(loadedTabs[value]);
    //     // checkCourseTypeHigherEd();
    //     if (selectedTabText === "documents") {
    //         manageTabData();
    //     } else {
    //         setCourseListForTabContent(courseDataArr, value);
    //     }
    //     return;
    // }
    $.ajax({
        type: 'GET',
        // dataType: "json",
        url: newURL,
        headers: { 'X-PJAX': true },
        // data: {
        //     activetab: value,
        // },
        async: true,
        beforeSend: function () {},
        success: function (data) {
            loadedTabs[value] = data;
            $('.' + value.toLowerCase() + '-li-detail').html(data);
            console.log('step 2',value,selectedTabText);

            toggleTabLoader(value.toLowerCase(), true);
            setDefaultCourse();
            console.log('course', courseDataArr, selectedStudCourseID);
            // checkCourseTypeHigherEd();
            if (courseDataArr && courseDataArr.length > 0) {
                let isCancelled = courseDataArr.some(
                    (obj) => obj.id === parseInt(selectedStudCourseID) && obj.status === 'Cancelled'
                );
                handleButtonTitleAttr('.updateCourse', !isCancelled);
            }
            if (selectedTabText === 'documents') {
                manageTabData();
            } else if (selectedTabText === 'activitylog') {
                manageActivityLogTab();
            }else if(selectedTabText === 'riskmatrix'){
                manageRiskMatrixTab();
            } else {
                setCourseListForTabContent(courseDataArr, value, true);
            }
        },
        error: function (err) {},
    });
    history.pushState({ path: newURL }, '', newURL);
}

function removeLastParameter(pathname, value) {
    const segments = pathname.split('/');
    if (segments.length > 3) {
        segments.pop();
    }
    return segments.join('/') + '/' + value;
}

function setCourseListForTabContent(courseDataArr, value = '', refetch = false) {
    var courseDropDownSelecter = '.header_course_list';

    if (value != '') {
        courseDropDownSelecter = '.' + value.toLowerCase() + '-li-detail .header_course_list';
    }
    $(courseDropDownSelecter).kendoDropDownList({
        dataTextField: 'course_title',
        dataValueField: 'id',
        template:
            '<div class="flex items-center justify-between course_list"><span class="k-state-default">#: data.course_title #</span><span class="k-icon k-i-check"></span></div>',
        valueTemplate: $('#courseDropdownList').html(),
        //dataSource: getDropdownDataSource('get-student-courses', {'student_id': studentId}),
        dataSource: courseDataArr,
        dataBound: function (e) {
            $('body').find('.overflowWrapper').css('overflow-y', 'auto');
            let tempBound = e.sender.dataSource._data;
            if (selectedStudCourseID == '') {
                if (tempBound.length > 1) {
                    // selectedStudCourseID = tempBound[1].id; // GNG-3771
                    const courseIDValue = new URLSearchParams(window.location.search).get(
                        'courseId'
                    );
                    selectedStudCourseID = courseIDValue ? courseIDValue : tempBound[1].id;
                    selectedDataArr = {
                        college_id: collegeId,
                        student_id: studentId,
                        student_course_id: selectedStudCourseID,
                    };
                    $('#student_id').val(studentId);
                    $('#student_course_id').val(selectedStudCourseID);
                    if (refetch) {
                        checkCourseTypeHigherEd(true);
                    }
                    let list = e.sender.dataSource._data;
                    if (list && list.length > 0) {
                        let isCancelled = list.some(
                            (obj) =>
                                obj.id === parseInt(selectedStudCourseID) &&
                                obj.status === 'Cancelled'
                        );
                        handleButtonTitleAttr('.updateCourse', !isCancelled);
                    }
                }
                this.select(1);
            } else {
                if (refetch) {
                    manageTabData();
                }
            }
            const tempArray = Object.values(tempBound);
            const index = tempArray.findIndex((course) => {
                return course.id === parseInt(selectedStudCourseID);
            });
            this.select(selectedStudCourseID === '' ? 1 : index);
        },
        select: function (e) {
            const status = e.dataItem.status;
            handleButtonTitleAttr('.updateCourse', status !== 'Cancelled');

            let contentMappings = {
                summary: '#studSummaryTab',
                course: '#studCourseTab',
                attendance: '#studAttendanceTab',
                timetable: '#studTimetableTab, #customDatePickerCalendar, #timetable-month',
                documents: '#studDocumentsTab',
                result: '#studResultTab',
                payments: '#studPaymentsTab',
                placement: '#studPlacementTab',
                activitylog: '#studActivityLogTab',
                riskmatrix: '#studRiskMatrixTab',
            };

            toggleContentLoader(contentMappings[selectedTabText], true);
            // if (e.dataItem.id == "0") {
            //     // notificationDisplay('Work under process', '', 'error');
            //     $(".enrollNewCourseBtn").click();
            //     //isHigherEd = false;
            // } else {
            //     selectedStudCourseID = e.dataItem.id;
            //     $("#student_id").val(studentId);
            //     $("#student_course_id").val(e.dataItem.id);
            //     selectedDataArr = {
            //         college_id: collegeId,
            //         student_id: studentId,
            //         student_course_id: e.dataItem.id,
            //     };
            //     $.cookie("selectedSubjectId", "");
            //     $.cookie("selectedBatchId", "");
            //     checkCourseTypeHigherEd();
            //     updateURLParameter("courseId", selectedStudCourseID);
            //     if (!isSyncing) {
            //         syncRadioWithDropdown(selectedStudCourseID);
            //     }
            // }
        },
        change: function (e) {
            isSameTab = true;
            let courseID = e.sender.value();
            let list = e.sender.dataSource._data;
            if (list && list.length > 0) {
                let isCancelled = list.some(
                    (obj) => obj.id === parseInt(courseID) && obj.status === 'Cancelled'
                );
                handleButtonTitleAttr('.updateCourse', !isCancelled);
            }

            if (courseID == '0') {
                $('.enrollNewCourseBtn').click();
            } else {
                selectedStudCourseID = courseID;
                $('#student_id').val(studentId);
                $('#student_course_id').val(courseID);
                selectedDataArr = {
                    college_id: collegeId,
                    student_id: studentId,
                    student_course_id: courseID,
                };
                $.cookie('selectedSubjectId', '');
                $.cookie('selectedBatchId', '');
                checkCourseTypeHigherEd();
                updateURLParameter('courseId', selectedStudCourseID);
                if (!isSyncing) {
                    syncRadioWithDropdown(selectedStudCourseID);
                }
            }
        },
    });
}

function checkCourseTypeHigherEd(flag = false) {
    ajaxActionV2(
        'api/verify-higher-ed-course-type',
        'POST',
        selectedDataArr,
        function (response) {
            //isHigherEd = (response.data.isHigherEd) ? true : false;
            isHigherEd = response.data.isHigherEd;
            isActiveCourse = response.data.isActiveCourse;
            isDeferCourse = response.data.isDeferCourse;
            // if (flag) {
            //     setActiveTabLoad();
            // } else {
            //     manageTabData();
            // }
            manageTabData();
        },
        false,
        function (flag) {
            if (isServerRequest && !isSameTab) {
                console.log('step 3',selectedTabText);
                toggleTabLoader(selectedTabText.toLowerCase(), flag);
            }
        }
    );
}
function getTabItem(id) {
    return $('#tabStrip')
        .data('kendoTabStrip')
        .tabGroup.children("li[id='" + id + "']")
        .index();
}
function manageTabData(tabOnly = false) {
    showDivsWithDelay();
    let functionMappings = {
        summary: 'manageSummaryTab',
        course: 'manageCourseTab',
        attendance: 'manageAttendanceTab',
        documents: 'manageDocumentsTab',
        timetable: 'manageTimetableTab',
        result: 'manageResultTab',
        payments: 'managePaymentsTab',
        placement: 'managePlacementTab',
        activitylog: 'manageActivityLogTab',
        riskmatrix: 'manageRiskMatrixTab',
    };
    window[functionMappings[selectedTabText]]();
    return false;
}

function manageUploadDiv() {
    const basePath = window.location.pathname.split('/')[1].includes('kendo')
        ? window.location.pathname.split('/')[1]
        : '';
    const baseUrl = window.location.origin + '/' + basePath;
    $(document)
        .find('#stud_profile_pic')
        .kendoUpload({
            async: {
                chunkSize: 20000000, // bytes
                saveUrl: baseUrl + 'api/upload-student-profile-pic',
                removeUrl: baseUrl + 'api/remove-student-profile-pic',
                //autoUpload: true,
            },
            upload: function (e) {
                let fileData = e.files[0];

                if (typeof fileData.validationErrors !== 'undefined') {
                    let errorData = fileData.validationErrors[0];
                    if (errorData == 'invalidMaxFileSize') {
                        notificationDisplay('File size must be less from 2MB.', '', 'error');
                    }
                    if (errorData == 'invalidFileExtension') {
                        notificationDisplay('Only allow file type jpg, jpeg or png', '', 'error');
                    }
                    return false;
                }
                e.data = selectedDataArr;
            },
            multiple: false,
            validation: {
                allowedExtensions: ['.jpg', '.JPG', '.png', '.PNG', '.jpeg', '.JPEG'],
                maxFileSize: 2000000, // 2mb
            },
            success: function (e) {
                if (e.response.status == 'success') {
                    notificationDisplay(e.response.message, '', e.response.status);
                }
                if (e.operation == 'upload') {
                    for (var i = 0; i < e.files.length; i++) {
                        let file = e.files[i].rawFile;
                        if (file) {
                            var reader = new FileReader();
                            reader.onloadend = function () {
                                $('.display_profile_pic').html(
                                    '<img src="' +
                                        this.result +
                                        '" / class="w-24 h-24 rounded-full">'
                                );
                                $('#profile_pic').val(this.result);
                            };
                            reader.readAsDataURL(file);
                        }
                    }
                }
            },
            remove: function (e) {
                e.data = selectedDataArr;
                let defaultImgPath = baseUrl + 'v2/img/user.png';
                $('.display_profile_pic').html(
                    '<img src="' + defaultImgPath + '" alt="searchIcon" />'
                );
                // notificationDisplay(
                //     "Uploaded image remove successfully.",
                //     "",
                //     "success",
                // );
            },
            error: function (e) {
                notificationDisplay(e.response.message, '', e.response.status);
            },
        });
}

function kendoWindowOpen(windowID, title = '') {
    let kendoWindow = $(document).find(windowID);
    if (title.length > 0) {
        kendoWindow.data('kendoWindow').title(title);
    }
    kendoWindow.getKendoWindow().open();
    kendoWindow
        .parent('div')
        .find('.k-window-titlebar')
        .addClass('titlebar-sms-modal gradientbackground')
        .find('.k-window-title')
        .addClass('text-lg font-medium leading-normal text-white');
}

function openCenterWindowWithHeight(
    titleText,
    widthVal = 34,
    heightVal = 25,
    topVal = 25,
    leftVal = 33
) {
    return {
        title: titleText,
        width: widthVal + '%',
        height: heightVal + '%',
        actions: ['close'],
        draggable: false,
        resizable: false,
        modal: true,
        position: {
            top: topVal + '%',
            left: leftVal + '%',
        },
        animation: {
            open: {
                effects: 'fade:in',
                duration: 300,
            },
            close: {
                effects: 'fade:out',
                duration: 300,
            },
        },
    };
}

function openCenterWindow(titleText, widthVal = 34, topVal = 25, leftVal = 33) {
    return {
        title: titleText,
        width: widthVal + '%',
        // height: "70%",
        actions: ['close'],
        draggable: false,
        resizable: false,
        modal: true,
        position: {
            top: topVal + '%',
            left: leftVal + '%',
        },
        animation: {
            open: {
                effects: 'fade:in',
                duration: 300,
            },
            close: {
                effects: 'fade:out',
                duration: 300,
            },
        },
    };
}

function triggerCalendar(inputId, format = dateFormatFrontSideJS) {
    $(inputId).on('click', function () {
        var datePicker = $(this).data('kendoDatePicker');
        datePicker.open();
        $(this).on('blur', function () {
            var inputDate = $(this).val();
            if (!inputDate) {
                return;
            }
            var isValidDate = kendo.parseDate(inputDate, format) !== null;
            // If not valid, reset to today's date
            if (!isValidDate) {
                datePicker.value(new Date());
                datePicker.trigger('change');
            }
        });
    });
}

function setKendoDatePicker(inputId, value = new Date(), format = dateFormatFrontSideJS) {
    triggerCalendar(inputId);
    $(inputId).kendoDatePicker({
        format: format,
        value: value,
    });
}

function setKendoTextArea(inputId, placeHolderText, rows = 5, value = '') {
    $(inputId).kendoTextArea({
        value: value,
        placeholder: placeHolderText,
        rows: rows,
    });
}

function getDropdown(id, value, apiURL, postArr = [], placeholder = '') {
    $('#' + id).kendoDropDownList({
        filter: 'contains',
        optionLabel: placeholder,
        filterInput: {
            width: '100%',
        },
        dataTextField: 'Name',
        dataValueField: 'Id',
        dataType: 'json',
        dataSource: getDropdownDataSource(apiURL, postArr),
        value: value,
    });
}
function getDropdownWithData(id, value, dataItems, postArr = [], placeholder = '') {
    $('#' + id).kendoDropDownList({
        filter: 'contains',
        optionLabel: placeholder,
        filterInput: {
            width: '100%',
        },
        dataTextField: 'Name',
        dataValueField: 'Id',
        dataType: 'json',
        dataSource: dataItems,
        value: value,
    });
}

function getDropdownBlank(id, value, apiURL, postArr = [], placeholder) {
    $('#' + id).kendoDropDownList({
        filter: 'contains',
        optionLabel: placeholder,
    });
}

function setDropdownWithoutAPI(inputField, dataText, dataValue, dataSource, selectedVal = '') {
    let dropDownList = $(inputField).kendoDropDownList({
        dataTextField: dataText,
        dataValueField: dataValue,
        dataSource: dataSource,
    });
    if (selectedVal.length > 0) {
        $(inputField).data('kendoDropDownList').value(selectedVal);
    } else if (dataSource.length > 0) {
        dropDownList.trigger('change');
    }
}

function formValidateAndReturnFormData(formId) {
    //var validator = $(formId).data("kendoValidator");
    let validator = defaultFormValidator(formId);
    if (!validator.validate()) {
        return false;
    }
    let tempArr = {
        college_id: collegeId,
        student_id: studentId,
        student_course_id: selectedStudCourseID,
    };
    let dataArr = getSerializeFormArray(formId, tempArr);
    return dataArr;
}

function getSerializeFormArray(formId, arr = []) {
    let serializeArr = $(document)
        .find(formId)
        .find(
            'input[name], select[name], textarea[name], input[type=checkbox], input[type=radio], :hidden'
        )
        .serializeArray();
    $(serializeArr).each(function (i, field) {
        if (arr.hasOwnProperty(field.name)) {
            if (!Array.isArray(arr[field.name])) {
                arr[field.name] = [arr[field.name]];
            }
            arr[field.name].push(field.value);
        } else {
            arr[field.name] = field.value;
        }
    });
    return arr;
}

function setKendoTimelineDataSource(url, dataArr) {
    return {
        transport: getTransportReadOnly(url, dataArr),
        schema: {
            model: {
                id: 'title',
                fields: {
                    date: {
                        type: 'date',
                    },
                },
            },
        },
        sort: { field: 'date', dir: 'desc' },
    };
}

function setKendoTimelineTitle(e, divId, emptyMsg = '') {
    let events = e.sender.dataSource._view;
    if (events.length == 0) {
        let displayEmptyMsg = emptyMsg == '' ? 'No records Found' : emptyMsg;
        $(document).find(divId).find('.k-timeline-events-list').remove();
        $(document)
            .find(divId)
            .find('.k-timeline-track ul')
            .html(
                '<li class="k-timeline-track-item k-timeline-flag-wrap"><span class="k-timeline-flag">' +
                    displayEmptyMsg +
                    '</span></li>'
            );
    }
    for (let i = 0; i < events.length; i++) {
        let titleText = events[i].unit_name;
        let dateLi = $(document)
            .find(divId)
            .find('.k-timeline-track-wrap')
            .find('ul li')
            .eq(i + 1)
            .find('.k-timeline-date');
        dateLi.html(
            '<span class="timeLineFont" style="font-family: Roboto !important;" title="' +
                titleText +
                '">' +
                titleText +
                '<hr>' +
                dateLi.text() +
                '</span>'
        );
        $(dateLi).parent().addClass('w-44');
        // $(dateLi).parent().addClass("truncate");
        $(dateLi).parent().addClass('text-center');
    }
}

function mergeWithSelectedDataArr(column, value) {
    return {
        [column]: value,
        college_id: collegeId,
        student_id: studentId,
        student_course_id: selectedStudCourseID,
    };
}

function onOpenDeleteDialog(gridID) {
    $(gridID).parent().find('.k-dialog-titlebar').addClass('gradientbackground');
    $(gridID)
        .parent()
        .find('.k-primary')
        .addClass(
            'text-white bg-primary-blue-500 hover:shadow-lg rounded-lg focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-indigo-500'
        );
}

function refreshGrid2(gridID) {
    // $(gridID).data('kendoGrid').refresh();
    if ($(gridID).data('kendoGrid')) {
        $(gridID).data('kendoGrid').dataSource.read();
    }
}

function setModalFooterTemplate(
    btnText = 'Save',
    btnType = 'submit',
    btnClass = '',
    isDisabled = false
) {
    return `<div class="modal-footer w-full inline-flex space-x-4 items-center justify-end px-1 py-1">
                <button type="button" class="btn-secondary cancelBtn">
                    <p class="text-sm font-medium leading-4 text-gray-700">Cancel</p>
                </button>
                <button type="${btnType}" class="${btnClass} btn-primary" ${
                    isDisabled ? 'disabled' : ''
                }>
                    <p class="text-sm font-medium leading-4 text-white">${btnText}</p>
                </button>
            </div>`;
}

function setWindowFooterTemplate(
    btnText = 'SAVE',
    btnType = 'submit',
    btnClass = '',
    isDisabled = false
) {
    return `<div class="modal-footer w-full inline-flex space-x-4 items-center justify-end py-5 bottom-0 right-0 fixed border-t bg-white px-6">
                <div class="float-right flex space-x-4 items-center justify-end">
                    <button type="button" class="btn-secondary px-6 py-2 cancelBtn">
                        <p class="text-sm font-medium leading-4 text-gray-700">CANCEL</p>
                    </button>
                    <button type="${btnType}" class="${btnClass} btn-primary px-3 py-2" ${
                        isDisabled ? 'disabled' : ''
                    } title="${isDisabled ? 'Required field are not filled' : btnText}">
                        <p class="text-sm font-medium leading-4 text-white">${btnText}</p>
                    </button>
                </div>
            </div>`;
}

function setCourseListWhenStatusUpdate(data) {
    hasCourseChange = true;
    var headerCourseListValue = $(document)
        .find('select.header_course_list')
        .data('kendoDropDownList')
        .value();
    courseDataArr = data.mainArray;
    setCourseListForTabContent(data.mainArray, '', false);
    $(document)
        .find(".header_course_list[data-role='dropdownlist']")
        .each(function () {
            $(this).data('kendoDropDownList').setDataSource(data.mainArray);
            $(this).data('kendoDropDownList').value(headerCourseListValue);
        });
    $(document).find('.NewCourse').html();
    let htmlCourse = '';
    $.each(data.mainArray, function (index, row) {
        if (index != 0) {
            let color = defaultStudentCourseStatusColor(row.status);
            htmlCourse += `
                <label class="course-container flex items-start space-x-2 border border-gray-300 p-2 rounded-md"
                       data-course="${row.id}">
                    <input type="radio" name="selected_course" value="${row.id}"
                           data-index="${index}" class="k-radio">
                    <div class="flex items-center justify-between w-full">
                        <div class="space-y-1">
                        <span class="text-xs leading-4 text-gray-700">${row.course_title}</span>
                        <span class="text-xs text-gray-600 italic">${row.start_date} - ${row.finish_date}</span>
                        </div>
                        <div class="flex items-center justify-center px-2.5 py-0.5 bg-${color}-100 rounded-md">
                            <p class="text-xs leading-4 text-center text-${color}-800 truncate">${row.status}</p>
                        </div>
                    </div>
                </label>`;
        }
    });
    $(document).find('.NewCourse').html(htmlCourse);
    setDefaultCourse();

    ajaxActionV2(
        'api/get-student-summary-tab',
        'POST',
        selectedDataArr,
        function (response) {
            let cDetail = response.data.course_detail;
            $(document)
                .find('#studCourseTab')
                .html(
                    kendo.template($('#studCourseTemplate').html())({
                        arr: cDetail,
                    })
                );
            $(document).find('#courseCampus').text(cDetail.campus_name);
        },
        false,
        function (flag) {
            toggleContentLoader('#studSummaryTab', flag);
        }
    );

    ajaxActionV2('api/get-current-course-summary', 'POST', selectedDataArr, function (response) {
        let responseArr = {
            data: response.data.currentCourseSummary,
            getResultCalculationMethod: response.data.getResultCalculationMethod,
            studentDetails: response.data.studentDetails,
        };
        $(document)
            .find('.studentCourseDetailHeader')
            .html(kendo.template($('#studentCourseDetailHeaderTemplate').html())(responseArr));
        $(document)
            .find('.studentAcademicSummaryDetails')
            .html(kendo.template($('#studentAcademicSummaryTemplate').html())(responseArr));
    });

    // Update course status/date with bg-color
    //loadCourseTab();
}

function toggleActionMenu(buttonDivId, actionListDiv) {
    let checkHtml = $(document).find(actionListDiv);
    if (checkHtml.hasClass('active')) {
        //checkHtml.slideUp(200);
        checkHtml.removeClass('active');
        $(buttonDivId).find('.viewAllActions').removeClass('active');
    } else {
        //checkHtml.slideDown(200);
        checkHtml.addClass('active');
        $(buttonDivId).find('.viewAllActions').addClass('active');
    }
}
const initUsiFormEvents = () => {
    const singleSelect = $(document).find('#singlename');
    $(singleSelect).on('change', function (e) {
        console.log('in');
        const checked = document.querySelector('#singlename')?.checked;
        const familyNameWrapper = document.querySelector('#family_name_wrapper');
        const lastname = document.querySelector('#usiDetailsForm #lastname');
        var lastNameVal = $('#lastname').val();
        var firstNameVal = $('#firstname').val();

        if (!familyNameWrapper) return;
        if (checked) {
            $('#first_name_wrapper').find('#firstNameLabel').text('');
            $('#first_name_wrapper')
                .find('#firstNameLabel')
                .text(firstNameVal + ' ' + lastNameVal);
            $('#first_name_wrapper')
                .find('#firstname')
                .val(firstNameVal + ' ' + lastNameVal);
            $('#first_name_wrapper').find('.firstNameInputLabel').text('Single Name');

            familyNameWrapper.classList.add('hidden');
            lastname.removeAttribute('required');
        } else {
            $('#first_name_wrapper').find('#firstNameLabel').text('');
            $('#first_name_wrapper').find('#firstNameLabel').text(firstNameVal);
            $('#first_name_wrapper').find('#firstname').val(firstNameVal);
            $('#first_name_wrapper').find('.firstNameInputLabel').text('First Name');
            familyNameWrapper.classList.remove('hidden');
            lastname.setAttribute('required', 'true');
        }
    });
};

var myEditor21;
var myEditorFlag = true;
function setEditorHtml() {
    if (myEditorFlag) {
        setTimeout(() => {
            // ClassicEditor.create(document.querySelector("#quickAddNewNote"))
            //     .then((editor) => {
            //         editor.ui.view.editable.element.style.height = "150px";
            //         myEditor21 = editor;
            //     })
            //     .catch((error) => {
            //         console.error(error);
            //     });
            CKEDITOR.ClassicEditor.defaultConfig = {
                toolbar: {
                    items: [
                        'heading',
                        '|',
                        'bold',
                        '|',
                        'italic',
                        '|',
                        'bulletedList',
                        'numberedList',
                        '|',
                        'insertTable',
                        '|',
                        'blockQuote',
                        '|',
                        'undo',
                        'redo',
                    ],
                },
                image: {
                    toolbar: ['imageStyle:full', 'imageStyle:side', '|', 'imageTextAlternative'],
                },
                table: {
                    contentToolbar: ['tableColumn', 'tableRow', 'mergeTableCells'],
                },
                language: 'en',
            };
            CKEDITOR.ClassicEditor.create(document.querySelector('#quickAddNewNote'), {
                ckfinder: {
                    uploadUrl: site_url + 'api/upload-file-email-text-editor',
                },
                mention: {
                    feeds: [
                        {
                            marker: '{',
                            feed: window.tagJson,
                            minimumCharacters: 0,
                        },
                    ],
                },
                removePlugins: [
                    'RealTimeCollaborativeComments',
                    'RealTimeCollaborativeTrackChanges',
                    'RealTimeCollaborativeRevisionHistory',
                    'PresenceList',
                    'Comments',
                    'TrackChanges',
                    'TrackChangesData',
                    'RevisionHistory',
                    'Pagination',
                    'WProofreader',
                    'MathType',
                ],
            })
                .then((editor) => {
                    editor.ui.view.editable.element.style.height = '150px';
                    myEditor21 = editor;
                })
                .catch((error) => {
                    console.error(error);
                });
            myEditorFlag = false;
        }, 300);
    } else {
        setTimeout(() => {
            $(document).find('#addNoteModal').find('.addNoteDiv').toggleClass('p-0.5');
        }, 500);
    }
}

(function ($) {
    jQuery.fn.hasAttr = function (attribute) {
        return this.attr(attribute) !== undefined;
    };
})(jQuery);

$('body').on('click', '#studProfilePic', function (e) {
    e.preventDefault();
    let name = $('.getStudentName').text();
    kendoWindowOpen('#displayProfilePicModal');
    $('#displayProfilePicModal').prev().find('.k-window-title').text(name);
});

$('body').on('keypress', '.numberOnly', function (e) {
    let charCode = e.which ? e.which : event.keyCode;
    if (String.fromCharCode(charCode).match(/[^0-9]/g)) {
        return false;
    }
});

$('body').on('keypress', '.amountOnly', function (e) {
    let charCode = e.which ? e.which : event.keyCode;
    // Allow numbers, decimal point, and backspace
    if (String.fromCharCode(charCode).match(/[^0-9\.]/g)) {
        return false;
    }
    // Allow only one decimal point
    if ($(this).val().indexOf('.') !== -1 && String.fromCharCode(charCode) === '.') {
        return false;
    }
});

$('body').on('click', '.quickAddNoteButton', function (e) {
    e.preventDefault();
    setEditorHtml();

    $('#addNoteForm')[0].reset();
    $('.attachFile').text('Attach Files');
    $('#note_student_id').val(studentId);
    kendoWindowOpen('#addNoteModal');
    /*if(myEditorFlag){
        setTimeout(function (){
            ClassicEditor.create(document.querySelector('#quickAddNewNote'))
                .then(editor => {
                    editor.ui.view.editable.element.style.height = 'auto';
                    myEditor22 = editor;
                })
                .catch(error => {
                    console.error(error);
                });
            myEditorFlag = false;
        },300);
    }*/
});

$('body').on('click', '.previewBtnNote', function (e) {
    e.preventDefault();
    let filepath = $(this).attr('data-file-path');
    let previewId = $(this).attr('data-preview-id');
    let previewDetails = kendo.template($('#previewTemplate').html())({
        previewId: previewId,
    });
    $(document).find('#previewDocumentModalNote').html(previewDetails);
    previewDocument(previewId, filepath);
    kendoWindowOpen('#previewDocumentModalNote');
});

$('body').on('change', '.student_type', function (e) {
    e.preventDefault();
    ajaxActionV2(
        'api/get-visa-status',
        'POST',
        { student_type: $(this).val() },
        function (response) {
            let select = $('#visa_status');
            select.empty();
            $.each(response.data, function (index, option) {
                let optionItem = '<option value="' + option.id + '">' + option.name + '</option>';
                select.append(optionItem);
            });
        }
    );
});
$('body').on('click', '.edit_profile', async function (e) {
    // TODO TEMPLATE:RENDER
    // await TemplateLoader.loadTemplatesByCategory("user");
    e.preventDefault();
    ajaxActionV2('api/get-student-details', 'POST', selectedDataArr, function (response) {
        let arr = response.data.student_detail;
        let responseArr = { arr: arr, data: response.data };
        let userIcon = kendo.template($('#userIconTemplate').html())(responseArr);
        let basicInfo = kendo.template($('#basicInfoTemplate').html())(responseArr);
        let passportDetails = kendo.template($('#passportDetailsTemplate').html())(responseArr);
        let visaDetails = kendo.template($('#visaDetailsTemplate').html())(responseArr);
        let residentialAddress = kendo.template($('#residentialAddressTemplate').html())(
            responseArr
        );
        let postalAddress = kendo.template($('#postalAddressTemplate').html())(responseArr);
        let permanentAddress = kendo.template($('#permanentAddressTemplate').html())(responseArr);
        let contactDetails = kendo.template($('#contactDetailsTemplate').html())(responseArr);
        let emergencyDetails = kendo.template($('#emergencyDetailsTemplate').html())(responseArr);
        let surveyDetails = kendo.template($('#surveyDetailsTemplate').html())(responseArr);
        let usiDetails = kendo.template($('#usiDetailsTemplate').html())(responseArr);

        $(document).find('.user-display-name').text(arr.full_name);
        $(document).find('.user-display-generated-id').text(arr.generated_stud_id);
        $(document).find('.user-display-id-copy').data('copy', arr.generated_stud_id);
        $(document).find('#userIconTab').html(userIcon);
        $(document).find('#basicInfoTab').html(basicInfo);
        $(document).find('#passportDetailsTab').html(passportDetails);
        $(document).find('#visaDetailsTab').html(visaDetails);
        $(document).find('#residentialAddressTab').html(residentialAddress);
        $(document).find('#postalAddressTab').html(postalAddress);
        $(document).find('#permanentAddressTab').html(permanentAddress);
        $(document).find('#contactDetailsTab').html(contactDetails);
        $(document).find('#emergencyDetailsTab').html(emergencyDetails);
        $(document).find('#surveyDetailsTab').html(surveyDetails);
        $(document).find('#usiDetailsTab').html(usiDetails);

        let editStudentModal = $(document).find('#editStudentModal');
        var wwidth = $(document).width();
        var sswidth = $('.student-profile-left-sidebar').width() || 256;
        var swidth = $('#tw-sidebar').width() + 16 + sswidth;
        var twidth = wwidth - swidth;
        var windoWidth = '';
        var windoLeft = '';
        windoWidth = twidth + 'px';
        windoLeft = swidth + 'px';
        editStudentModal.getKendoWindow().setOptions({
            width: windoWidth,
            position: { left: windoLeft, top: '0' },
        });
        editStudentModal.getKendoWindow().open();
        editStudentModal
            .parent('div')
            .find('.k-window-titlebar')
            .addClass('titlebar-sms-modal gradientbackground')
            .find('.k-window-title')
            .addClass('text-lg font-medium leading-normal text-white');
        editStudentModal.find('.viewDetailTab').removeClass('active');
        editStudentModal.find('.viewDetailTab:first').addClass('active');
        editStudentModal.find('.editStudentModal-wrap').animate({ scrollTop: 0 }, 500);
        setTimeout(function () {
            $('#visa_expiry_date').kendoDatePicker({
                format: dateFormatFrontSideJS,
            });
            $('#passport_expiry').kendoDatePicker({
                format: dateFormatFrontSideJS,
            });
            $('#dob').kendoDatePicker({ format: dateFormatFrontSideJS });
            ajaxActionV2('api/get-country-list', 'POST', {}, function (response) {
                getDropdownWithData('country_of_birth', arr.birth_country, response.data);
                getDropdownWithData('current_country', arr.current_country, response.data);
                getDropdownWithData('postal_country', arr.postal_country, response.data);
                getDropdownWithData('permanent_country', arr.permanent_country, response.data);
                getDropdownWithData('nationality_of_birth', arr.nationality, response.data);
            });
        }, 1000);
    });
});

$('body').on('click', '#addressHistoryBtn', function (e) {
    let dataArr = {
        college_id: collegeId,
        student_id: studentId,
        type: 'student_address',
    };
    if (addressHistoryFlag) {
        addressHistoryFlag = false;
        $(addressHistoryGrid).kendoGrid({
            dataSource: customDataSource(
                'api/get-student-address-history',
                {
                    updated_field: { type: 'string' },
                    updated_on: { type: 'date' },
                    updated_by: { type: 'string' },
                },
                dataArr
            ),
            columns: [
                {
                    field: 'updated_field',
                    title: 'Address',
                    minResizableWidth: 100,
                },
                {
                    field: 'updated_on',
                    title: 'Updated On',
                    minResizableWidth: 100,
                    template:
                        "<div class=' flex items-center text-13 leading-4 text-gray-600'>#: kendo.toString(updated_on,'" +
                        dateFormatFrontSideJS +
                        "') # </div>",
                },
                {
                    field: 'updated_by',
                    title: 'Updated By',
                    minResizableWidth: 100,
                },
            ],
            noRecords: noRecordTemplate(),
        });
        customGridHtml(addressHistoryGrid);
    } else {
        refreshGrid(addressHistoryGrid, dataArr);
    }
    setTimeout(() => {
        kendowindowOpen(addressHistoryModal);
    }, 500);
});

$('body').on('click', '#emergencyDetailsHistoryBtn', function (e) {
    let dataArr = {
        college_id: collegeId,
        student_id: studentId,
        type: 'student_emergency_details',
    };
    if (emergencyDetailsHistoryFlag) {
        emergencyDetailsHistoryFlag = false;
        $(emergencyDetailsHistoryGrid).kendoGrid({
            dataSource: customDataSource(
                'api/get-student-emergency-details-history',
                {
                    value: { type: 'string' },
                    updated_on: { type: 'date' },
                    updated_by: { type: 'string' },
                },
                dataArr
            ),
            columns: [
                {
                    field: 'value',
                    title: 'Properties',
                    template: function (dataItem) {
                        return manageProperties(dataItem.properties);
                    },
                    width: 500,
                },
                {
                    field: 'updated_on',
                    title: 'Updated On',
                    width: 100,
                    template:
                        "<div class=' flex items-center text-13 leading-4 text-gray-600'>#: kendo.toString(updated_on,'" +
                        dateFormatFrontSideJS +
                        "') # </div>",
                },
                {
                    field: 'updated_by',
                    title: 'Updated By',
                    width: 100,
                    minResizableWidth: 100,
                },
            ],
            noRecords: noRecordTemplate(),
        });
        customGridHtml(emergencyDetailsHistoryGrid);
    } else {
        refreshGrid(emergencyDetailsHistoryGrid, dataArr);
    }
    setTimeout(() => {
        kendowindowOpen(emergencyDetailsHistoryModal);
    }, 500);
});

/*$("body").on("click", "input[name=student_type]:radio", function (e) {
    e.preventDefault();
    $(".student_type_class").removeClass(
        "border-primary-blue-200 bg-primary-blue-50 z-10",
    );
    if ($(this).val() == "Offshore") {
        $(this)
            .parent("label")
            .addClass("border-primary-blue-200 bg-primary-blue-50 z-10");
    } else if ($(this).val() == "Onshore") {
        $(this)
            .parent("label")
            .addClass("border-primary-blue-200 bg-primary-blue-50 z-10");
    } else if ($(this).val() == "Domestic") {
        $(this)
            .parent("label")
            .addClass("border-primary-blue-200 bg-primary-blue-50 z-10");
    }
});*/

$('body').on('change', 'input[name=student_type]:radio', function (e) {
    $('.student_type_check').removeClass('border-primary-blue-200 bg-primary-blue-50 z-10');
    if (e.target.value == 'Offshore') {
        $(this).parent().parent('li').addClass('border-primary-blue-200 bg-primary-blue-50 z-10');
    } else if (e.target.value == 'Onshore') {
        $(this).parent().parent('li').addClass('border-primary-blue-200 bg-primary-blue-50 z-10');
    } else if (e.target.value == 'Domestic') {
        $(this).parent().parent('li').addClass('border-primary-blue-200 bg-primary-blue-50 z-10');
    }
});

$('body').on('click', '.editBasicInfoBtn', function (e) {
    e.preventDefault();
    $(document).find('.basicInfo').addClass('hidden');
    $(document).find('.editBasicInfo').removeClass('hidden');
    manageUploadDiv();
});

$('body').on('click', '.backToBasicInfo', function (e) {
    e.preventDefault();
    $(document).find('.basicInfo').removeClass('hidden');
    $(document).find('.editBasicInfo').addClass('hidden');
});

$('body').on('click', '.saveBasicInfo', function (e) {
    e.preventDefault();
    let validator = defaultFormValidator('#basicInfoForm');
    if (!validator.validate()) {
        return false;
    }
    let formData = getSerializeFormArray('#basicInfoForm', {
        student_id: studentId,
    });
    ajaxActionV2('api/update-student-details', 'POST', formData, function (response) {
        notificationDisplay('BasicInfo save successfully.', '', 'success');
        let basicInfo = kendo.template($('#basicInfoTemplate').html())({
            arr: response.data,
        });
        $('.user-display-name').text(
            response.data.first_name +
                ' ' +
                (response.data.family_name ? response.data.family_name : '')
        );
        $('#getStudentName').text(
            response.data.first_name +
                ' ' +
                (response.data.family_name ? response.data.family_name : '')
        );
        $('#studentNameTitle').text(
            response.data.first_name +
                ' ' +
                (response.data.family_name ? response.data.family_name : '')
        );
        $(document).find('#basicInfoTab').html(basicInfo);
        $(document).find('.basicInfo').removeClass('hidden');
        $(document).find('.editBasicInfo').addClass('hidden');

        if (typeof response.data.DOB !== 'undefined' && response.data.DOB !== null) {
            $('#dob').kendoDatePicker({
                format: dateFormatFrontSideJS,
                value: response.data.DOB,
            });
        }
        console.log('one', response.data.DOB);

        updateStudentDetailSummaryTab();
    });
});

$('body').on('click', '.editPassportDetailsBtn', function (e) {
    e.preventDefault();
    $(document).find('.passportDetails').addClass('hidden');
    $(document).find('.editPassportDetails').removeClass('hidden');
});

$('body').on('click', '.backToPassportDetails', function (e) {
    e.preventDefault();
    $(document).find('.passportDetails').removeClass('hidden');
    $(document).find('.editPassportDetails').addClass('hidden');
});

$('body').on('click', '.savePassportDetails', function (e) {
    e.preventDefault();
    let validator = defaultFormValidator('#passportDetailsForm');
    if (!validator.validate()) {
        return false;
    }
    let countryName = $('#country_of_birth').data('kendoDropDownList').text();
    $(document).find('#birth_country_name').val(countryName);
    let nationality = $('#nationality_of_birth').data('kendoDropDownList').text();
    $(document).find('#birth_nationality_name').val(nationality);

    let formData = getSerializeFormArray('#passportDetailsForm', {
        student_id: studentId,
    });
    ajaxActionV2('api/update-student-details', 'POST', formData, function (response) {
        notificationDisplay('PassportDetails save successfully.', '', 'success');
        let residentialAddress = kendo.template($('#passportDetailsTemplate').html())({
            arr: response.data,
        });
        $(document).find('#passportDetailsTab').html(residentialAddress);
        getDropdown('country_of_birth', response.data.country_of_birth, 'get-country-list');
        getDropdown('nationality_of_birth', response.data.nationality, 'get-nationality');
        $('#passport_expiry').kendoDatePicker({
            format: dateFormatFrontSideJS,
        });
        $(document).find('.passportDetails').removeClass('hidden');
        $(document).find('.editPassportDetails').addClass('hidden');
        updateStudentDetailSummaryTab();
    });
});

$('body').on('click', '.editVisaDetailsBtn', function (e) {
    e.preventDefault();
    $(document).find('.visaDetails').addClass('hidden');
    $(document).find('.editVisaDetails').removeClass('hidden');
});

$('body').on('click', '.backToVisaDetails', function (e) {
    e.preventDefault();
    $(document).find('.visaDetails').removeClass('hidden');
    $(document).find('.editVisaDetails').addClass('hidden');
});

$('body').on('click', '.saveVisaDetails', function (e) {
    e.preventDefault();
    let validator = defaultFormValidator('#visaDetailsForm');
    if (!validator.validate()) {
        return false;
    }
    let formData = getSerializeFormArray('#visaDetailsForm', {
        student_id: studentId,
    });
    ajaxActionV2('api/update-student-details', 'POST', formData, function (response) {
        notificationDisplay('VisaDetails save successfully.', '', 'success');
        let residentialAddress = kendo.template($('#visaDetailsTemplate').html())({
            arr: response.data,
        });
        $(document).find('#visaDetailsTab').html(residentialAddress);
        $('#visa_expiry_date').kendoDatePicker({
            format: dateFormatFrontSideJS,
        });
        $(document).find('.visaDetails').removeClass('hidden');
        $(document).find('.editVisaDetails').addClass('hidden');
        updateStudentDetailSummaryTab();
    });
});

$('body').on('click', '.editResidentialAddressBtn', function (e) {
    e.preventDefault();
    $(document).find('.residentialAddress').addClass('hidden');
    $(document).find('.editResidentialAddress').removeClass('hidden');
    initializeAutocomplete();
});

$('body').on('click', '.backToResidentialAddress', function (e) {
    e.preventDefault();
    $(document).find('.residentialAddress').removeClass('hidden');
    $(document).find('.editResidentialAddress').addClass('hidden');
});

$('body').on('click', '.saveResidentialAddress', function (e) {
    e.preventDefault();
    let validator = defaultFormValidator('#residentialAddressForm');
    if (!validator.validate()) {
        return false;
    }
    let countryName = $('#current_country').data('kendoDropDownList').text();
    $(document).find('#current_country_name').val(countryName);
    let formData = getSerializeFormArray('#residentialAddressForm', {
        student_id: studentId,
    });
    ajaxActionV2('api/update-student-details', 'POST', formData, function (response) {
        notificationDisplay('ResidentialAddress save successfully.', '', 'success');
        let residentialAddress = kendo.template($('#residentialAddressTemplate').html())({
            arr: response.data,
        });
        $(document).find('#residentialAddressTab').html(residentialAddress);
        getDropdown('current_country', response.data.current_country, 'get-country-list');
        $(document).find('.residentialAddress').removeClass('hidden');
        $(document).find('.editResidentialAddress').addClass('hidden');
        updateStudentDetailSummaryTab();
    });
});
$('body').on('click', '#profileHistoryBtn', function (e) {
    let dataArr = {
        college_id: collegeId,
        student_id: studentId,
        type: 'student_email_phone',
    };
    if (profileHistoryFlag) {
        profileHistoryFlag = false;
        $(profileHistoryGrid).kendoGrid({
            dataSource: customDataSource(
                'api/get-student-address-history',
                {
                    updated_on: { type: 'date' },
                    updated_value: { type: 'string' },
                    updated_by: { type: 'string' },
                },
                dataArr
            ),
            columns: [
                {
                    field: 'updated_value',
                    title: 'Properties',
                    width: 500,
                    template: function (dataItem) {
                        return manageProperties(dataItem.properties);
                    },
                },
                {
                    field: 'updated_on',
                    title: 'Updated On',
                    width: 100,
                    template:
                        "<div class=' flex items-center text-13 leading-4 text-gray-600'>#: kendo.toString(updated_on,'" +
                        dateFormatFrontSideJS +
                        "') # </div>",
                },
                {
                    width: 100,
                    field: 'updated_by',
                    title: 'Updated By',
                },
            ],
            noRecords: noRecordTemplate(),
        });
        customGridHtml(profileHistoryGrid);
    } else {
        refreshGrid(profileHistoryGrid, dataArr);
    }
    setTimeout(() => {
        kendowindowOpen(profileHistoryModal);
    }, 500);
});

$('body').on('click', '.editPostalAddressBtn', function (e) {
    e.preventDefault();
    $(document).find('.postalAddress').addClass('hidden');
    $(document).find('.editPostalAddress').removeClass('hidden');
    initializeAutocomplete();
});

$('body').on('click', '.backToPostalAddress', function (e) {
    e.preventDefault();
    $(document).find('.postalAddress').removeClass('hidden');
    $(document).find('.editPostalAddress').addClass('hidden');
});

$('body').on('click', '.savePostalAddress', function (e) {
    e.preventDefault();
    let validator = defaultFormValidator('#postalAddressForm');
    if (!validator.validate()) {
        return false;
    }
    let countryName = $('#postal_country').data('kendoDropDownList').text();
    $(document).find('#postal_country_name').val(countryName);
    let formData = getSerializeFormArray('#postalAddressForm', {
        student_id: studentId,
    });
    ajaxActionV2('api/update-student-details', 'POST', formData, function (response) {
        notificationDisplay('PostalAddress save successfully.', '', 'success');
        let postalAddress = kendo.template($('#postalAddressTemplate').html())({
            arr: response.data,
        });
        $(document).find('#postalAddressTab').html(postalAddress);
        getDropdown('postal_country', response.data.postal_country, 'get-country-list');
        $(document).find('.postalAddress').removeClass('hidden');
        $(document).find('.editPostalAddress').addClass('hidden');
        updateStudentDetailSummaryTab();
    });
});
function manageProperties(dataItem) {
    let result = '';
    result += "<div class='log-grid mb-6'>";
    for (let j = 0; j < dataItem.length; j++) {
        result +=
            "<div class='grid grid-cols-6 gap-4 border-b border-gray-200 py-2 px-2'>" +
            "<div class='col-span-2'>" +
            "<div class='tw-badge inline-flex items-center justify-center px-3 py-1 w-fit text-xs border-none leading-normal bg-primary-blue-500 text-white rounded-md'>" +
            dataItem[j]['field_name'] +
            '</div>' +
            '</div>' +
            "<div class='col-span-4'>" +
            "<div class='flex items-center gap-2'>" +
            "<div class='tw-badge inline-flex items-center justify-center px-3 py-1 w-fit text-xs border-none leading-normal bg-green-50 rounded-md text-gray-600'>" +
            dataItem[j]['old'] +
            '</div>' +
            "<svg width='14' height='14' viewBox='0 0 20 20' fill='none' xmlns='http://www.w3.org/2000/svg'>" +
            "<path fill='currentColor' d='M10.837 3.13a.5.5 0 0 0-.674.74L16.33 9.5H2.5a.5.5 0 0 0 0 1h13.828l-6.165 5.628a.5.5 0 0 0 .674.739l6.916-6.314a.747.747 0 0 0 0-1.108z'></path>" +
            '</svg>' +
            "<div class='tw-badge inline-flex items-center justify-center px-3 py-1 w-fit text-xs border-none leading-normal bg-red-50 rounded-md text-gray-400'>" +
            dataItem[j]['attributes'] +
            '</div>' +
            '</div>' +
            '</div>' +
            '</div>';
    }

    result += '</div>';

    return result;
}
$('body').on('click', '.editPermanentAddressBtn', function (e) {
    e.preventDefault();
    $(document).find('.permanentAddress').addClass('hidden');
    $(document).find('.editPermanentAddress').removeClass('hidden');
});

$('body').on('click', '.backToPermanentAddress', function (e) {
    e.preventDefault();
    $(document).find('.permanentAddress').removeClass('hidden');
    $(document).find('.editPermanentAddress').addClass('hidden');
});

$('body').on('click', '.savePermanentAddress', function (e) {
    e.preventDefault();
    let validator = defaultFormValidator('#permanentAddressForm');
    if (!validator.validate()) {
        return false;
    }
    let countryName = $('#permanent_country').data('kendoDropDownList').text();
    $(document).find('#permanent_country_name').val(countryName);
    let formData = getSerializeFormArray('#permanentAddressForm', {
        student_id: studentId,
    });
    ajaxActionV2('api/update-student-details', 'POST', formData, function (response) {
        notificationDisplay('PermanentAddress save successfully.', '', 'success');
        let usiDetails = kendo.template($('#permanentAddressTemplate').html())({
            arr: response.data,
        });
        $(document).find('#permanentAddressTab').html(usiDetails);
        getDropdown('permanent_country', response.data.permanent_country, 'get-country-list');
        $(document).find('.permanentAddress').removeClass('hidden');
        $(document).find('.editPermanentAddress').addClass('hidden');
        updateStudentDetailSummaryTab();
    });
});

$('body').on('click', '.editContactDetailsBtn', function (e) {
    e.preventDefault();
    $(document).find('.contactDetails').addClass('hidden');
    $(document).find('.editContactDetails').removeClass('hidden');
});

$('body').on('click', '.backToContactDetails', function (e) {
    e.preventDefault();
    $(document).find('.contactDetails').removeClass('hidden');
    $(document).find('.editContactDetails').addClass('hidden');
});

$('body').on('click', '.saveContactDetails', function (e) {
    e.preventDefault();
    let validator = defaultFormValidator('#contactDetailsForm');
    if (!validator.validate()) {
        return false;
    }
    let formData = getSerializeFormArray('#contactDetailsForm', {
        student_id: studentId,
    });
    ajaxActionV2('api/update-student-details', 'POST', formData, function (response) {
        if (response.status == 'error') {
            notificationDisplay(response.message, '', response.status);
            return false;
        }
        notificationDisplay('ContactDetails save successfully.', '', 'success');
        let usiDetails = kendo.template($('#contactDetailsTemplate').html())({
            arr: response.data,
        });
        $(document).find('#contactDetailsTab').html(usiDetails);
        $(document).find('.contactDetails').removeClass('hidden');
        $(document).find('.editContactDetails').addClass('hidden');
        updateStudentDetailSummaryTab();
    });
});

$('body').on('click', '.editEmergencyDetailsBtn', function (e) {
    e.preventDefault();
    $(document).find('.emergencyDetails').addClass('hidden');
    $(document).find('.editEmergencyDetails').removeClass('hidden');
});

$('body').on('click', '.backToEmergencyDetails', function (e) {
    e.preventDefault();
    $(document).find('.emergencyDetails').removeClass('hidden');
    $(document).find('.editEmergencyDetails').addClass('hidden');
});

$('body').on('click', '.saveEmergencyDetails', function (e) {
    e.preventDefault();
    let validator = defaultFormValidator('#emergencyDetailsForm');
    if (!validator.validate()) {
        return false;
    }
    let formData = getSerializeFormArray('#emergencyDetailsForm', {
        student_id: studentId,
    });
    ajaxActionV2('api/update-additional-students', 'POST', formData, function (response) {
        notificationDisplay('EmergencyDetails save successfully.', '', 'success');
        let emergencyDetails = kendo.template($('#emergencyDetailsTemplate').html())({
            arr: response.data,
        });
        $(document).find('#emergencyDetailsTab').html(emergencyDetails);
        $(document).find('.emergencyDetails').removeClass('hidden');
        $(document).find('.editEmergencyDetails').addClass('hidden');
        updateStudentDetailSummaryTab();
    });
});

$('body').on('click', '.editSurveyDetailsBtn', function (e) {
    e.preventDefault();
    $(document).find('.surveyDetails').addClass('hidden');
    $(document).find('.editSurveyDetails').removeClass('hidden');
});
$('body').on('click', '.backToSurveyDetails', function (e) {
    e.preventDefault();
    $(document).find('.surveyDetails').removeClass('hidden');
    $(document).find('.editSurveyDetails').addClass('hidden');
});

$('body').on('click', '.saveSurveyDetails', function (e) {
    e.preventDefault();
    let validator = defaultFormValidator('#surveyDetailsForm');
    if (!validator.validate()) {
        return false;
    }
    let formData = getSerializeFormArray('#surveyDetailsForm', {
        student_id: studentId,
    });
    ajaxActionV2('api/update-additional-students', 'POST', formData, function (response) {
        notificationDisplay('SurveyDetails save successfully.', '', 'success');
        console.log(response);
        let surveyDetails = kendo.template($('#surveyDetailsTemplate').html())({
            arr: response.data,
            data: response.data,
        });
        $(document).find('#surveyDetailsTab').html(surveyDetails);
        $(document).find('.surveyDetails').removeClass('hidden');
        $(document).find('.editSurveyDetails').addClass('hidden');
        updateStudentDetailSummaryTab();
    });
});

$('body').on('click', '.editUsiDetailsBtn', function (e) {
    e.preventDefault();
    $(document).find('.usiDetails').addClass('hidden');
    $(document).find('.editUsiDetails').removeClass('hidden');
    initUsiFormEvents(e);
});

$('body').on('click', '.backToUsiDetails', function (e) {
    e.preventDefault();
    $(document).find('.usiDetails').removeClass('hidden');
    $(document).find('.editUsiDetails').addClass('hidden');
});

$('body').on('click', '.saveUsiDetails', function (e) {
    e.preventDefault();
    let validator = defaultFormValidator('#usiDetailsForm');
    if (!validator.validate()) {
        return false;
    }
    let formData = getSerializeFormArray('#usiDetailsForm', {
        student_id: studentId,
    });
    ajaxActionV2('api/update-student-details', 'POST', formData, function (response) {
        if (response.status == 'error') {
            notificationDisplay(response.message, '', response.status);
            return false;
        } else {
            if (response.data.is_usi_verified) {
                notificationDisplay('Usi Details save successfully.', '', 'success');
            } else {
                notificationDisplay(response.data.usi_invalid_reason, '', 'error');
            }
        }
        let usiDetails = kendo.template($('#usiDetailsTemplate').html())({
            arr: response.data,
        });
        $(document).find('#usiDetailsTab').html(usiDetails);
        $(document).find('.usiDetails').removeClass('hidden');
        $(document).find('.editUsiDetails').addClass('hidden');
        if (typeof response.data.dateOfBirth !== 'undefined') {
            $('#dob').kendoDatePicker({
                format: dateFormatFrontSideJS,
                value: response.data.dateOfBirth,
            });
        }

        updateStudentDetailSummaryTab();
    });
});

$('body').on('click', '.viewAllDoc', function (e) {
    e.preventDefault();
    // $('#recentdocul').toggleClass('h-28');
    $('#documents_tab').click();
});

$('body').on('click', '.viewAllPaymentDiv', function (e) {
    e.preventDefault();
    let hiddenItems = $('.grid-payment-container .grid-item:nth-child(n+7)');
    if ($(window).width() <= 1536) {
        hiddenItems = $('.grid-payment-container .grid-item:nth-child(n+5)');
    }
    if (hiddenItems.is(':hidden')) {
        hiddenItems.fadeIn('fast');
        $('.viewAllPaymentDiv .viewAllPaymentText').text('See Less');
        $('.viewAllPaymentDiv .viewAllPaymentText').append(
            '&nbsp;<span class="k-icon k-i-arrow-chevron-up"></span>'
        );
    } else {
        hiddenItems.fadeOut('fast', function () {
            $(this).slideUp('fast');
        });
        $('.viewAllPaymentDiv .viewAllPaymentText').text('See More');
        $('.viewAllPaymentDiv .viewAllPaymentText').append(
            '&nbsp;<span class="k-icon k-i-arrow-chevron-down"></span>'
        );
    }
});

$('body').on('click', '.recentDocumentDownload', function (e) {
    var filePath = $(this).attr('data-href');
    var link = document.createElement('a');
    link.href = filePath;
    link.download = filePath.substr(filePath.lastIndexOf('/') + 1);
    link.click();
});

$('body').on('click', '.cancelBtn', function (e) {
    e.preventDefault();
    $(this).closest('.k-window-content').data('kendoWindow').close();
    //let modalID = $(this).attr('data-type-id');
    //window.parent.$('#'+modalID).data("kendoWindow").close();
});

setupTogglePopup('#manageColumnsForCourseTab', '#manageColumnBoxForCourseTab');
setupTogglePopup('#manageColumnsForPaymentTab', '#manageColumnBoxForPaymentTab');

$('body').on('click', '.verifyUSINumber', function (e) {
    e.preventDefault();
    let validator = defaultFormValidator('#usiDetailsForm');
    if (!validator.validate()) {
        return false;
    }
    startAjaxLoader();
    let formData = getSerializeFormArray('#usiDetailsForm', {
        student_id: studentId,
    });
    ajaxActionV2('api/verify-student-usi-number', 'POST', formData, function (response) {
        if (typeof response.data.is_usi_verified == 'undefined') {
            $(document).find('#responceTable').addClass('hidden');
        } else {
            $(document).find('#responceTable').removeClass('hidden');
        }
        if (typeof response.data !== 'undefined') {
            let usiTemplate = kendo.template($('#studentUsiTemplate').html())({
                result: response.data.Result || '',
                firstName: response.data.FirstName || '',
                familyName: response.data.FamilyName || '',
                singleName: response.data.SingleName || '',
                dob: response.data.DateOfBirth || '',
                variant: getBadgeVariant(response.data.Result || ''),
            });
            $(document).find('#responceTable').html('').html(usiTemplate);

            if (
                response.success === 1 &&
                response.data.Result &&
                response.data.Result.includes('Valid') &&
                response.data.Result.includes('failed')
            ) {
                $('.verifyUsi').removeClass('hidden');
                $('.notVerifyUsi').addClass('hidden');
            } else {
                $('.verifyUsi').addClass('hidden');
                $('.notVerifyUsi').removeClass('hidden');
            }
        }

        if (response.data.usi_invalid_reason) {
            $('.notVerifyUsiReason').text(response.data.usi_invalid_reason);
        }
        notificationDisplay(response.message, '', response.status);
        stopAjaxLoader();
    });
});

function setActiveTabLoad() {
    const tabactiveValue = new URLSearchParams(window.location.search).get('activetab');
    // const tabactiveValue = $.cookie('activetab');
    if (tabactiveValue === 'summary' && $('#summary_tab').length) {
        manageTabData();
    } else if ($('#' + tabactiveValue + '_tab').length) {
        $('#' + tabactiveValue + '_tab').trigger('click');
    } else {
        manageTabData();
    }
    const actionValue = new URLSearchParams(window.location.search).get('action');
    if (actionValue) {
        if ($('.' + actionValue).length) {
            $('.' + actionValue).trigger('click');
        }
    }
}

function updateStudentDetailSummaryTab() {
    ajaxActionV2('api/get-student-details', 'POST', selectedDataArr, function (response) {
        $(document)
            .find('#studentDetails')
            .html(kendo.template($('#studentDetailTemplate').html())(response.data.student_detail));
        $(document)
            .find('#studentDetailsV2')
            .html(
                kendo.template($('#studentMoreDetailTemplate').html())(response.data.student_detail)
            );
        $(document).find('.agencyNameByCourse').text(selectedCourseAgencyName);
        //$(document).find("#studentMoreDetailTemplate").find('.agencyNameByCourse').text(selectedCourseAgencyName);
    });
}

function startAjaxLoader() {
    $(document)
        .on('ajaxStart', function () {
            kendo.ui.progress($(document.body), true);
        })
        .on('ajaxStop', function () {
            kendo.ui.progress($(document.body), false);
        });
}

function stopAjaxLoader() {
    $(document)
        .on('ajaxStart', function () {
            kendo.ui.progress($(document.body), false);
        })
        .on('ajaxStop', function () {
            kendo.ui.progress($(document.body), false);
        });
}

function withActiveCourseCheck(handler) {
    return function (event) {
        if (!isActiveCourse) {
            event.preventDefault();
            notificationDisplay('Course is not active', '', 'error');
            return;
        }
        handler(event); // Call the original handler if the check passes
    };
}

function returnDateOnly(dateVal, format = 'yyyy-MM-dd') {
    return kendo.toString(new Date(dateVal), format);
    //return new Date(dateVal).toISOString().split("T")[0];
}

function initializeAutocomplete() {
    var input = document.getElementById('autocompleteResidentialAddress');
    var autocomplete = new google.maps.places.Autocomplete(input);

    google.maps.event.addListener(autocomplete, 'place_changed', function () {
        var place = autocomplete.getPlace();

        var components_by_type = {};
        for (var i = 0; i < place.address_components.length; i++) {
            var c = place.address_components[i];
            components_by_type[c.types[0]] = c;
        }

        $('#current_building_name').val('');

        var suburb =
            components_by_type['locality'] !== undefined
                ? components_by_type['locality'].long_name
                : '';
        $('#current_city').val(suburb);

        var unitNo =
            components_by_type['subpremise'] !== undefined
                ? components_by_type['subpremise'].long_name
                : '';
        $('#current_unit_detail').val(unitNo);

        var street_no =
            components_by_type['street_number'] !== undefined
                ? components_by_type['street_number'].long_name
                : '';
        $('#current_street_no').val(street_no);
        var street_name =
            components_by_type['route'] !== undefined ? components_by_type['route'].long_name : '';
        $('#current_street_name').val(street_name);
        var postcode =
            components_by_type['postal_code'] !== undefined
                ? components_by_type['postal_code'].long_name
                : '';
        $('#current_postcode').val(postcode);

        if (components_by_type['country'].long_name == 'Australia') {
            var state_short =
                components_by_type['administrative_area_level_1'] !== undefined
                    ? components_by_type['administrative_area_level_1'].short_name
                    : '';
            $('#current_state').val(state_short);
        } else {
            var state =
                components_by_type['administrative_area_level_1'] !== undefined
                    ? components_by_type['administrative_area_level_1'].long_name
                    : '';
            $('#current_state').val(state);
        }
        var country =
            components_by_type['country'] !== undefined
                ? components_by_type['country'].long_name
                : '';
        var dropdownlist = $('#current_country').data('kendoDropDownList');
        dropdownlist.select(function (dataItem) {
            return dataItem.Name === country;
        });
        //  $("#country option").filter(function(index) { return $(this).text() === country; }).attr('selected', 'selected');
    });

    var autocompletePostalAddress = document.getElementById('autocompletePostalAddress');
    var autocompletePostalAddressAutoComplete = new google.maps.places.Autocomplete(
        autocompletePostalAddress
    );

    google.maps.event.addListener(
        autocompletePostalAddressAutoComplete,
        'place_changed',
        function () {
            var place = autocompletePostalAddressAutoComplete.getPlace();
            var components_by_type = {};
            for (var i = 0; i < place.address_components.length; i++) {
                var c = place.address_components[i];
                components_by_type[c.types[0]] = c;
            }

            $('#postal_building_name').val('');

            var suburb =
                components_by_type['locality'] !== undefined
                    ? components_by_type['locality'].long_name
                    : '';
            $('#postal_city').val(suburb);

            var unitNo =
                components_by_type['subpremise'] !== undefined
                    ? components_by_type['subpremise'].long_name
                    : '';
            $('#postal_unit_detail').val(unitNo);

            var street_no =
                components_by_type['street_number'] !== undefined
                    ? components_by_type['street_number'].long_name
                    : '';
            $('#postal_street_no').val(street_no);
            var street_name =
                components_by_type['route'] !== undefined
                    ? components_by_type['route'].long_name
                    : '';
            $('#postal_street_name').val(street_name);
            var postcode =
                components_by_type['postal_code'] !== undefined
                    ? components_by_type['postal_code'].long_name
                    : '';
            $('#postal_postcode').val(postcode);

            if (components_by_type['country'].long_name == 'Australia') {
                var state_short =
                    components_by_type['administrative_area_level_1'] !== undefined
                        ? components_by_type['administrative_area_level_1'].short_name
                        : '';
                $('#postal_state').val(state_short);
            } else {
                var state =
                    components_by_type['administrative_area_level_1'] !== undefined
                        ? components_by_type['administrative_area_level_1'].long_name
                        : '';
                $('#postal_state').val(state);
            }
            var country =
                components_by_type['country'] !== undefined
                    ? components_by_type['country'].long_name
                    : '';
            var dropdownlist = $('#postal_country').data('kendoDropDownList');
            dropdownlist.select(function (dataItem) {
                return dataItem.Name === country;
            });
            //  $("#country option").filter(function(index) { return $(this).text() === country; }).attr('selected', 'selected');
        }
    );
}

function currencyFormat(amount = 0) {
    return kendo.toString(amount, 'c');
}

function syncRadioWithDropdown(courseId) {
    // if (isSyncing) return;
    // isSyncing = true;
    if (!courseId) {
        courseId = $('input[name="selected_course"]').first().val();
    }
    $('input[name="selected_course"]').each(function () {
        if ($(this).val() == courseId.trim()) {
            $(this).prop('checked', true);
        } else {
            $(this).prop('checked', false);
        }
    });

    $(document).find('.course-container').removeClass('border-primary-blue-500 bg-primary-blue-50');
    $(document)
        .find(`.course-container[data-course="${courseId}"]`)
        .addClass('border-primary-blue-500 bg-primary-blue-50');

    // isSyncing = false;
}

function setDefaultCourse() {
    let selectedCourseId = getQueryParam('courseId');
    syncRadioWithDropdown(selectedCourseId);
}

$('body').on('change', 'input[name="selected_course"]', function () {
    const selectedCourse = $(this).val();
    const index = $(this).data('index');
    // syncRadioWithDropdown(selectedCourse);
    let dropdownlist = $(document).find('select.header_course_list').data('kendoDropDownList');
    dropdownlist.value(selectedCourse);
    dropdownlist.trigger('change');
});

function onOpenCenterDialog(divID) {
    let className =
        'text-white bg-primary-blue-500 hover:shadow-lg rounded-lg focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-indigo-500';
    $(divID).parent().find('.k-dialog-titlebar').addClass('gradientbackground');
    $(divID).parent().find('.k-primary').addClass(className);
}

function resetFlags() {
    isSameTab = false;
    isServerRequest = false;
    // agentCommission.js
    pCommissionFlag = true;
    pBonusFlag = true;
    //installmentAndPrepayment.js
    pScheduleFlag = true;
    pInitialPayFlag = true;
    // miscellaneous.js
    pMiscellaneousFlag = true;
    // recordStudentService.js
    pServiceFlag = true;
    // recordTransfer.js
    pPaymentFlag = true;
    paidPaymentTransferListFlag = true;
    // refundHistory.js
    pRefundFlag = true;
    // scholarship.js
    pScholarshipFlag = true;
    // attendanceTab.js
    attendanceGridViewFlag = true;
    attendanceCalViewFlag = true;
    // resultTab.js
    resultUnitGrid = true;
    // summaryTab.js
    sScheduleFlag = true;
    // courseTab.js
    unitGridFlag = true;
    // placementTab.js
    vpmsFlag = true;
}

$(document).ready(function () {
    $(document).on('change', '#sameAsResidential', function () {
        if ($(this).is(':checked')) {
            const formData = $('#residentialAddressForm').serializeArray();

            formData.forEach(function (field) {
                const postalFieldName = field.name.replace('current_', 'postal_');

                $(`#postalAddressForm [name="${postalFieldName}"]`).val(field.value);
            });
            let country = $('#current_country_name').val();
            var dropdownlist = $('#postal_country').data('kendoDropDownList');
            dropdownlist.select(function (dataItem) {
                return dataItem.Name === country;
            });
        } else {
            $('#postalAddressForm input').val('');
        }
    });
});
