import "./bootstrap";
import { createApp, h } from "vue";
import { createInertiaApp } from "@inertiajs/vue3";
import globalHelper from "./plugins/global-helper";
import debounce from "./plugins/debouncer";
import PrimaryButton from "@spa/components/PrimaryButton.vue";
import Icons from "@spa/components/Icons.vue";
import FileIcons from "@spa/components/Icons/FileIcons.vue";
import UploadIcons from "@spa/components/Icons/UploadIcons.vue";
import ActionIcons from "@spa/components/Icons/ActionIcons.vue";
import SecondaryButton from "@spa/components/SecondaryButton.vue";
import UnitTypeButton from "@spa/components/UnitTypeButton.vue";
import InputGroup from "@spa/components/InputGroup.vue";
import { Input } from "@progress/kendo-vue-inputs";
// import "@progress/kendo-theme-default/dist/all.css";
import { ZiggyVue } from "./vendor/ziggy/vue.m";
// const response = await fetch(window["APP_URL"] + "/spa/ziggy");
// const Ziggy = await response.json();
import { resolvePageComponent } from "laravel-vite-plugin/inertia-helpers";
import VueKonva from "vue-konva";

import store from "@spa/stores";
import App from "./App.vue";
import { CustomFormatIntlService } from "./helpers/CustomFormatIntlService";

const customIntlService = new CustomFormatIntlService("en-GB");

createInertiaApp({
    id: "vue-app",
    resolve: (name) => {
        // const page = require(`./pages/${name}`);
        const resolvedPage = resolvePageComponent(
            `./pages/${name}.vue`,
            import.meta.glob("./pages/**/*.vue"),
        ).then((page) => {
            page.default.layout = App;
            return page;
        });

        // page.default.layout = name.startsWith('Public/') ? undefined : Layout
        // page.default.layout = App;
        return resolvedPage;
    },
    setup({ el, App, props, plugin }) {
        const app = createApp({ render: () => h(App, props) });

        app.directive("debounce", (el, binding) => debounce(el, binding));
        app.mixin(globalHelper);

        app.use(plugin)
            .use(store)
            .component("icon", Icons)
            .component("primary-btn", PrimaryButton)
            .component("secondary-btn", SecondaryButton)
            .component("input-group", InputGroup)
            .component("unit-type", UnitTypeButton)
            .component("k-input", Input)
            .component("file-icon", FileIcons)
            .component("upload-icon", UploadIcons)
            .component("action-icon", ActionIcons)
            .use(ZiggyVue, Ziggy)
            .use(VueKonva);

        // app.provide.config.globalProperties.$route = route;
        // app.provide('route',  route);
        app.provide("kendoIcons", "font");
        app.provide("kendoIntlService", customIntlService);

        app.mount(el);
    },
});
