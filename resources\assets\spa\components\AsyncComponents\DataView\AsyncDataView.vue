<template>
    <div :class="rootClasses">
        <div v-if="$slots.header || showHeader" :class="headerClasses">
            <div class="flex justify-end">
                <SelectButton v-model="layout" :options="options" :allowEmpty="false">
                    <template #option="{ option }">
                        <span class="text-gray-500">
                            <icon
                                :name="option === 'list' ? 'list' : 'grid'"
                                :width="16"
                                :height="16"
                                :fill="'currentColor'"
                            />
                        </span>
                    </template>
                </SelectButton>
            </div>
            <slot name="header"></slot>
        </div>
        <div :class="contentClasses">
            <template v-if="!empty">
                <slot v-if="$slots.list && layout === 'list'" name="list" :items="items"></slot>
                <slot v-if="$slots.grid && layout === 'grid'" name="grid" :items="items"></slot>
            </template>
            <div v-else :class="emptyMessageClasses">
                <slot name="empty" :layout="layout">
                    {{ emptyMessage }}
                </slot>
            </div>
        </div>
        <div :class="paginatorClasses" v-if="showPaginator">
            <Pager
                :skip="
                    props.pageable
                        ? (store.serverPagination.page - 1) * store.serverPagination.rowsPerPage
                        : 0
                "
                :take="props.pageable ? store.serverPagination.rowsPerPage : store?.all?.length"
                :total="props.pageable ? store.serverPagination.rowsNumber : store?.all?.length"
                v-bind="gridPageable"
                @pagechange="onPageChange"
            />
        </div>
        <div v-if="$slots.footer" :class="footerClasses">
            <slot name="footer"></slot>
        </div>
    </div>
</template>

<script setup>
import { ref, computed, watch, inject } from 'vue';
import { Pager } from '@progress/kendo-vue-data-tools';
import { twMerge } from 'tailwind-merge';
import SelectButton from '@spa/components/ui/selectbutton/SelectButton.vue';

// Define props
const props = defineProps({
    first: {
        type: Number,
        default: 0,
    },
    rows: {
        type: Number,
        default: 0,
    },
    dataItems: {
        type: Array,
        default: null,
    },
    dataKey: {
        type: String,
        default: null,
    },
    sortField: {
        type: String,
        default: null,
    },
    sortOrder: {
        type: Number,
        default: null,
    },
    totalRecords: {
        type: Number,
        default: 0,
    },
    lazy: {
        type: Boolean,
        default: false,
    },
    store: {
        type: Object,
        default: null,
    },
    showPaginator: {
        type: Boolean,
        default: true,
    },
    showHeader: {
        type: Boolean,
        default: false,
    },
    paginatorPosition: {
        type: String,
        default: 'bottom',
    },
    pt: {
        type: Object,
        default: () => ({}),
    },
    emptyMessage: {
        type: String,
        default: 'No data available.',
    },
});

const gridPageable = ref({
    buttonCount: 5,
    info: true,
    type: 'numeric',
    pageSizes: false,
    previousNext: true,
    pageSizeDefs: [10, 20, 50, 100, 150, 200],
});

// Define emits
const emit = defineEmits(['update:first', 'update:rows', 'page']);

const layout = ref('list');
const options = ref(['list', 'grid']);

watch(
    () => props.sortField,
    () => {
        store.fetchPaged();
    }
);

watch(
    () => props.sortOrder,
    () => {
        store.fetchPaged();
    }
);

// Methods
const resetPage = () => {
    emit('update:first', d_first.value);
};

// Computed properties
const empty = computed(() => {
    return !props.dataItems || props.dataItems.length === 0;
});

const items = computed(() => {
    if (props.dataItems && props.dataItems.length) {
        let data = props.dataItems;
        return data;
    } else {
        return null;
    }
});

// Computed classes
const rootClasses = computed(() => {
    return twMerge('space-y-4', props.pt.root);
});

const headerClasses = computed(() => {
    return twMerge('tw-data-view__header', props.pt.header);
});

const contentClasses = computed(() => {
    return twMerge('tw-data-view__content', props.pt.content);
});

const emptyMessageClasses = computed(() => {
    return twMerge('tw-data-view__empty', props.pt.empty);
});

const paginatorClasses = computed(() => {
    return twMerge('tw-pager', props.pt.paginator);
});

const footerClasses = computed(() => {
    return twMerge('tw-data-view__footer', props.pt.footer);
});

const onPageChange = (event) => {
    if (event.event.type === 'scroll') {
        return;
    }
    const page = Math.floor(event.page.skip / event.page.take) + 1;
    props.store.serverPagination.page = page;
    props.store.serverPagination.rowsPerPage = event.page.take;
    props.store.fetchPaged();
};

const onSortChange = (event) => {
    console.log('event', event);
    sort.value = event.sort;
    const field = event.sort[0].field;
    const isDescending = event.sort[0].dir === 'desc';
    props.store.serverPagination.sortBy = field;
    props.store.serverPagination.descending = isDescending;
    props.store.fetchPaged();
};

// Expose methods
defineExpose({
    onPageChange,
    resetPage,
    empty,
    items,
});
</script>
