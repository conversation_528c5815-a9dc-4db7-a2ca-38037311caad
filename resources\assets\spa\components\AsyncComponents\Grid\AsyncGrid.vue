<template>
    <async-grid-filters
        class="mb-4"
        :store="store"
        :has-search="hasSearch"
        :has-filters="hasFilters"
        :has-bulk-actions="hasBulkActions"
        :has-export="hasExport"
        :has-reset-filters="hasRestFilters"
        :has-create-action="hasCreateAction"
        :create-btn-label="createBtnLabel"
        @handel-reset="emit('handelReset')"
        :filter-columns="filterColumns"
        :has-manage-columns="hasManageColumns"
        :has-all-select="hasAllSelect"
        :columns="columns"
    >
        <template v-if="hasBulkActions" v-slot:bulk-actions>
            <slot name="bulk-actions" />
        </template>
        <template v-if="hasFilters" v-slot:filters>
            <slot name="filters" />
        </template>
        <template
            v-if="hasManageColumns && columns.some((col) => col.canHide)"
            v-slot:manage-column
        >
            <grid-column-selector
                :columns="columns"
                :visible-count="visibleCount"
                :total-columns="columns.length"
                :is-column-visible="isColumnVisible"
                :toggle-column="toggleColumn"
                :toggle-all-columns="toggleAllColumns"
                :reset-columns="resetColumns"
            />
        </template>
    </async-grid-filters>
    <grid-wrapper
        :fullWidth="false"
        :rowHover="true"
        :rounded="true"
        class="flex-1 overflow-y-auto"
        :loading="store.loading"
    >
        <grid
            :style="gridStyle"
            :loading="store.loading"
            :loader="'loaderTemplate'"
            :data-items="store.all"
            :columns="gridColumns"
            :skip="
                props.pageable
                    ? (store.serverPagination.page - 1) * store.serverPagination.rowsPerPage
                    : 0
            "
            :take="props.pageable ? store.serverPagination.rowsPerPage : store.all.length"
            :total="props.pageable ? store.serverPagination.rowsNumber : store.all.length"
            :selected-field="enableSelection ? 'selected' : undefined"
            :selection-mode="enableSelection ? 'multiple' : undefined"
            @selectionchange="onSelectionChange"
            @headerselectionchange="onHeaderSelectionChange"
            @rowclick="onRowClick"
            @pagechange="onPageChange"
            :pageable="gridPageable"
            :scrollable="true"
            :resizable="true"
            :sortable="{ allowUnsort: false }"
            :sort="sort"
            @sortchange="onSortChange"
            :filter="filter"
            @filterchange="onFilterChange"
        >
            <template #loaderTemplate>
                <table-loader v-if="store.loading" />
            </template>
            <GridNoRecords>
                <empty-state />
            </GridNoRecords>
            <template #sortingHeaderCell="{ props }">
                <div
                    class="flex cursor-pointer items-center justify-between gap-2 truncate text-13"
                >
                    {{ props.title }}
                    <div class="flex flex-1 justify-end gap-1">
                        <SortingIndicator :dir="getDirection(props.field)" v-if="props.sortable" />
                        <VDropdown
                            :distance="6"
                            :triggers="['click']"
                            :auto-hide="currentColumn(props.field)?.filterType !== 'advance'"
                            :placement="'bottom-end'"
                            v-if="currentColumn(props.field)?.filterable"
                        >
                            <icon name="grid-filter" fill="currentColor" />
                            <template #popper>
                                <template
                                    v-if="
                                        typeof currentColumn(props.field)?.filterable === 'boolean'
                                    "
                                >
                                    <slot
                                        :name="`column-menu-${currentColumn(props.field)?.name}`"
                                        :props="{
                                            ...props,
                                            rowForm: getRowForm(props.dataIndex),
                                            column: gridColumns.find(
                                                (col) => col.field === props.field
                                            ),
                                            filter,
                                            sort,
                                            onColumnSortChange,
                                            onFilterChange,
                                        }"
                                    ></slot>
                                </template>
                                <AsyncGridColumnFilter
                                    v-else-if="
                                        typeof currentColumn(props.field)?.filterable === 'object'
                                    "
                                    :column="currentColumn(props.field)"
                                    :filter="filter"
                                    :filterable="currentColumn(props.field)?.filterable"
                                    :sort="sort"
                                    :sortable="{ allowUnsort: false }"
                                    @sortchange="onColumnSortChange"
                                    @filterchange="onFilterChange"
                                    :has-checkbox-filter="
                                        currentColumn(props.field)?.filterType === 'checkbox'
                                    "
                                    :has-advance-filter="
                                        currentColumn(props.field)?.filterType === 'advance'
                                    "
                                    :has-custom-filter="
                                        currentColumn(props.field)?.filterType === 'custom'
                                    "
                                    :data-items="store.all"
                                />
                            </template>
                        </VDropdown>
                    </div>
                </div>
            </template>
            <template #selectedTemplate="{ props }">
                <td>
                    <Checkbox
                        :checked="!!store.selected?.find((item) => item.id === props.dataItem.id)"
                        @update:checked="
                            (val) => {
                                if (val) {
                                    store.selected.push(props.dataItem);
                                } else {
                                    store.selected = store.selected.filter(
                                        (item) => item.id !== props.dataItem.id
                                    );
                                }
                            }
                        "
                    ></Checkbox>
                </td>
            </template>
            <template #selectedHeaderTemplate="{ props }">
                <Checkbox
                    :checked="store.selected?.length === store.all?.length"
                    @update:checked="
                        (val) => {
                            if (val) {
                                store.selected = [...store.all];
                            } else {
                                store.selected = [];
                            }
                        }
                    "
                ></Checkbox>
            </template>
            <template #actionsTemplate="{ props }">
                <GridActionsBlock
                    :row="props.dataItem"
                    :actions="!hasMoreActions ? actions : []"
                    :store="store"
                    :tdAttrs="{
                        class: props.class,
                        colspan: props.colspan,
                        role: props.role,
                        'aria-selected': props['aria-selected'],
                        'data-grid-col-index': props['data-grid-col-index'],
                        id: props.id,
                    }"
                    @download="(row) => emit('download', row)"
                >
                    <template #actions="{ row }">
                        <slot name="actions" :row="row" v-if="!hasMoreActions" />
                        <VDropdown
                            :distance="4"
                            :triggers="['click']"
                            :auto-hide="true"
                            :placement="'bottom-end'"
                            v-if="hasMoreActions"
                            :popup-class="'tw-action-dropdown'"
                        >
                            <GridActionButton :tooltip-title="'More Actions'">
                                <icon name="more-horizontal" />
                            </GridActionButton>
                            <template #popper>
                                <GridActionsDropdown
                                    :key="props.dataItem.id"
                                    :row="props.dataItem"
                                    :actions="actions"
                                    :selected-rows="store.selected"
                                    :store="store"
                                />
                                <slot name="actions" :row="row" />
                            </template> </VDropdown></template
                ></GridActionsBlock>
            </template>
            <template v-slot:defaultCellTemplate="{ props }">
                <slot
                    :name="`default-cell`"
                    :props="{
                        ...props,
                        rowForm: getRowForm(props.dataIndex),
                    }"
                >
                    <td>
                        <template v-if="typeof formatCellData(props.columnIndex) === 'function'">
                            {{ formatCellData(props.columnIndex)(props.dataItem[props.field]) }}
                        </template>
                        <template v-else>
                            <template
                                v-if="
                                    props.dataItem[props.field] === '' ||
                                    props.dataItem[props.field] === null
                                "
                            >
                                N/A
                            </template>
                            <template v-else>
                                {{ props.dataItem[props.field] ?? 'N/A' }}
                            </template>
                        </template>
                    </td>
                </slot>
            </template>
            <template
                v-for="(col, i) in Array.isArray(columns) ? columns.filter((r) => r.replace) : []"
                :key="col?.name || i"
                v-slot:[`${col?.name}Template`]="{ props }"
            >
                <td
                    :colspan="props?.colspan"
                    :class="props.class"
                    :role="props.role"
                    :aria-selected="props['aria-selected']"
                    :data-grid-col-index="props['data-grid-col-index']"
                    :id="props.id"
                >
                    <slot
                        :name="`body-cell-${col?.name}`"
                        :props="{
                            ...props,
                            rowForm: getRowForm(props.dataIndex),
                        }"
                    ></slot>
                </td>
            </template>
        </grid>
    </grid-wrapper>
    <grid-floating-actions
        v-if="hasFloatingActions"
        :store="store"
        :has-bulk-delete="hasBulkDelete"
        :has-bulk-export="hasBulkExport"
    >
        <template v-if="hasFloatingActions" v-slot:floating-actions>
            <slot name="floating-actions" />
        </template>
    </grid-floating-actions>
</template>

<script setup>
import GridWrapper from '@spa/components/KendoGrid/GridWrapper';
import TableLoader from '@spa/components/KendoGrid/TableLoader';
import EmptyState from '@spa/components/KendoGrid/EmptyState';
import { ref, computed, watch } from 'vue';
import {
    Grid,
    GridColumnMenuCheckboxFilter,
    GridColumnMenuFilter,
    GridColumnMenuSort,
    GridNoRecords,
} from '@progress/kendo-vue-grid';
import AsyncGridFilters from '@spa/components/AsyncComponents/Grid/Partials/AsyncGridFilters.vue';
import HeaderTemplate from '@spa/components/KendoGrid/templates/HeaderTemplate.vue';
import Checkbox from '@spa/components/Checkbox.vue';
import GridActionsBlock from '@spa/components/AsyncComponents/Grid/Partials/GridActionsBlock.vue';
import GridFloatingActions from '@spa/components/AsyncComponents/Grid/Partials/GridFloatingActions.vue';
import { useColumnVisibility } from '@spa/services/validators/useColumnVisibility.js';
import GridColumnSelector from '@spa/components/AsyncComponents/Grid/Partials/GridColumnSelector.vue';
import SortingIndicator from '@spa/components/KendoGrid/SortingIndicator.vue';
import AsyncGridColumnFilter from '@spa/components/AsyncComponents/Grid/Partials/AsyncGridColumnFilter.vue';
import GridActionsDropdown from '@spa/components/AsyncComponents/Grid/Partials/GridActionDropdown.vue';
import GridActionButton from '@spa/components/AsyncComponents/Grid/Partials/GridActionButton.vue';

const props = defineProps({
    store: { type: Object, required: true },
    columns: { type: Array, default: () => [] },
    enableSelection: {
        type: Boolean,
        default: false,
    },
    gridStyle: {
        default: { height: '70vh' },
    },
    selectedField: {
        type: String,
        default: 'selected',
    },
    hasSearch: { type: Boolean, default: true },
    hasFilters: { type: Boolean, default: true },
    hasBulkActions: { type: Boolean, default: true },
    hasExport: { type: Boolean, default: true },
    hasRestFilters: { type: Boolean, default: true },
    hasCreateAction: { type: Boolean, default: true },
    hasSection: { type: Boolean, default: true },
    hasActions: { type: Boolean, default: true },
    hasFloatingActions: { type: Boolean, default: false },
    hasBulkDelete: { type: Boolean, default: true },
    hasBulkExport: { type: Boolean, default: true },
    hasManageColumns: { type: Boolean, default: true },
    hasAllSelect: { type: Boolean, default: false },
    hasMoreActions: { type: Boolean, default: false },
    actions: {
        type: Array,
        default: () => ['edit', 'delete'],
    },
    actionsConfig: {
        type: Object,
        default: () => ({}),
    },
    pageable: { type: Boolean, default: true },
    filterColumns: { type: Number, default: 2 },
    storageKey: { type: String, default: 'grid-columns' },
    createBtnLabel: { type: String, default: 'Create' },
});

const emit = defineEmits(['handelReset', 'download', 'rowclick']);

const gridPageable = ref({
    buttonCount: 5,
    info: true,
    type: 'numeric',
    pageSizes: true,
    previousNext: true,
});

const sort = ref([]);
const filter = ref({
    filters: [],
    logic: 'and',
});

const getDirection = (field) => {
    const sortBy = props.store.serverPagination.sortBy;
    const sortField = props.columns.find((item) => item.field === field);
    if (!sortField) return null;
    const sortDirection = props.store.serverPagination.descending ? 'desc' : 'asc';
    if (sortBy === field) {
        return sortDirection;
    }
    return null;
};
const getRowForm = (dataIndex) => {
    const dataIndexStart =
        props.store.serverPagination.rowsPerPage * (props.store.serverPagination.page - 1);
    const currentIndex = dataIndex - dataIndexStart - 1;
    return props.store.form_rows[currentIndex] ?? {};
};

const formatCellData = (columnIndex) => {
    const column = props.columns[columnIndex];
    return column?.formatCellData;
};

const gridColumns = computed(() => {
    const newColumns = [];
    if (props.enableSelection) {
        newColumns.push({
            field: 'selected',
            name: 'selected',
            cell: 'selectedTemplate',
            headerCell: 'selectedHeaderTemplate',
            width: '50px',
            filterable: false,
            sortable: false,
            selectable: true,
        });
    }
    if (Array.isArray(visibleColumns.value)) {
        visibleColumns.value.forEach((column) => {
            const newColumn = { ...column };
            if (column.replace) {
                newColumn.cell = `${column.name}Template`;
            } else {
                newColumn.cell = 'defaultCellTemplate';
            }
            if (typeof column.filterable === 'object') {
                newColumn.filter = column.filterable?.filter;
                newColumn.filterType = column.filterable?.type;
            }
            if (column.sortable) {
                newColumn.headerCell = 'sortingHeaderCell';
            }
            if (!column.sortable || column.filterable) {
                newColumn.sortable = false;
            }
            if (!column.name) {
                newColumn.name = column.field;
            }
            newColumns.push(newColumn);
        });
    }
    if (props.hasActions) {
        newColumns.push({
            name: 'actions',
            title: 'Actions',
            field: 'actions',
            cell: 'actionsTemplate',
            actions: props.actions,
            sortable: false,
            minResizableWidth: 200,
            width: '200px',
        });
    }
    newColumns.push({});
    return newColumns;
});

const currentColumn = (field) => {
    return gridColumns.value.find((col) => col.field === field);
};

const onPageChange = (event) => {
    if (event.event.type === 'scroll') {
        return;
    }
    const page = Math.floor(event.page.skip / event.page.take) + 1;
    props.store.serverPagination.page = page;
    props.store.serverPagination.rowsPerPage = event.page.take;
    props.store.fetchPaged();
};

const onSelectionChange = (event) => {
    if (!props.store.selected) {
        props.store.selected = [];
    }
    if (event?.event?.target?.checked === true) {
        const exists = props.store.selected.find((item) => item?.id === event.dataItem?.id);
        if (exists) {
            return;
        }
        props.store.selected.push(event.dataItem);
    } else if (event?.event?.target?.checked === false) {
        props.store.selected = props.store.selected.filter(
            (item) => item?.id !== event.dataItem?.id
        );
    }
};

const onRowClick = (event) => {
    emit('rowclick', event);
};

const onHeaderSelectionChange = (event) => {
    const checked = event.event.target.checked;
    props.store.all = props.store.all.map((item) => ({
        ...item,
        selected: checked,
    }));
    props.store.selected = checked ? props.store.all : [];
};

const onSortChange = (event) => {
    console.log('event', event);
    sort.value = event.sort;
    const field = event.sort[0]?.field;
    const isDescending = event.sort[0]?.dir === 'desc';
    fetchSorted(field, isDescending);
};

const onColumnSortChange = (newDescriptor, event) => {
    console.log('desc', newDescriptor);
    sort.value = newDescriptor;
    const field = newDescriptor[0]?.field;
    const isDescending = newDescriptor[0]?.dir === 'desc';
    fetchSorted(field, isDescending);
};

const onFilterChange = (newDescriptor, event) => {
    console.log('filter', newDescriptor, event);
    filter.value = newDescriptor;
};

const fetchSorted = (field, isDescending) => {
    props.store.serverPagination.sortBy = field;
    props.store.serverPagination.descending = isDescending;
    props.store.fetchPaged();
};

const {
    visibleColumns,
    visibleCount,
    toggleColumn,
    toggleAllColumns,
    resetColumns,
    isColumnVisible,
} = useColumnVisibility(props.columns, props.storageKey);
</script>
