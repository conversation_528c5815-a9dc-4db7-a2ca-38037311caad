<template>
    <GridColumnMenuSort
        :column="column"
        :sortable="{ allowUnsort: false }"
        :sort="sort"
        @sortchange="onSortChange"
        v-if="hasSort"
    />
    <div class="border-t p-1" v-if="hasCheckboxFilter || hasAdvanceFilter || hasCustomFilter">
        <GridColumnMenuCheckboxFilter
            :column="column"
            :filter="filter"
            :filterable="true"
            :unique-data="true"
            :search-box="false"
            :data-items="dataItems"
            :expanded="true"
            @filterfocus="handleFocus"
            @closemenu="closeMenu"
            @expandchange="expandChange"
            @filterchange="onFilterChange"
            v-if="hasCheckboxFilter"
        />
        <GridColumnMenuFilter
            :expanded="true"
            :column="column"
            :filterable="true"
            :filter="filter"
            @filterfocus="handleFocus"
            @closemenu="closeMenu"
            @expandchange="expandChange"
            @filterchange="onFilterChange"
            v-if="hasAdvanceFilter"
        />
        <GridColumnMenuItemGroup v-if="hasCustomFilter">
            <GridColumnMenuItem :title="title" @menuitemclick="expandChange" />
            <GridColumnMenuItemContent :show="true">
                <div class="k-column-list-wrapper">
                    <slot />
                </div>
            </GridColumnMenuItemContent>
        </GridColumnMenuItemGroup>
    </div>
</template>

<script setup>
import { ref, watch, toRefs } from 'vue';
import {
    GridColumnMenuItemGroup,
    GridColumnMenuItemContent,
    GridColumnMenuItem,
    GridColumnMenuCheckboxFilter,
    GridColumnMenuFilter,
    GridColumnMenuSort,
} from '@progress/kendo-vue-grid';

defineProps({
    column: Object,
    sortable: [Boolean, Object],
    sort: Array,
    filter: Object,
    filterable: Boolean,
    columns: Array,
    title: String,
    icon: String,
    hasSort: {
        type: Boolean,
        default: true,
    },
    hasCheckboxFilter: {
        type: Boolean,
        default: false,
    },
    hasAdvanceFilter: {
        type: Boolean,
        default: false,
    },
    custom: Boolean,
    hasCustomFilter: {
        type: Boolean,
        default: false,
    },
    dataItems: Object,
    store: Object,
});

const emit = defineEmits([
    'columnssubmit',
    'closemenu',
    'expandchange',
    'filterchange',
    'sortchange',
]);

const handleFocus = (e) => {
    emit('contentfocus', e);
};

const expandChange = () => {
    emit('expandchange');
};

const closeMenu = () => {
    emit('closemenu');
};

const onFilterChange = (newDescriptor, e) => {
    emit('filterchange', newDescriptor, e);
};

const onSortChange = (newDescriptor, e) => {
    emit('sortchange', newDescriptor, e);
};
</script>
