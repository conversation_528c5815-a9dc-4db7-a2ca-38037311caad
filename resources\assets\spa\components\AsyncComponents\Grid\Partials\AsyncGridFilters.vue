<template>
    <div class="space-y-4">
        <Card v-if="hasFilters" :pt="{ root: 'relative p-4 lg:p-6 rounded-lg' }">
            <template #content>
                <div
                    class="flex flex-col items-start justify-between gap-x-24 md:flex-row md:items-center"
                >
                    <div :class="filterWrapperClass">
                        <slot name="filters" />
                    </div>
                    <div>
                        <Button
                            v-if="hasRestFilters"
                            variant="ghost"
                            class="flex min-w-32 cursor-pointer justify-start px-0"
                            @click="emit('handelReset')"
                        >
                            <icon
                                :name="'sync'"
                                :fill="isAllFilterValuesNull ? '#69c0ff' : '#1890FF'"
                                :className="reset ? 'animate-spin' : ''"
                            />
                            <span> Reset Filters </span>
                        </Button>
                    </div>
                </div>
            </template>
        </Card>
        <div class="flex flex-wrap items-center justify-between gap-4">
            <div class="item-center flex gap-4">
                <div v-if="hasSearch" class="coursesSearchInputField relative">
                    <span class="absolute left-3 top-1/2 -translate-y-1/2">
                        <icon :name="'lens'" width="16" height="16"
                    /></span>
                    <input
                        type="text"
                        id="offscreen"
                        v-model="store.filters.query"
                        @change=""
                        v-debounce="300"
                        class="tw-input-text pl-8"
                        placeholder="Enter Keyword"
                    />
                </div>
                <div
                    class="flex items-center gap-2"
                    v-if="store.selected?.length > 0 && hasAllSelect"
                >
                    <p>All {{ store.selected?.length }} items selected.</p>
                    <Button variant="link" @click="store.selectAll()"
                        >Select all {{ store.serverPagination.rowsNumber }} items.</Button
                    >
                </div>
            </div>
            <div class="flex w-full flex-wrap items-center justify-between gap-3 md:w-auto">
                <slot v-if="hasBulkActions" name="bulk-actions" />
                <Button v-if="hasExport" class="tw-btn-secondary !h-9 shadow-none" size="xs">
                    <file-icon name="xlsx" class="h-6 w-6 text-gray-500" />
                    Export (xls)
                </Button>
                <Button
                    v-if="hasCreateAction"
                    @click="() => store.createFunction()"
                    class="tw-btn-primary !h-9 shadow-none"
                    size="xs"
                    :disabled="store.loading"
                >
                    <icon :name="'plus'" :fill="'currentColor'" :width="'16'" :height="'16'" />
                    {{ createBtnLabel }}
                </Button>

                <Button
                    v-if="hasRefreshAction"
                    @click="
                        () => {
                            store.fetchPaged();
                        }
                    "
                    class="!size-9 rounded-md border bg-white p-2 shadow-none"
                    size="xs"
                    variant="icon"
                    :loading="store.loading"
                    loading-text=""
                >
                    <svg
                        width="25"
                        height="25"
                        class="size-5 fill-gray-600 text-gray-600"
                        viewBox="0 0 24 24"
                        xmlns="http://www.w3.org/2000/svg"
                    >
                        <path
                            d="M12 4.75a7.25 7.25 0 1 0 7.201 6.406c-.068-.588.358-1.156.95-1.156.515 0 .968.358 1.03.87a9.25 9.25 0 1 1-3.432-6.116V4.25a1 1 0 1 1 2.001 0v2.698l.034.052h-.034v.25a1 1 0 0 1-1 1h-3a1 1 0 1 1 0-2h.666A7.219 7.219 0 0 0 12 4.75Z"
                        />
                    </svg>
                </Button>
                <slot name="manage-column" />
            </div>
        </div>
    </div>
</template>
<script setup>
import Button from '@spa/components/Buttons/Button.vue';
import Card from '@spa/components/Card/Card.vue';
import { ref, computed } from 'vue';
import { twMerge } from 'tailwind-merge';

const reset = ref(false);
const search = ref('');
const isAllFilterValuesNull = ref(false);

const props = defineProps({
    store: { type: Object, required: true },
    hasSearch: { type: Boolean, default: true },
    hasFilters: { type: Boolean, default: true },
    hasBulkActions: { type: Boolean, default: true },
    hasExport: { type: Boolean, default: true },
    hasRestFilters: { type: Boolean, default: true },
    hasRefreshAction: { type: Boolean, default: true },
    hasCreateAction: { type: Boolean, default: true },
    createBtnLabel: { type: String, default: 'Create' },
    filterColumns: { type: Number, default: 2 },
    hasManageColumns: { type: Boolean, default: true },
    columns: { type: Array, default: () => [] },
    storageKey: { type: String, default: 'grid-columns' },
    hasAllSelect: { type: Boolean, default: false },
});

const filterWrapperClass = computed(() => {
    const columnsMap = {
        1: 'grid-cols-1',
        2: 'grid-cols-2',
        3: 'grid-cols-3',
        4: 'grid-cols-4',
    };
    return twMerge('grid grid-cols-1 gap-4 gap-x-20', columnsMap[props.filterColumns]);
});

const emit = defineEmits(['handelReset']);
</script>
<style lang=""></style>
