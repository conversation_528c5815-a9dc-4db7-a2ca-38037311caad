<script setup>
import Button from '@spa/components/Buttons/Button.vue';
import { Tooltip } from '@progress/kendo-vue-tooltip';

const props = defineProps({
    tooltipTitle: {
        type: String,
        default: '',
    },
});

const emit = defineEmits(['click']);
</script>

<template>
    <Tooltip
        :anchor-element="'target'"
        :position="'top'"
        :parentTitle="true"
        :tooltipClassName="'flex !p-1'"
        :class="'w-full'"
    >
        <Button
            :variant="'icon'"
            @click="emit('click')"
            class="cursor-pointer text-gray-600"
            v-bind="$attrs"
            :title="tooltipTitle"
        >
            <slot />
        </Button>
    </Tooltip>
</template>
