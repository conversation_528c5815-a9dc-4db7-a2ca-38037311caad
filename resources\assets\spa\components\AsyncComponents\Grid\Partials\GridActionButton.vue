<script setup>
import Button from '@spa/components/Buttons/Button.vue';

const props = defineProps({
    tooltipTitle: {
        type: String,
        default: '',
    },
});

const emit = defineEmits(['click']);
</script>

<template>
    <Button
        :variant="'icon'"
        @click="emit('click')"
        class="cursor-pointer text-gray-600"
        v-bind="$attrs"
        v-tooltip="tooltipTitle"
    >
        <slot />
    </Button>
</template>
