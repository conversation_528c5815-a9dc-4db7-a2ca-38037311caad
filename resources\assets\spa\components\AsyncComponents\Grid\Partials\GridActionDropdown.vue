<template>
    <template v-for="item in visiblePrimaryActions" :key="item.id">
        <div
            class="flex cursor-pointer justify-start rounded px-2 py-1.5 hover:bg-gray-100"
            :class="{ 'gap-2': item.icon != '', 'cursor-not-allowed opacity-50': item.disabled }"
            :style="{ width: width + 'px' }"
            @click="handleItemClick(item)"
        >
            <div class="flex items-center" v-if="item.icon != ''">
                <icon :name="item.icon" :width="16" :height="16" />
            </div>
            <div class="text-sm text-gray-700">{{ item.label }}</div>
        </div>
    </template>

    <div
        class="my-1 border-t border-gray-200"
        v-if="visiblePrimaryActions.length > 0 && visibleSecondaryActions.length > 0"
    ></div>

    <!-- Secondary Actions -->
    <template v-for="item in visibleSecondaryActions" :key="item.id">
        <div
            v-if="!item.subActions"
            class="flex cursor-pointer justify-start rounded px-2 py-1.5 hover:bg-gray-100"
            :class="{ 'gap-2': item.icon != '', 'cursor-not-allowed opacity-50': item.disabled }"
            :style="{ width: width + 'px' }"
            @click="handleItemClick(item)"
        >
            <div class="flex items-center" v-if="item.icon != ''">
                <icon :name="item.icon" :width="18" :height="18" />
            </div>
            <div class="text-sm text-gray-700">{{ item.label }}</div>
        </div>

        <!-- Action with submenu -->
        <VDropdown v-else placement="right-start" :triggers="['hover']">
            <div
                class="flex cursor-pointer justify-start rounded px-2 py-1.5 hover:bg-gray-100"
                :class="{
                    'gap-2': item.icon != '',
                    'cursor-not-allowed opacity-50': item.disabled,
                }"
                :style="{ width: width + 'px' }"
            >
                <div class="flex items-center" v-if="item.icon != ''">
                    <icon :name="item.icon" :width="18" :height="18" />
                </div>
                <div class="text-sm text-gray-700">{{ item.label }}</div>
                <div class="ml-auto flex items-center">
                    <icon name="chevron-right" :width="16" :height="16" />
                </div>
            </div>
            <template #popper>
                <div
                    v-for="subItem in item.subActions"
                    :key="subItem.id"
                    class="flex cursor-pointer justify-start rounded px-2 py-1.5 hover:bg-gray-100"
                    :class="{ 'gap-2': subItem.icon != '' }"
                    :style="{ width: width - 20 + 'px' }"
                    @click="handleItemClick(subItem)"
                >
                    <div class="flex items-center" v-if="subItem.icon != ''">
                        <icon :name="subItem.icon" :width="18" :height="18" />
                    </div>
                    <div class="text-sm text-gray-700">{{ subItem.label }}</div>
                </div>
            </template>
        </VDropdown>
    </template>

    <!-- Destructive Actions -->
    <template v-for="item in visibleDestructiveActions" :key="item.id">
        <div
            class="flex cursor-pointer justify-start rounded px-2 py-1.5 hover:bg-red-50"
            :class="{ 'gap-2': item.icon != '', 'cursor-not-allowed opacity-50': item.disabled }"
            :style="{ width: width + 'px' }"
            @click="handleItemClick(item)"
        >
            <div class="flex items-center text-red-500" v-if="item.icon != ''">
                <icon :name="item.icon" :width="18" :height="18" />
            </div>
            <div class="text-sm text-red-600">{{ item.label }}</div>
        </div>
    </template>
</template>

<script>
import { computed } from 'vue';

export default {
    name: 'GridActionsDropdown',
    props: {
        selectedRows: {
            type: Array,
            default: () => [],
        },
        row: {
            type: Object,
            required: true,
        },
        store: {
            type: Object,
            required: true,
        },
        userPermissions: {
            type: Array,
            default: () => [
                'read',
                'edit',
                'delete',
                'export',
                'archive',
                'assign',
                'status_change',
            ],
        },
        width: {
            type: Number,
            default: 180,
        },
        actionsConfig: {
            type: Object,
            default: () => ({
                primary: [
                    {
                        id: 'edit',
                        label: 'Edit',
                        icon: 'edit',
                        action: 'edit',
                        disabled: false,
                        visible: true,
                        permissions: ['edit'],
                    },
                    {
                        id: 'view',
                        label: 'View Details',
                        icon: 'eye',
                        action: 'view',
                        disabled: false,
                        visible: true,
                        permissions: ['read'],
                    },
                ],
                secondary: [
                    {
                        id: 'archive',
                        label: 'Archive',
                        icon: 'archive',
                        action: 'archive',
                        confirmMessage: 'Are you sure you want to archive the selected items?',
                        disabled: false,
                        visible: true,
                        permissions: ['archive'],
                    },
                    {
                        id: 'bulk_status_change',
                        label: 'Change Status',
                        icon: 'toggle-left',
                        action: 'bulk_status_change',
                        minSelection: 1,
                        disabled: false,
                        visible: true,
                        permissions: ['status_change'],
                        subActions: [
                            {
                                id: 'status_active',
                                label: 'Set Active',
                                status: 'active',
                            },
                            {
                                id: 'status_inactive',
                                label: 'Set Inactive',
                                status: 'inactive',
                            },
                            {
                                id: 'status_pending',
                                label: 'Set Pending',
                                status: 'pending',
                            },
                        ],
                    },
                ],
                destructive: [
                    {
                        id: 'delete',
                        label: 'Delete',
                        icon: 'trash',
                        action: 'delete',
                        variant: 'destructive',
                        confirmMessage:
                            'Are you sure you want to delete the selected items? This action cannot be undone.',
                        shortcut: 'Delete',
                        disabled: false,
                        visible: true,
                        permissions: ['delete'],
                    },
                ],
            }),
        },
        actions: {
            type: Array,
            default: () => ['edit', 'delete'],
        },
    },
    emits: ['action-clicked'],
    setup(props, { emit }) {
        const selectedRowsCount = computed(() => props.selectedRows.length);

        const filterActions = (actions) => {
            return actions.filter((action) => {
                if (!props.actions.includes(action.id)) return false;

                // Check visibility
                if (!action.visible) return false;

                // Check permissions
                if (action.permissions && action.permissions.length > 0) {
                    const hasPermission = action.permissions.some((permission) =>
                        props.userPermissions.includes(permission)
                    );
                    if (!hasPermission) return false;
                }

                // Check selection constraints for bulk actions
                if (action.minSelection && selectedRowsCount.value < action.minSelection) {
                    return false;
                }
                if (action.maxSelection && selectedRowsCount.value > action.maxSelection) {
                    return false;
                }

                return true;
            });
        };

        const visiblePrimaryActions = computed(() =>
            filterActions(props.actionsConfig.primary || [])
        );
        const visibleSecondaryActions = computed(() =>
            filterActions(props.actionsConfig.secondary || [])
        );
        const visibleDestructiveActions = computed(() =>
            filterActions(props.actionsConfig.destructive || [])
        );

        const handleItemClick = (item) => {
            if (item.disabled) return;

            if (item.id === 'edit') {
                props.store.edit(props.row);
                return;
            } else if (item.id === 'view') {
                props.store.view(props.row);
                return;
            } else if (item.id === 'delete') {
                props.store.confirmDelete(props.row);
                return;
            }

            // Show confirmation if required
            if (item.confirmMessage) {
                const confirmed = window.confirm(item.confirmMessage);
                if (!confirmed) return;
            }

            // Emit the action
            emit('action-clicked', {
                action: item,
                selectedRows: props.selectedRows,
            });
        };

        return {
            selectedRowsCount,
            visiblePrimaryActions,
            visibleSecondaryActions,
            visibleDestructiveActions,
            handleItemClick,
        };
    },
};
</script>
