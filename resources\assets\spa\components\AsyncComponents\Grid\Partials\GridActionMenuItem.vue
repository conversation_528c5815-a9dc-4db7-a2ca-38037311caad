<template>
    <div
        class="flex cursor-pointer justify-start rounded px-2 py-1.5 hover:bg-gray-100"
        :class="{ 'gap-2': item.icon != '', 'cursor-not-allowed opacity-50': item.disabled }"
        @click="emit('click')"
    >
        <div class="flex items-center" v-if="item.icon != ''">
            <icon :name="item.icon" :width="16" :height="16" />
        </div>
        <div class="text-sm text-gray-700">{{ item.label }}</div>
    </div>
</template>
<script setup>
const props = defineProps({
    item: {
        type: Object,
        required: true,
    },
    width: {
        type: Number,
        default: 180,
    },
});

const emit = defineEmits(['click']);

const handleItemClick = () => {
    emit('click');
};
</script>
<style scoped></style>
