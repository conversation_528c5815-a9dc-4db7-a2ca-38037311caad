<script setup>
import Button from '@spa/components/Buttons/Button.vue';
import { Tooltip } from '@progress/kendo-vue-tooltip';
import GridActionsDropdown from '@spa/components/DropdownMenu/DropdownMenu.vue';
import GridActionButton from '@spa/components/AsyncComponents/Grid/Partials/GridActionButton.vue';
import { Dropdown } from 'floating-vue';

const props = defineProps({
    store: {},
    row: {
        type: Object,
        required: true,
    },
    actions: {
        type: Array,
        default() {
            return ['edit', 'delete'];
        },
    },
    tdAttrs: {
        type: Object,
        default: () => ({}),
    },
});

const emit = defineEmits(['download']);

const hasAccess = (action) => {
    return props.actions.includes(action);
};
</script>

<template>
    <td v-bind="tdAttrs">
        <div class="flex items-center gap-1 px-2">
            <GridActionButton
                v-if="hasAccess('view')"
                @click="
                    () => {
                        store.viewFormData(row);
                    }
                "
                :tooltip-title="'View Detail'"
            >
                <icon :name="'eye'" :width="20" :height="20" />
            </GridActionButton>
            <GridActionButton
                v-if="hasAccess('edit')"
                @click="
                    () => {
                        store.edit(row);
                    }
                "
                :tooltip-title="'Edit'"
            >
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke-width="1.5"
                    stroke="currentColor"
                    class="size-5"
                >
                    <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        d="m16.862 4.487 1.687-1.688a1.875 1.875 0 1 1 2.652 2.652L10.582 16.07a4.5 4.5 0 0 1-1.897 1.13L6 18l.8-2.685a4.5 4.5 0 0 1 1.13-1.897l8.932-8.931Zm0 0L19.5 7.125M18 14v4.75A2.25 2.25 0 0 1 15.75 21H5.25A2.25 2.25 0 0 1 3 18.75V8.25A2.25 2.25 0 0 1 5.25 6H10"
                    />
                </svg>
            </GridActionButton>
            <GridActionButton
                v-if="hasAccess('download') && row.file_name"
                @click="
                    () => {
                        $emit('download', row);
                    }
                "
                :tooltip-title="'Download'"
            >
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke-width="1.5"
                    stroke="currentColor"
                    class="size-5"
                >
                    <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        d="M3 16.5v2.25A2.25 2.25 0 0 0 5.25 21h13.5A2.25 2.25 0 0 0 21 18.75V16.5M16.5 12 12 16.5m0 0L7.5 12m4.5 4.5V3"
                    />
                </svg>
            </GridActionButton>
            <GridActionButton
                v-if="hasAccess('delete')"
                @click="
                    () => {
                        store.confirmDelete(row);
                    }
                "
                :tooltip-title="'Delete'"
                :class="'text-red-500'"
            >
                <svg
                    xmlns="http://www.w3.org/2000/svg"
                    fill="none"
                    viewBox="0 0 24 24"
                    stroke-width="{1.5}"
                    stroke="currentColor"
                    class="size-5"
                >
                    <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        d="m14.74 9-.346 9m-4.788 0L9.26 9m9.968-3.21c.342.052.682.107 1.022.166m-1.022-.165L18.16 19.673a2.25 2.25 0 0 1-2.244 2.077H8.084a2.25 2.25 0 0 1-2.244-2.077L4.772 5.79m14.456 0a48.108 48.108 0 0 0-3.478-.397m-12 .562c.34-.059.68-.114 1.022-.165m0 0a48.11 48.11 0 0 1 3.478-.397m7.5 0v-.916c0-1.18-.91-2.164-2.09-2.201a51.964 51.964 0 0 0-3.32 0c-1.18.037-2.09 1.022-2.09 2.201v.916m7.5 0a48.667 48.667 0 0 0-7.5 0"
                    />
                </svg>
            </GridActionButton>
            <slot name="actions" :row="row" />
        </div>
    </td>
</template>
