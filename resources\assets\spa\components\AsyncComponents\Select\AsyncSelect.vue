<script setup>
import { ref, computed, onMounted, watch } from 'vue';
import { DropDownList } from '@progress/kendo-vue-dropdowns';
import DropdownMultiSelect from '@spa/components/Dropdown/DropdownMultiSelect.vue';
import { Error, Hint, Label } from '@progress/kendo-vue-labels';
import { FieldWrapper } from '@progress/kendo-vue-form';
import Button from '@spa/components/Buttons/Button.vue';
const popupOptions = {
    animate: false,
    popupClass: 'tw-popup',
};

const props = defineProps({
    indicaterequired: {
        type: Boolean,
        default: false,
    },
    validationMessage: {
        type: String,
        default: '',
    },
    valid: {
        type: Boolean,
        default: true,
    },
    label: String,
    labelClass: String,
    editorId: String,
    hint: String,
    optional: Boolean,
    className: String,
    optionValue: String,
    optionLabel: String,
    disabled: Boolean,
    store: Object,
    modelValue: [String, Number, Array, Object],
    clearable: {
        type: Boolean,
        default: false,
    },
    multiple: {
        type: Boolean,
        default: true,
    },
    readonly: Boolean,
    useChips: {
        type: Boolean,
        default: true,
    },
    placeholder: {
        type: String,
        default: '',
    },
    hasCreateAction: {
        type: Boolean,
        default: false,
    },
    filters: {
        type: Object,
        default: () => ({}),
    },
    initFormData: {
        type: Object,
        default: () => ({}),
    },
    forceReload: {
        type: Boolean,
        default: false,
    },
});

const emit = defineEmits(['update:modelValue']);
const allOptions = ref([]);
const loading = ref(false);
const searchValue = ref('');
const dropDownRef = ref(null);

const filteredOptions = computed(() => {
    if (!searchValue.value) return allOptions.value;
    const needle = searchValue.value.toLowerCase();
    return allOptions.value.filter((item) =>
        item[props.optionLabel]?.toLowerCase().includes(needle)
    );
});

const defaultItem = computed(() => ({
    [props.optionLabel]: props.placeholder,
    [props.optionValue]: null,
}));

onMounted(async () => {
    // The store's watcher will automatically call fetchPaged() due to immediate: true
    // So we just need to set the initial options from the store

    //note: if initial options are not set, fetchPaged need to fire as we call data from api
    //      as it is async component please feel free to improve this if you can.
    // init filters
    if (props.filters) {
        props.store.filters = props.filters;
    }
    if (props.forceReload) {
        await props.store?.fetchPaged();
    } else {
        if (!props.store?.all.length) {
            await props.store?.fetchPaged();
        }
    }
    if (props.store?.all) {
        allOptions.value = props.store.all;
    }
});

watch(
    () => props.store?.all,
    (newVal) => {
        if (newVal) {
            allOptions.value = newVal;
        }
    },
    { deep: true }
);

const computedValue = computed({
    get() {
        return props.modelValue;
    },
    set(val) {
        emit('update:modelValue', val);
    },
});

function onChange(event) {
    emit('update:modelValue', event.value);
}

function onFilterChange(event) {
    if (!event || !event.filter) return;

    const value = event.filter.value || '';
    searchValue.value = value;

    if (!props.store || !props.store.filters) return;

    props.store.filters.query = value;

    // For empty searches, refresh from server
    if (value === '') {
        loading.value = true;
        try {
            props.store.fetchPaged();
        } catch (e) {
            console.error('Error fetching options:', e);
        } finally {
            loading.value = false;
        }
    }
}
const handleFooterClick = () => {
    console.log('dropDownRef', dropDownRef.value);
    dropDownRef.value?.handleBlur();
    props.store.formData = {
        ...props.initFormData,
    };
    props.store.formDialog = true;
};
</script>

<template>
    <FieldWrapper>
        <Label
            :class="labelClass"
            :editor-id="editorId"
            :editor-valid="valid"
            :disabled="disabled"
            :optional="optional"
            v-if="label"
        >
            {{ label }}
            <span v-if="indicaterequired" :class="'ml-1 text-red-500'">*</span>
        </Label>
        <div class="k-form-field-wrap">
            <div :class="`async-select ${className}`">
                <DropdownMultiSelect
                    v-if="multiple"
                    @update:multiSelectRef="
                        (val) => {
                            dropDownRef = val;
                        }
                    "
                    :data-items="filteredOptions"
                    :text-field="optionLabel"
                    :value-field="optionValue"
                    :data-item-key="optionValue"
                    :filterable="true"
                    :clear-button="clearable"
                    :disabled="disabled"
                    :readonly="readonly"
                    :loading="loading"
                    :value-primitive="true"
                    :popup-settings="popupOptions"
                    :default-item="defaultItem"
                    @filterchange="onFilterChange"
                    v-model="computedValue"
                    :style="{
                        width: '100%',
                        minWidth: '200px',
                        maxWidth: '600px',
                    }"
                    :footer="hasCreateAction ? 'myFooter' : ''"
                    :touched="false"
                >
                    <template v-if="hasCreateAction" #myFooter>
                        <div class="p-[1rem]">
                            <Button variant="primary" size="xs" @click="handleFooterClick">
                                <span>Add New</span>
                                <icon name="add" width="16" height="16" fill="#fff" />
                            </Button>
                        </div>
                    </template>
                </DropdownMultiSelect>
                <DropDownList
                    v-else
                    ref="dropDownRef"
                    :data-items="filteredOptions"
                    :text-field="optionLabel"
                    :value-field="optionValue"
                    :data-item-key="optionValue"
                    v-model="computedValue"
                    :filterable="true"
                    :clear-button="clearable"
                    :disabled="disabled"
                    :readonly="readonly"
                    :loading="loading"
                    :value-primitive="true"
                    :popup-settings="popupOptions"
                    :default-item="defaultItem"
                    @filterchange="onFilterChange"
                    :footer="hasCreateAction ? 'myFooter' : ''"
                >
                    <template v-if="hasCreateAction" #myFooter>
                        <div class="p-[1rem]">
                            <Button variant="primary" size="xs" @click="handleFooterClick">
                                <span>Add New</span>
                                <icon name="add" width="16" height="16" fill="#fff" />
                            </Button>
                        </div>
                    </template>
                </DropDownList>
            </div>
            <Error v-if="!valid">
                {{ validationMessage }}
            </Error>
            <Hint v-else-if="hint">{{ hint }}</Hint>
        </div>
    </FieldWrapper>
    <slot name="createDialog"></slot>
</template>
<style lang="scss">
.async-select {
    .k-multiselect {
        padding-top: 0 !important;
        padding-bottom: 0 !important;
    }
}
</style>
