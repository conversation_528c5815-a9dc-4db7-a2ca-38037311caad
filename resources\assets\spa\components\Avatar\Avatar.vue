<template>
    <div :class="rootClass" :aria-labelledby="ariaLabelledby" :aria-label="ariaLabel">
        <slot>
            <span v-if="label" :class="labelClass">{{ label }}</span>
            <component v-else-if="$slots.icon" :is="$slots.icon" :class="iconClass" />
            <icon :name="icon" v-else-if="icon" :class="iconClass" />
            <img
                v-else-if="image"
                :src="image"
                :alt="ariaLabel"
                @error="onError"
                :class="imageClass"
            />
        </slot>
    </div>
</template>
<script>
import { twMerge } from 'tailwind-merge';

export default {
    props: {
        pt: {
            type: Object,
            default: {},
        },
        image: {
            type: String,
            default:
                'https://s3-alpha-sig.figma.com/img/b5c5/3457/c6b669e9e197a26764c4bedb8ae3ad90?Expires=1721001600&Key-Pair-Id=APKAQ4GOSFWCVNEHN3O4&Signature=K4CminsVy9QoRPFWoyZLT8FGbr1Wrr0b7htFUZkWhG2dUYpGLvx7alLZhiGqkxfIDXx-rMgJDW8dKwQPL6GmHWNsQ0Txsgk1uqCaCyI0Vwph4c9B6Wk4QcvFDByUoEssjp158LBduURNz~O7em08GVCVoQPJX9ytXXM6GviNH0Bcy1DSrelBcpqkni2AgaIjUXi9VePZ8APO353n4l66Wb9nGqK-~bPbscOkqIHM0FXxGeLVjlvjXn6ZGwiExm5y70sWk9zxv0e13CkpnnL7~W0I9vSGSvLhZ1OMh1525hrj293nJUVmDdyEVqlY-Ltsbdxk618CA8EBb-PV~h9i-w__',
        },
        label: {
            type: String,
            default: '',
        },
        icon: {
            type: String,
            default: '',
        },
        ariaLabelledby: {
            type: String,
            default: '',
        },
        ariaLabel: {
            type: String,
            default: '',
        },
    },
    computed: {
        rootClass() {
            return twMerge(
                'w-7 h-7 flex items-center justify-center gap-2 bg-primary-blue-500 rounded-full',
                this.pt.root
            );
        },
        iconClass() {
            return twMerge('w-6 h-6', this.pt.icon);
        },
        imageClass() {
            return twMerge('w-6 h-6 rounded-full', this.pt.image);
        },
        labelClass() {
            return twMerge('text-sm font-medium text-white leading-7 h-7', this.pt.label);
        },
    },
};
</script>
<style lang=""></style>
