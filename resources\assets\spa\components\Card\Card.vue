<template lang="">
    <div v-bind="$attrs" :class="rootClasses">
        <div v-if="$slots.header" :class="headerClasses">
            <slot name="header"></slot>
        </div>
        <div v-if="$slots.content" :class="contentClasses">
            <slot name="content"></slot>
        </div>
        <div v-if="$slots.footer" :class="footerClasses">
            <slot name="footer"></slot>
        </div>
    </div>
</template>
<script>
import { twMerge } from 'tailwind-merge';
export default {
    name: 'Card',
    components: {
        twMerge,
    },
    props: {
        pt: {
            type: Object,
            default: {},
        },
        variant: {
            type: String,
            default: 'default',
        },
    },
    computed: {
        rootClasses() {
            return twMerge(
                'tw-card rounded-lg bg-white border border-gray-200 shadow',
                this.variant === 'default' ? 'max-md:p-3 p-4' : 'p-0',
                this.pt.root
            );
        },
        headerClasses() {
            return twMerge(
                'tw-card__header',
                this.variant === 'default' ? ' p-0' : 'p-4 border-b border-gray-200',
                this.pt.header
            );
        },
        contentClasses() {
            return twMerge(
                'tw-card__content space-y-4',
                this.variant === 'default' ? ' p-0' : 'p-4',
                this.pt.content
            );
        },
        footerClasses() {
            return twMerge(
                'tw-card__footer',
                this.variant === 'default' ? ' p-0' : 'p-4',
                this.pt.footer
            );
        },
    },
};
</script>
<style lang=""></style>
