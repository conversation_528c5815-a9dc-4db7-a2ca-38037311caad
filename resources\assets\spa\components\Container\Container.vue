<template>
    <div :class="containerClasses" :style="containerStyles">
        <template v-if="$slots.tabs || showTabs" :class="tabsClasses">
            <slot name="tabs"></slot>
        </template>
        <!-- Main content area -->
        <main :class="mainClasses" :style="mainStyles">
            <!-- Header slot -->
            <header v-if="$slots.header || showHeader" :class="headerClasses">
                <slot name="header">
                    <h1 v-if="title" :class="titleClasses">{{ title }}</h1>
                </slot>
            </header>
            <!-- Primary content -->
            <article :class="contentClasses">
                <slot name="default"></slot>
            </article>
        </main>

        <footer v-if="$slots.footer || showFooter" :class="footerClasses">
            <slot name="footer"></slot>
        </footer>
    </div>
</template>

<script setup>
import { computed } from 'vue';
import { twMerge } from 'tailwind-merge';

const props = defineProps({
    // Layout configuration
    layout: {
        type: String,
        default: 'default',
        validator: (value) => ['default', 'full-width'].includes(value),
    },

    // Container settings
    maxWidth: {
        type: String,
        default: '1920px',
    },
    padding: {
        type: String,
        default: '0',
    },
    gap: {
        type: String,
        default: '1.5rem',
    },

    // Content settings
    title: {
        type: String,
        default: '',
    },

    // Visibility controls
    showHeader: {
        type: Boolean,
        default: false,
    },
    showTabs: {
        type: Boolean,
        default: false,
    },
    showFooter: {
        type: Boolean,
        default: false,
    },

    // Responsive settings
    responsive: {
        type: Boolean,
        default: true,
    },
    pt: {
        type: Object,
        default: {},
    },
});

const containerClasses = computed(() => {
    return twMerge('mx-auto', props.pt.root);
});

const headerClasses = computed(() => {
    return twMerge('flex', props.pt.header);
});

const mainClasses = computed(() => {
    return twMerge('mx-auto', props.pt.main);
});

const tabsClasses = computed(() => {
    return twMerge('', props.pt.tabs);
});

const contentClasses = computed(() => {
    return twMerge('flex-1', props.pt.content);
});

const secondaryClasses = computed(() => {
    return twMerge('w-1/4', props.pt.secondary);
});

const footerClasses = computed(() => {
    return twMerge('flex justify-between items-center', props.pt.footer);
});

const containerStyles = computed(() => {
    return {
        gap: props.gap,
    };
});

const mainStyles = computed(() => {
    return {
        maxWidth: props.responsive ? props.maxWidth : '100%',
        padding: props.padding,
        gap: props.gap,
    };
});
</script>

<style></style>
