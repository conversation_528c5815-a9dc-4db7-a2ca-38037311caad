<template>
    <div class="group flex items-center gap-1">
        <div ref="slotRef" class="w-fit cursor-pointer" @click="handleSlotClick">
            <slot v-if="$slots.default" />
            <template v-else>
                {{ text }}
            </template>
        </div>
        <button
            type="button"
            class="w-fit"
            :class="autoHide ? 'hidden group-hover:inline-flex' : 'inline-flex'"
            :data-text="text"
            @click="handleButtonClick"
        >
            <IconCopy24Regular
                class="icon-copy size-4 text-gray-400"
                title="Copy"
                v-show="!copied"
            />
            <IconCheckmark24Regular
                class="icon-check size-4 text-green-500"
                title="Copied!"
                v-show="copied"
            />
        </button>
    </div>
</template>

<script setup>
import { ref } from 'vue';
import { IconCopy24Regular, IconCheckmark24Regular } from '@iconify-prerendered/vue-fluent';

const props = defineProps({
    text: String,
    autoHide: {
        type: <PERSON>ole<PERSON>,
        default: true,
    },
});

const copied = ref(false);
const slotRef = ref(null);

const copyText = async (textToCopy) => {
    try {
        if (navigator.clipboard) {
            await navigator.clipboard.writeText(textToCopy);
        } else {
            const textarea = document.createElement('textarea');
            textarea.value = textToCopy;
            document.body.appendChild(textarea);
            textarea.select();
            document.execCommand('copy');
            document.body.removeChild(textarea);
        }

        copied.value = true;
        setTimeout(() => {
            copied.value = false;
        }, 2000);
    } catch (e) {
        console.error('Copy failed:', e);
    }
};

const handleButtonClick = () => {
    const textToCopy = props.text || slotRef.value?.textContent?.trim() || '';
    copyText(textToCopy);
};

const handleSlotClick = () => {
    const slotText = slotRef.value?.textContent?.trim();
    if (slotText) {
        copyText(slotText);
    }
};
</script>
