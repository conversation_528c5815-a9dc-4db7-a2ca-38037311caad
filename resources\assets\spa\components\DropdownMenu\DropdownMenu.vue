<template>
    <!--    <VDropdown>-->
    <!--        <template #popper>-->
    <!-- Primary Actions -->
    <template v-for="item in visiblePrimaryActions" :key="item.id">
        <div
            class="flex cursor-pointer justify-start rounded px-2 py-1.5 hover:bg-gray-100"
            :class="{ 'gap-2': item.icon != '', 'cursor-not-allowed opacity-50': item.disabled }"
            :style="{ width: width + 'px' }"
            @click="handleItemClick(item)"
        >
            <div class="flex items-center" v-if="item.icon != ''">
                <icon :name="item.icon" :width="20" :height="20" />
            </div>
            <div class="text-sm text-gray-700">{{ item.label }}</div>
            <div class="ml-auto text-xs text-gray-400" v-if="showShortcuts && item.shortcut">
                {{ item.shortcut }}
            </div>
        </div>
    </template>

    <!-- Separator -->
    <div
        class="my-1 border-t border-gray-200"
        v-if="visiblePrimaryActions.length > 0 && visibleSecondaryActions.length > 0"
    ></div>

    <!-- Secondary Actions -->
    <template v-for="item in visibleSecondaryActions" :key="item.id">
        <!-- Regular action -->
        <div
            v-if="!item.subActions"
            class="flex cursor-pointer justify-start rounded px-2 py-1.5 hover:bg-gray-100"
            :class="{ 'gap-2': item.icon != '', 'cursor-not-allowed opacity-50': item.disabled }"
            :style="{ width: width + 'px' }"
            @click="handleItemClick(item)"
        >
            <div class="flex items-center" v-if="item.icon != ''">
                <icon :name="item.icon" :width="20" :height="20" />
            </div>
            <div class="text-sm text-gray-700">{{ item.label }}</div>
        </div>

        <!-- Action with submenu -->
        <VDropdown v-else placement="right-start" :triggers="['hover']">
            <div
                class="flex cursor-pointer justify-start rounded px-2 py-1.5 hover:bg-gray-100"
                :class="{
                    'gap-2': item.icon != '',
                    'cursor-not-allowed opacity-50': item.disabled,
                }"
                :style="{ width: width + 'px' }"
            >
                <div class="flex items-center" v-if="item.icon != ''">
                    <icon :name="item.icon" :width="20" :height="20" />
                </div>
                <div class="text-sm text-gray-700">{{ item.label }}</div>
                <div class="ml-auto flex items-center">
                    <icon name="chevron-right" :width="16" :height="16" />
                </div>
            </div>
            <template #popper>
                <div
                    v-for="subItem in item.subActions"
                    :key="subItem.id"
                    class="flex cursor-pointer justify-start rounded px-2 py-1.5 hover:bg-gray-100"
                    :class="{ 'gap-2': subItem.icon != '' }"
                    :style="{ width: width - 20 + 'px' }"
                    @click="handleItemClick(subItem)"
                >
                    <div class="flex items-center" v-if="subItem.icon != ''">
                        <icon :name="subItem.icon" :width="20" :height="20" />
                    </div>
                    <div class="text-sm text-gray-700">{{ subItem.label }}</div>
                </div>
            </template>
        </VDropdown>
    </template>

    <!-- Separator -->
    <div
        class="my-1 border-t border-gray-200"
        v-if="visibleSecondaryActions.length > 0 && visibleBulkActions.length > 0"
    ></div>

    <!-- Bulk Actions -->
    <template v-for="item in visibleBulkActions" :key="item.id">
        <div
            v-if="!item.subActions"
            class="flex cursor-pointer justify-start rounded px-2 py-1.5 hover:bg-gray-100"
            :class="{ 'gap-2': item.icon != '', 'cursor-not-allowed opacity-50': item.disabled }"
            :style="{ width: width + 'px' }"
            @click="handleItemClick(item)"
        >
            <div class="flex items-center" v-if="item.icon != ''">
                <icon :name="item.icon" :width="20" :height="20" />
            </div>
            <div class="text-sm text-gray-700">{{ item.label }}</div>
        </div>

        <!-- Bulk action with submenu -->
        <VDropdown v-else placement="right-start" :triggers="['hover']">
            <div
                class="flex cursor-pointer justify-start rounded px-2 py-1.5 hover:bg-gray-100"
                :class="{
                    'gap-2': item.icon != '',
                    'cursor-not-allowed opacity-50': item.disabled,
                }"
                :style="{ width: width + 'px' }"
            >
                <div class="flex items-center" v-if="item.icon != ''">
                    <icon :name="item.icon" :width="20" :height="20" />
                </div>
                <div class="text-sm text-gray-700">{{ item.label }}</div>
                <div class="ml-auto flex items-center">
                    <icon name="chevron-right" :width="16" :height="16" />
                </div>
            </div>
            <template #popper>
                <div
                    v-for="subItem in item.subActions"
                    :key="subItem.id"
                    class="flex cursor-pointer justify-start rounded px-2 py-1.5 hover:bg-gray-100"
                    :class="{ 'gap-2': subItem.icon != '' }"
                    :style="{ width: width - 20 + 'px' }"
                    @click="handleItemClick(subItem)"
                >
                    <div class="flex items-center" v-if="subItem.icon != ''">
                        <icon :name="subItem.icon" :width="20" :height="20" />
                    </div>
                    <div class="text-sm text-gray-700">{{ subItem.label }}</div>
                </div>
            </template>
        </VDropdown>
    </template>

    <!-- Separator -->
    <div
        class="my-1 border-t border-gray-200"
        v-if="
            (visiblePrimaryActions.length > 0 ||
                visibleSecondaryActions.length > 0 ||
                visibleBulkActions.length > 0) &&
            visibleDestructiveActions.length > 0
        "
    ></div>

    <!-- Destructive Actions -->
    <template v-for="item in visibleDestructiveActions" :key="item.id">
        <div
            class="flex cursor-pointer justify-start rounded px-2 py-1.5 hover:bg-red-50"
            :class="{ 'gap-2': item.icon != '', 'cursor-not-allowed opacity-50': item.disabled }"
            :style="{ width: width + 'px' }"
            @click="handleItemClick(item)"
        >
            <div class="flex items-center" v-if="item.icon != ''">
                <icon :name="item.icon" :width="20" :height="20" class="text-red-600" />
            </div>
            <div class="text-sm text-red-600">{{ item.label }}</div>
            <div class="ml-auto text-xs text-red-400" v-if="showShortcuts && item.shortcut">
                {{ item.shortcut }}
            </div>
        </div>
    </template>
    <!--        </template>-->
    <!--    </VDropdown>-->
</template>

<script>
import { computed } from 'vue';

export default {
    name: 'GridActionsDropdown',
    props: {
        selectedRows: {
            type: Array,
            default: () => [],
        },
        userPermissions: {
            type: Array,
            default: () => [
                'read',
                'edit',
                'delete',
                'export',
                'archive',
                'share',
                'bulk_edit',
                'assign',
                'status_change',
            ],
        },
        width: {
            type: Number,
            default: 200,
        },
        showShortcuts: {
            type: Boolean,
            default: true,
        },
        actionsConfig: {
            type: Object,
            default: () => ({
                primary: [
                    {
                        id: 'edit',
                        label: 'Edit',
                        icon: 'edit',
                        action: 'edit',
                        shortcut: 'Ctrl+E',
                        disabled: false,
                        visible: true,
                        permissions: ['edit'],
                    },
                    {
                        id: 'view',
                        label: 'View Details',
                        icon: 'eye',
                        action: 'view',
                        shortcut: 'Ctrl+V',
                        disabled: false,
                        visible: true,
                        permissions: ['read'],
                    },
                    {
                        id: 'duplicate',
                        label: 'Duplicate',
                        icon: 'copy',
                        action: 'duplicate',
                        shortcut: 'Ctrl+D',
                        disabled: false,
                        visible: true,
                        permissions: ['create'],
                    },
                ],
                secondary: [
                    {
                        id: 'archive',
                        label: 'Archive',
                        icon: 'archive',
                        action: 'archive',
                        confirmMessage: 'Are you sure you want to archive the selected items?',
                        disabled: false,
                        visible: true,
                        permissions: ['archive'],
                    },
                    {
                        id: 'export',
                        label: 'Export',
                        icon: 'download',
                        action: 'export',
                        disabled: false,
                        visible: true,
                        permissions: ['export'],
                        subActions: [
                            {
                                id: 'export_csv',
                                label: 'Export as CSV',
                                icon: 'file-text',
                            },
                            {
                                id: 'export_pdf',
                                label: 'Export as PDF',
                                icon: 'file',
                            },
                            {
                                id: 'export_excel',
                                label: 'Export as Excel',
                                icon: 'file-spreadsheet',
                            },
                        ],
                    },
                    {
                        id: 'share',
                        label: 'Share',
                        icon: 'share',
                        action: 'share',
                        disabled: false,
                        visible: true,
                        permissions: ['share'],
                    },
                ],
                bulk: [
                    {
                        id: 'bulk_edit',
                        label: 'Bulk Edit',
                        icon: 'edit-2',
                        action: 'bulk_edit',
                        minSelection: 2,
                        maxSelection: 100,
                        disabled: false,
                        visible: true,
                        permissions: ['bulk_edit'],
                    },
                    {
                        id: 'bulk_assign',
                        label: 'Bulk Assign',
                        icon: 'user-plus',
                        action: 'bulk_assign',
                        minSelection: 1,
                        disabled: false,
                        visible: true,
                        permissions: ['assign'],
                    },
                    {
                        id: 'bulk_status_change',
                        label: 'Change Status',
                        icon: 'toggle-left',
                        action: 'bulk_status_change',
                        minSelection: 1,
                        disabled: false,
                        visible: true,
                        permissions: ['status_change'],
                        subActions: [
                            {
                                id: 'status_active',
                                label: 'Set Active',
                                status: 'active',
                            },
                            {
                                id: 'status_inactive',
                                label: 'Set Inactive',
                                status: 'inactive',
                            },
                            {
                                id: 'status_pending',
                                label: 'Set Pending',
                                status: 'pending',
                            },
                        ],
                    },
                ],
                destructive: [
                    {
                        id: 'delete',
                        label: 'Delete',
                        icon: 'trash',
                        action: 'delete',
                        variant: 'destructive',
                        confirmMessage:
                            'Are you sure you want to delete the selected items? This action cannot be undone.',
                        shortcut: 'Delete',
                        disabled: false,
                        visible: true,
                        permissions: ['delete'],
                    },
                ],
            }),
        },
    },
    emits: ['action-clicked'],
    setup(props, { emit }) {
        const selectedRowsCount = computed(() => props.selectedRows.length);

        const filterActions = (actions) => {
            return actions.filter((action) => {
                // Check visibility
                if (!action.visible) return false;

                // Check permissions
                if (action.permissions && action.permissions.length > 0) {
                    const hasPermission = action.permissions.some((permission) =>
                        props.userPermissions.includes(permission)
                    );
                    if (!hasPermission) return false;
                }

                // Check selection constraints for bulk actions
                if (action.minSelection && selectedRowsCount.value < action.minSelection) {
                    return false;
                }
                if (action.maxSelection && selectedRowsCount.value > action.maxSelection) {
                    return false;
                }

                return true;
            });
        };

        const visiblePrimaryActions = computed(() =>
            filterActions(props.actionsConfig.primary || [])
        );
        const visibleSecondaryActions = computed(() =>
            filterActions(props.actionsConfig.secondary || [])
        );
        const visibleBulkActions = computed(() => {
            // Only show bulk actions when multiple items are selected
            if (selectedRowsCount.value <= 1) return [];
            return filterActions(props.actionsConfig.bulk || []);
        });
        const visibleDestructiveActions = computed(() =>
            filterActions(props.actionsConfig.destructive || [])
        );

        const handleItemClick = (item) => {
            if (item.disabled) return;

            // Show confirmation if required
            if (item.confirmMessage) {
                const confirmed = window.confirm(item.confirmMessage);
                if (!confirmed) return;
            }

            // Emit the action
            emit('action-clicked', {
                action: item,
                selectedRows: props.selectedRows,
            });
        };

        return {
            selectedRowsCount,
            visiblePrimaryActions,
            visibleSecondaryActions,
            visibleBulkActions,
            visibleDestructiveActions,
            handleItemClick,
        };
    },
};
</script>
