<template>
    <div>
        <MultiSelect
            ref="multiSelectRef"
            :access-key="accessKey"
            :adaptive="adaptive"
            :allow-custom="allowCustom"
            :area-described-by="areaDescribedBy"
            :area-label="areaLabel"
            :area-labelled-by="ariaLabelledBy"
            :auto-close="autoClose"
            :class="fieldClass"
            :data-items="dataItems"
            :data-item-key="dataItemKey"
            :default-value="defaultValue"
            :style="style"
            :popup-settings="popupSettings"
            :tag-render="'tagRender'"
            :disabled="disabled"
            :tab-index="tabIndex"
            :name="name"
            :id="id"
            :loading="loading"
            :placeholder="placeholder"
            :required="required"
            :text-field="textField"
            :value="value"
            :valid="valid"
            :validate="validate"
            :value-field="valueField"
            :valuePrimitive="valuePrimitive"
            @change="handleChange"
            :class-name="computedSizeClass"
            :footer="footer"
        >
            <template v-slot:tagRender="{ props }">
                <li
                    class="flex items-center gap-2 rounded-2xl border border-gray-300 px-2 py-1 text-xs leading-5"
                    v-if="props.index < 3"
                >
                    <span class="custom-tag">
                        {{ props.tagData.text }}
                    </span>
                    <span
                        class="cursor-pointer rounded-xl p-0.5 leading-5 text-gray-600 hover:bg-red-500/60 hover:[&_svg]:text-white"
                        @click="handleRemoveItem(props, $event)"
                    >
                        <icon name="cross" width="14" height="14" fill="currentColor" />
                    </span>
                </li>
                <template v-if="props.index === 3">
                    <div
                        class="flex h-7 w-7 cursor-pointer items-center justify-center rounded-full border border-gray-300 text-xs leading-5"
                        ref="popupRef"
                        @click="handleMore"
                    >
                        +{{ props.dataItems.length - 3 }}
                    </div>

                    <Popup
                        :anchor="'popupRef'"
                        :append-to="'body'"
                        :show="show"
                        :popup-class="'!px-2 !py-1 !w-auto !border border-gray-300 space-y-1'"
                        :anchor-align="anchorAlign"
                        :popup-align="popupAlign"
                        :animate="false"
                    >
                        <div class="flex flex-col gap-1">
                            <div
                                v-for="(item, index) in props.dataItems.slice(2)"
                                :key="index"
                                class="k-link flex items-center justify-between gap-2 rounded hover:bg-gray-100"
                            >
                                <span class="p-1 leading-5">{{ item.text }}</span>
                                <span
                                    class="cursor-pointer rounded-xl p-0.5 leading-5 text-gray-600 hover:bg-red-500/60 hover:[&_svg]:text-white"
                                    @click="handleRemoveItem(props, $event)"
                                >
                                    <icon name="cross" width="14" height="14" fill="currentColor" />
                                </span>
                            </div>
                        </div>
                    </Popup>
                </template>
            </template>
            <template v-slot:[footer]="{ props }">
                <slot :name="footer" v-bind:props="props"></slot>
            </template>
        </MultiSelect>
    </div>
</template>
<script>
import { MultiSelect } from '@progress/kendo-vue-dropdowns';
import { Popup } from '@progress/kendo-vue-popup';
import { twMerge } from 'tailwind-merge';

export default {
    props: {
        modelValue: {
            type: Array,
            default: () => [],
        },
        // Define all Kendo MultiSelect props here to accept them
        accessKey: String,
        adaptive: Boolean,
        adaptiveTitle: String,
        allowCustom: Boolean,
        ariaDescribedBy: String,
        ariaLabel: String,
        ariaLabelledBy: String,
        autoClose: Boolean,
        dataItemKey: String,
        dataItems: Array,
        defaultValue: Array,
        dir: String,
        disabled: Boolean,
        fillMode: String,
        filter: String,
        filterable: Boolean,
        // focusedItemIndex: Function,
        // footer: [String, Object, Function],
        groupField: String,
        // groupHeaderItemRender: Function,
        // groupStickyHeaderItemRender: Function,
        // header: [String, Object, Function],
        id: String,
        inputAttributes: Object,
        // itemRender: Function,
        label: String,
        // listNoDataRender: Function,
        loading: Boolean,
        name: String,
        placeholder: String,
        popupSettings: {
            type: Object,
            default: {
                animate: false,
            },
        },
        removeTagIcon: String,
        required: {
            type: Boolean,
            default: false,
        },
        rounded: String,
        size: String,
        tabIndex: Number,
        // tagRender: Function,
        tags: Array,
        tagsRounded: String,
        textField: String,
        valid: {
            type: Boolean,
            default: true,
        },
        validate: {
            type: Boolean,
            default: false,
        },
        validationMessage: String,
        validityStyles: Boolean,
        valueField: String,
        valuePrimitive: Boolean,
        virtual: Object,

        // Events
        onBlur: Function,
        onClose: Function,
        onFilterchange: Function,
        onFocus: Function,
        onOpen: Function,
        onPagechange: Function,
        opened: Boolean,
        pt: {
            type: Object,
            default: () => {},
        },
        variant: String,
        footer: String,
    },
    emits: ['update:modelValue', 'remove', 'update:multiSelectRef'],
    components: {
        MultiSelect,
        Popup,
    },
    computed: {
        kendoProps() {
            const allProps = { ...this.$props };
            delete allProps.modelValue;
            return allProps;
        },
        fieldClass() {
            let extraClass = '';
            if (this.variant === 'borderless') {
                extraClass = 'tw-input-borderless';
            }
            return twMerge('tw-multiple-dropdown', extraClass, this.pt?.field);
        },
    },
    mounted() {
        document.addEventListener('click', this.clickOutsideHandler);
        this.$emit('update:multiSelectRef', this.$refs.multiSelectRef);
    },
    beforeDestroy() {
        document.removeEventListener('click', this.clickOutsideHandler);
    },
    data() {
        return {
            show: false,
            anchorAlign: { horizontal: 'right', vertical: 'bottom' },
            popupAlign: { horizontal: 'right', vertical: 'top' },
        };
    },
    methods: {
        handleMore(e) {
            this.show = !this.show;
            e.stopPropagation();
        },
        handleRemoveItem(props, event) {
            event.stopPropagation();
            props.onRemove();
        },
        handleRemove(event) {
            this.$emit('remove', event);
        },
        clickOutsideHandler(event) {
            const popupElement = this.$refs.popupRef;
            if (!popupElement) return;
            const clickedInsidePopup = popupElement.contains(event.target);
            const clickedOnLink = event.target.classList.contains('k-link');
            if (this.show && !clickedInsidePopup && !clickedOnLink) {
                this.show = false;
            }
        },
        handleChange(event) {
            console.log('Event', event);
            this.$emit('update:modelValue', event.value);
        },
    },
};
</script>
<style lang=""></style>
