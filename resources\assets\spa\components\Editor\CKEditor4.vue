<template>
    <textarea id="editor" :value="modelValue"></textarea>
</template>

<script>
import { loadCkEditor } from '@spa/helpers/index.js';

export default {
    name: 'CkEditor4',
    props: ['modelValue'],
    emits: ['update:modelValue'],
    async mounted() {
        try {
            await loadCkEditor();
            this.initEditor();
        } catch (err) {
            console.error('Editor failed to load');
        }
    },
    beforeUnmount() {
        const editor = CKEDITOR.instances.editor;
        if (editor) {
            editor.destroy(true);
        }
    },
    methods: {
        initEditor() {
            const vm = this;

            CKEDITOR.replace('editor', {
                allowedContent: true,
                fullPage: true,
                autoGrow_onStartup: true,
                height: '500px',
                on: {
                    pluginsLoaded: function () {
                        const editor = this;
                        const config = editor.config;

                        editor.addCommand('resetTemplate', {
                            exec: function (editor) {
                                $('<div id="clnBody"></div>')
                                    .appendTo('body')
                                    .html('<div><h6>Are you sure?</h6></div>')
                                    .dialog({
                                        modal: true,
                                        title: 'Reset PDF Template',
                                        zIndex: 10000,
                                        width: 'auto',
                                        resizable: false,
                                        buttons: {
                                            Yes: function () {
                                                var tId = $('#id').val();
                                                var dataArr = { id: tId };
                                                var url = 'pdf-template/ajaxAction';
                                                var action = 'getPdfTemplateOfDefaultTemplate';

                                                ajaxAction(
                                                    url,
                                                    action,
                                                    dataArr,
                                                    function (resultData) {
                                                        var result = jQuery.parseJSON(resultData);
                                                        editor.setData('');
                                                        setTimeout(function () {
                                                            editor.insertHtml(
                                                                result[0].default_template
                                                            );
                                                            $('#clnBody').remove();
                                                        }, 1000);
                                                    }
                                                );
                                            },
                                            No: function () {
                                                $(this).remove();
                                            },
                                        },
                                    });
                            },
                        });

                        editor.ui.addButton('RU', {
                            label: 'Reset',
                            command: 'resetTemplate',
                            icon: 'https://cdn-icons-png.flaticon.com/128/1632/1632708.png', // your reset icon
                        });

                        editor.ui.addRichCombo('my-combo', {
                            label: 'Select Parameters',
                            title: 'Select Parameters',
                            toolbar: 'basicstyles,0',
                            panel: {
                                css: [CKEDITOR.skin.getPath('editor')].concat(config.contentsCss),
                                multiSelect: false,
                                attributes: { 'aria-label': 'Select Parameters' },
                            },
                            init: function () {
                                const that = this;
                                that.startGroup('Select Parameters');
                                const result = JSON.parse(paramScript);
                                Object.keys(result).forEach((key) => {
                                    that.add(key, result[key]);
                                });
                            },
                            onClick: function (value) {
                                editor.focus();
                                editor.fire('saveSnapshot');
                                editor.insertHtml(value);
                                editor.fire('saveSnapshot');
                            },
                        });
                    },

                    change: function () {
                        vm.$emit('update:modelValue', this.getData());
                    },
                },
            });
        },
    },
    watch: {
        modelValue(newVal) {
            const editor = CKEDITOR.instances.editor;
            if (editor && newVal !== editor.getData()) {
                editor.setData(newVal || '');
            }
        },
    },
};
</script>
