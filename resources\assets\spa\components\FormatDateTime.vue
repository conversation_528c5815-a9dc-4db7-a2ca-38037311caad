<template>
    <span :class="textClass">{{ formattedDateRange }}</span>
</template>

<script>
import dayjs from 'dayjs';
import { twMerge } from 'tailwind-merge';
const defaultFormat = 'DD MMM YYYY hh:mm:ss A';
export default {
    name: 'FormattedDate',
    props: {
        date: {
            type: String,
        },
        notvalid: {
            type: String,
            default: '',
        },
        formatType: {
            type: String,
            default: defaultFormat,
        },
        endDate: {
            type: String,
            default: null,
        },
        prefix: {
            type: String,
            default: '',
        },
        suffix: {
            type: String,
            default: '',
        },
        infix: {
            type: String,
            default: '',
        },
        pt: {
            type: Object,
            default: {},
        },
    },
    methods: {
        formatDate(date, format = null) {
            if (!date) {
                return '';
            }
            const inputFormats = [
                'MMM DD, YYYY',
                'DD MMM YYYY',
                'MM/DD/YYYY',
                'MM-DD-YYYY',
                'DD-MM-YYYY',
            ];
            const parsed = dayjs(date, inputFormats);
            if (!parsed.isValid()) return this.notvalid;
            const dateFormat = format || defaultFormat;
            try {
                return parsed.format(dateFormat);
            } catch (error) {
                return parsed.format(defaultFormat);
            }
        },
    },
    computed: {
        formattedStartDate() {
            if (!this.date) {
                console.error('Date is required.');
                return '';
            }
            return this.formatDate(this.date, this.formatType);
        },
        formattedEndDate() {
            if (this.endDate) {
                return this.formatDate(this.endDate, this.formatType);
            }
            return null;
        },
        formattedDateRange() {
            let result = this.prefix ? `${this.prefix} ` : '';
            result += this.formattedStartDate;
            if (this.endDate) {
                result += this.infix ? ` ${this.infix} ` : ' ';
                result += this.formattedEndDate;
            }
            if (this.suffix) {
                result += ` ${this.suffix}`;
            }
            return result;
        },
        textClass() {
            return twMerge('text-sm text-gray-700', this.pt.text);
        },
    },
};
</script>
