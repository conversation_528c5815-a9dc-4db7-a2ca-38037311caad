<template>
    <div :class="rootClass">
        <span :class="titleClass">{{ props.title }}</span>
        <span class="flex flex-1 justify-end gap-1">
            <sort-indicator :dir="dir" v-if="props.sortable" />
            <VDropdown :distance="6" :triggers="['click']" v-if="filterable">
                <icon name="grid-filter" fill="currentColor" />
                <template #popper>
                    <div class="p-2"></div>
                </template>
            </VDropdown>
        </span>
    </div>
</template>
<script>
import { twMerge } from 'tailwind-merge';
import SortingIndicator from '../SortingIndicator.vue';
export default {
    props: {
        props: Object,
        pt: {
            type: Object,
            default: {},
        },
        dir: {
            type: String,
            default: 'default',
        },
        filterable: {
            type: Boolean,
            default: false,
        },
    },
    components: {
        'sort-indicator': SortingIndicator,
    },
    computed: {
        rootClass() {
            return twMerge(
                this.props.class,
                this.pt.rootClass,
                'cursor-pointer flex items-center justify-between gap-2'
            );
        },
        titleClass() {
            return twMerge('text-13 truncate', this.pt.titleClass);
        },
    },
    methods: {},
};
</script>
<style lang=""></style>
