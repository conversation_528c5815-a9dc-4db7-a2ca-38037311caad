<template lang="">
    <td
        :class="rootClass"
        :colspan="props.colspan"
        :role="props.role"
        @click="$emit('itemClick', $event)"
    >
        <a :target="openInNewTab ? '_blank' : '_self'" :href="getLink" :class="wrapperClass">{{
            props.dataItem[props.field]
        }}</a>
    </td>
</template>
<script>
import { twMerge } from 'tailwind-merge';
export default {
    props: {
        props: Object,
        pt: {
            type: Object,
            default: {},
        },
    },
    emits: {
        itemClick: null,
    },
    computed: {
        rootClass() {
            return twMerge('group', this.props.class, this.pt.root);
        },
        wrapperClass() {
            return twMerge(
                'flex items-center gap-1 !text-primary-blue-500 group-hover:underline',
                this.pt.wrapper
            );
        },
        getLink() {
            return this.props.dataItem.link;
        },
        openInNewTab() {
            return this.props.dataItem.openInNewTab; // Assuming `openInNewTab` is in your dataItem
        },
    },
};
</script>
<style lang=""></style>
