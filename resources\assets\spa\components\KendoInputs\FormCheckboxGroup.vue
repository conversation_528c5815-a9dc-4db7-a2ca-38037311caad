<template>
    <fieldwrapper :class="rootClasses">
        <klabel
            :id="labelId"
            :editor-id="id"
            :editor-valid="valid"
            :editor-disabled="disabled"
            :class="labelClass"
        >
            {{ label }}
        </klabel>
        <div class="space-y-2">
            <div
                class="k-form-field-wrap"
                v-for="(item, index) in dataItems"
                :key="index"
            >
                <checkbox
                    :valid="valid"
                    :name="name"
                    :id="`checkbox_${item.id}`"
                    @change="handleChange"
                    @blur="handleBlur"
                    @focus="handleFocus"
                    :class="fieldClasses"
                    :disabled="disabled"
                    :checked="value"
                >
                    <label
                        :class="checkboxTextClass"
                        :for="`checkbox_${item.id}`"
                        :editor-id="id"
                        :editor-valid="valid"
                        :optional="optional"
                        :style="{ display: 'inline-block' }"
                    >
                        {{ item.label }}
                    </label>
                </checkbox>
            </div>
        </div>
        <error v-if="showValidationMessage">
            {{ validationMessage }}
        </error>
        <hint v-else>{{ hint }}</hint>
    </fieldwrapper>
</template>
<script>
import { FieldWrapper } from "@progress/kendo-vue-form";
import { Error, Hint, Label } from "@progress/kendo-vue-labels";
import { Checkbox } from "@progress/kendo-vue-inputs";
import { twMerge } from "tailwind-merge";

export default {
    props: {
        touched: Boolean,
        label: String,
        validationMessage: String,
        hint: String,
        id: String,
        valid: Boolean,
        disabled: Boolean,
        optional: Boolean,
        visited: Boolean,
        modified: Boolean,
        checked: { Boolean, default: false },
        defaultChecked: { Boolean, default: false },
        value: {
            type: [Boolean, String, Number],
            default: false,
        },
        name: String,
        dataItems: {
            type: Array,
            default: [],
        },
        pt: {
            type: Object,
            default: {},
        },
    },
    emits: {
        change: null,
        blur: null,
        focus: null,
    },
    components: {
        fieldwrapper: FieldWrapper,
        error: Error,
        hint: Hint,
        klabel: Label,
        checkbox: Checkbox,
    },
    data: function () {
        return {
            boxchecked: false,
        };
    },
    mounted() {
        this.boxchecked = this.checked;
    },
    computed: {
        showValidationMessage() {
            return this.$props.touched && this.$props.validationMessage;
        },
        showHint() {
            return !this.showValidationMessage && this.$props.hint;
        },
        hintId() {
            return this.showHint ? `${this.$props.id}_hint` : "";
        },
        errorId() {
            return this.showValidationMessage ? `${this.$props.id}_error` : "";
        },
        labelClass() {
            return twMerge("k-form-label k-checkbox-label", this.pt.label);
        },
        fieldClasses() {
            return twMerge("tw-input__checkbox", this.className, this.pt.field);
        },
        rootClasses() {
            return twMerge("tw-form__wrapper", this.pt.root);
        },
        checkboxTextClass() {
            return twMerge(
                "tw-input__checklabel  text-gray-700  ml-1",
                this.pt.flabel,
            );
        },
    },
    methods: {
        handleChange(e) {
            this.boxchecked = e.value;
            this.$emit("change", e);
        },
        handleBlur(e) {
            this.$emit("blur", e);
        },
        handleFocus(e) {
            this.$emit("focus", e);
        },
    },
};
</script>
