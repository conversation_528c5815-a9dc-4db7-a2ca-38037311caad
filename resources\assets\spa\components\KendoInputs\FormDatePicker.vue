<template>
    <fieldwrapper :class="rootClass">
        <klabel
            :editor-id="id"
            :editor-valid="internalValid"
            :disabled="disabled"
            :optional="optional"
            :class="labelClass"
        >
            {{ label }}
            <span v-if="indicaterequired || required" class="ml-1 text-red-500">*</span>
            <i class="block text-[8px] text-gray-500" v-if="helperText"> [{{ helperText }}] </i>
        </klabel>
        <div :class="wrapClass">
            <datepicker
                :format="format"
                :formatPlaceholder="formatPlaceholder"
                :valid="internalValid"
                :id="id"
                :disabled="disabled"
                :placeholder="placeholder"
                :min="min"
                :max="max"
                :default-value="getDefaultValue"
                @change="handleChange"
                @input="handleInput"
                @blur="handleBlur"
                @focus="handleFocus"
                :class="fieldClass"
            />
            <error v-if="showValidationMessage">
                {{ validationMessage }}
            </error>
            <hint v-else>{{ hint }}</hint>
        </div>
    </fieldwrapper>
</template>

<script>
import { FieldWrapper } from '@progress/kendo-vue-form';
import { Error, Hint, Label } from '@progress/kendo-vue-labels';
import { DatePicker } from '@progress/kendo-vue-dateinputs';
import { twMerge } from 'tailwind-merge';
import { parseDate, formatDate } from '@progress/kendo-intl';

export default {
    props: {
        modelValue: [Date, String],
        optional: Boolean,
        disabled: Boolean,
        placeholder: {
            type: String,
            default: 'dd-mm-yyyy',
        },
        touched: Boolean,
        label: String,
        format: { type: String, default: 'dd-MM-yyyy' },
        emitFormat: { type: String, default: null },
        validationMessage: String,
        hint: String,
        helperText: String,
        id: String,
        valid: {
            type: Boolean,
            default: true,
        },
        defaultValue: {
            type: [null, Date],
            default: null,
        },
        min: {
            type: Date,
            default: () => new Date('1900'),
        },
        max: {
            type: Date,
            default: () => new Date(new Date().setFullYear(new Date().getFullYear() + 100)),
        },
        value: {
            type: [Date, String],
            default: null,
        },
        pt: {
            type: Object,
            default: () => ({}),
        },
        indicaterequired: { type: Boolean, default: false },
        required: { type: Boolean, default: false },
        orientation: { type: String, default: 'vertical' },
        formatPlaceholder: {
            type: Object,
            default: () => ({
                day: 'dd',
                month: 'mm',
                year: 'yyyy',
            }),
        },
    },
    components: {
        fieldwrapper: FieldWrapper,
        error: Error,
        hint: Hint,
        klabel: Label,
        datepicker: DatePicker,
    },
    data() {
        return {
            internalValid: this.valid,
        };
    },
    computed: {
        showValidationMessage() {
            return this.touched && this.validationMessage;
        },
        showHint() {
            return !this.showValidationMessage && this.hint;
        },
        hintId() {
            return this.showHint ? `${this.id}_hint` : '';
        },
        errorId() {
            return this.showValidationMessage ? `${this.id}_error` : '';
        },
        getDefaultValue() {
            const parsedDate = new Date(this.value || this.modelValue);
            if (
                this.value ||
                (this.modelValue &&
                    !isNaN(parsedDate.getTime()) &&
                    parsedDate >= this.min &&
                    parsedDate <= this.max)
            ) {
                return parsedDate;
            } else {
                return this.defaultValue;
            }
        },
        fieldClass() {
            return twMerge('tw-input__datepicker', this.pt.field);
        },
        rootClass() {
            const baseClass = 'tw-form__fieldwrapper';
            const orientationClass = this.orientation === 'horizontal' ? 'field-horizontal' : '';
            return twMerge(`${baseClass} ${orientationClass}`, this.pt.root);
        },
        wrapClass() {
            return twMerge('k-form-field-wrap', this.pt.wrap);
        },
        labelClass() {
            return twMerge(
                'tw-form__label mb-1 font-medium leading-5 text-gray-700',
                this.pt.label
            );
        },
    },
    emits: ['input', 'change', 'blur', 'focus', 'update:modelValue'],
    watch: {
        modelValue(newVal, oldVal) {
            if (newVal !== oldVal) {
                this.internalValid = true; // reset validation on modelValue change
            }
        },
        valid(newVal) {
            this.internalValid = newVal; // sync from parent
        },
    },
    methods: {
        handelEmitFormat(value) {
            if (!this.emitFormat) return value;
            const formatedDate = parseDate(value);
            if (formatedDate) {
                return formatDate(formatedDate, this.emitFormat);
            }
            return value;
        },
        handleChange(e) {
            this.$emit('change', e);
            this.$emit('update:modelValue', this.handelEmitFormat(e.value));
        },
        handleInput(e) {
            this.$emit('input', e);
            this.$emit('update:modelValue', this.handelEmitFormat(e.value));
        },
        handleBlur(e) {
            this.$emit('blur', e);
        },
        handleFocus(e) {
            this.$emit('focus', e);
        },
    },
};
</script>
