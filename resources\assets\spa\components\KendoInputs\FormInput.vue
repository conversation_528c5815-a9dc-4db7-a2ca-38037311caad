<template>
    <fieldwrapper :class="rootClass">
        <klabel
            :class="labelClass"
            :editor-id="id"
            :editor-valid="internalValid"
            :disabled="disabled"
            :optional="optional"
        >
            {{ label }}
            <div v-if="hint && hintOnLabel" class="font-light italic text-gray-400">
                ({{ hint }})
            </div>
            <span v-if="required || indicaterequired" class="ml-1 text-red-500">*</span>
        </klabel>
        <div :class="wrapClass">
            <div class="relative">
                <kinput
                    v-model="vModel"
                    :value="value"
                    :valid="internalValid"
                    :id="id"
                    :default-value="defaultValue"
                    :disabled="disabled"
                    :placeholder="placeholder"
                    :name="name"
                    :class="inputClass"
                    :type="type === 'password' ? (showPassword ? 'text' : 'password') : type"
                    @change="handleUpdate"
                    @input="handleChange"
                    @blur="handleBlur"
                    @focus="handleFocus"
                />
                <button
                    @click="showPassword = !showPassword"
                    class="absolute right-2 top-1/2 -translate-y-1/2"
                    type="button"
                    v-if="type === 'password'"
                >
                    <PasswordToggleIcon :isVisible="showPassword" />
                </button>
            </div>
            <error v-if="showValidationMessage">
                {{ validationMessage }}
            </error>
            <hint v-else-if="!showValidationMessage && !hintOnLabel">{{ hint }}</hint>
        </div>
    </fieldwrapper>
</template>
<script>
import { FieldWrapper } from '@progress/kendo-vue-form';
import { Error, Hint, Label } from '@progress/kendo-vue-labels';
import { Input } from '@progress/kendo-vue-inputs';
import PasswordToggleIcon from '@spa/components/Icons/PasswordToggleIcon.vue';
import { twMerge } from 'tailwind-merge';

export default {
    props: {
        modelValue: [String, Number],
        optional: Boolean,
        disabled: Boolean,
        placeholder: String,
        touched: Boolean,
        label: String,
        validationMessage: String,
        hint: String,
        hintOnLabel: { type: Boolean, default: false },
        type: { type: String, default: 'text' },
        id: String,
        valid: {
            type: Boolean,
            default: true,
        },
        class: String,
        indicaterequired: { type: Boolean, default: false },
        orientation: { type: String, default: 'vertical' },
        value: [String, Number],
        defaultValue: String,
        name: String,
        required: { type: Boolean, default: false },
        pt: {
            type: Object,
            default: () => ({}),
        },
    },
    components: {
        fieldwrapper: FieldWrapper,
        error: Error,
        hint: Hint,
        klabel: Label,
        kinput: Input,
        PasswordToggleIcon,
    },
    data() {
        return {
            showPassword: false,
            internalValid: this.valid,
        };
    },
    computed: {
        vModel: {
            get() {
                return this.modelValue;
            },
            set(value) {
                this.$emit('update:modelValue', value);
            },
        },
        showValidationMessage() {
            return this.touched && this.validationMessage;
        },
        showHint() {
            return !this.showValidationMessage && this.hint;
        },
        hintId() {
            return this.showHint ? `${this.id}_hint` : '';
        },
        errorId() {
            return this.showValidationMessage ? `${this.id}_error` : '';
        },
        rootClass() {
            const baseClass = 'tw-form__fieldwrapper';
            const orientationClass = this.orientation === 'horizontal' ? 'field-horizontal' : '';
            return twMerge(`${baseClass} ${orientationClass}`, this.pt.root);
        },
        wrapClass() {
            return twMerge('k-form-field-wrap', this.pt.wrap);
        },
        labelClass() {
            return twMerge(
                'tw-form__label mb-1 font-medium leading-5 text-gray-700',
                this.pt.label
            );
        },
        inputClass() {
            return twMerge('tw-form__input', this.pt.input);
        },
    },
    watch: {
        modelValue(newVal, oldVal) {
            if (newVal !== oldVal) {
                this.internalValid = true; // Reset valid on input change
            }
        },
        valid(newVal) {
            this.internalValid = newVal; // sync from parent
        },
    },
    emits: ['input', 'change', 'blur', 'focus', 'update:modelValue'],
    methods: {
        handleChange(e) {
            this.$emit('change', e);
        },
        handleBlur(e) {
            this.$emit('change', e);
            this.$emit('blur', e);
        },
        handleFocus(e) {
            this.$emit('focus', e);
        },
        handleUpdate(e) {
            // Optional: custom update logic
        },
    },
};
</script>
