<template>
    <fieldwrapper>
        <KLabel
            :editor-id="id"
            :editor-valid="valid"
            :disabled="disabled"
            :optional="optional"
            v-if="label"
        >
            {{ label }}
            <span v-if="required || indicaterequired" class="ml-1 text-red-500">*</span>
        </KLabel>
        <div class="k-form-field-wrap tw-suffix-textbox">
            <KInputBox
                :valid="valid"
                :id="id"
                :value="value"
                :disabled="disabled"
                :placeholder="placeholder"
                :input-suffix="'suffix'"
                :spinners="spinners"
                :step="step"
                :min="min"
                :max="max"
                :format="format"
                @input="handleChange"
                @blur="handleBlur"
                @focus="handleFocus"
            >
                <template v-slot:suffix v-if="suffix">
                    <DropDownList
                        :data-items="dropdownData"
                        :text-field="dropdownField"
                        :data-item-key="dropdownKey"
                        :value-field="dropdownValue"
                        :value-primitive="dropdownPrimitive"
                        :value="suffix"
                        @change="handleDropdownChange"
                        v-if="dropdownData"
                        :popup-settings="{
                            className: 'tw-width-auto',
                            animate: false,
                        }"
                    >
                    </DropDownList>
                    <div class="px-2" v-else>{{ suffix }}</div>
                </template>
            </KInputBox>
            <error v-if="showValidationMessage">
                {{ validationMessage }}
            </error>
            <hint v-else>{{ hint }}</hint>
        </div>
    </fieldwrapper>
</template>
<script>
import { FieldWrapper } from '@progress/kendo-vue-form';
import { Error, Hint, Label as KLabel } from '@progress/kendo-vue-labels';
import { NumericTextBox as KInputBox } from '@progress/kendo-vue-inputs';
import { DropDownList } from '@progress/kendo-vue-dropdowns';
export default {
    props: {
        optional: Boolean,
        disabled: Boolean,
        placeholder: String,
        spinners: { type: Boolean, default: false },
        step: {
            type: [Number],
            default: 1,
        },
        min: [null, Number],
        max: [null, Number],
        touched: Boolean,
        label: String,
        validationMessage: String,
        hint: String,
        id: String,
        format: String,
        valid: Boolean,
        suffix: { type: [Number, String, Object], default: null },
        dropdownData: { type: Object, default: null },
        dropdownField: { type: String, default: 'text' },
        dropdownKey: { type: String, default: 'id' },
        dropdownValue: { type: String, default: 'id' },
        dropdownPrimitive: { type: Boolean, default: false },
        value: {
            type: [String, Number],
            default: '',
        },
        indicaterequired: { type: Boolean, default: false },
        required: { type: Boolean, default: false },
    },
    components: {
        fieldwrapper: FieldWrapper,
        DropDownList,
        error: Error,
        hint: Hint,
        KLabel,
        KInputBox,
    },
    data: function () {
        return {
            show: false,
        };
    },
    computed: {
        filterChange(event) {
            this.items = this.filterData(event.filter);
        },
        filterData(filter) {
            const data = allData.slice();
            return filterBy(data, filter);
        },
        showValidationMessage() {
            return this.$props.touched && this.$props.validationMessage;
        },
        showHint() {
            return !this.showValidationMessage && this.$props.hint;
        },
        hintId() {
            return this.showHint ? `${this.$props.id}_hint` : '';
        },
        errorId() {
            return this.showValidationMessage ? `${this.$props.id}_error` : '';
        },
    },
    emits: {
        input: null,
        change: null,
        blur: null,
        focus: null,
    },
    methods: {
        handleChange(e) {
            this.$emit('change', e);
        },
        handleBlur(e) {
            this.$emit('change', e);
            this.$emit('blur', e);
        },
        handleFocus(e) {
            this.$emit('focus', e);
        },
        handleDropdownChange(e) {
            this.$emit('suffixchanged', e);
        },
    },
};
</script>
