<template>
    <div class="flex items-center gap-3">
        <Switch
            :checked="modelValue"
            @change="handleChange"
            @blur="handleBlur"
            @focus="handleFocus"
            :disabled="disabled"
            :class="switchClasses"
        />
        <span v-if="label" :class="labelClasses">{{ label }}</span>
    </div>
</template>

<script setup>
import { computed } from 'vue';
import { Switch } from '@progress/kendo-vue-inputs';
import { twMerge } from 'tailwind-merge';

const props = defineProps({
    modelValue: {
        type: Boolean,
        default: false,
    },
    label: {
        type: String,
        default: '',
    },
    disabled: {
        type: Boolean,
        default: false,
    },
    size: {
        type: String,
        default: 'normal',
        validator: (value) => ['small', 'normal', 'large'].includes(value),
    },
    pt: {
        type: Object,
        default: () => ({}),
    },
});

const emit = defineEmits(['update:modelValue', 'change', 'blur', 'focus']);

const switchClasses = computed(() => {
    const sizeClasses = {
        small: 'scale-75',
        normal: '',
        large: 'scale-125',
    };

    return twMerge('transition-all duration-200', sizeClasses[props.size], props.pt.switch);
});

const labelClasses = computed(() => {
    const sizeClasses = {
        small: 'text-sm',
        normal: 'text-base',
        large: 'text-lg',
    };

    return twMerge(
        'font-medium text-gray-700 select-none',
        sizeClasses[props.size],
        props.disabled ? 'opacity-50' : '',
        props.pt.label
    );
});

const handleChange = (event) => {
    // Kendo Switch provides the value in event.value, not event.target.checked
    const newValue = event.value;
    emit('update:modelValue', newValue);
    emit('change', event);
};

const handleBlur = (event) => {
    emit('blur', event);
};

const handleFocus = (event) => {
    emit('focus', event);
};
</script>

<style scoped></style>
