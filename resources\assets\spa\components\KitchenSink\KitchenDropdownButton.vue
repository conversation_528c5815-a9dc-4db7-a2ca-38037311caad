<template lang="">
    <ks-component
        title="Dropdown Button"
        :importValue="importValue"
        :componentValue="componentValue"
    >
        <template #contentSlot>
            <p>Icon dropdown buttons</p>
        </template>
        <template #previewSlot>
            <div class="flex items-center justify-center"></div>
        </template>
    </ks-component>
</template>
<script>
import KitchenComponent from './KitchenComponent.vue';
import { moreHorizontalIcon } from '@progress/kendo-svg-icons';

export default {
    components: {
        'dropdown-menu': DropDownMenu,
        'ks-component': KitchenComponent,
    },
    data: function () {
        return {
            icon: moreHorizontalIcon,
            options: [
                {
                    text: 'Course Structure',
                    icon: 'structure',
                },
                {
                    text: 'Deactivate',
                    icon: 'toggle-off',
                },
                {
                    text: 'Delete',
                    icon: 'delete',
                },
            ],
            importValue: `import { moreHorizontalIcon } from "@progress/kendo-svg-icons";

export default {
    components: {
        "dropdown-menu": DropDownMenu,
        "ks-component": KitchenComponent,
    },
    data: function () {
        return {
            icon: moreHorizontalIcon,
            options: [
                {
                    text: "Course Structure",
                    icon: "structure",
                },
                {
                    text: "Deactivate",
                    icon: "toggle-off",
                },
                {
                    text: "Delete",
                    icon: "delete",
                },
            ],
            }
        }
    }
}`,
            componentValue: `<dropdown-menu :items="options" :icon="icon"></dropdown-menu>`,
        };
    },
};
</script>
<style lang=""></style>
