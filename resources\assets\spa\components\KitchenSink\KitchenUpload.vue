<template lang="">
    <ks-component
        title="File Upload"
        :importValue="importValue"
        :componentValue="componentValue"
        :bladeValue="bladeValue"
    >
        <template #contentSlot>
            <p>Reusable component for upload file</p>
        </template>
        <template #previewSlot>
            <div class="space-y-6">
                <ks-uploader :variant="'dropzone'" />
                <ks-uploader />
            </div>
        </template>
    </ks-component>
</template>
<script>
import KitchenComponent from './KitchenComponent.vue';
// import Avatar from "../Avatar/Avatar.vue";
import FileUploader from '../Uploader/FileUploader.vue';

export default {
    components: {
        'ks-component': KitchenComponent,
        'ks-uploader': FileUploader,
    },
    data() {
        return {
            importValue: `import CopyAction from "../CopyAction.vue";`,
            componentValue: `<copy-action textToCopy="This is the text" />`,
            bladeValue: `<x-v2.copy data-text="Text to copy" />`,
        };
    },
};
</script>
<style lang=""></style>
