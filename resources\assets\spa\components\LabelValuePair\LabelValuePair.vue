<template>
    <div :class="rootClass">
        <div :class="labelClass">
            {{ label }}
        </div>
        <slot v-if="$slots.default" />
        <div v-else-if="$slots.valueSlot" :class="valueClass">
            <slot name="valueSlot" />
        </div>
        <template v-else>
            <FormatDate
                :date="value"
                v-if="fieldType == 'date'"
                :pt="{ text: 'font-normal text-sm text-gray-900 justify-end flex' }"
            />
            <div :class="valueClass" v-else class="truncate">
                {{ value }}
                <div v-if="label === 'Application ID'">
                    <copy-action :text="value" />
                </div>
            </div>
        </template>
    </div>
    <div class="h-px w-full border border-gray-200" v-if="bordered"></div>
</template>
<script>
import { twMerge } from 'tailwind-merge';
import CopyAction from '@spa/components/CopyAction.vue';
import FormatDate from '@spa/components/FormatDate.vue';
export default {
    props: {
        label: String,
        value: String,
        fieldType: { type: String, default: 'text' },
        pt: {
            type: Object,
            default: {},
        },
        layout: {
            type: String,
            default: 'grid',
        },
        bordered: {
            type: Boolean,
            default: false,
        },
        orientation: {
            type: String,
            default: 'horizontal',
        },
    },
    components: {
        'copy-action': CopyAction,
        FormatDate,
    },
    computed: {
        rootClass() {
            if (this.orientation === 'vertical') {
                return twMerge('space-y-1', this.pt.root);
            }
            return twMerge(
                this.layout === 'grid' ? 'grid grid-cols-3 gap-2' : 'flex justify-between',
                this.pt.root
            );
        },
        labelClass() {
            return twMerge(
                ' text-gray-500',
                this.orientation === 'vertical' ? 'text-xs' : 'col-span-1',
                this.pt.label
            );
        },
        valueClass() {
            return twMerge('col-span-2 text-gray-900 flex gap-1', this.pt.value);
        },
    },
};
</script>
<style lang=""></style>
