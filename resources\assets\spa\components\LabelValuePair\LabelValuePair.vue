<template lang="">
    <div :class="rootClass">
        <div :class="labelClass">
            {{ label }}
        </div>
        <div v-if="$slots.valueSlot" :class="valueClass">
            <slot name="valueSlot" />
        </div>
        <template v-else>
            <FormatDate
                :date="value"
                v-if="fieldType == 'date'"
                :pt="{ text: 'font-normal text-sm text-gray-900 justify-end flex' }"
            />
            <div :class="valueClass" v-else class="truncate">
                {{ value }}
                <div v-if="label === 'Application ID'">
                    <copy-action :text="value" />
                </div>
            </div>
        </template>
    </div>
    <div class="h-px w-full border border-gray-200" v-if="bordered"></div>
</template>
<script>
import { twMerge } from 'tailwind-merge';
import CopyAction from '@spa/components/CopyAction.vue';
import FormatDate from '@spa/components/FormatDate.vue';
export default {
    props: {
        label: String,
        value: String,
        fieldType: { type: String, default: 'text' },
        pt: {
            type: Object,
            default: {},
        },
        layout: {
            type: String,
            default: 'grid',
        },
        bordered: {
            type: Boolean,
            default: false,
        },
    },
    components: {
        'copy-action': CopyAction,
        FormatDate,
    },
    computed: {
        rootClass() {
            return twMerge(
                this.layout === 'grid' ? 'grid grid-cols-3' : 'flex justify-between',
                this.pt.root
            );
        },
        labelClass() {
            return twMerge('col-span-1 font-medium text-gray-500', this.pt.label);
        },
        valueClass() {
            return twMerge('col-span-2 text-gray-900 flex gap-1', this.pt.value);
        },
    },
};
</script>
<style lang=""></style>
