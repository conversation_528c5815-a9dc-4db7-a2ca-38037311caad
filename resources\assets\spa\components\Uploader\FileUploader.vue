<template>
    <div :class="rootClass">
        <k-external
            :upload-ref="'upload'"
            :custom-hint="hintUI"
            :custom-note="noteUI"
            :disabled="disabled"
            v-if="variant === 'dropzone'"
            :element="'myElement'"
            @click="onButtonClick"
        >
            <template v-slot:myHintTemplate>
                <div class="flex items-center gap-1">
                    <span v-if="linkedMsg" class="font-medium text-primary-blue-500">{{
                        linkedMsg
                    }}</span>
                    <span v-if="hintMsg" class="text-gray-700">{{ hintMsg }}</span>
                </div>
            </template>
            <template v-slot:myNoteTemplate>
                <span v-if="noteMsg" class="text-xxs">{{ noteMsg }}</span>
            </template>
        </k-external>
        <tw-button
            variant="secondary"
            v-else-if="isCustomUploader"
            @click="onButtonClick"
            class="h-auto w-full justify-between border-dashed bg-gray-50 p-2"
        >
            <div
                class="flex h-8 items-center justify-center rounded-md border border-gray-300 bg-white px-2"
            >
                <icon-attach class="h-5 w-5 text-gray-400" />
                {{ buttonLabel }}
            </div>
            <p class="text-xs text-gray-700">Drop files here to upload</p>
        </tw-button>
        <tw-button
            variant="secondary"
            className="h-8 leading-[30px] min-w-[130px]"
            v-else-if="variant === 'button'"
            @click="onButtonClick"
        >
            <icon
                :name="iconName"
                v-if="(isAgencyUploadForm && isReuploaded) || isPersonalDetailForm"
            />
            <icon-attach class="h-5 w-5 text-gray-400" v-else />
            {{ isAgencyUploadForm && isReuploaded ? 'Reupload' : buttonLabel }}
        </tw-button>
        <k-upload
            v-model="vModel"
            ref="upload"
            :accept="accept"
            :action-layout="actionsLayout"
            :auto-upload="autoUpload"
            :batch="batch"
            :class-name="className"
            :default-files="defaultFiles"
            :disabled="disabled"
            :files="files"
            :id="id"
            :multiple="multiple"
            :restrictions="restrictions"
            @add="onAdd"
            @remove="onRemove"
            @progress="onProgress"
            @statuschange="onStatusChange"
            @beforeremove="onBeforeRemove"
            @beforeupload="onBeforeUpload"
            @cancel="onCancel"
            :with-credentials="false"
            :list="customList ? customListItemUI : ''"
            :save-url="saveUrl"
            :remove-url="removeUrl"
        >
            <template v-slot:myTemplate="{ props }">
                <div class="flex w-full flex-col">
                    <div class="flex w-full flex-wrap items-center gap-4" v-if="!showNotList">
                        <UploadListItem
                            :files="props.files"
                            :async="props.async"
                            @cancel="props.onCancel"
                            @remove="props.onRemove"
                            @progress="props.onProgress"
                            @removeItem="onRemoveItem"
                            :pt="{
                                chip: 'rounded-lg h-[31.35px] border-gray-300 w-fit overflow-hidden',
                            }"
                            :view="customListUi"
                            :progress="progress"
                            :error="error"
                            :isCustomUploader="isCustomUploader"
                        />
                    </div>
                    <div class="p-4" v-if="error">
                        <div class="w-full rounded-md border border-red-500 bg-red-50 p-4">
                            <p class="text-center text-sm text-red-500">
                                {{ error }}
                            </p>
                        </div>
                    </div>
                </div>
            </template>
        </k-upload>
    </div>
</template>
<script>
import { Upload, ExternalDropZone, UploadListSingleItem } from '@progress/kendo-vue-upload';
import { twMerge } from 'tailwind-merge';
import Button from '@spa/components/Buttons/Button.vue';
import { IconAttach20Regular } from '@iconify-prerendered/vue-fluent';
import UploadListItem from '@spa/components/Uploader/UploadListItem.vue';
import { useDocumentStore } from '@spa/stores/modules/document.store';
import { ProgressBar } from '@progress/kendo-vue-progressbars';

export default {
    setup() {
        const store = useDocumentStore();
        return { store };
    },
    props: {
        modelValue: {
            type: Array,
            default: [],
        },
        showNotList: {
            type: Boolean,
            default: false,
        },
        accept: {
            type: String,
            // default:
            //     "file_extension | audio/* | video/* | image/* | media_types",
            default: 'image/jpg',
        },
        actionsLayout: {
            type: String,
            default: 'end',
        },
        autoUpload: {
            type: Boolean,
            default: true,
        },
        batch: {
            type: Boolean,
            default: false,
        },
        className: {
            type: String,
            default: '',
        },
        defaultFiles: {
            type: Array,
            default: [],
        },
        disabled: {
            type: Boolean,
            default: false,
        },
        id: {
            type: String,
            default: '',
        },
        multiple: {
            type: Boolean,
            default: true,
        },
        restrictions: {
            type: Object,
            default: {},
        },
        linkedMsg: {
            type: String,
            default: '',
        },
        hintMsg: {
            type: String,
            default: 'Click or drag file to this area to upload.',
        },
        noteMsg: {
            type: String,
            default:
                'Support for a single or bulk upload. Strictly prohibited from uploading company data or other banned files.',
        },
        pt: {
            type: Object,
            default: {},
        },
        variant: {
            // "button" | "dropzone"
            type: String,
            default: 'button',
        },
        buttonLabel: {
            type: String,
            default: 'Attach Files',
        },
        customList: {
            type: [Boolean, String],
            default: true,
        },
        isCustomUploader: {
            type: [Boolean, String],
            default: false,
        },
        isPadding: {
            type: [Boolean, String],
            default: false,
        },
        isDocumentUploaderClass: {
            type: [Boolean, String],
            default: false,
        },
        isAgencyUploadForm: {
            type: [Boolean, String],
            default: false,
        },
        isPersonalDetailForm: {
            type: [Boolean, String],
            default: true,
        },
        progress: {
            type: Object,
            default: () => ({}),
        },
        saveUrl: String,
        removeUrl: String,
        customListUi: {
            type: String,
            default: 'chip',
        },
        error: {
            type: String,
            default: null,
        },
        iconName: {
            type: String,
            default: null,
        },
    },
    components: {
        'k-upload': Upload,
        'k-external': ExternalDropZone,
        'tw-button': Button,
        'icon-attach': IconAttach20Regular,
        UploadListItem,
        'k-single': UploadListSingleItem,
        ProgressBar,
    },
    watch: {
        progress: {
            handler(newValue, oldValue) {
                // Update progress in this.files based on newValue
                Object.keys(newValue).forEach((key) => {
                    const fileIndex = parseInt(key, 10); // Convert key to an integer
                    if (this.files[fileIndex]) {
                        // Update the progress of the corresponding file
                        this.files[fileIndex].progress = newValue[key];
                    }
                });

                console.log('Files after update:', this.files);
            },
        },
    },
    data() {
        return {
            customListItemUI: 'myTemplate',
            hintUI: 'myHintTemplate',
            noteUI: 'myNoteTemplate',
            files: [],
            isReuploaded: false,
        };
    },
    computed: {
        vModel: {
            get() {
                return this.modelValue;
            },
            set(value) {
                this.$emit('update:modelValue', value);
            },
        },
        rootClass() {
            return twMerge(
                'tw-upload',
                this.customList
                    ? `tw-upload--custom-list tw-upload--custom-list-progress ${this.isPadding ? 'flex items-center gap-4' : ''} ${this.isDocumentUploaderClass ? 'media-file-uploader' : ''} `
                    : 'tw-upload--default',
                this.pt.root
            );
        },
        dropzoneClass() {
            return twMerge('tw-upload__dropzone', this.pt.root);
        },
    },
    methods: {
        onAdd(e) {
            console.log('On Add', e);
            this.files = e.newState;
            this.$emit('add', e, this.files);
            this.isReuploaded = e.newState.length > 0;
        },
        onRemove(e) {
            console.log('On Remove', e);
            this.files = e.affectedFiles;
            this.$emit('remove', e, this.files);
            this.isReuploaded = false;
        },
        onStatusChange(e) {
            this.$emit('statuschange', e);
        },
        onProgress(e) {
            console.log('On onProgress', e);
            this.$emit('progress', e);
        },
        onBeforeRemove(e) {
            this.$emit('beforeremove', e);
        },
        onBeforeUpload(e) {
            this.files = e.files;
            this.$emit('beforeupload', e, this.files);
        },
        onCancel(e) {
            this.$emit('cancel', e);
        },
        onButtonClick(e) {
            const uploadElement = this.$refs.upload.$el;

            // Assuming the div is the first div inside uploadElement
            const targetDiv = uploadElement.querySelector('.k-upload-button');
            targetDiv.click();
        },
        onRemoveItem(e) {
            console.log('On Remove Item', e);
            this.files = this.files.filter((file) => file.uid !== e);
        },
    },
};
</script>
<style lang=""></style>
