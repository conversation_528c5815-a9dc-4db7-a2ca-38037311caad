<template>
    <k-single
        :files="files"
        :async="async"
        @cancel="onCancel"
        @remove="onRemove"
        @retry="onRetry"
        ref="singleUpload"
        v-if="view == 'default'"
        :style="{ style }"
    />
    <template v-else-if="view == 'chip'">
        <div :class="chipClass" v-for="file in files">
            <file-icon :name="getIconName(file.extension)" width="28" height="28" class="min-w-7" />
            <div class="overflow-hidden">
                <span class="min-w-fit">{{ file.name }}</span>
            </div>
            <icon-dismiss
                class="h-4 w-4 min-w-7 cursor-pointer stroke-2 text-gray-400 hover:text-red-600"
                v-if="removable"
                @click="onClick(file.uid)"
            />
        </div>
    </template>

    <template v-else-if="view == 'clientProgress'">
        <div
            class="relative w-full border-y px-2 py-4"
            v-for="file in files"
            :key="file.name"
            :class="[(isCustomUploader = 'rounded border border-gray-300 py-2 focus:shadow-none')]"
        >
            <div class="flex w-full items-center justify-between">
                <div class="flex items-center gap-2">
                    <file-icon :name="getIconName(file.extension)" width="28" height="28" />
                    <div>
                        <p class="max-w-80 truncate">
                            {{ file.name }}
                        </p>
                        <p class="text-[10px] text-green-500" v-if="file.progress === 100">
                            File Uploaded Successfully.
                        </p>
                    </div>
                </div>
                <div class="flex items-center justify-end gap-2">
                    <span>{{ file.progress.toFixed(2) }}%</span>
                    <icon-dismiss-circle
                        class="h-5 w-5 cursor-pointer stroke-2 hover:text-red-600"
                        v-if="removable && file.progress !== 100"
                        :class="error ? 'text-red-500' : 'text-gray-400'"
                        @click="onClick(file.uid)"
                    />
                    <icon-checkmark-circle class="h-5 w-5 text-green-500" v-else />
                </div>
            </div>

            <ProgressBar :value="file.progress" v-if="file.progress !== 100" />
        </div>
    </template>
</template>
<script>
import {
    IconDismiss20Regular,
    IconDismissCircle20Regular,
    IconCheckmarkCircle20Filled,
} from '@iconify-prerendered/vue-fluent';
import { twMerge } from 'tailwind-merge';
import { UploadListSingleItem } from '@progress/kendo-vue-upload';
import { ProgressBar } from '@progress/kendo-vue-progressbars';

export default {
    props: {
        files: Array,
        disabled: Boolean,
        async: Object,
        removable: {
            type: Boolean,
            default: true,
        },
        isCustomUploader: {
            type: Boolean,
            default: false,
        },
        view: {
            // 'chip' | 'default'
            type: String,
            default: 'chip',
        },
        progress: {
            type: Object,
            default: {},
        },
        error: {
            type: String,
            default: null,
        },
        pt: {
            type: Object,
            default: () => ({}),
        },
    },
    computed: {
        rootClass() {
            return twMerge('', this.pt.root);
        },
        chipClass() {
            return twMerge(
                'inline-flex items-center gap-2 rounded border border-gray-200 bg-white p-0.5 ps-2 pe-1 text-sm leading-5 text-gray-700 shrink-0 chip-custom-style',
                this.pt.chip
            );
        },
    },
    components: {
        'icon-dismiss': IconDismiss20Regular,
        'k-single': UploadListSingleItem,
        ProgressBar,
        'icon-dismiss-circle': IconDismissCircle20Regular,
        'icon-checkmark-circle': IconCheckmarkCircle20Filled,
    },
    methods: {
        onClick(e) {
            console.log('removing');
            this.onRemove(e);
        },
        onRetry(uid) {
            this.$emit('retry', uid);
        },
        onRemove(uid) {
            this.$emit('remove', uid);
        },
        onCancel(uid) {
            this.$emit('cancel', uid);
        },
        getIconName(extension) {
            console.log('Files');
            let extensionMapping = {
                '.pdf': 'pdf',
                '.xlsx': 'xlsx',
                '.png': 'image',
                '.jpg': 'image',
                '.jpeg': 'image',
                '.docx': 'docx',
            };
            return extensionMapping[extension] || 'pdf';
        },
    },
};
</script>
<style lang=""></style>
