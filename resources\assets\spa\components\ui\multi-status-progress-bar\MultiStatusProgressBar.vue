<template>
    <div :class="rootClasses">
        <div :class="barClasses" :style="{ height: height, lineHeight: height }">
            <div
                v-for="(item, index) in items"
                :key="index"
                class="h-inherit inline-block"
                :class="[item.colorClass]"
                :style="{ width: calculatePercentage(item.value) + '%', height: height }"
            ></div>
        </div>
    </div>
</template>

<script setup>
import { computed } from 'vue';
import { twMerge } from 'tailwind-merge';

const props = defineProps({
    items: {
        type: Array,
        required: true,
        validator: (value) => {
            return value.every(
                (item) => item.label && typeof item.value === 'number' && item.colorClass
            );
        },
    },
    total: {
        type: Number,
        default: null,
    },
    pt: {
        type: Object,
        default: () => ({}),
    },
    height: {
        type: String,
        default: '6px',
    },
});

const total = computed(() => {
    return props.total || props.items.reduce((sum, item) => sum + item.value, 0);
});

const calculatePercentage = (value) => {
    if (total.value === 0) return 0;
    return Math.round((value / total.value) * 100);
};

const rootClasses = computed(() => {
    return twMerge('progress-container', props.pt.root);
});

const barClasses = computed(() => {
    return twMerge('progress-bar w-full overflow-hidden rounded-full bg-gray-200', props.pt.bar);
});
</script>

<style scoped>
/* Default color classes - can be overridden by props */
.bg-present {
    @apply bg-green-500;
}

.bg-absent {
    @apply bg-yellow-500;
}

.bg-leave {
    @apply bg-red-500;
}

.bg-holiday {
    @apply bg-blue-500;
}
</style>
