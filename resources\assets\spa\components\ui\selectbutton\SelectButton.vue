<template>
    <div :class="rootClasses" role="group" :aria-labelledby="ariaLabelledby">
        <template v-for="(option, index) of options" :key="getOptionRenderKey(option)">
            <ToggleButton
                :modelValue="isSelected(option)"
                :onLabel="getOptionLabel(option)"
                :offLabel="getOptionLabel(option)"
                :disabled="disabled || isOptionDisabled(option)"
                :size="size"
                :readonly="isOptionReadonly(option)"
                @change="onOptionSelect($event, option, index)"
            >
                <template v-if="$slots.option" #default>
                    <slot name="option" :option="option" :index="index">
                        <span>{{ getOptionLabel(option) }}</span>
                    </slot>
                </template>
            </ToggleButton>
        </template>
    </div>
</template>

<script setup>
import { computed } from 'vue';
import ToggleButton from '@spa/components/ui/togglebutton/ToggleButton.vue';
import { Button as KButton, ButtonGroup } from '@progress/kendo-vue-buttons';

const props = defineProps({
    options: Array,
    optionLabel: String,
    optionValue: String,
    dataKey: String,
    optionDisabled: String,
    allowEmpty: {
        type: Boolean,
        default: true,
    },
    multiple: {
        type: Boolean,
        default: false,
    },
    disabled: Boolean,
    modelValue: null,
    $invalid: Boolean,
    ariaLabelledby: String,
    size: String,
});

// Define emits - Added update:modelValue for proper v-model support
const emit = defineEmits(['change', 'update:modelValue']);

// Methods converted to functions
const getOptionLabel = (option) => {
    return props.optionLabel ? option[props.optionLabel] : option;
};

const getOptionValue = (option) => {
    return props.optionValue ? option[props.optionValue] : option;
};

const getOptionRenderKey = (option) => {
    return props.dataKey ? option[props.dataKey] : getOptionLabel(option);
};

const isOptionDisabled = (option) => {
    return props.optionDisabled ? option[props.optionDisabled] : false;
};

const isOptionReadonly = (option) => {
    if (props.allowEmpty) return false;

    let selected = isSelected(option);

    if (props.multiple) {
        return selected && props.modelValue && props.modelValue.length === 1;
    } else {
        return selected;
    }
};

const isSelected = (option) => {
    let selected = false;
    let optionValue = getOptionValue(option);

    if (props.multiple) {
        if (props.modelValue && Array.isArray(props.modelValue)) {
            selected = props.modelValue.some((val) => val === optionValue);
        }
    } else {
        selected = props.modelValue === optionValue;
    }

    return selected;
};

const onOptionSelect = (event, option, index) => {
    if (props.disabled || isOptionDisabled(option) || isOptionReadonly(option)) {
        return;
    }

    let selected = isSelected(option);
    let optionValue = getOptionValue(option);
    let newValue;

    if (props.multiple) {
        if (selected) {
            // Remove the option from selection
            newValue = props.modelValue
                ? props.modelValue.filter((val) => val !== optionValue)
                : [];
            if (!props.allowEmpty && newValue.length === 0) return;
        } else {
            // Add the option to selection
            newValue = props.modelValue ? [...props.modelValue, optionValue] : [optionValue];
        }
    } else {
        if (selected && !props.allowEmpty) return;
        newValue = selected ? null : optionValue;
    }

    // Emit both events for maximum compatibility
    emit('update:modelValue', newValue);
    emit('change', { event: event, value: newValue });
};

// Computed properties
const equalityKey = computed(() => {
    return props.optionValue ? null : props.dataKey;
});

const rootClasses = computed(() => {
    // Add your root classes logic here
    return {
        'toggle-group bg-gray-200 rounded-md p-1': true,
        'toggle-group--disabled': props.disabled,
        'toggle-group--invalid': props.$invalid,
    };
});

// Expose methods and computed properties for template usage
defineExpose({
    getOptionLabel,
    getOptionValue,
    getOptionRenderKey,
    isOptionDisabled,
    isOptionReadonly,
    onOptionSelect,
    isSelected,
    equalityKey,
});
</script>

<style scoped>
.toggle-group {
    display: flex;
    gap: 0.25rem;
}

.toggle-group--disabled {
    opacity: 0.6;
    pointer-events: none;
}
</style>
