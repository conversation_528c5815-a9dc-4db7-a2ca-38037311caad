<template>
    <div class="tab-wrapper">
        <!-- Tab Headers -->
        <div class="flex gap-8 border-b border-gray-200">
            <button
                v-for="(tab, index) in tabs"
                :key="tab.name"
                @click="selectTab(index)"
                :class="[
                    'px-16 py-2 text-base font-medium transition-colors focus:outline-none max-md:px-4',
                    selected === index
                        ? 'border-b-2 border-primary-blue-500 text-primary-blue-500'
                        : 'text-gray-500 hover:text-primary-blue-500',
                ]"
            >
                <slot name="titleRender" :props="{ title: tab.title, index: index, key: tab.name }">
                    <!-- Default title rendering -->
                    <div
                        class="group flex items-center justify-center gap-1 hover:text-primary-blue-500"
                    >
                        <span v-if="showIcon">
                            <icon
                                :name="getIconName(index)"
                                fill="currentColor"
                                width="24"
                                height="24"
                                viewBox="0 0 24 24"
                            />
                        </span>

                        {{ tab.title }}
                    </div>
                </slot>
            </button>
        </div>

        <!-- Tab Content -->
        <div class="tab-content mt-8">
            <div
                v-for="(tab, index) in tabs"
                :key="index"
                v-show="selected === index"
                class="tab-panel"
            >
                <slot
                    :name="`tab-panel-${tab.name}`"
                    :props="{ tab: tab, index: index, isActive: selected === index }"
                >
                    <!-- Default content if no slot provided -->
                    <div class="p-4 text-gray-600">No content provided for {{ tab.title }}</div>
                </slot>
            </div>
        </div>
    </div>
</template>
<script setup>
import { ref, watch } from 'vue';
const props = defineProps({
    tabs: {
        type: Array,
        required: true,
        validator: (tabs) => {
            return tabs.every(
                (tab) =>
                    typeof tab === 'object' &&
                    tab.hasOwnProperty('name') &&
                    tab.hasOwnProperty('title')
            );
        },
    },
    defaultSelected: {
        type: Number,
        default: 0,
    },
    showIcon: {
        type: Boolean,
        default: false,
    },
});

const emit = defineEmits(['select']);

const selected = ref(props.defaultSelected);

const selectTab = (index) => {
    selected.value = index;
    emit('select', index, props.tabs[index]);
};

const getIconName = (index) => {
    // You can customize this method based on your icon naming convention
    // or pass icon names in the tabs array
    const iconNames = ['home', 'user', 'settings', 'dashboard', 'chart'];
    return props.tabs[index].icon || iconNames[index] || 'default';
};

watch(
    () => props.defaultSelected,
    (newVal) => {
        selected.value = newVal;
    }
);
</script>

<style scoped></style>
