<template>
    <button
        type="button"
        @click="onChange"
        @blur="onBlur"
        :tabindex="tabindex"
        :disabled="disabled"
        :class="rootClasses"
        :aria-label="ariaLabel"
        :aria-labelledby="ariaLabelledby"
        :aria-pressed="modelValue"
    >
        <span :class="contentClasses">
            <slot>
                <slot name="icon" :class="iconClasses">
                    <i v-if="currentIcon" :class="currentIcon"></i>
                </slot>
                <span :class="labelClasses">{{ label }}</span>
            </slot>
        </span>
    </button>
</template>

<script>
import { twMerge } from 'tailwind-merge';

export default {
    props: {
        onIcon: String,
        offIcon: String,
        onLabel: {
            type: String,
            default: 'Yes',
        },
        offLabel: {
            type: String,
            default: 'No',
        },
        iconPos: {
            type: String,
            default: 'left',
        },
        readonly: {
            type: Boolean,
            default: false,
        },
        modelValue: {
            type: Boolean,
            default: false,
        },
        disabled: {
            type: <PERSON>olean,
            default: false,
        },
        tabindex: {
            type: Number,
            default: null,
        },
        ariaLabelledby: {
            type: String,
            default: null,
        },
        ariaLabel: {
            type: String,
            default: null,
        },
        size: {
            type: String,
            default: null,
        },
        fluid: {
            type: Boolean,
            default: null,
        },
        pt: {
            type: Object,
            default: () => ({}),
        },
    },
    emits: ['change', 'update:modelValue'],
    computed: {
        active() {
            return this.modelValue === true;
        },
        hasLabel() {
            return this.isNotEmpty(this.onLabel) && this.isNotEmpty(this.offLabel);
        },
        label() {
            return this.hasLabel ? (this.modelValue ? this.onLabel : this.offLabel) : '\u00A0';
        },
        currentIcon() {
            if (this.modelValue && this.onIcon) {
                return this.onIcon;
            } else if (!this.modelValue && this.offIcon) {
                return this.offIcon;
            }
            return null;
        },
        rootClasses() {
            return twMerge(
                'tw-btn-icon',
                this.modelValue ? 'active pressed' : '',
                this.disabled ? 'disabled opacity-50 cursor-not-allowed' : 'cursor-pointer',
                this.readonly ? 'readonly cursor-default' : '',
                this.size ? `size-${this.size}` : '',
                this.fluid ? 'w-full' : '',
                this.pt.root
            );
        },
        contentClasses() {
            return twMerge(
                'inline-flex items-center justify-center gap-2',
                // Icon position classes
                this.iconPos === 'right' ? 'flex-row-reverse' : 'flex-row',
                this.pt.content
            );
        },
        iconClasses() {
            return twMerge('w-4 h-4', this.pt.icon);
        },
        labelClasses() {
            return twMerge('text-sm font-medium', this.pt.label);
        },
    },
    methods: {
        onChange(event) {
            if (!this.disabled && !this.readonly) {
                const newValue = !this.modelValue;

                // Emit update:modelValue for v-model support
                this.$emit('update:modelValue', newValue);

                // Emit change event with additional context
                this.$emit('change', {
                    originalEvent: event,
                    value: newValue,
                    checked: newValue,
                });
            }
        },
        onBlur(event) {
            // Handle blur event if needed
            // this.formField.onBlur?.(event);
        },
        isNotEmpty(value) {
            return value !== null && value !== undefined && value !== '';
        },
    },
};
</script>

<style scoped>
.tw-btn-icon:not(.disabled):hover {
    @apply bg-gray-50;
}

.tw-btn-icon.active {
    @apply border-gray-300 bg-white;
}

.tw-btn-icon:focus {
    @apply outline-none ring-1 ring-blue-500;
}

.tw-btn-icon.disabled {
    @apply cursor-not-allowed opacity-50;
}

.tw-btn-icon.readonly {
    @apply cursor-default;
}

/* Size variants */
.tw-btn-icon.size-small {
    @apply px-2 py-1 text-xs;
}

.tw-btn-icon.size-large {
    @apply px-6 py-3 text-base;
}
</style>
