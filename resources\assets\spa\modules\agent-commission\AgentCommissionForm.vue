<template>
    <AsyncForm
        :type="'kendo'"
        :layout="{ cols: 2, gap: 16 }"
        :orientation="'vertical'"
        position="right"
        :dialogTitle="dialogTitle"
        :store="store"
        :has-custom-submit="true"
        @submit="onSubmit"
    >
        <div class="grid grid-cols-1 gap-4 p-6">
            <FormValidationWrapper
                :validation-message="store.errors?.agent_status"
                :valid="!store.errors?.agent_status"
                :indicate-required="true"
                label="Agent Status:"
            >
                <template #field>
                    <EnumSelect
                        className="tw-w-full"
                        enum-class="GalaxyAPI\Enums\AgentStatusEnum"
                        v-model="formData.agent_status"
                        :default-value="1"
                        :placeholder="'Select Status'"
                    />
                </template>
            </FormValidationWrapper>
            <AgentSelect
                name="agent_id"
                label="Agent Name:"
                v-model="formData.agent_id"
                :validation-message="store.errors?.agent_id"
                :valid="!store.errors?.agent_id"
                :touched="true"
                :indicaterequired="true"
            />
            <CourseTypeSelect
                name="course_type"
                label="Course Type:"
                v-model="formData.course_type"
                :validation-message="store.errors?.course_type"
                :valid="!store.errors?.course_type"
                :touched="true"
                :indicaterequired="true"
                :multiple="false"
                :placeholder="'Select Course Type'"
            />
            <field
                :id="'course_status'"
                :name="'course_status'"
                :label="'Course Status'"
                :data-items="booleanOptions"
                :component="FormRadioGroup"
                v-model="store.formData.course_status"
                :required="true"
                :layout="'horizontal'"
                :indicaterequired="true"
                :validation-message="store.errors?.course_status"
                :valid="!store.errors?.course_status"
            />
            <CoursesCheckbox
                name="courses"
                label="Courses:"
                v-model="formData.courses"
                :validation-message="store.errors?.courses"
                :valid="!store.errors?.courses"
                :touched="true"
                :indicaterequired="true"
                :multiple="false"
                :placeholder="'Select Courses'"
                v-if="!formData.id"
            />

            <field
                :id="'course'"
                :name="'course'"
                :label="'Course'"
                :data-items="booleanOptions"
                :component="FormInput"
                v-model="store.formData.course.name"
                :required="true"
                :layout="'horizontal'"
                :indicaterequired="true"
                :validation-message="store.errors?.course"
                :valid="!store.errors?.course"
                :disabled="true"
                v-else
            />

            <FormValidationWrapper
                :validation-message="store.errors?.commission_period"
                :valid="!store.errors?.commission_period"
                :indicate-required="true"
                label="Commission Period:"
            >
                <template #field>
                    <EnumSelect
                        className="tw-w-full"
                        enum-class="GalaxyAPI\Enums\CommissionPeriodEnum"
                        v-model="formData.commission_period"
                        :default-value="1"
                        :placeholder="'Select Commission Period'"
                    />
                </template>
            </FormValidationWrapper>
            <field
                :id="'commission'"
                :name="'commission'"
                v-model="formData.commission"
                :label="'Commission'"
                :component="FormNumericInput"
                :validation-message="store.errors?.commission"
                :valid="!store.errors?.commission"
            />
            <field
                :id="'gst'"
                :name="'gst'"
                :label="'GST'"
                :data-items="booleanOptions"
                :component="FormRadioGroup"
                v-model="store.formData.gst"
                :required="true"
                :layout="'horizontal'"
                :indicaterequired="true"
                :validation-message="store.errors?.gst"
                :valid="!store.errors?.gst"
                :format="'dd-MM-yyyy'"
                :emit-format="'yyyy-MM-dd'"
            />
            <field
                :id="'rate_valid_from'"
                :name="'rate_valid_from'"
                v-model="formData.rate_valid_from"
                :label="'Rate Valid From'"
                :component="FormDatePicker"
                :validation-message="store.errors?.recorded_date"
                :valid="!store.errors?.recorded_date"
                :format="'dd-MM-yyyy'"
                :emit-format="'yyyy-MM-dd'"
            />
            <field
                :id="'rate_valid_to'"
                :name="'rate_valid_to'"
                v-model="formData.rate_valid_to"
                :label="'Rate Valid To'"
                :component="FormDatePicker"
                :validation-message="store.errors?.recorded_date"
                :valid="!store.errors?.recorded_date"
            />
        </div>
    </AsyncForm>
</template>
<script setup>
import AsyncForm from '@spa/components/AsyncComponents/Form/AsyncFormPopup.vue';
import FormInput from '@spa/components/KendoInputs/FormInput.vue';
import { useAgentCommissionStore } from '@spa/stores/modules/agent-commission/useAgentCommissionStore.js';
import { storeToRefs } from 'pinia';
import { Field as field } from '@progress/kendo-vue-form';
import {
    requiredtrue,
    requiredDate,
    requiredpositivenumber,
} from '@spa/services/validators/kendoCommonValidator.js';
import FormDropDown from '@spa/components/KendoInputs/FormDropDown.vue';
import FormRadioGroup from '@spa/components/KendoInputs/FormRadioGroup.vue';
import FormDatePicker from '@spa/components/KendoInputs/FormDatePicker.vue';
import EnumSelect from '@spa/components/AsyncComponents/Select/EnumSelect.vue';
import FormValidationWrapper from '@spa/components/KendoInputs/FormValidationWrapper.vue';
import AgentSelect from '@spa/modules/agent/AgentSelect.vue';
import CourseTypeSelect from '@spa/modules/courses/CourseTypeSelect.vue';
import CoursesCheckbox from '@spa/modules/courses/CoursesCheckbox.vue';
import FormNumericInput from '@spa/components/KendoInputs/FormNumericInput.vue';
import { computed, watch } from 'vue';

const store = useAgentCommissionStore();
const { formData } = storeToRefs(store);

const dialogTitle = computed(() => {
    return formData.value?.id ? 'Edit Agent Commission' : '';
});

const booleanOptions = [
    {
        label: 'Yes',
        value: true,
    },
    {
        label: 'No',
        value: false,
    },
];

const onSubmit = async (e) => {
    console.log('submit', e);
    if (formData.value.id) {
        formData.value = {
            ...formData.value,
            course_id: formData.value.course.id,
        };
        store.submitFormData();
        return;
    }
    try {
        await store.addAgentCommissionBulk();
        store.fetchPaged();
        store.closeFunction();
    } catch (e) {
        console.error('Error adding agent commission:', e);
    }
};

watch(
    () => store.formData,
    (val) => {
        if (formData.id) {
            formData.value = {
                ...formData.value,
                course_id: formData.value.course.id,
                commission: parseInt(formData.value.commission),
            };
        }
    }
);
</script>
