<template>
    <AsyncGrid
        :columns="columns"
        :store="store"
        :has-create-action="true"
        :has-export="false"
        :has-filters="true"
        :filter-columns="2"
        :gridStyle="{
            height: '100%',
        }"
        :create-btn-label="'Add Agent Commission'"
        @handel-reset="
            () => {
                initFilters();
            }
        "
        :actions="['delete', 'edit']"
    >
        <template #filters="{ props }">
            <FilterBlockWrapper label="Agent">
                <AgentSelect v-model="store.filters.agentId" />
            </FilterBlockWrapper>
            <FilterBlockWrapper label="Period">
                <EnumSelect
                    className="tw-w-full"
                    enum-class="GalaxyAPI\Enums\CommissionPeriodEnum"
                    v-model="store.filters.commissionPeriod"
                    :placeholder="'Select Commission Period'"
                    :hasSelectAll="true"
                />
            </FilterBlockWrapper>
            <!--            <FilterBlockWrapper label="Course">-->
            <!--                <CoursesSelect v-model="store.filters.course" />-->
            <!--            </FilterBlockWrapper>-->
        </template>
        <template #body-cell-origin="{ props }">
            <span class="capitalize">{{ props.dataItem?.origin }}</span>
        </template>
        <template #body-cell-course_name="{ props }">
            {{ props.dataItem?.course?.code }} - {{ props.dataItem?.course?.name }}
        </template>
        <template #body-cell-agent="{ props }">
            {{ props.dataItem?.agent?.name }}
        </template>
        <template #body-cell-commission_period="{ props }">
            <Badge :variant="'outline'" :dark="false">
                <span class="capitalize">{{ props.dataItem?.commission_period_name }}</span>
            </Badge>
        </template>
        <template #body-cell-commission="{ props }"> {{ props.dataItem?.commission }}% </template>
        <template #body-cell-gst="{ props }">
            <span v-if="props.dataItem?.gst === 'GST'">Yes</span>
            <span v-else>No</span>
        </template>
        <template #body-cell-rate_valid_from="{ props }">
            <FormatDate :date="props.dataItem?.rate_valid_from" />
        </template>
        <template #body-cell-rate_valid_to="{ props }">
            <FormatDate :date="props.dataItem?.rate_valid_to" />
        </template>
    </AsyncGrid>
    <AgentCommissionForm />
</template>

<script setup>
import AsyncGrid from '@spa/components/AsyncComponents/Grid/AsyncGrid.vue';
import { ref, onMounted } from 'vue';
import { useAgentCommissionStore } from '@spa/stores/modules/agent-commission/useAgentCommissionStore.js';
import AgentCommissionForm from '@spa/modules/agent-commission/AgentCommissionForm.vue';
import FormatDate from '@spa/components/FormatDate.vue';
import Badge from '@spa/components/badges/Badge.vue';
import FilterBlockWrapper from '@spa/components/AsyncComponents/Grid/Partials/FilterBlockWrapper.vue';
import AgentSelect from '@spa/modules/agent/AgentSelect.vue';
import EnumSelect from '@spa/components/AsyncComponents/Select/EnumSelect.vue';
import CoursesSelect from '@spa/modules/courses/CoursesSelect.vue';

const store = useAgentCommissionStore();

const columns = [
    {
        field: 'origin',
        name: 'origin',
        title: 'Origin',
        replace: true,
        width: '100px',
    },
    {
        field: 'agent',
        name: 'agent',
        title: 'Agent',
        replace: true,
        width: '150px',
    },
    {
        field: 'commission_period',
        name: 'commission_period',
        title: 'Period',
        replace: true,
        width: '150px',
    },
    {
        field: 'course',
        name: 'course_name',
        title: 'Course Name',
        replace: true,
        sortable: true,
        width: '300px',
    },
    {
        field: 'rate_valid_from',
        name: 'rate_valid_from',
        title: 'Valid From',
        replace: true,
        width: '130px',
    },
    {
        field: 'rate_valid_to',
        name: 'rate_valid_to',
        title: 'Valid To',
        replace: true,
        width: '130px',
    },
    {
        field: 'commission',
        name: 'commission',
        title: 'Commission',
        replace: true,
        width: '110px',
        sortable: true,
    },
    {
        field: 'gst',
        name: 'gst',
        title: 'GST',
        replace: true,
        width: '100px',
    },
    // Add more columns as needed
];

const initFilters = () => {
    store.filters = {};
};

onMounted(() => {
    initFilters();
});
</script>
