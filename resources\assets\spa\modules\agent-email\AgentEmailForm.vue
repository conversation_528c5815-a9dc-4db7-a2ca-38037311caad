<template>
    <AsyncForm
        :type="'kendo'"
        :layout="{ cols: 2, gap: 16 }"
        :orientation="'vertical'"
        position="right"
        :dialogTitle="'Add AgentEmail'"
        :store="store"
    >
        <div class="flex flex-col">
            <div class="space-y-4 px-6 py-4">
                <AgentSelect
                    :multiple="true"
                    name="mail_to"
                    label="To"
                    :orientation="'horizontal'"
                    v-model="formData.mail_to"
                />
                <field
                    :id="'from_mail'"
                    :name="'from_mail'"
                    :label="'From'"
                    :component="FormInput"
                    v-model="formData.from_mail"
                    :orientation="'horizontal'"
                    :default-item="{
                        label: 'Select From Email',
                        value: null,
                    }"
                />
                <field
                    :id="'subject'"
                    :name="'subject'"
                    :label="'Subject'"
                    :component="FormInput"
                    v-model="formData.subject"
                    :orientation="'horizontal'"
                />
                <InsertTemplateDialog @select="onSelect" :recipient="RECIPIENT_NO" />
            </div>
            <div class="flex-1 overflow-y-auto">
                <field
                    :id="'message'"
                    :name="'message'"
                    :component="'editor-content'"
                    :orientation="'horizontal'"
                    v-model="formData.message"
                    :default-content="content"
                    :default-edit-mode="'div'"
                >
                    <template #editor-content="{ props }">
                        <FormEditor v-bind="props" ref="editorRef" />
                    </template>
                </field>
                <!--                <div class="px-6 py-3">-->
                <!--                    <field-->
                <!--                        :id="'photos'"-->
                <!--                        :name="'photos'"-->
                <!--                        v-model="formData.photos"-->
                <!--                        :label="'Upload Photos'"-->
                <!--                        :hintMessage="'Hint: Select your additional photos'"-->
                <!--                        :component="FileUploader"-->
                <!--                        :multiple="false"-->
                <!--                        :autoUpload="false"-->
                <!--                        :accept="'image/*'"-->
                <!--                        @add="handleOnAdd"-->
                <!--                    />-->
                <!--                </div>-->
            </div>
        </div>
    </AsyncForm>
</template>
<script setup>
import { watch } from 'vue';
import AsyncForm from '@spa/components/AsyncComponents/Form/AsyncFormPopup.vue';
import FormInput from '@spa/components/KendoInputs/FormInput.vue';
import { useAgentEmailStore } from '@spa/stores/modules/agent-email/useAgentEmailStore.js';
import { storeToRefs } from 'pinia';
import { Field } from '@progress/kendo-vue-form';
import FormDropDown from '@spa/components/KendoInputs/FormDropDown.vue';
import { Editor } from '@progress/kendo-vue-editor';
import InsertTemplateDialog from '@spa/modules/staffemail/partials/InsertTemplateDialog.vue';
import Checkbox from '@spa/components/Checkbox.vue';
import FormEditor from '@spa/components/KendoInputs/FormEditor.vue';
import AgentSelect from '@spa/modules/agent/AgentSelect.vue';
import FormValidationWrapper from '@spa/components/KendoInputs/FormValidationWrapper.vue';
import { ref } from 'vue';

const RECIPIENT_NO = 3;
const store = useAgentEmailStore();
const { formData } = storeToRefs(store);
const content = ref('');
const editorRef = ref(null);
const onSelect = (template) => {
    content.value = template.content;
    editorRef.value?.setHTML(template.content);
    formData.value = {
        ...formData.value,
        subject: template.subject,
        message: template.content,
        template_id: template.id,
    };
};
const fetchAuthUser = async () => {
    try {
        const response = await $http.get('api/v2/tenant/users/get-auth-user');
        if (response) {
            formData.value.from_mail = response.data.email;
        }
    } catch (error) {
        console.error('Error fetching data:', error);
    }
};

watch(
    () => store.formDialog,
    async (val) => {
        if (val) {
            await fetchAuthUser();
        }
    }
);
</script>
