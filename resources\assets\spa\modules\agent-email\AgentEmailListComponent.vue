<template>
    <AsyncGrid
        :columns="columns"
        :store="store"
        :has-create-action="true"
        :has-export="false"
        :has-filters="false"
        :gridStyle="{
            height: '100%',
        }"
        @handel-reset="
            () => {
                initFilters();
            }
        "
        :actions="['delete']"
    >
    </AsyncGrid>
    <AgentEmailForm />
</template>

<script setup>
import AsyncGrid from '@spa/components/AsyncComponents/Grid/AsyncGrid.vue';
import { ref, onMounted } from 'vue';
import { useAgentEmailStore } from '@spa/stores/modules/agent-email/useAgentEmailStore.js';
import AgentEmailForm from '@spa/modules/agent-email/AgentEmailForm.vue';

const store = useAgentEmailStore();

const columns = [
    {
        field: 'name',
        title: 'Name',
        width: '200px',
    },
    // Add more columns as needed
];

const initFilters = () => {
    store.filters = {};
};

onMounted(() => {
    initFilters();
});
</script>
