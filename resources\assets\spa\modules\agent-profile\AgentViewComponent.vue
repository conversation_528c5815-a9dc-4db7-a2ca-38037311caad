<template>
    <ProfileHeaderWithTabs :tabs="tabs" :currentTab="activeTab">
        <template #titleSlot>
            <h2 class="max-w-xl truncate text-2xl font-medium">{{ store.formData?.name }}</h2>
        </template>
        <template #buttongroup>
            <Button :variant="'primary'" size="xs" @click="staffEmailStore.formDialog = true">
                <span :class="'text-white'">
                    <icon :name="'mail'" :fill="'#ffffff'" size="16" />
                </span>
                <span>Send Mail</span>
            </Button>
        </template>
    </ProfileHeaderWithTabs>
    <div class="flex-1 overflow-y-auto px-6 py-5">
        <AgentDetailCardComponent v-if="activeTab === 'profile'" />
        <AgentCommunicationListComponent
            v-if="activeTab === 'communication-logs'"
            :agentId="agentId"
        />
        <AgentStudentsListComponent v-if="activeTab === 'students-list'" :agentId="agentId" />
        <AgentPaymentHistoryListComponent v-if="activeTab === 'payments'" :agentId="agentId" />
        <AgentStudentCommissionListComponent
            v-if="activeTab === 'commissions'"
            :agentId="agentId"
        />
        <CreditBonusAllocationListComponent
            v-if="activeTab === 'credit-bonus-allocation'"
            :agentId="agentId"
        />
    </div>
    <StaffEmailForm :data="store.formData" />
</template>
<script setup>
import ProfileHeaderWithTabs from '@spa/modules/common/ProfileHeaderWithTabs.vue';
import Button from '@spa/components/Buttons/Button.vue';
import { ref, computed, onMounted } from 'vue';
import { usePage } from '@inertiajs/vue3';
import StaffEmailForm from '@spa/modules/staffemail/StaffEmailForm.vue';
import { useAgentStore } from '@spa/stores/modules/agent/useAgentStore.js';
import AgentDetailCardComponent from '@spa/modules/agent-profile/agent-dashboard/AgentDetailCardComponent.vue';
import AgentCommunicationListComponent from '@spa/modules/agent-profile/agent-communication/AgentCommunicationListComponent.vue';
import AgentStudentsListComponent from '@spa/modules/agent-profile/agent-students/AgentStudentsListComponent.vue';
import AgentPaymentHistoryListComponent from '@spa/modules/agent-profile/agent-payment-history/AgentPaymentHistoryListComponent.vue';
import AgentStudentCommissionListComponent from '@spa/modules/agent-profile/student-agent-commission/StudentAgentCommissionListComponent.vue';
import CreditBonusAllocationListComponent from '@spa/modules/credit-bonus-allocation/CreditBonusAllocationListComponent.vue';
let props = defineProps();

const store = useAgentStore();

const $page = usePage();

const activeTab = computed(() => {
    const path = $page.url;
    const slug = path.split('/').pop();
    if (!isNaN(Number(slug))) {
        return 'profile';
    }
    return slug || 'profile';
});

const agentId = computed(() => $page.props.params.id);

const tabs = [
    {
        name: 'Profile',
        slug: 'profile',
        route: route('spa.agent-profile', { id: agentId.value }),
    },
    {
        name: 'Communication Logs',
        slug: 'communication-logs',
        route: route('spa.agent-communication-logs', { id: agentId.value }),
    },
    {
        name: 'Students List',
        slug: 'students-list',
        route: route('spa.agent-students-list', { id: agentId.value }),
    },
    {
        name: 'Documents',
        slug: 'documents',
        route: route('spa.agent-documents', { id: agentId.value }),
    },
    {
        name: 'Payment History',
        slug: 'payments',
        route: route('spa.agent-payments', { id: agentId.value }),
    },
    {
        name: 'Process Commission',
        slug: 'commissions',
        route: route('spa.agent-commissions', { id: agentId.value }),
    },
    {
        name: 'Credit/Bonus Allocation',
        slug: 'credit-bonus-allocation',
        route: route('spa.agent-credit-bonus-allocation', { id: agentId.value }),
    },
];

onMounted(() => {
    store.fetchDataById(agentId.value);
});
</script>
<style lang=""></style>
