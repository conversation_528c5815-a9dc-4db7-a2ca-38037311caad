<template>
    <AsyncForm
        :type="'kendo'"
        :layout="{ cols: 2, gap: 16 }"
        :orientation="'vertical'"
        position="right"
        :dialogTitle="'Send Email'"
        :store="store"
        :initial-values="formData"
    >
        {{ formData }}
        <div class="flex flex-col">
            <div class="space-y-4 px-6 py-4">
                <field
                    :id="'comment_by_name'"
                    :name="'comment_by_name'"
                    :label="'Commented By'"
                    :component="FormInput"
                    :disabled="true"
                    v-model="store.formData.comment_by_name"
                    :orientation="'horizontal'"
                />
                <field
                    :id="'category'"
                    :name="'category'"
                    :label="'Category/Type'"
                    :component="FormDropDown"
                    :data-items="formOptions?.category"
                    :text-field="'label'"
                    :data-item-key="'value'"
                    :value-field="'value'"
                    :value-primitive="true"
                    v-model="store.formData.category"
                    :orientation="'horizontal'"
                    :default-item="{
                        label: 'Select Category/Type',
                        value: null,
                    }"
                />
                <field
                    :id="'status'"
                    :name="'status'"
                    :label="'Status'"
                    :component="FormDropDown"
                    :data-items="formOptions?.status"
                    :text-field="'label'"
                    :data-item-key="'value'"
                    :value-field="'value'"
                    :value-primitive="true"
                    v-model="store.formData.status"
                    :orientation="'horizontal'"
                    :default-item="{
                        label: 'Select Category/Type',
                        value: null,
                    }"
                />
            </div>
            <div class="flex-1 overflow-y-auto">
                <field
                    :id="'comment'"
                    :name="'comment'"
                    :component="'editor-content'"
                    :orientation="'horizontal'"
                    v-model="store.formData.comment"
                    :default-content="content"
                    :default-edit-mode="'div'"
                >
                    <template #editor-content="{ props }">
                        <FormEditor v-bind="props" :tools="tools" ref="editorRef" />
                    </template>
                </field>
                <!--                <div class="px-6 py-3">-->
                <!--                    <field-->
                <!--                        :id="'photos'"-->
                <!--                        :name="'photos'"-->
                <!--                        v-model="store.formData.photos"-->
                <!--                        :label="'Upload Photos'"-->
                <!--                        :hintMessage="'Hint: Select your additional photos'"-->
                <!--                        :component="FileUploader"-->
                <!--                        :multiple="false"-->
                <!--                        :autoUpload="false"-->
                <!--                        :accept="'image/*'"-->
                <!--                        @add="handleOnAdd"-->
                <!--                    />-->
                <!--                </div>-->
            </div>
        </div>
    </AsyncForm>
</template>
<script setup>
import { ref, onMounted, watch, computed, nextTick } from 'vue';
import AsyncForm from '@spa/components/AsyncComponents/Form/AsyncFormPopup.vue';
import FormInput from '@spa/components/KendoInputs/FormInput.vue';
import { storeToRefs } from 'pinia';
import { Field } from '@progress/kendo-vue-form';
import FormDropDown from '@spa/components/KendoInputs/FormDropDown.vue';
import Checkbox from '@spa/components/Checkbox.vue';
import FileUploader from '@spa/components/Uploader/FileUploader.vue';
import FormEditor from '@spa/components/KendoInputs/FormEditor.vue';
import { useAgentCommunicationStore } from '@spa/stores/modules/agentcommunication/useAgentCommunicationStore.js';

// Uncomment these if needed:
// import FormTextArea from '@spa/components/KendoInputs/FormTextArea.vue';
// import FormDatePicker from '@spa/components/KendoInputs/FormDatePicker.vue';
const props = defineProps({
    data: { type: Object, default: {} },
    agentId: { type: Number, default: 0 },
});

const store = useAgentCommunicationStore();
const { formData } = storeToRefs(store);
const tools = [
    ['Bold', 'Italic', 'Underline'],
    ['Undo', 'Redo'],
    ['Link', 'Unlink'],
    ['AlignLeft', 'AlignCenter', 'AlignRight'],
    ['InsertTable'],
    ['OrderedList', 'UnorderedList', 'Indent', 'Outdent'],
];
const content = ref('');
const editorRef = ref(null);
const fromEmail = ref([]);

const formOptions = ref({
    comment_by: '',
    category: [],
    status: [],
});

const fetchFormOptions = async () => {
    try {
        const response = await $http.get(`api/v2/tenant/agent-communication/get-form-options`);
        formOptions.value = response.data;
    } catch (error) {
        console.error('Error fetching form options:', error);
    }
};

const onCheckboxChange = (e) => {
    console.log('e', e);
};

onMounted(async () => {
    await fetchFormOptions();
});

watch(
    () => store.formDialog,
    (val) => {
        if (val) {
            formData.value = {
                ...formData.value,
                comment_by_name: formOptions.value.comment_by,
                agent_id: props.agentId,
            };
        }
    }
);
</script>
