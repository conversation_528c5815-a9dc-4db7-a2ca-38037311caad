<template>
    <AsyncGrid
        :columns="columns"
        :store="store"
        :has-create-action="true"
        :create-btn-label="'Add Communication Log'"
        :has-export="false"
        :has-filters="false"
        :gridStyle="{
            height: '100%',
        }"
        @handel-reset="
            () => {
                initFilters();
            }
        "
        :actions="['delete']"
    >
        <template #body-cell-created_at="{ props }">
            <FormatDateTime :date="props.dataItem?.created_at" />
        </template>
        <template #body-cell-status="{ props }">
            <Badge :variant="'purple'">{{ props.dataItem?.status_name }}</Badge>
        </template>
        <template #body-cell-type="{ props }">
            {{ props.dataItem?.type_name }}
        </template>
        <template #body-cell-log="{ props }">
            <p class="line-clamp-4" v-html="props.dataItem?.comment"></p>
        </template>
        <template #body-cell-recorded_by="{ props }">
            {{ props.dataItem?.comment_by?.full_name }}
        </template>
    </AsyncGrid>
    <AgentCommunicationForm :agentId="agentId" />
</template>

<script setup>
import AsyncGrid from '@spa/components/AsyncComponents/Grid/AsyncGrid.vue';
import { ref, onMounted } from 'vue';
import { useAgentCommunicationStore } from '@spa/stores/modules/agentcommunication/useAgentCommunicationStore.js';
import AgentCommunicationForm from '@spa/modules/agent-profile/agent-communication/AgentCommunicationForm.vue';
import FormatDateTime from '@spa/components/FormatDateTime.vue';
import Badge from '@spa/components/badges/Badge.vue';

const store = useAgentCommunicationStore();

const props = defineProps({
    agentId: Number,
});

const columns = [
    {
        name: 'created_at',
        title: 'Date/Time',
        field: 'created_at',
        sortable: true,
        replace: true,
        width: 200,
    },
    {
        name: 'type',
        title: 'Type',
        field: 'type_name',
        sortable: false,
        replace: true,
        width: 150,
    },
    {
        name: 'log',
        title: 'Log',
        field: 'comment',
        replace: true,
        width: 350,
    },
    {
        name: 'status',
        title: 'Status',
        field: 'status_name',
        sortable: true,
        replace: true,
        width: 130,
    },
    {
        name: 'recorded_by',
        title: 'Recorded By',
        field: 'recorded_by',
        sortable: true,
        replace: true,
    },
    // Add more columns as needed
];

const initFilters = () => {
    store.filters = {
        agentId: props.agentId,
    };
};

onMounted(() => {
    initFilters();
});
</script>
