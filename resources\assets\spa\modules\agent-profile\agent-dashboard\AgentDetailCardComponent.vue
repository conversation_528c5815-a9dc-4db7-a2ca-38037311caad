<template>
    <Card
        :pt="{
            root: 'p-0 md:p-0 rounded-md shadow border border-gray-300',
            content: 'p-6 space-y-3',
            header: 'p-4 md:px-6 md:py-4 border-b border-gray-200',
        }"
    >
        <template #header>
            <div class="flex items-center justify-between">
                <div class="flex items-center gap-2">
                    <h3 class="text-lg font-semibold">Personal Information</h3>
                </div>
                <Button
                    :size="'sm'"
                    :class="'border border-primary-blue-50 bg-primary-blue-50 text-primary-blue-500 shadow-none hover:text-white'"
                    @click="store.formDialog = true"
                >
                    <icon :name="'edit'" :fill="'currentColor'" :height="'18'" :width="'18'" />
                    Edit
                </Button>
            </div>
        </template>
        <template #content>
            <LabelValuePair
                :label="'Staff Number'"
                :value="store.formData?.staff_number"
                :bordered="true"
            />

            <!--            <template v-for="(value, key) in store.formData" :key="index">-->
            <!--                <LabelValuePair :label="key" :value="value" :bordered="true" />-->
            <!--            </template>-->
        </template>
    </Card>
    <TeacherForm />
</template>
<script setup>
import { computed, onMounted } from 'vue';
import Card from '@spa/components/Card/Card.vue';
import { usePage } from '@inertiajs/vue3';
import Button from '@spa/components/Buttons/Button.vue';
import LabelValuePair from '@spa/components/LabelValuePair/LabelValuePair.vue';
import TeacherForm from '@spa/modules/teacher/TeacherForm.vue';
import { useAgentStore } from '@spa/stores/modules/agent/useAgentStore.js';

const store = useAgentStore();
const $page = usePage();
const teacherId = computed(() => $page.props.params.id);

onMounted(() => {
    store.fetchDataById(teacherId.value);
});
</script>
<style lang=""></style>
