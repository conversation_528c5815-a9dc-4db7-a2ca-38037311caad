<template>
    <AsyncGrid
        :columns="columns"
        :store="store"
        :has-create-action="false"
        :has-actions="false"
        :has-export="false"
        :has-filters="false"
        :gridStyle="{
            height: '100%',
        }"
        @handel-reset="
            () => {
                initFilters();
            }
        "
    >
        <template #body-cell-application_id="{ props }">
            <CopyToClipboard :text="props.dataItem.application_reference_id" />
        </template>
        <template #body-cell-student_id="{ props }">
            <CopyToClipboard :text="props.dataItem.generated_stud_id" />
        </template>
        <template #body-cell-student_name="{ props }">
            <div class="flex items-center gap-2">
                <Avatar :label="'AN'" />
                <span>{{ props.dataItem.student_name }}</span>
            </div>
        </template>
        <template #body-cell-current_course="{ props }">
            <div class="flex gap-2">
                <span class="truncate">{{ props.dataItem?.current_course?.[0].course_name }}</span>
                <span
                    class="rounded-sm bg-gray-100 px-2 py-1 text-xs"
                    v-if="props.dataItem?.current_course?.length > 1"
                >
                    +{{ props.dataItem.current_course.length - 1 }}</span
                >
            </div>
        </template>
        <template #body-cell-campus="{ props }">
            <div class="flex gap-2">
                <span class="truncate">{{ props.dataItem?.campus?.[0] }}</span>
                <span
                    class="rounded-sm bg-gray-100 px-2 py-1 text-xs"
                    v-if="props.dataItem?.campus?.length > 1"
                >
                    +{{ props.dataItem.campus.length - 1 }}</span
                >
            </div>
        </template>
    </AsyncGrid>
</template>

<script setup>
import AsyncGrid from '@spa/components/AsyncComponents/Grid/AsyncGrid.vue';
import { ref, onMounted } from 'vue';
import { useAgentStudentsStore } from '@spa/stores/modules/agentstudents/useAgentStudentsStore.js';
import Badge from '@spa/components/badges/Badge.vue';
import Avatar from '@spa/components/Avatar/Avatar.vue';
import CopyToClipboard from '@spa/components/CopyAction/CopyToClipboard.vue';

const store = useAgentStudentsStore();

const props = defineProps({
    agentId: Number,
});

const columns = [
    {
        field: 'application_reference_id',
        name: 'application_id',
        title: 'Application ID',
        replace: true,
        width: '200px',
    },
    {
        field: 'generated_stud_id',
        name: 'student_id',
        title: 'Student ID',
        replace: true,
        width: '200px',
    },
    {
        field: 'student_name',
        name: 'student_name',
        title: 'Student Name',
        replace: true,
        width: '200px',
    },
    {
        field: 'campus',
        name: 'campus',
        title: 'Campus',
        replace: true,
        width: '200px',
    },
    {
        field: 'current_course_name',
        name: 'current_course',
        title: 'Current Course',
        replace: true,
    },
    // Add more columns as needed
];

const getBadgeVariant = (value) => {
    let badgeMapping = {
        'Current Student': 'primary-blue',
        Cancelled: 'red',
        Transitioned: 'yellow',
        Completed: 'green',
        Finished: 'green',
        Withdrawn: 'pink',
        Suspended: 'red',
        Enrolled: 'primary-blue',
        'New Application Request': 'green',
    };
    return badgeMapping[value] || 'default';
};

const initFilters = () => {
    store.filters = {
        agentId: props.agentId,
    };
};

onMounted(() => {
    initFilters();
});
</script>
