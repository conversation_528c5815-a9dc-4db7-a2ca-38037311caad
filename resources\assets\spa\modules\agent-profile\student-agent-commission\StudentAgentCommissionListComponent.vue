<template>
    <AsyncGrid
        :columns="columns"
        :store="store"
        :has-create-action="false"
        :has-bulk-actions="true"
        :has-export="false"
        :has-filters="true"
        :gridStyle="{
            height: '100%',
        }"
        @handel-reset="
            () => {
                initFilters();
            }
        "
        :actions="['delete']"
        :enable-selection="true"
    >
        <template #bulk-actions>
            <Button
                v-if="store.selected?.length > 0"
                class="tw-btn-primary !h-9 shadow-none"
                size="xs"
                @click="
                    () => {
                        store.formDialog = true;
                    }
                "
            >
                <icon name="check" :fill="'currentColor'" />
                Approve Commission
            </Button>
            <Button
                v-if="store.selected?.length > 0"
                class="tw-btn-primary !h-9 shadow-none"
                size="xs"
                @click="
                    () => {
                        store.formDialog = true;
                    }
                "
            >
                <icon name="sync" :fill="'currentColor'" />
                Sync To Xero
            </Button>
        </template>
        <template #body-cell-student_id="{ props }">
            <CopyToClipboard :text="props.dataItem?.student?.generated_stud_id" />
        </template>
        <template #body-cell-student_name="{ props }">
            <div class="flex items-center gap-2">
                <Avatar :label="'AN'" />
                <span>{{ props.dataItem?.student?.name }}</span>
            </div>
        </template>
        <template #body-cell-course="{ props }">
            {{ props.dataItem?.course?.code }} - {{ props.dataItem?.course?.name }}
        </template>
        <template #body-cell-invoice_number="{ props }">
            {{ props.dataItem?.formatted_invoice_number }}
        </template>
        <template #body-cell-paid_date="{ props }">
            <FormatDateTime :date="props.dataItem?.paid_date" />
        </template>
        <template #body-cell-comm_paid_date="{ props }">
            <FormatDateTime :date="props.dataItem?.comm_paid_date" />
        </template>
        <template #body-cell-refund_amount="{ props }">
            <PriceColumn :value="props.dataItem?.refund_amount" />
        </template>
        <template #body-cell-transaction_amount="{ props }">
            <PriceColumn :value="props.dataItem?.transaction_amount" />
        </template>
        <template #body-cell-commission_payable="{ props }">
            <PriceColumn :value="props.dataItem?.commission_payable" />
        </template>
        <template #body-cell-gst_amount="{ props }">
            <PriceColumn :value="props.dataItem?.gst_amount" />
        </template>
        <template #body-cell-total_paid="{ props }">
            <PriceColumn :value="props.dataItem?.total_paid" />
        </template>
        <template #body-cell-remarks="{ props }">
            <div v-html="props.dataItem?.remarks || '-'" />
        </template>
    </AsyncGrid>
    <StudentAgentCommissionForm />
</template>

<script setup>
import AsyncGrid from '@spa/components/AsyncComponents/Grid/AsyncGrid.vue';
import { ref, onMounted } from 'vue';
import { useStudentAgentCommissionStore } from '@spa/stores/modules/student-agent-commission/useStudentAgentCommissionStore.js';
import StudentAgentCommissionForm from '@spa/modules/agent-profile/student-agent-commission/StudentAgentCommissionForm.vue';
import PriceColumn from '@spa/components/AsyncComponents/Grid/Partials/ColumnTemplates/PriceColumn.vue';
import Avatar from '@spa/components/Avatar/Avatar.vue';
import FormatDateTime from '@spa/components/FormatDateTime.vue';
import CopyToClipboard from '@spa/components/CopyAction/CopyToClipboard.vue';

const store = useStudentAgentCommissionStore();

const columns = [
    {
        field: 'formatted_invoice_number',
        name: 'invoice_number',
        title: 'Invoice Number',
        width: '200px',
        replace: true,
    },
    {
        field: 'generated_stud_id',
        name: 'student_id',
        title: 'Student ID',
        width: '200px',
        replace: true,
    },
    {
        field: 'student_name',
        name: 'student_name',
        title: 'Student Name',
        width: '200px',
        replace: true,
    },
    {
        field: 'course_code',
        name: 'course',
        title: 'Course',
        width: '200px',
        replace: true,
    },
    {
        field: 'due_date',
        title: 'Due Date',
        width: '200px',
    },
    {
        field: 'paid_date',
        name: 'paid_date',
        title: 'Paid Date',
        width: '200px',
        replace: true,
    },
    {
        field: 'transaction_amount',
        name: 'transaction_amount',
        title: 'Transaction Amount',
        width: '200px',
        replace: true,
    },
    {
        field: 'refund_amount',
        name: 'refund_amount',
        title: 'Refund Amount',
        width: '200px',
        replace: true,
    },
    {
        field: 'commission',
        name: 'commission',
        title: 'Commission',
        width: '200px',
        replace: true,
    },
    {
        field: 'gst_amount',
        name: 'gst_amount',
        title: 'GST',
        width: '200px',
        replace: true,
    },
    {
        field: 'total_paid',
        name: 'total_paid',
        title: 'Commission Paid',
        width: '200px',
        replace: true,
    },
    {
        field: 'commission_payable',
        name: 'commission_payable',
        title: 'Commission Payable',
        width: '200px',
        replace: true,
    },
    {
        field: 'remarks',
        name: 'remarks',
        title: 'Remarks',
        width: '200px',
        replace: true,
    },
    // Add more columns as needed
];

const initFilters = () => {
    store.filters = {};
};

onMounted(() => {
    initFilters();
});
</script>
