<template>
    <AsyncGrid
        :columns="columns"
        :store="store"
        :has-create-action="true"
        :create-btn-label="'Add Agent'"
        :has-export="false"
        :has-filters="false"
        :gridStyle="{
            height: '100%',
        }"
        @handel-reset="
            () => {
                initFilters();
            }
        "
        :has-actions="true"
        :has-more-actions="true"
        :actions="['edit', 'delete']"
        :has-floating-actions="false"
        :enable-selection="true"
        :has-bulk-delete="true"
        :has-bulk-export="false"
    >
        <template #body-cell-status="{ props }">
            <Badge :variant="getBadgeVariant(props.dataItem.status.name)">
                {{ props.dataItem.status.name }}
            </Badge>
        </template>
        <template #body-cell-name="{ props }">
            {{ props.dataItem.name }}
        </template>
        <template #body-cell-account_manager="{ props }">
            {{ props.dataItem.manager?.name || '-' }}
        </template>
        <template #body-cell-super_agent="{ props }">
            {{ props.dataItem.super_agent?.name || '-' }}
        </template>
        <template #floating-actions>
            <Button @click="agentEmailStore.formDialog = true" :variant="'secondary'" :size="'sm'">
                <icon name="mail" width="16" height="16" :fill="'currentColor'" />
                Send Mail
            </Button>
        </template>
        <template #column-menu-id="{ props }">
            <async-grid-column-filter
                :column="props.column"
                :filter="props.filter"
                :sort="props.sort"
                :columns="columns"
                @sortchange="(desc, e) => props.onColumnSortChange(desc, e)"
                @filterchange="(desc, e) => props.onFilterChange(desc, e)"
                :has-custom-filter="true"
                :data-items="store.all"
            >
            </async-grid-column-filter>
        </template>
        <template #actions="{ row }">
            <GridActionMenuItem
                @click="editAgent(row)"
                :item="{
                    icon: 'mail',
                    label: 'Communication Log',
                    id: 'communication-log',
                }"
            />
            <GridActionMenuItem
                @click="editAgent(row)"
                :item="{
                    icon: 'user',
                    label: 'Create User',
                    id: 'create-user',
                }"
            />
            <GridActionMenuItem
                @click="editAgent(row)"
                :item="{
                    icon: 'add',
                    label: 'Add Agent Status',
                    id: 'add-agent-status',
                }"
            />
            <GridActionMenuItem
                @click="editAgent(row)"
                :item="{
                    icon: 'user',
                    label: 'View Students',
                    id: 'view-students',
                }"
            />
            <GridActionMenuItem
                @click="editAgent(row)"
                :item="{
                    icon: 'download',
                    label: 'Export Students',
                    id: 'export-students',
                }"
            />
        </template>
    </AsyncGrid>
    <AgentForm />
    <AgentEmailForm />
</template>

<script setup>
import AsyncGrid from '@spa/components/AsyncComponents/Grid/AsyncGrid.vue';
import { ref, onMounted } from 'vue';
import { useAgentStore } from '@spa/stores/modules/agent/useAgentStore.js';
import AgentForm from '@spa/modules/agent/AgentForm.vue';
import Badge from '@spa/components/badges/Badge.vue';
import Button from '@spa/components/Buttons/Button.vue';
import AgentEmailForm from '@spa/modules/agent-email/AgentEmailForm.vue';
import { useAgentEmailStore } from '@spa/stores/modules/agent-email/useAgentEmailStore.js';
import AsyncGridColumnFilter from '@spa/components/AsyncComponents/Grid/Partials/AsyncGridColumnFilter.vue';
import GridActionMenuItem from '@spa/components/AsyncComponents/Grid/Partials/GridActionMenuItem.vue';
const store = useAgentStore();
const agentEmailStore = useAgentEmailStore();

const columns = [
    {
        field: 'id',
        name: 'id',
        title: 'ID',
        width: '100px',
        sortable: true,
        // filterable: true,
    },
    {
        field: 'agency_name',
        name: 'name',
        title: 'Name',
        width: '200px',
        sortable: true,
        replace: true,
    },
    {
        field: 'contact_person',
        title: 'Person',
        width: '200px',
        canHide: true,
        sortable: true,
    },
    {
        field: 'phone',
        title: 'Phone',
        width: '150px',
        canHide: true,
    },
    {
        field: 'primary_email',
        name: 'primary_email',
        title: 'Email',
        width: '150px',
        canHide: true,
        sortable: true,
        // filterable: {
        //     filter: 'text',
        //     type: 'advance',
        // },
    },
    {
        field: 'agent_code',
        title: 'Agent Code',
        width: '150px',
        canHide: true,
    },
    {
        field: 'super_agent_id',
        name: 'super_agent',
        title: 'Super Agent',
        width: '150px',
        sortable: true,
        replace: true,
    },
    {
        field: 'status',
        name: 'status',
        title: 'Status',
        width: '100px',
        replace: true,
        sortable: true,
    },
    {
        field: 'account_manager_id',
        name: 'account_manager',
        title: 'Account Manager',
        width: '200px',
        replace: true,
        canHide: true,
        sortable: true,
    },
    // Add more columns as needed
];

const getBadgeVariant = (value) => {
    let badgeMapping = {
        Active: 'success',
        Inactive: 'error',
        Preliminary: 'info',
        'Agent Active': 'success',
        'Agent Inactive': 'error',
        Terminated: 'error',
    };
    return badgeMapping[value] || 'info';
};

const initFilters = () => {
    store.filters = {};
};

onMounted(() => {
    initFilters();
});
</script>
