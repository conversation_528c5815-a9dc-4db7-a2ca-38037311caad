<template>
    <AsyncForm
        :type="'kendo'"
        :layout="{ cols: 2, gap: 16 }"
        :orientation="'vertical'"
        position="right"
        :dialogTitle="'Add Courses'"
        :store="store"
    >
        <div class="p-4">
            <div class="p-2">
                <FormInput
                    name="name"
                    label="Name"
                    v-model="formData.name"
                    :validation-message="store.errors?.name"
                    :valid="!store.errors?.name"
                    :touched="true"
                    :indicaterequired="true"
                />
            </div>
        </div>
    </AsyncForm>
</template>
<script setup>
import AsyncForm from '@spa/components/AsyncComponents/Form/AsyncFormPopup.vue';
import FormInput from '@spa/components/KendoInputs/FormInput.vue';
import { useCoursesStore } from '@spa/stores/modules/courses/useCoursesStore.js';
import { storeToRefs } from 'pinia';
// Uncomment these if needed:
// import FormTextArea from '@spa/components/KendoInputs/FormTextArea.vue';
// import FormDatePicker from '@spa/components/KendoInputs/FormDatePicker.vue';

const store = useCoursesStore();
const { formData } = storeToRefs(store);
</script>
