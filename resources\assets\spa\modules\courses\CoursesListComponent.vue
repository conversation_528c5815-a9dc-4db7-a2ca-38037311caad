<template>
    <AsyncGrid
        :columns="columns"
        :store="store"
        :has-create-action="true"
        :has-export="false"
        :has-filters="false"
        :gridStyle="{
            height: '100%',
        }"
        @handel-reset="
            () => {
                initFilters();
            }
        "
        :actions="['delete']"
    >
    </AsyncGrid>
    <CoursesForm />
</template>

<script setup>
import AsyncGrid from '@spa/components/AsyncComponents/Grid/AsyncGrid.vue';
import { ref, onMounted } from 'vue';
import { useCoursesStore } from '@spa/stores/modules/courses/useCoursesStore.js';
import CoursesForm from '@spa/modules/courses/CoursesForm.vue';

const store = useCoursesStore();

const columns = [
    {
        field: 'name',
        title: 'Name',
        width: '200px',
    },
    // Add more columns as needed
];

const initFilters = () => {
    store.filters = {};
    // Reset pagination to page 1 when filters are reset
    store.serverPagination.page = 1;
};

onMounted(() => {
    initFilters();
});
</script>
