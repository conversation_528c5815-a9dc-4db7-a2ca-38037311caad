<template>
    <AsyncGrid
        :columns="columns"
        :store="store"
        :has-create-action="true"
        :has-export="false"
        :has-filters="false"
        :has-actions="true"
        :create-btn-label="'Allocate Credit Bonus'"
        :gridStyle="{
            height: '100%',
        }"
        @handel-reset="
            () => {
                initFilters();
            }
        "
        :actions="['delete', 'edit']"
    >
        <template #body-cell-type_name="{ props }">
            {{ props.dataItem?.type_name }}
        </template>
        <template #body-cell-payment_mode="{ props }">
            <Badge>
                {{ props.dataItem?.payment_mode_data?.name }}
            </Badge>
        </template>
        <template #body-cell-credit_used="{ props }">
            <PriceColumn :value="props.dataItem?.credit_used" />
        </template>
        <template #body-cell-credit_amount="{ props }">
            <PriceColumn :value="props.dataItem?.amount" />
        </template>
        <template #body-cell-comment="{ props }">
            <p v-html="props.dataItem?.comment"></p>
        </template>
        <template #actions="{ row }">
            <GridActionButton
                @click="handlePreview(row)"
                tooltip-title="View Student Payment Agent Credit"
            >
                <icon :name="'eye'" :fill="'currentColor'" :height="'20'" :width="'20'" />
            </GridActionButton>
        </template>
    </AsyncGrid>
    <CreditBonusAllocationForm :agentId="agentId" />
    <SidebarDrawer
        :visibleDialog="store.showStudentPaymentAgentCredit"
        :hideOnOverlayClick="true"
        :fixedActionBar="false"
        :width="'80%'"
        :max-width="'900px'"
        @drawerclose="store.showStudentPaymentAgentCredit = false"
    >
        <template #title>
            <div class="text-lg font-medium">Student Payment Agent Credit</div>
        </template>
        <template #content>
            <StudentPaymentAgentCreditListComponent
                :filters="{
                    bonusId: store.selected?.id,
                }"
            />
        </template>
    </SidebarDrawer>
</template>

<script setup>
import AsyncGrid from '@spa/components/AsyncComponents/Grid/AsyncGrid.vue';
import { ref, onMounted } from 'vue';
import { useCreditBonusAllocationStore } from '@spa/stores/modules/credit-bonus-allocation/useCreditBonusAllocationStore.js';
import CreditBonusAllocationForm from '@spa/modules/credit-bonus-allocation/CreditBonusAllocationForm.vue';
import Badge from '@spa/components/badges/Badge.vue';
import PriceColumn from '@spa/components/AsyncComponents/Grid/Partials/ColumnTemplates/PriceColumn.vue';
import StudentPaymentAgentCreditListComponent from '@spa/modules/student-payment-agent-credit/StudentPaymentAgentCreditListComponent.vue';
import SidebarDrawer from '@spa/components/KendoModals/SidebarDrawer.vue';
import GridActionButton from '@spa/components/AsyncComponents/Grid/Partials/GridActionButton.vue';

const props = defineProps({
    agentId: [String, Number],
});

const store = useCreditBonusAllocationStore();

const columns = [
    {
        field: 'type',
        name: 'type_name',
        title: 'Type',
        width: '200px',
        replace: true,
        sortable: true,
    },
    {
        field: 'amount',
        name: 'credit_amount',
        title: 'Credit Amount',
        width: '150px',
        replace: true,
        sortable: true,
    },
    {
        field: 'payment_mode',
        name: 'payment_mode',
        title: 'Payment Mode',
        width: '150px',
        replace: true,
        sortable: true,
    },
    {
        field: 'credit_used',
        name: 'credit_used',
        title: 'Credit Used',
        width: '150px',
        replace: true,
        sortable: true,
    },
    {
        field: 'comment',
        name: 'comment',
        title: 'Comment',
        replace: true,
        width: 300,
    },
    // Add more columns as needed
];

const handlePreview = (row) => {
    store.selected = row;
    store.showStudentPaymentAgentCredit = true;
};

const initFilters = () => {
    store.filters = {
        agentId: props.agentId,
    };
};

onMounted(() => {
    initFilters();
});
</script>
