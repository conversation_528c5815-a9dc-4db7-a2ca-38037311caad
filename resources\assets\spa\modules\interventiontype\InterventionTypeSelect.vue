<script setup>
import AsyncSelect from '@spa/components/AsyncComponents/Select/AsyncSelect.vue';
import CreateForm from '@spa/modules/interventiontype/InterventionTypeForm.vue';
import { useInterventionTypeStore } from '@spa/stores/modules/interventiontype/useInterventionTypeStore.js';
import { computed } from 'vue';

const props = defineProps({
    modelValue: [String, Number, Array, Object],
    label: String,
    className: String,
    optionValue: {
        type: String,
        default: 'id',
    },
    optionLabel: {
        type: String,
        default: 'intervention_type',
    },
    disabled: Boolean,
    clearable: {
        type: Boolean,
        default: true,
    },
    multiple: {
        type: Boolean,
        default: false,
    },
    readonly: Boolean,
    useChips: {
        type: Boolean,
        default: false,
    },
    placeholder: {
        type: String,
        default: 'Select Intervention Type',
    },
    hasCreateAction: {
        type: Boolean,
        default: true,
    },
    valid: {
        type: Boolean,
        default: true,
    },
    validationMessage: {
        type: String,
        default: '',
    },
    indicaterequired: {
        type: Boolean,
        default: false,
    },
    initFormData: {
        type: Object,
        default: () => ({}),
    },
    filters: {
        type: Object,
        default: () => ({}),
    },
    forceReload: {
        type: Boolean,
        default: false,
    },
});

const emit = defineEmits(['update:modelValue', 'created']);
const vModel = computed({
    get() {
        return props.modelValue;
    },
    set(val) {
        emit('update:modelValue', val);
    },
});
const store = useInterventionTypeStore();
</script>

<template>
    <AsyncSelect
        :label="label"
        :className="className"
        :optionValue="optionValue"
        :optionLabel="optionLabel"
        :disabled="disabled"
        :store="store"
        v-model="vModel"
        :clearable="clearable"
        :multiple="multiple"
        :readonly="readonly"
        :useChips="useChips"
        :placeholder="placeholder"
        :hasCreateAction="hasCreateAction"
        :valid="valid"
        :validationMessage="validationMessage"
        :indicaterequired="indicaterequired"
        :initFormData="initFormData"
        :filters="filters"
        :forceReload="forceReload"
    >
        <template #createDialog>
            <CreateForm :position="'center'" @success="(res) => emit('created', res)" />
        </template>
    </AsyncSelect>
</template>

<style scoped></style>
