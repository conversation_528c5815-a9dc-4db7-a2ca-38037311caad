<template>
    <SidebarDrawer
        :visibleDialog="store.formDialog"
        :fixedActionBar="false"
        :primaryBtnLabel="`Bulk Verify`"
        :secondaryBtnLabel="'Cancel'"
        :isDisabled="store.ctxLoading['update-default'] || store.ctxLoading['update']"
        :isSubmitting="store.ctxLoading['update-default'] || store.ctxLoading['update']"
        :pt="{ content: 'p-0', root: 'max-w-[800px] w-full' }"
        @drawerclose="store.formDialog = false"
        :wrapper-id="'pdfTemplateForm'"
    >
        <template #title>
            <div class="text-lg font-medium">Edit Pdf Template</div>
        </template>
        <template #content>
            <Form
                @submit="handleSubmit"
                :initialValues="initialValues"
                :ref="store.formRef"
                :ignoreModified="true"
            >
                <FormElement>
                    <fieldset class="space-y-4 pt-4">
                        <!-- PDF Type Field -->
                        <div class="px-6">
                            <Field
                                :id="'type_label'"
                                :name="'type_label'"
                                :component="'myTemplate'"
                                :label="'Pdf Type:'"
                                :orientation="'horizontal'"
                                :disabled="true"
                                :value="store.formData.type_label"
                            >
                                <template v-slot:myTemplate="{ props }">
                                    <FormInput
                                        v-bind="props"
                                        @blur="props.onBlur"
                                        @focus="props.onFocus"
                                    />
                                </template>
                            </Field>
                        </div>
                        <!-- Editor -->
                        <div class="h-auto flex-1 space-y-4 shadow-inner-line">
                            <div class="flex items-center justify-end gap-4 px-6">
                                <Button
                                    variant="secondary"
                                    size="xs"
                                    class="min-w-[100px]"
                                    :disabled="false"
                                    @click="handleLoadDefault"
                                >
                                    <IconClipboardCode24Regular class="size-5 text-gray-400" />
                                    Load Default Template
                                </Button>
                                <Button
                                    variant="primary"
                                    size="xs"
                                    class="min-w-[100px]"
                                    :disabled="false"
                                    @click="showTemplateSlug = true"
                                >
                                    <IconBraces24Regular class="size-5 text-white" />
                                    Pdf Attributes
                                </Button>
                            </div>
                            <!--                            <Editor-->
                            <!--                                :tools="editorTools"-->
                            <!--                                :content-style="{-->
                            <!--                                    minHeight: '400px',-->
                            <!--                                    height: '100%',-->
                            <!--                                    borderRadius: '8px',-->
                            <!--                                }"-->
                            <!--                                :default-content="store.formData?.pdf_template"-->
                            <!--                                :default-edit-mode="'div'"-->
                            <!--                                ref="editorRef"-->
                            <!--                            />-->
                            <CkEditor4 v-model="store.formData.pdf_template" />
                        </div>
                        <div
                            class="sticky bottom-0 flex w-full justify-end gap-4 border-t border-gray-200 bg-white px-6 py-4"
                        >
                            <Button class="h-9" variant="secondary" @click="handleClose">
                                <span>Close</span>
                            </Button>
                            <Button
                                type="submit"
                                variant="primary"
                                class="h-9 min-w-[100px]"
                                :loading="store.ctxLoading['update']"
                                :loadingText="'Updating...'"
                            >
                                Update
                            </Button>
                            <Button
                                variant="primary"
                                class="h-9 min-w-[100px]"
                                :loading="store.ctxLoading['update-default']"
                                :loadingText="'Updating...'"
                                @click="handleSetDefault"
                            >
                                Set Default
                            </Button>
                        </div>
                    </fieldset>
                </FormElement>
            </Form>
        </template>
    </SidebarDrawer>

    <!-- Modal for viewing template slug -->
    <ViewPdfTemplateSlugDialog :visible="showTemplateSlug" @close="showTemplateSlug = false" />
</template>

<script setup>
import { ref, computed, watch } from 'vue';
import { Dialog } from '@progress/kendo-vue-dialogs';
import Button from '@spa/components/Buttons/Button.vue';
import { Form, FormElement, Field } from '@progress/kendo-vue-form';
import { requiredtrue } from '@spa/services/validators/kendoCommonValidator.js';
import { useLoaderStore } from '@spa/stores/modules/global-loader';
import FormInput from '@spa/components/KendoInputs/FormInput.vue';
import { Editor } from '@progress/kendo-vue-editor';
import ViewPdfTemplateSlugDialog from '@spa/modules/onboarding/ViewPdfTemplateSlugDialog.vue';
import { IconBraces24Regular, IconClipboardCode24Regular } from '@iconify-prerendered/vue-fluent';
import { twMerge } from 'tailwind-merge';
import { usePdfTempolateStore } from '@spa/stores/modules/pdf-templates/pdfTemplateStore.js';
import globalHelper from '@spa/plugins/global-helper';
import SidebarDrawer from '@spa/components/KendoModals/SidebarDrawer.vue';
import useConfirm from '@spa/services/useConfirm';
import CkEditor4 from '@spa/components/Editor/CKEditor4.vue';

const editorTools = [
    'FontSize',
    ['Bold', 'Italic', 'Underline', 'Strikethrough'],
    ['Subscript', 'Superscript'],
    ['AlignLeft', 'AlignCenter', 'AlignRight', 'AlignJustify'],
    ['Indent', 'Outdent'],
    ['OrderedList', 'UnorderedList'],
    // 'FontName',
    'FormatBlock',
    ['Undo', 'Redo'],
    ['Link', 'Unlink', 'InsertImage', 'ViewHtml'],
    ['InsertTable'],
    ['AddRowBefore', 'AddRowAfter', 'AddColumnBefore', 'AddColumnAfter'],
    ['DeleteRow', 'DeleteColumn', 'DeleteTable'],
    ['MergeCells', 'SplitCell'],
];

const store = usePdfTempolateStore();
const loaderStore = useLoaderStore();
const confirm = useConfirm();

const showTemplateSlug = ref(false);
const closing = ref(false);
const editorRef = ref(null);

const dialogClass = computed(() => {
    return twMerge(
        'tw-sidebar-drawer tw-content-p-0 absolute top-0 right-0 h-screen custom-modal-wrapper',
        closing.value ? 'tw-slideout-to-right' : 'tw-slidein-from-right'
    );
});

const initialValues = computed(() => {
    return {
        pdf_type: store.formData?.type_label,
        pdf_template: store.formData?.pdf_template,
    };
});

const handleSubmit = (values) => {
    // let editorHtml = editorRef.value.getHTML();
    // store.formData.pdf_template = editorHtml;
    store
        .update()
        .then((res) => {
            if (res.error) {
                globalHelper.methods.showPopupError(res.error, 'Error');
            } else {
                globalHelper.methods.showPopupSuccess('Template updated successfully', 'Success');
                store.closeFunction();
                store.fetchPaged();
            }
        })
        .catch((err) => {
            store.errors = err.response.data.errors;
        });
};

const handleSetDefault = () => {
    let editorHtml = editorRef.value.getHTML();
    loaderStore.contextLoaders['button'] = true;
    store
        .updateDefaultTemplate({ ...store.formData, default_template: editorHtml })
        .then((res) => {
            if (res.error) {
                globalHelper.methods.showPopupError(res.error, 'Error');
            } else {
                globalHelper.methods.showPopupSuccess(
                    'Template set as default successfully',
                    'Success'
                );
                store.closeFunction();
                store.fetchPaged();
            }
        })
        .finally(() => {
            loaderStore.contextLoaders['button'] = false;
        });
};

const handleClose = () => {
    closing.value = true;
    setTimeout(() => {
        store.formDialog = false;
        closing.value = false;
    }, 300);
};

const handleLoadDefault = () => {
    confirm.require({
        message:
            'Are you sure you want to load default template? This will overwrite the current template.',
        header: 'Load Default PDF Template?',
        icon: 'pi pi-exclamation-triangle',
        variant: 'warning',
        acceptLabel: 'Confirm',
        rejectLabel: 'Cancel',
        width: 500,
        accept: async () => {
            // editorRef.value?.setHTML(store.formData?.default_template);
            store.formData.pdf_template = store.formData?.default_template;
        },
        reject: () => {
            return false;
        },
        onHide: () => {
            return false;
        },
    });
};
</script>
<style lang=""></style>
