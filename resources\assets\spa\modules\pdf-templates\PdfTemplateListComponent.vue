<template>
    <AsyncGrid
        :columns="columns"
        :store="store"
        :show-refresh-button="false"
        :show-filter-button="false"
        :add-permissions="null"
        :enableSelection="false"
        :has-create-action="false"
        :has-export="false"
        :has-filters="false"
        :gridStyle="{
            height: '100%',
        }"
        @handel-reset="
            () => {
                initFilters();
                store.selected = [];
            }
        "
    >
        <template #bulk-actions> </template>
        <template #body-cell-updated_date="{ props }">
            <FormatDate :date="props.dataItem?.updated_at" />
        </template>
        <template #body-cell-type_label="{ props }">
            {{ props.dataItem?.type_label }}
        </template>
        <template #body-cell-actions="{ props }">
            <div class="flex justify-start space-x-2">
                <Tooltip
                    :anchor-element="'target'"
                    :position="'top'"
                    :parentTitle="true"
                    :tooltipClassName="'flex !p-1'"
                    :class="'w-full'"
                >
                    <button
                        @click="handleEdit(props.dataItem)"
                        class="cursor-pointer text-gray-400"
                    >
                        <icon :name="'pencil'" :width="16" :height="16" :fill="'currentColor'" />
                    </button>
                </Tooltip>
            </div>
        </template>
    </AsyncGrid>
    <EditPdfTemplateDialog :store="store" />
</template>

<script setup>
import { usePdfTempolateStore } from '@spa/stores/modules/pdf-templates/pdfTemplateStore.js';
import AsyncGrid from '@spa/components/AsyncComponents/Grid/AsyncGrid.vue';
import { ref, onMounted, watch } from 'vue';
import FormatDate from '@spa/components/FormatDate.vue';
import { Tooltip } from '@progress/kendo-vue-tooltip';
import EditPdfTemplateDialog from '@spa/modules/onboarding/EditPdfTemplateDialog.vue';

const store = usePdfTempolateStore();
const columns = [
    {
        name: 'id',
        title: 'Id',
        field: 'id',
        sortable: false,
    },
    {
        name: 'type_label',
        title: 'PDF Type',
        field: 'type',
        sortable: false,
        replace: true,
        width: 300,
    },
    {
        name: 'updated_date',
        field: 'updated_at',
        title: 'Updated Date',
        sortable: false,
        replace: true,
    },
    {
        name: 'updated_by_name',
        field: 'updated_by_name',
        title: 'Updated By',
        sortable: false,
    },
    {
        name: 'actions',
        field: 'actions',
        title: 'Actions',
        sortable: false,
        replace: true,
    },
];
const initFilters = () => {
    store.filters = {};
};

const initInertiaData = () => {};

const handleEdit = async (item) => {
    console.log('Edit clicked for item:', item);
    try {
        // Fetch complete data for the item
        await store.fetchDataById(item.id);
        // Open the dialog
        store.formDialog = true;
    } catch (error) {
        console.error('Error fetching PDF template data:', error);
        // Fallback to using the grid data
        store.edit(item);
    }
};

onMounted(() => {
    initFilters();
});
</script>
