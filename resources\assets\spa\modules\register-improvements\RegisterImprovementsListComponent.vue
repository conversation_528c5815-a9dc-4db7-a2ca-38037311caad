<template>
    <AsyncGrid
        :columns="columns"
        :store="store"
        :show-refresh-button="false"
        :show-filter-button="false"
        :add-permissions="null"
        :enableSelection="false"
        :has-create-action="true"
        :has-export="false"
        :has-filters="true"
        :gridStyle="{
            height: '100%',
        }"
        @handel-reset="
            () => {
                initFilters();
                store.selected = [];
            }
        "
        :filter-columns="2"
    >
        <template #filters>
            <FilterBlockWrapper label="Category">
                <ImprovementFilterSelect
                    v-model="store.filters.category"
                    :options="store.dropdowns?.categories"
                    :placeholder="'Select Category'"
                />
            </FilterBlockWrapper>

            <FilterBlockWrapper label="Case Status">
                <ImprovementFilterSelect
                    v-model="store.filters.status"
                    :options="statusOptions"
                    :placeholder="'Select Status'"
                />
            </FilterBlockWrapper>
            <FilterBlockWrapper label="Logged By">
                <ImprovementFilterSelect
                    v-model="store.filters.loggedBy"
                    :options="store.dropdowns?.loggedBy"
                    :placeholder="'Select Logged By'"
                />
            </FilterBlockWrapper>
            <FilterBlockWrapper label="Requested By">
                <ImprovementFilterSelect
                    v-model="store.filters.requestedBy"
                    :options="store.dropdowns?.requestedBy"
                    :placeholder="'Select Requested By'"
                />
            </FilterBlockWrapper>
        </template>
        <template #bulk-actions> </template>
        <template #body-cell-logged_date="{ props }">
            <FormatDate :date="props.dataItem?.updated_at" />
        </template>
        <template #body-cell-status="{ props }">
            <Badge :variant="props.dataItem?.status === '1' ? 'success' : 'error'">{{
                props.dataItem?.status === '1' ? 'Open' : 'Closed'
            }}</Badge>
        </template>
        <template #body-cell-requester_name="{ props }">
            {{ props.dataItem?.requester?.full_name }}
        </template>
        <template #body-cell-actions="{ props }">
            <div class="flex justify-start space-x-2">
                <Tooltip
                    :anchor-element="'target'"
                    :position="'top'"
                    :parentTitle="true"
                    :tooltipClassName="'flex !p-1'"
                    :class="'w-full'"
                >
                    <button
                        @click="store.edit(props.dataItem)"
                        class="cursor-pointer text-gray-400"
                        title="Edit"
                    >
                        <icon :name="'pencil'" :width="16" :height="16" :fill="'currentColor'" />
                    </button>
                </Tooltip>
                <Tooltip
                    :anchor-element="'target'"
                    :position="'top'"
                    :parentTitle="true"
                    :tooltipClassName="'flex !p-1'"
                    :class="'w-full'"
                >
                    <button
                        @click="store.confirmDelete(props.dataItem)"
                        class="cursor-pointer"
                        title="Delete"
                    >
                        <icon :name="'delete'" :width="16" :height="16" />
                    </button>
                </Tooltip>
            </div>
        </template>
    </AsyncGrid>
    <RegisterImprovementForm />
</template>

<script setup>
import { useRegisterImprovementStore } from '@spa/stores/modules/continuous-improvement/registerImprovementStore.js';
import AsyncGrid from '@spa/components/AsyncComponents/Grid/AsyncGrid.vue';
import { onMounted, computed } from 'vue';
import FormatDate from '@spa/components/FormatDate.vue';
import { Tooltip } from '@progress/kendo-vue-tooltip';
import RegisterImprovementForm from '@spa/modules/register-improvements/RegisterImprovementForm.vue';
import Badge from '@spa/components/badges/Badge.vue';
import Select from '@spa/components/AsyncComponents/Select/AsyncSelect.vue';
import ImprovementFilterSelect from '@spa/modules/register-improvements/partials/ImprovementFilterSelect.vue';
import FilterBlockWrapper from '@spa/components/AsyncComponents/Grid/Partials/FilterBlockWrapper.vue';
import { DateRangePicker } from '@progress/kendo-vue-dateinputs';

const store = useRegisterImprovementStore();
const columns = [
    {
        name: 'category',
        title: 'Category',
        field: 'category',
        sortable: true,
    },
    {
        name: 'case_detail',
        title: 'Details',
        field: 'case_detail',
        width: 300,
        sortable: false,
    },
    {
        name: 'logged_date',
        title: 'Lodged Date',
        field: 'logged_date',
        sortable: true,
        replace: true,
    },
    {
        name: 'requester_name',
        title: 'Requested By',
        field: 'requester_name',
        sortable: true,
        replace: true,
    },
    {
        name: 'action_taken',
        field: 'action_taken',
        title: 'Action Taken',
        sortable: false,
        width: 300,
    },
    {
        name: 'status',
        title: 'Status',
        field: 'status',
        sortable: true,
        replace: true,
        width: 80,
    },
];

const statusOptions = [
    {
        label: 'All',
        value: 'all',
    },
    {
        label: 'Open',
        value: 1,
    },
    {
        label: 'Closed',
        value: 0,
    },
];

const initFilters = () => {
    store.filters = {};
};

const initInertiaData = () => {};
onMounted(() => {
    initFilters();
});
</script>
