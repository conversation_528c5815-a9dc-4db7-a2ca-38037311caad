<template>
    <AsyncForm
        :initial-values="store.formData"
        @submit="onSubmit"
        @change="onChange"
        :type="'kendo'"
        :layout="{ cols: 2, gap: 16 }"
        :orientation="'vertical'"
        :dialogTitle="'Manage Semester'"
        :override="true"
        :store="store"
    >
        <SemesterFormContent />
    </AsyncForm>
</template>

<script setup>
import { watch } from 'vue';
import { useSemesterStore } from '@spa/stores/modules/semester/semesterStore.js';
import AsyncForm from '@spa/components/AsyncComponents/Form/AsyncFormPopup.vue';
import SemesterFormContent from '@spa/modules/semester/partials/SemesterFormContent.vue';

const store = useSemesterStore();

// Watch for form dialog opening to ensure constants are loaded
watch(
    () => store.formDialog,
    async (val) => {
        if (val) {
            // Ensure form constants are loaded when dialog opens
            if (
                store.courseTypes.length === 0 ||
                store.calendarTypes.length === 0 ||
                store.years.length === 0
            ) {
                await store.loadFormConstants();
            }
        }
    }
);

const onSubmit = () => {
    // Form submission is handled by the store
};

const onChange = () => {
    // Handle form changes if needed
};
</script>
