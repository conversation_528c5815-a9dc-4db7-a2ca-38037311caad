<template>
    <AsyncGrid
        :store="store"
        :has-create-action="true"
        :has-filters="true"
        :columns="columns"
        :gridStyle="{
            height: '100%',
        }"
        @handel-reset="
            () => {
                initFilters();
            }
        "
        :filter-columns="2"
        :has-actions="true"
        :has-export="false"
        :actions="['edit', 'delete']"
    >
        <template #filters>
            <FilterBlockWrapper label="Course Type">
                <CourseTypeSelect v-model="store.filters.course_type_id" />
            </FilterBlockWrapper>
            <FilterBlockWrapper label="Year">
                <YearSelect v-model="store.filters.year" />
            </FilterBlockWrapper>
            <FilterBlockWrapper label="Calendar Type">
                <CalendarTypeSelect v-model="store.filters.calendar_type" />
            </FilterBlockWrapper>
        </template>
    </AsyncGrid>
    <SemesterForm />
</template>

<script setup>
import { onMounted } from 'vue';
import { useSemesterStore } from '@spa/stores/modules/semester/semesterStore.js';
import AsyncGrid from '@spa/components/AsyncComponents/Grid/AsyncGrid.vue';
import FilterBlockWrapper from '@spa/components/AsyncComponents/Grid/Partials/FilterBlockWrapper.vue';
import SemesterForm from '@spa/modules/semester/SemesterForm.vue';
import CourseTypeSelect from '@spa/modules/semester/partials/CourseTypeSelect.vue';
import YearSelect from '@spa/modules/semester/partials/YearSelect.vue';
import CalendarTypeSelect from '@spa/modules/semester/partials/CalendarTypeSelect.vue';

const store = useSemesterStore();

const columns = [
    {
        field: 'semester_name',
        title: 'Semester Name',
    },
    {
        field: 'course_type',
        title: 'Course Type',
        width: '150px',
    },
    {
        field: 'calendar_type',
        title: 'Calendar Type',
        width: '150px',
    },
    {
        field: 'year',
        title: 'Year',
        width: '100px',
    },
    {
        field: 'semester_start',
        title: 'Semester Start',
        width: '150px',
    },
    {
        field: 'semester_finish',
        title: 'Semester Finish',
        width: '150px',
    },
];

const initFilters = () => {
    store.filters = {
        course_type_id: null,
        year: null,
        calendar_type: null,
    };
    // Reset pagination to page 1 when filters are reset
    store.serverPagination.page = 1;
};

onMounted(async () => {
    initFilters();
    await store.loadFormConstants();
});
</script>
