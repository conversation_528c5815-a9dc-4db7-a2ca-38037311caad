<script setup>
import { computed } from 'vue';
import { DropDownList } from '@progress/kendo-vue-dropdowns';
import { useSemesterStore } from '@spa/stores/modules/semester/semesterStore.js';

const props = defineProps({
    modelValue: [String, Number, Array, Object],
    label: String,
    className: String,
    optionValue: {
        type: String,
        default: 'value',
    },
    optionLabel: {
        type: String,
        default: 'text',
    },
    disabled: Boolean,
    clearable: {
        type: Boolean,
        default: true,
    },
    multiple: {
        type: Boolean,
        default: false,
    },
    readonly: Boolean,
    placeholder: {
        type: String,
        default: 'Select Calendar Type',
    },
});

const emit = defineEmits(['update:modelValue']);
const vModel = computed({
    get() {
        return props.modelValue;
    },
    set(val) {
        emit('update:modelValue', val);
    },
});

const store = useSemesterStore();

const calendarTypeOptions = computed(() => {
    if (!store.calendarTypes || !Array.isArray(store.calendarTypes)) return [];
    return store.calendarTypes;
});
</script>

<template>
    <DropDownList
        v-model="vModel"
        :data-items="calendarTypeOptions"
        :text-field="optionLabel"
        :value-field="optionValue"
        :data-item-key="optionValue"
        :value-primitive="true"
        :default-item="{ [optionLabel]: placeholder, [optionValue]: null }"
        :filterable="true"
        :clear-button="clearable"
        :disabled="disabled"
        :readonly="readonly"
        :multiple="multiple"
        :class-name="className"
        class="w-full"
    />
</template>

<style scoped></style>
