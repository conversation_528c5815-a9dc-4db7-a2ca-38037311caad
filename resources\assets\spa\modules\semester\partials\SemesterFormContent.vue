<template>
    <div class="grid grid-cols-2 gap-4 p-6">
        <div class="col-span-2">
            <Field
                :id="'semester_name'"
                :name="'semester_name'"
                :label="'Semester Name'"
                :component="'textTemplate'"
                :validator="requiredtrue"
                :indicaterequired="true"
                v-model="store.formData['semester_name']"
            >
                <template #textTemplate="{ props }">
                    <FormInput
                        v-bind="props"
                        @change="props.onChange"
                        @blur="props.onBlur"
                        @focus="props.onFocus"
                        placeholder="Enter Semester Name"
                    />
                </template>
            </Field>
        </div>

        <Field
            :id="'course_type_id'"
            :name="'course_type_id'"
            :label="'Course Type'"
            :component="'dropdownTemplate'"
            :validator="requiredtrue"
            :data-items="courseTypeOptions"
            :text-field="'text'"
            :data-item-key="'value'"
            :value-field="'value'"
            :value-primitive="true"
            :default-item="{
                text: 'Select Course Type',
                value: null,
            }"
            v-model="store.formData['course_type_id']"
        >
            <template #dropdownTemplate="{ props }">
                <FormDropDown
                    v-bind="props"
                    @change="props.onChange"
                    @blur="props.onBlur"
                    @focus="props.onFocus"
                />
            </template>
        </Field>

        <Field
            :id="'calendar_type'"
            :name="'calendar_type'"
            :label="'Calendar Type'"
            :component="'dropdownTemplate'"
            :validator="requiredtrue"
            :data-items="calendarTypeOptions"
            :text-field="'text'"
            :data-item-key="'value'"
            :value-field="'value'"
            :value-primitive="true"
            :default-item="{
                text: 'Select Calendar Type',
                value: null,
            }"
            v-model="store.formData['calendar_type']"
        >
            <template #dropdownTemplate="{ props }">
                <FormDropDown
                    v-bind="props"
                    @change="props.onChange"
                    @blur="props.onBlur"
                    @focus="props.onFocus"
                />
            </template>
        </Field>

        <Field
            :id="'year'"
            :name="'year'"
            :label="'Year'"
            :component="'dropdownTemplate'"
            :validator="requiredtrue"
            :data-items="yearOptions"
            :text-field="'text'"
            :data-item-key="'value'"
            :value-field="'value'"
            :value-primitive="true"
            :default-item="{
                text: 'Select Year',
                value: null,
            }"
            v-model="store.formData['year']"
        >
            <template #dropdownTemplate="{ props }">
                <FormDropDown
                    v-bind="props"
                    @change="props.onChange"
                    @blur="props.onBlur"
                    @focus="props.onFocus"
                />
            </template>
        </Field>

        <Field
            :id="'semester_start'"
            :name="'semester_start'"
            :label="'Semester Start'"
            :component="'dateTemplate'"
            :validator="requiredtrue"
            v-model="store.formData['semester_start']"
        >
            <template #dateTemplate="{ props }">
                <FormDatePicker
                    v-bind="props"
                    @change="props.onChange"
                    @blur="props.onBlur"
                    @focus="props.onFocus"
                    :format="'dd-MM-yyyy'"
                    placeholder="dd-mm-yyyy"
                />
            </template>
        </Field>

        <Field
            :id="'semester_finish'"
            :name="'semester_finish'"
            :label="'Semester Finish'"
            :component="'dateTemplate'"
            :validator="requiredtrue"
            v-model="store.formData['semester_finish']"
        >
            <template #dateTemplate="{ props }">
                <FormDatePicker
                    v-bind="props"
                    @change="props.onChange"
                    @blur="props.onBlur"
                    @focus="props.onFocus"
                    :format="'dd-MM-yyyy'"
                    placeholder="dd-mm-yyyy"
                />
            </template>
        </Field>
    </div>
</template>

<script setup>
import { computed } from 'vue';
import { useSemesterStore } from '@spa/stores/modules/semester/semesterStore.js';
import { requiredtrue } from '@spa/services/validators/kendoCommonValidator.js';
import { Field } from '@progress/kendo-vue-form';
import FormInput from '@spa/components/KendoInputs/FormInput.vue';
import FormDropDown from '@spa/components/KendoInputs/FormDropDown.vue';
import FormDatePicker from '@spa/components/KendoInputs/FormDatePicker.vue';

const store = useSemesterStore();

// Convert course types to dropdown format
const courseTypeOptions = computed(() => {
    if (!store.courseTypes || !Array.isArray(store.courseTypes)) return [];
    return store.courseTypes;
});

// Convert calendar types to dropdown format
const calendarTypeOptions = computed(() => {
    if (!store.calendarTypes || !Array.isArray(store.calendarTypes)) return [];
    return store.calendarTypes;
});

// Convert years to dropdown format
const yearOptions = computed(() => {
    if (!store.years || !Array.isArray(store.years)) return [];
    return store.years;
});
</script>
