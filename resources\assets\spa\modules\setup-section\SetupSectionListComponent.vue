<template>
    <AsyncGrid
        :columns="columns"
        :store="store"
        :has-create-action="true"
        :has-export="false"
        :has-filters="false"
        :gridStyle="{
            height: '100%',
        }"
        @handel-reset="
            () => {
                initFilters();
            }
        "
        :actions="['delete']"
    >
    </AsyncGrid>
    <SetupSectionForm />
</template>

<script setup>
import AsyncGrid from '@spa/components/AsyncComponents/Grid/AsyncGrid.vue';
import { ref, onMounted } from 'vue';
import { useSetupSectionStore } from '@spa/stores/modules/setup-section/useSetupSectionStore.js';
import SetupSectionForm from '@spa/modules/setup-section/SetupSectionForm.vue';

const store = useSetupSectionStore();

const columns = [
    {
        field: 'name',
        title: 'Name',
        width: '200px',
    },
    // Add more columns as needed
];

const initFilters = () => {
    store.filters = {};
};

onMounted(() => {
    initFilters();
});
</script>
