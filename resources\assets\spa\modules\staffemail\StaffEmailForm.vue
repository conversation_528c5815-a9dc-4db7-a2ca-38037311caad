<template>
    <AsyncForm
        :type="'kendo'"
        :layout="{ cols: 2, gap: 16 }"
        :orientation="'vertical'"
        position="right"
        :dialogTitle="'Send Email'"
        :store="store"
        :initial-values="formData"
    >
        <div class="flex flex-col">
            <div class="space-y-4 px-6 py-4">
                <field
                    :id="'mail_to'"
                    :name="'mail_to'"
                    :label="'To'"
                    :component="FormInput"
                    :disabled="true"
                    v-model="formData.mail_to"
                    :orientation="'horizontal'"
                />
                <div class="flex items-center gap-2">
                    <Checkbox id="send_to_personal" v-model="formData.send_to_personal" />
                    <label for="send_to_personal">Send to personal email</label>
                </div>
                <field
                    :id="'from_mail'"
                    :name="'from_mail'"
                    :label="'From'"
                    :component="FormDropDown"
                    :data-items="fromEmailOptions"
                    :text-field="'label'"
                    :data-item-key="'value'"
                    :value-field="'value'"
                    :value-primitive="true"
                    v-model="formData.from_mail"
                    :orientation="'horizontal'"
                    :default-item="{
                        label: 'Select From Email',
                        value: null,
                    }"
                />
                <field
                    :id="'subject'"
                    :name="'subject'"
                    :label="'Subject'"
                    :component="FormInput"
                    v-model="formData.subject"
                    :orientation="'horizontal'"
                />
                <InsertTemplateDialog @select="onSelect" />
            </div>
            <div class="flex-1 overflow-y-auto">
                <field
                    :id="'message'"
                    :name="'message'"
                    :component="'editor-content'"
                    :orientation="'horizontal'"
                    v-model="formData.message"
                    :default-content="content"
                    :default-edit-mode="'div'"
                >
                    <template #editor-content="{ props }">
                        <FormEditor v-bind="props" :tools="tools" ref="editorRef" />
                    </template>
                </field>
                <div class="flex items-center gap-2 px-6 py-4 shadow-inner-top">
                    <Checkbox id="log_status" v-model="formData.log_status" />
                    <label for="log_status">Add to Staff Communication Log</label>
                </div>
                <div class="px-6 py-3">
                    <field
                        :id="'photos'"
                        :name="'photos'"
                        v-model="formData.photos"
                        :label="'Upload Photos'"
                        :hintMessage="'Hint: Select your additional photos'"
                        :component="FormUpload"
                        :multiple="true"
                        :autoUpload="false"
                        :accept="'image/*'"
                        @add="handleOnAdd"
                    />
                </div>
            </div>
        </div>
    </AsyncForm>
</template>
<script setup>
import { ref, onMounted, watch, computed } from 'vue';
import AsyncForm from '@spa/components/AsyncComponents/Form/AsyncFormPopup.vue';
import FormInput from '@spa/components/KendoInputs/FormInput.vue';
import { useStaffEmailStore } from '@spa/stores/modules/staffemail/useStaffEmailStore.js';
import { storeToRefs } from 'pinia';
import { Field } from '@progress/kendo-vue-form';
import FormDropDown from '@spa/components/KendoInputs/FormDropDown.vue';
import { Editor } from '@progress/kendo-vue-editor';
import InsertTemplateDialog from '@spa/modules/staffemail/partials/InsertTemplateDialog.vue';
import Checkbox from '@spa/components/Checkbox.vue';
import FormEditor from '@spa/components/KendoInputs/FormEditor.vue';
import FormUpload from '@spa/components/KendoInputs/FormUpload.vue';

// Uncomment these if needed:
// import FormTextArea from '@spa/components/KendoInputs/FormTextArea.vue';
// import FormDatePicker from '@spa/components/KendoInputs/FormDatePicker.vue';
const props = defineProps({
    data: { type: Object, default: {} },
});

const store = useStaffEmailStore();
const { formData } = storeToRefs(store);
const tools = [
    ['Bold', 'Italic', 'Underline'],
    ['Undo', 'Redo'],
    ['Link', 'Unlink'],
    ['AlignLeft', 'AlignCenter', 'AlignRight'],
    ['InsertTable'],
    ['OrderedList', 'UnorderedList', 'Indent', 'Outdent'],
];
const content = ref('');
const editorRef = ref(null);
const fromEmail = ref([]);
const onSelect = (template) => {
    content.value = template.content;
    editorRef.value?.setHTML(template.content);
    formData.value = {
        ...formData.value,
        subject: template.subject,
        message: template.content,
        template_id: template.id,
    };
};

const fromEmailOptions = computed(() => {
    return store.collegeEmail.map((item) => {
        return {
            label: `${item.type} (${item.email})`,
            value: item.email,
        };
    });
});

const onCheckboxChange = (e) => {
    console.log('e', e);
};

const handleOnAdd = (e, files) => {
    console.log('handleAdd', files);
};

watch(
    () => store.formDialog,
    (val) => {
        if (val) {
            store.getCollegeMail();
            formData.value = {
                ...formData.value,
                mail_to: props.data.email,
                staff_id: props.data.id,
                from_mail: fromEmailOptions.value[0]?.value,
            };
        }
    }
);
</script>
