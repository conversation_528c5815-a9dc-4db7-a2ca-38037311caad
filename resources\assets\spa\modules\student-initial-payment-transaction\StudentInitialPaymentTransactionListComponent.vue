<template>
    <AsyncGrid
        :columns="columns"
        :store="store"
        :has-create-action="true"
        :has-export="false"
        :has-filters="false"
        :gridStyle="{
            height: '100%',
        }"
        @handel-reset="
            () => {
                initFilters();
            }
        "
        :actions="['delete']"
    >
    </AsyncGrid>
    <StudentInitialPaymentTransactionForm />
</template>

<script setup>
import AsyncGrid from '@spa/components/AsyncComponents/Grid/AsyncGrid.vue';
import { ref, onMounted } from 'vue';
import { useStudentInitialPaymentTransactionStore } from '@spa/stores/modules/studentinitialpaymenttransaction/useStudentInitialPaymentTransactionStore.js';
import StudentInitialPaymentTransactionForm from '@spa/modules/student-initial-payment-transaction/StudentInitialPaymentTransactionForm.vue';

const store = useStudentInitialPaymentTransactionStore();

const columns = [
    {
        field: 'name',
        title: 'Name',
        width: '200px',
    },
    // Add more columns as needed
];

const initFilters = () => {
    store.filters = {};
};

onMounted(() => {
    initFilters();
});
</script>
