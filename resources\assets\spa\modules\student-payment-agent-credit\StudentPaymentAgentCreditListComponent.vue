<template>
    <AsyncGrid
        :columns="columns"
        :store="store"
        :has-create-action="false"
        :has-export="false"
        :has-filters="false"
        :has-actions="false"
        :gridStyle="{
            height: '100%',
        }"
        @handel-reset="
            () => {
                initFilters();
            }
        "
    >
    </AsyncGrid>
    <StudentPaymentAgentCreditForm />
</template>

<script setup>
import AsyncGrid from '@spa/components/AsyncComponents/Grid/AsyncGrid.vue';
import { ref, onMounted } from 'vue';
import { useStudentPaymentAgentCreditStore } from '@spa/stores/modules/student-payment-agent-credit/useStudentPaymentAgentCreditStore.js';
import StudentPaymentAgentCreditForm from '@spa/modules/student-payment-agent-credit/StudentPaymentAgentCreditForm.vue';

const props = defineProps({
    filters: Object,
    default: () => ({}),
});
const store = useStudentPaymentAgentCreditStore();

const columns = [
    {
        field: 'student_id',
        name: 'student_id',
        title: 'Student Id',
        replace: true,
        width: '200px',
    },
    {
        field: 'course_info',
        name: 'course_info',
        title: 'Course Info',
        replace: true,
        width: '200px',
    },
    {
        field: 'used_transaction',
        name: 'used_transaction',
        title: 'Used Transaction',
        replace: true,
        width: '200px',
    },
    {
        field: 'receipt',
        name: 'receipt',
        title: 'Receipt',
        replace: true,
        width: '200px',
    },
    {
        field: 'amount',
        name: 'amount',
        title: 'Amount',
        replace: true,
        width: '200px',
    },
    // Add more columns as needed
];

const initFilters = () => {
    const filters = {
        ...props.filters,
    };
    store.filters = filters;
};

onMounted(() => {
    initFilters();
});
</script>
