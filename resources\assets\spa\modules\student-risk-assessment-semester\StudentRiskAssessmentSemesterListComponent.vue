<template>
    <AsyncGrid
        :columns="columns"
        :store="store"
        :has-create-action="false"
        :has-export="false"
        :has-filters="true"
        :filter-columns="2"
        :gridStyle="{
            height: '100%',
        }"
        @handel-reset="
            () => {
                initFilters();
            }
        "
        :has-actions="true"
        :actions="[]"
        :has-floating-actions="false"
        :enable-selection="false"
        :has-bulk-delete="false"
        :has-bulk-export="false"
    >
        <template #filters>
            <FilterBlockWrapper label="Risk Level">
                <RiskLevelSelect v-model="store.filters.risk_type" />
            </FilterBlockWrapper>
            <FilterBlockWrapper label="Status">
                <StatusSelect v-model="store.filters.status" />
            </FilterBlockWrapper>
        </template>
        <!-- Grouped View Templates -->
        <template v-if="store.viewMode === 'grouped'" #body-cell-student_name="{ props }">
            {{ props.dataItem.student_name || '-' }}
        </template>
        <template v-if="store.viewMode === 'grouped'" #body-cell-student_number="{ props }">
            {{ props.dataItem.student_number || '-' }}
        </template>
        <template v-if="store.viewMode === 'grouped'" #body-cell-course_name="{ props }">
            {{ props.dataItem.course?.display_name || '-' }}
        </template>
        <template v-if="store.viewMode === 'grouped'" #body-cell-total_assessments="{ props }">
            <span class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800">
                {{ props.dataItem.assessment_summary?.total_assessments || 0 }}
            </span>
        </template>
        <template v-if="store.viewMode === 'grouped'" #body-cell-current_risk_level="{ props }">
            <Badge :variant="getRiskTypeBadgeVariant(props.dataItem.assessment_summary?.current_risk_level)">
                {{ props.dataItem.assessment_summary?.current_risk_level_name || '-' }}
            </Badge>
        </template>
        <template v-if="store.viewMode === 'grouped'" #body-cell-risk_trend="{ props }">
            <span class="inline-flex items-center text-sm font-medium" :class="getRiskTrendClass(props.dataItem.risk_trend?.trend)">
                {{ props.dataItem.risk_trend?.trend_display || '-' }}
            </span>
        </template>
        <template v-if="store.viewMode === 'grouped'" #body-cell-latest_assessment="{ props }">
            {{ props.dataItem.assessment_summary?.latest_assessment_date_formatted || '-' }}
        </template>
        <template v-if="store.viewMode === 'grouped'" #body-cell-needs_attention="{ props }">
            <Badge v-if="props.dataItem.needs_attention" variant="error">
                Needs Attention
            </Badge>
            <Badge v-else variant="success">
                Good
            </Badge>
        </template>

        <!-- Original Detailed View Templates -->
        <template v-else #body-cell-student_name="{ props }">
            {{ props.dataItem.risk_assessment?.student?.name || '-' }}
        </template>
        <template v-else #body-cell-student_id="{ props }">
            {{ props.dataItem.risk_assessment?.student?.student_id || '-' }}
        </template>
        <template v-else #body-cell-course_name="{ props }">
            {{ props.dataItem.risk_assessment?.course?.name || '-' }}
        </template>
        <template v-else #body-cell-semester_name="{ props }">
            {{ props.dataItem.semester?.semester_name || '-' }}
        </template>
        <template v-else #body-cell-risk_type="{ props }">
            <Badge :variant="getRiskTypeBadgeVariant(props.dataItem.risk_type)">
                {{ props.dataItem.risk_type_name || getRiskTypeName(props.dataItem.risk_type) }}
            </Badge>
        </template>
        <template v-else #body-cell-status="{ props }">
            <Badge :variant="getStatusBadgeVariant(props.dataItem.status)">
                {{ props.dataItem.status_formatted || formatStatus(props.dataItem.status) }}
            </Badge>
        </template>
        <template v-else #body-cell-queue_date="{ props }">
            {{ props.dataItem.queue_date_formatted || formatDate(props.dataItem.queue_date) }}
        </template>
        <template #actions="{ row }">
            <div class="flex items-center gap-2">
                <button
                    v-if="store.viewMode === 'grouped'"
                    @click="openStudentDetailsModal(row)"
                    class="inline-flex items-center rounded-md border border-blue-200 bg-blue-50 px-3 py-1.5 text-sm font-medium text-blue-600 transition-colors duration-200 hover:bg-blue-100 hover:text-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1"
                    title="View Assessment History"
                >
                    <svg class="mr-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"
                        ></path>
                    </svg>
                    View History
                </button>
                <button
                    v-else
                    @click="openDetailsModal(row)"
                    class="inline-flex items-center rounded-md border border-blue-200 bg-blue-50 px-3 py-1.5 text-sm font-medium text-blue-600 transition-colors duration-200 hover:bg-blue-100 hover:text-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1"
                    title="View Details"
                >
                    <svg class="mr-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                        ></path>
                        <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                        ></path>
                    </svg>
                    View Details
                </button>
            </div>
        </template>

        <!-- View Mode Toggle -->
        <template #toolbar-left>
            <div class="flex items-center space-x-2">
                <label class="text-sm font-medium text-gray-700">View:</label>
                <div class="flex rounded-md shadow-sm">
                    <button
                        @click="store.setViewMode('grouped')"
                        :class="[
                            'px-3 py-2 text-sm font-medium rounded-l-md border',
                            store.viewMode === 'grouped'
                                ? 'bg-blue-600 text-white border-blue-600'
                                : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                        ]"
                    >
                        Grouped by Student
                    </button>
                    <button
                        @click="store.setViewMode('detailed')"
                        :class="[
                            'px-3 py-2 text-sm font-medium rounded-r-md border-t border-r border-b',
                            store.viewMode === 'detailed'
                                ? 'bg-blue-600 text-white border-blue-600'
                                : 'bg-white text-gray-700 border-gray-300 hover:bg-gray-50'
                        ]"
                    >
                        Individual Records
                    </button>
                </div>
            </div>
        </template>
    </AsyncGrid>

    <!-- Details Modal -->
    <SidebarDrawer
        :visibleDialog="isModalVisible"
        :hideOnOverlayClick="true"
        :fixedActionBar="false"
        :width="'60%'"
        @drawerclose="closeDetailsModal"
    >
        <template #title>
            <div class="text-lg font-medium">Risk Assessment Details</div>
        </template>
        <template #content>
            <div v-if="selectedRecord" class="space-y-6 p-6">
                <!-- Student Information -->
                <div class="rounded-lg bg-gray-50 p-4">
                    <h3 class="mb-3 text-lg font-semibold text-gray-900">Student Information</h3>
                    <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                        <div>
                            <label class="block text-sm font-medium text-gray-700"
                                >Student Name</label
                            >
                            <p class="mt-1 text-sm text-gray-900">
                                {{ selectedRecord.risk_assessment?.student?.name || '-' }}
                            </p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700"
                                >Student ID</label
                            >
                            <p class="mt-1 text-sm text-gray-900">
                                {{ selectedRecord.risk_assessment?.student?.student_id || '-' }}
                            </p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Course</label>
                            <p class="mt-1 text-sm text-gray-900">
                                {{ selectedRecord.risk_assessment?.course?.name || '-' }}
                            </p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Semester</label>
                            <p class="mt-1 text-sm text-gray-900">
                                {{ selectedRecord.semester?.semester_name || '-' }}
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Risk Assessment Details -->
                <div class="rounded-lg border bg-white p-4">
                    <h3 class="mb-3 text-lg font-semibold text-gray-900">
                        Risk Assessment Details
                    </h3>
                    <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                        <div>
                            <label class="block text-sm font-medium text-gray-700"
                                >Risk Category</label
                            >
                            <p class="mt-1">
                                <Badge
                                    :variant="getCategoryBadgeVariant(selectedRecord.risk_category)"
                                >
                                    {{ formatRiskCategory(selectedRecord.risk_category) }}
                                </Badge>
                            </p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700"
                                >Risk Level</label
                            >
                            <p class="mt-1">
                                <Badge :variant="getRiskTypeBadgeVariant(selectedRecord.risk_type)">
                                    {{
                                        selectedRecord.risk_type_name ||
                                        getRiskTypeName(selectedRecord.risk_type)
                                    }}
                                </Badge>
                            </p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Status</label>
                            <p class="mt-1">
                                <Badge :variant="getStatusBadgeVariant(selectedRecord.status)">
                                    {{
                                        selectedRecord.status_formatted ||
                                        formatStatus(selectedRecord.status)
                                    }}
                                </Badge>
                            </p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700"
                                >Queue Date</label
                            >
                            <p class="mt-1 text-sm text-gray-900">
                                {{
                                    selectedRecord.queue_date_formatted ||
                                    formatDate(selectedRecord.queue_date)
                                }}
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Additional Information -->
                <div class="rounded-lg border bg-white p-4">
                    <h3 class="mb-3 text-lg font-semibold text-gray-900">Additional Information</h3>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Remarks</label>
                            <p class="mt-1 text-sm text-gray-900">
                                {{ selectedRecord.remarks || 'No remarks available' }}
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Timestamps -->
                <div class="rounded-lg bg-gray-50 p-4">
                    <h3 class="mb-3 text-lg font-semibold text-gray-900">Record Information</h3>
                    <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Record ID</label>
                            <p class="mt-1 text-sm text-gray-900">{{ selectedRecord.id }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700"
                                >Created At</label
                            >
                            <p class="mt-1 text-sm text-gray-900">
                                {{ formatDate(selectedRecord.created_at) }}
                            </p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700"
                                >Updated At</label
                            >
                            <p class="mt-1 text-sm text-gray-900">
                                {{ formatDate(selectedRecord.updated_at) }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </template>
    </SidebarDrawer>

    <!-- Student Details Modal for Grouped View -->
    <StudentSemesterDetailsModal
        v-model="isStudentModalVisible"
        :student-data="selectedStudentData"
        @close="closeStudentDetailsModal"
    />
</template>

<script setup>
import AsyncGrid from '@spa/components/AsyncComponents/Grid/AsyncGrid.vue';
import FilterBlockWrapper from '@spa/components/AsyncComponents/Grid/Partials/FilterBlockWrapper.vue';
import RiskLevelSelect from './partials/RiskLevelSelect.vue';
import StatusSelect from './partials/StatusSelect.vue';
import SidebarDrawer from '@spa/components/KendoModals/SidebarDrawer.vue';
import StudentSemesterDetailsModal from './StudentSemesterDetailsModal.vue';
import { ref, onMounted, computed } from 'vue';
import { useStudentRiskAssessmentSemesterStore } from '@spa/stores/modules/student-risk-assessment-semester/useStudentRiskAssessmentSemesterStore.js';
import Badge from '@spa/components/badges/Badge.vue';

const store = useStudentRiskAssessmentSemesterStore();

// Modal state
const isModalVisible = ref(false);
const selectedRecord = ref(null);

// Student details modal state
const isStudentModalVisible = ref(false);
const selectedStudentData = ref(null);

// Dynamic columns based on view mode
const columns = computed(() => {
    if (store.viewMode === 'grouped') {
        // Grouped view columns (student summary)
        return [
            {
                field: 'student_number',
                name: 'student_number',
                title: 'Student ID',
                width: '120px',
                sortable: true,
                replace: true,
            },
            {
                field: 'student_name',
                name: 'student_name',
                title: 'Student Name',
                width: '200px',
                sortable: true,
                replace: true,
            },
            {
                field: 'course.display_name',
                name: 'course_name',
                title: 'Course',
                width: '250px',
                sortable: true,
                replace: true,
            },
            {
                field: 'assessment_summary.total_assessments',
                name: 'total_assessments',
                title: 'Total Assessments',
                width: '140px',
                sortable: true,
                replace: true,
            },
            {
                field: 'assessment_summary.current_risk_level',
                name: 'current_risk_level',
                title: 'Current Risk',
                width: '120px',
                sortable: true,
                replace: true,
            },
            {
                field: 'risk_trend.trend',
                name: 'risk_trend',
                title: 'Trend',
                width: '120px',
                sortable: true,
                replace: true,
            },
            {
                field: 'assessment_summary.latest_assessment_date',
                name: 'latest_assessment',
                title: 'Latest Assessment',
                width: '140px',
                sortable: true,
                replace: true,
            },
            {
                field: 'needs_attention',
                name: 'needs_attention',
                title: 'Status',
                width: '120px',
                sortable: true,
                replace: true,
            },
        ];
    } else {
        // Detailed view columns (individual records)
        return [
            {
                field: 'id',
                title: 'ID',
                width: '80px',
                sortable: true,
            },
            {
                field: 'risk_assessment.student.student_id',
                name: 'student_id',
                title: 'Student ID',
                width: '120px',
                sortable: true,
                replace: true,
            },
            {
                field: 'risk_assessment.student.name',
                name: 'student_name',
                title: 'Student Name',
                width: '200px',
                sortable: true,
                replace: true,
            },
            {
                field: 'risk_assessment.course.name',
                name: 'course_name',
                title: 'Course',
                width: '200px',
                sortable: true,
                replace: true,
                canHide: true,
            },
            {
                field: 'semester.semester_name',
                name: 'semester_name',
                title: 'Semester',
                width: '150px',
                sortable: true,
                replace: true,
            },
            {
                field: 'risk_type',
                name: 'risk_type',
                title: 'Risk Level',
                width: '120px',
                sortable: true,
                replace: true,
            },
            {
                field: 'status',
                name: 'status',
                title: 'Status',
                width: '120px',
                sortable: true,
                replace: true,
            },
            {
                field: 'queue_date',
                name: 'queue_date',
                title: 'Queue Date',
                width: '120px',
                sortable: true,
                replace: true,
            },
        ];
    }
});

const getCategoryBadgeVariant = (category) => {
    const mapping = {
        attendance: 'warning',
        result: 'info',
        payment: 'error',
        moodle_activity: 'success',
    };
    return mapping[category] || 'secondary';
};

const getRiskTypeBadgeVariant = (riskType) => {
    const mapping = {
        0: 'success', // Low/None
        1: 'warning', // Low
        2: 'error', // Medium
        3: 'critical', // High/Critical
    };
    return mapping[riskType] || 'secondary';
};

const getStatusBadgeVariant = (status) => {
    const mapping = {
        pending: 'warning',
        in_progress: 'info',
        completed: 'success',
        cancelled: 'error',
    };
    return mapping[status] || 'secondary';
};

const formatRiskCategory = (category) => {
    const mapping = {
        attendance: 'Attendance',
        result: 'Result',
        payment: 'Payment',
        moodle_activity: 'Moodle Activity',
    };
    return mapping[category] || category;
};

const getRiskTypeName = (riskType) => {
    const mapping = {
        0: 'None',
        1: 'Low',
        2: 'Medium',
        3: 'High',
    };
    return mapping[riskType] || 'Unknown';
};

const formatStatus = (status) => {
    return status.replace('_', ' ').replace(/\b\w/g, (l) => l.toUpperCase());
};

const formatDate = (date) => {
    if (!date) return '-';
    return new Date(date).toLocaleDateString('en-AU', {
        day: '2-digit',
        month: 'short',
        year: 'numeric',
    });
};

const initFilters = () => {
    store.filters = {
        risk_type: null,
        status: null,
    };
    // Reset pagination to page 1 when filters are reset
    store.serverPagination.page = 1;
};

// Modal functions
const openDetailsModal = (record) => {
    selectedRecord.value = record;
    isModalVisible.value = true;
};

const closeDetailsModal = () => {
    isModalVisible.value = false;
    selectedRecord.value = null;
};

// Student details modal functions
const openStudentDetailsModal = (studentData) => {
    selectedStudentData.value = studentData;
    isStudentModalVisible.value = true;
};

const closeStudentDetailsModal = () => {
    isStudentModalVisible.value = false;
    selectedStudentData.value = null;
};

// Risk trend styling
const getRiskTrendClass = (trend) => {
    const mapping = {
        improving: 'text-green-600',
        declining: 'text-red-600',
        stable: 'text-gray-600',
    };
    return mapping[trend] || 'text-gray-600';
};

onMounted(() => {
    console.log('🚀 Component mounted, initializing...');
    // Initialize with grouped view by default
    store.setViewMode('grouped');
    initFilters();
    console.log('📊 Current view mode:', store.viewMode);
    console.log('📋 Current columns:', columns.value);
});
</script>
