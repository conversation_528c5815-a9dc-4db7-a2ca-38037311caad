<template>
    <AsyncGrid
        :columns="columns"
        :store="store"
        :has-create-action="false"
        :has-export="false"
        :has-filters="true"
        :filter-columns="2"
        :gridStyle="{
            height: '100%',
        }"
        @handel-reset="
            () => {
                initFilters();
            }
        "
        :has-actions="true"
        :actions="[]"
        :has-floating-actions="false"
        :enable-selection="false"
        :has-bulk-delete="false"
        :has-bulk-export="false"
    >
        <template #filters>
            <FilterBlockWrapper label="Risk Level">
                <RiskLevelSelect v-model="store.filters.risk_type" />
            </FilterBlockWrapper>
            <FilterBlockWrapper label="Status">
                <StatusSelect v-model="store.filters.status" />
            </FilterBlockWrapper>
        </template>
        <template #body-cell-student_name="{ props }">
            {{ props.dataItem.risk_assessment?.student?.name || '-' }}
        </template>
        <template #body-cell-student_id="{ props }">
            {{ props.dataItem.risk_assessment?.student?.student_id || '-' }}
        </template>
        <template #body-cell-course_name="{ props }">
            {{ props.dataItem.risk_assessment?.course?.name || '-' }}
        </template>
        <template #body-cell-semester_name="{ props }">
            {{ props.dataItem.semester?.semester_name || '-' }}
        </template>
        <template #body-cell-risk_type="{ props }">
            <Badge :variant="getRiskTypeBadgeVariant(props.dataItem.risk_type)">
                {{ props.dataItem.risk_type_name || getRiskTypeName(props.dataItem.risk_type) }}
            </Badge>
        </template>
        <template #body-cell-status="{ props }">
            <Badge :variant="getStatusBadgeVariant(props.dataItem.status)">
                {{ props.dataItem.status_formatted || formatStatus(props.dataItem.status) }}
            </Badge>
        </template>
        <template #body-cell-queue_date="{ props }">
            {{ props.dataItem.queue_date_formatted || formatDate(props.dataItem.queue_date) }}
        </template>
        <template #actions="{ row }">
            <div class="flex items-center gap-2">
                <button
                    @click="openDetailsModal(row)"
                    class="inline-flex items-center rounded-md border border-blue-200 bg-blue-50 px-3 py-1.5 text-sm font-medium text-blue-600 transition-colors duration-200 hover:bg-blue-100 hover:text-blue-700 focus:outline-none focus:ring-2 focus:ring-blue-500 focus:ring-offset-1"
                    title="View Details"
                >
                    <svg class="mr-1 h-4 w-4" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"
                        ></path>
                        <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M2.458 12C3.732 7.943 7.523 5 12 5c4.478 0 8.268 2.943 9.542 7-1.274 4.057-5.064 7-9.542 7-4.477 0-8.268-2.943-9.542-7z"
                        ></path>
                    </svg>
                    View Details
                </button>
            </div>
        </template>
    </AsyncGrid>

    <!-- Details Modal -->
    <SidebarDrawer
        :visibleDialog="isModalVisible"
        :hideOnOverlayClick="true"
        :fixedActionBar="false"
        :width="'60%'"
        @drawerclose="closeDetailsModal"
    >
        <template #title>
            <div class="text-lg font-medium">Risk Assessment Details</div>
        </template>
        <template #content>
            <div v-if="selectedRecord" class="space-y-6 p-6">
                <!-- Student Information -->
                <div class="rounded-lg bg-gray-50 p-4">
                    <h3 class="mb-3 text-lg font-semibold text-gray-900">Student Information</h3>
                    <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                        <div>
                            <label class="block text-sm font-medium text-gray-700"
                                >Student Name</label
                            >
                            <p class="mt-1 text-sm text-gray-900">
                                {{ selectedRecord.risk_assessment?.student?.name || '-' }}
                            </p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700"
                                >Student ID</label
                            >
                            <p class="mt-1 text-sm text-gray-900">
                                {{ selectedRecord.risk_assessment?.student?.student_id || '-' }}
                            </p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Course</label>
                            <p class="mt-1 text-sm text-gray-900">
                                {{ selectedRecord.risk_assessment?.course?.name || '-' }}
                            </p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Semester</label>
                            <p class="mt-1 text-sm text-gray-900">
                                {{ selectedRecord.semester?.semester_name || '-' }}
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Risk Assessment Details -->
                <div class="rounded-lg border bg-white p-4">
                    <h3 class="mb-3 text-lg font-semibold text-gray-900">
                        Risk Assessment Details
                    </h3>
                    <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                        <div>
                            <label class="block text-sm font-medium text-gray-700"
                                >Risk Category</label
                            >
                            <p class="mt-1">
                                <Badge
                                    :variant="getCategoryBadgeVariant(selectedRecord.risk_category)"
                                >
                                    {{ formatRiskCategory(selectedRecord.risk_category) }}
                                </Badge>
                            </p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700"
                                >Risk Level</label
                            >
                            <p class="mt-1">
                                <Badge :variant="getRiskTypeBadgeVariant(selectedRecord.risk_type)">
                                    {{
                                        selectedRecord.risk_type_name ||
                                        getRiskTypeName(selectedRecord.risk_type)
                                    }}
                                </Badge>
                            </p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Status</label>
                            <p class="mt-1">
                                <Badge :variant="getStatusBadgeVariant(selectedRecord.status)">
                                    {{
                                        selectedRecord.status_formatted ||
                                        formatStatus(selectedRecord.status)
                                    }}
                                </Badge>
                            </p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700"
                                >Queue Date</label
                            >
                            <p class="mt-1 text-sm text-gray-900">
                                {{
                                    selectedRecord.queue_date_formatted ||
                                    formatDate(selectedRecord.queue_date)
                                }}
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Additional Information -->
                <div class="rounded-lg border bg-white p-4">
                    <h3 class="mb-3 text-lg font-semibold text-gray-900">Additional Information</h3>
                    <div class="space-y-4">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Remarks</label>
                            <p class="mt-1 text-sm text-gray-900">
                                {{ selectedRecord.remarks || 'No remarks available' }}
                            </p>
                        </div>
                    </div>
                </div>

                <!-- Timestamps -->
                <div class="rounded-lg bg-gray-50 p-4">
                    <h3 class="mb-3 text-lg font-semibold text-gray-900">Record Information</h3>
                    <div class="grid grid-cols-1 gap-4 md:grid-cols-2">
                        <div>
                            <label class="block text-sm font-medium text-gray-700">Record ID</label>
                            <p class="mt-1 text-sm text-gray-900">{{ selectedRecord.id }}</p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700"
                                >Created At</label
                            >
                            <p class="mt-1 text-sm text-gray-900">
                                {{ formatDate(selectedRecord.created_at) }}
                            </p>
                        </div>
                        <div>
                            <label class="block text-sm font-medium text-gray-700"
                                >Updated At</label
                            >
                            <p class="mt-1 text-sm text-gray-900">
                                {{ formatDate(selectedRecord.updated_at) }}
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        </template>
    </SidebarDrawer>
</template>

<script setup>
import AsyncGrid from '@spa/components/AsyncComponents/Grid/AsyncGrid.vue';
import FilterBlockWrapper from '@spa/components/AsyncComponents/Grid/Partials/FilterBlockWrapper.vue';
import RiskLevelSelect from './partials/RiskLevelSelect.vue';
import StatusSelect from './partials/StatusSelect.vue';
import SidebarDrawer from '@spa/components/KendoModals/SidebarDrawer.vue';
import { ref, onMounted } from 'vue';
import { useStudentRiskAssessmentSemesterStore } from '@spa/stores/modules/student-risk-assessment-semester/useStudentRiskAssessmentSemesterStore.js';
import Badge from '@spa/components/badges/Badge.vue';

const store = useStudentRiskAssessmentSemesterStore();

// Modal state
const isModalVisible = ref(false);
const selectedRecord = ref(null);

const columns = [
    {
        field: 'id',
        title: 'ID',
        width: '80px',
        sortable: true,
    },
    {
        field: 'risk_assessment.student.student_id',
        name: 'student_id',
        title: 'Student ID',
        width: '120px',
        sortable: true,
        replace: true,
    },
    {
        field: 'risk_assessment.student.name',
        name: 'student_name',
        title: 'Student Name',
        width: '200px',
        sortable: true,
        replace: true,
    },
    {
        field: 'risk_assessment.course.name',
        name: 'course_name',
        title: 'Course',
        width: '200px',
        sortable: true,
        replace: true,
        canHide: true,
    },
    {
        field: 'semester.semester_name',
        name: 'semester_name',
        title: 'Semester',
        width: '150px',
        sortable: true,
        replace: true,
    },
    {
        field: 'risk_type',
        name: 'risk_type',
        title: 'Risk Level',
        width: '120px',
        sortable: true,
        replace: true,
    },
    {
        field: 'status',
        name: 'status',
        title: 'Status',
        width: '120px',
        sortable: true,
        replace: true,
    },
    {
        field: 'queue_date',
        name: 'queue_date',
        title: 'Queue Date',
        width: '120px',
        sortable: true,
        replace: true,
    },
];

const getCategoryBadgeVariant = (category) => {
    const mapping = {
        attendance: 'warning',
        result: 'info',
        payment: 'error',
        moodle_activity: 'success',
    };
    return mapping[category] || 'secondary';
};

const getRiskTypeBadgeVariant = (riskType) => {
    const mapping = {
        0: 'success', // Low/None
        1: 'warning', // Low
        2: 'error', // Medium
        3: 'critical', // High/Critical
    };
    return mapping[riskType] || 'secondary';
};

const getStatusBadgeVariant = (status) => {
    const mapping = {
        pending: 'warning',
        in_progress: 'info',
        completed: 'success',
        cancelled: 'error',
    };
    return mapping[status] || 'secondary';
};

const formatRiskCategory = (category) => {
    const mapping = {
        attendance: 'Attendance',
        result: 'Result',
        payment: 'Payment',
        moodle_activity: 'Moodle Activity',
    };
    return mapping[category] || category;
};

const getRiskTypeName = (riskType) => {
    const mapping = {
        0: 'None',
        1: 'Low',
        2: 'Medium',
        3: 'High',
    };
    return mapping[riskType] || 'Unknown';
};

const formatStatus = (status) => {
    return status.replace('_', ' ').replace(/\b\w/g, (l) => l.toUpperCase());
};

const formatDate = (date) => {
    if (!date) return '-';
    return new Date(date).toLocaleDateString('en-AU', {
        day: '2-digit',
        month: 'short',
        year: 'numeric',
    });
};

const initFilters = () => {
    store.filters = {
        risk_type: null,
        status: null,
    };
    // Reset pagination to page 1 when filters are reset
    store.serverPagination.page = 1;
};

// Modal functions
const openDetailsModal = (record) => {
    selectedRecord.value = record;
    isModalVisible.value = true;
};

const closeDetailsModal = () => {
    isModalVisible.value = false;
    selectedRecord.value = null;
};

onMounted(() => {
    initFilters();
});
</script>
