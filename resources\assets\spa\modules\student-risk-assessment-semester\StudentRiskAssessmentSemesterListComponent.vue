<template>
    <AsyncGrid
        :columns="columns"
        :store="store"
        :has-create-action="false"
        :has-export="true"
        :has-filters="true"
        :filter-columns="4"
        :gridStyle="{
            height: '100%',
        }"
        @handel-reset="
            () => {
                initFilters();
            }
        "
        :has-actions="false"
        :has-floating-actions="false"
        :enable-selection="false"
        :has-bulk-delete="false"
        :has-bulk-export="false"
    >
        <template #filters>
            <FilterBlockWrapper label="Student">
                <input
                    type="text"
                    v-model="store.filters.student_search"
                    v-debounce="300"
                    class="tw-input-text"
                    placeholder="Search by student name or ID"
                />
            </FilterBlockWrapper>
            <FilterBlockWrapper label="Course">
                <CourseSelect v-model="store.filters.course_id" />
            </FilterBlockWrapper>
            <FilterBlockWrapper label="Risk Level">
                <RiskLevelSelect v-model="store.filters.risk_type" />
            </FilterBlockWrapper>
            <FilterBlockWrapper label="Status">
                <StatusSelect v-model="store.filters.status" />
            </FilterBlockWrapper>
        </template>
        <template #body-cell-student_name="{ props }">
            {{ props.dataItem.risk_assessment?.student?.name || '-' }}
        </template>
        <template #body-cell-student_id="{ props }">
            {{ props.dataItem.risk_assessment?.student?.student_id || '-' }}
        </template>
        <template #body-cell-course_name="{ props }">
            {{ props.dataItem.risk_assessment?.course?.name || '-' }}
        </template>
        <template #body-cell-semester_name="{ props }">
            {{ props.dataItem.semester?.semester_name || '-' }}
        </template>
        <template #body-cell-risk_category="{ props }">
            <Badge :variant="getCategoryBadgeVariant(props.dataItem.risk_category)">
                {{ formatRiskCategory(props.dataItem.risk_category) }}
            </Badge>
        </template>
        <template #body-cell-risk_type="{ props }">
            <Badge :variant="getRiskTypeBadgeVariant(props.dataItem.risk_type)">
                {{ props.dataItem.risk_type_name || getRiskTypeName(props.dataItem.risk_type) }}
            </Badge>
        </template>
        <template #body-cell-status="{ props }">
            <Badge :variant="getStatusBadgeVariant(props.dataItem.status)">
                {{ props.dataItem.status_formatted || formatStatus(props.dataItem.status) }}
            </Badge>
        </template>
        <template #body-cell-queue_date="{ props }">
            {{ props.dataItem.queue_date_formatted || formatDate(props.dataItem.queue_date) }}
        </template>
        <template #body-cell-is_overdue="{ props }">
            <Badge v-if="props.dataItem.is_overdue" variant="error">
                Overdue
            </Badge>
            <Badge v-else-if="props.dataItem.is_due_today" variant="warning">
                Due Today
            </Badge>
            <span v-else>-</span>
        </template>
    </AsyncGrid>
</template>

<script setup>
import AsyncGrid from '@spa/components/AsyncComponents/Grid/AsyncGrid.vue';
import FilterBlockWrapper from '@spa/components/AsyncComponents/Grid/Partials/FilterBlockWrapper.vue';
import CourseSelect from './partials/CourseSelect.vue';
import RiskLevelSelect from './partials/RiskLevelSelect.vue';
import StatusSelect from './partials/StatusSelect.vue';
import { ref, onMounted } from 'vue';
import { useStudentRiskAssessmentSemesterStore } from '@spa/stores/modules/student-risk-assessment-semester/useStudentRiskAssessmentSemesterStore.js';
import Badge from '@spa/components/badges/Badge.vue';

const store = useStudentRiskAssessmentSemesterStore();

const columns = [
    {
        field: 'id',
        title: 'ID',
        width: '80px',
        sortable: true,
    },
    {
        field: 'risk_assessment.student.name',
        name: 'student_name',
        title: 'Student Name',
        width: '200px',
        sortable: true,
        replace: true,
    },
    {
        field: 'risk_assessment.course.name',
        name: 'course_name',
        title: 'Course',
        width: '200px',
        sortable: true,
        replace: true,
        canHide: true,
    },
    {
        field: 'semester.semester_name',
        name: 'semester_name',
        title: 'Semester',
        width: '150px',
        sortable: true,
        replace: true,
    },
    {
        field: 'risk_category',
        name: 'risk_category',
        title: 'Risk Category',
        width: '150px',
        sortable: true,
        replace: true,
    },
    {
        field: 'risk_type',
        name: 'risk_type',
        title: 'Risk Level',
        width: '120px',
        sortable: true,
        replace: true,
    },
    {
        field: 'status',
        name: 'status',
        title: 'Status',
        width: '120px',
        sortable: true,
        replace: true,
    },
    {
        field: 'queue_date',
        name: 'queue_date',
        title: 'Queue Date',
        width: '120px',
        sortable: true,
        replace: true,
    },
    {
        field: 'is_overdue',
        name: 'is_overdue',
        title: 'Due Status',
        width: '120px',
        replace: true,
        canHide: true,
    },
    {
        field: 'remarks',
        title: 'Remarks',
        width: '200px',
        canHide: true,
    },
];

const getCategoryBadgeVariant = (category) => {
    const mapping = {
        'attendance': 'warning',
        'result': 'info',
        'payment': 'error',
        'moodle_activity': 'success',
    };
    return mapping[category] || 'secondary';
};

const getRiskTypeBadgeVariant = (riskType) => {
    const mapping = {
        0: 'success',    // Low/None
        1: 'warning',    // Low
        2: 'error',      // Medium
        3: 'critical',   // High/Critical
    };
    return mapping[riskType] || 'secondary';
};

const getStatusBadgeVariant = (status) => {
    const mapping = {
        'pending': 'warning',
        'in_progress': 'info',
        'completed': 'success',
        'cancelled': 'error',
    };
    return mapping[status] || 'secondary';
};

const formatRiskCategory = (category) => {
    const mapping = {
        'attendance': 'Attendance',
        'result': 'Result',
        'payment': 'Payment',
        'moodle_activity': 'Moodle Activity',
    };
    return mapping[category] || category;
};

const getRiskTypeName = (riskType) => {
    const mapping = {
        0: 'None',
        1: 'Low',
        2: 'Medium',
        3: 'High',
    };
    return mapping[riskType] || 'Unknown';
};

const formatStatus = (status) => {
    return status.replace('_', ' ').replace(/\b\w/g, l => l.toUpperCase());
};

const formatDate = (date) => {
    if (!date) return '-';
    return new Date(date).toLocaleDateString('en-AU', {
        day: '2-digit',
        month: 'short',
        year: 'numeric'
    });
};

const initFilters = () => {
    store.filters = {
        student_search: null,
        course_id: null,
        risk_type: [],
        status: [],
    };
};

onMounted(() => {
    initFilters();
});
</script>
