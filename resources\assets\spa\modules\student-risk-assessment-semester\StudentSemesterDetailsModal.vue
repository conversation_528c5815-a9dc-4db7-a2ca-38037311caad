<template>
    <SidebarDrawer
        v-model="isVisible"
        :title="modalTitle"
        :loading="store.loadingDetails"
        @close="handleClose"
    >
        <template #content>
            <div v-if="store.loadingDetails" class="flex items-center justify-center py-8">
                <div class="text-center">
                    <div class="animate-spin rounded-full h-8 w-8 border-b-2 border-blue-600 mx-auto"></div>
                    <p class="mt-2 text-sm text-gray-600">Loading student details...</p>
                </div>
            </div>

            <div v-else-if="store.studentDetails.length === 0" class="text-center py-8">
                <div class="text-gray-400 mb-2">
                    <svg class="mx-auto h-12 w-12" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                        <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12h6m-6 4h6m2 5H7a2 2 0 01-2-2V5a2 2 0 012-2h5.586a1 1 0 01.707.293l5.414 5.414a1 1 0 01.293.707V19a2 2 0 01-2 2z"></path>
                    </svg>
                </div>
                <h3 class="text-lg font-medium text-gray-900 mb-1">No Assessment Records</h3>
                <p class="text-sm text-gray-600">No semester assessment records found for this student.</p>
            </div>

            <div v-else class="space-y-4">
                <!-- Student Summary Header -->
                <div class="bg-gray-50 rounded-lg p-4 mb-6">
                    <h3 class="text-lg font-semibold text-gray-900 mb-2">Assessment History</h3>
                    <div class="grid grid-cols-2 gap-4 text-sm">
                        <div>
                            <span class="text-gray-600">Total Assessments:</span>
                            <span class="font-medium ml-2">{{ store.studentDetails.length }}</span>
                        </div>
                        <div>
                            <span class="text-gray-600">Latest Assessment:</span>
                            <span class="font-medium ml-2">{{ getLatestAssessmentDate() }}</span>
                        </div>
                    </div>
                </div>

                <!-- Assessment Records -->
                <div class="space-y-3">
                    <div
                        v-for="(assessment, index) in store.studentDetails"
                        :key="assessment.id"
                        class="border border-gray-200 rounded-lg p-4 hover:shadow-sm transition-shadow"
                    >
                        <div class="flex items-start justify-between mb-3">
                            <div class="flex-1">
                                <div class="flex items-center gap-3 mb-2">
                                    <Badge :variant="getRiskTypeBadgeVariant(assessment.risk_type)">
                                        {{ assessment.risk_type_name }}
                                    </Badge>
                                    <Badge :variant="getStatusBadgeVariant(assessment.status)">
                                        {{ assessment.status_formatted }}
                                    </Badge>
                                </div>
                                <div class="text-sm text-gray-600 space-y-1">
                                    <div>
                                        <span class="font-medium">Semester:</span>
                                        {{ assessment.semester?.semester_name || '-' }}
                                    </div>
                                    <div>
                                        <span class="font-medium">Queue Date:</span>
                                        {{ assessment.queue_date_formatted || '-' }}
                                    </div>
                                    <div>
                                        <span class="font-medium">Category:</span>
                                        {{ formatCategory(assessment.risk_category) }}
                                    </div>
                                </div>
                            </div>
                            <div class="text-xs text-gray-500">
                                #{{ index + 1 }}
                            </div>
                        </div>

                        <!-- Remarks -->
                        <div v-if="assessment.remarks" class="mt-3 p-3 bg-gray-50 rounded text-sm">
                            <span class="font-medium text-gray-700">Remarks:</span>
                            <p class="mt-1 text-gray-600">{{ assessment.remarks }}</p>
                        </div>

                        <!-- Assessment Data -->
                        <div v-if="assessment.data && Object.keys(assessment.data).length > 0" class="mt-3">
                            <details class="group">
                                <summary class="cursor-pointer text-sm font-medium text-blue-600 hover:text-blue-700">
                                    View Assessment Data
                                </summary>
                                <div class="mt-2 p-3 bg-blue-50 rounded text-xs">
                                    <pre class="whitespace-pre-wrap text-gray-700">{{ JSON.stringify(assessment.data, null, 2) }}</pre>
                                </div>
                            </details>
                        </div>
                    </div>
                </div>
            </div>
        </template>

        <template #footer>
            <div class="flex justify-end space-x-3">
                <button
                    @click="handleClose"
                    class="px-4 py-2 text-sm font-medium text-gray-700 bg-white border border-gray-300 rounded-md hover:bg-gray-50 focus:outline-none focus:ring-2 focus:ring-offset-2 focus:ring-blue-500"
                >
                    Close
                </button>
            </div>
        </template>
    </SidebarDrawer>
</template>

<script setup>
import { computed, watch } from 'vue';
import SidebarDrawer from '@spa/components/KendoModals/SidebarDrawer.vue';
import Badge from '@spa/components/badges/Badge.vue';
import { useStudentRiskAssessmentSemesterStore } from '@spa/stores/modules/student-risk-assessment-semester/useStudentRiskAssessmentSemesterStore.js';

const props = defineProps({
    modelValue: {
        type: Boolean,
        default: false,
    },
    studentData: {
        type: Object,
        default: null,
    },
});

const emit = defineEmits(['update:modelValue', 'close']);

const store = useStudentRiskAssessmentSemesterStore();

const isVisible = computed({
    get: () => props.modelValue,
    set: (value) => emit('update:modelValue', value),
});

const modalTitle = computed(() => {
    if (props.studentData) {
        return `Assessment History - ${props.studentData.student_name}`;
    }
    return 'Student Assessment History';
});

// Watch for modal opening to fetch data
watch(() => props.modelValue, async (newValue) => {
    if (newValue && props.studentData?.student_id) {
        try {
            await store.fetchStudentDetails(props.studentData.student_id);
        } catch (error) {
            console.error('Failed to load student details:', error);
        }
    }
});

const handleClose = () => {
    emit('close');
    store.clearStudentDetails();
};

const getLatestAssessmentDate = () => {
    if (store.studentDetails.length === 0) return '-';
    const latest = store.studentDetails[0]; // Already sorted by created_at desc
    return latest.created_at ? new Date(latest.created_at).toLocaleDateString() : '-';
};

const getRiskTypeBadgeVariant = (riskType) => {
    const mapping = {
        0: 'success',
        1: 'warning',
        2: 'error',
        3: 'critical',
    };
    return mapping[riskType] || 'secondary';
};

const getStatusBadgeVariant = (status) => {
    const mapping = {
        pending: 'warning',
        in_progress: 'info',
        completed: 'success',
        cancelled: 'error',
    };
    return mapping[status] || 'secondary';
};

const formatCategory = (category) => {
    if (!category) return '-';
    return category.replace(/_/g, ' ').replace(/\b\w/g, l => l.toUpperCase());
};
</script>

<style scoped>
details[open] summary {
    margin-bottom: 0.5rem;
}
</style>
