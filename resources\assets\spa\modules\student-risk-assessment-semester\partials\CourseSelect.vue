<script setup>
import { computed, onMounted, ref } from 'vue';
import { DropDownList } from '@progress/kendo-vue-dropdowns';

const props = defineProps({
    modelValue: [String, Number, Array, Object],
    disabled: Boolean,
    clearable: {
        type: Boolean,
        default: true,
    },
    placeholder: {
        type: String,
        default: 'Select Course',
    },
});

const emit = defineEmits(['update:modelValue']);

const vModel = computed({
    get() {
        return props.modelValue;
    },
    set(val) {
        emit('update:modelValue', val);
    },
});

const courses = ref([]);
const loading = ref(false);

const fetchCourses = async () => {
    loading.value = true;
    try {
        const response = await fetch('/api/get-courses-list-for-filter?activeCourse=1');
        const data = await response.json();
        if (data.success && data.data) {
            courses.value = data.data.map(course => ({
                id: course.value,
                name: course.label
            }));
        } else {
            courses.value = [];
        }
    } catch (error) {
        console.error('Error fetching courses:', error);
        courses.value = [];
    } finally {
        loading.value = false;
    }
};

onMounted(() => {
    fetchCourses();
});
</script>

<template>
    <DropDownList
        v-model="vModel"
        :data-items="courses"
        :text-field="'name'"
        :value-field="'id'"
        :data-item-key="'id'"
        :value-primitive="true"
        :default-item="{ name: placeholder, id: null }"
        :filterable="true"
        :clear-button="clearable"
        :disabled="disabled || loading"
        :loading="loading"
        class="w-full"
    />
</template>

<style scoped></style>
