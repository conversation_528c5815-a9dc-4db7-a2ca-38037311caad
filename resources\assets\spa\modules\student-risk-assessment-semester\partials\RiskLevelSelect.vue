<script setup>
import { computed } from 'vue';
import { DropDownList } from '@progress/kendo-vue-dropdowns';

const props = defineProps({
    modelValue: [String, Number],
    disabled: Boolean,
    clearable: {
        type: Boolean,
        default: true,
    },
    placeholder: {
        type: String,
        default: 'Select Risk Level',
    },
});

const emit = defineEmits(['update:modelValue']);

const vModel = computed({
    get() {
        return props.modelValue;
    },
    set(val) {
        emit('update:modelValue', val);
    },
});

const riskLevelOptions = [
    { id: 0, name: 'None' },
    { id: 1, name: 'Low' },
    { id: 2, name: 'Medium' },
    { id: 3, name: 'High' },
];
</script>

<template>
    <DropDownList
        v-model="vModel"
        :data-items="riskLevelOptions"
        :text-field="'name'"
        :value-field="'id'"
        :data-item-key="'id'"
        :value-primitive="true"
        :default-item="{ name: placeholder, id: null }"
        :filterable="true"
        :clear-button="clearable"
        :disabled="disabled"
        class="w-full"
    />
</template>

<style scoped></style>
