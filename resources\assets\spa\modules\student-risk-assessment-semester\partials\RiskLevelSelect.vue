<script setup>
import { computed } from 'vue';
import DropdownMultiSelect from '@spa/components/Dropdown/DropdownMultiSelect.vue';

const props = defineProps({
    modelValue: [Array],
    disabled: Boolean,
    clearable: {
        type: Boolean,
        default: true,
    },
    placeholder: {
        type: String,
        default: 'Select Risk Levels',
    },
});

const emit = defineEmits(['update:modelValue']);

const vModel = computed({
    get() {
        return props.modelValue || [];
    },
    set(val) {
        emit('update:modelValue', val);
    },
});

const riskLevelOptions = [
    { id: 0, name: 'None' },
    { id: 1, name: 'Low' },
    { id: 2, name: 'Medium' },
    { id: 3, name: 'High' },
];
</script>

<template>
    <DropdownMultiSelect
        v-model="vModel"
        :data-items="riskLevelOptions"
        :text-field="'name'"
        :value-field="'id'"
        :data-item-key="'id'"
        :value-primitive="true"
        :filterable="true"
        :clear-button="clearable"
        :disabled="disabled"
        :placeholder="placeholder"
        :style="{
            width: '100%',
            minWidth: '200px',
        }"
    />
</template>

<style scoped></style>
