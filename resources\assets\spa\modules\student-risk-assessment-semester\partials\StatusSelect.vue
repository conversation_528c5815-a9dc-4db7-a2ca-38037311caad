<script setup>
import { computed } from 'vue';
import { DropDownList } from '@progress/kendo-vue-dropdowns';

const props = defineProps({
    modelValue: [String],
    disabled: Boolean,
    clearable: {
        type: Boolean,
        default: true,
    },
    placeholder: {
        type: String,
        default: 'Select Status',
    },
});

const emit = defineEmits(['update:modelValue']);

const vModel = computed({
    get() {
        return props.modelValue;
    },
    set(val) {
        emit('update:modelValue', val);
    },
});

const statusOptions = [
    { id: 'pending', name: 'Pending' },
    { id: 'in_progress', name: 'In Progress' },
    { id: 'completed', name: 'Completed' },
    { id: 'cancelled', name: 'Cancelled' },
];
</script>

<template>
    <DropDownList
        v-model="vModel"
        :data-items="statusOptions"
        :text-field="'name'"
        :value-field="'id'"
        :data-item-key="'id'"
        :value-primitive="true"
        :default-item="{ name: placeholder, id: null }"
        :filterable="true"
        :clear-button="clearable"
        :disabled="disabled"
        class="w-full"
    />
</template>

<style scoped></style>
