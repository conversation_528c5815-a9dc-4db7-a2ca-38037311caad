<template>
    <div class="space-y-6">
        <Card :pt="{ root: 'relative p-4 lg:p-6 rounded-lg' }">
            <template #content>
                <StudentRiskAssessmentSettingsForm />
            </template>
        </Card>
    </div>
</template>

<script setup>
import Card from '@spa/components/Card/Card.vue';
import StudentRiskAssessmentSettingsForm from '@spa/modules/student-risk-assessment-settings/StudentRiskAssessmentSettingsForm.vue';
</script>

<style scoped></style>
