<template>
    <div class="mx-auto max-w-6xl p-6">
        <div class="mb-8">
            <h1 class="mb-2 text-2xl font-bold text-gray-900">Student Risk Assessment Settings</h1>
            <p class="text-gray-600">
                Configure risk assessment parameters and thresholds for student monitoring.
            </p>
        </div>
        <Form :key="formKey" :initial-values="store.formData" @submit="handleSubmit" :ref="formRef">
            <FormElement :id="uniqueId" novalidate>
                <fieldset class="space-y-6">
                    <StudentRiskAssessmentSettingsFormContent />

                    <div class="flex items-center justify-end gap-4 border-t border-gray-200 pt-6">
                        <Button
                            size="base"
                            type="button"
                            variant="secondary"
                            class="min-w-[100px]"
                            @click="handleCancel"
                        >
                            <span>Cancel</span>
                        </Button>
                        <Button
                            size="base"
                            type="submit"
                            variant="primary"
                            class="min-w-[100px]"
                            :disabled="store.loading"
                        >
                            <span>{{ store.loading ? 'Saving...' : 'Save Settings' }}</span>
                        </Button>
                    </div>
                </fieldset>
            </FormElement>
        </Form>
    </div>
</template>

<script setup>
import { ref, onMounted, watch } from 'vue';
import { useStudentRiskAssessmentSettingsStore } from '@spa/stores/modules/student-risk-assessment-settings/studentRiskAssessmentSettingsStore.js';
import { Form, FormElement } from '@progress/kendo-vue-form';
import Button from '@spa/components/Buttons/Button.vue';
import StudentRiskAssessmentSettingsFormContent from '@spa/modules/student-risk-assessment-settings/partials/StudentRiskAssessmentSettingsFormContent.vue';
import { uuid } from '@spa/helpers/index.js';

const store = useStudentRiskAssessmentSettingsStore();
const formRef = ref(null);
const uniqueId = ref('form_id_' + uuid() + Date.now());
const formKey = ref(0);

// Watch for changes in store data and force form re-render
watch(
    () => store.isLoading,
    (newValue, oldValue) => {
        if (oldValue && !newValue) {
            // Data has finished loading, force form re-render
            formKey.value++;
        }
    }
);

const handleSubmit = async (e) => {
    try {
        // Update store with current form values from the Kendo Form
        // The form event 'e' contains the current form field values
        if (e && typeof e === 'object') {
            // Merge form values into store, but preserve store values for fields not in form
            Object.keys(e).forEach((key) => {
                if (e[key] !== undefined) {
                    store.formData[key] = e[key];
                }
            });
        }

        await store.submitFormData();
    } catch (error) {
        console.error('Failed to save settings:', error);
    }
};

const handleCancel = () => {
    // Handle cancel action - maybe reset form or navigate away
    store.initialize();
};

// Initialize store when component mounts
onMounted(async () => {
    await store.initialize();
});
</script>

<style scoped></style>
