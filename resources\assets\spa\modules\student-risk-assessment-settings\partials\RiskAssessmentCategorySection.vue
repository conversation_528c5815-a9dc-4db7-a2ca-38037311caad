<template>
    <div class="mb-8 overflow-hidden rounded-2xl border border-gray-200 bg-gray-50">
        <div class="border-b border-gray-200 bg-gray-100 px-8 py-5">
            <h2 class="flex items-center text-xl font-semibold text-gray-700">
                <svg
                    class="mr-3 h-6 w-6 text-gray-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                >
                    <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M19 11H5m14 0a2 2 0 012 2v6a2 2 0 01-2 2H5a2 2 0 01-2-2v-6a2 2 0 012-2m14 0V9a2 2 0 00-2-2M5 11V9a2 2 0 012-2m0 0V5a2 2 0 012-2h6a2 2 0 012 2v2M7 7h10"
                    ></path>
                </svg>
                Risk Assessment Category
            </h2>
        </div>
        <div class="space-y-8 p-8">
            <!-- Student Attendance Subsection -->
            <StudentAttendanceSubsection />

            <!-- Moodle Access Subsection -->
            <MoodleAccessSubsection />

            <!-- Student Results Subsection -->
            <StudentResultsSubsection />

            <!-- Student Fee Payment Subsection -->
            <StudentFeePaymentSubsection />
        </div>
    </div>
</template>

<script setup>
import StudentAttendanceSubsection from './subsections/StudentAttendanceSubsection.vue';
import MoodleAccessSubsection from './subsections/MoodleAccessSubsection.vue';
import StudentResultsSubsection from './subsections/StudentResultsSubsection.vue';
import StudentFeePaymentSubsection from './subsections/StudentFeePaymentSubsection.vue';
</script>

<style scoped></style>
