<template>
    <div class="mb-8 overflow-hidden rounded-2xl border border-gray-200 bg-gray-50">
        <div class="border-b border-gray-200 bg-gray-100 px-8 py-5">
            <h2 class="flex items-center text-xl font-semibold text-gray-700">
                <svg
                    class="mr-3 h-6 w-6 text-gray-600"
                    fill="none"
                    stroke="currentColor"
                    viewBox="0 0 24 24"
                >
                    <path
                        stroke-linecap="round"
                        stroke-linejoin="round"
                        stroke-width="2"
                        d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                    ></path>
                </svg>
                Risk Assessment Type
            </h2>
        </div>
        <div class="p-8">
            <div class="grid grid-cols-1 gap-6 md:grid-cols-3">
                <!-- Low Risk Card -->
                <div
                    class="rounded-xl border-2 border-green-500 bg-white p-6 shadow-sm transition-transform hover:-translate-y-1"
                >
                    <div class="mb-5 flex items-center text-lg font-semibold">
                        <div class="mr-3 h-3 w-3 rounded-full bg-green-500"></div>
                        Low Risk
                    </div>
                    <div class="space-y-4">
                        <div>
                            <label class="mb-2 block font-medium text-gray-700"
                                >Weeks Configuration:</label
                            >
                            <Field
                                :id="'category_low_risk_weeks'"
                                :name="'category_low_risk_weeks'"
                                :component="'numberTemplate'"
                                :validator="requiredtrue"
                            >
                                <template #numberTemplate="{ props }">
                                    <FormNumericTextBox
                                        v-bind="props"
                                        @change="props.onChange"
                                        @blur="props.onBlur"
                                        @focus="props.onFocus"
                                        :min="1"
                                        :max="52"
                                        :decimals="0"
                                        :format="'n0'"
                                    />
                                </template>
                            </Field>
                        </div>
                        <div>
                            <label class="mb-2 block font-medium text-gray-700"
                                >Email Template:</label
                            >
                            <Field
                                :id="'category_low_risk_email'"
                                :name="'category_low_risk_email'"
                                :component="'dropdownTemplate'"
                                :data-items="store.emailTemplates ?? []"
                                :text-field="'text'"
                                :data-item-key="'value'"
                                :value-field="'value'"
                                :value-primitive="true"
                                :default-item="{
                                    text: 'Select Email Template',
                                    value: null,
                                }"
                            >
                                <template #dropdownTemplate="{ props }">
                                    <FormDropDown
                                        v-bind="props"
                                        @change="props.onChange"
                                        @blur="props.onBlur"
                                        @focus="props.onFocus"
                                    />
                                </template>
                            </Field>
                        </div>
                    </div>
                </div>

                <!-- Medium Risk Card -->
                <div
                    class="rounded-xl border-2 border-amber-500 bg-white p-6 shadow-sm transition-transform hover:-translate-y-1"
                >
                    <div class="mb-5 flex items-center text-lg font-semibold">
                        <div class="mr-3 h-3 w-3 rounded-full bg-amber-500"></div>
                        Medium Risk
                    </div>
                    <div class="space-y-4">
                        <div>
                            <label class="mb-2 block font-medium text-gray-700"
                                >Weeks Configuration:</label
                            >
                            <Field
                                :id="'category_medium_risk_weeks'"
                                :name="'category_medium_risk_weeks'"
                                :component="'numberTemplate'"
                                :validator="requiredtrue"
                            >
                                <template #numberTemplate="{ props }">
                                    <FormNumericTextBox
                                        v-bind="props"
                                        @change="props.onChange"
                                        @blur="props.onBlur"
                                        @focus="props.onFocus"
                                        :min="1"
                                        :max="52"
                                        :decimals="0"
                                        :format="'n0'"
                                    />
                                </template>
                            </Field>
                        </div>
                        <div>
                            <label class="mb-2 block font-medium text-gray-700"
                                >Email Template:</label
                            >
                            <Field
                                :id="'category_medium_risk_email'"
                                :name="'category_medium_risk_email'"
                                :component="'dropdownTemplate'"
                                :data-items="store.emailTemplates ?? []"
                                :text-field="'text'"
                                :data-item-key="'value'"
                                :value-field="'value'"
                                :value-primitive="true"
                                :default-item="{
                                    text: 'Select Email Template',
                                    value: null,
                                }"
                            >
                                <template #dropdownTemplate="{ props }">
                                    <FormDropDown
                                        v-bind="props"
                                        @change="props.onChange"
                                        @blur="props.onBlur"
                                        @focus="props.onFocus"
                                    />
                                </template>
                            </Field>
                        </div>
                    </div>
                </div>

                <!-- High Risk Card -->
                <div
                    class="rounded-xl border-2 border-red-500 bg-white p-6 shadow-sm transition-transform hover:-translate-y-1"
                >
                    <div class="mb-5 flex items-center text-lg font-semibold">
                        <div class="mr-3 h-3 w-3 rounded-full bg-red-500"></div>
                        High Risk
                    </div>
                    <div class="space-y-4">
                        <div>
                            <label class="mb-2 block font-medium text-gray-700"
                                >Weeks Configuration:</label
                            >
                            <Field
                                :id="'category_high_risk_weeks'"
                                :name="'category_high_risk_weeks'"
                                :component="'numberTemplate'"
                                :validator="requiredtrue"
                            >
                                <template #numberTemplate="{ props }">
                                    <FormNumericTextBox
                                        v-bind="props"
                                        @change="props.onChange"
                                        @blur="props.onBlur"
                                        @focus="props.onFocus"
                                        :min="1"
                                        :max="52"
                                        :decimals="0"
                                        :format="'n0'"
                                    />
                                </template>
                            </Field>
                        </div>
                        <div>
                            <label class="mb-2 block font-medium text-gray-700"
                                >Email Template:</label
                            >
                            <Field
                                :id="'category_high_risk_email'"
                                :name="'category_high_risk_email'"
                                :component="'dropdownTemplate'"
                                :data-items="store.emailTemplates ?? []"
                                :text-field="'text'"
                                :data-item-key="'value'"
                                :value-field="'value'"
                                :value-primitive="true"
                                :default-item="{
                                    text: 'Select Email Template',
                                    value: null,
                                }"
                            >
                                <template #dropdownTemplate="{ props }">
                                    <FormDropDown
                                        v-bind="props"
                                        @change="props.onChange"
                                        @blur="props.onBlur"
                                        @focus="props.onFocus"
                                    />
                                </template>
                            </Field>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { Field } from '@progress/kendo-vue-form';
import FormDropDown from '@spa/components/KendoInputs/FormDropDown.vue';
import FormNumericTextBox from '@spa/components/KendoInputs/FormNumericTextBox.vue';
import { requiredtrue } from '@spa/services/validators/kendoCommonValidator.js';
import { useStudentRiskAssessmentSettingsStore } from '@spa/stores/modules/student-risk-assessment-settings/studentRiskAssessmentSettingsStore.js';

const store = useStudentRiskAssessmentSettingsStore();
</script>

<style scoped></style>
