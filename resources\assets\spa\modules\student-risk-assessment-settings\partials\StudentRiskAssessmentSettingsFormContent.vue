<template>
    <div class="space-y-8">
        <!-- Enable/Disable Toggle -->
        <div class="overflow-hidden rounded-2xl border border-gray-200 bg-gray-50">
            <div class="border-b border-gray-200 bg-gray-100 px-8 py-5">
                <h2 class="flex items-center text-xl font-semibold text-gray-700">
                    <svg
                        class="mr-3 h-6 w-6 text-gray-600"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                    >
                        <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"
                        ></path>
                    </svg>
                    Risk Assessment Settings
                </h2>
            </div>
            <div class="p-8">
                <div class="flex items-center justify-between">
                    <div>
                        <h3 class="mb-2 text-lg font-semibold text-gray-700">
                            Enable Risk Assessment
                        </h3>
                        <p class="text-gray-600">
                            Turn on automated risk assessment monitoring for students
                        </p>
                    </div>
                    <div class="flex items-center">
                        <Field
                            :id="'risk_assessment_enabled'"
                            :name="'risk_assessment_enabled'"
                            :component="'toggleTemplate'"
                        >
                            <template #toggleTemplate="{ props }">
                                <ToggleSwitch
                                    :modelValue="riskAssessmentEnabled"
                                    @update:modelValue="
                                        (value) => {
                                            riskAssessmentEnabled = value;
                                            props.onChange({ target: { value: value } });
                                        }
                                    "
                                    :label="riskAssessmentEnabled ? 'Enabled' : 'Disabled'"
                                />
                            </template>
                        </Field>
                    </div>
                </div>
            </div>
        </div>

        <!-- Risk Assessment Configuration (Hidden when disabled) -->
        <div v-if="riskAssessmentEnabled" class="transition-all duration-300">
            <!-- Risk Assessment Type Section -->
            <RiskAssessmentTypeSection />

            <!-- Risk Assessment Category Section -->
            <RiskAssessmentCategorySection />
        </div>
    </div>
</template>

<script setup>
import { ref, watch, onMounted } from 'vue';
import { Field } from '@progress/kendo-vue-form';
import ToggleSwitch from '@spa/components/KendoInputs/ToggleSwitch.vue';
import RiskAssessmentTypeSection from './RiskAssessmentTypeSection.vue';
import RiskAssessmentCategorySection from './RiskAssessmentCategorySection.vue';
import { useStudentRiskAssessmentSettingsStore } from '@spa/stores/modules/student-risk-assessment-settings/studentRiskAssessmentSettingsStore.js';

const store = useStudentRiskAssessmentSettingsStore();

// Create a local ref that syncs with the store
const riskAssessmentEnabled = ref(store.formData['risk_assessment_enabled']);

// Watch for changes in the local ref and update the store
watch(riskAssessmentEnabled, (newValue) => {
    store.formData['risk_assessment_enabled'] = newValue;
});

// Watch for changes in the store and update the local ref
watch(
    () => store.formData['risk_assessment_enabled'],
    (newValue) => {
        riskAssessmentEnabled.value = newValue;
    }
);

// Initialize the ref with the current store value
onMounted(() => {
    riskAssessmentEnabled.value = store.formData['risk_assessment_enabled'];
});
</script>

<style scoped></style>
