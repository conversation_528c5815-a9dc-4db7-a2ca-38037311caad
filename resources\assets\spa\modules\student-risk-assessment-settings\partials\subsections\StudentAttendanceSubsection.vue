<template>
    <div class="mb-8">
        <div class="mb-4 flex items-center justify-between">
            <h3 class="flex items-center text-lg font-semibold text-gray-700">
                <span class="mr-3 rounded-lg bg-primary-blue-100 p-2">
                    <svg
                        class="h-5 w-5 text-primary-blue-600"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                    >
                        <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"
                        ></path>
                    </svg>
                </span>
                Student Attendance
            </h3>
            <Field
                :id="'attendance_enabled'"
                :name="'attendance_enabled'"
                :component="'toggleTemplate'"
            >
                <template #toggleTemplate="{ props }">
                    <ToggleSwitch
                        :modelValue="store.formData['attendance_enabled']"
                        @update:modelValue="
                            (value) => {
                                store.formData['attendance_enabled'] = value;
                                props.onChange({ target: { value: value } });
                            }
                        "
                        :label="store.formData['attendance_enabled'] ? 'Enabled' : 'Disabled'"
                        :size="'small'"
                    />
                </template>
            </Field>
        </div>

        <div v-if="store.formData['attendance_enabled']" class="transition-all duration-300">
            <div
                class="mb-4 rounded-lg border border-primary-blue-200 bg-primary-blue-50 p-3 text-sm italic text-primary-blue-800"
            >
                Applicable Policy: Monitor student attendance patterns to identify at-risk students
            </div>
            <div class="grid grid-cols-1 gap-4 md:grid-cols-3">
                <!-- Low Risk -->
                <div class="rounded-lg border-2 border-green-500 bg-white p-4 shadow-sm">
                    <div class="mb-3 flex items-center font-semibold">
                        <div class="mr-2 h-3 w-3 rounded-full bg-green-500"></div>
                        Low Risk
                    </div>
                    <div>
                        <label class="mb-1 block text-sm font-medium text-gray-700"
                            >Threshold Value:</label
                        >
                        <div class="flex items-center gap-2">
                            <Field
                                :id="'attendance_low_risk'"
                                :name="'attendance_low_risk'"
                                :component="'numberTemplate'"
                                :validator="validatePercentage"
                            >
                                <template #numberTemplate="{ props }">
                                    <FormNumericTextBox
                                        v-bind="props"
                                        @change="
                                            handlePercentageChange(
                                                $event,
                                                'attendance_low_risk',
                                                props.onChange
                                            )
                                        "
                                        @blur="props.onBlur"
                                        @focus="props.onFocus"
                                        :min="0"
                                        :max="100"
                                        :decimals="0"
                                        :format="'n0'"
                                        class="flex-1"
                                    />
                                </template>
                            </Field>
                            <span class="text-sm font-semibold text-gray-500">%</span>
                        </div>
                    </div>
                </div>

                <!-- Medium Risk -->
                <div class="rounded-lg border-2 border-amber-500 bg-white p-4 shadow-sm">
                    <div class="mb-3 flex items-center font-semibold">
                        <div class="mr-2 h-3 w-3 rounded-full bg-amber-500"></div>
                        Medium Risk
                    </div>
                    <div>
                        <label class="mb-1 block text-sm font-medium text-gray-700"
                            >Threshold Value:</label
                        >
                        <div class="flex items-center gap-2">
                            <Field
                                :id="'attendance_medium_risk'"
                                :name="'attendance_medium_risk'"
                                :component="'numberTemplate'"
                                :validator="validatePercentage"
                            >
                                <template #numberTemplate="{ props }">
                                    <FormNumericTextBox
                                        v-bind="props"
                                        @change="
                                            handlePercentageChange(
                                                $event,
                                                'attendance_medium_risk',
                                                props.onChange
                                            )
                                        "
                                        @blur="props.onBlur"
                                        @focus="props.onFocus"
                                        :min="0"
                                        :max="100"
                                        :decimals="0"
                                        :format="'n0'"
                                        class="flex-1"
                                    />
                                </template>
                            </Field>
                            <span class="text-sm font-semibold text-gray-500">%</span>
                        </div>
                    </div>
                </div>

                <!-- High Risk -->
                <div class="rounded-lg border-2 border-red-500 bg-white p-4 shadow-sm">
                    <div class="mb-3 flex items-center font-semibold">
                        <div class="mr-2 h-3 w-3 rounded-full bg-red-500"></div>
                        High Risk
                    </div>
                    <div>
                        <label class="mb-1 block text-sm font-medium text-gray-700"
                            >Threshold Value:</label
                        >
                        <div class="flex items-center gap-2">
                            <Field
                                :id="'attendance_high_risk'"
                                :name="'attendance_high_risk'"
                                :component="'numberTemplate'"
                                :validator="validatePercentage"
                            >
                                <template #numberTemplate="{ props }">
                                    <FormNumericTextBox
                                        v-bind="props"
                                        @change="
                                            handlePercentageChange(
                                                $event,
                                                'attendance_high_risk',
                                                props.onChange
                                            )
                                        "
                                        @blur="props.onBlur"
                                        @focus="props.onFocus"
                                        :min="0"
                                        :max="100"
                                        :decimals="0"
                                        :format="'n0'"
                                        class="flex-1"
                                    />
                                </template>
                            </Field>
                            <span class="text-sm font-semibold text-gray-500">%</span>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { watch } from 'vue';
import { Field } from '@progress/kendo-vue-form';
import FormNumericTextBox from '@spa/components/KendoInputs/FormNumericTextBox.vue';
import ToggleSwitch from '@spa/components/KendoInputs/ToggleSwitch.vue';
import { useStudentRiskAssessmentSettingsStore } from '@spa/stores/modules/student-risk-assessment-settings/studentRiskAssessmentSettingsStore.js';

const store = useStudentRiskAssessmentSettingsStore();

// Custom validator for percentage values (0-100)
const validatePercentage = (value) => {
    // If attendance is disabled, don't validate these fields
    if (!store.formData.attendance_enabled) {
        return '';
    }

    if (!value && value !== 0) {
        return 'This field is required.';
    }
    const numValue = Number(value);
    if (isNaN(numValue)) {
        return 'Please enter a valid number.';
    }
    if (numValue < 0) {
        return 'Value cannot be negative.';
    }
    if (numValue > 100) {
        return 'Value cannot exceed 100%.';
    }
    return '';
};

// Handle percentage change with validation
const handlePercentageChange = (event, fieldName, originalOnChange) => {
    let value = event.value;

    // Enforce max value of 100
    if (value > 100) {
        value = 100;
        // Create a new event object with the corrected value
        const correctedEvent = { ...event, value: value };
        store.formData[fieldName] = value;
        originalOnChange(correctedEvent);
    } else {
        // Update store data for valid values
        store.formData[fieldName] = value;
        originalOnChange(event);
    }
};
</script>

<style scoped></style>
