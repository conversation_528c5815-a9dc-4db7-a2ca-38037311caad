<template>
    <div class="mb-8">
        <div class="mb-4 flex items-center justify-between">
            <h3 class="flex items-center text-lg font-semibold text-gray-700">
                <span class="mr-3 rounded-lg bg-primary-blue-100 p-2">
                    <svg
                        class="h-5 w-5 text-primary-blue-600"
                        fill="none"
                        stroke="currentColor"
                        viewBox="0 0 24 24"
                    >
                        <path
                            stroke-linecap="round"
                            stroke-linejoin="round"
                            stroke-width="2"
                            d="M12 8c-1.657 0-3 .895-3 2s1.343 2 3 2 3 .895 3 2-1.343 2-3 2m0-8c1.11 0 2.08.402 2.599 1M12 8V7m0 1v8m0 0v1m0-1c-1.11 0-2.08-.402-2.599-1"
                        ></path>
                    </svg>
                </span>
                Student Fee Payment
            </h3>
            <Field :id="'payment_enabled'" :name="'payment_enabled'" :component="'toggleTemplate'">
                <template #toggleTemplate="{ props }">
                    <ToggleSwitch
                        :modelValue="store.formData['payment_enabled']"
                        @update:modelValue="
                            (value) => {
                                store.formData['payment_enabled'] = value;
                                props.onChange({ target: { value: value } });
                            }
                        "
                        :label="store.formData['payment_enabled'] ? 'Enabled' : 'Disabled'"
                        :size="'small'"
                    />
                </template>
            </Field>
        </div>

        <div v-if="store.formData['payment_enabled']" class="transition-all duration-300">
            <div
                class="mb-4 rounded-lg border border-primary-blue-200 bg-primary-blue-50 p-3 text-sm italic text-primary-blue-800"
            >
                Applicable Policy: Track payment status and outstanding fees to identify students at
                financial risk
            </div>

            <div class="grid grid-cols-1 gap-4 md:grid-cols-3">
                <!-- Low Risk -->
                <div class="rounded-lg border-2 border-green-500 bg-white p-4 shadow-sm">
                    <div class="mb-3 flex items-center font-semibold">
                        <div class="mr-2 h-3 w-3 rounded-full bg-green-500"></div>
                        Low Risk
                    </div>
                    <div>
                        <label class="mb-2 block text-sm font-medium text-gray-700"
                            >Payment Status Criteria:</label
                        >
                        <Field
                            :id="'payment_low_risk_criteria'"
                            :name="'payment_low_risk_criteria'"
                            :component="'checkboxTemplate'"
                            v-model="store.formData['payment_low_risk_criteria']"
                        >
                            <template #checkboxTemplate="{ props }">
                                <FormCheckboxGroup
                                    v-bind="props"
                                    @change="
                                        handleCheckboxChange(
                                            $event,
                                            'payment_low_risk_criteria',
                                            props.onChange
                                        )
                                    "
                                    @blur="props.onBlur"
                                    @focus="props.onFocus"
                                    :dataItems="lowRiskPaymentCriteria"
                                />
                            </template>
                        </Field>
                    </div>
                </div>

                <!-- Medium Risk -->
                <div class="rounded-lg border-2 border-amber-500 bg-white p-4 shadow-sm">
                    <div class="mb-3 flex items-center font-semibold">
                        <div class="mr-2 h-3 w-3 rounded-full bg-amber-500"></div>
                        Medium Risk
                    </div>
                    <div>
                        <label class="mb-2 block text-sm font-medium text-gray-700"
                            >Payment Status Criteria:</label
                        >
                        <Field
                            :id="'payment_medium_risk_criteria'"
                            :name="'payment_medium_risk_criteria'"
                            :component="'checkboxTemplate'"
                            v-model="store.formData['payment_medium_risk_criteria']"
                        >
                            <template #checkboxTemplate="{ props }">
                                <FormCheckboxGroup
                                    v-bind="props"
                                    @change="
                                        handleCheckboxChange(
                                            $event,
                                            'payment_medium_risk_criteria',
                                            props.onChange
                                        )
                                    "
                                    @blur="props.onBlur"
                                    @focus="props.onFocus"
                                    :dataItems="mediumRiskPaymentCriteria"
                                />
                            </template>
                        </Field>
                    </div>
                </div>

                <!-- High Risk -->
                <div class="rounded-lg border-2 border-red-500 bg-white p-4 shadow-sm">
                    <div class="mb-3 flex items-center font-semibold">
                        <div class="mr-2 h-3 w-3 rounded-full bg-red-500"></div>
                        High Risk
                    </div>
                    <div>
                        <label class="mb-2 block text-sm font-medium text-gray-700"
                            >Payment Status Criteria:</label
                        >
                        <Field
                            :id="'payment_high_risk_criteria'"
                            :name="'payment_high_risk_criteria'"
                            :component="'checkboxTemplate'"
                            v-model="store.formData['payment_high_risk_criteria']"
                        >
                            <template #checkboxTemplate="{ props }">
                                <FormCheckboxGroup
                                    v-bind="props"
                                    @change="
                                        handleCheckboxChange(
                                            $event,
                                            'payment_high_risk_criteria',
                                            props.onChange
                                        )
                                    "
                                    @blur="props.onBlur"
                                    @focus="props.onFocus"
                                    :dataItems="highRiskPaymentCriteria"
                                />
                            </template>
                        </Field>
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>

<script setup>
import { Field } from '@progress/kendo-vue-form';
import FormCheckboxGroup from '@spa/components/KendoInputs/FormCheckboxGroup.vue';
import ToggleSwitch from '@spa/components/KendoInputs/ToggleSwitch.vue';
import { useStudentRiskAssessmentSettingsStore } from '@spa/stores/modules/student-risk-assessment-settings/studentRiskAssessmentSettingsStore.js';

const store = useStudentRiskAssessmentSettingsStore();

// Payment criteria options for different risk levels
const lowRiskPaymentCriteria = [
    { id: 'payment_plan_approved', label: 'All payment schedule payments paid' },
];

const mediumRiskPaymentCriteria = [
    { id: 'payment_plan_approved', label: 'All payment schedule payments paid' },
];

const highRiskPaymentCriteria = [
    { id: 'payment_plan_approved', label: 'All payment schedule payments paid' },
];

// Handle checkbox change
const handleCheckboxChange = (event, fieldName, originalOnChange) => {
    console.log('Checkbox change event:', event);
    console.log(`${fieldName} changed:`, event.value);

    // Update store data
    store.formData[fieldName] = event.value;
    originalOnChange(event);

    console.log(`${fieldName} final value in store:`, store.formData[fieldName]);
};
</script>

<style scoped></style>
