<template>
    <div class="space-y-4">
        <div class="grid grid-cols-1 gap-4 lg:grid-cols-4">
            <Card
                v-for="(card, index) in cards"
                :key="card.title"
                :pt="{
                    root: `border shadow-none ${card.bgColor} ${card.borderColor}`,
                }"
                @click="handleCardClick(card)"
            >
                <template #content>
                    <div class="flex flex-col gap-1">
                        <p class="text-2xl font-bold max-md:text-xl" :class="card.textColor">
                            {{ card.value }}
                        </p>
                        <h3 class="text-sm text-gray-600">{{ card.title }}</h3>
                    </div>
                </template>
            </Card>
        </div>
        <AsyncGrid
            :columns="columns"
            :store="store"
            :has-create-action="false"
            :has-export="false"
            :has-filters="true"
            :gridStyle="{
                height: '100%',
            }"
            :enable-selection="true"
            :has-floating-actions="true"
            :has-bulk-actions="true"
            :has-bulk-delete="false"
            :has-bulk-export="false"
            :filter-columns="3"
            @handel-reset="
                () => {
                    initFilters();
                }
            "
            :actions="['view']"
        >
            <template #bulk-actions>
                <Button variant="secondary" class="shadow-none">
                    <file-icon name="csv" fill="currentColor" />
                    Export Report
                </Button>
                <Button
                    variant="secondary"
                    class="shadow-none"
                    :type="'link'"
                    :href="route('spa.student-risk-assessment-settings')"
                >
                    <icon name="setting" fill="currentColor" />
                    Settings
                </Button>
            </template>
            <template #filters>
                <FilterBlockWrapper label="Risk Level">
                    <EnumSelect
                        enum-class="GalaxyAPI\Enums\RiskLevelEnum"
                        v-model="store.filters.riskLevel"
                        :placeholder="'Select Risk Level'"
                        :default-value="1"
                        :has-select-all="true"
                    />
                </FilterBlockWrapper>
                <FilterBlockWrapper label="Status">
                    <EnumSelect
                        enum-class="GalaxyAPI\Enums\PaymentStatusEnum"
                        v-model="store.filters.paymentStatus"
                        :placeholder="'Select Payment Status'"
                        :has-select-all="true"
                    />
                </FilterBlockWrapper>
                <FilterBlockWrapper label="Course">
                    <CoursesSelect
                        v-model="store.filters.course"
                        :placeholder="'Select Course'"
                        :has-select-all="true"
                    />
                </FilterBlockWrapper>
            </template>
            <template #body-cell-student_name="{ props }">
                <div class="flex items-center gap-2">
                    <Avatar :label="'AN'" />
                    <span>{{ props.dataItem.name }}</span>
                </div>
            </template>
            <template #body-cell-course="{ props }">
                {{ props.dataItem.course?.code }} - {{ props.dataItem?.course?.name }}
            </template>
            <template #body-cell-risk_level="{ props }">
                <Badge :variant="getBadgeVariant(props.dataItem?.risk_level)">
                    {{ props.dataItem?.risk_level }}
                </Badge>
            </template>
            <template #body-cell-progress="{ props }">
                <div class="space-y-1">
                    <ProgressBar
                        :value="props.dataItem?.progress"
                        :label-visible="false"
                        :style="{ height: '12px' }"
                    />
                    <p class="text-xs font-medium text-gray-700">{{ props.dataItem?.progress }}%</p>
                </div>
            </template>
            <template #body-cell-attendance="{ props }">
                <div class="space-y-1">
                    <VMenu>
                        <MultiStatusProgressBar
                            :items="[
                                {
                                    label: 'Present',
                                    value: props.dataItem?.attendance?.present,
                                    colorClass: 'bg-present',
                                },
                                {
                                    label: 'Absent',
                                    value: props.dataItem?.attendance?.absent,
                                    colorClass: 'bg-absent',
                                },
                            ]"
                            :total="props.dataItem?.attendance?.total"
                            :height="'12px'"
                        />
                        <template #popper>
                            <div class="w-52 space-y-2 p-2 text-gray-700">
                                <p class="text-sm font-medium text-gray-700">
                                    {{ props.dataItem?.attendance?.present_percent.toFixed(2) }}%
                                    <span class="text-gray-500"
                                        >({{ props.dataItem?.attendance?.present }} /
                                        {{ props.dataItem?.attendance?.total }})</span
                                    >
                                </p>
                                <div class="flex items-center gap-2 text-xs">
                                    <span class="h-3 w-3 rounded-sm bg-green-500"></span>
                                    <span>Present</span>
                                    <span class="ml-auto text-gray-500"
                                        >{{ props.dataItem?.attendance?.present }} days</span
                                    >
                                </div>
                                <div class="flex items-center gap-2 text-xs">
                                    <span class="h-3 w-3 rounded-sm bg-yellow-500"></span>
                                    <span>Absent</span>
                                    <span class="ml-auto text-gray-500"
                                        >{{ props.dataItem?.attendance?.absent }} days</span
                                    >
                                </div>
                                <div class="flex items-center gap-2 text-xs">
                                    <span class="h-3 w-3 rounded-sm bg-gray-300"></span>
                                    <span>Total</span>
                                    <span class="ml-auto text-gray-500"
                                        >{{ props.dataItem?.attendance?.total }} days</span
                                    >
                                </div>
                            </div>
                        </template>
                    </VMenu>
                    <p class="text-xs font-medium text-gray-700">
                        {{ props.dataItem?.attendance?.present_percent.toFixed(2) }}%
                        <span class="text-gray-500"
                            >({{ props.dataItem?.attendance?.present }} /
                            {{ props.dataItem?.attendance?.total }})</span
                        >
                    </p>
                </div>
            </template>
            <template #body-cell-payment_status="{ props }">
                <span :class="getPaymentStatusClass(props.dataItem?.risk_level)">{{
                    props.dataItem?.payment_status
                }}</span>
            </template>
            <template #body-cell-last_contacted="{ props }">
                {{ props.dataItem?.last_contacted }}
            </template>
            <template #actions="{ row }">
                <GridActionButton tooltip-title="Urgent Call">
                    <icon :name="'phone'" :fill="'currentColor'" :height="'20'" :width="'20'" />
                </GridActionButton>
                <GridActionButton tooltip-title="Encourage Student">
                    <icon :name="'encourage'" :fill="'currentColor'" :height="'20'" :width="'20'" />
                </GridActionButton>
                <GridActionButton tooltip-title="Follow Up">
                    <icon :name="'follow-up'" :fill="'currentColor'" :height="'20'" :width="'20'" />
                </GridActionButton>
                <GridActionButton tooltip-title="Email">
                    <icon :name="'mail'" :fill="'currentColor'" :height="'20'" :width="'20'" />
                </GridActionButton>
            </template>
        </AsyncGrid>
    </div>
    <StudentRiskAssessmentForm />
</template>

<script setup>
import AsyncGrid from '@spa/components/AsyncComponents/Grid/AsyncGrid.vue';
import { ref, onMounted } from 'vue';
import { useStudentRiskAssessmentStore } from '@spa/stores/modules/student-risk-assessment/useStudentRiskAssessmentStore.js';
import StudentRiskAssessmentForm from '@spa/modules/student-risk-assessment/StudentRiskAssessmentForm.vue';
import Badge from '@spa/components/badges/Badge.vue';
import FilterBlockWrapper from '@spa/components/AsyncComponents/Grid/Partials/FilterBlockWrapper.vue';
import { ProgressBar, ChunkProgressBar } from '@progress/kendo-vue-progressbars';
import MultiStatusProgressBar from '@spa/components/ui/multi-status-progress-bar/MultiStatusProgressBar.vue';
import EnumSelect from '@spa/components/AsyncComponents/Select/EnumSelect.vue';
import Button from '@spa/components/Buttons/Button.vue';
import Avatar from '@spa/components/Avatar/Avatar.vue';
import GridActionButton from '@spa/components/AsyncComponents/Grid/Partials/GridActionButton.vue';
import CoursesSelect from '@spa/modules/teacher/partials/CoursesSelect.vue';
import Card from '@spa/components/Card/Card.vue';

const store = useStudentRiskAssessmentStore();

const cards = [
    {
        enumId: null,
        value: 100,
        title: 'Total Students',
        bgColor: 'bg-gray-100',
        borderColor: 'border-gray-500',
        textColor: 'text-gray-800',
    },
    {
        enumId: 3,
        value: 10,
        title: 'High Risk Students',
        bgColor: 'bg-red-50',
        borderColor: 'border-red-500',
        textColor: 'text-red-500',
    },
    {
        enumId: 2,
        value: 20,
        title: 'Medium Risk Students',
        bgColor: 'bg-yellow-50',
        borderColor: 'border-yellow-500',
        textColor: 'text-yellow-500',
    },
    {
        enumId: 1,
        value: 30,
        title: 'Low Risk Students',
        bgColor: 'bg-green-50',
        borderColor: 'border-green-500',
        textColor: 'text-green-500',
    },
];

const columns = [
    {
        field: 'student_name',
        name: 'student_name',
        title: 'Student Name',
        replace: true,
        width: '200px',
        sortable: true,
    },
    {
        field: 'course',
        name: 'course',
        title: 'Course',
        replace: true,
        width: '250px',
        sortable: true,
    },
    {
        field: 'risk_level',
        name: 'risk_level',
        title: 'Risk Level',
        replace: true,
        width: '100px',
        sortable: true,
    },
    {
        field: 'progress',
        name: 'progress',
        title: 'Progress',
        replace: true,
        width: '180px',
    },
    {
        field: 'attendance',
        name: 'attendance',
        title: 'Attendance',
        replace: true,
        width: '180px',
    },
    {
        field: 'payment_status',
        name: 'payment_status',
        title: 'Payment',
        replace: true,
        width: '150px',
        sortable: true,
    },
    {
        field: 'last_contact',
        name: 'last_contact',
        title: 'Last Contacted',
        width: '150px',
        sortable: true,
    },
    // Add more columns as needed
];

const getBadgeVariant = (value) => {
    let badgeMapping = {
        HIGH: 'error',
        MEDIUM: 'warning',
        LOW: 'success',
    };
    return badgeMapping[value] || 'default';
};

const getPaymentStatusClass = (value) => {
    let classMapping = {
        HIGH: 'text-green-500',
        MEDIUM: 'text-yellow-500',
        LOW: 'text-red-500',
    };
    return classMapping[value] || 'text-gray-500';
};

const handleCardClick = (card) => {
    store.filters.riskLevel = card?.enumId;
    store.fetchPaged();
};

const initFilters = () => {
    store.filters = {};
};

onMounted(() => {
    initFilters();
});
</script>
