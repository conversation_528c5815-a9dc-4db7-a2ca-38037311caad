<template>
    <div class="space-y-4">
        <div class="grid grid-cols-1 gap-4 lg:grid-cols-4">
            <Card
                v-for="(card, index) in cards"
                :key="card.title"
                :pt="{
                    root: `border shadow-none cursor-pointer transition-all duration-200 hover:shadow-md ${card.bgColor} ${card.borderColor} ${isCardActive(card) ? 'ring-2 ring-blue-500 shadow-lg' : ''}`,
                }"
                @click="handleCardClick(card)"
            >
                <template #content>
                    <div class="flex flex-col gap-1">
                        <p class="text-2xl font-bold max-md:text-xl" :class="card.textColor">
                            {{ card.value }}
                        </p>
                        <h3 class="text-sm text-gray-600">{{ card.title }}</h3>
                        <div v-if="isCardActive(card)" class="mt-1">
                            <span
                                class="inline-flex items-center rounded-full bg-blue-100 px-2 py-1 text-xs font-medium text-blue-800"
                            >
                                Active Filter
                            </span>
                        </div>
                    </div>
                </template>
            </Card>
        </div>
        <AsyncGrid
            :columns="columns"
            :store="store"
            :has-create-action="false"
            :has-export="false"
            :has-filters="true"
            :gridStyle="{
                height: '100%',
            }"
            :enable-selection="true"
            :has-floating-actions="true"
            :has-bulk-actions="true"
            :has-bulk-delete="false"
            :has-bulk-export="false"
            :filter-columns="3"
            @handel-reset="
                () => {
                    initFilters();
                }
            "
            :actions="['view']"
        >
            <template #bulk-actions>
                <Button variant="secondary" class="shadow-none">
                    <file-icon name="csv" fill="currentColor" />
                    Export Report
                </Button>
                <Button
                    variant="secondary"
                    class="shadow-none"
                    :type="'link'"
                    :href="route('spa.student-risk-assessment-settings')"
                >
                    <icon name="setting" fill="currentColor" />
                    Settings
                </Button>
            </template>
            <template #filters>
                <FilterBlockWrapper label="Risk Level">
                    <EnumSelect
                        enum-class="GalaxyAPI\Enums\RiskLevelEnum"
                        v-model="store.filters.riskLevel"
                        :placeholder="'Select Risk Level'"
                        :default-value="1"
                        :has-select-all="true"
                    />
                </FilterBlockWrapper>
                <FilterBlockWrapper label="Status">
                    <EnumSelect
                        enum-class="GalaxyAPI\Enums\PaymentStatusEnum"
                        v-model="store.filters.paymentStatus"
                        :placeholder="'Select Payment Status'"
                        :has-select-all="true"
                    />
                </FilterBlockWrapper>
                <FilterBlockWrapper label="Course">
                    <CoursesSelect
                        v-model="store.filters.course"
                        :placeholder="'Select Course'"
                        :has-select-all="true"
                    />
                </FilterBlockWrapper>
            </template>
            <template #body-cell-student_name="{ props }">
                <div class="flex items-center gap-2">
                    <Avatar :label="'AN'" />
                    <span>{{ props.dataItem.name }}</span>
                </div>
            </template>
            <template #body-cell-course="{ props }">
                {{ props.dataItem.course?.code }} - {{ props.dataItem?.course?.name }}
            </template>
            <template #body-cell-risk_level="{ props }">
                <Badge :variant="getBadgeVariant(props.dataItem?.risk_level)">
                    {{ props.dataItem?.risk_level }}
                </Badge>
            </template>
            <template #body-cell-last_contacted="{ props }">
                {{ props.dataItem?.last_contacted }}
            </template>
            <template #actions="{ row }">
                <GridActionButton tooltip-title="Urgent Call">
                    <icon :name="'phone'" :fill="'currentColor'" :height="'20'" :width="'20'" />
                </GridActionButton>
                <GridActionButton tooltip-title="Encourage Student">
                    <icon :name="'encourage'" :fill="'currentColor'" :height="'20'" :width="'20'" />
                </GridActionButton>
                <GridActionButton tooltip-title="Follow Up">
                    <icon :name="'follow-up'" :fill="'currentColor'" :height="'20'" :width="'20'" />
                </GridActionButton>
                <GridActionButton tooltip-title="Email">
                    <icon :name="'mail'" :fill="'currentColor'" :height="'20'" :width="'20'" />
                </GridActionButton>
            </template>
        </AsyncGrid>
    </div>
    <StudentRiskAssessmentForm />
</template>

<script setup>
import AsyncGrid from '@spa/components/AsyncComponents/Grid/AsyncGrid.vue';
import { onMounted, computed } from 'vue';
import { useStudentRiskAssessmentStore } from '@spa/stores/modules/student-risk-assessment/useStudentRiskAssessmentStore.js';
import StudentRiskAssessmentForm from '@spa/modules/student-risk-assessment/StudentRiskAssessmentForm.vue';
import Badge from '@spa/components/badges/Badge.vue';
import FilterBlockWrapper from '@spa/components/AsyncComponents/Grid/Partials/FilterBlockWrapper.vue';
import { ProgressBar, ChunkProgressBar } from '@progress/kendo-vue-progressbars';
import MultiStatusProgressBar from '@spa/components/ui/multi-status-progress-bar/MultiStatusProgressBar.vue';
import EnumSelect from '@spa/components/AsyncComponents/Select/EnumSelect.vue';
import Button from '@spa/components/Buttons/Button.vue';
import Avatar from '@spa/components/Avatar/Avatar.vue';
import GridActionButton from '@spa/components/AsyncComponents/Grid/Partials/GridActionButton.vue';
import CoursesSelect from '@spa/modules/teacher/partials/CoursesSelect.vue';
import Card from '@spa/components/Card/Card.vue';

const store = useStudentRiskAssessmentStore();

// Dynamic cards computed from store's summary data
const cards = computed(() => {
    console.log('Store summary data:', store.summaryData);
    return [
        {
            enumId: null,
            value: store.summaryData.total,
            title: 'Total Students',
            bgColor: 'bg-gray-100',
            borderColor: 'border-gray-500',
            textColor: 'text-gray-800',
        },
        {
            enumId: 3,
            value: store.summaryData.high_risk,
            title: 'High Risk Students',
            bgColor: 'bg-red-50',
            borderColor: 'border-red-500',
            textColor: 'text-red-500',
        },
        {
            enumId: 2,
            value: store.summaryData.medium_risk,
            title: 'Medium Risk Students',
            bgColor: 'bg-yellow-50',
            borderColor: 'border-yellow-500',
            textColor: 'text-yellow-500',
        },
        {
            enumId: 1,
            value: store.summaryData.low_risk,
            title: 'Low Risk Students',
            bgColor: 'bg-green-50',
            borderColor: 'border-green-500',
            textColor: 'text-green-500',
        },
    ];
});

const columns = [
    {
        field: 'student_name',
        name: 'student_name',
        title: 'Student Name',
        replace: true,
        width: '200px',
        sortable: true,
    },
    {
        field: 'course',
        name: 'course',
        title: 'Course',
        replace: true,
        width: '250px',
        sortable: true,
    },
    {
        field: 'risk_level',
        name: 'risk_level',
        title: 'Risk Level',
        replace: true,
        width: '100px',
        sortable: true,
    },
    {
        field: 'last_contact',
        name: 'last_contact',
        title: 'Last Contacted',
        width: '150px',
        sortable: true,
    },
];

const getBadgeVariant = (value) => {
    let badgeMapping = {
        HIGH: 'error',
        MEDIUM: 'warning',
        LOW: 'success',
    };
    return badgeMapping[value] || 'default';
};

const handleCardClick = (card) => {
    // Clear existing filters first and set new filter in one operation
    // This prevents multiple watcher triggers
    const newFilters = {};

    // Set risk level filter if card has enumId (not total card)
    // Note: Backend expects 'riskLevel' which gets converted to 'scopeFilterRiskLevel'
    if (card?.enumId !== null) {
        newFilters.riskLevel = card.enumId;
    }

    console.log('Setting filters:', newFilters);
    console.log('Card clicked:', card);

    // Update filters in one operation - let the watcher handle fetchPaged()
    store.filters = newFilters;
};

const isCardActive = (card) => {
    // Total card is active when no risk level filter is set
    if (card.enumId === null) {
        const isActive = !store.filters.riskLevel;
        console.log('Total card active check:', {
            isActive,
            currentFilter: store.filters.riskLevel,
        });
        return isActive;
    }
    // Other cards are active when their enumId matches the current filter
    const isActive = store.filters.riskLevel === card.enumId;
    console.log('Card active check:', {
        cardEnumId: card.enumId,
        currentFilter: store.filters.riskLevel,
        isActive,
    });
    return isActive;
};

const initFilters = () => {
    store.filters = {};
    // Don't call fetchPaged() here - let the store's filter watcher handle it
};

onMounted(() => {
    initFilters();
});
</script>
