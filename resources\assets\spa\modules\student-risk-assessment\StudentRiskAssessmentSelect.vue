<script setup>
import AsyncSelect from '@spa/components/AsyncComponents/Select/AsyncSelect.vue';
import { useStudentRiskAssessmentStore } from '@spa/stores/modules/student-risk-assessment/useStudentRiskAssessmentStore.js';
import { computed } from 'vue';

const props = defineProps({
    modelValue: [String, Number, Array, Object],
    label: String,
    className: String,
    optionValue: {
        type: String,
        default: 'id',
    },
    optionLabel: {
        type: String,
        default: 'name',
    },
    disabled: Boolean,
    clearable: {
        type: Boolean,
        default: true,
    },
    multiple: {
        type: Boolean,
        default: false,
    },
    readonly: Boolean,
    useChips: {
        type: Boolean,
        default: false,
    },
    placeholder: {
        type: String,
        default: 'Select StudentRiskAssessment',
    },
});

const emit = defineEmits(['update:modelValue']);
const vModel = computed({
    get() {
        return props.modelValue;
    },
    set(val) {
        emit('update:modelValue', val);
    },
});
const store = useStudentRiskAssessmentStore();
</script>

<template>
    <AsyncSelect
        :label="label"
        :className="className"
        :optionValue="optionValue"
        :optionLabel="optionLabel"
        :disabled="disabled"
        :store="store"
        v-model="vModel"
        :clearable="clearable"
        :multiple="multiple"
        :readonly="readonly"
        :useChips="useChips"
        :placeholder="placeholder"
    />
</template>

<style scoped></style>
