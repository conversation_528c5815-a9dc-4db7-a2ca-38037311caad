<template>
    <Card
        :pt="{
            root: 'relative p-4 lg:p-6 rounded-lg shadow-none border-none',
            header: 'border-b max-md:pb-4 pb-6',
            content: 'pt-6',
        }"
    >
        <template #header>
            <div class="flex items-start gap-2">
                <Avatar :label="'RG'" :pt="{ root: 'w-12 h-12', label: 'text-lg' }" />
                <div class="flex flex-col gap-1">
                    <div class="flex w-full items-baseline gap-3">
                        <p class="text-xl font-semibold"><PERSON></p>
                        <span class="text-xs text-gray-500"
                            ><CopyToClipboard :text="1810138" :auto-hide="false"
                        /></span>
                    </div>
                    <div class="flex items-center gap-5">
                        <div class="flex items-center gap-1 text-gray-500">
                            <icon name="mail" :fill="'currentColor'" :width="'18'" :height="'18'" />
                            <p class="text-sm">
                                <CopyToClipboard
                                    :text="'<EMAIL>'"
                                    :auto-hide="false"
                                />
                            </p>
                        </div>
                    </div>
                    <div class="flex items-center gap-6"></div>
                    <div class="flex items-center gap-6">
                        <div class="mt-3 flex items-center">
                            <LabelValuePair :label="'Risk Status:'">
                                <Badge :variant="'error'">High Risk</Badge>
                            </LabelValuePair>
                        </div>
                    </div>
                </div>
                <Button :type="'link'" :size="'sm'" :variant="'secondary'" :class="'ml-auto'">
                    <icon :name="'edit'" :fill="'currentColor'" :height="'18'" :width="'18'" />
                    Edit Student
                </Button>
            </div>
        </template>
        <template #content>
            <div class="px-14 max-md:px-0">
                <Tabs :tabs="tabs" :show-icon="false" @select="handleTabSelect">
                    <template #tab-panel-overview="{ tab, index, isActive }">
                        <Card :variant="'compact'" :pt="{ root: 'bg-gray-100' }">
                            <template #header>
                                <div class="flex items-center gap-2">
                                    <icon
                                        name="user"
                                        :fill="'currentColor'"
                                        :width="'20'"
                                        :height="'20'"
                                    />
                                    <h2 class="text-lg font-medium">Student Information</h2>
                                </div>
                            </template>
                            <template #content>
                                <div class="grid grid-cols-2 gap-4">
                                    <div class="col-span-1 space-y-6">
                                        <LabelValuePair
                                            :label="'Student Email'"
                                            :value="'<EMAIL>'"
                                            :orientation="'vertical'"
                                        />
                                        <LabelValuePair
                                            :label="'Student Phone'"
                                            :value="'+****************'"
                                            :orientation="'vertical'"
                                        />
                                        <LabelValuePair
                                            :label="'College'"
                                            :value="'Brisbane College'"
                                            :orientation="'vertical'"
                                        />
                                    </div>
                                    <div class="col-span-1 space-y-6">
                                        <LabelValuePair
                                            :label="'Course'"
                                            :value="'Diploma of Early Childhood Education and Care'"
                                            :orientation="'vertical'"
                                        />
                                        <LabelValuePair
                                            :label="'Start Date'"
                                            :value="'March 15, 2025'"
                                            :orientation="'vertical'"
                                        />
                                        <LabelValuePair
                                            :label="'Expected Completion'"
                                            :value="'September 14, 2025'"
                                            :orientation="'vertical'"
                                        />
                                    </div>
                                </div>
                            </template>
                        </Card>
                    </template>
                    <template #tab-panel-risk_metrics="{ tab, index, isActive }">
                        <Card :variant="'compact'" :pt="{ root: 'bg-gray-100' }">
                            <template #header>
                                <div class="flex items-center gap-2">
                                    <icon
                                        name="candlestick"
                                        :fill="'currentColor'"
                                        :width="'20'"
                                        :height="'20'"
                                    />
                                    <h2 class="text-lg font-medium">Risk Metrics</h2>
                                </div>
                            </template>
                            <template #content>
                                <div class="grid grid-cols-2 gap-4">
                                    <div class="col-span-1 space-y-6">
                                        <LabelValuePair
                                            :label="'Course Progress'"
                                            :orientation="'vertical'"
                                        >
                                            <div class="flex w-72 flex-col gap-0">
                                                <div class="flex items-center justify-end">
                                                    <span class="text-xs text-gray-500">85%</span>
                                                </div>
                                                <ProgressBar
                                                    :value="85"
                                                    :label-visible="false"
                                                    :style="{ height: '6px' }"
                                                />
                                            </div>
                                        </LabelValuePair>
                                        <LabelValuePair
                                            :label="'Attendance Rate'"
                                            :orientation="'vertical'"
                                        >
                                            <MultiStatusProgressBar
                                                :items="[
                                                    {
                                                        label: 'Present',
                                                        value: 20,
                                                        colorClass: 'bg-present',
                                                    },
                                                    {
                                                        label: 'Absent',
                                                        value: 5,
                                                        colorClass: 'bg-absent',
                                                    },
                                                ]"
                                                :pt="{
                                                    root: 'w-72',
                                                }"
                                                height="8px"
                                            />
                                            <div class="w-52 space-y-2 text-gray-700">
                                                <p class="text-xs font-medium text-gray-700">
                                                    22.50%
                                                    <span class="text-gray-500">(45 / 200)</span>
                                                </p>
                                                <div class="flex items-center gap-2 text-xs">
                                                    <span
                                                        class="h-3 w-3 rounded-sm bg-green-500"
                                                    ></span>
                                                    <span>Present</span>
                                                    <span class="ml-auto text-gray-500"
                                                        >45 days</span
                                                    >
                                                </div>
                                                <div class="flex items-center gap-2 text-xs">
                                                    <span
                                                        class="h-3 w-3 rounded-sm bg-yellow-500"
                                                    ></span>
                                                    <span>Absent</span>
                                                    <span class="ml-auto text-gray-500"
                                                        >9 days</span
                                                    >
                                                </div>
                                                <div class="flex items-center gap-2 text-xs">
                                                    <span
                                                        class="h-3 w-3 rounded-sm bg-gray-300"
                                                    ></span>
                                                    <span>Total</span>
                                                    <span class="ml-auto text-gray-500"
                                                        >200 days</span
                                                    >
                                                </div>
                                            </div>
                                        </LabelValuePair>
                                    </div>
                                    <div class="col-span-1 space-y-6">
                                        <LabelValuePair
                                            :label="'Payment Status'"
                                            :orientation="'vertical'"
                                        >
                                            <span :class="getPaymentStatusClass('HIGH')">Paid</span>
                                        </LabelValuePair>
                                        <LabelValuePair
                                            :label="'Last Assignment'"
                                            :value="'15 days ago'"
                                            :orientation="'vertical'"
                                        />
                                        <LabelValuePair
                                            :label="'Last Contact'"
                                            :value="'3 days ago (By Email)'"
                                            :orientation="'vertical'"
                                        />
                                    </div>
                                </div>
                            </template>
                        </Card>
                    </template>
                    <template #tab-panel-intervention_actions="{ tab, index, isActive }">
                        <Card :variant="'compact'" :pt="{ root: 'bg-gray-100' }">
                            <template #header>
                                <div class="flex items-center gap-2">
                                    <h2 class="text-lg font-medium">Intervention Actions</h2>
                                </div>
                            </template>
                            <template #content>
                                <div class="grid grid-cols-3 gap-4">
                                    <Card
                                        v-for="action in interventionActions"
                                        :key="action.title"
                                        :pt="{
                                            root: 'border border-gray-200 space-y-4',
                                        }"
                                    >
                                        <template #header>
                                            <div class="flex items-center gap-2">
                                                <div
                                                    class="flex h-10 w-10 items-center justify-center rounded-full"
                                                    :class="getInterventionVariant(action.theme).bg"
                                                >
                                                    <span
                                                        :class="
                                                            getInterventionVariant(action.theme)
                                                                .text
                                                        "
                                                    >
                                                        <icon
                                                            :name="action.icon"
                                                            :fill="'currentColor'"
                                                            :width="'20'"
                                                            :height="'20'"
                                                        />
                                                    </span>
                                                </div>
                                                <h3 class="text-base font-medium">
                                                    {{ action.title }}
                                                </h3>
                                            </div>
                                        </template>
                                        <template #content>
                                            <p class="text-sm text-gray-500">
                                                {{ action.description }}
                                            </p>
                                        </template>
                                        <template #footer>
                                            <Button
                                                :size="'sm'"
                                                :variant="'secondary'"
                                                @click="handleAction(action)"
                                            >
                                                {{ action.btnLabel }}
                                            </Button></template
                                        >
                                    </Card>
                                </div>
                            </template>
                        </Card>
                    </template>
                </Tabs>
            </div>
        </template>
    </Card>
    <SidebarDrawer
        :title="activeAction?.title"
        :visibleDialog="isSidebarOpen"
        @close="isSidebarOpen = false"
        :width="'50%'"
        :maxWidth="'600px'"
    >
        <template #title>
            <div class="text-lg font-medium">{{ activeAction?.title }}</div>
        </template>
        <template #content>
            <p>{{ activeAction?.description }}</p>
        </template>
    </SidebarDrawer>
</template>

<script setup>
import { ref } from 'vue';
import Card from '@spa/components/Card/Card.vue';
import Avatar from '@spa/components/Avatar/Avatar.vue';
import CopyToClipboard from '@spa/components/CopyAction/CopyToClipboard.vue';
import Button from '@spa/components/Buttons/Button.vue';
import LabelValuePair from '@spa/components/LabelValuePair/LabelValuePair.vue';
import Badge from '@spa/components/badges/Badge.vue';
import Tabs from '@spa/components/ui/tabs/Tabs.vue';
import { ProgressBar } from '@progress/kendo-vue-progressbars';
import MultiStatusProgressBar from '@spa/components/ui/multi-status-progress-bar/MultiStatusProgressBar.vue';
import SidebarDrawer from '@spa/components/KendoModals/SidebarDrawer.vue';
import FormWrapper from '@spa/components/KendoModals/SidebarDrawer.vue';

const tabs = [
    {
        title: 'Student Information',
        name: 'overview',
    },
    {
        title: 'Risk Metrics',
        name: 'risk_metrics',
    },
    {
        title: 'Intervention Actions',
        name: 'intervention_actions',
    },
];

const interventionActions = [
    {
        title: 'Urgent Call',
        key: 'urgent_call',
        description: 'Schedule immediate phone call to discuss issue',
        icon: 'smartphone',
        btnLabel: 'Schedule Call',
        theme: 'danger',
    },
    {
        title: 'Payment Plan',
        key: 'payment_plan',
        description: 'Offer flexible payment options',
        icon: 'hand-coins',
        btnLabel: 'Setup Payment Plan',
        theme: 'warning',
    },
    {
        title: 'Academic Support',
        key: 'academic_support',
        description: 'Assign tutor or additional resources',
        icon: 'support',
        btnLabel: 'Assign Tutor',
        theme: 'primary',
    },
    {
        title: 'Email Sequence',
        key: 'email_sequence',
        description: 'Send automated motivational emails',
        icon: 'mail',
        btnLabel: 'Start Email Sequence',
        theme: 'secondary',
    },
    {
        title: 'Deadline Extension',
        key: 'deadline_extension',
        description: 'Extend assignment or assessment deadlines',
        icon: 'extension',
        btnLabel: 'Extend Deadline',
        theme: 'tertiary',
    },
    {
        title: 'Peer Support',
        key: 'peer_support',
        description: 'Connect with study buddy or groups',
        icon: 'users',
        btnLabel: 'Assign Study Buddy',
        theme: 'success',
    },
];

const isSidebarOpen = ref(false);
const activeAction = ref(null);

const getPaymentStatusClass = (value) => {
    let classMapping = {
        HIGH: 'text-green-500',
        MEDIUM: 'text-yellow-500',
        LOW: 'text-red-500',
    };
    return classMapping[value] || 'text-gray-500';
};

const getInterventionVariant = (theme) => {
    let variantMapping = {
        danger: {
            bg: 'bg-red-100',
            text: 'text-red-800',
        },
        warning: {
            bg: 'bg-yellow-100',
            text: 'text-yellow-800',
        },
        primary: {
            bg: 'bg-primary-blue-100',
            text: 'text-primary-blue-800',
        },
        secondary: {
            bg: 'bg-gray-100',
            text: 'text-gray-800',
        },
        tertiary: {
            bg: 'bg-primary-blue-100',
            text: 'text-primary-blue-800',
        },
        success: {
            bg: 'bg-green-100',
            text: 'text-green-800',
        },
    };
    return variantMapping[theme] || variantMapping['secondary'];
};

const handleAction = (action) => {
    console.log('action', action);
    activeAction.value = action;
    isSidebarOpen.value = true;
    if (action.key === 'deadline_extension') {
    }
};
</script>

<style scoped></style>
