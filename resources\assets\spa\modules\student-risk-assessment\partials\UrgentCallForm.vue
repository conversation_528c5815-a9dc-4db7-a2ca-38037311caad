<template>
    <AsyncForm
        :type="'kendo'"
        :layout="{ cols: 2, gap: 16 }"
        :orientation="'vertical'"
        position="right"
        :dialogTitle="'Urgent Call'"
        :store="store"
    >
        <div class="p-4">
            <div class="p-2">
                <FormInput
                    name="name"
                    label="Name"
                    v-model="formData.name"
                    :validation-message="store.errors?.name"
                    :valid="!store.errors?.name"
                    :touched="true"
                    :indicaterequired="true"
                />
            </div>
        </div>
    </AsyncForm>
</template>
<script setup>
import AsyncForm from '@spa/components/AsyncComponents/Form/AsyncForm.vue';
import FormInput from '@spa/components/KendoInputs/FormInput.vue';
import { useEmailTemplateStore } from '@spa/stores/modules/emailtemplate/useEmailTemplateStore.js';
import { storeToRefs } from 'pinia';
// Uncomment these if needed:
// import FormTextArea from '@spa/components/KendoInputs/FormTextArea.vue';
// import FormDatePicker from '@spa/components/KendoInputs/FormDatePicker.vue';

const store = useEmailTemplateStore();
const { formData } = storeToRefs(store);
</script>
