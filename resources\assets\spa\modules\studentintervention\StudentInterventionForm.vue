<template>
    <AsyncForm
        :type="'kendo'"
        :layout="{ cols: 2, gap: 16 }"
        :orientation="'vertical'"
        position="right"
        :dialogTitle="'Add Student Intervention'"
        :store="store"
    >
        <div class="space-y-4 p-4">
            <div class="grid grid-cols-1 gap-4">
                <FormDatePicker
                    name="recorded_date"
                    label="Date"
                    v-model="formData.recorded_date"
                    :validation-message="store.errors?.recorded_date"
                    :valid="!store.errors?.recorded_date"
                    :touched="true"
                    :indicaterequired="true"
                    :format="'dd-MM-yyyy'"
                    :emit-format="'yyyy-MM-dd'"
                />
                <StudentSelect
                    name="student_id"
                    label="Student"
                    v-model="formData.student_id"
                    :validation-message="store.errors?.student_id"
                    :valid="!store.errors?.student_id"
                    :touched="true"
                    :indicaterequired="true"
                />
                <FormValidationWrapper
                    :validation-message="store.errors?.intervention_for"
                    :valid="!store.errors?.intervention_for"
                    :indicate-required="true"
                    label="Intervention Type"
                >
                    <template #field>
                        <EnumSelect
                            className="tw-w-full"
                            enum-class="GalaxyAPI\Enums\InterventionTypeEnum"
                            v-model="formData.intervention_for"
                            placeholder="Select Intervention Type"
                        />
                    </template>
                </FormValidationWrapper>
                <CoursesSelect
                    name="course_id"
                    label="Course"
                    v-model="formData.course_id"
                    :validation-message="store.errors?.course_id"
                    :valid="!store.errors?.course_id"
                    :touched="true"
                    :indicaterequired="true"
                />
                <SemesterSelect
                    name="semester"
                    label="Semester"
                    v-model="formData.semester"
                    :validation-message="store.errors?.semester"
                    :valid="!store.errors?.semester"
                    :indicaterequired="true"
                />

                <InterventionTypeSelect
                    name="intervention_type_ids"
                    label="Intervention Type"
                    v-model="formData.intervention_type_ids"
                    :validation-message="store.errors?.intervention_type_ids"
                    :valid="!store.errors?.intervention_type_ids"
                    :multiple="true"
                    :touched="false"
                    :indicaterequired="false"
                />
            </div>

            <div class="col-span-2">
                <FormTextArea
                    name="details"
                    label="Details of Intervention Case"
                    v-model="formData.details"
                    :validation-message="store.errors?.details"
                    :valid="!store.errors?.details"
                    :touched="true"
                    :indicaterequired="true"
                />
                <InterventionStrategySelect
                    :key="formData.intervention_type"
                    :filters="{
                        interventionType: formData.intervention_type,
                    }"
                    name="intervention_strategy_ids"
                    label="Intervention Strategy"
                    v-model="formData.intervention_strategy_ids"
                    :validation-message="store.errors?.intervention_strategy_ids"
                    :valid="!store.errors?.intervention_strategy_ids"
                    :touched="true"
                    :multiple="true"
                />
                <FormTextArea
                    name="action_taken"
                    label="Action Taken"
                    v-model="formData.action_taken"
                    :validation-message="store.errors?.action_taken"
                    :valid="!store.errors?.action_taken"
                    :touched="true"
                    :indicaterequired="true"
                />

                <FormTextArea
                    name="prevent_reoccurance"
                    label="Action Taken to Prevent ReOccurrence"
                    v-model="formData.prevent_reoccurance"
                    :validation-message="store.errors?.prevent_reoccurance"
                    :valid="!store.errors?.prevent_reoccurance"
                    :touched="true"
                    :indicaterequired="true"
                />
                <FormValidationWrapper
                    :validation-message="store.errors?.status"
                    :valid="!store.errors?.status"
                    :indicate-required="true"
                    label="Status"
                >
                    <template #field>
                        <DropDownList
                            :data-items="[
                                { text: 'Open', value: 1 },
                                { text: 'Closed', value: 2 },
                            ]"
                            :text-field="'text'"
                            :data-item-key="'value'"
                            :value-field="'value'"
                            :value-primitive="true"
                            v-model="formData.status"
                        />
                    </template>
                </FormValidationWrapper>
            </div>

            <div class="grid grid-cols-2 gap-4">
                <FormDatePicker
                    name="due_date"
                    label="Due Date"
                    v-model="formData.due_date"
                    :validation-message="store.errors?.due_date"
                    :valid="!store.errors?.due_date"
                    :touched="true"
                    :indicaterequired="true"
                    :format="'dd-MM-yyyy'"
                    :emit-format="'yyyy-MM-dd'"
                />
                <FormDatePicker
                    name="outcome_date"
                    label="Outcome Date"
                    v-model="formData.outcome_date"
                    :validation-message="store.errors?.outcome_date"
                    :valid="!store.errors?.outcome_date"
                    :touched="true"
                    :indicaterequired="true"
                    :format="'dd-MM-yyyy'"
                    :emit-format="'yyyy-MM-dd'"
                />
            </div>
            <TeacherSelect
                name="teacher_id"
                label="Escalated To"
                v-model="formData.teacher_id"
                :validation-message="store.errors?.teacher_id"
                :valid="!store.errors?.teacher_id"
                :touched="true"
                :indicaterequired="true"
            />
            <FormUpload
                name="file_attachment"
                label="File attachment"
                v-model="formData.file_attachment"
                :validation-message="store.errors?.file_attachment"
                :valid="!store.errors?.file_attachment"
                :touched="true"
            />
        </div>
    </AsyncForm>
</template>
<script setup>
import AsyncForm from '@spa/components/AsyncComponents/Form/AsyncFormPopup.vue';
import FormInput from '@spa/components/KendoInputs/FormInput.vue';
import FormTextArea from '@spa/components/KendoInputs/FormTextArea.vue';
import FormDatePicker from '@spa/components/KendoInputs/FormDatePicker.vue';
import FormDropDown from '@spa/components/KendoInputs/FormDropDown.vue';
import { useStudentInterventionStore } from '@spa/stores/modules/studentintervention/useStudentInterventionStore.js';
import { storeToRefs } from 'pinia';
import FileUpload from '@agentportal/gteprocess/partials/FileUpload.vue';
import CoursesSelect from '@spa/modules/courses/CoursesSelect.vue';
import InterventionTypeSelect from '@spa/modules/interventiontype/InterventionTypeSelect.vue';
import SemesterSelect from '@spa/modules/semester/SemesterSelect.vue';
import EnumSelect from '@spa/components/AsyncComponents/Select/EnumSelect.vue';
import FormValidationWrapper from '@spa/components/KendoInputs/FormValidationWrapper.vue';
import InterventionStrategySelect from '@spa/modules/interventionstrategy/InterventionStrategySelect.vue';
import { DropDownList } from '@progress/kendo-vue-dropdowns';
import FormUpload from '@spa/components/KendoInputs/FormUpload.vue';
import TeacherSelect from '@spa/modules/timesheet-approval/partials/TeacherSelect.vue';
import { Field, FormElement } from '@progress/kendo-vue-form';
import StudentSelect from '@spa/modules/register-improvements/partials/StudentSelect.vue';

const store = useStudentInterventionStore();
const { formData } = storeToRefs(store);
</script>
