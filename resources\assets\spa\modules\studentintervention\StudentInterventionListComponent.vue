<template>
    <AsyncGrid
        :columns="columns"
        :store="store"
        :has-create-action="true"
        :has-export="false"
        :has-filters="false"
        :gridStyle="{
            height: '100%',
        }"
        @handel-reset="
            () => {
                initFilters();
            }
        "
        :actions="['delete', 'view']"
    >
        <template #body-cell-intervention_types="{ props }">
            <badge
                v-for="(intervention_type, index) in props.dataItem?.intervention_types ?? []"
                :key="index"
                :variant="'secondary'"
                class="m-[2px]"
            >
                {{ intervention_type }}
            </badge>
        </template>
        <template #body-cell-intervention_strategies="{ props }">
            <badge
                v-for="(intervention_strategy, index) in props.dataItem?.intervention_strategies ??
                []"
                :key="index"
                :variant="'secondary'"
                class="m-[2px]"
            >
                {{ intervention_strategy }}
            </badge>
        </template>
    </AsyncGrid>
    <StudentInterventionForm />
</template>

<script setup>
import AsyncGrid from '@spa/components/AsyncComponents/Grid/AsyncGrid.vue';
import { ref, onMounted } from 'vue';
import { useStudentInterventionStore } from '@spa/stores/modules/studentintervention/useStudentInterventionStore.js';
import StudentInterventionForm from '@spa/modules/studentintervention/StudentInterventionForm.vue';
import Badge from '@spa/components/badges/Badge.vue';

const store = useStudentInterventionStore();

const columns = [
    {
        field: 'recorded_date',
        title: 'Recorded Date',
        width: '200px',
        formatCellData: (val) => {
            return val ?? 'N/A';
        },
    },
    {
        field: 'course',
        title: 'Course ID',
        width: '200px',
        formatCellData: (val) => {
            return val?.course_code ?? 'N/A';
        },
    },
    {
        field: 'course',
        title: 'Course ID',
        width: '200px',
        formatCellData: (val) => {
            return val?.course_name ?? 'N/A';
        },
    },
    {
        field: 'semester',
        title: 'Semester',
        width: '200px',
        formatCellData: (val) => {
            return val?.semester_name ?? 'N/A';
        },
    },
    {
        field: 'student',
        title: 'Student ID',
        width: '200px',
        formatCellData: (val) => {
            return val?.generated_stud_id ?? 'N/A';
        },
    },
    {
        field: 'student',
        title: 'Name',
        width: '200px',
        formatCellData: (val) => {
            return val?.full_name ?? 'N/A';
        },
    },
    {
        field: 'due_date',
        title: 'Due Date',
        width: '200px',
        formatCellData: (val) => {
            return val ?? 'N/A';
        },
    },
    {
        field: 'outcome_date',
        title: 'Outcome Date',
        width: '200px',
        formatCellData: (val) => {
            return val ?? 'N/A';
        },
    },
    {
        field: 'intervention_types',
        name: 'intervention_types',
        title: 'Intervention Type',
        width: '200px',
        replace: true,
    },
    {
        field: 'intervention_strategies',
        name: 'intervention_strategies',
        title: 'Strategy',
        width: '200px',
        replace: true,
    },
    {
        field: 'teacher',
        title: 'Escalated To',
        width: '200px',
        formatCellData: (val) => {
            return val?.full_name ?? 'N/A';
        },
    },
    {
        field: 'case_status',
        title: 'Status',
        width: '200px',
        formatCellData: (val) => {
            return val == 1 ? 'Case Open' : 'Case Close';
        },
    },
];

const initFilters = () => {
    store.filters = {};
};

onMounted(() => {
    initFilters();
});
</script>
