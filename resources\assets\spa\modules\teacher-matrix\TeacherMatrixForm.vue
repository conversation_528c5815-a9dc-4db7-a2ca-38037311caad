<template>
    <AsyncForm
        :form-config="formFields"
        :initial-values="store.formData"
        @submit="onSubmit"
        @submitcheck="handleSubmitclick"
        @change="onChange"
        :type="'kendo'"
        :layout="{ cols: 2, gap: 16 }"
        :orientation="'vertical'"
        :dialogTitle="'Add Teacher Matrix'"
        :override="true"
        :store="store"
        :max-width="'600px'"
    >
        <TeacherMatrixFormContent :store="store" />
    </AsyncForm>
</template>
<script setup>
import { reactive } from 'vue';
import { useTeacherMatrixStore } from '@spa/stores/modules/teacher/useTeacherMatrixStore.js';
import AsyncForm from '@spa/components/AsyncComponents/Form/AsyncFormPopup.vue';
import TeacherMatrixFormContent from '@spa/modules/teacher-matrix/partials/TeacherMatrixFormContent.vue';

const store = useTeacherMatrixStore();

const handleSubmitclick = (value) => {
    store.isFormValid = value;
};
</script>
<style lang=""></style>
