<template>
    <AsyncGrid
        :columns="columns"
        :store="store"
        :show-refresh-button="false"
        :show-filter-button="false"
        :add-permissions="null"
        :enableSelection="true"
        :has-create-action="true"
        :create-btn-label="'Add Teacher'"
        :has-filters="false"
        :has-export="true"
        :has-floating-actions="true"
        :has-bulk-delete="true"
        :gridStyle="{
            height: '100%',
        }"
        @handel-reset="
            () => {
                initFilters();
                store.selected = [];
            }
        "
        :actions="['delete']"
    >
        <template #bulk-actions> </template>
        <template #body-cell-logged_date="{ props }">
            <FormatDate :date="props.dataItem?.updated_at" />
        </template>
        <template #body-cell-username="{ props }">
            {{ props.dataItem?.username }}
        </template>
        <template #body-cell-is_active="{ props }">
            <Badge
                :dark="true"
                :variant="parseInt(props.dataItem?.is_active) === 1 ? 'success' : 'error'"
                >{{ parseInt(props.dataItem?.is_active) === 1 ? 'Active' : 'Deactive' }}</Badge
            >
        </template>
        <template #body-cell-full_name="{ props }">
            <Button
                variant="link"
                @click="router.visit(route('spa.teacher-profile', [props.dataItem.id]))"
            >
                {{ props.dataItem?.full_name }}
            </Button>
        </template>
        <template #actions="{ row }">
            <grid-action-button
                :tooltipTitle="'View Profile'"
                @click="router.visit(route('spa.teacher-profile', [row.id]))"
            >
                <icon name="user" width="20" height="20" />
            </grid-action-button>
        </template>
    </AsyncGrid>
    <TeacherForm />
</template>

<script setup>
import { useTeacherStore } from '@spa/stores/modules/teacher/useTeacherStore.js';
import AsyncGrid from '@spa/components/AsyncComponents/Grid/AsyncGrid.vue';
import { onMounted } from 'vue';
import FormatDate from '@spa/components/FormatDate.vue';
import { Tooltip } from '@progress/kendo-vue-tooltip';
import Badge from '@spa/components/badges/Badge.vue';
import TeacherForm from '@spa/modules/teacher/TeacherForm.vue';
import Button from '@spa/components/Buttons/Button.vue';
import { router } from '@inertiajs/vue3';
import GridActionButton from '@spa/components/AsyncComponents/Grid/Partials/GridActionButton.vue';

const store = useTeacherStore();
const columns = [
    {
        name: 'staff_number',
        title: 'Staff Number',
        field: 'staff_number',
        sortable: true,
    },
    {
        name: 'full_name',
        title: 'Teacher/Trainee',
        field: 'first_name',
        width: 200,
        sortable: true,
        replace: true,
    },
    {
        name: 'username',
        title: 'Username',
        field: 'user_id',
        sortable: true,
        width: 200,
        canHide: true,
        replace: true,
    },
    {
        name: 'email',
        title: 'Email Address',
        field: 'email',
        sortable: true,
        width: 200,
        canHide: true,
    },
    {
        name: 'is_active',
        title: 'Status',
        field: 'is_active',
        sortable: true,
        replace: true,
    },
    {
        name: 'address',
        title: 'Address',
        field: 'address',
        sortable: true,
        width: 150,
    },
    {
        name: 'city_town',
        title: 'City Town',
        field: 'city_town',
        sortable: true,
    },
];
const initFilters = () => {
    store.filters = {};
    // Reset pagination to page 1 when filters are reset
    store.serverPagination.page = 1;
};

const initInertiaData = () => {};
onMounted(() => {
    initFilters();
});
</script>
