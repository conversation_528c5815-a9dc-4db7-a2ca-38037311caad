<script setup>
import AsyncSelect from '@spa/components/AsyncComponents/Select/AsyncSelect.vue';
import { useCoursesStore } from '@spa/stores/modules/courses/useCoursesStore.js';
import { FieldWrapper } from '@progress/kendo-vue-form';
import { Error, Hint, Label } from '@progress/kendo-vue-labels';
import { computed, onMounted } from 'vue';
const props = defineProps({
    modelValue: [String, Number, Array, Object],
    label: String,
    className: String,
    optionValue: {
        type: String,
        default: 'id',
    },
    optionLabel: {
        type: String,
        default: 'name',
    },
    disabled: Boolean,
    clearable: {
        type: Boolean,
        default: true,
    },
    multiple: {
        type: Boolean,
        default: false,
    },
    readonly: Boolean,
    useChips: {
        type: Boolean,
        default: false,
    },
    placeholder: {
        type: String,
        default: 'Select Courses',
    },
    hasCreateAction: {
        type: Boolean,
        default: false,
    },
    valid: {
        type: Boolean,
        default: true,
    },
    validationMessage: {
        type: String,
        default: '',
    },
    indicaterequired: {
        type: Boolean,
        default: false,
    },
});
const emit = defineEmits(['update:modelValue']);

const vModel = computed({
    get() {
        return props.modelValue;
    },
    set(val) {
        emit('update:modelValue', val);
    },
});
const store = useCoursesStore();

const initFilters = () => {
    store.filters = {
        status: 1,
    };
};

onMounted(() => {
    initFilters();
});
</script>

<template>
    <AsyncSelect
        :label="label"
        :className="className"
        :optionValue="optionValue"
        :optionLabel="optionLabel"
        :disabled="disabled"
        :store="store"
        v-model="vModel"
        :clearable="clearable"
        :multiple="multiple"
        :readonly="readonly"
        :useChips="useChips"
        :placeholder="placeholder"
        :hasCreateAction="hasCreateAction"
        :valid="valid"
        :validationMessage="validationMessage"
        :indicaterequired="indicaterequired"
    />
</template>
