<template>
    <AsyncGrid
        :columns="columns"
        :store="store"
        :show-refresh-button="false"
        :show-filter-button="false"
        :add-permissions="null"
        :enableSelection="false"
        :has-create-action="false"
        :has-export="false"
        :has-filters="true"
        :filter-columns="2"
        :has-actions="false"
        :gridStyle="{
            height: '100%',
        }"
        @handel-reset="handleReset"
    >
        <template #filters>
            <FilterBlockWrapper label="Financial Year">
                <FinancialYearSelect v-model="store.filters.financialYear" />
            </FilterBlockWrapper>
            <FilterBlockWrapper label="Pay Period">
                <PayPeriodSelect
                    v-model="store.filters.payPeriod"
                    :financial-year="store.filters.financialYear"
                    :disabled="!store.filters.financialYear"
                />
            </FilterBlockWrapper>
            <FilterBlockWrapper label="Teacher">
                <TeacherSelect v-model="store.filters.teacher" />
            </FilterBlockWrapper>
        </template>

        <template #body-cell-teacher_name="{ props }">
            {{ props.dataItem.teacher_name }}
        </template>
        <template #body-cell-total_class="{ props }">
            {{ props.dataItem.total_class }}
        </template>
        <template #body-cell-extra_activity="{ props }">
            {{ props.dataItem.extra_activity }}
        </template>
        <template #body-cell-actions="{ props }">
            <div class="flex justify-start">
                <Tooltip
                    :anchor-element="'target'"
                    :position="'top'"
                    :parentTitle="true"
                    :tooltipClassName="'flex !p-1'"
                    :class="'mr-2'"
                >
                    <Button
                        variant="ghost"
                        class="flex cursor-pointer justify-start px-0"
                        @click="handleViewClassTimesheet(props.dataItem)"
                    >
                        <svg
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <path
                                d="M10 15L12 17L14 15"
                                stroke="currentColor"
                                stroke-width="2"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                            />
                            <path
                                d="M12 9V17"
                                stroke="currentColor"
                                stroke-width="2"
                                stroke-linecap="round"
                                stroke-linejoin="round"
                            />
                        </svg>
                    </Button>
                </Tooltip>
                <Tooltip
                    :anchor-element="'target'"
                    :position="'top'"
                    :parentTitle="true"
                    :tooltipClassName="'flex !p-1'"
                    :class="'mr-2'"
                >
                    <Button
                        variant="ghost"
                        class="flex cursor-pointer justify-start px-0"
                        @click="handleViewExtraActivityTimesheet(props.dataItem)"
                    >
                        <svg
                            width="24"
                            height="24"
                            viewBox="0 0 24 24"
                            fill="none"
                            xmlns="http://www.w3.org/2000/svg"
                        >
                            <rect
                                x="3"
                                y="3"
                                width="7"
                                height="7"
                                rx="1"
                                stroke="currentColor"
                                stroke-width="2"
                            />
                            <rect
                                x="14"
                                y="3"
                                width="7"
                                height="7"
                                rx="1"
                                stroke="currentColor"
                                stroke-width="2"
                            />
                            <rect
                                x="14"
                                y="14"
                                width="7"
                                height="7"
                                rx="1"
                                stroke="currentColor"
                                stroke-width="2"
                            />
                            <rect
                                x="3"
                                y="14"
                                width="7"
                                height="7"
                                rx="1"
                                stroke="currentColor"
                                stroke-width="2"
                            />
                        </svg>
                    </Button>
                </Tooltip>
            </div>
        </template>
    </AsyncGrid>

    <!-- Approved Timesheet Modal -->
    <ApprovedTimesheetModal
        :visible="modalVisible"
        :periodId="selectedPeriodId"
        :staffId="selectedStaffId"
        :type="selectedType"
        @close="handleCloseModal"
    />
</template>

<script setup>
import { ref, watch } from 'vue';
import { useTimesheetApprovedStore } from '@spa/stores/modules/timesheet-approved/timesheetApprovedStore.js';
import AsyncGrid from '@spa/components/AsyncComponents/Grid/AsyncGrid.vue';
import FilterBlockWrapper from '@spa/components/AsyncComponents/Grid/Partials/FilterBlockWrapper.vue';
import FinancialYearSelect from './partials/FinancialYearSelect.vue';
import PayPeriodSelect from './partials/PayPeriodSelect.vue';
import TeacherSelect from './partials/TeacherSelect.vue';
import Button from '../../components/Buttons/Button.vue';
import { Tooltip } from '@progress/kendo-vue-tooltip';
import ApprovedTimesheetModal from './partials/ApprovedTimesheetModal.vue';

const store = useTimesheetApprovedStore();

// Modal state
const modalVisible = ref(false);
const selectedPeriodId = ref(null);
const selectedStaffId = ref(null);
const selectedType = ref('C');

// Grid columns
const columns = [
    {
        name: 'teacher_name',
        title: 'Teacher Name',
        field: 'teacher_name',
        sortable: true,
        replace: true,
        width: 200,
    },
    {
        name: 'total_class',
        title: 'Total Class',
        field: 'total_class',
        sortable: true,
        replace: true,
    },
    {
        name: 'extra_activity',
        title: 'Extra Activity',
        field: 'extra_activity',
        sortable: true,
        replace: true,
    },
    {
        name: 'actions',
        title: 'Actions',
        field: 'actions',
        sortable: false,
        replace: true,
    },
];

// Methods
const handleReset = () => {
    initFilters();
    store.selected = [];
};

const initFilters = () => {
    store.filters = {
        financialYear: null,
        payPeriod: null,
        teacher: null,
    };
    // Reset pagination to page 1 when filters are reset
    store.serverPagination.page = 1;
};

// Modal methods
const handleViewClassTimesheet = (dataItem) => {
    selectedPeriodId.value = dataItem.timesheet_period_id || store.filters.payPeriod;
    selectedStaffId.value = dataItem.staff_id || store.filters.teacher;
    selectedType.value = 'C'; // Class timesheet
    modalVisible.value = true;
};

const handleViewExtraActivityTimesheet = (dataItem) => {
    selectedPeriodId.value = dataItem.timesheet_period_id || store.filters.payPeriod;
    selectedStaffId.value = dataItem.staff_id || store.filters.teacher;
    selectedType.value = 'E'; // Extra activity timesheet
    modalVisible.value = true;
};

const handleCloseModal = () => {
    modalVisible.value = false;
    selectedPeriodId.value = null;
    selectedStaffId.value = null;
    selectedType.value = 'C';
};

// Watch for filter changes to fetch data
watch(
    () => [store.filters.financialYear, store.filters.payPeriod, store.filters.teacher],
    ([financialYear, payPeriod, teacher]) => {
        if (financialYear && payPeriod && teacher) {
            store.fetchPaged();
        }
    },
    { deep: true }
);
</script>
