<template>
    <AsyncGrid
        :columns="columns"
        :store="store"
        :show-refresh-button="true"
        :show-filter-button="true"
        :add-permissions="null"
        :enableSelection="true"
        :has-create-action="false"
        :has-export="false"
        :has-actions="false"
        :gridStyle="{
            height: '100%',
        }"
        @handel-reset="
            () => {
                initFilters();
                store.selected = [];
            }
        "
        :hasActions="false"
        :has-floating-actions="false"
    >
        <template #bulk-actions>
            <Button
                v-if="store.selected?.length > 0"
                class="tw-btn-primary !h-9 shadow-none"
                size="xs"
                @click="
                    () => {
                        store.formDialog = true;
                    }
                "
            >
                Verify USI ({{ store.selected?.length ?? 0 }})
            </Button>
            <USIExportButton />
        </template>
        <template #filters>
            <FilterBlockWrapper label="USI Filters">
                <USIFilterSelect v-model="store.filters.hasUsi" />
            </FilterBlockWrapper>
            <FilterBlockWrapper label="Status">
                <USIStatusFilterSelect v-model="store.filters.usiStatus" />
            </FilterBlockWrapper>
            <FilterBlockWrapper label="Course Type">
                <CourseTypeSelect v-model="store.filters.courseTypeIds" name="courseTypeIds" />
            </FilterBlockWrapper>
        </template>
        <template #body-cell-other_info="{ props }">
            <div class="p-2">
                <div><b>DOB:</b>{{ props.dataItem?.dob ?? 'N/A' }}</div>
                <div><b>Gender:</b>{{ props.dataItem?.gender ?? 'N/A' }}</div>
                <div><b>Email:</b>{{ props.dataItem?.email ?? 'N/A' }}</div>
            </div>
        </template>
        <template #body-cell-birthplace="{ props }">
            <EditableColumn
                v-model:rowForm="props.rowForm"
                :data-item="props.dataItem"
                :store="store"
                placeholder="City of Birth"
                column="birthplace"
            ></EditableColumn>
        </template>
        <template #body-cell-student_name="{ props }">
            {{ props.dataItem.student_name }}
        </template>
        <template #body-cell-usi="{ props }">
            <EditableColumn
                v-model:rowForm="props.rowForm"
                :data-item="props.dataItem"
                :store="store"
                placeholder="Enter USI"
                column="USI"
            ></EditableColumn>
        </template>
        <template #body-cell-is_usi_verified="{ props }">
            <span
                class="rounded-md p-2 text-center text-xs text-white"
                :class="{
                    'bg-green-500': props.dataItem?.is_usi_verified === 1,
                    'bg-red-500': props.dataItem?.is_usi_verified === 0,
                    'bg-gray-500': props.dataItem?.is_usi_verified === null,
                }"
            >
                <span v-if="props.dataItem?.is_usi_verified === 1"> Verified </span>
                <span v-else-if="props.dataItem?.is_usi_verified === 0"> Not Verified </span>
                <span v-else> N/A </span>
            </span>
        </template>
        <template #column-menu-student_name="{ props }">
            <async-grid-column-filter
                :column="props.column"
                :filterable="props.filterable"
                :filter="props.filter"
                :sortable="props.sortable"
                :sort="props.sort"
                :columns="columns"
                @sortchange="(e) => props.onSortchange(e)"
                @filterchange="(e) => props.onFilterchange(e)"
                @closemenu="(e) => props.onClosemenu(e)"
                @contentfocus="(e) => props.onContentfocus(e)"
                @columnssubmit="onColumnsSubmit"
                :type="'default'"
            >
            </async-grid-column-filter>
        </template>
    </AsyncGrid>
    <USIStudentForm />
</template>

<script setup>
import { useUsiStudentStore } from '@spa/stores/modules/usi/usiStudentStore.js';
import USIStudentForm from '@spa/modules/usi-verifications/USIStudentForm.vue';
import AsyncGrid from '@spa/components/AsyncComponents/Grid/AsyncGrid.vue';
import { ref, onMounted, watch } from 'vue';
import Button from '@spa/components/Buttons/Button.vue';
import EditableColumn from '@spa/components/AsyncComponents/Grid/Partials/EditableColumn.vue';
import FilterBlockWrapper from '@spa/components/AsyncComponents/Grid/Partials/FilterBlockWrapper.vue';
import USIFilterSelect from '@spa/modules/usi-verifications/partials/USIFilterSelect.vue';
import AsyncGridColumnFilter from '@spa/components/AsyncComponents/Grid/Partials/AsyncGridColumnFilter.vue';
import USIStatusFilterSelect from '@spa/modules/usi-verifications/partials/USIStatusFilterSelect.vue';
import CourseTypeSelect from '@spa/modules/courses/CourseTypeSelect.vue';
import USIExportButton from '@spa/modules/usi-verifications/partials/USIExportButton.vue';
const store = useUsiStudentStore();
const columns = [
    {
        name: 'id',
        title: 'Id',
        field: 'generated_stud_id',
        sortable: true,
    },
    {
        name: 'student_name',
        title: 'Student Name',
        field: 'first_name',
        sortable: true,
        columnFilter: false,
        replace: true,
    },
    {
        name: 'other_info',
        title: 'Other Info',
        field: 'other_info',
        sortable: false,
        replace: true,
    },
    {
        name: 'birthplace',
        title: 'Brith Place',
        field: 'birthplace',
        sortable: true,
        replace: true,
    },
    {
        name: 'usi',
        title: 'USI',
        field: 'USI',
        sortable: true,
        replace: true,
    },
    {
        name: 'is_usi_verified',
        title: 'USI Status',
        field: 'is_usi_verified',
        sortable: true,
        replace: true,
    },
];
const initFilters = () => {
    store.filters = {
        hasID: true,
        hasUsi: 'all',
        usiStatus: 'all',
        courseType: [],
    };
    // Reset pagination to page 1 when filters are reset
    store.serverPagination.page = 1;
};
onMounted(() => {
    initFilters();
});
</script>
