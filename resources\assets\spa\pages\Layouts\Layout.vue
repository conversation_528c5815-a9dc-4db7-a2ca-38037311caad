<script setup>
import { Link } from '@inertiajs/vue3';
import { twMerge } from 'tailwind-merge';

import { computed, onMounted } from 'vue';
import GlobalLoader from '@spa/components/Loader/GlobalLoader.vue';
import Container from '@spa/components/Container/Container.vue';

const props = defineProps({
    noSpacing: {
        type: Boolean,
        default: false,
    },
    pt: {
        type: Object,
        default: {},
    },
    loading: {
        type: Boolean,
        default: true,
    },
    layout: {
        type: String,
        default: 'default',
        validator: (value) => ['default', 'sidebar-right', 'full-width'].includes(value),
    },

    // Container settings
    maxWidth: {
        type: String,
        default: '1920px',
    },
    padding: {
        type: String,
        default: '0',
    },
    gap: {
        type: String,
        default: '1.5rem',
    },

    // Content settings
    title: {
        type: String,
        default: '',
    },

    // Visibility controls
    showHeader: {
        type: <PERSON>olean,
        default: false,
    },
    showTabs: {
        type: <PERSON><PERSON><PERSON>,
        default: false,
    },
    showFooter: {
        type: Boolean,
        default: false,
    },

    // Responsive settings
    responsive: {
        type: Boolean,
        default: true,
    },
});
onMounted(() => {});
const rootClass = computed(() => {
    return twMerge(
        'new-header__fixed w-full overflow-y-auto',
        props.noSpacing ? 'px-0' : 'px-6',
        props.pt.root
    );
});

const wrapperClass = computed(() => {
    return twMerge(props.noSpacing ? 'mt-0' : 'mt-4', props.pt.wrapper);
});
</script>

<template>
    <slot name="pageTitleContent">
        <!-- <div class="justify-start font-semibold text-white">SPA</div> -->
    </slot>
    <GlobalLoader :loading="loading" loadingText="">
        <Container
            :layout="layout"
            :max-width="maxWidth"
            :padding="padding"
            :gap="gap"
            :pt="{
                root: rootClass,
                content: wrapperClass,
            }"
            :responsive="responsive"
        >
            <template #tabs>
                <slot name="tabs" />
            </template>
            <template #header>
                <slot name="header" />
            </template>
            <template #default>
                <slot />
            </template>
            <template #footer>
                <slot name="footer" />
            </template>
        </Container>
        <!--        <main :class="rootClass">-->
        <!--            <article :class="wrapperClass">-->
        <!--                <slot />-->
        <!--            </article>-->
        <!--        </main>-->
    </GlobalLoader>
    <slot name="modalContent"></slot>
</template>
<style>
.gradientbackground {
    background: linear-gradient(270deg, #06b6d4 20.35%, #1e93ff 75.64%);
    /* margin-bottom: -3px; */
}
</style>
