<template lang="">
    <Layout :noSpacing="true" :loading="false">
        <template v-slot:pageTitleContent>
            <PageTitleContent :title="getHeaderTitle" :back="false" />
        </template>
        <template #tabs>
            <DocumentHeaderTabs
                :currentTab="activeTab || 'miscellaneous'"
                :filterquery="getFilterQuery"
                :loginType="loginType"
                v-if="tabDisplayFlag"
            />
        </template>
        <MediaManager
            :files="files"
            :folders="folders"
            :columns="columns"
            :parentId="parentId"
            :breadcrumbs="breadcrumbs"
            :loginType="loginType"
            :currentTab="activeTab || 'miscellaneous'"
            :pagination="$document.state.pageable"
            :isShowBookMarkSwich="isShowBookMarkSwich"
            :viewAccess="checkViewAccess"
            @upload="$document.onUpload"
            @changeSwitch="$document.onChangeSwitch"
            @createFolder="$document.onCreate"
            @bulkDelete="$document.onBulkDelete"
            @bulkMove="$document.onMove"
            @keywordSearch="keywordSearch"
            @viewAccess="$document.onViewAccess"
            @bulkStudentAccess="$document.onViewAccess"
            @bulkTeacherAccess="$document.onViewAccess"
            @breadcrumbClick="$document.onBreadcrumbClick"
            @bookmark="$document.onBookmark"
            @openFolder="$document.onOpenFolder"
            @isShowBookmark="$document.isShowBookmark"
            @rename="$document.onRename"
            @move="$document.onMove"
            @download="$document.onDownload"
            @delete="$document.onDelete"
            @dragMove="$document.onMove"
            :decryptItParentId="decryptItParentId"
            @changePage="handlePageChange"
            @sortChange="handleSortChange"
        />
    </Layout>
</template>
<script setup>
import { ref, computed, watch } from 'vue';

import MediaManager from '@spa/components/MediaManager/MediaManager.vue';
import Layout from '@spa/pages/Layouts/Layout.vue';
import PageTitleContent from '@spa/pages/Layouts/PageTitleContent.vue';
import useDocumentResource from '@spa/services/documents/documentsResource';
import { useDocumentStore } from '@spa/stores/modules/document.store';
import DocumentHeaderTabs from './partials/DocumentHeaderTabs.vue';

const documentStore = useDocumentStore();
// Props
const props = defineProps({
    data: {
        type: Object,
        default: () => ({}),
    },
    grid: {
        type: Object,
        default: () => ({}),
    },
});

const keyword = computed(() => documentStore.getSearchKeywordStore);
const columns = computed(() => props.data.grid.columns);
const files = computed(() => documentStore.getDocuments);
const folders = computed(() => props.data.folders);
const breadcrumbs = computed(() => props.data.breadcrumbs);
const parentId = computed(() => props.data.parentId);
const decryptItParentId = computed(() => props.data.decryptItParentId);
const propsFile = computed(() => props.data.grid.data);
const apibasepath = computed(() => props.data.apibasepath);
const loginType = computed(() => props.data.loginType || 'admin');
const tabDisplayFlag = computed(() => props.data.tabDisplayFlag || false);
const activeTab = computed(() => props.data.activeTab || 'miscellaneous');
const pagination = computed(() => props.data.grid.meta);

const $document = useDocumentResource(apibasepath.value, {
    filters: {
        p: props.data?.parentId || 0,
        search: props.query?.search || '',
        sort: props.query?.sort || '',
        dir: props.query?.dir || '',
        take: props.data.grid.meta.per_page || 10,
        page: props.data.grid.meta.current_page || 1,
        b: props.data?.isShowBookMarkSwich || false,
    },
    only: ['grid', 'data', 'breadcrumbs', 'filters'],
});

const getHeaderTitle = computed(() => {
    let titleMappings = {
        miscellaneous: 'Miscellaneous Documents',
        course: 'Course Documents',
        subject: 'Subject Documents',
    };

    return titleMappings[activeTab] || 'Documents';
});

watch(
    () => $document.state.filters,
    (val) => {
        //    $document.fetch();
    },
    { deep: true }
);
documentStore.setDocuments(props.data.grid.data);

const keywordSearch = (selected) => {
    if (selected.searchKeyword.length > 2) {
        $document.state.pageable.currentPage = 1;
        $document.state.pageable.totalItems = 10;
        $document.state.pageable.skip = (2 - 1) * 10;
        $document.onKeywordSearch(selected);
    }
    if (selected.searchKeyword.length <= 2) {
        $document.state.pageable.currentPage = props.data.grid.meta.current_page;
        $document.state.pageable.totalItems = props.data.grid.meta.total;
        $document.state.pageable.skip = (props.data.grid.meta.current_page - 1) * 10;
        documentStore.setDocuments(props.data.grid.data);
    }

    keyword.value = selected.searchKeyword;
    documentStore.setSearchKeywordStore(selected.searchKeyword);
};

watch(propsFile, (newFileData, oldFileData) => {
    documentStore.setDocuments(newFileData);
});

const handlePageChange = (page, take) => {
    $document.state.filters.page = page;
    $document.state.filters.take = take;
    $document.state.filters.p = parentId;
    $document.fetch();
};

const handleSortChange = (sort) => {
    $document.state.filters.sort = sort[0]?.field || null;
    $document.state.filters.dir = sort[0]?.dir || null;
    $document.fetch();
};

watch(pagination, (newFileData, oldFileData) => {
    $document.state.pageable.currentPage = newFileData.current_page;
    $document.state.pageable.totalItems = newFileData.total;
    $document.state.pageable.pageSizeValue = newFileData.per_page;
    $document.state.pageable.skip = (newFileData.current_page - 1) * 10;
});

const isShowBookMarkSwich = computed(() => props.data.isShowBookMarkSwich);
const checkViewAccess = computed(() => {
    if (
        loginType.value == 'admin' &&
        (activeTab.value == 'course' || activeTab.value == 'subject')
    ) {
        return true;
    } else {
        return false;
    }
});
</script>
<style lang=""></style>
