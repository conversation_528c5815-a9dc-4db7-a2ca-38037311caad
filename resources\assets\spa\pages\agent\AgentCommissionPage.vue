<script setup>
import Layout from '@spa/pages/Layouts/Layout';
import { Head } from '@inertiajs/vue3';
import PageTitleContent from '@spa/pages/Layouts/PageTitleContent.vue';
import AgentCommissionListComponent from '@spa/modules/agent-commission/AgentCommissionListComponent.vue';
</script>
<template>
    <Layout :no-spacing="true">
        <Head title="Agents List" />
        <template v-slot:pageTitleContent>
            <PageTitleContent title="Agents Commission List" :back="false" />
        </template>
        <div class="h-screen-header flex flex-col px-8 py-6">
            <AgentCommissionListComponent />
        </div>
    </Layout>
</template>

<style scoped></style>
