<script setup>
import Layout from '@spa/pages/Layouts/Layout';
import { Head } from '@inertiajs/vue3';
import PageTitleContent from '@spa/pages/Layouts/PageTitleContent.vue';
import AgentViewComponent from '@spa/modules/agent-profile/AgentViewComponent.vue';
</script>
<template>
    <Layout :no-spacing="true" :loading="false">
        <Head title="Agents List" />
        <template v-slot:pageTitleContent>
            <PageTitleContent title="Agents List" :back="false" />
        </template>
        <div class="h-screen-header flex flex-col">
            <AgentViewComponent />
        </div>
    </Layout>
</template>

<style scoped></style>
