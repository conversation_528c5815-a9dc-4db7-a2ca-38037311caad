<template lang="">
    <Layout :noSpacing="true" :actionSticky="true" :loading="false">
        <template v-slot:pageTitleContent>
            <PageTitleContent :title="'Mailbox - Inbox'" :back="false" />
        </template>
        <template #tabs>
            <MailboxHeaderTabs :currentTab="data.tab || 'inbox'" :filterquery="getFilterQuery" />
        </template>
        <div class="space-y-4 px-4 py-6 md:px-8">
            <HeaderTab
                :filters="getFilters"
                :action="data.action"
                :hastextsearch="true"
                :search-text="resource.state?.filters?.search || ''"
                @filter="updateFilter"
                @compose="handleToggle"
                :isRequest="true"
                :agentData="data.agentData"
                :variant="'secondary'"
            />
            <PreviewPane
                :show="showPreview"
                :total="getTotalItems"
                :current-index="getCurrentRecordIndex"
                :identifier="getIdentifier"
                @close-preview="handlePreviewClose"
                @next="goNext"
                @prev="goPrev"
            >
                <template #content>
                    <RequestGrid
                        :data="gridData"
                        :columns="getColumns"
                        :pagination="this.resource.state.pageable"
                        @sort="sortDataHandler"
                        @changepage="changePageHandler"
                        v-if="loadGrid"
                        @viewDetail="handleViewDetail"
                        :actionSticky="true"
                        :isTableCellHeight="true"
                        :isTableHeadHeight="true"
                        :selectedRowIndex="gridDataPreviewIndex"
                    />
                </template>
                <template #preview>
                    <MessageLogPreview :message="previewItem" />
                </template>
            </PreviewPane>
        </div>
        <RequestDrawer
            :data="formData"
            :user="data.userInfo || {}"
            @saved="refreshGrid"
            :visible="requestDrawerVisible"
            @close="handleClose"
        />
    </Layout>
</template>
<script>
import { watch } from 'vue';
import Layout from '@spa/pages/Layouts/Layout.vue';
import PageTitleContent from '@spa/pages/Layouts/PageTitleContent.vue';
import Dropdowns from '@agentportal/payments/partials/Dropdowns.vue';
import RequestDrawer from '@agentportal/requests/partials/RequestDrawer.vue';
import { IconEdit24Regular } from '@iconify-prerendered/vue-fluent';
import IconInput from '@spa/components/IconInput.vue';
import Button from '@spa/components/Buttons/Button.vue';
import HeaderTab from '@agentportal/payments/partials/HeaderTab.vue';
import {
    agentsResource,
    setPagination,
    prepareAgentMailboxData,
} from '@spa/services/agent/agentsResource.js';
import RequestGrid from '@agentportal/common/CommonGrid.vue';
import MailboxHeaderTabs from './partials/MailboxHeaderTabs.vue';
import PreviewPane from '@spa/components/KendoGrid/PreviewPane.vue';
import Badge from '@spa/components/badges/Badge.vue';
import MessageLogPreview from '@spa/pages/agentportal/common/MessageLogPreview.vue';

export default {
    setup(props) {
        const resource = agentsResource({
            filters: props.query || {},
            only: ['grid', 'data', 'filters', 'formData'],
        });
        watch(
            () => resource.state.filters,
            (val) => {
                resource.fetch();
            },
            { deep: true }
        );
        return {
            resource,
        };
    },
    props: {
        data: { type: Object, default: {} },
        formData: { type: Object, default: {} },
        grid: { type: Object, default: {} },
        filters: { type: Object, default: {} },
        query: Object,
    },
    components: {
        Layout,
        PageTitleContent,
        RequestGrid,
        'search-input': IconInput,
        Button,
        'icon-pencil': IconEdit24Regular,
        Dropdowns,
        RequestDrawer,
        HeaderTab,
        MailboxHeaderTabs,
        PreviewPane,
        Badge,
        MessageLogPreview,
    },
    data() {
        return {
            loadGrid: false,
            gridData: [],
            allColumns: [],
            requestDrawerVisible: false,
            showPreview: false,
            previewItem: {},
            gridDataPreviewIndex: null,
            selectPreviewItem: false,
            gridDataLength: this.resource.state.pageable,
        };
    },
    mounted() {
        this.gridData = this.prepareAgentMailboxData(this.grid.report.data);
        this.setPagination(this.resource, this.grid.report.meta);
        this.allColumns = this.grid?.columns || [];
        this.loadGrid = true;
    },
    computed: {
        getFilters() {
            return this.filters || {};
        },
        getFilterQuery() {
            const params = this.resource.state.filters;
            return Object.fromEntries(
                Object.entries(params).filter(([key, value]) => value !== null && value !== '')
            );
        },
        getColumns() {
            return this.allColumns;
        },
        getTotalItems() {
            return this.resource?.state?.pageable?.totalItems || 0;
        },
        getCurrentRecordIndex() {
            return this.gridDataPreviewIndex || 0;
        },
        getIdentifier() {
            return this.previewItem?.identifier || null;
        },
        getStartIndex() {
            const itemsPerPage = this.resource?.state?.pageable?.pageSizeValue || 0;
            const currentPage = this.resource?.state?.pageable?.currentPage || 1;
            return itemsPerPage * (currentPage - 1) + 0;
        },
        getEndIndex() {
            const totalItems = this.resource?.state?.pageable?.pageSizeValue || 0;
            const itemsPerPage = this.resource?.state?.pageable?.pageSizeValue || 0;
            const currentPage = this.resource?.state?.pageable?.currentPage || 1;
            const lastIndex = itemsPerPage * (currentPage - 1) + (itemsPerPage - 1);
            return lastIndex > this.getTotalItems ? this.getTotalItems : lastIndex;
        },
    },
    methods: {
        prepareAgentMailboxData,
        setPagination,
        sortDataHandler(sort) {
            this.resource.state.filters.sort = sort[0]?.field || null;
            this.resource.state.filters.dir = sort[0]?.dir || null;
        },
        changePageHandler(page, take) {
            this.resource.state.filters.page = page;
            this.resource.state.filters.take = take;
            this.handlePreviewClose();
        },
        updateFilter(filters) {
            Object.keys(filters).reduce((acc, key) => {
                const value = filters[key];
                // If the value is an array, convert it to a comma-separated string
                this.resource.state.filters[key] = Array.isArray(value) ? value.join(',') : value;
                return acc;
            }, {});
            this.resource.state.filters.page = 1;
        },
        refreshGrid() {
            this.resource.fetch();
        },
        handleToggle() {
            this.requestDrawerVisible = !this.requestDrawerVisible;
        },
        handleClose() {
            this.requestDrawerVisible = false;
        },
        handleViewDetail(item, dataIndex) {
            this.gridDataPreviewIndex = dataIndex - 1;
            this.showPreview = true;
            this.previewItem = item;
            const index = this.gridData.findIndex((record) => record.id == item.id);
            if (index >= 0) {
                const itemsPerPage = this.resource?.state?.pageable?.pageSizeValue || 0;
                const currentPage = this.resource?.state?.pageable?.currentPage || 1;
                this.gridDataPreviewIndex = itemsPerPage * (currentPage - 1) + index;
                this.updateViewedStatus(item);
            } else {
                this.gridDataPreviewIndex = null;
            }
        },
        handlePreviewClose() {
            this.showPreview = false;
            this.previewItem = {};
            this.gridDataPreviewIndex = null;
        },
        goNext() {
            console.log('go next', this.getEndIndex, this.gridDataPreviewIndex);
            if (this.getEndIndex > this.gridDataPreviewIndex) {
                //if the index is out of page scope
                this.gridDataPreviewIndex++;
                this.previewItem = this.gridData[this.gridDataPreviewIndex];
            } else if (this.gridDataPreviewIndex <= this.getTotalItems) {
                //load next page and then
                this.selectPreviewItem = true;
                this.gridDataPreviewIndex++;
                this.resource.state.filters.page++;
            }
        },
        goPrev() {
            if (this.getStartIndex < this.gridDataPreviewIndex) {
                this.gridDataPreviewIndex--;
                this.previewItem = this.gridData[this.gridDataPreviewIndex];
            } else if (this.gridDataPreviewIndex > 0) {
                //load next page and then
                this.selectPreviewItem = true;
                this.gridDataPreviewIndex--;
                this.resource.state.filters.page--;
            }
        },
        async updateViewedStatus(item) {
            let isSuccess = false;
            try {
                if (item.new_flag == 0) return;
                const resp = await $http.post(
                    route('spa.agent.viewmailbox'),
                    {
                        id: item.id,
                    },
                    {
                        forceFormData: true,
                    }
                );
                if (resp && resp.success === 1) {
                    isSuccess = true;
                }
            } catch (error) {
                console.log(error);
            } finally {
                if (isSuccess) {
                    const dataIndex = this.gridData.findIndex((record) => record.id == item.id);
                    if (dataIndex >= 0) {
                        this.gridData[dataIndex]['new_flag'] = 0;
                    }
                }
            }
        },
    },
    watch: {
        grid: {
            handler(newval, oldval) {
                this.gridData = this.prepareAgentMailboxData(newval.report.data);
                this.setPagination(this.resource, newval.report.meta);
                this.allColumns = newval.columns || [];
                if (this.selectPreviewItem == true) {
                    const itemsPerPage = this.resource?.state?.pageable?.pageSizeValue || 0;
                    const currentPage = this.resource?.state?.pageable?.currentPage || 1;
                    const relativeIndex =
                        this.gridDataPreviewIndex - (currentPage - 1) * itemsPerPage;
                    this.previewItem = this.gridData[relativeIndex] || {};
                }
            },
            deep: true,
        },
        gridData: {
            handler(newval, oldval) {
                this.gridDataLength = newval.length || 0;
            },
            deep: true,
        },
    },
};
</script>
<style lang=""></style>
