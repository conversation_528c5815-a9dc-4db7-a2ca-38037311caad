<template lang="">
    <Layout :noSpacing="true" :actionSticky="true" :loading="false">
        <template v-slot:pageTitleContent>
            <PageTitleContent :title="'Payments - Approved Commission'" :back="false" />
        </template>
        <template #tabs>
            <PaymentHeaderTabs :currentTab="data.currentTab || 'approvedCommission'" />
        </template>
        <div class="space-y-4 px-4 py-6 md:px-8">
            <HeaderTab
                :gridData="gridData"
                :filters="getFilters"
                :hastextsearch="true"
                :action="data.action"
                :actions="getActionBtns"
                @filter="updateFilter"
                @compose="handleToggle"
                :isRequest="false"
                @export="handleExport"
                :variant="'primary'"
                :width="'w-[300px]'"
                :dateRangeWidth="'w-[300px]'"
                :showPresetFilter="true"
            />
            <ApprovedCommissionGrid
                :selectable="true"
                :data="gridData"
                :columns="getColumns"
                :pagination="this.resource.state.pageable"
                :sort="getSortInfo"
                :hasSelect="hasSelectColumn"
                @sort="sortDataHandler"
                @changepage="changePageHandler"
                v-if="loadGrid"
                :actionSticky="false"
                :isTableCellHeight="true"
                :isTableHeadHeight="true"
            />
        </div>
    </Layout>
</template>
<script>
import { watch } from 'vue';
import Layout from '@spa/pages/Layouts/Layout.vue';
import PageTitleContent from '@spa/pages/Layouts/PageTitleContent.vue';
import HeaderTab from '@agentportal/payments/partials/HeaderTab.vue';
import {
    agentsResource,
    setPagination,
    preparePaymentHistoryGridData,
} from '@spa/services/agent/agentsResource.js';
import ApprovedCommissionGrid from '@agentportal/common/CommonGrid.vue';
import PaymentHeaderTabs from './partials/PaymentHeaderTabs.vue';
import { routeHistory } from '@spa/helpers/routeHistory';

export default {
    setup(props) {
        const resource = agentsResource({
            filters: props.query || {},
            only: ['grid', 'filters', 'data'],
        });
        watch(
            () => resource.state.filters,
            (val) => {
                resource.fetch();
            },
            { deep: true }
        );
        return {
            resource,
        };
    },
    props: {
        data: { type: Object, default: {} },
        formData: { type: Object, default: {} },
        grid: { type: Object, default: {} },
        filters: { type: Object, default: {} },
        query: Object,
    },
    components: {
        Layout,
        PageTitleContent,
        ApprovedCommissionGrid,
        HeaderTab,
        PaymentHeaderTabs,
    },
    data() {
        return {
            loadGrid: false,
            gridData: [],
            allColumns: [],
            visible: false,
            headerActions: [
                {
                    icon: 'downloadIcon',
                    action: 'export',
                    value: 'excel',
                    title: 'Export (xls)',
                },
            ],
        };
    },
    created() {
        routeHistory.previousRoute = window.location.pathname;
    },
    mounted() {
        this.gridData = this.preparePaymentHistoryGridData(this.grid.report.data);
        this.setPagination(this.resource, this.grid.report.meta);
        this.allColumns = this.grid?.columns || [];
        this.loadGrid = true;
    },
    computed: {
        getFilters() {
            return this.filters || {};
        },
        getColumns() {
            return this.allColumns;
        },
        getSearchText() {
            return this.resource.state.filters.search || '';
        },
        getActionBtns() {
            return this.headerActions;
        },
        hasSelectColumn() {
            return this.grid?.selectable || false;
        },
    },
    methods: {
        preparePaymentHistoryGridData,
        setPagination,
        sortDataHandler(sort) {
            this.resource.state.filters.sort = sort[0]?.field || null;
            this.resource.state.filters.dir = sort[0]?.dir || null;
        },
        changePageHandler(page, take) {
            this.resource.state.filters.page = page;
            this.resource.state.filters.take = take;
        },
        updateFilter(filters) {
            Object.keys(filters).reduce((acc, key) => {
                const value = filters[key];
                // If the value is an array, convert it to a comma-separated string
                this.resource.state.filters[key] = Array.isArray(value) ? value.join(',') : value;
                return acc;
            }, {});
            this.resource.state.filters.page = 1;
        },
        refreshGrid() {
            this.resource.fetch();
        },
        handleToggle() {
            this.visible = !this.visible;
        },
        handleExport($e) {
            const selectedIds = this.gridData
                .filter((item) => item.selected)
                .map((item) => item.record_id);
            this.resource.exportdata({
                export: $e,
                records: selectedIds.length > 0 ? selectedIds : 'all',
            });
        },
    },
    watch: {
        grid: {
            handler(newval, oldval) {
                this.gridData = this.preparePaymentHistoryGridData(newval.report.data);
                this.setPagination(this.resource, newval.report.meta);
                this.allColumns = newval.columns || [];
            },
            deep: true,
        },
    },
};
</script>
<style lang=""></style>
