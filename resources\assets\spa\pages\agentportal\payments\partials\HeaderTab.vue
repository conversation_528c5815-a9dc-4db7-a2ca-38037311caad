<template>
    <div class="space-y-4">
        <Card
            :pt="{ root: 'relative p-4 lg:p-6 rounded-lg' }"
            v-if="loadFilters && filters && filters.length > 0"
        >
            <template #content>
                <div
                    class="flex flex-col items-start justify-between gap-x-24 md:flex-row md:items-center"
                >
                    <div class="grid grid-cols-1 gap-4 gap-x-20 md:grid-cols-2">
                        <template v-for="(filter, index) in filters" :key="index">
                            <div
                                class="flex items-center gap-4"
                                v-if="filter && filter.type == 'autocomplete'"
                            >
                                <div class="mb-1 min-w-[90px] text-gray-700" v-if="showLabel">
                                    {{ filter.label || '' }}:
                                </div>
                                <SearchStudent
                                    :student="filter.trace?.data || {}"
                                    @updated="handleFilerUpdate($event, filter)"
                                    :style="{ width: '100%' }"
                                />
                            </div>
                            <div
                                class="flex items-center gap-4"
                                v-if="filter && filter.type == 'dropdown'"
                            >
                                <div
                                    class="mb-1 min-w-[90px] max-w-[300px] text-gray-700"
                                    v-if="showLabel"
                                >
                                    {{ filter.label || '' }}:
                                </div>
                                <!-- {{ filter }} -->
                                <Dropdowns
                                    v-if="filter.multiple"
                                    :field="filter"
                                    :clearable="true"
                                    @updated="handleFilerUpdate($event, filter)"
                                    :width="width"
                                />
                                <div
                                    class="w-full max-w-[300px] truncate"
                                    v-else-if="!filter.multiple"
                                >
                                    <Dropdown
                                        :field="filter"
                                        @updated="handleFilerUpdate($event, filter)"
                                    />
                                </div>
                            </div>

                            <div
                                class="flex items-center gap-4"
                                v-if="filter && filter.type == 'radio'"
                            >
                                <div class="mb-1 min-w-[90px] text-gray-700" v-if="showLabel">
                                    {{ filter.label || '' }}:
                                </div>
                                <formradiogroup
                                    @change="handleFilerUpdate($event, filter)"
                                    :data-items="filter.options"
                                    :layout="'horizontal'"
                                    :valid="true"
                                    :value="filter?.trace || 'All'"
                                />
                            </div>
                            <div
                                class="flex items-center gap-4"
                                v-if="filter && filter.type == 'daterangewithtypefilter'"
                            >
                                <div class="mb-1 min-w-[90px] text-gray-700" v-if="showLabel">
                                    {{ filter.label || '' }}:
                                </div>
                                <DateRangePopover
                                    :picker="filter"
                                    :opens="filter?.opens || []"
                                    :pickerLabel="filter?.label"
                                    :selected="filter?.trace || []"
                                    @updated="handleFilerUpdate($event, filter)"
                                    class="h-9"
                                />
                            </div>
                            <div
                                class="flex items-center gap-4"
                                v-if="filter && filter.type == 'daterange'"
                            >
                                <div class="mb-1 min-w-[90px] text-gray-700" v-if="showLabel">
                                    {{ filter.label || '' }}:
                                </div>
                                <CustomDateRangePicker
                                    :picker="filter"
                                    @updated="handleFilerUpdate($event, filter)"
                                    :show="showDatePicker"
                                    class="h-9"
                                    :class="dateRangeWidth"
                                    @focus="showDatePicker = true"
                                    @close="handleDatePickerClose"
                                    :reset="reset"
                                    v-if="!showPresetFilter"
                                />
                                <!-- <PresetDateRangePicker @update:preset="handleFilerUpdate($event, filter)" v-if="showPresetFilter" /> -->
                                <PresetDateRangePicker
                                    :picker="filter"
                                    @update:preset="handlePresetUpdate"
                                    v-if="showPresetFilter"
                                />
                            </div>
                        </template>
                    </div>
                    <div>
                        <Button
                            variant="ghost"
                            class="flex min-w-32 cursor-pointer justify-start px-0"
                            @click="resetFilterValues"
                            :disabled="isAllFilterValuesNull"
                        >
                            <icon
                                :name="'sync'"
                                :fill="isAllFilterValuesNull ? '#69c0ff' : '#1890FF'"
                                :className="reset ? 'animate-spin' : ''"
                            />
                            <span> Reset Filters </span>
                        </Button>
                    </div>
                </div>
            </template>
        </Card>
        <template v-if="hastextsearch">
            <div class="flex flex-wrap items-center justify-between gap-4">
                <div v-if="hastextsearch">
                    <search-input
                        v-model.lazy="filterValues.search"
                        :pt="{ root: 'w-64 md:w-80 h-9' }"
                        placeholder="Enter keyword"
                        :debounce="300"
                        :autocomplete="'off'"
                    />
                </div>
                <div class="flex w-full flex-wrap items-center justify-between gap-3 md:w-auto">
                    <div v-for="action in actions" class="flex items-center" :key="action.id">
                        <Button
                            variant="secondary"
                            class="h-9"
                            @click="handleActionClick(action)"
                            :disabled="
                                !gridData || gridData.length === 0 || action.action === 'excel'
                            "
                        >
                            <file-icon name="xlsx" class="h-6 w-6 text-gray-500" />
                            <span>{{ action.title }}</span>
                        </Button>
                    </div>
                    <Button
                        class="tw-btn-secondary !h-9 shadow-none"
                        @click="handleComposeEmail"
                        size="xs"
                        v-if="isExcel"
                    >
                        <file-icon name="xlsx" class="h-6 w-6 text-gray-500" />
                        Export (xls)
                    </Button>
                    <CreateNewModal
                        :agentData="this.agentData"
                        :action="action"
                        v-if="!isStudentList && action"
                        :variant="variant"
                        :width="'50%'"
                    />
                    <Button
                        class="tw-btn-primary !h-9 shadow-none"
                        :disabled="shouldDisableInqueryButton"
                        @click="handleComposeEmail"
                        size="xs"
                        v-if="isRequest"
                    >
                        <icon :name="'mail'" :fill="'currentColor'" :width="'16'" :height="'16'" />
                        Compose Mail
                    </Button>
                </div>
            </div>
        </template>
    </div>
</template>
<script>
import { debounce, filter } from 'lodash';
import Dropdowns from '@agentportal/payments/partials/Dropdowns.vue';
import Dropdown from '@agentportal/payments/partials/Dropdown.vue';
import { IconArrowDownload24Regular, IconEdit24Regular } from '@iconify-prerendered/vue-fluent';
import IconInput from '@spa/components/IconInput.vue';
import Button from '@spa/components/Buttons/Button.vue';
import CustomDateRangePicker from '@spa/components/KendoInputs/CustomDateRangePicker.vue';
import PresetDateRangePicker from '@spa/components/KendoInputs/PresetDateRangePicker.vue';
import DateRangePopover from '@spa/components/Popover/DateRangePopover.vue';
import Card from '@spa/components/Card/Card.vue';
import CreateNewModal from '@agentportal/student/partials/CreateNewModal.vue';
import FormRadioGroup from '@spa/components/KendoInputs/FormRadioGroup.vue';
import SearchStudent from '@spa/components/filters/SearchStudent.vue';

export default {
    components: {
        'search-input': IconInput,
        Button,
        'icon-download': IconArrowDownload24Regular,
        Dropdowns,
        Dropdown,
        CustomDateRangePicker,
        PresetDateRangePicker,
        DateRangePopover,
        formradiogroup: FormRadioGroup,
        Card,
        CreateNewModal,
        'icon-pencil': IconEdit24Regular,
        SearchStudent,
    },
    data() {
        return {
            loadFilters: true,
            selectedOption: 'All',
            showLabel: true,
            filterValues: {},
            initBaseValue: false,
            showDatePicker: false,
            search: null,
            debouncedEmitFilter: debounce(function (newval) {
                if (newval.search === '') {
                    newval.search = null;
                }
                this.$emit('filter', newval);
            }, 300), // 300ms debounce delay
            reset: false,
            showPresetFilter: false,
        };
    },
    props: {
        filters: { type: Array, default: [] },
        hastextsearch: { type: Object, default: {} },
        searchText: { type: String, default: '' },
        actions: { type: Object, default: {} },
        agentData: { type: Object, default: {} },
        studentData: { type: Object, default: {} },
        gridData: { type: Object, default: {} },
        action: { type: String, default: null },
        isStudentList: { type: Boolean, default: false },
        isRequest: { type: Boolean, default: false },
        showPresetFilter: { type: Boolean, default: false },
        isExcel: { type: Boolean, default: false },
        variant: { type: String, default: 'secondary' },
        width: { type: String, default: '' },
        dateRangeWidth: { type: String, default: '' },
    },
    mounted() {
        this.setBaseValues();
        //this.initBaseValue = true;
    },
    computed: {
        isAllFilterValuesNull() {
            // return false;
            return Object.values(this.filterValues).every(
                (value) => value === null || value === ''
            );
        },
        shouldDisableInqueryButton() {
            if (this.studentData) {
                return false;
            }
            return true;
        },
    },
    methods: {
        handleFilerUpdate($e, filter) {
            console.log('Type', $e, filter);
            if (filter.type == 'autocomplete' && filter.field == 'student') {
                this.resetFilterValues();
                this.filterValues[filter.field] = $e.id;
            } else if (Array.isArray($e.value) || ($e.value && typeof $e.value === 'object')) {
                if (Array.isArray($e.value)) {
                    this.filterValues[$e.field] = $e.value.join(',');
                    //Object.assign(this.filterValues, {[$e.field] : updateFields});
                } else {
                    for (const [innerKey, innerValue] of Object.entries($e.value)) {
                        this.filterValues[innerKey] = innerValue;
                    }
                }
            } else {
                if ($e.field) {
                    this.filterValues[$e.field] = $e.value;
                } else {
                    this.filterValues[filter.field] = $e.value;
                }
            }
        },
        handlePresetUpdate(preset) {
            console.log('Preset', preset);
            // this.filterValues = { ...this.filterValues, ...preset.value };

            //
            const formattedDates = {};
            preset.value &&
                Object.keys(preset.value).forEach((key) => {
                    if (preset.value[key] instanceof Date) {
                        const date = preset.value[key];
                        const year = date.getFullYear();
                        const month = String(date.getMonth() + 1).padStart(2, '0');
                        const day = String(date.getDate()).padStart(2, '0');
                        formattedDates[key] = `${year}-${month}-${day}`;
                    } else {
                        formattedDates[key] = preset.value[key];
                    }
                });
            this.filterValues = {
                ...this.filterValues,
                ...formattedDates,
            };
            console.log(preset.field);
        },
        setBaseValues() {
            if (!this.filters || this.filters.length === undefined) return;
            const filters = {};
            Object.keys(this.filters).map((key) => {
                const filterItem = this.filters[key] || {};
                if (filterItem.field) {
                    if (filterItem.type == 'autocomplete') {
                        filters[filterItem.field] = filterItem.trace?.data?.id || null;
                    } else if (
                        Array.isArray(filterItem.field) ||
                        (filterItem.field && typeof filterItem.field === 'object')
                    ) {
                        Object.keys(filterItem.field).map((key) => {
                            filters[key] = filterItem.trace[key] || null;
                        });
                    } else {
                        filters[filterItem.field] = filterItem.trace;
                    }
                }
            });
            filters['search'] = this.searchText;
            Object.assign(this.filterValues, filters);
        },
        handleDatePickerClose() {
            this.showDatePicker = false;
        },
        resetFilterValues() {
            if (!this.filters || this.filters.length === undefined) return;
            const filters = {};
            this.resetFilterAnimation();
            Object.keys(this.filters).map((key) => {
                const filterItem = this.filters[key] || {};
                if (filterItem.field) {
                    if (
                        Array.isArray(filterItem.field) ||
                        (filterItem.field && typeof filterItem.field === 'object')
                    ) {
                        Object.keys(filterItem.field).map((key) => {
                            filters[key] = null;
                        });
                    } else {
                        filters[filterItem.field] = null;
                    }
                }
            });
            filters['search'] = '';
            Object.assign(this.filterValues, filters);
        },
        exportData() {
            this.filterValues['export'] = 'excel';
        },
        handleActionClick(btn) {
            this.$emit(btn.action, btn.value);
        },
        handleComposeEmail() {
            this.$emit('compose');
        },
        reloadFilters() {
            this.loadFilters = false;
            this.$nextTick(() => {
                this.loadFilters = true;
            });
        },
        resetFilterAnimation() {
            this.reset = true;
            setTimeout(() => {
                this.reset = false;
            }, 500);
        },
    },
    watch: {
        filterValues: {
            handler(newval, oldval) {
                if (this.initBaseValue) {
                    this.debouncedEmitFilter(newval);
                } else {
                    this.initBaseValue = true;
                }
            },
            deep: true,
        },
        filters: {
            handler(newval, oldval) {
                this.reloadFilters();
            },
            deep: true,
        },
    },
};
</script>
<style lang=""></style>
