<template lang="">
    <Layout :noSpacing="true" :loading="false" :actionSticky="true">
        <template v-slot:pageTitleContent>
            <PageTitleContent :title="'Student Applications - Offfered'" :back="false" />
        </template>
        <template #tabs>
            <OfferHeaderTabs :currentTab="'pending'" />
        </template>
        <div class="space-y-4 px-4 py-6 md:px-8">
            <HeaderTab
                :filters="getFilters"
                :hastextsearch="true"
                :actions="getActionBtns"
                :action="data.action"
                @filter="updateFilter"
            />
            <StudentOfferGrid
                :data="gridData"
                :type="'pending'"
                :pagination="this.resource.state.pageable"
                @sort="sortDataHandler"
                @changepage="changePageHandler"
            />
        </div>
    </Layout>
</template>
<script>
import { ref, computed, watch } from 'vue';
import Layout from '@spa/pages/Layouts/Layout.vue';
import PageTitleContent from '@spa/pages/Layouts/PageTitleContent.vue';
import { TabStrip } from '@progress/kendo-vue-layout';
import OfferHeaderTabs from '@agentportal/student/partials/OfferHeaderTabs.vue';
import StudentOfferGrid from '@agentportal/student/partials/StudentOfferGrid.vue';
import { IconArrowDownload24Regular } from '@iconify-prerendered/vue-fluent';
import IconInput from '@spa/components/IconInput.vue';
import Button from '@spa/components/Buttons/Button.vue';

import {
    agentsResource,
    setPagination,
    prepareStudentOfferGridData,
} from '@spa/services/agent/agentsResource.js';
import HeaderTab from '@agentportal/payments/partials/HeaderTab.vue';
import { routeHistory } from '@spa/helpers/routeHistory';

export default {
    setup(props) {
        const resource = agentsResource({
            filters: {
                search: props.query?.search || '',
                commission: props.filters?.trace?.commission || null,
                date: props.filters?.trace?.date || null,
                fromdate: props.filters?.trace?.fromdate || null,
                todate: props.filters?.trace?.todate || null,
                course: props.filters?.trace?.course || null,
                take: props.grid?.commission?.per_page || 10,
                page: props.grid?.commission?.current_page || 1,
            },
            only: ['grid', 'data'],
        });
        watch(
            () => resource.state.filters,
            (val) => {
                resource.fetch();
            },
            { deep: true }
        );
        return {
            resource,
        };
    },
    props: {
        data: { type: Object, default: {} },
        grid: { type: Object, default: {} },
        filters: { type: Object, default: {} },
    },
    data() {
        return {
            gridData: [],
            headerActions: [
                {
                    icon: 'downloadIcon',
                    action: 'export',
                    value: 'excel',
                    title: 'Export (xls)',
                },
            ],
        };
    },
    created() {
        routeHistory.previousRoute = window.location.pathname;
    },
    mounted() {
        this.gridData = this.prepareStudentOfferGridData(this.grid.students.data);
        this.setPagination(this.resource, this.grid.students.meta);
    },
    components: {
        Layout,
        PageTitleContent,
        OfferHeaderTabs,
        StudentOfferGrid,
        'search-input': IconInput,
        Button,
        'icon-download': IconArrowDownload24Regular,
        HeaderTab,
    },
    computed: {
        getFilters() {
            return this.filters || {};
        },
        getActionBtns() {
            return this.headerActions;
        },
    },
    methods: {
        prepareStudentOfferGridData,
        setPagination,
        sortDataHandler(sort) {
            this.resource.state.filters.sort = sort[0]?.field || null;
            this.resource.state.filters.dir = sort[0]?.dir || null;
        },
        changePageHandler(page, take) {
            this.resource.state.filters.page = page;
            this.resource.state.filters.take = take;
        },
        updateFilter(filters) {
            Object.keys(filters).reduce((acc, key) => {
                const value = filters[key];
                // If the value is an array, convert it to a comma-separated string
                this.resource.state.filters[key] = Array.isArray(value) ? value.join(',') : value;
                return acc;
            }, {});
            this.resource.state.filters.page = 1;
        },
        handleExport($e) {
            this.resource.exportdata({ export: $e });
        },
    },
    watch: {
        grid: {
            handler(newval, oldval) {
                this.gridData = this.prepareStudentOfferGridData(newval.students.data);
                this.setPagination(this.resource, newval.students.meta);
            },
            deep: true,
        },
    },
};
</script>
<style lang=""></style>
