<template>
    <Layout :noSpacing="true" :loading="false" :show-tabs="true" :responsive="false">
        <Head title="Attendance by Batch" />
        <template v-slot:pageTitleContent>
            <PageTitleContent
                :title="getPageTitle.text"
                :back="getPageTitle.back"
                @goback="handleBackAction"
            />
        </template>
        <template #tabs>
            <HeaderTab
                :currentTab="'batch'"
                :query="getPropsQuery"
                :pt="{
                    root: 'mt-0',
                }"
                :stretched="false"
                v-if="currentProcess == 'grid'"
            />
        </template>
        <div v-if="currentProcess == 'grid'">
            <!-- <GlobalContextLoader context="overlay-spinner"> -->
            <div class="space-y-6">
                <div class="space-y-4 px-8">
                    <div class="flex gap-2">
                        <FilterByBatch
                            :filters="filters"
                            :pull="['filters', 'batch', 'summaryData', 'gridData']"
                        />
                    </div>
                    <GlobalContextLoader context="batch-info-card">
                        <BatchInfoSection
                            :batch="batchName"
                            :course="courseName"
                            :batchDuration="batchDurationDate"
                            :timeDuration="batchDurationTime"
                            :batchDays="concatenatedBatchDays"
                            :roomNo="roomName"
                            :numOfStudents="studentCount"
                            :loadTable="loadTable"
                        />
                    </GlobalContextLoader>
                </div>
                <BatchGrid
                    :loading="loaderStore.contextLoaders['batch-info-card']"
                    @sendwarning="sendWarningShow"
                    :course="getCurrentCourse"
                    :campus="getCurrentCampus"
                    :batch="getCurrentBatch"
                    :courses="filters?.courses"
                    :campuses="filters?.campuses"
                    :batches="filters?.batches"
                    :emailTotalAttachmentAllowed="emailTotalAttachmentAllowed"
                />
            </div>
            <!-- </GlobalContextLoader> -->
        </div>
        <div v-else-if="currentProcess == 'warning'">
            <SendWarningLetterForm
                :students="getAllStudentsForWarning"
                :actions="sendLetterActions"
                :course="getCurrentCourse"
                :campus="getCurrentCampus"
                :batch="getCurrentBatch"
                :courses="filters?.courses"
                :campuses="filters?.campuses"
                :batches="filters?.batches"
                @completed="handleWarningSentAction"
            />
        </div>
    </Layout>
</template>
<script>
import { ref, computed, watch } from 'vue';
import { router, Head, usePage } from '@inertiajs/vue3';
import Layout from '@spa/pages/Layouts/Layout';
import { mapState } from 'pinia';
import { useAttendanceStore } from '@spa/stores/modules/attendance';
import {
    attendanceResource,
    prepareStudentsDataForWarning,
} from '@spa/services/attendanceResource.js';

import PageTitleContent from '@spa/pages/Layouts/PageTitleContent';
import SkeletonBatchInfo from '@spa/components/Skeleton/SkeletonBatchInfo.vue';

import HeaderTab from '@spa/pages/attendanceview/commons/HeaderTab';
import SendWarningLetterForm from '@spa/pages/attendanceview/commons/SendWarningLetterForm.vue';
import FilterByBatchV2 from '@spa/components/filters/FilterByBatchV2.vue';

import BatchGrid from './BatchGrid';
import BatchInfoSection from './BatchInfoSection';
import GlobalContextLoader from '@spa/components/Loader/GlobalContextLoader.vue';
import { useLoaderStore } from '@spa/stores/modules/global-loader';
import Spinner from '@spa/components/Loader/Spinner.vue';
//take these to commons
export default {
    setup(props) {
        const loaderStore = useLoaderStore();
        const resource = attendanceResource({
            filters: {
                search: props.query?.search || '',
                campus: props.filters?.trace?.campus_id || null,
                course: props.filters?.trace?.course_id || null,
                subject: props.filters?.trace?.subject_id || null,
                batch: props.filters?.trace?.batch_id || null,
                take: props.query?.take || 10,
                page: props.query?.page || 1,
            },
            only: ['filters', 'summaryData', 'batch', 'gridData'],
        });
        watch(
            () => resource.state.filters,
            (val) => {
                resource.fetch();
            },
            { deep: true }
        );
        return {
            resource,
            loaderStore,
        };
    },
    components: {
        Layout,
        Head,
        HeaderTab,
        BatchGrid,
        BatchInfoSection,
        PageTitleContent,
        SkeletonBatchInfo,
        SendWarningLetterForm,
        FilterByBatch: FilterByBatchV2,
        GlobalContextLoader,
        Spinner,
    },
    props: {
        filters: { type: Object, default: [] },
        batch: { type: Object, default: [] },
        query: { type: Object, default: [] },
        summaryData: { type: Object, default: [] },
        gridData: { type: Object, default: [] },
        emailTotalAttachmentAllowed: {
            type: Number,
            required: true,
        },
    },
    data() {
        return {
            currentProcess: 'grid',
            courseName: '',
            batchName: '',
            batchDurationDate: '',
            batchDurationTime: '',
            concatenatedBatchDays: '',
            roomName: '',
            studentCount: 0,
            studentData: [],
            selectedStudents: [],
            batchGridComponentRef: '',
            timetableid: 0,
            courseId: 0,
            loadTable: false,
            sendLetterActions: [
                {
                    type: 'letter',
                    button: 'secondary',
                    show: true,
                    disabled: false,
                    label: 'Generate Letter Only',
                },
                {
                    type: 'letterwithwatermark',
                    button: 'secondary',
                    show: false,
                    disabled: false,
                    label: 'Generate Letter with Watermark',
                },
                {
                    type: 'send',
                    button: 'primary',
                    show: true,
                    disabled: false,
                    label: 'Generate Letter and Send Email',
                },
            ],
            /*
            this variable is to define what will next process be when back button clicked
            to reduce the if else checks
            */
            processFlow: [
                {
                    current: 'warning',
                    next: 'grid',
                },
                {
                    current: 'email',
                    next: 'grid',
                },
                {
                    current: 'sms',
                    next: 'grid',
                },
            ],
        };
    },
    mounted() {
        this.updateBatch();
        this.storeBatchData();
        router.on('start', (event) => {
            this.loaderStore.startContextLoading('batch-info-card');
        });
        router.on('finish', (event) => {
            this.loaderStore.stopContextLoading('batch-info-card');
        });
        router.on('error', (event) => {
            this.loaderStore.stopContextLoading('batch-info-card');
        });
    },
    beforeUnmount() {
        this.resetAllRows();
    },
    computed: {
        ...mapState(useAttendanceStore, [
            'setSelectedTimeTable',
            'setAvailalbeDates',
            'availalbeDates',
            'setCurrentMarkDate',
            'currentMarkDate',
            'setEnrolledStudents',
            'enrolledStudents',
            'studentsCount',
            'prepareColumnsRowsForGrid',
            'setGirdAllSelected',
            'girdAllSelected',
            'resetAllRows',
        ]),
        filterSettings() {
            return [
                {
                    name: 'campus',
                    loads: 'course',
                    loading: false,
                    selected: this.filters?.trace?.campus_id || null,
                    visible: true,
                },
                {
                    name: 'course',
                    loads: 'subject',
                    loading: false,
                    selected: this.filters?.trace?.course_id || null,
                    visible: true,
                },
                {
                    name: 'subject',
                    loads: 'batch',
                    loading: false,
                    selected: this.filters?.trace?.subject_id || null,
                    visible: true,
                },
                {
                    name: 'batch',
                    loads: null,
                    loading: false,
                    selected: this.filters?.trace?.batch_id || null,
                    visible: true,
                },
            ];
        },
        getPropsQuery() {
            const $page = usePage();
            let queryPrams = {};
            if ($page.props && $page.props.query) {
                queryPrams = $page.props.query;
            }
            if (this.gridData) {
                const firstStudentKey = Object.keys(this.gridData?.students)[0];
                const firstStudentId = this.gridData?.students[firstStudentKey]?.student_id || null;
                if (firstStudentId) queryPrams = { student: firstStudentId };
            }
            return queryPrams;
        },
        getPageTitle() {
            let pageTitleText = '';
            let hasBackBtn = false;
            switch (this.currentProcess) {
                case 'warning':
                    pageTitleText = 'Generate Attendance Warning Letter';
                    hasBackBtn = true;
                    break;
                case 'sms':
                    pageTitleText = 'Send SMS to Students';
                    hasBackBtn = '';
                    break;
                default:
                    pageTitleText = 'View Attendance';
                    hasBackBtn = false;
                    break;
            }
            return { text: pageTitleText, back: hasBackBtn };
        },
        getAllStudentsForWarning() {
            return this.prepareStudentsDataForWarning(this.selectedStudents, false);
        },
        getCurrentCourse() {
            return this.batch?.course_id || '';
        },
        getCurrentCampus() {
            return this.batch?.campus_id || '';
        },
        getCurrentBatch() {
            return this.batch?.batch_id || '';
        },
        getFilterScopes() {
            return this.filters.seeds || [];
        },
        getFilters() {
            return this.filters.trace || [];
        },
        getFilterParams() {
            return this.filterSettings.filter((item) => item.visible === true);
        },
    },
    methods: {
        prepareStudentsDataForWarning,
        storeBatchData() {
            this.prepareColumnsRowsForGrid(this.gridData);
        },
        updateBatch() {
            this.setSelectedTimeTable(this.batch);
            this.courseName = this.batch?.course || '';
            this.batchName = this.batch?.batch || '';
            this.batchDurationDate = this.batch?.batchDurationDate || '';
            this.batchDurationTime = this.batch?.batchDurationTime || '';
            this.concatenatedBatchDays = this.batch?.batchDays || '';
            this.roomName = this.batch?.batchRoomName || '';
            this.studentCount = this.batch?.batchStudentCount || '0';
            this.timetableid = this.batch?.timetable_id || '';
            this.courseId = this.batch?.course_id || '';
        },
        handleBackAction() {
            const processFlow = this.processFlow.find(
                (process) => process.current === this.currentProcess
            );
            this.currentProcess = processFlow?.next || 'grid';
        },
        handleWarningSentAction() {
            this.handleBackAction();
            this.resource.fetch();
            return;
        },
        sendWarningShow(students) {
            //get the list of selected students
            this.selectedStudents = students;
            this.currentProcess = 'warning';
        },
        handleFilterUpdated(resp) {
            this.resource.state.filters.type = resp.course_type || null;
            this.resource.state.filters.year = resp.year || null;
            this.resource.state.filters.course = resp.course_id || null;
            this.resource.state.filters.semester = resp.semester_id || null;
            this.resource.state.filters.subject = resp.subject_id || null;
            this.resource.state.filters.batch = resp.batch_id || null;
        },
        handleFilterChanged(resp) {
            resp.forEach((obj) => {
                const field = Object.keys(obj)[0];
                const value = obj[field] || null;
                if (field && this.resource.state.filters.hasOwnProperty(field)) {
                    this.resource.state.filters[field] = value || null;
                }
            });
        },
        handleFilterSelected(resp) {
            const field = resp.name || null;
            if (field && this.resource.state.filters.hasOwnProperty(field)) {
                this.resource.state.filters[field] = resp.selected || null;
            }
        },
    },
    watch: {
        batch: {
            handler() {
                this.updateBatch();
            },
            deep: true,
        },
        gridData: {
            handler() {
                this.storeBatchData();
            },
            deep: true,
        },
    },
};
</script>

<style></style>
