<template>
    <Layout :noSpacing="true" :loading="false">
        <Head title="Attendance by Student" />
        <template v-slot:pageTitleContent>
            <PageTitleContent :title="getPageTitle.text" :back="false" @goback="handleBackAction" />
        </template>
        <template #tabs v-if="currentProcess == 'grid'">
            <HeaderTab
                :currentTab="'student'"
                :query="getPropsQuery"
                :pt="{
                    root: 'mt-0',
                }"
                :stretched="false"
            />
        </template>
        <div v-if="currentProcess == 'grid'">
            <!-- <GlobalContextLoader context="overlay-spinner"> -->
            <div class="space-y-6 px-8 pb-4">
                <div class="space-y-4">
                    <div class="flex gap-2">
                        <FilterByBatch
                            :filters="filters"
                            :mode="'student'"
                            :pull="['filters', 'batch', 'studentStatus', 'gridData']"
                        />
                    </div>
                    <GlobalContextLoader context="batch-info-card">
                        <StudentInfoSection
                            :data="studentAttendanceStatus"
                            :student="getStudentDetails"
                            :batch="batch"
                            v-if="hasStudent && hasStudentData"
                        />
                    </GlobalContextLoader>
                </div>
                <template v-if="!loaderStore.contextLoaders['batch-info-card']">
                    <div class="space-y-4" v-if="hasStudentData">
                        <MoreAction
                            :isShowCalenderView="true"
                            @hideShowView="hideShowView"
                            @sendWarningShowEmit="sendWarningShow"
                            @sendMailPopupShowEmit="sendMailPopupShow"
                            @sendSMSPopupShowEmit="sendSMSPopupShow"
                            :studentData="studentEmailData"
                        ></MoreAction>
                        <StudentGrid
                            v-if="isListViewShow"
                            :loading="loaderStore.contextLoaders['batch-info-card']"
                        />
                        <StudentCalender v-if="!isListViewShow" />
                    </div>
                    <tw-no-data
                        :iconName="computedNoDataIcon"
                        :title="computedNoDataTitle"
                        :subtitle="computedNoDataSubtitle"
                        v-else
                    />
                </template>
            </div>
            <!-- </GlobalContextLoader> -->
        </div>
        <div v-else-if="currentProcess == 'warning'">
            <SendWarningLetterForm
                :students="getAllStudentsForWarning"
                :actions="sendLetterActions"
                :course="getCurrentCourse"
                :campus="getCurrentCampus"
                :batch="getCurrentBatch"
                :courses="filters?.courses"
                :campuses="filters?.campuses"
                :batches="filters?.batches"
                @completed="handleWarningSentAction"
                :is-by-student="true"
            />
        </div>
        <SendMail
            v-if="isSendMailPopupShow"
            :visible="isSendMailPopupShow"
            :students="getAllStudentsForWarning"
            :course="getCurrentCourse"
            :campus="getCurrentCampus"
            :batch="getCurrentBatch"
            :courses="filters?.courses"
            :campuses="filters?.campuses"
            :batches="filters?.batches"
            @closed="sendMailPopupShow"
        />
        <SendSMS
            v-if="isSendSMSPopupShow"
            :visible="isSendSMSPopupShow"
            @closed="sendSMSPopupShow"
            :students="getAllStudentsForWarning"
        />
    </Layout>
</template>
<script>
import { ref, computed, watch } from 'vue';
import { router, Head, usePage } from '@inertiajs/vue3';
import Layout from '@spa/pages/Layouts/Layout';
import { mapState } from 'pinia';
import { useAttendanceStore } from '@spa/stores/modules/attendance';
import {
    attendanceResource,
    prepareStudentsDataForWarning,
} from '@spa/services/attendanceResource.js';

import PageTitleContent from '@spa/pages/Layouts/PageTitleContent';
import SkeletonBatchInfo from '@spa/components/Skeleton/SkeletonBatchInfo.vue';

import HeaderTab from '@spa/pages/attendanceview/commons/HeaderTab';
import SendWarningLetterForm from '@spa/pages/attendanceview/commons/SendWarningLetterForm.vue';

import SendMail from '@spa/pages/attendanceview/commons/SendMail';
import SendSMS from '@spa/pages/attendanceview/commons/SendSMS';

import FilterByBatchV2 from '@spa/components/filters/FilterByBatchV2.vue';

import BatchGrid from './BatchGrid';
import StudentInfoSection from './StudentInfoSection';
import MoreAction from './MoreAction';
import StudentGrid from './StudentGrid';
import StudentCalender from './StudentCalender';
import NoData from '../../components/NoData/NoData.vue';
import GlobalContextLoader from '../../components/Loader/GlobalContextLoader.vue';
import { useLoaderStore } from '../../stores/modules/global-loader';
//take these to commons

export default {
    setup(props) {
        const loaderStore = useLoaderStore();
        const resource = attendanceResource({
            filters: {
                search: props.query?.search || '',
                campus: props.filters?.trace?.campus_id || null,
                course: props.filters?.trace?.course_id || null,
                subject: props.filters?.trace?.subject_id || null,
                batch: props.filters?.trace?.batch_id || null,
                student: props.filters?.trace?.student_id || null,
                take: props.query?.take || 10,
                page: props.query?.page || 1,
            },
            only: ['filters', 'summaryData', 'studentStatus', 'gridData'],
        });
        watch(
            () => resource.state.filters,
            (val) => {
                resource.fetch();
            },
            { deep: true }
        );
        return {
            resource,
            loaderStore,
        };
    },
    components: {
        Layout,
        Head,
        HeaderTab,
        BatchGrid,
        FilterByBatch: FilterByBatchV2,
        StudentInfoSection,
        PageTitleContent,
        SkeletonBatchInfo,
        SendWarningLetterForm,
        MoreAction,
        StudentGrid,
        StudentCalender,
        SendMail,
        SendSMS,
        'tw-no-data': NoData,
        GlobalContextLoader,
    },
    props: {
        filters: { type: Object, default: [] },
        batch: { type: Object, default: [] },
        studentStatus: { type: Object, default: [] },
        gridData: { type: Object, default: [] },
    },
    data() {
        return {
            currentProcess: 'grid',
            courseName: '',
            batchName: '',
            batchDurationDate: '',
            batchDurationTime: '',
            concatenatedBatchDays: '',
            roomName: '',
            studentCount: 0,
            studentData: [],
            selectedStudents: [],
            batchGridComponentRef: '',
            timetableid: 0,
            courseId: 0,
            loadTable: false,
            isListViewShow: true,
            isSendMailPopupShow: false,
            isSendSMSPopupShow: false,
            sendLetterActions: [
                {
                    type: 'letter',
                    button: 'secondary',
                    show: true,
                    disabled: false,
                    label: 'Generate Letter Only',
                },
                {
                    type: 'letterwithwatermark',
                    button: 'secondary',
                    show: false,
                    disabled: false,
                    label: 'Generate Letter with Watermark',
                },
                {
                    type: 'send',
                    button: 'primary',
                    show: true,
                    disabled: false,
                    label: 'Generate Letter and Send Email',
                },
            ],
            /* 
            this variable is to define what will next process be when back button clicked
            to reduce the if else checks 
            */
            processFlow: [
                {
                    current: 'warning',
                    next: 'grid',
                },
                {
                    current: 'email',
                    next: 'grid',
                },
                {
                    current: 'sms',
                    next: 'grid',
                },
            ],
        };
    },
    mounted() {
        this.storeStudentData();
        router.on('start', (event) => {
            this.loaderStore.startContextLoading('overlay-spinner');
        });
        router.on('finish', (event) => {
            this.loaderStore.stopContextLoading('overlay-spinner');
        });
        router.on('error', (event) => {
            this.loaderStore.stopContextLoading('overlay-spinner');
        });
    },
    beforeUnmount() {
        this.resetAllRows();
    },
    computed: {
        ...mapState(useAttendanceStore, [
            'setSelectedTimeTable',
            'setAvailalbeDates',
            'availalbeDates',
            'setCurrentMarkDate',
            'currentMarkDate',
            'setStudentAttendanceData',
            'setStudentAttendanceStatus',
            'studentAttendanceStatus',
            'studentAttendance',
            'resetAllRows',
        ]),
        filterSettings() {
            return [
                {
                    name: 'campus',
                    loads: 'course',
                    loading: false,
                    selected: this.filters?.trace?.campus_id || null,
                    visible: true,
                },
                {
                    name: 'course',
                    loads: 'subject',
                    loading: false,
                    selected: this.filters?.trace?.course_id || null,
                    visible: true,
                },
                {
                    name: 'subject',
                    loads: 'batch',
                    loading: false,
                    selected: this.filters?.trace?.subject_id || null,
                    visible: true,
                },
                {
                    name: 'batch',
                    loads: 'student',
                    loading: false,
                    selected: this.filters?.trace?.batch_id || null,
                    visible: true,
                },
                {
                    name: 'student',
                    loads: null,
                    loading: false,
                    selected: this.filters?.trace?.student_id || null,
                    visible: true,
                },
            ];
        },
        getPropsQuery() {
            const $page = usePage();
            let timetableId = this.gridData?.timetable_id || null;
            let subjectId = this.gridData?.subject_id || null;
            let courseId = this.studentAttendanceStatus?.course?.current?.course_id || null;

            return { course: courseId, subject: subjectId, batch: timetableId };
        },
        getFixedCols() {
            return [
                {
                    field: 'selected',
                    headerSelectionValue: this.girdAllSelected,
                    locked: true,
                },
                {
                    title: 'STUDENT NAME',
                    field: 'name',
                    headerCell: 'headerTemplate',
                    width: '200px',
                    locked: true,
                },
                {
                    title: 'OVERALL',
                    field: 'overall',
                    headerCell: 'headerTemplate',
                    width: '80px',
                    locked: true,
                },
            ];
        },
        getPageTitle() {
            let pageTitleText = '';
            let hasBackBtn = false;
            switch (this.currentProcess) {
                case 'warning':
                    pageTitleText = 'Generate Attendance Warning Letter';
                    hasBackBtn = true;
                    break;
                case 'sms':
                    pageTitleText = 'Send SMS to Students';
                    hasBackBtn = '';
                    break;
                default:
                    pageTitleText = 'View Attendance';
                    hasBackBtn = false;
                    break;
            }
            return { text: pageTitleText, back: hasBackBtn };
        },
        getAllStudentsForWarning() {
            return this.prepareStudentsDataForWarning(this.selectedStudents, true);
        },
        getCurrentCourse() {
            return this.batch?.course_id || '';
        },
        getCurrentCampus() {
            return this.batch?.campus_id || '';
        },
        getCurrentBatch() {
            return this.batch?.batch_id || '';
        },
        getCurrentStudent() {
            const defaultStudent = this.filters?.seeds.students[0]?.id || null;
            const student = parseInt(this.query?.student || defaultStudent);
            return isNaN(student) ? 0 : student;
        },
        getStudentDetails() {
            let studentData = Object.assign({}, this.gridData);
            if (studentData.attendance_data) delete studentData.attendance_data;
            if (Object.keys(studentData).length === 0) {
                const student = this.filters?.trace?.student?.data || {};
                Object.assign(studentData, student);
            }
            return studentData;
        },
        studentEmailData() {
            return [];
        },
        attenDanceDataCalendar() {
            return this.gridData;
        },
        getFilterScopes() {
            return this.filters.seeds || [];
        },
        getFilters() {
            return this.filters.trace || [];
        },
        getFilterParams() {
            return this.filterSettings.filter((item) => item.visible === true);
        },
        hasStudent() {
            return this.filters?.trace?.student?.data?.id || null;
        },
        hasAttendanceData() {
            return this.gridData?.enrollment_id || 0;
        },
        hasStudentData() {
            return this.hasStudent && this.hasAttendanceData;
        },
        computedNoDataTitle() {
            return !this.hasStudent ? 'Find Students by Name' : 'Enrollment Not Found';
        },
        computedNoDataSubtitle() {
            return !this.hasStudent
                ? "Enter a student's name in the search box above to find them instantly."
                : 'The student is not enrolled in the selected course, subject, or batch. Please choose a different option.';
        },
        computedNoDataIcon() {
            return !this.hasStudent ? 'person-search' : 'search-info';
        },
    },
    methods: {
        prepareStudentsDataForWarning,
        storeStudentData() {
            this.setStudentAttendanceData(this.gridData);
            this.setStudentAttendanceStatus(this.studentStatus);
        },
        handleBackAction() {
            const processFlow = this.processFlow.find(
                (process) => process.current === this.currentProcess
            );
            this.currentProcess = processFlow?.next || 'grid';
        },
        handleWarningSentAction() {
            this.handleBackAction();
            this.resource.fetch();
            return;
        },
        sendWarningShow() {
            //get the list of selected students
            this.selectedStudents = [this.studentAttendance];
            this.currentProcess = 'warning';
        },
        hideShowView(isShow) {
            this.isListViewShow = isShow;
        },
        sendMailPopupShow(isShow) {
            this.selectedStudents = [this.studentAttendance];
            this.isSendMailPopupShow = !this.isSendMailPopupShow;
        },
        sendSMSPopupShow(show) {
            this.selectedStudents = [this.studentAttendance];
            this.isSendSMSPopupShow = show; //!this.isSendSMSPopupShow;
        },
        handleFilterUpdated(resp) {
            this.resource.state.filters.type = resp.course_type || null;
            this.resource.state.filters.year = resp.year || null;
            this.resource.state.filters.course = resp.course_id || null;
            this.resource.state.filters.semester = resp.semester_id || null;
            this.resource.state.filters.subject = resp.subject_id || null;
            this.resource.state.filters.batch = resp.batch_id || null;
            this.resource.state.filters.student = resp.student_id || null;
        },
        handleFilterChanged(resp) {
            resp.forEach((obj) => {
                const field = Object.keys(obj)[0];
                const value = obj[field] || null;
                if (field && this.resource.state.filters.hasOwnProperty(field)) {
                    this.resource.state.filters[field] = value || null;
                }
            });
        },
        handleFilterSelected(resp) {
            const field = resp.name || null;
            if (field && this.resource.state.filters.hasOwnProperty(field)) {
                this.resource.state.filters[field] = resp.selected || null;
            }
        },
        updateBatch() {
            this.setSelectedTimeTable(this.batch);
            this.courseName = this.batch?.course || '';
            this.batchName = this.batch?.batch || '';
            this.batchDurationDate = this.batch?.batchDurationDate || '';
            this.batchDurationTime = this.batch?.batchDurationTime || '';
            this.concatenatedBatchDays = this.batch?.batchDays || '';
            this.roomName = this.batch?.batchRoomName || '';
            this.studentCount = this.batch?.batchStudentCount || '0';
            this.timetableid = this.batch?.timetable_id || '';
            this.courseId = this.batch?.course_id || '';
        },
    },
    watch: {
        batch: {
            handler() {
                this.updateBatch();
            },
            deep: true,
        },
        gridData: {
            handler() {
                this.storeStudentData();
            },
            deep: true,
        },
        studentStatus: {
            handler() {
                this.storeStudentData();
            },
            deep: true,
        },
    },
};
</script>
<style></style>
