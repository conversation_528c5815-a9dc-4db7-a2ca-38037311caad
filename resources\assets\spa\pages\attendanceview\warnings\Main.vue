<template>
    <Layout>
        <Head title="Attendance Warning" />
        <template v-slot:pageTitleContent>
            <PageTitleContent :title="getPageTitle.text" :back="false" @goback="handleBackAction" />
        </template>
        <div v-if="currentProcess == 'grid'">
            <div class="space-y-4">
                <div class="flex justify-between">
                    <div class="flex gap-2">
                        <FilterByBatch :filters="filters" />
                    </div>
                    <Button @click="warningSettings" variant="secondary" :size="'sm'">
                        <div class="flex w-auto items-center">
                            <icon :name="'setting'" :width="18" :height="18" :fill="'#9CA3AF'" />
                            <div class="ml-2 text-sm text-gray-700">Warning Settings</div>
                        </div>
                    </Button>
                </div>
                <GlobalContextLoader context="filters-changed" :overlay="false">
                    <AttendanceWarningSummaryReport :reportdata="summaryData" />
                </GlobalContextLoader>
                <GlobalContextLoader context="filters-changed" :overlay="false">
                    <div class="container-stretch space-y-4 bg-white pt-6" ref="gridContainer">
                        <SkeletonBatchInfo :loader="loadTable" v-if="loadTable" />
                        <div class="space-y-4" v-else>
                            <div class="flex items-center justify-between px-4">
                                <div class="flex space-x-2">
                                    <Button
                                        variant="secondary"
                                        @click="toggleFilterWindow"
                                        :size="'xs'"
                                    >
                                        <div class="flex w-auto items-center">
                                            <icon
                                                :name="'filter'"
                                                :width="16"
                                                :height="16"
                                                :fill="'#9CA3AF'"
                                            />
                                            <div class="ml-2 text-sm text-gray-700">Filters</div>
                                        </div>
                                    </Button>
                                    <div class="text-base font-medium text-gray-700">
                                        {{ `Attendance warning for ${getCampusBatchName}` }}
                                    </div>
                                </div>
                                <div class="flex space-x-2">
                                    <managecolumns
                                        :columns="columns"
                                        @restore="restoreColumns"
                                        @update:toggle="handleCheckToggle"
                                    />
                                </div>
                            </div>
                            <GridWrapper
                                :selectable="true"
                                :scrollable="true"
                                id="attendanceWarningGrid"
                                :overlay="filterLoader"
                            >
                                <Grid
                                    :data-items="getBatchStudents"
                                    :skip="resource.state.pageable.skip"
                                    :take="resource.state.filters.take"
                                    :total="total"
                                    :selected-field="this.selectedField"
                                    :columns="gridColumns"
                                    :pageable="resource.state.filters.pageable"
                                    @pagechange="pageChangeHandler"
                                    @headerselectionchange="onHeaderSelectionChange"
                                    @selectionchange="onSelectionChange"
                                    :loader="'loaderTemplate'"
                                    :style="{
                                        width: '100%',
                                    }"
                                    @hidecolumn="hidecolumn"
                                    :resizable="true"
                                    ref="batchGridWrapper"
                                >
                                    <GridNoRecords>
                                        <EmptyState />
                                    </GridNoRecords>
                                    <template v-slot:loaderTemplate>
                                        <TableLoader v-if="resource.state.loading" />
                                    </template>
                                    <template #defaultCell="{ props }">
                                        <default-cell v-bind:props="props" />
                                    </template>
                                    <template v-slot:sortableHeader="{ props }">
                                        <div
                                            class="flex items-center justify-center gap-2"
                                            @click="handleSort(props.field)"
                                        >
                                            <div>
                                                {{ props.title }}
                                            </div>
                                            <!-- <icon
                                                    :name="getSortIcon(props.field)"
                                                    :width="16"
                                                    :height="16"
                                                    :fill="
                                                        iscurrentSorting(
                                                            props.field,
                                                            'color',
                                                        )
                                                    "
                                                /> -->
                                            <sort-icon :dir="getSortIcon(props.field)" />
                                        </div>
                                    </template>
                                    <template v-slot:actionsHeader="{ props }">
                                        <div class="flex items-center justify-center gap-2">
                                            <div class="uppercase text-gray-500">
                                                {{ props.title }}
                                            </div>
                                        </div>
                                    </template>
                                    <template v-slot:attendancePercentCell="{ props }">
                                        <td
                                            :class="props.class"
                                            :colspan="props.colspan"
                                            :role="props.role"
                                            :aria-selected="props['aria-selected']"
                                            :data-grid-col-index="props['data-grid-col-index']"
                                        >
                                            <StudentAttendanceBadge
                                                :present="props.dataItem.total_present"
                                                :absent="props.dataItem.total_absent"
                                                :total="props.dataItem.total_dates"
                                                :pending="props.dataItem.total_pending"
                                                :alldates="props.dataItem.all_timetable_dates"
                                                :type="'general'"
                                            />
                                        </td>
                                    </template>
                                    <template v-slot:timeCell="{ props }">
                                        <td
                                            :class="props.class"
                                            :colspan="props.colspan"
                                            :aria-selected="props['aria-selected']"
                                            :data-grid-col-index="props['data-grid-col-index']"
                                        >
                                            <div class="flex items-center">
                                                <icon
                                                    :name="'time'"
                                                    :width="20"
                                                    :height="20"
                                                    :fill="'#9CA3AF'"
                                                />
                                                <span>{{ props.dataItem.class_time }}</span>
                                            </div>
                                        </td>
                                    </template>
                                    <template v-slot:warningCell="{ props }">
                                        <td
                                            :class="props.class"
                                            :colspan="props.colspan"
                                            :aria-selected="props['aria-selected']"
                                            :data-grid-col-index="props['data-grid-col-index']"
                                        >
                                            <div
                                                class="flex cursor-pointer items-center"
                                                v-if="props.dataItem.warnings_sent > 0"
                                                @click="showWarningDetails(props.dataItem)"
                                            >
                                                <span class="font-medium text-primary-blue-500"
                                                    >{{ props.dataItem.warnings_sent
                                                    }}{{
                                                        props.dataItem.warnings_sent > 1
                                                            ? ' Warnings'
                                                            : ' Warning'
                                                    }}</span
                                                >
                                            </div>
                                            <div v-else>
                                                <span class="text-gray-400">No warnings yet</span>
                                            </div>
                                        </td>
                                    </template>
                                    <template v-slot:actionsCell="{ props }">
                                        <td :class="props.class">
                                            <tw-tooltip
                                                :anchor-element="'target'"
                                                :position="'top'"
                                                :parentTitle="true"
                                                :tooltipClassName="'flex'"
                                                :class="'w-full cursor-pointer'"
                                                :openDelay="'200'"
                                            >
                                                <div :title="'View More'">
                                                    <action-menu
                                                        :items="actionsOptions"
                                                        :icon="actionIcon"
                                                        :variant="'action'"
                                                        :width="200"
                                                        @itemClick="
                                                            handleActionsClick(
                                                                $event,
                                                                props.dataItem
                                                            )
                                                        "
                                                    ></action-menu>
                                                </div>
                                            </tw-tooltip>
                                        </td>
                                    </template>
                                </Grid>
                            </GridWrapper>
                        </div>
                    </div>
                </GlobalContextLoader>
                <AttendanceWarningGrid v-if="stateMarkAttendance" @marked="reloadGrid" />
            </div>
            <BulkActions
                v-if="showBulkAction > 0"
                @sendwarning="sendWarningShow"
                @sendemail="sendEmailShow"
                @sendsms="sendSmsShow"
                :datalength="showBulkAction"
                @cancel="handleBulkCancel"
            />
            <SendMail
                v-if="isSendMailPopupShow"
                :visible="isSendMailPopupShow"
                :students="getAllStudentsForWarning"
                :course="getCurrentCourse"
                :campus="getCurrentCampus"
                :batch="getCurrentBatch"
                :courses="filters?.seeds?.courses"
                :campuses="filters?.seeds?.campuses"
                :batches="filters?.seeds?.batches"
                @closed="sendMailPopupShow"
            />
            <!--
                <SendSMS
                    :smsTemplateData="smsTemplateData"
                    :visible="isSendSMSPopupShow"
                    @closed="sendSMSPopupShow"
                    :studentData="studentEmailData"
                    :requestedData="requestedData"
                />
                -->
        </div>
        <div v-if="currentProcess == 'warning'">
            <SendWarningLetterForm
                :students="getAllStudentsForWarning"
                :actions="sendLetterActions"
                :course="getCurrentCourse"
                :campus="getCurrentCampus"
                :batch="getCurrentBatch"
                :courses="filters?.seeds?.courses"
                :campuses="filters?.seeds?.campuses"
                :batches="filters?.seeds?.batches"
                @completed="handleWarningSentAction"
            />
        </div>
        <ViewWarningLogs
            v-if="showWarningLogs"
            :student="studentForLog"
            :dialogVisible="showWarningLogs"
            @close="hideWarningDetails"
        />
        <WarningSettingDialog
            :visible="openWarningSettingsDialog"
            :data="warningSettingsData"
            v-if="openWarningSettingsDialog"
            @close="closeWarningSettingsDialog"
            @saved="updateSettingsData"
        />
    </Layout>
</template>
<script>
import { ref, computed, watch } from 'vue';
import { moreHorizontalIcon } from '@progress/kendo-svg-icons';
import { mapState } from 'pinia';
import Layout from '@spa/pages/Layouts/Layout';
import { Head } from '@inertiajs/vue3';
import { Grid, GridNoRecords } from '@progress/kendo-vue-grid';
import PageTitleContent from '@spa/pages/Layouts/PageTitleContent';
import SkeletonBatchInfo from '@spa/components/Skeleton/SkeletonBatchInfo.vue';
import { useAttendanceStore } from '@spa/stores/modules/attendance';
import AttendanceWarningSummaryReport from './AttendanceWarningSummaryReport.vue';
import AttendanceWarningReportFilter from './AttendanceWarningReportFilter.vue';
import AttendanceWarningGrid from './AttendanceWarningGrid.vue';
import {
    attendanceResource,
    prepareStudentsDataForWarning,
} from '@spa/services/attendanceResource.js';
import EmptyState from '@spa/components/KendoGrid/EmptyState.vue';
import TableLoader from '@spa/components/KendoGrid/TableLoader.vue';
import Button from '@spa/components/Buttons/Button.vue';
import StudentAttendanceBadge from '@spa/components/badges/StudentAttendanceBadge.vue';
import ActionMenu from '@spa/components/KendoGrid/ActionMenu.vue';
import BulkActions from '@spa/pages/attendanceview/commons/BulkActions.vue';
import SendWarningLetterForm from '@spa/pages/attendanceview/commons/SendWarningLetterForm.vue';
import ViewWarningLogs from './ViewWarningLogs.vue';
import WarningSettingDialog from './WarningSettingDialog.vue';
import GridWrapper from '@spa/components/KendoGrid/GridWrapper.vue';
import ManageColumns from '@spa/components/KendoGrid/ManageColumns.vue';

import SendMail from '@spa/pages/attendanceview/commons/SendMail';
import SendSMS from '@spa/pages/attendanceview/commons/SendSMS';
import { filterBy } from '@progress/kendo-vue-data-tools';
import SortingIndicator from '@spa/components/KendoGrid/SortingIndicator.vue';
import { ADJUST_PADDING, COLUMN_MIN } from '@spa/helpers/constants';
import DefaultCellTemplate from '@spa/components/KendoGrid/templates/DefaultCellTemplate.vue';
import FilterByBatchV2 from '@spa/components/filters/FilterByBatchV2.vue';
import { Tooltip } from '@progress/kendo-vue-tooltip';
import { useLoaderStore } from '@spa/stores/modules/global-loader';
import GlobalContextLoader from '@spa/components/Loader/GlobalContextLoader.vue';

let minGridWidth = 0;

const columnsData = [
    {
        field: 'full_name',
        title: 'Students',
        headerCell: 'sortableHeader',
        minWidth: 200,
        checked: true,
        locked: true,
        cell: 'defaultCell',
    },
    {
        field: 'batch',
        title: 'Batch',
        checked: true,
        minWidth: 200,
        minResizableWidth: 100,
        cell: 'defaultCell',
    },
    {
        field: 'subject_name',
        title: 'Subject Name',
        minWidth: 300,
        checked: true,
        minResizableWidth: 100,
        cell: 'defaultCell',
    },
    {
        field: 'semester_name',
        title: 'Semester',
        minWidth: 150,
        checked: true,
        minResizableWidth: 100,
        cell: 'defaultCell',
    },
    {
        field: 'percent',
        title: 'Overall',
        headerCell: 'sortableHeader',
        cell: 'attendancePercentCell',
        minWidth: 100,
        checked: true,
        minResizableWidth: 100,
    },
    {
        field: 'warnings_sent',
        title: 'Warnings Count',
        headerCell: 'sortableHeader',
        cell: 'warningCell',
        minWidth: 150,
        checked: true,
        minResizableWidth: 100,
    },
    {
        title: 'Action',
        headerCell: 'actionsHeader',
        cell: 'actionsCell',
        minWidth: 100,
        minResizableWidth: 80,
    },
];

export default {
    setup(props) {
        const loader = useLoaderStore();
        const resource = attendanceResource({
            filters: {
                search: props.query?.search || '',
                campus: props.query?.campus || null,
                course: props.query?.course || null,
                batch: props.query?.batch || null,
                take: props.query?.take || 10,
                page: props.query?.page || 1,
            },
            only: ['filters', 'summaryData', 'gridData'],
        });
        watch(
            () => resource.state.filters,
            (val) => {
                resource.fetch();
            },
            { deep: true }
        );
        return {
            resource,
            loader,
        };
    },
    props: {
        filters: { type: Object, default: [] },
        query: { type: Object, default: [] },
        gridData: { type: Object, default: [] },
        summaryData: { type: Object, default: [] },
        settingsData: { type: Object, default: {} },
    },
    components: {
        Layout,
        Head,
        Grid,
        GridNoRecords,
        EmptyState,
        TableLoader,
        PageTitleContent,
        SkeletonBatchInfo,
        AttendanceWarningSummaryReport,
        FilterByBatch: FilterByBatchV2,
        AttendanceWarningGrid,
        Button,
        StudentAttendanceBadge,
        'action-menu': ActionMenu,
        BulkActions,
        SendWarningLetterForm,
        ViewWarningLogs,
        SendMail,
        SendSMS,
        GridWrapper,
        managecolumns: ManageColumns,
        'sort-icon': SortingIndicator,
        'default-cell': DefaultCellTemplate,
        'tw-tooltip': Tooltip,
        WarningSettingDialog,
        GlobalContextLoader,
    },
    mounted() {
        this.gridWidth = this.$refs.batchGridWrapper.offsetWidth;
        //this.filterParams = this.filters;
        this.gridElement = document.querySelector('.k-grid');
        window.addEventListener('resize', this.handleResize);
        columnsData.map((item) => {
            return (minGridWidth += item.minWidth);
        });

        this.gridCurrent = this.gridElement.offsetWidth;
        this.setMinWidth = this.gridElement.offsetWidth < minGridWidth;
        this.warningSettingsData = this.settingsData ?? {};
        this.defineColumns();
    },
    data() {
        return {
            currentProcess: 'grid',
            selectedField: 'selected',
            loadTable: false,
            loader: false,
            filterLoader: false,
            dataItems: [],
            sortDir: false,
            gridWidth: 0,
            actionIcon: moreHorizontalIcon,
            showWarningLogs: false,
            actionsOptions: [
                {
                    text: 'Send SMS',
                    value: 'sms',
                    icon: '',
                },
                {
                    text: 'Send Email',
                    value: 'email',
                    icon: '',
                },
                {
                    text: 'Send Warning Letter',
                    value: 'warning',
                    icon: '',
                },
                {
                    text: 'View Student Profile',
                    value: 'profile',
                    icon: '',
                },
            ],
            sendLetterActions: [
                {
                    type: 'letter',
                    button: 'secondary',
                    show: true,
                    disabled: false,
                    label: 'Generate Letter Only',
                },
                {
                    type: 'letterwithwatermark',
                    button: 'secondary',
                    show: false,
                    disabled: false,
                    label: 'Generate Letter with Watermark',
                },
                {
                    type: 'send',
                    button: 'primary',
                    show: true,
                    disabled: false,
                    label: 'Generate Letter and Send Email',
                },
            ],
            /*
            this variable is to define what will next process be when back button clicked
            to reduce the if else checks
            */
            processFlow: [
                {
                    current: 'warning',
                    next: 'grid',
                },
                {
                    current: 'email',
                    next: 'grid',
                },
                {
                    current: 'sms',
                    next: 'grid',
                },
            ],
            isSendMailPopupShow: false,
            columns: [],
            gridElement: '',
            gridCurrent: 0,
            setMinWidth: false,
            openWarningSettingsDialog: false,
            warningSettingsData: {},
        };
    },
    computed: {
        gridColumns() {
            return [
                {
                    field: this.selectedField,
                    headerSelectionValue: this.areAllSelected,
                    width: 50,
                },
                ...this.columns,
            ];
        },
        filterSettings() {
            return [
                {
                    name: 'campus',
                    loads: 'batch',
                    loading: false,
                    selected: this.filters?.trace?.campus_id || null,
                    visible: true,
                },
                {
                    name: 'batch',
                    loads: null,
                    loading: false,
                    selected: this.filters?.trace?.batch_id || null,
                    visible: true,
                },
            ];
        },
        getBatchStudents() {
            return this.gridData || [];
        },
        areAllSelected() {
            return this.gridData.findIndex((item) => item.selected === false) === -1;
        },
        showBulkAction() {
            const selectedStudentsCount = this.selectedStudents?.length || 0;
            return selectedStudentsCount;
        },
        selectedStudents() {
            return this.gridData.filter((entry) => entry.selected === true);
        },
        total() {
            return this.gridData?.meta?.total || 0;
        },
        markingNeeed() {
            return this.summaryData?.total || 0;
        },
        getCampusBatchName() {
            return '';
            const campus = this.filters.seeds.campuses.find(
                (campus) => campus.id === this.resource.state.filters.campus
            );
            const batch = this.filters.seeds.batches.find(
                (batch) => batch.id === this.resource.state.filters.batch
            );
            return ''.concat(campus.text || '', ', Batch ').concat(batch?.batch || '');
            //return `${campus.text || ''}, Batch ${batch.batch || ''}`;
        },
        getPageTitle() {
            let pageTitleText = '';
            let hasBackBtn = false;
            switch (this.currentProcess) {
                case 'warning':
                    pageTitleText = 'Generate Attendance Warning Letter';
                    hasBackBtn = true;
                    break;
                case 'sms':
                    pageTitleText = 'Send SMS to Students';
                    hasBackBtn = '';
                    break;
                default:
                    pageTitleText = 'Attendance Warning';
                    hasBackBtn = false;
                    break;
            }
            return { text: pageTitleText, back: hasBackBtn };
        },
        getCurrentCourse() {
            //const defaultCourse = this.filters?.seeds?.courses[0]?.id || null;
            //const course = parseInt(this.query?.course || defaultCourse);
            const course = parseInt(this.filters?.trace?.course_id || null);
            return isNaN(course) ? 0 : course;
        },
        getCurrentCampus() {
            //const defaultCampus = this.filters?.seeds?.campuses[0]?.id || null;
            //const campus = parseInt(this.query?.campus || defaultCampus);
            const campus = parseInt(this.filters?.trace?.campus_id || null);
            return isNaN(campus) ? 0 : campus;
        },
        getCurrentBatch() {
            //const defaultBatch = this.filters?.batches[0]?.id || null;
            //const batch = parseInt(this.query?.batch || defaultBatch);
            const batch = parseInt(this.filters?.trace?.batch_id || null);
            return isNaN(batch) ? 0 : batch;
        },
        getAllStudentsForWarning() {
            return this.prepareStudentsDataForWarning(this.gridData, false);
        },
        getFilterScopes() {
            return this.filters.seeds || [];
        },
        getFilters() {
            return this.filters.trace || [];
        },
        getFilterParams() {
            return this.filterSettings.filter((item) => item.visible === true);
        },
    },
    methods: {
        defineColumns() {
            this.columns = columnsData.map((column, index) => {
                return {
                    field: column.field,
                    title: column.title,
                    width: this.setWidth(column.minWidth),
                    checked: column?.cheked,
                    minResizableWidth: column.minResizableWidth ? column.minResizableWidth : 100,
                    headerCell: column.headerCell || '',
                    cell: column.cell || '',
                };
            });
        },
        handleResize() {
            if (this.gridElement.offsetWidth < minGridWidth && !this.setMinWidth) {
                this.setMinWidth = true;
            } else if (this.gridElement.offsetWidth > minGridWidth) {
                this.gridCurrent = this.gridElement.offsetWidth;
                this.setMinWidth = false;
            }
            this.defineColumns();
        },
        setWidth(minWidth) {
            let width = this.setMinWidth
                ? minWidth
                : minWidth + (this.gridCurrent - minGridWidth) / columnsData.length;
            width < COLUMN_MIN ? width : (width -= ADJUST_PADDING);
            return width;
        },
        prepareStudentsDataForWarning,
        handleBackAction() {
            const processFlow = this.processFlow.find(
                (process) => process.current === this.currentProcess
            );
            this.currentProcess = processFlow?.next || 'grid';
        },
        handleWarningSentAction() {
            this.handleBackAction();
            this.resource.fetch();
            return;
        },
        toggleAll(status) {
            this.gridData.forEach((student) => {
                student.selected = status;
            });
        },
        onHeaderSelectionChange(event) {
            let checked = event.event.target.checked;
            this.toggleAll(checked);
        },
        handleBulkCancel() {
            this.toggleAll(false);
        },
        pageChangeHandler: function (event) {
            const page = parseInt(event.page.skip / event.page.take);
            this.resource.state.filters.page = isNaN(page) ? 1 : page + 1;
            this.resource.state.filters.take = event.page.take;
            return true;
        },
        setPercentage(percentage) {
            return Math.round(this.gridWidth / 100) * percentage;
        },
        handleSort(field) {
            this.sortDir = !this.sortDir;
            this.resource.state.filters.sort = field;
            this.resource.state.filters.dir = this.sortDir ? 'desc' : 'asc';
        },
        iscurrentSorting(field, askfor) {
            if (this.resource.state.filters.sort == field) {
                return askfor == 'color' ? '#1890ff' : 'text-primary-blue-500';
            }
            return askfor == 'color' ? '#9CA3AF' : null;
        },
        getSortIcon(field) {
            if (this.resource.state.filters.sort == field) {
                return this.resource.state.filters.dir == 'desc' ? 'down' : 'up';
            }
            return 'chevron-up-down';
        },
        askTrainer(item) {
            return;
        },
        reloadGrid() {
            this.resource.fetch();
            return;
        },
        print() {
            console.log('print');
        },
        download() {
            console.log('download');
        },
        warningSettings() {
            this.openWarningSettingsDialog = true;
        },
        closeWarningSettingsDialog() {
            this.openWarningSettingsDialog = false;
        },
        toggleFilterWindow() {
            return;
        },
        onSelectionChange(event) {
            event.dataItem[this.selectedField] = !event.dataItem[this.selectedField];
        },
        onRowClick(event) {
            this.toggleAll(false);
            event.dataItem[this.selectedField] = !event.dataItem[this.selectedField];
        },
        handleActionsClick(e, item) {
            switch (e.target.closest('[data-item]').dataset.item) {
                case 'sms':
                    this.getActionStudent(item);
                    this.sendSms(item);
                    break;
                case 'email':
                    this.getActionStudent(item);
                    this.sendEmailShow(item);
                    break;
                case 'warning':
                    this.getActionStudent(item);
                    this.sendWarningShow(item);
                    break;
                case 'profile':
                    const url = this.route('student-profile-view', [item.student_id]);
                    window.open(url, '_blank');
                    break;
            }
        },
        sendWarningShow() {
            this.currentProcess = 'warning';
        },
        sendEmailShow(item) {
            this.isSendMailPopupShow = true;
            return false;
        },
        sendMailPopupShow() {
            this.isSendMailPopupShow = false;
            return false;
        },
        sendSmsShow() {
            return false;
        },
        showWarningDetails(data) {
            this.studentForLog = data;
            this.showWarningLogs = true;
        },
        hideWarningDetails() {
            this.studentForLog = [];
            this.showWarningLogs = false;
        },
        getActionStudent(item) {
            let index = this.gridData.findIndex((obj) => obj.timetable_id === item.timetable_id);

            if (index !== -1) {
                this.gridData[index].selected = true;
            }
        },
        hidecolumn: function (field, value) {
            this.columns.map((column) => {
                if (column.field === field) {
                    this.filterLoader = true;
                    setTimeout(() => {
                        column.hidden = value;
                        this.filterLoader = false;
                    }, 500);
                }
            });
        },
        restoreColumns() {
            this.columns.map((column) => {
                if (column.hidden) {
                    this.filterLoader = true;
                    setTimeout(() => {
                        column.hidden = false;
                        column.checked = true;
                        this.filterLoader = false;
                    }, 500);
                }
            });
        },
        handleCheckToggle(field, value) {
            this.hidecolumn(field, value);
        },
        handleFilterUpdated(resp) {
            this.resource.state.filters.type = resp.course_type || null;
            this.resource.state.filters.year = resp.year || null;
            this.resource.state.filters.course = resp.course_id || null;
            this.resource.state.filters.semester = resp.semester_id || null;
            this.resource.state.filters.subject = resp.subject_id || null;
            this.resource.state.filters.batch = resp.batch_id || null;
            this.handleResize();
        },
        handleFilterChanged(resp) {
            resp.forEach((obj) => {
                const field = Object.keys(obj)[0];
                const value = obj[field] || null;
                if (field && this.resource.state.filters.hasOwnProperty(field)) {
                    this.resource.state.filters[field] = value || null;
                }
            });
            this.handleResize();
        },
        handleFilterSelected(resp) {
            this.handleResize();
            const field = resp.name || null;
            if (field && this.resource.state.filters.hasOwnProperty(field)) {
                this.resource.state.filters[field] = resp.selected || null;
            }
        },
        updateSettingsData(data) {
            this.warningSettingsData = data ?? {};
            this.closeWarningSettingsDialog();
        },
    },
    watch: {
        settingsData: {
            handler(newval) {
                this.warningSettingsData = newval;
            },
            deep: true,
        },
    },
};
</script>
