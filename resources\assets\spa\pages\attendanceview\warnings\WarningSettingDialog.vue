<template>
    <k-dialog
        :visibleDialog="visible"
        :hideOnOverlayClick="true"
        :fixedActionBar="true"
        :width="'40%'"
        @modalclose="closeModal"
        @modalsaved="saveModel"
        :isSubmitting="loading"
        :primaryBtnLabel="loading ? 'Saving' : 'Save'"
        :isDisabled="loading"
    >
        <template #title>
            <div class="text-lg font-medium">Attendance Warning Settings</div>
        </template>
        <template #content>
            <div class="text-gray-600">
                <form novalidate>
                    <input-group
                        label="Mail Status"
                        :labelClass="'text-sm font-medium text-gray-700'"
                        class="space-y-1"
                    >
                        <Switch v-model="attendanceSettingsData.status" name="mail_status" />
                    </input-group>
                    <input-group class="space-y-2">
                        <label class="font-medium">Send Warning Email automatically when</label>
                        <div class="space-y-2 rounded border p-4">
                            <div class="flex items-center space-x-1">
                                <div>
                                    <Checkbox
                                        v-model="
                                            attendanceSettingsData.send_on_consequitive_absence
                                        "
                                        id="send_on_consequitive_absence"
                                        :disabled="!attendanceSettingsData.status"
                                    />
                                </div>
                                <label for="send_on_consequitive_absence">
                                    Student has consecutive absence of
                                </label>
                                <div class="w-16">
                                    <numerictextbox
                                        v-model="
                                            attendanceSettingsData.consequitive_absence_days_threshold
                                        "
                                        :spinners="false"
                                        :step="1"
                                        :min="0"
                                        :max="30"
                                        :class="w - 10"
                                        :disabled="
                                            !attendanceSettingsData.send_on_consequitive_absence ||
                                            !attendanceSettingsData.status
                                        "
                                    />
                                </div>
                                <label for="send_on_consequitive_absence">Days</label>
                            </div>
                            <div class="flex-grow">
                                <label for="send_on_consequitive_absence">Template to use</label>
                                <DropDownList
                                    v-model="attendanceSettingsData.template_days"
                                    data-item-key="id"
                                    :value-primitive="true"
                                    value-field="id"
                                    text-field="text"
                                    :data-items="templates"
                                    group-field="group"
                                    :filterable="true"
                                    @filterchange="filterChange"
                                    :default-item="{
                                        text: 'Select Template',
                                        id: '',
                                    }"
                                    :popup-settings="{
                                        className: 'tw-width-auto',
                                        animate: false,
                                    }"
                                    :disabled="!attendanceSettingsData.status"
                                >
                                </DropDownList>
                            </div>
                        </div>
                        <div class="space-y-2 rounded border p-4">
                            <div class="flex items-center space-x-1">
                                <div>
                                    <Checkbox
                                        v-model="
                                            attendanceSettingsData.send_on_attendance_percentage
                                        "
                                        id="send_on_attendance_percentage"
                                        :disabled="!attendanceSettingsData.status"
                                    />
                                </div>
                                <label for="send_on_attendance_percentage">
                                    Student has consecutive absence of
                                </label>
                                <div class="w-16">
                                    <numerictextbox
                                        v-model="
                                            attendanceSettingsData.absence_precentage_threshold
                                        "
                                        :spinners="false"
                                        :step="1"
                                        :min="0"
                                        :max="100"
                                        :class="w - 10"
                                        :disabled="
                                            !attendanceSettingsData.send_on_attendance_percentage ||
                                            !attendanceSettingsData.status
                                        "
                                    />
                                </div>
                                <label for="send_on_attendance_percentage">%</label>
                            </div>
                            <div class="flex-grow">
                                <label for="send_on_consequitive_absence">Template to use</label>
                                <DropDownList
                                    v-model="attendanceSettingsData.template_precentage"
                                    data-item-key="id"
                                    :value-primitive="true"
                                    value-field="id"
                                    text-field="text"
                                    :data-items="templates"
                                    group-field="group"
                                    :filterable="true"
                                    @filterchange="filterChange"
                                    :default-item="{
                                        text: 'Select Template',
                                        id: '',
                                    }"
                                    :popup-settings="{
                                        className: 'tw-width-auto',
                                        animate: false,
                                    }"
                                    :disabled="!attendanceSettingsData.status"
                                >
                                </DropDownList>
                            </div>
                        </div>
                    </input-group>
                </form>
            </div>
        </template>
    </k-dialog>
</template>
<script>
import axios from 'axios';
import Button from '@spa/components/Buttons/Button';
import KendoDialog from '@spa/components/KendoModals/KendoDialog.vue';
import TextInput from '@spa/components/TextInput.vue';
import { DropDownList } from '@progress/kendo-vue-dropdowns';
import { Input, NumericTextBox, Switch, RadioButton, Checkbox } from '@progress/kendo-vue-inputs';
import { getWarningTemplates } from '@spa/services/attendanceResource.js';
import { groupBy, filterBy } from '@progress/kendo-data-query';

export default {
    props: {
        visible: { type: Boolean, default: false },
        data: { type: Object, default: {} },
    },
    components: {
        Button,
        'k-dialog': KendoDialog,
        TextInput,
        Input,
        numerictextbox: NumericTextBox,
        DropDownList,
        Switch,
        Checkbox,
    },
    data() {
        return {
            cancelSource: axios.CancelToken.source(),
            attendanceSettingsData: this.data,
            templateRefreshInterval: null,
            templates: [],
            templatesList: [],
            loading: false,
        };
    },
    mounted() {
        this.loadTemplates();
        this.startTemplateRefresh();
    },
    computed: {
        getTemplateList() {
            return [
                {
                    id: 1,
                    text: 'Template 1',
                },
                {
                    id: 2,
                    text: 'Template 2',
                },
            ];
        },
    },
    methods: {
        getWarningTemplates,
        closeModal() {
            this.stopTemplateRefresh();
            this.$emit('close');
        },
        async loadTemplates() {
            this.templatesList = await this.getWarningTemplates('warning', false);
            this.templatesList = this.groupAndFlatten(this.templatesList);
            this.templates = this.templatesList;
        },
        filterChange(event) {
            this.templates = this.filterData(event.filter);
        },
        filterData(filter) {
            const data = this.templatesList.slice();
            return filterBy(data, filter);
        },
        groupAndFlatten(data) {
            return data.warnings.flatMap((group) =>
                (group.items || []).map((item) => ({
                    id: item.email_template_id,
                    text: item.title,
                    group: group.title,
                }))
            );
        },
        startTemplateRefresh() {
            this.templateRefreshInterval = setInterval(() => {
                this.loadTemplates();
            }, 10000);
        },
        stopTemplateRefresh() {
            if (this.templateRefreshInterval) {
                clearInterval(this.templateRefreshInterval);
                this.templateRefreshInterval = null;
            }
        },
        saveModel() {
            if (this.loading) {
                return false;
            }
            this.loading = true;

            $http
                .post(route('spa.attendance.save.warningsettings'), this.attendanceSettingsData, {
                    cancelToken: this.cancelSource.token,
                })
                .then((resp) => {
                    if (resp.status === 'error') {
                        this.loading = false;
                    } else {
                        this.closeModal();
                    }
                })
                .catch((error) => {
                    this.loading = false;
                })
                .finally((resp) => {
                    this.loading = false;
                });
        },
    },
    watch: {
        data: {
            handler(newval, oldval) {
                this.attendanceSettingsData = newval;
            },
            deep: true,
        },
    },
};
</script>
