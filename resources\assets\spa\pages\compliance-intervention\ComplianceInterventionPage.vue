<script setup>
import Layout from '@spa/pages/Layouts/Layout';
import { Head } from '@inertiajs/vue3';
import PageTitleContent from '@spa/pages/Layouts/PageTitleContent.vue';
import StudentInterventionListComponent from '@spa/modules/studentintervention/StudentInterventionListComponent.vue';
</script>
<template>
    <Layout :no-spacing="true">
        <Head title="Student Intervention" />
        <template v-slot:pageTitleContent>
            <PageTitleContent title="Student Intervention" :back="false" />
        </template>
        <div class="h-screen-header flex flex-col px-8 py-6">
            <StudentInterventionListComponent />
        </div>
    </Layout>
</template>
<style scoped></style>
