<template>
    <Layout :loading="true" :noSpacing="true">
        <Head title="Courses List" />
        <template v-slot:pageTitleContent>
            <PageTitleContent :title="'List of Available Courses'" :back="false" />
        </template>
        <div class="h-screen-header mx-auto flex flex-col space-y-4 px-8 py-4">
            <div class="space-y-4">
                <h2 class="justify-start text-2xl font-semibold text-gray-800">Manage Courses</h2>
                <div class="flex items-center justify-between gap-4">
                    <div class="coursesSearchInputField relative w-full md:w-fit">
                        <span class="absolute left-3 top-1/2 -translate-y-1/2">
                            <icon :name="'lens'" width="16" height="16" />
                        </span>
                        <input
                            type="text"
                            id="searchKeyword"
                            ref="searchKeyword"
                            v-model.lazy="$resource.state.filters['keyword']"
                            v-debounce="300"
                            class="tw-input-text h-[2.32rem] w-[300px] pl-8"
                            placeholder="Search"
                            autocomplete="off"
                            @change="resetPagination"
                            @focus="searchingByKeyword = true"
                            @blur="searchingByKeyword = false"
                        />
                    </div>
                    <div class="flex w-full flex-wrap items-start justify-end gap-2">
                        <div class="w-full md:w-fit">
                            <Link :href="route('spa.courses.add')">
                                <Button size="sm">
                                    <icon :name="'plus'" :fill="'white'" width="16" height="16" />
                                    <span>Add Course</span>
                                </Button>
                            </Link>
                        </div>
                        <div class="w-full md:w-fit">
                            <Button variant="secondary" size="sm" @click="addCourseManually">
                                <icon :name="'plus'" width="16" height="16" />
                                <span>Add Course Manually</span>
                            </Button>
                        </div>
                        <div class="flex space-x-2" v-if="isMoodleConnect">
                            <Button
                                variant="secondary"
                                size="sm"
                                @click="handleBulkSyncToMoodle"
                                title="Bulk sync to Moodle"
                                :disabled="selectedIds.size === 0"
                            >
                                <icon :name="'sync'" width="16" height="16" />
                                <span title="Bulk Sync To Moodle">Sync To Moodle</span>
                            </Button>
                            <Button
                                variant="secondary"
                                size="sm"
                                @click="handleAllCourseSyncFromMoodle"
                                title="Sync From Moodle"
                            >
                                <icon :name="'import'" width="16" height="16" />
                                <span>Sync From Moodle</span>
                            </Button>
                        </div>
                    </div>
                </div>
                <div class="flex items-center gap-2">
                    <template v-if="selectedIds.size || isMoodleConnect">
                        <Tooltip
                            :anchor-element="'target'"
                            :position="'top'"
                            :parentTitle="true"
                            :className="'flex items-center'"
                            :tooltipClassName="'!p-1.5'"
                        >
                            <div
                                class="border-gray-1 flex items-center gap-1 rounded-lg border border-gray-300 bg-white px-2 py-2"
                            >
                                <checkbox
                                    :checked="isAllChecked"
                                    @change="toggleSelectAll"
                                    :label="
                                        selectedIds.size === 0
                                            ? 'Check All'
                                            : `${selectedIds.size} Selected`
                                    "
                                    class="inline-flex"
                                />
                                <template v-if="selectedIds.size != 0">
                                    <div class="h-4 w-px bg-gray-200"></div>

                                    <Button
                                        variant="icon"
                                        class="h-5 w-5 p-px"
                                        title="Clear Selection"
                                        @click="handleAllRemoveSelectedIds"
                                    >
                                        <icon name="cross" />
                                    </Button>
                                </template>
                            </div>
                        </Tooltip>
                    </template>
                    <div class="w-full basis-auto md:min-w-[110px] md:max-w-[150px]">
                        <DropDownList
                            :data-items="filterScopes?.types"
                            :text-field="'label'"
                            :data-item-key="'value'"
                            :valueField="'value'"
                            v-model.lazy="$resource.state.filters['type']"
                            :valuePrimitive="true"
                            :default-item="{
                                label: 'All Types',
                                value: null,
                            }"
                            :popup-settings="{
                                className: 'tw-width-auto',
                                animate: false,
                            }"
                            @change="resetPagination"
                        >
                        </DropDownList>
                    </div>
                    <div class="w-full basis-auto md:min-w-[110px] md:max-w-[150px]">
                        <DropDownList
                            :data-items="filterScopes?.faculties"
                            :text-field="'text'"
                            :data-item-key="'id'"
                            :valueField="'id'"
                            v-model.lazy="$resource.state.filters['faculty']"
                            :valuePrimitive="true"
                            :default-item="{
                                text: 'All Faculties',
                                id: null,
                            }"
                            :popup-settings="{
                                className: 'tw-width-auto',
                                animate: false,
                            }"
                            @change="resetPagination"
                            :class="'!h-[2.32rem]'"
                        >
                        </DropDownList>
                    </div>
                    <div class="w-full basis-auto md:min-w-[110px] md:max-w-[150px]">
                        <DropDownList
                            :data-items="filterScopes?.status"
                            :text-field="'text'"
                            :data-item-key="'id'"
                            :valueField="'id'"
                            v-model.lazy="$resource.state.filters['status']"
                            :valuePrimitive="true"
                            :default-item="{
                                text: 'All Status',
                                id: null,
                            }"
                            :popup-settings="{
                                className: 'tw-width-auto',
                                animate: false,
                            }"
                            @change="resetPagination"
                            :class="'!h-[2.32rem]'"
                        >
                        </DropDownList>
                    </div>
                    <div class="w-full basis-auto md:min-w-[110px] md:max-w-[150px]">
                        <DropDownList
                            :data-items="filterScopes?.duration"
                            :text-field="'text'"
                            :data-item-key="'id'"
                            :valueField="'id'"
                            v-model.lazy="$resource.state.filters['duration']"
                            :valuePrimitive="true"
                            :default-item="{
                                text: 'All Duration',
                                id: null,
                            }"
                            :popup-settings="{
                                className: 'tw-width-auto',
                                animate: false,
                            }"
                            @change="resetPagination"
                            :class="'!h-[2.32rem]'"
                        >
                        </DropDownList>
                    </div>
                </div>
                <div class="flex items-center" v-if="pageFilters.active">
                    <div
                        class="border-1 mr-2 rounded-xl border-gray-50 bg-white px-2 py-1"
                        v-if="pageFilters.type.name"
                    >
                        <div class="flex justify-between space-x-4">
                            <div class="text-sm text-gray-700">
                                {{ pageFilters.type.name }}
                            </div>
                            <div
                                class="cursor-pointer text-gray-400 hover:text-primary-blue-500"
                                @click="clearType"
                            >
                                <svg
                                    width="12"
                                    height="20"
                                    viewBox="0 0 24 24"
                                    fill="none"
                                    xmlns="http://www.w3.org/2000/svg"
                                >
                                    <path
                                        d="M6 18L18 6M6 6L18 18"
                                        stroke="gray"
                                        stroke-width="2"
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                    />
                                </svg>
                            </div>
                        </div>
                    </div>
                    <div
                        class="border-1 mr-2 rounded-xl border-gray-50 bg-white px-2 py-1"
                        v-if="pageFilters.faculty.name"
                    >
                        <div class="flex items-center justify-between space-x-4">
                            <div class="text-sm text-gray-700">
                                {{ pageFilters.faculty.name }}
                            </div>
                            <span class="cursor-pointer" @click="clearFaculty">
                                <icon :name="'cross'" :width="12" :height="12" />
                            </span>
                        </div>
                    </div>
                    <div
                        class="border-1 mr-2 rounded-xl border-gray-50 bg-white px-2 py-1"
                        v-if="pageFilters.status.name"
                    >
                        <div class="flex items-center justify-between space-x-4">
                            <div class="text-sm text-gray-700">
                                {{ pageFilters.status.name }}
                            </div>
                            <span class="cursor-pointer" @click="clearStatus">
                                <icon :name="'cross'" :width="12" :height="12" />
                            </span>
                        </div>
                    </div>
                    <div
                        class="border-1 mr-2 rounded-xl border-gray-50 bg-white px-2 py-1"
                        v-if="pageFilters.duration.name"
                    >
                        <div class="flex items-center justify-between space-x-4">
                            <div class="text-sm text-gray-700">
                                {{ pageFilters.duration.name }}
                            </div>
                            <span class="cursor-pointer" @click="clearDuration">
                                <icon :name="'cross'" :width="12" :height="12" />
                            </span>
                        </div>
                    </div>
                    <div class="py-1">
                        <div
                            v-if="pageFilters.active"
                            class="flex cursor-pointer justify-between space-x-4"
                            @click="clearAllFilters"
                        >
                            <div
                                class="cursor-pointer text-sm text-primary-blue-500 hover:text-primary-blue-600"
                            >
                                Clear All Filters
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <!-- <div class="" v-if="isMoodleConnect">
      <checkbox :checked="isAllSelected" @change="toggleSelectAll" />
    </div> -->

            <div ref="listContainer" class="flex-1 overflow-y-auto">
                <GlobalContextLoader context="course-list" :overlay="true">
                    <template v-if="courses.data.length">
                        <div class="space-y-4">
                            <div
                                v-for="(course, index) in courses.data"
                                :key="index"
                                class="tw-slideup tw-animate group rounded-md border border-gray-200 bg-gray-50 py-2 pe-6 ps-4"
                                :style="animationDelayStyle[index]"
                            >
                                <div
                                    class="block justify-between gap-4 md:flex lg:gap-0"
                                    :id="`course${index + 1}`"
                                >
                                    <button
                                        class="block cursor-pointer items-center gap-3 md:flex"
                                        @click="
                                            followToProfile(
                                                course.course_code,
                                                course.has_all_information
                                            )
                                        "
                                        title="Click to view course"
                                    >
                                        <div
                                            class="my-auto mr-4 flex h-fit items-center space-x-2"
                                            :title="
                                                course.moodle_data?.sync_status === 'Synced'
                                                    ? 'Already Synced'
                                                    : ''
                                            "
                                            v-if="isMoodleConnect"
                                        >
                                            <checkbox
                                                @click="$event.stopPropagation()"
                                                :checked="selectedIds.has(course.id)"
                                                @change="() => toggleRowSelection(course.id)"
                                                :disabled="
                                                    course.moodle_data?.sync_status === 'Synced'
                                                "
                                            />
                                        </div>
                                        <!-- <div class="mr-4 hidden md:block" v-else>
                                <icon :name="'coursebook'" :width="48" :height="48" />
                            </div> -->

                                        <div class="space-y-4 lg:space-y-0">
                                            <div
                                                class="mb-2 grid w-full grid-cols-1 gap-2 space-x-0 md:flex lg:gap-0 lg:space-x-2"
                                            >
                                                <div
                                                    class="my-auto flex items-center justify-between gap-4 font-medium"
                                                >
                                                    <div>
                                                        {{ course.course_code }} -
                                                        {{ course.course_name }}
                                                    </div>
                                                    <Tooltip
                                                        :anchor-element="'target'"
                                                        :position="'top'"
                                                        :parentTitle="true"
                                                        :className="'flex items-center'"
                                                        :tooltipClassName="'!p-1.5'"
                                                    >
                                                        <div
                                                            class="relative right-auto top-auto flex translate-y-0 items-center space-x-2 md:absolute md:right-4 md:top-1/2 md:-translate-y-1/2"
                                                        >
                                                            <span
                                                                v-if="
                                                                    loaderStore.contextLoaders[
                                                                        `course_moodle_sync_${course.id}`
                                                                    ]
                                                                "
                                                                >Syncing..</span
                                                            >
                                                            <template v-else>
                                                                <div
                                                                    class="tw-btn-action h-7 w-7 border border-gray-200"
                                                                    title="Re-sync to Moodle"
                                                                    v-if="
                                                                        isMoodleConnect &&
                                                                        course.moodle_data
                                                                            ?.sync_status ===
                                                                            'Sync Failed'
                                                                    "
                                                                    @click="
                                                                        handleSyncToMoodle(
                                                                            course,
                                                                            're-sync'
                                                                        );
                                                                        $event.stopPropagation();
                                                                    "
                                                                >
                                                                    <icon
                                                                        :name="'re-sync'"
                                                                        :width="20"
                                                                        :height="20"
                                                                    />
                                                                </div>

                                                                <div
                                                                    class="tw-btn-action h-7 w-7 border border-gray-200"
                                                                    title="Sync to Moodle"
                                                                    v-if="
                                                                        isMoodleConnect &&
                                                                        course.moodle_data
                                                                            ?.sync_status !==
                                                                            'Sync Failed' &&
                                                                        course.moodle_data
                                                                            ?.sync_status !==
                                                                            'Synced'
                                                                    "
                                                                    @click="
                                                                        handleSyncToMoodle(course);
                                                                        $event.stopPropagation();
                                                                    "
                                                                >
                                                                    <icon
                                                                        :name="'sync'"
                                                                        :width="16"
                                                                        :height="16"
                                                                    />
                                                                </div>
                                                            </template>

                                                            <div
                                                                class="tw-btn-action h-7 w-7 border border-gray-200"
                                                            >
                                                                <Link
                                                                    :href="
                                                                        route(
                                                                            'spa.courses.profile',
                                                                            [course.course_code]
                                                                        )
                                                                    "
                                                                    title="View course profile"
                                                                    v-if="
                                                                        course.has_all_information
                                                                    "
                                                                >
                                                                    <icon
                                                                        :name="'pencil'"
                                                                        :width="16"
                                                                        :height="16"
                                                                    />
                                                                </Link>
                                                                <Link
                                                                    :href="
                                                                        route(
                                                                            'spa.courses.detailadd',
                                                                            [course.course_code]
                                                                        )
                                                                    "
                                                                    title="Complete all required information"
                                                                    v-else
                                                                >
                                                                    <icon
                                                                        :name="'pencil'"
                                                                        :width="16"
                                                                        :height="16"
                                                                    />
                                                                </Link>
                                                            </div>
                                                            <CourseActionsMenu
                                                                :isMoodleConnect="isMoodleConnect"
                                                                :courseItem="course"
                                                                @structure="
                                                                    showCourseStructure(course)
                                                                "
                                                                @delete="handleDelete(course)"
                                                                @sync="handleSyncToMoodle(course)"
                                                                @changestatus="
                                                                    handleStatusChange(course)
                                                                "
                                                            />
                                                        </div>
                                                    </Tooltip>
                                                </div>

                                                <div
                                                    class="grid w-fit grid-cols-1 gap-2 md:flex md:grid-cols-2"
                                                >
                                                    <Link
                                                        :href="
                                                            route('spa.courses.detailadd', [
                                                                course.course_code,
                                                            ])
                                                        "
                                                        title="Click to complete course profile"
                                                        v-if="!course.has_all_information"
                                                        class="cursor_pointer"
                                                    >
                                                        <CourseStatusBtn
                                                            :status="course.activated_now"
                                                            :completed="course.has_all_information"
                                                        />
                                                    </Link>
                                                    <CourseStatusBtn
                                                        :status="course.activated_now"
                                                        :completed="course.has_all_information"
                                                        v-else
                                                    />
                                                    <div
                                                        class="rounded-lg bg-red-50 px-2 py-0.5"
                                                        v-if="course.superseededinfo?.is_superseded"
                                                    >
                                                        <div
                                                            class="text-xs font-normal text-red-700"
                                                        >
                                                            Superseded by:
                                                            {{
                                                                course.superseededinfo
                                                                    ?.superseded_course?.Code
                                                            }}
                                                            on
                                                            {{
                                                                course.superseededinfo
                                                                    ?.superseded_date
                                                            }}
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                            <div
                                                class="grid w-full grid-cols-2 gap-2 space-x-0 text-xs md:grid-cols-3 lg:space-x-2 xl:flex"
                                            >
                                                <div class="flex">
                                                    <div class="mr-2 text-gray-400">
                                                        Course Type:
                                                    </div>
                                                    <div class="text-gray-700">
                                                        {{ course.course_type_text }}
                                                    </div>
                                                </div>
                                                <div class="flex">
                                                    <div class="mr-2 text-gray-400">Duration:</div>
                                                    <div class="text-gray-700">
                                                        {{ course.duration_text }}
                                                    </div>
                                                </div>
                                                <div class="flex">
                                                    <div class="mr-2 text-gray-400">
                                                        Delivery Target:
                                                    </div>
                                                    <div class="text-gray-700">
                                                        {{ course.delivery_target_text }}
                                                    </div>
                                                </div>
                                                <div class="flex">
                                                    <div class="mr-2 text-gray-400">
                                                        {{ course.fee_target_text }} Fee:
                                                    </div>
                                                    <div class="text-gray-700">
                                                        {{ course.fee_text }}
                                                    </div>
                                                </div>
                                                <div class="flex">
                                                    <div class="mr-2 text-gray-400">
                                                        PreRequisite:
                                                    </div>
                                                    <div class="text-gray-700">
                                                        {{ course.prerequisite_text || 'N/A' }}
                                                    </div>
                                                </div>
                                                <div class="flex">
                                                    <div class="mr-2 text-gray-400">WP Hours:</div>
                                                    <div class="text-gray-700">
                                                        {{ course.total_hours_text || 'N/A' }}
                                                    </div>
                                                </div>
                                            </div>
                                            <div
                                                class="flex w-full space-x-2 pt-2 text-xs"
                                                v-if="isMoodleConnect"
                                            >
                                                <div class="flex">
                                                    <div class="mr-2 text-gray-400">
                                                        Moodle Status:
                                                    </div>
                                                    <div
                                                        :class="{
                                                            'text-green-600':
                                                                course.moodle_data?.sync_status ===
                                                                'Synced',
                                                            'text-red-600':
                                                                course.moodle_data?.sync_status ===
                                                                'Sync Failed',
                                                            'text-gray-700':
                                                                course.moodle_data?.sync_status ===
                                                                'Not Sync',
                                                        }"
                                                    >
                                                        {{
                                                            course.moodle_data?.sync_status ??
                                                            'Not Synced'
                                                        }}
                                                    </div>
                                                </div>
                                                <div
                                                    class="flex"
                                                    v-if="
                                                        course.moodle_data?.sync_status ===
                                                            'Synced' ||
                                                        course.moodle_data?.sync_status ===
                                                            'Sync Failed'
                                                    "
                                                >
                                                    <div class="mr-2 text-gray-400">
                                                        {{
                                                            course.moodle_data?.sync_status ===
                                                            'Sync Failed'
                                                                ? 'Failed At:'
                                                                : 'Synced At:'
                                                        }}
                                                    </div>
                                                    <div class="text-gray-700">
                                                        {{
                                                            course.moodle_data?.sync_status ===
                                                            'Sync Failed'
                                                                ? course.moodle_data?.failed_at
                                                                : course.moodle_data?.sync_at
                                                        }}
                                                    </div>
                                                </div>
                                                <div
                                                    class="flex"
                                                    v-if="
                                                        course.moodle_data?.sync_status ===
                                                            'Sync Failed' &&
                                                        course.moodle_data?.failed_msg
                                                    "
                                                >
                                                    <div class="mr-2 text-gray-400">
                                                        Error Message:
                                                    </div>
                                                    <div class="text-red-700">
                                                        {{ course.moodle_data?.failed_msg }}
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </button>
                                </div>
                            </div>
                        </div>
                    </template>
                    <div
                        v-else
                        class="flex h-80 w-full items-center rounded-md border border-gray-200 bg-gray-50 p-6"
                    >
                        <div class="mx-auto items-center space-y-4">
                            <div class="flex justify-center">
                                <div class="left-icon">
                                    <svg
                                        width="48"
                                        height="42"
                                        viewBox="0 0 48 42"
                                        fill="none"
                                        xmlns="http://www.w3.org/2000/svg"
                                    >
                                        <path
                                            d="M14.4 10.2769C14.4 9.28279 15.2059 8.4769 16.2 8.4769H30.6C31.5941 8.4769 32.4 9.28279 32.4 10.2769V13.8769C32.4 14.871 31.5941 15.6769 30.6 15.6769H16.2C15.2059 15.6769 14.4 14.871 14.4 13.8769V10.2769ZM17.1 12.9769H29.7V11.1769H17.1V12.9769ZM9 7.5769C9 5.09162 11.0147 3.0769 13.5 3.0769H34.2C36.6853 3.0769 38.7 5.09162 38.7 7.5769V33.2269C38.7 33.9725 38.0956 34.5769 37.35 34.5769H11.7C11.7 35.571 12.5059 36.3769 13.5 36.3769H37.35C38.0956 36.3769 38.7 36.9813 38.7 37.7269C38.7 38.4725 38.0956 39.0769 37.35 39.0769H13.5C11.0147 39.0769 9 37.0622 9 34.5769V7.5769ZM11.7 31.8769H36V7.5769C36 6.58279 35.1941 5.7769 34.2 5.7769H13.5C12.5059 5.7769 11.7 6.58279 11.7 7.5769V31.8769Z"
                                            fill="#1890FF"
                                        />
                                    </svg>
                                </div>
                                <div class="course"></div>
                            </div>
                            <div class="flex justify-center text-gray-400">
                                You have not added any courses
                            </div>
                            <div class="flex justify-center">
                                <Link :href="route('spa.courses.add')">
                                    <PrimaryButton>
                                        <svg
                                            width="16"
                                            height="16"
                                            viewBox="0 0 16 16"
                                            fill="none"
                                            xmlns="http://www.w3.org/2000/svg"
                                        >
                                            <path
                                                fill-rule="evenodd"
                                                clip-rule="evenodd"
                                                d="M7.99844 2.3999C8.44027 2.3999 8.79844 2.75807 8.79844 3.1999V7.1999H12.7984C13.2403 7.1999 13.5984 7.55807 13.5984 7.9999C13.5984 8.44173 13.2403 8.7999 12.7984 8.7999H8.79844V12.7999C8.79844 13.2417 8.44027 13.5999 7.99844 13.5999C7.55661 13.5999 7.19844 13.2417 7.19844 12.7999V8.7999H3.19844C2.75661 8.7999 2.39844 8.44173 2.39844 7.9999C2.39844 7.55807 2.75661 7.1999 3.19844 7.1999H7.19844V3.1999C7.19844 2.75807 7.55661 2.3999 7.99844 2.3999Z"
                                                fill="white"
                                            />
                                        </svg>
                                        <div class="ml-2 capitalize">Add Course</div>
                                    </PrimaryButton>
                                </Link>
                            </div>
                        </div>
                    </div>
                </GlobalContextLoader>
            </div>
            <div class="course-pagination my-4" v-if="courses.data.length">
                <pager
                    :skip="$resource.state.pageable.skip"
                    :take="$resource.state.pageable.pageSizeValue"
                    :total="$resource.state.pageable.totalItems"
                    @changedPage="handlePageChange"
                    @changedPageSize="handlePagesizeChange"
                    :button-count="$resource.state.pageable.buttonCount"
                    :info="$resource.state.pageable.info"
                    :previous-next="true"
                    :type="'numeric'"
                    :page-sizes="$resource.state.pageable.pageSizes"
                    :pager-render="'myTemplate'"
                    :responsive="false"
                >
                    <template v-slot:myTemplate="{ props }">
                        <PagerTemplate
                            :current-page="props.current"
                            :page-size="props.perpage"
                            :skip="props.skip"
                            :take="props.take"
                            :size-defs="props.pageSizes"
                            :total-pages="$resource.state.pageable.totalPages"
                            :total-items="props.total"
                        />
                    </template>
                </pager>
            </div>
        </div>
        <div v-if="structureWindowVisible">
            <courseStructure
                :course="courseStructureData"
                :visible="structureWindowVisible"
                @closed="closeStructureWindow"
            />
        </div>
        <QuickAddFormNew
            :course="localCourseUnits"
            v-model:visible="QuickAdd.QuickAddFormVisible"
            v-model:loading="QuickAdd.dataLoading"
            v-model:custom="QuickAdd.customCourse"
            :coursetypes="filterScopes?.types"
            :searchText="''"
            @added="handedSaved"
            @saved="handedSaved"
            @closed="closeQuickAddForm"
        />
    </Layout>
</template>
<script setup>
import { onMounted, reactive, ref, watch, computed, toRef, nextTick } from 'vue';
import { Pager } from '@progress/kendo-vue-data-tools';
import Layout from '../Layouts/Layout';
import { Head, Link, router } from '@inertiajs/vue3';
import Menu from './commons/Menu';
import PrimaryButton from '@spa/components/Buttons/PrimaryButton.vue';
import SecondaryButton from '@spa/components/Buttons/SecondaryButton.vue';
import CourseStatusBtn from './commons/CourseStatusButtons.vue';
import CourseActionsMenu from './commons/CourseActionsMenu.vue';
import PageTitleContent from '../Layouts/PageTitleContent';
import { ToolBar, ToolBarItem } from '@progress/kendo-buttons-vue-wrapper';
import { useCourseResource, setPagination } from '@spa/services/courseFormResource';
import courseStructure from './includes/courseStructure.vue';
import { DropDownList } from '@progress/kendo-vue-dropdowns';
import PagerTemplate from '@spa/components/ListViewPagination.vue';
import QuickAddFormNew from './QuickAddFormNew.vue';
import SkeletonList from '@spa/components/Skeleton/SkeletonList.vue';
import { useCoursesStore } from '@spa/stores/modules/courses';
import GlobalContextLoader from '@spa/components/Loader/GlobalContextLoader.vue';
import { useLoaderStore } from '@spa/stores/modules/global-loader';
import useConfirm from '@spa/services/useConfirm.js';
import { Checkbox } from '@progress/kendo-vue-inputs';
import globalHelper from '@spa/plugins/global-helper.js';
import Button from '@spa/components/Buttons/Button.vue';
import { Tooltip } from '@progress/kendo-vue-tooltip';

const loaderStore = useLoaderStore();

const props = defineProps({
    user: Object,
    query: Object,
    courses: Object,
    filterScopes: Object,
    isMoodleConnect: Boolean,
});

const { user, query, filterScopes, isMoodleConnect } = props;

const courses = toRef(props, 'courses');

const selectedIds = ref(new Set());
const isAllSelected = computed(() => {
    return (
        courses.value.data.length > 0 &&
        courses.value.data.every((item) => selectedIds.value.has(item.id))
    );
});

const store = useCoursesStore();
/*
store the value of filters in a store
this is help to preserve previous filter state when user clicks on back button
*/
const $resource = useCourseResource({
    filters: {
        //keyword: params.keyword,
        keyword: query?.keyword || null,
        type: query?.type || null,
        faculty: query?.faculty || null,
        status: query?.status || null,
        duration: query?.duration || null,
        take: query?.take || 10,
        page: query?.page || 1,
    },
    only: ['courses', 'filterScopes'],
});
const QuickAdd = reactive({
    QuickAddFormVisible: false,
    CourseData: null,
    dataLoading: false,
    customCourse: true,
});
const localCourseUnits = ref(null);
const foundFaculty = filterScopes?.faculties.find(
    (item) => parseInt(item.id) == parseInt(query?.faculty)
);
const defaultFaculty = foundFaculty ? foundFaculty : { text: 'Faculties', id: null };
const courseStructureData = ref([]);
const structureWindowVisible = ref(false);
const searchingByKeyword = ref(false);
const resetPagination = () => {
    $resource.state.filters.page = 1;
};
const listContainer = ref(null);

onMounted(() => {
    store.updateCourseListFilters($resource.state.filters);
    setPagination($resource, props.courses.meta);
});
watch(
    () => $resource.state.filters,
    (val) => {
        store.updateCourseListFilters(val);
        $resource.fetch({});
    },
    { deep: true }
);

watch(
    () => props.courses,
    (val) => {
        setPagination($resource, val.meta);
    },
    { deep: true }
);

const isAllChecked = computed(
    () =>
        courses.value?.data.length > 0 &&
        courses.value?.data.every((item) => selectedIds.value.has(item.id))
);

const numberOfPages = computed(() => {
    let pgs = $resource.state.filters['totalPages'] || 0;
    return pgs;
});

const getCourseId = (uid = '', type = '') => {
    return `${type}${uid}`;
};
const showCourseStructure = (item) => {
    courseStructureData.value = item;
    structureWindowVisible.value = true;
};
const closeStructureWindow = () => {
    courseStructureData.value = [];
    structureWindowVisible.value = false;
};
const onFilterChange = (ev) => {
    this.filter = ev.filter;
};
const handlePageChange = (e) => {
    $resource.state.filters['page'] = e;

    nextTick(() => {
        if (listContainer.value) {
            listContainer.value.scrollTop = 0;
        }
    });
    return false;
};
const handlePagesizeChange = (e) => {
    $resource.state.filters['page'] = 1;
    $resource.state.filters['take'] = e;
    nextTick(() => {
        if (listContainer.value) {
            listContainer.value.scrollTop = 0;
        }
    });
    return false;
};
const pageFilters = computed(() => {
    //"type": 2, "faculty": null, "status": "incomplete", "duration": "40"
    let filterFound = false;
    let typeFilter = filterScopes?.types.find(
        (type) => type.value === $resource.state.filters['type']
    );
    let facultyFilter = filterScopes?.faculties.find(
        (faculty) => faculty.id === $resource.state.filters['faculty']
    );
    let statusFilter = filterScopes?.status.find((s) => s.id === $resource.state.filters['status']);
    let durationFilter = filterScopes?.duration.find(
        (d) => d.id === $resource.state.filters['duration']
    );
    if (typeFilter || facultyFilter || statusFilter || durationFilter) {
        filterFound = true;
    }
    return {
        type: {
            name: typeFilter?.label,
        },
        faculty: {
            name: facultyFilter?.text,
        },
        status: {
            name: statusFilter?.text,
        },
        duration: {
            name: durationFilter?.text,
        },
        active: filterFound,
    };
});
const handleDelete = (item) => {
    $resource.onRemove(item.id, 'Are you sure you want to remove course?');
};
const handleStatusChange = (item) => {
    let messageText = 'activate';
    if (item.activated_now == 1) {
        messageText = 'deactivate';
    }
    const course = { id: item.id, status: item.activated_now == 1 ? 0 : 1 };
    $resource.onStatusChange(
        course,
        `Are you sure you want to ${messageText} ${item.course_code} - ${item.course_name}?`
    );
};
const handleSyncToMoodle = (item, type = 'sync') => {
    $resource.onSync(
        item.id,
        type,
        `Are you sure you want to ${type} this course (${item.course_code}) to Moodle ?`
    );
};
const handleBulkSyncToMoodle = () => {
    if (selectedIds.value.size === 0) {
        globalHelper.methods.showPopupError('Please select at least one course to sync.', 'Error');
        return;
    }
    const selectedCourseIds = [...selectedIds.value];
    $resource.onSync(
        selectedCourseIds,
        'bulk-sync',
        `Are you sure you want to Sync this selected course to Moodle ?`
    );
};
const handleAllCourseSyncFromMoodle = () => {
    $resource.fromSync(`Are you sure you want to Sync all courses from Moodle ?`);
};
const clearType = () => {
    $resource.state.filters['type'] = null;
};
const clearFaculty = () => {
    $resource.state.filters['faculty'] = null;
};
const clearStatus = () => {
    $resource.state.filters['status'] = null;
};
const clearDuration = () => {
    $resource.state.filters['duration'] = null;
};
const clearAllFilters = () => {
    $resource.state.filters['keyword'] = null;
    $resource.state.filters['type'] = null;
    $resource.state.filters['faculty'] = null;
    $resource.state.filters['status'] = null;
    $resource.state.filters['duration'] = null;
};
const followToProfile = (courseCode, has_all_information) => {
    if (!has_all_information) {
        return router.visit(route('spa.courses.detailadd', [courseCode]));
    }
    const profileUrl = route('spa.courses.profile', [courseCode]);
    router.get(
        profileUrl,
        {},
        {
            replace: true,
            onBefore: () => {
                loaderStore.startLoading();
            },
            onError: () => {
                loaderStore.stopLoading();
            },
            onFinish: () => {
                loaderStore.stopLoading();
            },
        }
    );
};
const closeQuickAddForm = () => {
    QuickAdd.CourseData = null;
    QuickAdd.QuickAddFormVisible = false;
    QuickAdd.customCourse = false;
    localCourseUnits.value = null;
    return;
    //QuickAdd.CourseData = null;
    //QuickAdd.QuickAddFormVisible = false;
};
const addCourseManually = () => {
    QuickAdd.CourseData = [];
    QuickAdd.QuickAddFormVisible = true;
    QuickAdd.customCourse = true;
    localCourseUnits.value = null;
};
const handedSaved = () => {
    return;
};
// const toggleSelectAll = () => {
//     selectedIds.value = isAllSelected.value
//         ? new Set()
//         : new Set(courses.value.data.map((item) => item.id));
// };

const toggleSelectAll = () => {
    const items = courses.value.data;
    console.log('items', isAllChecked.value);
    if (isAllChecked.value) {
        items.forEach((item) => selectedIds.value.delete(item.id));
    } else {
        items.forEach((item) => selectedIds.value.add(item.id));
    }
};

const toggleRowSelection = (id) => {
    // console.log(selectedIds.value.size);
    selectedIds.value[selectedIds.value.has(id) ? 'delete' : 'add'](id);
};

const animationDelayStyle = computed(() => {
    return courses.value.data.map((item, index) => {
        const delay = index * 5;
        return {
            'animation-delay': `${delay}ms`,
        };
    });
});

const handleAllRemoveSelectedIds = () => {
    selectedIds.value = new Set();
};
</script>
