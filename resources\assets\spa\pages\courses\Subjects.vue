<template>
    <Layout :loading="true" :noSpacing="true">
        <Head title="Courses List" />
        <template v-slot:pageTitleContent>
            <PageTitleContent :title="'List of Available Subjects'" :back="false" />
        </template>
        <div class="h-screen-header mx-auto flex flex-col space-y-4 px-8 py-4">
            <div class="space-y-4">
                <h2 class="justify-start text-2xl font-semibold text-gray-800">Subjects List</h2>
                <div class="flex items-center justify-start gap-4">
                    <div class="coursesSearchInputField relative w-full md:w-fit">
                        <span class="absolute left-3 top-1/2 -translate-y-1/2">
                            <icon :name="'lens'" width="16" height="16" />
                        </span>
                        <input
                            type="text"
                            id="searchKeyword"
                            ref="searchKeyword"
                            v-model.lazy="resource.state.filters['keyword']"
                            v-debounce="300"
                            class="tw-input-text h-[2.32rem] w-[300px] pl-8"
                            placeholder="Search"
                            autocomplete="off"
                            @change="resetPagination"
                            @focus="searchingByKeyword = true"
                            @blur="searchingByKeyword = false"
                        />
                    </div>
                    <div class="w-full basis-auto md:min-w-[110px] md:max-w-[150px]">
                        <DropDownList
                            :data-items="filterScopes?.types"
                            :text-field="'label'"
                            :data-item-key="'value'"
                            :valueField="'value'"
                            v-model.lazy="resource.state.filters['type']"
                            :valuePrimitive="true"
                            :default-item="{
                                label: 'All Types',
                                value: null,
                            }"
                            :popup-settings="{
                                className: 'tw-width-auto',
                                animate: false,
                            }"
                            @change="resetPagination"
                            :class="'!h-[2.32rem]'"
                        >
                        </DropDownList>
                    </div>
                    <div class="w-full basis-auto md:min-w-[110px] md:max-w-[150px]">
                        <DropDownList
                            :data-items="filterScopes?.sort"
                            :text-field="'text'"
                            :data-item-key="'id'"
                            :valueField="'id'"
                            v-model.lazy="resource.state.filters['sort']"
                            :valuePrimitive="true"
                            :default-item="{
                                text: 'Default Sorting (Latest First)',
                                id: null,
                            }"
                            :popup-settings="{
                                className: 'tw-width-auto',
                                animate: false,
                            }"
                            @change="resetPagination"
                            :class="'!h-[2.32rem]'"
                        >
                        </DropDownList>
                    </div>
                </div>
                <div class="flex items-center" v-if="pageFilters.active">
                    <div
                        class="border-1 mr-2 rounded-xl border-gray-50 bg-white px-2 py-1"
                        v-if="pageFilters.type.name"
                    >
                        <div class="flex items-center justify-between space-x-4">
                            <div class="text-sm text-gray-700">
                                {{ pageFilters.type.name }}
                            </div>
                            <span class="cursor-pointer" @click="clearStatus">
                                <icon :name="'cross'" :width="12" :height="12" />
                            </span>
                        </div>
                    </div>
                    <div
                        class="border-1 mr-2 rounded-xl border-gray-50 bg-white px-2 py-1"
                        v-if="pageFilters.sort.name"
                    >
                        <div class="flex items-center justify-between space-x-4">
                            <div class="text-sm text-gray-700">
                                {{ pageFilters.sort.name }}
                            </div>
                            <span class="cursor-pointer" @click="clearStatus">
                                <icon :name="'cross'" :width="12" :height="12" />
                            </span>
                        </div>
                    </div>
                    <div class="py-1">
                        <div
                            v-if="pageFilters.active"
                            class="flex cursor-pointer justify-between space-x-4"
                            @click="clearAllFilters"
                        >
                            <div
                                class="cursor-pointer text-sm text-primary-blue-500 hover:text-primary-blue-600"
                            >
                                Clear All Filters
                            </div>
                        </div>
                    </div>
                </div>
            </div>

            <div ref="listContainer" class="flex-1 overflow-y-auto">
                <GlobalContextLoader context="course-list" :overlay="true">
                    <template v-if="subjects.data.length">
                        <div class="space-y-4">
                            <div
                                v-for="(subject, index) in subjects.data"
                                :key="index"
                                class="tw-slideup tw-animate group rounded-md border border-gray-200 bg-gray-50 py-2 pe-6 ps-4"
                                :style="animationDelayStyle[index]"
                            >
                                <div
                                    class="block justify-between gap-4 md:flex lg:gap-0"
                                    :id="`subject${index + 1}`"
                                >
                                    <div class="space-y-4 lg:space-y-0">
                                        <div
                                            class="mb-2 grid w-full grid-cols-1 gap-2 space-x-0 md:flex lg:gap-0 lg:space-x-2"
                                        >
                                            <div
                                                class="my-auto flex items-center justify-between gap-4 font-medium"
                                            >
                                                <div>
                                                    {{ subject.subject_code }} -
                                                    {{ subject.subject_name }}
                                                </div>
                                                <Tooltip
                                                    :anchor-element="'target'"
                                                    :position="'top'"
                                                    :parentTitle="true"
                                                    :className="'flex items-center'"
                                                    :tooltipClassName="'!p-1.5'"
                                                >
                                                    <div
                                                        class="relative right-auto top-auto flex translate-y-0 items-center space-x-2 md:absolute md:right-4 md:top-1/2 md:-translate-y-1/2"
                                                    >
                                                        <div
                                                            v-if="subjectEditable"
                                                            class="tw-btn-action h-7 w-7 border border-gray-200"
                                                            @click="editSubject(subject)"
                                                        >
                                                            <icon
                                                                :name="'pencil'"
                                                                :width="16"
                                                                :height="16"
                                                            />
                                                        </div>
                                                        <div
                                                            class="tw-btn-action h-7 w-7 border border-gray-200"
                                                            @click="getSubjectDetails(subject)"
                                                        >
                                                            <icon
                                                                :name="'preview'"
                                                                :width="16"
                                                                :height="16"
                                                            />
                                                        </div>
                                                        <!-- <div
                                                            class="tw-btn-action h-7 w-7 border border-gray-200"
                                                        >
                                                            <Link
                                                                :href="
                                                                    route(
                                                                        'spa.courses.subject.delete',
                                                                        [subject.id]
                                                                    )
                                                                "
                                                                title="View course profile"
                                                            >
                                                                <icon
                                                                    :name="'trash'"
                                                                    :width="16"
                                                                    :height="16"
                                                                />
                                                            </Link>
                                                        </div> -->
                                                    </div>
                                                </Tooltip>
                                            </div>
                                        </div>
                                        <div
                                            class="grid w-full grid-cols-2 gap-2 space-x-0 text-xs md:grid-cols-3 lg:space-x-2 xl:flex"
                                        >
                                            <div class="flex">
                                                <div class="mr-2 text-gray-400">Contact Hours:</div>
                                                <div class="text-gray-700">
                                                    {{ subject.contact_hours }}
                                                </div>
                                            </div>
                                            <div class="flex">
                                                <div class="mr-2 text-gray-400">Created On:</div>
                                                <div class="text-gray-700">
                                                    <dateformat :date="subject.created_at" />
                                                </div>
                                            </div>
                                            <div class="flex">
                                                <div class="mr-2 text-gray-400">
                                                    Last Updated On:
                                                </div>
                                                <div class="text-gray-700">
                                                    <dateformat :date="subject.updated_at" />
                                                </div>
                                            </div>
                                            <div class="flex">
                                                <div class="mr-2 text-gray-400">Units:</div>
                                                <div class="flex space-x-2 text-gray-700">
                                                    <div>{{ subject.units.length }}</div>
                                                    <div
                                                        v-if="subject.units.length > 0"
                                                        class="flex"
                                                    >
                                                        (
                                                        <div class="flex space-x-1">
                                                            <a
                                                                v-for="(
                                                                    unit, index
                                                                ) in subject.units"
                                                                class="cursor-pointer text-primary-blue-600"
                                                                @click="showUnitDetails(unit)"
                                                                >{{ unit.unit_code }}</a
                                                            >
                                                        </div>
                                                        )
                                                    </div>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </template>
                    <div
                        v-else
                        class="flex h-80 w-full items-center rounded-md border border-gray-200 bg-gray-50 p-6"
                    >
                        <div class="mx-auto items-center space-y-4">
                            <div class="flex justify-center">
                                <div class="left-icon">
                                    <svg
                                        width="48"
                                        height="42"
                                        viewBox="0 0 48 42"
                                        fill="none"
                                        xmlns="http://www.w3.org/2000/svg"
                                    >
                                        <path
                                            d="M14.4 10.2769C14.4 9.28279 15.2059 8.4769 16.2 8.4769H30.6C31.5941 8.4769 32.4 9.28279 32.4 10.2769V13.8769C32.4 14.871 31.5941 15.6769 30.6 15.6769H16.2C15.2059 15.6769 14.4 14.871 14.4 13.8769V10.2769ZM17.1 12.9769H29.7V11.1769H17.1V12.9769ZM9 7.5769C9 5.09162 11.0147 3.0769 13.5 3.0769H34.2C36.6853 3.0769 38.7 5.09162 38.7 7.5769V33.2269C38.7 33.9725 38.0956 34.5769 37.35 34.5769H11.7C11.7 35.571 12.5059 36.3769 13.5 36.3769H37.35C38.0956 36.3769 38.7 36.9813 38.7 37.7269C38.7 38.4725 38.0956 39.0769 37.35 39.0769H13.5C11.0147 39.0769 9 37.0622 9 34.5769V7.5769ZM11.7 31.8769H36V7.5769C36 6.58279 35.1941 5.7769 34.2 5.7769H13.5C12.5059 5.7769 11.7 6.58279 11.7 7.5769V31.8769Z"
                                            fill="#1890FF"
                                        />
                                    </svg>
                                </div>
                                <div class="course"></div>
                            </div>
                            <div class="flex justify-center text-gray-400">
                                {{ getNoDataFoundText }}
                            </div>
                        </div>
                    </div>
                </GlobalContextLoader>
            </div>
            <div class="course-pagination my-4" v-if="subjects.data.length">
                <pager
                    :skip="resource.state.pageable.skip"
                    :take="resource.state.pageable.pageSizeValue"
                    :total="resource.state.pageable.totalItems"
                    @changedPage="handlePageChange"
                    @changedPageSize="handlePagesizeChange"
                    :button-count="resource.state.pageable.buttonCount"
                    :info="resource.state.pageable.info"
                    :previous-next="true"
                    :type="'numeric'"
                    :page-sizes="resource.state.pageable.pageSizes"
                    :pager-render="'myTemplate'"
                    :responsive="false"
                >
                    <template v-slot:myTemplate="{ props }">
                        <PagerTemplate
                            :current-page="props.current"
                            :page-size="props.perpage"
                            :skip="props.skip"
                            :take="props.take"
                            :size-defs="props.pageSizes"
                            :total-pages="resource.state.pageable.totalPages"
                            :total-items="props.total"
                        />
                    </template>
                </pager>
            </div>
        </div>
        <div v-if="unitsDetailsVisible">
            <UnitDetails
                :unit="currentUnit"
                :visible="unitsDetailsVisible"
                @closed="closeUnitDetailsWindow"
            />
        </div>
        <div v-if="editSubjectWindowVisible">
            <UnitDetails
                :unit="currentUnit"
                :visible="editSubjectWindowVisible"
                @closed="closeUnitDetailsWindow"
            />
        </div>
        <div v-if="subjectDetailsWindowVisible">
            <SubjectDetails
                :subject="currentSubject"
                :visible="subjectDetailsWindowVisible"
                @closed="closeSubjectDetailsWindow"
                @unitdetails="showUnitDetails"
                @subjectdetails="showSubjectDetailsTable"
            />
        </div>
        <div v-if="subjectTableWindowVisible">
            <SubjectDetailsTable
                :subject="subjectData"
                :visible="subjectTableWindowVisible"
                @closed="closeSubjectTableWindow"
            />
        </div>
    </Layout>
</template>
<script>
import { onMounted, reactive, ref, watch, computed, toRef, nextTick } from 'vue';
import axios from 'axios';
import { Pager } from '@progress/kendo-vue-data-tools';
import Layout from '../Layouts/Layout';
import { Head, Link, router } from '@inertiajs/vue3';
import Menu from './commons/Menu';
import PrimaryButton from '@spa/components/Buttons/PrimaryButton.vue';
import SecondaryButton from '@spa/components/Buttons/SecondaryButton.vue';
import CourseStatusBtn from './commons/CourseStatusButtons.vue';
import CourseActionsMenu from './commons/CourseActionsMenu.vue';
import PageTitleContent from '../Layouts/PageTitleContent';
import { ToolBar, ToolBarItem } from '@progress/kendo-buttons-vue-wrapper';
import { useCourseResource, setPagination } from '@spa/services/courseFormResource';
import courseStructure from './includes/courseStructure.vue';
import { DropDownList } from '@progress/kendo-vue-dropdowns';
import PagerTemplate from '@spa/components/ListViewPagination.vue';
import QuickAddFormNew from './QuickAddFormNew.vue';
import SkeletonList from '@spa/components/Skeleton/SkeletonList.vue';
import { useCoursesStore } from '@spa/stores/modules/courses';
import { useLoaderStore } from '@spa/stores/modules/global-loader';
import useConfirm from '@spa/services/useConfirm.js';
import { Checkbox } from '@progress/kendo-vue-inputs';
import globalHelper from '@spa/plugins/global-helper.js';
import Button from '@spa/components/Buttons/Button.vue';
import { Tooltip } from '@progress/kendo-vue-tooltip';
import FormatDateTime from '@spa/components/FormatDateTime.vue';
import UnitDetails from '@spa/pages/courses/includes/subjectunits/UnitDetails.vue';
import SubjectDetails from '@spa/pages/courses/includes/subjectunits/SubjectDetails.vue';
import SubjectDetailsTable from '@spa/pages/courses/includes/subjectunits/SubjectDetailsTable.vue';

export default {
    setup(props) {
        const loaderStore = useLoaderStore();
        const store = useCoursesStore();
        const resource = useCourseResource({
            filters: props.query || {},
            only: ['subjects', 'filterScopes'],
        });
        watch(
            () => resource.state.filters,
            (val) => {
                resource.fetch();
            },
            { deep: true }
        );
        return {
            resource,
            loaderStore,
            store,
        };
    },
    props: {
        user: Object,
        query: Object,
        subjects: Object,
        filterScopes: Object,
    },
    components: {
        Layout,
        dateformat: FormatDateTime,
        UnitDetails,
        PageTitleContent,
        pager: Pager,
        PagerTemplate,
        DropDownList,
        Tooltip,
        Link,
        SubjectDetails,
        SubjectDetailsTable,
    },
    data() {
        return {
            cancelSource: axios.CancelToken.source(),
            subjectEditable: false,
            unitsDetailsVisible: false,
            editSubjectWindowVisible: false,
            subjectDetailsWindowVisible: false,
            subjectTableWindowVisible: false,
            subjectData: {},
            currentUnit: {},
            currentSubject: {},
        };
    },
    mounted() {
        this.setPagination(this.resource, this.subjects.meta);
    },
    computed: {
        pageFilters() {
            let filterFound = false;
            let sortFilter = this.filterScopes?.sort.find(
                (s) => s.id === this.resource.state.filters['sort']
            );
            let typeFilter = this.filterScopes?.types.find(
                (s) => s.value === this.resource.state.filters['type']
            );
            if (typeFilter || sortFilter) {
                filterFound = true;
            }
            return {
                type: {
                    name: typeFilter?.label || null,
                },
                sort: {
                    name: sortFilter?.text ? `Sorted:: ${sortFilter?.text}` : null,
                },
                active: filterFound,
            };
        },
        animationDelayStyle() {
            return this.subjects.data.map((item, index) => {
                const delay = index * 5;
                return {
                    'animation-delay': `${delay}ms`,
                };
            });
        },
        getNoDataFoundText() {
            const hasFilters = this.pageFilters.active && resource.state.filters.keyword != '';
            const message = 'No subjects found.';
            return hasFilters
                ? `${message} No subjects are in use in current system.`
                : `${message} Try changing your filter parameters.`;
        },
    },
    methods: {
        setPagination,
        resetPagination() {
            this.resource.state.filters.page = 1;
        },
        handlePageChange(e) {
            this.resource.state.filters['page'] = e;

            this.$nextTick(() => {
                if (this.$refs.listContainer) {
                    this.$refs.listContainer.scrollTop = 0;
                }
            });
            return false;
        },
        handlePagesizeChange(e) {
            this.resource.state.filters['page'] = 1;
            this.resource.state.filters['take'] = e;
            this.$nextTick(() => {
                if (this.$refs.listContainer) {
                    this.$refs.listContainer.scrollTop = 0;
                }
            });
            // nextTick(() => {
            //     if (listContainer.value) {
            //         listContainer.value.scrollTop = 0;
            //     }
            // });
            return false;
        },
        showUnitDetails(unit) {
            this.unitsDetailsVisible = true;
            this.currentUnit = unit;
        },
        closeUnitDetailsWindow() {
            this.unitsDetailsVisible = false;
            this.currentUnit = {};
        },
        clearAllFilters() {
            this.resource.state.filters = { page: 1 };
        },
        editSubject(subject) {
            this.editSubjectWindowVisible = true;
            this.currentSubject = subject;
        },
        cancelExistingRequest(context = null) {
            if (this.cancelSource) {
                this.cancelSource.cancel('Request cancel');
                if (context) {
                    this.loaderStore.stopContextLoading(context);
                }
                this.cancelSource = axios.CancelToken.source();
            }
        },
        getSubjectDetails(subject) {
            this.cancelExistingRequest('subject-details');
            this.subjectDetailsWindowVisible = true;
            this.currentSubject = subject;
            this.loaderStore.startContextLoading('subject-details');
            const url = route('spa.courses.subject.detail', [subject.id]);
            $http
                .get(url, {
                    cancelToken: this.cancelSource.token,
                })
                .then((resp) => {
                    this.store.setSubjectDetails(resp.data);
                })
                .catch((error) => {})
                .finally(() => {
                    this.loaderStore.stopContextLoading('subject-details');
                });
        },
        closeSubjectDetailsWindow() {
            this.cancelExistingRequest('subject-details');
            this.subjectDetailsWindowVisible = false;
            this.currentSubject = {};
        },
        showSubjectDetailsTable(subject) {
            this.subjectTableWindowVisible = true;
            this.subjectData = subject;
        },
        closeSubjectTableWindow() {
            this.subjectTableWindowVisible = false;
            this.subjectData = {};
        },
    },
    watch: {
        subjects: {
            handler(newval, oldval) {
                this.setPagination(this.resource, newval.meta);
            },
            deep: true,
        },
    },
};
</script>
