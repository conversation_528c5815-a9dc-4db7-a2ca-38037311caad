<template>
    <SidebarDrawer
        :visibleDialog="visible"
        :hideOnOverlayClick="false"
        :fixedActionBar="showEditForm"
        :width="'60%'"
        @drawerclose="cancelProcess"
        @drawersaved="handleSubmit"
        :primaryBtnLabel="'Save Criteria'"
        :wrapperClass="'tw-sticky-footer'"
        :isDisabled="false"
        :isSubmitting="savingForm"
    >
        <template #title>
            <div class="text-lg font-medium">{{ modelTitle }}</div>
        </template>
        <template #content>
            <div id="elements-spa" v-if="!showEditForm">
                <div class="mb-4 items-center text-right">
                    <Button :size="'xs'" :variant="'primary'" @click="addNewItem">
                        <icon :name="'plus'" :width="16" :height="16" :fill="'#fff'" />
                        <span class="min-w-fit">Add New Competency Criteria</span>
                    </Button>
                </div>
                <GridWrapper :rounded="true" class="competency-elements-grid">
                    <Grid
                        ref="elementsGrid"
                        :style="{ maxHeight: 'calc(100vh - 7rem)' }"
                        :data-items="competencyElements"
                        :columns="columns"
                        :resizable="true"
                        :loader="'loaderTemplate'"
                        :cell-render="'cellTemplate'"
                    >
                        <GridNoRecords>
                            <EmptyState />
                        </GridNoRecords>
                        <template v-slot:loaderTemplate>
                            <TableLoader v-if="loadingElements" />
                        </template>
                        <template #defaultCell="{ props }">
                            <default-cell v-bind:props="props" />
                        </template>
                        <template #statusTemplate="{ props }">
                            <td>
                                <FormSwitch
                                    :id="'itemStatus'"
                                    :name="'itemStatus'"
                                    :label="''"
                                    :value-label="false"
                                    :value="props.dataItem.active === 'Yes'"
                                    @change="updateSwitch(props.dataItem)"
                                >
                                </FormSwitch>
                            </td>
                        </template>
                        <template #actionCell="{ props }">
                            <td class="flex space-x-2">
                                <button
                                    class="cursor-pointer text-gray-400"
                                    @click="editElement(props.dataItem)"
                                    title="Edit this criteria"
                                >
                                    <icon :name="'pencil'" :fill="'currentColor'" />
                                </button>
                                <button
                                    class="cursor-pointer"
                                    @click="deleteElement(props.dataItem)"
                                    title="Delete this criteria"
                                >
                                    <icon :name="'delete-shade'" />
                                </button>
                            </td>
                        </template>
                    </Grid>
                </GridWrapper>
            </div>
            <div v-else>
                <fieldset class="k-form-fieldset space-y-6">
                    <div class="tw-form__row grid grid-cols-1">
                        <FormDropDown
                            :id="'unit_id'"
                            :name="'unit_id'"
                            :label="'Select Unit'"
                            :data-items="getUnits"
                            :default-item="defaultUnitField"
                            :layout="'horizontal'"
                            :component="'selectCourseTemplate'"
                            :text-field="'label'"
                            :valueField="'value'"
                            :valuePrimitive="true"
                            :valid="true"
                            :value="editItem.unit_id || null"
                            @change="selectUnit"
                        />
                        <span v-if="v$.unit_id.$error" class="text-red-500">Select an unit.</span>
                    </div>
                    <div class="tw-form__row grid grid-cols-1">
                        <label
                            class="tw-form__label mb-1 font-medium leading-5 text-gray-700"
                            for="competency_criteria"
                            >Competency Criteria</label
                        >
                        <k-input
                            :id="'competency_criteria'"
                            :name="'competency_criteria'"
                            :minlength="1"
                            v-model="editItem.competency_criteria"
                        ></k-input>
                        <span v-if="v$.competency_criteria.$error" class="text-red-500"
                            >Provide the competency criteria name.</span
                        >
                    </div>
                    <div class="tw-form__row grid grid-cols-1">
                        <label
                            class="tw-form__label mb-1 font-medium leading-5 text-gray-700"
                            for="notes"
                            >Notes</label
                        >
                        <k-textarea
                            :id="'notes'"
                            :name="'notes'"
                            :label="'Notes'"
                            :max="200"
                            :rows="4"
                            v-model="editItem.notes"
                        />
                        <span v-if="v$.notes.$error" class="text-red-500"
                            >Provide the competency criteria notes.</span
                        >
                    </div>
                    <div class="tw-form__row grid grid-cols-1">
                        <label
                            class="tw-form__label mb-1 font-medium leading-5 text-gray-700"
                            for="active"
                            >Active?</label
                        >
                        <radiogroup
                            :id="'active'"
                            :name="'active'"
                            :data-items="yesNoData"
                            v-model="editItem.active"
                            :layout="'horizontal'"
                        />
                    </div>
                </fieldset>
            </div>
        </template>
    </SidebarDrawer>
</template>
<script>
import { ref, computed } from 'vue';
import { mapState } from 'pinia';
import axios from 'axios';
import { useCoursesStore } from '@spa/stores/modules/courses';
import SidebarDrawer from '@spa/components/KendoModals/SidebarDrawer.vue';
import useVuelidate from '@vuelidate/core';
import { required, minLength, maxLength } from '@vuelidate/validators';

import { Grid } from '@progress/kendo-vue-grid';
import { GridNoRecords } from '@progress/kendo-vue-grid';
import GridWrapper from '@spa/components/KendoGrid/GridWrapper.vue';
import TableLoader from '@spa/components/KendoGrid/TableLoader.vue';
import HeaderTemplate from '@spa/components/KendoGrid/templates/HeaderTemplate.vue';
import EmptyState from '@spa/components/KendoGrid/EmptyState.vue';
import DefaultCellTemplate from '@spa/components/KendoGrid/templates/DefaultCellTemplate.vue';
import FormSwitch from '@spa/components/KendoInputs/FormSwitch.vue';
import Button from '@spa/components/Buttons/Button';
import CompetencyElementForm from '@spa/pages/courses/includes/CompetencyElementForm.vue';
import useConfirm from '@spa/services/useConfirm';

import { Input, TextArea, RadioGroup } from '@progress/kendo-vue-inputs';
import FormDropDown from '@spa/components/KendoInputs/FormDropDown.vue';

export default {
    setup() {
        const confirm = useConfirm();

        const editItem = ref({
            id: null,
            subject_id: null,
            unit_id: null,
            competency_criteria: '',
            notes: '',
            active: 'Yes',
        });

        const rules = {
            unit_id: { required },
            competency_criteria: { required, minLength: minLength(2) },
            notes: { required, minLength: minLength(2) },
        };
        function updateEditItem(newData) {
            editItem.value = { ...editItem.value, ...newData };
        }
        const v$ = useVuelidate(rules, editItem);

        const deleteItem = () => {
            return new Promise((resolve) => {
                confirm.require({
                    message: `Are you sure you want to delete the selected competency element?`,
                    header: 'Confirmation',
                    icon: 'pi pi-exclamation-triangle',
                    accept: async () => {
                        resolve(true);
                    },
                    reject: () => {
                        resolve(false);
                    },
                    onHide: () => {
                        resolve(false);
                    },
                });
            });
        };
        return { v$, editItem, updateEditItem, deleteItem };
    },
    props: {
        subject: { type: Object, default: [] },
        template: { type: [Array, Object], default: [] },
        visible: { type: Boolean, default: false },
    },
    components: {
        SidebarDrawer,
        Grid,
        GridNoRecords,
        GridWrapper,
        TableLoader,
        EmptyState,
        'header-cell': HeaderTemplate,
        'default-cell': DefaultCellTemplate,
        FormSwitch,
        Button,
        CompetencyElementForm,
        'k-input': Input,
        'k-textarea': TextArea,
        radiogroup: RadioGroup,
        FormDropDown,
    },
    data: function () {
        return {
            savingForm: false,
            loadingElements: false,
            cancelSource: axios.CancelToken.source(),
            competencyElements: [],
            showEditForm: false,
            yesNoData: [
                {
                    label: 'Yes',
                    value: 'Yes',
                },
                {
                    label: 'No',
                    value: 'No',
                },
            ],
            defaultUnitField: {
                label: 'Select Unit',
                value: '',
            },
            columns: [
                {
                    field: 'competency_criteria',
                    title: 'Criteria',
                    cell: 'defaultCell',
                },
                {
                    field: 'unit_name',
                    title: 'Unit',
                    width: 200,
                    cell: 'defaultCell',
                },
                {
                    field: 'subject_name',
                    title: 'Subject',
                    width: 200,
                    cell: 'defaultCell',
                },
                {
                    field: 'notes',
                    title: 'Notes',
                    width: 250,
                    cell: 'defaultCell',
                },
                {
                    field: 'active',
                    title: 'Status',
                    width: 100,
                    cell: 'statusTemplate',
                },
                {
                    field: 'actions',
                    title: 'Action',
                    width: 100,
                    cell: 'actionCell',
                },
            ],
        };
    },
    mounted() {
        this.loadCompetencyElements();
    },
    computed: {
        ...mapState(useCoursesStore, [
            'course',
            'setCourseProgress',
            'setUnits',
            'setCourseSubjects',
        ]),
        modelTitle() {
            let subTitle = '';
            if (this.showEditForm) {
                subTitle =
                    this.editItem?.id > 0
                        ? `Edit Criteria : ${this.editItem?.competency_criteria}`
                        : 'Add New Competency Criteria';
            } else {
                subTitle =
                    this.subject?.id > 0
                        ? `Competency Criterias for subject: ${this.subject?.subject_name}`
                        : '';
            }
            return this.savingForm ? 'Saving competency criteria information...' : subTitle;
        },
        getUnits() {
            return this.subject?.units?.map((item) => ({
                value: item.synced_for || null,
                label: `${item.unit_code}: ${item.unit_name}`,
            }));
        },
    },
    methods: {
        cancelProcess() {
            if (this.showEditForm) {
                this.updateEditItem([]);
                this.showEditForm = false;
                return;
            }
            this.cancelSource.cancel('Request cancel');
            this.$emit('closed');
        },
        selectUnit(e) {
            this.editItem.unit_id = e.value || null;
            return;
        },
        handleSubmit(formData) {
            this.v$.$touch();
            if (!this.v$.$invalid) {
                this.saveData();
            }
        },
        loadCompetencyElements() {
            const postData = {
                course_id: this.course.id || null,
                subject_id: this.subject.subject_id || null,
                template_id: this.template.id || null,
            };
            this.loadingElements = true;
            $http
                .post(route('spa.courses.loadcompetencyelements'), postData, {
                    cancelToken: this.cancelSource.token,
                })
                .then((resp) => {
                    if (resp['success']) {
                        this.competencyElements = resp.data || [];
                    }
                    this.loadingElements = false;
                })
                .catch((error) => {
                    this.loadingElements = false;
                });
        },
        updateSwitch(dataItem) {
            dataItem.active = dataItem.active == 'Yes' ? 'No' : 'Yes';
            this.updateEditItem(dataItem);
            this.saveData(false);
        },
        editElement(dataItem) {
            this.updateEditItem(dataItem);
            this.showEditForm = true;
        },
        async deleteElement(dataItem) {
            if (await this.deleteItem()) {
                const postData = {
                    id: dataItem.id || null,
                    subject_id: dataItem.subject_id || null,
                    unit_id: dataItem.unit_id || null,
                };
                $http
                    .post(route('spa.courses.removecompetencyelements'), postData, {
                        cancelToken: this.cancelSource.token,
                    })
                    .then((resp) => {
                        if (resp['success']) {
                            this.competencyElements = resp.data || [];
                        }
                        this.showEditForm = false;
                        this.savingForm = false;
                        this.loadingElements = false;
                    })
                    .catch((error) => {
                        this.savingForm = false;
                        this.loadingElements = false;
                    });
                return;
            }
        },
        addNewItem() {
            this.updateEditItem({
                id: null,
                unit_id: null,
                subject_id: this.subject.subject_id || null,
                competency_criteria: null,
                active: 'Yes',
                notes: null,
            });
            this.showEditForm = true;
        },
        saveData(showLoading = true) {
            if (showLoading) {
                this.savingForm = true;
                this.loadingElements = true;
            }
            $http
                .post(route('spa.courses.savecompetencyelements'), this.editItem, {
                    cancelToken: this.cancelSource.token,
                })
                .then((resp) => {
                    if (resp['success']) {
                        this.competencyElements = resp.data || [];
                    }
                    this.showEditForm = false;
                    this.savingForm = false;
                    this.loadingElements = false;
                })
                .catch((error) => {
                    this.savingForm = false;
                    this.loadingElements = false;
                });
        },
    },
};
</script>
