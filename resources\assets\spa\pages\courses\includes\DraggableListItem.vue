<template>
    <Draggable @press="onPress" @drag="onDrag" @release="onRelease" ref="draggable">
        <div :data-itemid="unit.Code" class="tw-draggable-list-item">
            <div
                class="flex cursor-move items-center justify-between space-x-1 border-b py-2 text-sm"
            >
                <div class="flex w-11/12">
                    <div class="flex w-2/12 space-x-2">
                        <div class="mr-2">
                            <icon :name="'draghandle'" />
                        </div>
                        <div
                            class="mr-2 rounded-md border-0 bg-primary-blue-100 px-4 py-1 text-xs text-primary-blue-800"
                            v-if="type == 'core'"
                        >
                            Core Unit
                        </div>
                        <div
                            class="mr-2 self-baseline rounded-md border-0 bg-gray-100 px-4 py-1 text-xs text-gray-800"
                            v-else
                        >
                            Elective
                        </div>
                    </div>
                    <div class="flex w-6/12 space-x-2">
                        <div class="text-gray-500">{{ unit.Code }}</div>
                        <div class="text-gray-700">{{ unit.Title }}</div>
                    </div>
                    <div class="uppercase text-gray-700" v-if="unit.subject_name">
                        {{ unit.subject_name }}
                    </div>
                    <div
                        class="mr-2 rounded-md border-0 bg-yellow-100 px-2.5 py-0.5 text-yellow-800"
                        v-else
                    >
                        Subject Unassigned
                    </div>
                </div>
                <div class="flex w-auto space-x-1">
                    <div class="cursor-pointer text-gray-400" @click="editUnit(unit)">
                        <icon :name="'pencil'" :fill="'currentColor'" />
                    </div>
                    <div class="cursor-pointer" @click="deleteUnit(unit)">
                        <icon :name="'cross'" />
                    </div>
                </div>
            </div>
        </div>
    </Draggable>
</template>
<script>
import { Draggable } from '@progress/kendo-vue-common';
import UnitListItem from '@spa/pages/courses/includes/templates/UnitListItem.vue';
export default {
    emits: {
        pressHandler: null,
        dragHandler: null,
        releaseHandler: null,
        editunit: null,
        deleteunit: null,
    },
    components: {
        Draggable,
        unitlistitem: UnitListItem,
    },
    props: {
        unit: { type: Object, default: {} },
        type: { type: String, default: '' },
    },
    data() {
        return {
            isDragged: false,
        };
    },
    methods: {
        onPress(event) {
            const element = this.$refs.draggable && this.$refs.draggable.element;
            console.log(this.$refs.draggable.element.classList);
            this.$refs.draggable.element.classList.add('active');
            if (!event.isTouch) {
                event.originalEvent.preventDefault();
            }
            this.isDragged = true;
            if (element) {
                this.$emit('pressHandler', this.$props.dataItem, event, element);
            }
        },
        onDrag(event) {
            document.getElementsByTagName('body')[0].style.cursor = 'move';
            const element = this.$refs.draggable && this.$refs.draggable.element;
            this.$refs.draggable.element.classList.add('active');
            if (!event.isTouch) {
                event.originalEvent.preventDefault();
            }
            event.originalEvent.stopPropagation();
            if (element) {
                this.$emit('dragHandler', this.$props.dataItem, event, element);
            }
        },
        onRelease(event) {
            document.getElementsByTagName('body')[0].style.cursor = 'default';
            const element = this.$refs.draggable && this.$refs.draggable.element;
            this.$refs.draggable.element.classList.remove('active');
            this.isDragged = false;
            if (element) {
                this.$emit('releaseHandler', this.$props.dataItem, event, element);
            }
        },
        editUnit(unit) {
            this.$emit('editunit', unit);
        },
        deleteUnit(unit) {
            this.$emit('deleteunit', unit);
        },
    },
};
</script>
