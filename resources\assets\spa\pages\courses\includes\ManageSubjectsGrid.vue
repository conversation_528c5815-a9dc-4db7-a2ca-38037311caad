<template lang="">
    <div class="grid grid-cols-12 gap-4 rounded-md bg-gray-100 py-1">
        <div class="col-span-2 uppercase"></div>
        <div class="col-span-5 uppercase text-gray-500">Subject Name</div>
        <div class="col-span-2 uppercase text-gray-500">Contact Hours</div>
        <div class="col-span-2 uppercase text-gray-500">Units</div>
        <div class=""></div>
    </div>
    <div v-for="(subject, subindex) in getCourseSubjects" :key="subindex" class="">
        <div class="grid w-full grid-cols-12 items-center gap-4 border-b p-2 text-sm">
            <div class="col-span-2 flex justify-start space-x-2">
                <div
                    class="mr-2"
                    :title="subject.is_active ? 'Active Subject' : 'Inactive Subject'"
                >
                    <icon :name="'checkcirc'" :fill="subject.is_active ? '#1890ff' : '#f3f4f6'" />
                </div>
                <div
                    v-if="gradingTypeExists(subject.grading_type)"
                    class="mr-2 flex items-center justify-center rounded-md border-0 bg-primary-blue-100 px-2.5 py-1 text-xs"
                >
                    {{ getGradingText(subject.grading_type) }}
                </div>
                <div v-else class="mr-2 rounded-md border-0 bg-gray-100 px-2.5 py-1 text-xs">
                    {{ getGradingText(subject.grading_type) }}
                </div>
            </div>
            <div class="col-span-4 flex items-center justify-start space-x-2">
                <div class="text-gray-500">
                    {{ subject.subject_code }}
                </div>
                <div class="text-gray-700">
                    {{ subject.subject_name }}
                </div>
            </div>
            <div class="col-span-2 flex justify-start space-x-2">
                <icon :name="'time'" />
                <div>{{ subject.contact_hours }} Hrs</div>
            </div>
            <div class="col-span-3 flex justify-between align-top">
                <div
                    class="w-full cursor-pointer text-start text-blue-500"
                    @click="showSubjectUnits(subindex)"
                >
                    {{
                        subject.units?.length
                            ? subject.units?.length > 1
                                ? subject.units?.length + ' Units'
                                : subject.units?.length + 'Unit'
                            : 'No Units'
                    }}
                </div>
                <transition name="fade">
                    <div
                        class="subjectUnits"
                        v-if="subject.units?.length > 0 && unitVisibilityIndex === subindex"
                    >
                        <div
                            v-for="(unit, index) in subject.units"
                            :key="index"
                            class="border-b py-2 text-gray-500"
                        >
                            {{ unit.unit_code }} -
                            {{ unit.unit_name }}
                        </div>
                    </div>
                </transition>
            </div>
            <div class="flex justify-end align-top">
                <div class="flex w-auto space-x-1">
                    <div
                        class="cursor-pointer text-gray-400"
                        @click="editSubject(subject)"
                        title="Edit subject"
                    >
                        <icon :name="'pencil'" :fill="'currentColor'" />
                    </div>
                    <div
                        class="cursor-pointer"
                        @click="deleteSubject(subject)"
                        title="Remove subject"
                    >
                        <icon :name="'cross'" />
                    </div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
export default {};
</script>
<style lang=""></style>
