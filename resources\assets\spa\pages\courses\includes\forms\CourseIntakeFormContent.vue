<template>
    <form-element>
        <fieldset class="k-form-fieldset space-y-6">
            <div class="space-y-6">
                <div class="mb-2' grid grid-cols-1 gap-x-6 gap-y-1">
                    <field
                        :id="'intake_name'"
                        :name="'intake_name'"
                        :component="'myTemplate'"
                        :label="'Intake Name'"
                        :placeholder="'Enter Intake Name'"
                        :validator="requiredtrue"
                        :indicaterequired="true"
                    >
                        <template v-slot:myTemplate="{ props }">
                            <forminput
                                v-bind="props"
                                @change="props.onChange"
                                @blur="props.onBlur"
                                @focus="props.onFocus"
                            />
                        </template>
                    </field>
                </div>
                <div>
                    <div class="mb-4 font-medium text-gray-700">Intake is provided in Campus</div>
                    <div class="mb-2' grid grid-cols-3 gap-x-6 gap-y-2">
                        <div
                            class="course-checkbox flex justify-start"
                            v-for="(campus, index) in selectedCampuses"
                            :key="index"
                        >
                            <field
                                :id="'campus_' + campus.id"
                                :name="'campus_' + campus.id"
                                :component="'myTemplate'"
                                :label="campus.text"
                                :validator="requiredCampus"
                                v-model="campus.selected"
                                :label-placement="'after'"
                            >
                                <template v-slot:myTemplate="{ props }">
                                    <checkbox
                                        v-bind="props"
                                        @change="props.onChange"
                                        @blur="props.onBlur"
                                        @focus="props.onFocus"
                                    />
                                </template>
                            </field>
                        </div>
                    </div>
                </div>
                <div class="mb-2' grid grid-cols-2 gap-x-6 gap-y-1">
                    <field
                        :id="'intake_start'"
                        :name="'intake_start'"
                        :component="'myTemplate'"
                        :label="'Intake Start'"
                        :placeholder="'Enter Intake Start'"
                        :validator="requiredDate"
                        :indicaterequired="true"
                    >
                        <template v-slot:myTemplate="{ props }">
                            <formdatepicker
                                v-bind="props"
                                @change="props.onChange"
                                @blur="props.onBlur"
                                @focus="props.onFocus"
                            />
                        </template>
                    </field>
                    <field
                        :id="'intake_end'"
                        :name="'intake_end'"
                        :component="'myTemplate'"
                        :label="'Intake End'"
                        :placeholder="'Enter Intake End'"
                        :validator="checkIntakeEnd"
                        :indicaterequired="true"
                    >
                        <template v-slot:myTemplate="{ props }">
                            <formdatepicker
                                v-bind="props"
                                @change="props.onChange"
                                @blur="props.onBlur"
                                @focus="props.onFocus"
                            />
                        </template>
                    </field>
                </div>
                <div class="mb-2' grid grid-cols-2 gap-x-6 gap-y-1">
                    <field
                        :id="'intake_receiver'"
                        :name="'intake_receiver'"
                        :label="'Intake Receiver'"
                        :component="'courseGeneral'"
                        :text-field="'text'"
                        :data-item-key="'id'"
                        :valueField="'id'"
                        :valuePrimitive="true"
                        :default-item="defaultTarget"
                        :data-items="deliveryTargetData"
                        :validator="requiredtrue"
                        :indicaterequired="true"
                    >
                        <template v-slot:courseGeneral="{ props }">
                            <formdropdown
                                v-bind="props"
                                @change="props.onChange"
                                @blur="props.onBlur"
                                @focus="props.onFocus"
                            />
                        </template>
                    </field>
                </div>
                <div class="grid grid-cols-2 gap-x-6 gap-y-4">
                    <div v-if="showInternationalFee">
                        <field
                            :id="'course_fee'"
                            :name="'course_fee'"
                            :label="'Tuition Fee'"
                            :component="'hoursFees'"
                            :step="5"
                            :spinners="1"
                            :placeholder="'Enter Tuition Fee'"
                            :validator="checkInternationalFeeRequired"
                            :format="'c2'"
                            :min="0"
                            :indicaterequired="true"
                        >
                            <template v-slot:hoursFees="{ props }">
                                <numerictextbox
                                    v-bind="props"
                                    @change="props.onChange"
                                    @blur="props.onBlur"
                                    @focus="props.onFocus"
                                />
                            </template>
                        </field>
                    </div>
                    <div v-if="showDomesticFee">
                        <field
                            :id="'course_domestic_fee'"
                            :name="'course_domestic_fee'"
                            :label="'Domestic Tuition Fee'"
                            :component="'hoursFees'"
                            :step="5"
                            :spinners="1"
                            :placeholder="'Enter Domestic Tuition Fee'"
                            :validator="checkDomesticFeeRequired"
                            :format="'c2'"
                            :min="0"
                            :indicaterequired="true"
                        >
                            <template v-slot:hoursFees="{ props }">
                                <numerictextbox
                                    v-bind="props"
                                    @change="props.onChange"
                                    @blur="props.onBlur"
                                    @focus="props.onFocus"
                                />
                            </template>
                        </field>
                    </div>
                </div>
                <div class="mb-2' grid grid-cols-2 gap-x-6 gap-y-1">
                    <field
                        :id="'intake_seats'"
                        :name="'intake_seats'"
                        :component="'myTemplate'"
                        :label="'Intake Seats'"
                        :placeholder="'Enter Intake Seats'"
                        :validator="requiredtrue"
                        :indicaterequired="true"
                        :step="1"
                        :spinners="1"
                        :min="0"
                        max="500"
                    >
                        <template v-slot:myTemplate="{ props }">
                            <numerictextbox
                                v-bind="props"
                                @change="props.onChange"
                                @blur="props.onBlur"
                                @focus="props.onFocus"
                            />
                        </template>
                    </field>
                    <field
                        :id="'restrict_enrollments_to_seats'"
                        :name="'restrict_enrollments_to_seats'"
                        :component="'switchTemplate'"
                        :label="'Is Seat Fixed'"
                    >
                        <template v-slot:switchTemplate="{ props }">
                            <formswitch
                                v-bind="props"
                                @change="props.onChange"
                                @blur="props.onBlur"
                                @focus="props.onFocus"
                            />
                        </template>
                    </field>
                </div>
                <div class="mb-2' grid grid-cols-2 gap-x-6 gap-y-1">
                    <field
                        :id="'class_start_date'"
                        :name="'class_start_date'"
                        :component="'myTemplate'"
                        :label="'Class Start Date'"
                        :placeholder="'Enter Class Start Date'"
                        :validator="requiredDate"
                        :indicaterequired="true"
                    >
                        <template v-slot:myTemplate="{ props }">
                            <formdatepicker
                                v-bind="props"
                                @change="props.onChange"
                                @blur="props.onBlur"
                                @focus="props.onFocus"
                            />
                        </template>
                    </field>
                    <field
                        :id="'class_end_date'"
                        :name="'class_end_date'"
                        :component="'myTemplate'"
                        :label="'Class End Date'"
                        :placeholder="'Enter Class End Date'"
                        :validator="checkClassEndDate"
                        :indicaterequired="true"
                    >
                        <template v-slot:myTemplate="{ props }">
                            <formdatepicker
                                v-bind="props"
                                @change="props.onChange"
                                @blur="props.onBlur"
                                @focus="props.onFocus"
                            />
                        </template>
                    </field>
                </div>
                <div class="mb-2' grid grid-cols-2 gap-x-6 gap-y-1">
                    <div class="mb-2' grid grid-cols-2 gap-x-6 gap-y-1">
                        <field
                            :id="'class_start_hour'"
                            :name="'class_start_hour'"
                            :component="'myTemplate'"
                            :label="'Class Start Hour'"
                            :placeholder="'Enter Class Start Hour'"
                            :text-field="'name'"
                            :data-item-key="'id'"
                            :valueField="'id'"
                            :valuePrimitive="true"
                            :dataItems="hours"
                            :validator="requiredtrue"
                            :indicaterequired="true"
                        >
                            <template v-slot:myTemplate="{ props }">
                                <formdropdown
                                    v-bind="props"
                                    @change="props.onChange"
                                    @blur="props.onBlur"
                                    @focus="props.onFocus"
                                />
                            </template>
                        </field>
                        <field
                            :id="'class_start_minute'"
                            :name="'class_start_minute'"
                            :component="'myTemplate'"
                            :label="'Class Start Minute'"
                            :placeholder="'Enter Class Start Minute'"
                            :text-field="'name'"
                            :data-item-key="'id'"
                            :valueField="'id'"
                            :valuePrimitive="true"
                            :dataItems="minutes"
                            :indicaterequired="true"
                        >
                            <template v-slot:myTemplate="{ props }">
                                <formdropdown
                                    v-bind="props"
                                    @change="props.onChange"
                                    @blur="props.onBlur"
                                    @focus="props.onFocus"
                                />
                            </template>
                        </field>
                    </div>
                    <div class="mb-2' grid grid-cols-2 gap-x-6 gap-y-1">
                        <field
                            :id="'class_end_hour'"
                            :name="'class_end_hour'"
                            :component="'myTemplate'"
                            :label="'Class End Hour'"
                            :placeholder="'Enter Class End Hour'"
                            :text-field="'name'"
                            :data-item-key="'id'"
                            :valueField="'id'"
                            :valuePrimitive="true"
                            :dataItems="hours"
                            :validator="requiredtrue"
                            :indicaterequired="true"
                        >
                            <template v-slot:myTemplate="{ props }">
                                <formdropdown
                                    v-bind="props"
                                    @change="props.onChange"
                                    @blur="props.onBlur"
                                    @focus="props.onFocus"
                                />
                            </template>
                        </field>
                        <field
                            :id="'class_end_minute'"
                            :name="'class_end_minute'"
                            :component="'myTemplate'"
                            :label="'Class End Minute'"
                            :placeholder="'Enter Class End Minute'"
                            :text-field="'name'"
                            :data-item-key="'id'"
                            :valueField="'id'"
                            :valuePrimitive="true"
                            :dataItems="minutes"
                            :indicaterequired="true"
                        >
                            <template v-slot:myTemplate="{ props }">
                                <formdropdown
                                    v-bind="props"
                                    @change="props.onChange"
                                    @blur="props.onBlur"
                                    @focus="props.onFocus"
                                />
                            </template>
                        </field>
                    </div>
                </div>
                <div class="mb-2' grid grid-cols-2 gap-x-6 gap-y-1" v-if="!isInEditMode">
                    <field
                        :id="'recurring_class'"
                        :name="'recurring_class'"
                        :component="'myTemplate'"
                        :label="'Recurring Class'"
                        :placeholder="'Enter Recurring Class'"
                        :validator="requiredtrue"
                        :dataItems="recurringIntervals"
                        :text-field="'name'"
                        :data-item-key="'id'"
                        :valueField="'id'"
                        :valuePrimitive="true"
                    >
                        <template v-slot:myTemplate="{ props }">
                            <formdropdown
                                v-bind="props"
                                @change="props.onChange"
                                @blur="props.onBlur"
                                @focus="props.onFocus"
                            />
                        </template>
                    </field>
                    <field
                        :id="'recurring_end_date'"
                        :name="'recurring_end_date'"
                        :component="'myTemplate'"
                        :label="'Recurring End Date'"
                        :placeholder="'Enter Recurring End Date'"
                        :validator="checkRecurringEndDate"
                        v-if="isClassRecurring"
                    >
                        <template v-slot:myTemplate="{ props }">
                            <formdatepicker
                                v-bind="props"
                                @change="props.onChange"
                                @blur="props.onBlur"
                                @focus="props.onFocus"
                            />
                        </template>
                    </field>
                </div>
                <div class="mb-2' grid grid-cols-2 gap-x-6 gap-y-1">
                    <field
                        :id="'intake_status'"
                        :name="'intake_status'"
                        :label="'Status'"
                        :data-items="intakeStatusData"
                        :layout="'horizontal'"
                        :component="'myTemplate'"
                    >
                        <template v-slot:myTemplate="{ props }">
                            <formradiogroup
                                v-bind="props"
                                @change="props.onChange"
                                @blur="props.onBlur"
                                @focus="props.onFocus"
                            />
                        </template>
                    </field>
                </div>
            </div>
        </fieldset>
        <dialog-actions-bar>
            <div class="flex w-full justify-end gap-2">
                <div class="mr-2">
                    <SecondaryButton @click="cancelProcess" :class="'hover:cursor-pointer'">
                        <div class="ml-2">Cancel</div>
                    </SecondaryButton>
                </div>
                <div class="mr-2">
                    <Button
                        :variant="'primary'"
                        :type="'submit'"
                        :size="'base'"
                        :disabled="!kendoForm.allowSubmit"
                        @click="checkFormErrors"
                    >
                        <ContextLoader
                            :context="'buttonLoading'"
                            template="spin"
                            :loadingText="'Saving Intake'"
                            :pt="{
                                root: 'flex items-center justify-center gap-1',
                            }"
                            v-if="saving"
                        />
                        <span v-else>Save Intake</span>
                    </Button>
                </div>
            </div>
        </dialog-actions-bar>
    </form-element>
</template>

<script>
import { Field, FormElement } from '@progress/kendo-vue-form';
import FormInput from '@spa/components/KendoInputs/FormInput.vue';
import FormTextArea from '@spa/components/KendoInputs/FormTextArea.vue';
import FormCheckboxInline from '@spa/components/KendoInputs/FormCheckboxInline.vue';
import FormNumericInput from '@spa/components/KendoInputs/FormNumericInput.vue';
import FormNumericTextbox from '@spa/components/KendoInputs/FormNumericTextBox.vue';
import FormDatePicker from '@spa/components/KendoInputs/FormDatePicker.vue';
import FormDateTimePicker from '@spa/components/KendoInputs/FormDateTimePicker.vue';
import FormRadioGroup from '@spa/components/KendoInputs/FormRadioGroup.vue';
import FormSwitch from '@spa/components/KendoInputs/FormSwitch.vue';
import FormDropDown from '@spa/components/KendoInputs/FormDropDown.vue';
import Button from '@spa/components/Buttons/Button';
import PrimaryButton from '@spa/components/Buttons/PrimaryButton.vue';
import SecondaryButton from '@spa/components/Buttons/SecondaryButton.vue';
import { mapState } from 'pinia';
import { useCoursesStore } from '@spa/stores/modules/courses';
import NavigationButtons from '@spa/pages/courses/includes/NavigationButtons.vue';
import FileUploader from '@spa/components/Uploader/FileUploader.vue';
import { Upload } from '@progress/kendo-vue-upload';
import { DialogActionsBar } from '@progress/kendo-vue-dialogs';
import { Checkbox } from '@progress/kendo-vue-inputs';
import ContextLoader from '@spa/components/Loader/ContextLoader.vue';

import {
    requiredtrue,
    requiredCode,
    validDate,
    requiredDate,
    validEndDate,
    requiredmonetoryvalue,
} from '@spa/services/validators/kendoCommonValidator.js';

export default {
    components: {
        field: Field,
        'form-element': FormElement,
        forminput: FormInput,
        formtextarea: FormTextArea,
        formcheckbox: FormCheckboxInline,
        checkbox: Checkbox,
        numericinput: FormNumericInput,
        numerictextbox: FormNumericTextbox,
        formradiogroup: FormRadioGroup,
        formswitch: FormSwitch,
        formdropdown: FormDropDown,
        formdatepicker: FormDatePicker,
        formdatetimepicker: FormDateTimePicker,
        Button,
        navigationbuttons: NavigationButtons,
        'file-upload': FileUploader,
        upload: Upload,
        'dialog-actions-bar': DialogActionsBar,
        PrimaryButton,
        SecondaryButton,
        ContextLoader,
    },
    props: {
        saving: { type: Boolean, default: false },
    },
    inject: {
        kendoForm: { default: {} },
    },
    mounted() {
        this.getCampusesData();
    },
    computed: {
        ...mapState(useCoursesStore, [
            'formInits',
            'currentposition',
            'operationMode',
            'formInits',
            'faculties',
        ]),
        hours() {
            const hours = [];
            for (let i = 5; i < 20; i++) {
                hours.push({ id: i, name: i.toString().padStart(2, '0') });
            }
            return hours;
        },
        minutes() {
            const minutes = [];
            for (let i = 0; i < 60; i = i + 5) {
                minutes.push({ id: i, name: i.toString().padStart(2, '0') });
            }
            return minutes;
        },
        recurringIntervals() {
            return [
                { id: 'non-recurring', name: 'Non-Recurring' },
                { id: 'daily', name: 'Daily' },
                { id: 'weekly', name: 'Weekly' },
                { id: 'monthly', name: 'Monthly' },
            ];
        },
        deliveryTargetData: function () {
            return this.formInits?.delivery_targets;
        },
        showDomesticFee: function () {
            const intakeReceiver = this.kendoForm.valueGetter('intake_receiver');
            return intakeReceiver == 'Both' || intakeReceiver == 'Domestic';
        },
        showInternationalFee: function () {
            const intakeReceiver = this.kendoForm.valueGetter('intake_receiver');
            return intakeReceiver != 'Domestic';
        },
        isClassRecurring() {
            const recurringClass = this.kendoForm.valueGetter('recurring_class');
            return recurringClass !== 'non-recurring';
        },
        isInEditMode() {
            return this.kendoForm.valueGetter('id') !== null;
        },
    },
    data: function () {
        return {
            dataitem: [],
            intakeStatusData: [
                { value: 1, label: 'Active' },
                { value: 0, label: 'Inactive' },
            ],
            defaultTarget: {
                text: 'Select Delivery Target ...',
                id: '',
            },
            selectedCampuses: [],
        };
    },
    methods: {
        requiredtrue,
        requiredCode,
        validDate,
        requiredDate,
        validEndDate,
        requiredmonetoryvalue,
        getCampusesData: function () {
            const facultiesData = this.faculties?.campus_list || '';
            const facultiesArr = facultiesData.split(',');
            const selectedCampuses = this.formInits?.campuses.filter((campus) =>
                facultiesArr.includes(String(campus.id))
            );
            const appliedCampuses = this.kendoForm.valueGetter('campuses') || [];
            this.selectedCampuses = selectedCampuses.map((campus) => ({
                text: campus.text,
                id: campus.id,
                selected:
                    this.isInEditMode || appliedCampuses.length > 0
                        ? appliedCampuses.includes(campus.id)
                        : true,
            }));
        },
        cancelProcess() {
            this.$emit('cancel');
        },
        requiredCampus() {
            const selectedCampuses = this.selectedCampuses.filter((campus) => campus.selected);
            if (selectedCampuses.length === 0) {
                return 'Please select at least one campus.';
            }
            return '';
        },
        convertDateSring(value) {
            //convert the string to yyyy-mm-dd format
            if (value instanceof Date) return value;
            const parts = value ? value.split('-') : '';
            let dateString = '';
            if (parts.length === 3) {
                dateString = `${parts[0]}-${parts[1]}-${parts[2]}`;
            }
            return dateString;
        },
        checkIntakeEnd(value) {
            const start = this.kendoForm.valueGetter('intake_start') || '';
            const startDate = this.convertDateSring(start);
            const endDate = this.convertDateSring(value);
            return this.validEndDate(
                endDate,
                startDate,
                0,
                'Intake end date must be greater than intake start date.'
            );
        },
        checkClassEndDate(value) {
            const start = this.kendoForm.valueGetter('class_start_date') || '';
            return this.validEndDate(
                this.convertDateSring(value),
                this.convertDateSring(start),
                0,
                'Class end date must be greater than class start date.'
            );
        },
        checkRecurringEndDate(value) {
            const start = this.kendoForm.valueGetter('class_start_date') || '';
            if (this.isClassRecurring) {
                return this.validEndDate(
                    this.convertDateSring(value),
                    this.convertDateSring(start),
                    1,
                    'Recurring end date must be greater than class start date.'
                );
            }
            return '';
        },
        checkInternationalFeeRequired(value) {
            const visible = this.showInternationalFee;
            if (visible) {
                return this.requiredmonetoryvalue(value);
            }
        },
        checkDomesticFeeRequired(value) {
            const visible = this.showDomesticFee;
            if (visible) {
                return this.requiredmonetoryvalue(value);
            }
        },
        checkFormErrors() {
            if (!this.kendoForm.valid && this.kendoForm.visited) {
                const firstElement = document.querySelector('.k-text-error');
                if (firstElement) {
                    firstElement.scrollIntoView({
                        behavior: 'smooth',
                        block: 'start', // aligns top of element with top of viewport
                        inline: 'nearest',
                    });
                }
            }
            // Add offset after scrolling
        },
    },
    watch: {
        formInits: {
            handler(newVal) {
                this.getCampusesData();
            },
            deep: true,
        },
        selectedCampuses: {
            handler(newVal) {
                const selectedValues = newVal
                    .filter((campus) => campus.selected)
                    .map((campus) => campus.id)
                    .join(',');
                this.kendoForm.onChange('campuses', { value: selectedValues });
            },
            deep: true,
        },
    },
};
</script>
