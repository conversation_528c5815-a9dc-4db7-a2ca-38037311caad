<template>
    <form-element>
        <fieldset class="k-form-fieldset space-y-6" :style="{ maxWidth: '650px' }">
            <div v-if="!isVetSynced" class="space-y-6">
                <div class="tw-radio-group">
                    <field
                        :id="'course_types'"
                        :name="'course_type_id'"
                        :label="isProfile ? 'Course Type' : 'Select Course Type'"
                        :data-items="courseTypesData"
                        :layout="'vertical'"
                        :component="'courseTypeTemplate'"
                        :validator="requiredtrue"
                    >
                        <template v-slot:courseTypeTemplate="{ props }">
                            <formradiogroup
                                v-bind="props"
                                @change="props.onChange"
                                @blur="props.onBlur"
                                @focus="props.onFocus"
                            />
                        </template>
                    </field>
                </div>
                <div class="grid grid-cols-2 gap-x-6 gap-y-1" :class="isVetType ? 'mb-0' : 'mb-2'">
                    <field
                        :id="'national_code'"
                        :name="'national_code'"
                        :component="'myTemplate'"
                        :label="'National Code'"
                        :placeholder="'Enter National Code'"
                        :validator="requiredCode"
                        :class="'uppercase'"
                    >
                        <template v-slot:myTemplate="{ props }">
                            <forminput
                                v-bind="props"
                                @change="props.onChange"
                                @blur="props.onBlur"
                                @focus="props.onFocus"
                            />
                        </template>
                    </field>
                    <field
                        :id="'course_code'"
                        :name="'course_code'"
                        :component="'myTemplate'"
                        :label="'Course Code'"
                        :placeholder="'Enter Course Code'"
                        :validator="requiredCode"
                        :class="'uppercase'"
                    >
                        <template v-slot:myTemplate="{ props }">
                            <forminput
                                v-bind="props"
                                @change="props.onChange"
                                @blur="props.onBlur"
                                @focus="props.onFocus"
                            />
                        </template>
                    </field>
                    <template v-if="isVetType">
                        <div
                            class="col-span-2 flex cursor-pointer items-center space-x-2"
                            @click="syncVetData"
                        >
                            <icon
                                :name="'loading'"
                                :width="24"
                                :height="24"
                                :stroke="'#E2E8F0'"
                                :fill="'#10B981'"
                                v-if="syncreqest"
                            />
                            <icon
                                :name="'sync'"
                                :width="16"
                                :height="16"
                                :fill="'#10B981'"
                                v-else
                            />
                            <div class="text-gray-700">
                                {{ syncreqest ? 'Requesting Data' : 'Sync' }}
                            </div>
                        </div>
                    </template>
                </div>
                <field
                    :id="'course_name'"
                    :name="'course_name'"
                    :component="'myTemplate'"
                    :label="'Course Name'"
                    :placeholder="'Enter Course Name'"
                    :validator="requiredtrue"
                >
                    <template v-slot:myTemplate="{ props }">
                        <forminput
                            v-bind="props"
                            @change="props.onChange"
                            @blur="props.onBlur"
                            @focus="props.onFocus"
                        />
                    </template>
                </field>
            </div>
            <div v-else class="space-y-6 py-6">
                <div class="field-value flex space-x-4 font-medium">
                    <div class="w-1/3 text-gray-500 md:w-1/4">Course Type</div>
                    <div class="w-3/4 text-gray-700">
                        {{ getCourseTypeText }}
                    </div>
                </div>
                <div class="field-value flex space-x-4 font-medium">
                    <div class="w-1/3 text-gray-500 md:w-1/4">National Code</div>
                    <div class="w-3/4 text-gray-700">
                        {{ kendoForm.valueGetter('national_code') }}
                    </div>
                </div>
                <div class="field-value flex space-x-4 font-medium">
                    <div class="w-1/3 text-gray-500 md:w-1/4">Course Code</div>
                    <div class="w-3/4 text-gray-700">
                        {{ kendoForm.valueGetter('course_code') }}
                    </div>
                </div>
                <div class="field-value flex space-x-4 font-medium">
                    <div class="w-1/3 text-gray-500 md:w-1/4">Course Name</div>
                    <div class="w-3/4 text-gray-700">
                        {{ kendoForm.valueGetter('course_name') }}
                    </div>
                </div>
            </div>
            <transition name="fade">
                <div class="mb-3 flex items-center justify-between" v-if="showModuleDelivery">
                    <div class="w-auto">
                        <label for="module_delivery" class="font-medium text-gray-700"
                            >Module Delivery</label
                        >
                    </div>
                    <div class="w-auto">
                        <field
                            :id="'module_delivery'"
                            :name="'module_delivery'"
                            :component="'switchTemplate'"
                        >
                            <template v-slot:switchTemplate="{ props }">
                                <formswitch
                                    v-bind="props"
                                    @change="props.onChange"
                                    @blur="props.onBlur"
                                    @focus="props.onFocus"
                                />
                            </template>
                        </field>
                    </div>
                </div>
            </transition>
            <transition name="fade">
                <div class="mb-3 flex items-center justify-between" v-if="showSuperSeeded">
                    <div class="w-auto">
                        <label for="module_delivery" class="font-medium text-gray-700"
                            >Is this course superseded</label
                        >
                    </div>
                    <div class="w-auto">
                        <field
                            :id="'is_superseded'"
                            :name="'is_superseded'"
                            :component="'switchTemplate'"
                        >
                            <template v-slot:switchTemplate="{ props }">
                                <formswitch
                                    v-bind="props"
                                    @change="props.onChange"
                                    @blur="props.onBlur"
                                    @focus="props.onFocus"
                                />
                            </template>
                        </field>
                    </div>
                </div>
            </transition>
            <transition name="fade">
                <div
                    class="mb-3 grid grid-cols-1 gap-4 space-x-0 md:flex md:gap-0 md:space-x-4"
                    v-if="showSuperseededDate"
                >
                    <div class="w-full md:w-1/2">
                        <field
                            :id="'superseded_date'"
                            :name="'superseded_date'"
                            :component="'myTemplate'"
                            :label="'Supersed Date'"
                            :hint="'Date from which the course is not available for enrollment'"
                            :validator="validDate"
                        >
                            <template v-slot:myTemplate="{ props }">
                                <formdatepicker
                                    v-bind="props"
                                    @change="props.onChange"
                                    @blur="props.onBlur"
                                    @focus="props.onFocus"
                                />
                            </template>
                        </field>
                    </div>
                    <div class="w-full md:w-1/2">
                        <field
                            :id="'is_super_code'"
                            :name="'is_super_code'"
                            :component="'myTemplate'"
                            :label="'Course code to supersed this course'"
                            :placeholder="'Enter Course Code'"
                            :validator="requiredSuperCode"
                            :class="'uppercase'"
                        >
                            <template v-slot:myTemplate="{ props }">
                                <forminput
                                    v-bind="props"
                                    @change="props.onChange"
                                    @blur="props.onBlur"
                                    @focus="props.onFocus"
                                />
                            </template>
                        </field>
                    </div>
                </div>
            </transition>
            <div
                class="mb-3 grid grid-cols-1 justify-between gap-4 space-x-0 md:flex md:gap-0 md:space-x-4"
            >
                <div class="w-full md:w-1/2">
                    <field
                        :id="'delivery_target'"
                        :name="'delivery_target'"
                        :label="'Delivery Target'"
                        :component="'courseGeneral'"
                        :text-field="'text'"
                        :data-item-key="'id'"
                        :valueField="'id'"
                        :valuePrimitive="true"
                        :default-item="defaultTarget"
                        :data-items="deliveryTargetData"
                        :validator="requiredtrue"
                    >
                        <template v-slot:courseGeneral="{ props }">
                            <formdropdown
                                v-bind="props"
                                @change="props.onChange"
                                @blur="props.onBlur"
                                @focus="props.onFocus"
                            />
                        </template>
                    </field>
                </div>
                <div class="w-full md:w-1/2">
                    <field
                        :id="'qualification_prerequisite_id'"
                        :name="'qualification_prerequisite_id'"
                        :label="'Qualification Prerequisite'"
                        :component="'courseGeneral'"
                        :text-field="'text'"
                        :data-item-key="'id'"
                        :valueField="'id'"
                        :valuePrimitive="true"
                        :default-item="defaultPrerequisites"
                        :data-items="coursePrerequisiteData"
                    >
                        <template v-slot:courseGeneral="{ props }">
                            <formdropdown
                                v-bind="props"
                                @change="props.onChange"
                                @blur="props.onBlur"
                                @focus="props.onFocus"
                            />
                        </template>
                    </field>
                </div>
            </div>
            <transition name="fade">
                <div v-if="showCriosCode">
                    <div class="mb-3">
                        <field
                            :id="'cricos_code'"
                            :name="'cricos_code'"
                            :component="'myTemplate'"
                            :label="'Course CRICOS Code'"
                            :placeholder="'Enter CRICOS Code'"
                            :validator="requiredCricosCode"
                            :class="'uppercase'"
                        >
                            <template v-slot:myTemplate="{ props }">
                                <forminput
                                    v-bind="props"
                                    @change="props.onChange"
                                    @blur="props.onBlur"
                                    @focus="props.onFocus"
                                />
                            </template>
                        </field>
                        <field
                            :id="'is_cricos_code'"
                            :name="'is_cricos_code'"
                            :label="'No CRICOS Code'"
                            :checked="nocricos"
                            :component="'switchTemplate'"
                            :class="'!mt-4'"
                        >
                            <template v-slot:switchTemplate="{ props }">
                                <formcheckbox
                                    v-bind="props"
                                    @change="props.onChange"
                                    @blur="props.onBlur"
                                    @focus="props.onFocus"
                                />
                            </template>
                        </field>
                    </div>
                </div>
            </transition>
            <div class="mb-3" v-if="showMajorSubject">
                <field
                    :id="'major'"
                    :name="'major'"
                    :label="'Major'"
                    :component="'courseFaculty'"
                    :placeholder="'Enter the Major Subject'"
                >
                    <template v-slot:courseFaculty="{ props }">
                        <forminput
                            v-bind="props"
                            @change="props.onChange"
                            @blur="props.onBlur"
                            @focus="props.onFocus"
                        />
                    </template>
                </field>
            </div>
            <div class="rounded-md border border-gray-200 p-4" v-if="isHigherEd || isShortCourse">
                <div class="mb-3" v-if="isHigherEd">
                    <field
                        :id="'qtac_course_id'"
                        :name="'qtac_course_id'"
                        :label="'QTAC Course ID'"
                        :component="'courseFaculty'"
                        :placeholder="'Enter QTAC Course ID'"
                    >
                        <template v-slot:courseFaculty="{ props }">
                            <forminput
                                v-bind="props"
                                @change="props.onChange"
                                @blur="props.onBlur"
                                @focus="props.onFocus"
                            />
                        </template>
                    </field>
                </div>
                <div class="mb-3">
                    <field
                        :id="'course_description'"
                        :name="'course_description'"
                        :component="'myTemplate'"
                        :label="'Description'"
                        :placeholder="'Enter Description'"
                    >
                        <template v-slot:myTemplate="{ props }">
                            <formtextarea
                                v-bind="props"
                                @change="props.onChange"
                                @blur="props.onBlur"
                                @focus="props.onFocus"
                            />
                        </template>
                    </field>
                </div>
                <div class="mb-3" v-if="isShortCourse">
                    <field
                        :id="'course_summary'"
                        :name="'course_summary'"
                        :component="'myTemplate'"
                        :label="'Course Summary'"
                        :placeholder="'Enter Brief Course Description'"
                        :max="500"
                        :hint="'Give very short summary of course (Max 500 characters)'"
                    >
                        <template v-slot:myTemplate="{ props }">
                            <formtextarea
                                v-bind="props"
                                @change="props.onChange"
                                @blur="props.onBlur"
                                @focus="props.onFocus"
                            />
                        </template>
                    </field>
                </div>
                <div class="mb-3" v-if="isShortCourse">
                    <field
                        :id="'entry_requirements'"
                        :name="'entry_requirements'"
                        :component="'myTemplate'"
                        :label="'Entry Requirements'"
                        :placeholder="'Enter Entry Requirements'"
                    >
                        <template v-slot:myTemplate="{ props }">
                            <formtextarea
                                v-bind="props"
                                @change="props.onChange"
                                @blur="props.onBlur"
                                @focus="props.onFocus"
                            />
                        </template>
                    </field>
                </div>
                <div class="mb-3" v-if="isShortCourse">
                    <field
                        :id="'identity_proof'"
                        :name="'identity_proof'"
                        :component="'myTemplate'"
                        :label="'Identity Proof Requirements'"
                        :placeholder="'Enter Identity Proof Requirements'"
                    >
                        <template v-slot:myTemplate="{ props }">
                            <formtextarea
                                v-bind="props"
                                @change="props.onChange"
                                @blur="props.onBlur"
                                @focus="props.onFocus"
                            />
                        </template>
                    </field>
                </div>
                <div class="mb-3" v-if="isShortCourse">
                    <field
                        :id="'course_tags'"
                        :name="'course_tags'"
                        :component="'myTemplate'"
                        :label="'Tags for the course'"
                        :placeholder="'Enter tags for this course'"
                        :hint="'Provide tags saperated by a comma. For example: Tag One, Tag Two etc.'"
                    >
                        <template v-slot:myTemplate="{ props }">
                            <forminput
                                v-bind="props"
                                @change="props.onChange"
                                @blur="props.onBlur"
                                @focus="props.onFocus"
                            />
                        </template>
                    </field>
                </div>
                <div class="mb-3" v-if="isShortCourse">
                    <div class="flex space-x-2 align-top">
                        <div v-if="hasCoverImage">
                            <img :src="hasCoverImage" class="w-64" />
                        </div>
                        <div>
                            <field
                                :id="'photos'"
                                :name="'photos'"
                                :label="'Upload Photos'"
                                :hintMessage="'Hint: Select your additional photos'"
                                :component="'myTemplate'"
                                :multiple="false"
                                :autoUpload="false"
                                :accept="'image/*'"
                                @add="handleOnAdd"
                            >
                                <template v-slot:myTemplate="{ props }">
                                    <file-upload
                                        v-bind="props"
                                        @change="props.onChange"
                                        @blur="props.onBlur"
                                        @focus="props.onFocus"
                                    ></file-upload>
                                </template>
                            </field>
                        </div>
                    </div>
                </div>
            </div>
        </fieldset>
        <div
            class="rounded-md border border-gray-200 bg-gray-50 p-4"
            v-if="!isProfile && showAvetmiss"
        >
            <div class="flex items-center space-x-2 border-b border-gray-200 pb-4">
                <div class="text-base font-normal text-gray-700">Avetmiss Values</div>
                <svg
                    width="20"
                    height="20"
                    viewBox="0 0 20 20"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <path
                        d="M10 2C14.4183 2 18 5.58172 18 10C18 14.4183 14.4183 18 10 18C5.58172 18 2 14.4183 2 10C2 5.58172 5.58172 2 10 2ZM10 3C6.13401 3 3 6.13401 3 10C3 13.866 6.13401 17 10 17C13.866 17 17 13.866 17 10C17 6.13401 13.866 3 10 3ZM10 13.5C10.4142 13.5 10.75 13.8358 10.75 14.25C10.75 14.6642 10.4142 15 10 15C9.58579 15 9.25 14.6642 9.25 14.25C9.25 13.8358 9.58579 13.5 10 13.5ZM10 5.5C11.3807 5.5 12.5 6.61929 12.5 8C12.5 8.72959 12.1848 9.40774 11.6513 9.8771L11.4967 10.0024L11.2782 10.1655L11.1906 10.2372C11.1348 10.2851 11.0835 10.3337 11.0346 10.3859C10.6963 10.7464 10.5 11.2422 10.5 12C10.5 12.2761 10.2761 12.5 10 12.5C9.72386 12.5 9.5 12.2761 9.5 12C9.5 10.988 9.79312 10.2475 10.3054 9.70162C10.4165 9.5832 10.532 9.47988 10.6609 9.37874L10.9076 9.19439L11.0256 9.09468C11.325 8.81435 11.5 8.42206 11.5 8C11.5 7.17157 10.8284 6.5 10 6.5C9.17157 6.5 8.5 7.17157 8.5 8C8.5 8.27614 8.27614 8.5 8 8.5C7.72386 8.5 7.5 8.27614 7.5 8C7.5 6.61929 8.61929 5.5 10 5.5Z"
                        fill="#9CA3AF"
                    />
                </svg>
            </div>
            <div class="mt-4 space-y-6">
                <div class="flex w-full space-x-4">
                    <div class="text-md w-1/4 text-xs font-light uppercase tracking-wider">
                        ANZSCO Code
                    </div>
                    <div class="text-md w-3/4 font-medium">
                        {{ getCourseANZSCOCode }}
                    </div>
                </div>
                <div class="flex w-full space-x-4">
                    <div class="text-md w-1/4 text-xs font-light uppercase tracking-wider">
                        Course Recognition
                    </div>
                    <div class="text-md w-3/4 font-medium">
                        {{ getCourseRecognition }}
                    </div>
                </div>
                <div class="flex w-full space-x-4">
                    <div class="text-md w-1/4 text-xs font-light uppercase tracking-wider">
                        Level of Education
                    </div>
                    <div class="text-md w-3/4 font-medium">
                        {{ getCourseEducationLevel }}
                    </div>
                </div>
                <div class="flex w-full space-x-4">
                    <div class="text-md w-1/4 text-xs font-light uppercase tracking-wider">
                        Field of Education
                    </div>
                    <div class="text-md w-3/4 font-medium">
                        {{ getCourseEducationField }}
                    </div>
                </div>
                <div class="flex w-full space-x-4">
                    <div class="text-md w-1/4 text-xs font-light uppercase tracking-wider">
                        Total Nominal Hours
                    </div>
                    <div class="text-md w-3/4 font-medium">
                        {{ getCourseNominalHours }}
                    </div>
                </div>
                <div class="flex w-full space-x-4">
                    <div class="text-md w-1/4 text-xs font-light uppercase tracking-wider">
                        VET Flag
                    </div>
                    <div class="text-md w-3/4 font-medium">
                        {{ getCourseVetFlag }}
                    </div>
                </div>
            </div>
        </div>
        <navigationbuttons :allowsave="kendoForm.allowSubmit" />
    </form-element>
</template>

<script>
import { Field, FormElement } from '@progress/kendo-vue-form';
import FormInput from '@spa/components/KendoInputs/FormInput.vue';
import FormTextArea from '@spa/components/KendoInputs/FormTextArea.vue';
import FormCheckboxInline from '@spa/components/KendoInputs/FormCheckboxInline.vue';
import FormNumericInput from '@spa/components/KendoInputs/FormNumericInput.vue';
import FormDatePicker from '@spa/components/KendoInputs/FormDatePicker.vue';
import FormRadioGroup from '@spa/components/KendoInputs/FormRadioGroup.vue';
import FormSwitch from '@spa/components/KendoInputs/FormSwitch.vue';
import FormDropDown from '@spa/components/KendoInputs/FormDropDown.vue';
import { Button } from '@progress/kendo-vue-buttons';
import { mapState } from 'pinia';
import { useCoursesStore } from '@spa/stores/modules/courses';
import NavigationButtons from '@spa/pages/courses/includes/NavigationButtons.vue';
import FileUploader from '@spa/components/Uploader/FileUploader.vue';
import { Upload } from '@progress/kendo-vue-upload';

import {
    requiredtrue,
    requiredCode,
    validDate,
} from '@spa/services/validators/kendoCommonValidator.js';

export default {
    components: {
        field: Field,
        'form-element': FormElement,
        forminput: FormInput,
        formtextarea: FormTextArea,
        formcheckbox: FormCheckboxInline,
        numericinput: FormNumericInput,
        formradiogroup: FormRadioGroup,
        formswitch: FormSwitch,
        formdropdown: FormDropDown,
        formdatepicker: FormDatePicker,
        kbutton: Button,
        navigationbuttons: NavigationButtons,
        'file-upload': FileUploader,
        upload: Upload,
    },
    props: {
        synced: { type: Boolean, default: false },
    },
    inject: {
        kendoForm: { default: {} },
    },
    computed: {
        ...mapState(useCoursesStore, ['formInits', 'currentposition', 'operationMode']),
        isVetSynced: function () {
            return this.synced === true;
        },
        getCourseTypeText: function () {
            const courseType = this.kendoForm.valueGetter('course_type_id');
            const result = this.courseTypesData.find((item) => item.value === courseType);
            return result ? result.label : 'N/A';
        },
        isVetType: function () {
            return this.kendoForm.valueGetter('course_type_id') == 2;
        },
        showModuleDelivery: function () {
            return this.isVetType;
        },
        showSuperSeeded: function () {
            return this.isVetType;
        },
        showVetFlag: function () {
            return this.isVetType;
        },
        showCriosCode: function () {
            const target = this.kendoForm.valueGetter('delivery_target');
            return target == 'International' || target == 'Both';
        },
        showSuperseededDate: function () {
            return this.showSuperSeeded && this.kendoForm.valueGetter('is_superseded') == true;
        },
        showMajorSubject: function () {
            return this.kendoForm.valueGetter('course_type_id') != 2;
        },
        isHigherEd: function () {
            return this.kendoForm.valueGetter('course_type_id') == 17;
        },
        isShortCourse: function () {
            return this.kendoForm.valueGetter('course_type_id') == 16;
        },
        courseTypesData: function () {
            return this.formInits?.course_types;
        },
        deliveryTargetData: function () {
            return this.formInits?.delivery_targets;
        },
        coursePrerequisiteData: function () {
            return this.formInits?.prerequisites;
        },
        getcurrentpos: function () {
            return this.currentposition;
        },
        getCourseANZSCOCode() {
            const searchFor = this.kendoForm.valueGetter('ANZSCO_code') || null;
            const found = this.formInits?.anzsco_codes.find((code) => code.id === searchFor);
            return found?.text || 'N/A';
        },
        getCourseRecognition() {
            const searchFor = parseInt(this.kendoForm.valueGetter('course_recognition_id')) || null;
            const found = this.formInits?.course_recognitions?.find(
                (recog) => recog.id === searchFor
            );
            return found?.text || 'N/A';
        },
        getCourseEducationLevel() {
            const searchFor = String(this.kendoForm.valueGetter('level_of_education_id')) || null;
            const found = this.formInits?.education_level.find((level) => level.code === searchFor);
            return found?.text || 'N/A';
        },
        getCourseEducationField() {
            const searchFor = String(this.kendoForm.valueGetter('field_of_education_id')) || null;
            const found = this.formInits?.education_field.find((field) => field.code === searchFor);
            return found?.text || 'N/A';
        },
        getCourseNominalHours() {
            const hours = this.kendoForm.valueGetter('total_nominal_hours') || null;
            return hours ? hours + ' Hrs' : 'N/A';
        },
        getCourseVetFlag() {
            const flag = this.kendoForm.valueGetter('AVETMISS_Report') || null;
            return flag ? 'Yes' : 'No';
        },
        isProfile() {
            return this.operationMode === 'profile';
        },
        nocricos() {
            return this.kendoForm.valueGetter('is_cricos_code') || false;
        },
        hasCoverImage() {
            let image = this.kendoForm.valueGetter('current_cover_image') || false;
            if (image) {
                return image.large;
            }
            return false;
        },
    },
    data: function () {
        return {
            dataitem: [],
            syncreqest: false,
            showAvetmiss: false,
            defaultTarget: {
                text: this.isProfile ? 'Delivery Target ...' : 'Select Delivery Target ...',
                id: '',
            },
            defaultPrerequisites: {
                id: '',
                text: this.isProfile ? 'Prerequisites ...' : 'Select Prerequisites ...',
            },
        };
    },
    methods: {
        requiredtrue,
        requiredCode,
        validDate,
        cancelprocess() {
            this.$emit('cancel');
        },
        requiredSuperCode(value) {
            //validate super code if superseded is true
            const requiredCode = this.showSuperseededDate;
            if (requiredCode) {
                return this.requiredCode(value);
            }
            return '';
        },
        requiredCricosCode(value) {
            const cricos = this.showCriosCode;
            const nocricos = this.kendoForm.valueGetter('is_cricos_code') || null;
            if (cricos && nocricos != true) {
                return this.requiredCode(value);
            }
        },
        syncVetData() {
            if (this.syncreqest) {
                return;
            }
            const code = this.kendoForm.valueGetter('national_code') || null;
            if (code.length > 0) {
                this.syncreqest = true;
                $http
                    .get(this.route('spa.courses.sync', [code]))
                    .then((resp) => {
                        if (resp.success === 1 && resp.course) {
                            for (const [field, fieldvalue] of Object.entries(resp.course)) {
                                this.kendoForm.onChange(field, {
                                    value: fieldvalue,
                                });
                            }
                        }
                        this.syncreqest = false;
                    })
                    .catch((error) => {
                        this.syncreqest = false;
                    });
            }
            return;
        },
        handleOnAdd(event) {
            const file = event.newState[0].getRawFile();
            this.kendoForm.onChange('cover_photo', {
                value: file,
            });
        },
        onRemove(event) {
            this.kendoForm.onChange('cover_photo', {
                value: event.newState,
            });
        },
    },
};
</script>
