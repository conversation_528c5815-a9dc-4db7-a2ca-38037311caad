<template>
    <div class="relative block justify-between gap-4 md:flex lg:gap-0" :id="`subject${count}`">
        <div class="space-y-4 lg:space-y-0">
            <div class="mb-2 grid w-full grid-cols-1 gap-2 space-x-0 md:flex lg:gap-0 lg:space-x-2">
                <div class="my-auto flex items-center justify-between gap-4 font-medium">
                    <div>
                        {{ subject.subject_code }} -
                        {{ subject.subject_name }}
                    </div>
                    <Tooltip
                        :anchor-element="'target'"
                        :position="'top'"
                        :parentTitle="true"
                        :className="'flex items-center'"
                        :tooltipClassName="'!p-1.5'"
                    >
                        <div
                            class="relative right-auto top-auto flex translate-y-0 items-center space-x-2 md:absolute md:right-4 md:top-1/2 md:-translate-y-1/2"
                        >
                            <a
                                :href="route('spa.courses.profile', [course]) + '#units'"
                                class="tw-btn-action h-7 w-7 border border-gray-200 text-gray-400"
                                title="View course profile"
                                target="_blank"
                                v-if="course"
                            >
                                <icon
                                    :name="'pencil'"
                                    :width="16"
                                    :height="16"
                                    :fill="'currentColor'"
                                />
                            </a>
                            <div
                                class="tw-btn-action h-7 w-7 border border-gray-200"
                                @click="getSubjectDetails"
                            >
                                <icon :name="'preview'" :width="16" :height="16" />
                            </div>
                            <div
                                class="tw-btn-action h-7 w-7 border border-gray-200"
                                @click="deleteSubject"
                            >
                                <icon :name="'trash'" :width="16" :height="16" :fill="'red'" />
                            </div>
                        </div>
                    </Tooltip>
                </div>
            </div>
            <div
                class="grid w-full grid-cols-2 gap-2 space-x-0 text-xs md:grid-cols-3 lg:space-x-2 xl:flex"
            >
                <div class="flex">
                    <div class="mr-2 text-gray-400">Contact Hours:</div>
                    <div class="text-gray-700">
                        {{ subject.contact_hours }}
                    </div>
                </div>
                <div class="flex">
                    <div class="mr-2 text-gray-400">Created On:</div>
                    <div class="text-gray-700">
                        <dateformat :date="subject.created_at" />
                    </div>
                </div>
                <div class="flex">
                    <div class="mr-2 text-gray-400">Last Updated On:</div>
                    <div class="text-gray-700">
                        <dateformat :date="subject.updated_at" />
                    </div>
                </div>
                <div class="flex">
                    <div class="mr-2 text-gray-400">Units:</div>
                    <div class="flex space-x-2 text-gray-700"></div>
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import { Head, Link, router } from '@inertiajs/vue3';

export default {
    setup() {},
    props: {
        subject: { type: Object, default: {} },
        count: { type: Number, default: null },
        course: { type: String, default: '' },
    },
    components: {
        Link,
    },
    data() {},
    computed: {},
    methods: {
        getSubjectDetails() {
            this.$emit('details');
        },
        deleteSubject() {
            this.$emit('delete');
        },
    },
};
</script>
