<template>
    <SidebarDrawer
        :visibleDialog="visible"
        :hideOnOverlayClick="false"
        :fixedActionBar="stubFalse"
        :width="'40%'"
        @drawerclose="cancelProcess"
        :primaryBtnLabel="'Save'"
        :existTertiary="false"
        :isDisabled="false"
        :isSubmitting="isCopying"
        :class="'custom-modal-wrapper'"
    >
        <template #title>
            <div class="text-lg font-medium capitalize">
                Unit Details :: {{ unitData.unit_code }}
            </div>
        </template>
        <template #content>
            <div id="unit-details" class="space-y-4" v-if="dataLoaded">
                <div class="rounded-0 flex items-center space-x-2 space-y-2">
                    <div class="font-medium">
                        {{ unitData.unit_code }} -
                        {{ unitData.unit_name }}
                    </div>
                    <div>
                        {{ description }}
                    </div>
                </div>
                <div
                    class="grid w-full grid-cols-1 gap-4 sm:grid-cols-1 md:grid-cols-2 lg:grid-cols-1"
                >
                    <div class="flex">
                        <div class="mr-2 text-gray-400">vet unit code:</div>
                        <div class="text-gray-700">
                            <span
                                v-if="isChanged('vet_unit_code')"
                                class="flex cursor-pointer space-x-2"
                                @click="toggleValue('vet_unit_code')"
                            >
                                <icon :name="'error'" :fill="'#ffa25e'" :width="16" :height="16" />
                                {{
                                    showOriginal.vet_unit_code
                                        ? unitData.vet_unit_code.original
                                        : unitData.vet_unit_code.current
                                }}
                            </span>
                            <span v-else>
                                {{ unitData.vet_unit_code }}
                            </span>
                        </div>
                    </div>
                    <div class="flex">
                        <div class="mr-2 text-gray-400">Unit type:</div>
                        <div class="text-gray-700">
                            <span
                                v-if="isChanged('unit_type')"
                                class="flex cursor-pointer space-x-2"
                                @click="toggleValue('unit_type')"
                            >
                                <icon :name="'error'" :fill="'#ffa25e'" :width="16" :height="16" />
                                {{
                                    showOriginal.unit_type
                                        ? unitData.unit_type.original
                                        : unitData.unit_type.current
                                }}
                            </span>
                            <span v-else>
                                {{ unitData.unit_type }}
                            </span>
                        </div>
                    </div>
                    <div class="flex">
                        <div class="mr-2 text-gray-400">Created On:</div>
                        <div class="text-gray-700">
                            <FormatDateTime :date="unitData.created_at" />
                        </div>
                    </div>
                    <div class="flex">
                        <div class="mr-2 text-gray-400">Last Updated On:</div>
                        <div class="text-gray-700">
                            <FormatDateTime :date="unitData.updated_at" />
                        </div>
                    </div>
                    <div class="flex">
                        <div class="mr-2 text-gray-400">Delivery Mode:</div>
                        <div class="text-gray-700">
                            <span
                                v-if="isChanged('delivery_mode')"
                                class="flex cursor-pointer space-x-2"
                                @click="toggleValue('delivery_mode')"
                            >
                                <icon :name="'error'" :fill="'#ffa25e'" :width="16" :height="16" />
                                {{
                                    showOriginal.delivery_mode
                                        ? unitData.delivery_mode.original
                                        : unitData.delivery_mode.current
                                }}
                            </span>
                            <span v-else>
                                {{ unitData.delivery_mode ?? 'NNN' }}
                            </span>
                        </div>
                    </div>
                    <div class="flex">
                        <div class="mr-2 text-gray-400">Nominal Hourse</div>
                        <div class="text-gray-700">
                            <span
                                v-if="isChanged('nominal_hours')"
                                class="flex cursor-pointer space-x-2"
                                @click="toggleValue('nominal_hours')"
                            >
                                <icon :name="'error'" :fill="'#ffa25e'" :width="16" :height="16" />
                                {{
                                    showOriginal.nominal_hours
                                        ? unitData.nominal_hours.original
                                        : unitData.nominal_hours.current
                                }}
                            </span>
                            <span v-else>
                                {{ unitData.nominal_hours ?? 'N/A' }}
                            </span>
                        </div>
                    </div>
                    <div class="flex">
                        <div class="mr-2 text-gray-400">Tuition Fees</div>
                        <div class="text-gray-700">
                            <span
                                v-if="isChanged('tution_fees')"
                                class="flex cursor-pointer space-x-2"
                                @click="toggleValue('tution_fees')"
                            >
                                <icon :name="'error'" :fill="'#ffa25e'" :width="16" :height="16" />
                                {{
                                    showOriginal.tution_fees
                                        ? unitData.tution_fees.original
                                        : unitData.tution_fees.current
                                }}
                            </span>
                            <span v-else>
                                {{ unitData.tution_fees ?? 'N/A' }}
                            </span>
                        </div>
                    </div>
                    <div class="flex">
                        <div class="mr-2 text-gray-400">Domestic Tuition Fees</div>
                        <div class="text-gray-700">
                            <span
                                v-if="isChanged('domestic_tution_fees')"
                                class="flex cursor-pointer space-x-2"
                                @click="toggleValue('domestic_tution_fees')"
                            >
                                <icon :name="'error'" :fill="'#ffa25e'" :width="16" :height="16" />
                                {{
                                    showOriginal.domestic_tution_fees
                                        ? unitData.domestic_tution_fees.original
                                        : unitData.domestic_tution_fees.current
                                }}
                            </span>
                            <span v-else>
                                {{ unitData.domestic_tution_fees ?? 'N/A' }}
                            </span>
                        </div>
                    </div>
                    <div class="flex">
                        <div class="mr-2 text-gray-400">Module Unit Flag</div>
                        <div class="text-gray-700">
                            <span
                                v-if="isChanged('module_unit_flag')"
                                class="flex cursor-pointer space-x-2"
                                @click="toggleValue('module_unit_flag')"
                            >
                                <icon :name="'error'" :fill="'#ffa25e'" :width="16" :height="16" />
                                {{
                                    showOriginal.module_unit_flag
                                        ? unitData.module_unit_flag.original
                                        : unitData.module_unit_flag.current
                                }}
                            </span>
                            <span v-else>
                                {{ unitData.module_unit_flag ?? 'N/A' }}
                            </span>
                        </div>
                    </div>
                    <div class="flex">
                        <div class="mr-2 text-gray-400">VET Flag</div>
                        <div class="text-gray-700">
                            <span
                                v-if="isChanged('vet_flag')"
                                class="flex cursor-pointer space-x-2"
                                @click="toggleValue('vet_flag')"
                            >
                                <icon :name="'error'" :fill="'#ffa25e'" :width="16" :height="16" />
                                {{
                                    showOriginal.vet_flag
                                        ? unitData.vet_flag.original
                                        : unitData.vet_flag.current
                                }}
                            </span>
                            <span v-else>
                                {{ unitData.vet_flag ?? 'N/A' }}
                            </span>
                        </div>
                    </div>
                    <div class="flex">
                        <div class="mr-2 text-gray-400">Work Placement</div>
                        <div class="text-gray-700">
                            <span
                                v-if="isChanged('work_placement')"
                                class="flex cursor-pointer space-x-2"
                                @click="toggleValue('work_placement')"
                            >
                                <icon :name="'error'" :fill="'#ffa25e'" :width="16" :height="16" />
                                {{
                                    showOriginal.work_placement
                                        ? unitData.work_placement.original
                                        : unitData.work_placement.current
                                }}
                            </span>
                            <span v-else>
                                {{ unitData.work_placement ?? 'N/A' }}
                            </span>
                        </div>
                    </div>
                    <div class="flex">
                        <div class="mr-2 text-gray-400">AVITMISS Report</div>
                        <div class="text-gray-700">
                            <span
                                v-if="isChanged('AVETMISS_Report')"
                                class="flex cursor-pointer space-x-2"
                                @click="toggleValue('AVETMISS_Report')"
                            >
                                <icon :name="'error'" :fill="'#ffa25e'" :width="16" :height="16" />
                                {{
                                    showOriginal.AVETMISS_Report
                                        ? unitData.AVETMISS_Report.original
                                        : unitData.AVETMISS_Report.current
                                }}
                            </span>
                            <span v-else>
                                {{ unitData.AVETMISS_Report ?? 'N/A' }}
                            </span>
                        </div>
                    </div>
                    <div class="flex">
                        <div class="mr-2 text-gray-400">Active</div>
                        <div class="text-gray-700">
                            <span
                                v-if="isChanged('status')"
                                class="flex cursor-pointer space-x-2"
                                @click="toggleValue('status')"
                            >
                                <icon :name="'error'" :fill="'#ffa25e'" :width="16" :height="16" />
                                {{
                                    showOriginal.status
                                        ? unitData.status.original
                                        : unitData.status.current
                                }}
                            </span>
                            <span v-else>
                                {{ unitData.status ? 'Active' : 'Inactive' }}
                            </span>
                        </div>
                    </div>
                </div>
            </div>
        </template>
    </SidebarDrawer>
</template>

<script>
import SidebarDrawer from '@spa/components/KendoModals/SidebarDrawer.vue';
import GlobalContextLoader from '@spa/components/Loader/GlobalContextLoader.vue';
import { useLoaderStore } from '@spa/stores/modules/global-loader';
import FormatDateTime from '@spa/components/FormatDateTime.vue';

export default {
    setup(props) {
        const loaderStore = useLoaderStore();
        return {
            loaderStore,
        };
    },
    props: {
        visible: { default: false },
        unit: { tyle: Object, default: {} },
    },
    components: {
        SidebarDrawer,
        GlobalContextLoader,
        FormatDateTime,
    },
    mounted() {
        this.setUnitData();
    },
    data() {
        return {
            dataLoaded: false,
            unitData: [],
            showOriginal: {},
        };
    },
    computed: {},
    methods: {
        cancelProcess() {
            this.$emit('closed');
        },
        setUnitData() {
            const syncData = this.unit.synced_data ?? [];
            const masterData = this.unit.master_data ?? [];
            const baseData = {
                id: syncData?.id || this.unit.id || null,
                unit_id: syncData?.unit_id || this.unit.unit_id || null,
                synced_for: syncData?.synced_for || this.unit.synced_for || null,
                unit_code: syncData?.unit_code || this.unit.unit_code || null,
                vet_unit_code: syncData?.vet_unit_code || this.unit.vet_unit_code || null,
                unit_name: syncData?.unit_name || this.unit.unit_name || null,
                description: syncData?.description || this.unit.description || null,
                unit_type: syncData?.unit_type || this.unit.unit_type || null,
                field_education: syncData?.field_education || this.unit.field_education || null,
                delivery_mode: syncData?.delivery_mode || this.unit.delivery_mode || null,
                internal: syncData?.internal || this.unit.internal || null,
                external: syncData?.external || this.unit.external || null,
                workplace_based_delivery:
                    syncData?.workplace_based_delivery ||
                    this.unit.workplace_based_delivery ||
                    null,
                nominal_hours: syncData?.nominal_hours || this.unit.nominal_hours || null,
                tution_fees: syncData?.tution_fees || this.unit.tution_fees || null,
                domestic_tution_fees:
                    syncData?.domestic_tution_fees || this.unit.domestic_tution_fees || null,
                module_unit_flag: syncData?.module_unit_flag || this.unit.module_unit_flag || null,
                vet_flag: syncData?.vet_flag || this.unit.vet_flag || null,
                work_placement: syncData?.work_placement || this.unit.work_placement || null,
                AVETMISS_Report: syncData?.AVETMISS_Report || this.unit.AVETMISS_Report || null,
                status: syncData?.status || this.unit.status || null,
                created_at: syncData?.created_at || this.unit.created_at || null,
                updated_at: syncData?.updated_at || this.unit.updated_at || null,
            };
            // Fields to ignore during diff
            const ignoreFields = [
                'id',
                'created_at',
                'updated_at',
                'synced_for',
                'unit_id',
                'course_id',
                'status',
                'subject_id',
            ];

            // Final adjusted data
            const adjustedData = {};

            for (const key in baseData) {
                const currentValue = baseData[key];
                const originalValue = masterData[key];

                if (ignoreFields.includes(key)) {
                    adjustedData[key] = currentValue;
                } else if (originalValue !== undefined && currentValue !== originalValue) {
                    adjustedData[key] = {
                        current: currentValue,
                        original: originalValue,
                        changed: true,
                    };
                } else {
                    adjustedData[key] = currentValue;
                }
            }
            this.unitData = adjustedData;
            this.dataLoaded = true;
        },
        isChanged(key) {
            return (
                this.unitData[key] &&
                typeof this.unitData[key] === 'object' &&
                this.unitData[key].changed
            );
        },
        toggleValue(key) {
            this.$set(this.showOriginal, key, !this.showOriginal[key]);
        },
    },
    watch: {
        unit: {
            handler(newval, oldval) {
                this.setUnitData(newval);
            },
            deep: true,
        },
    },
};
</script>
