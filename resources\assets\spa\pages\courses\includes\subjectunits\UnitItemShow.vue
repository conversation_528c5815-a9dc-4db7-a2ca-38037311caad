<template>
    <div class="grid grid-cols-12 gap-4">
        <div class="col-span-8 flex flex-wrap items-start items-center gap-2 md:flex-nowrap">
            <div class="" :title="getErrorsText">
                <icon :name="'error'" :fill="'#ffa25e'" :width="16" :height="16" v-if="hasErrors" />
                <icon :name="'checkcirc'" :width="16" :height="16" :fill="'#10B981'" v-else />
            </div>
            <div class="flex items-center">
                <icon :name="'draghandle'" className="mt-1 min-w-5" v-if="display != 'basic'" />
                <UnitTypeBadge
                    :type="unit.unit_type"
                    :item="`${isHigherEd ? 'Subject' : 'Unit'}`"
                />
            </div>
            <div class="relative flex flex-wrap gap-2 md:flex-nowrap">
                <div class="mt-1 text-xs text-gray-500" :title="unit.Code">
                    {{ getUnitCode }}
                </div>
                <div class="line-clamp-2 text-gray-700" :title="unit.Title">
                    {{ getUnitName }}
                </div>
            </div>
        </div>
        <div class="col-span-2 flex justify-start space-x-2">
            <icon :name="'time'" />
            <div v-if="unit.nominal_hours">{{ unit.nominal_hours }} Hrs</div>
            <div v-else>N/A</div>
        </div>
        <div class="col-span-2 flex flex-wrap justify-end gap-2 md:flex-nowrap">
            <div class="flex min-w-[50px] justify-end gap-2">
                <div class="cursor-pointer" @click="viewDetails" title="View Details">
                    <icon :name="'eye'" :width="18" :height="18" />
                </div>
                <div
                    class="cursor-pointer text-gray-400"
                    @click="editUnit"
                    title="Edit Unit"
                    v-if="editable"
                >
                    <icon :name="'pencil'" :width="20" :height="20" :fill="'currentColor'" />
                </div>
                <div class="cursor-pointer" @click="deleteUnit" title="Remove Unit">
                    <icon :name="'cross'" :width="20" :height="20" />
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import UnitTypeBadge from '@spa/pages/courses/commons/UnitTypeBadge.vue';
import ActionMenu from '@spa/components/KendoGrid/ActionMenu.vue';
import { mapState } from 'pinia';
import { useCoursesStore } from '@spa/stores/modules/courses';

export default {
    emits: ['editunit', 'deleteunit', 'assign'],
    props: {
        unit: { type: Object, default: {} },
        type: { type: String, default: '' },
        display: { type: String, default: 'full' },
        coursetype: { type: Number, default: null },
        editable: { type: Boolean, default: false },
    },
    data: function () {
        return {};
    },
    components: {
        UnitTypeBadge,
        'dropdown-menu': ActionMenu,
    },
    computed: {
        ...mapState(useCoursesStore, ['course', 'allTemplates']),
        getUnitName() {
            return this.unit.unit_name;
        },
        getUnitCode() {
            return this.unit.unit_code;
        },
        isHigherEd() {
            return this.isCourseHigherEd(this.coursetype || null);
        },
        hasErrors() {
            const errors = this.unit?.unit_status?.errors || [];
            if (errors.length > 0) return true;
        },
        getErrorsText() {
            const errors = this.unit?.unit_status?.errors || [];
            if (errors.length > 0) {
                return errors.join(', ');
            }
            return 'Unit is decently configured';
        },
    },
    methods: {
        viewDetails() {
            this.$emit('details', this.unit);
        },
        editUnit() {
            this.$emit('editunit', this.unit);
        },
        deleteUnit() {
            this.$emit('deleteunit', this.unit);
        },
    },
};
</script>
