<template>
    <div class="grid grid-cols-12 gap-4">
        <div class="col-span-8 flex flex-wrap items-start gap-2 md:flex-nowrap">
            <div class="flex items-center">
                <icon :name="'draghandle'" className="mt-1 min-w-5" v-if="display != 'basic'" />
                <UnitTypeBadge :type="type" :item="`${isHigherEd ? 'Subject' : 'Unit'}`" />
            </div>
            <div class="relative flex flex-wrap gap-2 md:flex-nowrap">
                <div
                    v-if="unit.duplicate_unit"
                    class="absolute -left-4 -top-1"
                    title="Duplicate Record"
                >
                    <icon :name="'error'" :fill="'#ffa25e'" :width="16" :height="16" />
                </div>
                <div class="mt-1 text-xs text-gray-500" :title="unit.Code">
                    {{ getUnitCode }}
                </div>
                <div class="line-clamp-2 text-gray-700" :title="unit.Title">
                    {{ getUnitName }}
                </div>
            </div>
        </div>
        <div class="col-span-2 flex justify-start space-x-2">
            <icon :name="'time'" />
            <div v-if="unit.unit_details.nominal_hours">
                {{ unit.unit_details.nominal_hours }} Hrs
            </div>
            <div v-else>N/A</div>
        </div>
        <div
            class="col-span-2 flex flex-wrap justify-between gap-2 md:flex-nowrap"
            v-if="display != 'basic'"
        >
            <div class="flex min-w-[50px] justify-end gap-2">
                <div class="cursor-pointer text-gray-400" @click="editUnit(unit)" title="Edit Unit">
                    <icon :name="'pencil'" :fill="'currentColor'" />
                </div>
                <div class="cursor-pointer" @click="deleteUnit(unit)" title="Remove Unit">
                    <icon :name="'cross'" />
                </div>
            </div>
        </div>
    </div>
</template>
<script>
import UnitTypeBadge from '@spa/pages/courses/commons/UnitTypeBadge.vue';
import ActionMenu from '@spa/components/KendoGrid/ActionMenu.vue';
import { mapState } from 'pinia';
import { useCoursesStore } from '@spa/stores/modules/courses';

export default {
    emits: ['editunit', 'deleteunit', 'assign'],
    props: {
        unit: { type: Object, default: {} },
        type: { type: String, default: '' },
        display: { type: String, default: 'full' },
    },
    data: function () {
        return {};
    },
    components: {
        UnitTypeBadge,
        'dropdown-menu': ActionMenu,
    },
    computed: {
        ...mapState(useCoursesStore, ['course', 'allTemplates']),
        getUnitName() {
            return this.unit.unit_details ? this.unit.unit_details.unit_name : this.unit.Title;
        },
        getUnitCode() {
            return this.unit.unit_details ? this.unit.unit_details.unit_code : this.unit.Code;
        },
        isHigherEd() {
            return this.isCourseHigherEd(this.course.course_type_id || null);
        },
    },
    methods: {
        editUnit(unit) {
            this.$emit('editunit', unit);
        },
        deleteUnit(unit) {
            this.$emit('deleteunit', unit);
        },
    },
};
</script>
