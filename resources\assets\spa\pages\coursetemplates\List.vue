<template>
    <Layout>
        <Head title="List of available course templates" />
        <template v-slot:pageTitleContent>
            <PageTitleContent :title="'Course Templates List'" :back="false" />
        </template>
        <div class="space-y-6">
            <div class="flex items-center justify-between">
                <div class="flex items-center justify-start gap-3">
                    <div>
                        <DropDownList
                            :data-items="coursesList"
                            :text-field="'title'"
                            :data-item-key="'id'"
                            :valueField="'id'"
                            :valuePrimitive="true"
                            v-model="templateFilters.course"
                            :filterable="true"
                            :default-item="{ id: null, title: 'All Courses' }"
                            :popup-settings="popupSettings"
                            :value-render="'valueRender'"
                            @filterchange="filterChange($event)"
                        >
                            <template v-slot:valueRender="{ props }">
                                <span :class="props.class" :id="props.id">
                                    <span
                                        :class="
                                            props.value.title ? 'text-gray-700' : 'text-gray-500'
                                        "
                                    >
                                        {{ props.value.title ? props.value.title : 'All Courses' }}
                                    </span>
                                </span>
                            </template>
                        </DropDownList>
                    </div>
                    <div>
                        <DropDownList
                            :data-items="getStatusList"
                            :text-field="'text'"
                            :data-item-key="'id'"
                            :valueField="'id'"
                            :valuePrimitive="true"
                            v-model="templateFilters.status"
                            :filterable="false"
                            :default-item="{ id: null, text: 'All Status' }"
                            :popup-settings="popupSettings"
                            :value-render="'valueRender'"
                        >
                            <template v-slot:valueRender="{ props }">
                                <span :class="props.class" :id="props.id">
                                    <span
                                        :class="
                                            props.value.text ? 'text-gray-700' : 'text-gray-500'
                                        "
                                    >
                                        {{ props.value.text ? props.value.text : 'All Status' }}
                                    </span>
                                </span>
                            </template>
                        </DropDownList>
                    </div>
                </div>
                <div class="flex items-center justify-end gap-4">
                    <div class="flex h-9 items-center justify-start space-x-2">
                        <IconInput
                            v-model.lazy="templateFilters.search"
                            :pt="{ root: 'w-52' }"
                            placeholder="Search"
                            :debounce="300"
                            :autocomplete="'off'"
                        />
                    </div>
                    <Button size="sm" variant="primary" @click="AddNewTemplate">
                        <icon :name="'email'" :width="25" :height="25" :fill="'white'" />
                        <span>Add Course Template</span>
                    </Button>
                </div>
            </div>
            <template v-if="showDataGrid == 2">
                <div class="z-10 flex w-full flex-col rounded-lg bg-white">
                    <GridWrapper
                        :rounded="true"
                        :borderedCell="true"
                        :rowHover="true"
                        id="mark-attendance-grid"
                        ref="gridWrapper"
                    >
                        <Grid
                            ref="grid"
                            :data-items="templates.data"
                            :loader="'loaderTemplate'"
                            :columns="getGridColumns"
                            :resizable="true"
                            :pageable="templates.data.length > 0 ? pageable : false"
                            :skip="resource.state.filters.take * (resource.state.filters.page - 1)"
                            :take="resource.state.filters.take"
                            :total="templates.meta.total"
                            @pagechange="resource.onPageChange"
                        >
                            <GridNoRecords>
                                <EmptyState />
                            </GridNoRecords>
                            <template v-slot:loaderTemplate>
                                <TableLoader v-if="resource.state.loading || loading || isSaving" />
                            </template>
                            <template v-slot:sortableHeader="{ props }">
                                <div
                                    class="flex justify-between gap-2"
                                    :class="iscurrentSorting(props.field)"
                                    @click="handleSort(props.field)"
                                >
                                    <div class="text-gray-500">
                                        {{ props.title }}
                                    </div>
                                    <div>
                                        <icon :name="'chevron-up-down'" :width="16" :height="16" />
                                    </div>
                                </div>
                            </template>
                            <template #toggleCell="{ props }">
                                <toggle-cell v-bind:props="props" />
                            </template>
                            <template #defaultCell="{ props }">
                                <default-cell v-bind:props="props" />
                            </template>
                            <template v-slot:uppercaseHeader="{ props }">
                                <div class="uppercase text-gray-500">
                                    {{ props.title }}
                                </div>
                            </template>
                            <template v-slot:courseNameTemplate="{ props }">
                                <td
                                    class="!truncate"
                                    :class="props.class"
                                    :colspan="props.colspan"
                                    :role="props.role"
                                    :title="
                                        getCourseName(
                                            props.dataItem.course.course_code,
                                            props.dataItem.course.course_name
                                        )
                                    "
                                >
                                    {{
                                        getCourseName(
                                            props.dataItem.course.course_code,
                                            props.dataItem.course.course_name
                                        )
                                    }}
                                </td>
                            </template>
                            <template v-slot:actionsButtons="{ props }">
                                <td class="">
                                    <div class="flex justify-start space-x-2">
                                        <div
                                            @click="editTemplate(props.dataItem)"
                                            class="cursor-pointer"
                                        >
                                            <icon
                                                :name="'pencil'"
                                                :width="16"
                                                :height="16"
                                                :fill="'#1890FF'"
                                            />
                                        </div>
                                        <div
                                            @click="deleteTemplate(props.dataItem)"
                                            class="cursor-pointer"
                                        >
                                            <icon :name="'delete'" :width="16" :height="16" />
                                        </div>
                                    </div>
                                </td>
                            </template>
                            <template #actionCell="{ props }">
                                <action-cell
                                    v-bind:props="props"
                                    :width="300"
                                    :items="actionMenu"
                                    @itemClick="handleActionsClick($event, props.dataItem)"
                                >
                                </action-cell>
                            </template>
                        </Grid>
                    </GridWrapper>
                </div>
            </template>
            <template v-else-if="showDataGrid == 1">
                <div class="z-10 flex w-full flex-col rounded-lg bg-white p-4">
                    No data found; Try changing the search parameters
                </div>
            </template>
            <template v-else>
                <no-data>
                    <template #icon>
                        <span class="text-primary-blue-500">
                            <icon name="book" width="48" height="48" viewbox="0 0 48 48" />
                        </span>
                    </template>
                    <template #content>
                        <p class="text-sm leading-5 text-gray-700">
                            You have not added Course Template
                        </p>
                    </template>
                    <template #button>
                        <Button size="sm" variant="primary"> Add Course Template </Button>
                    </template>
                </no-data>
            </template>
        </div>
        <TemplateAddEditModal
            v-if="templateForm"
            :loading="loadingTemplate"
            :visible="templateForm"
            :course="currentcourse"
            :courses="getCoursesList"
            @close="closeForm"
            @saved="reloadGrid"
        />
        <!--
    <AddEditModal
      v-model:visible="options.addEditModalVisible"
      :model="options.editItem"
      @added="$resource.fetch()"
      @saved="$resource.fetch()"
    />
    -->
        <!-- <DialogModal :show="true" :closeable="true">
      <template v-slot:title>
        Hello
      </template>
    </DialogModal> -->
    </Layout>
</template>
<script>
import { h, onMounted, reactive, ref, watch } from 'vue';
import { Head, router, usePage } from '@inertiajs/vue3';
import { mapState } from 'pinia';
import PageTitleContent from '@spa/pages/Layouts/PageTitleContent.vue';
import Layout from '@spa/pages/Layouts/Layout.vue';
import { filterBy } from '@progress/kendo-data-query';
import { Grid, GridNoRecords } from '@progress/kendo-vue-grid';
import courseTemplateResource from '@spa/services/courseTemplateResource';
import { DropDownList } from '@progress/kendo-vue-dropdowns';
import NoData from '@spa/components/NoData/NoData.vue';
import Button from '@spa/components/Buttons/Button.vue';
import IconInput from '@spa/components/IconInput.vue';
import TemplateAddEditModal from './TemplateAddEditModal.vue';
import { useCoursesStore } from '@spa/stores/modules/courses';
import useConfirm from '@spa/services/useConfirm';
import ToggleCellTemplate from '@spa/components/KendoGrid/templates/ToggleCellTemplate.vue';
import DefaultCellTemplate from '@spa/components/KendoGrid/templates/DefaultCellTemplate.vue';
import ActionCellTemplate from '@spa/components/KendoGrid/templates/ActionCellTemplate.vue';
import TableLoader from '@spa/components/KendoGrid/TableLoader.vue';

export default {
    setup(props) {
        const defaultCourse = props.filters?.courses[0]?.id || null;
        const defaultStatus = props.filters?.status[0]?.id || null;
        const resource = courseTemplateResource({
            filters: {
                search: props.query?.search || '',
                course: props.query?.course || defaultCourse,
                status: props.query?.status || defaultStatus,
                take: props.query?.take || 10,
                page: props.query?.page || 1,
            },
            only: ['formSeeds', 'filterScopes', 'templates'],
        });

        watch(
            () => resource.state.filters,
            (val) => {
                resource.fetch();
            },
            { deep: true }
        );
        const confirm = useConfirm();
        const deleteTemplate = (item) => {
            const templateId = item.id || null;
            resource.onRemove(templateId, 'Are you sure you want to remove selected template?');
        };
        return {
            resource,
            deleteTemplate,
        };
    },
    props: {
        user: Object,
        query: Object,
        templates: Object,
        currentcourse: Object,
        formSeeds: { type: [Array, Object], default: [] },
        filterScopes: Object,
    },
    components: {
        Layout,
        Head,
        PageTitleContent,
        'no-data': NoData,
        Button,
        IconInput,
        Grid,
        GridNoRecords,
        TemplateAddEditModal,
        DropDownList,
        'toggle-cell': ToggleCellTemplate,
        'default-cell': DefaultCellTemplate,
        'action-cell': ActionCellTemplate,
        TableLoader,
    },
    mounted() {
        this.gridWidth = this.$refs.gridWrapper ? this.$refs.gridWrapper.offsetWidth : null;
        this.setInits(this.formSeeds);
        this.coursesList = this.formSeeds?.courses || [];
        this.setCurrentConsumer('templates');
    },
    data: function () {
        return {
            gridWidth: 0,
            templateForm: false,
            addingUnits: false,
            loadingTemplate: false,
            coursesList: [],
            popupSettings: {
                className: 'tw-width-auto',
                animate: false,
            },
            getStatusList: [
                { id: 'active', text: 'Active Templates' },
                { id: 'inactiveactive', text: 'Inactive Templates' },
            ],
            actionMenu: [
                { text: 'Edit', value: 'edit_course_template', icon: 'edit' },
                {
                    text: 'Delete',
                    value: 'delete_course_template',
                    icon: 'delete',
                },
            ],
            templateFilters: {
                course: this.resource.state.filters.course,
                status: this.resource.state.filters.status,
                search: this.resource.state.filters.search,
            },
        };
    },
    computed: {
        ...mapState(useCoursesStore, [
            'setCurrentCourse',
            'setCurrentConsumer',
            'setInits',
            'formInits',
            'selectedUnits',
            'courseSubjects',
            'setUnits',
            'setUnitVariables',
            'setCourseSubjects',
            'setTemplates',
            'setCurrentTemplate',
            'allTemplates',
            'currentTemplate',
        ]),
        showDataGrid() {
            const hasData = this.templates.data && this.templates.data.length > 0;
            const isFiltered =
                this.resource.state.filters.course || this.resource.state.filters.status;
            if (hasData) {
                return 2;
            }
            if (isFiltered) {
                return 1;
            }
            return 0;
        },
        getGridColumns() {
            const fixedColumns = [
                {
                    title: 'Major/Template Name',
                    field: 'template_name',
                    headerCell: 'sortableHeader',
                    minResizableWidth: '200px',
                    width: this.setPercentage(25),
                    cell: 'defaultCell',
                },
                {
                    title: 'Course Name',
                    field: 'course_id',
                    headerCell: 'sortableHeader',
                    width: '80px',
                    width: this.setPercentage(25),
                    cell: 'courseNameTemplate',
                },
                {
                    title: 'Core Units No',
                    field: 'no_of_core_subject',
                    headerCell: 'sortableHeader',
                    width: '80px',
                    width: this.setPercentage(10),
                },
                {
                    title: 'Elective Units No',
                    field: 'no_of_elective_subject',
                    headerCell: 'sortableHeader',
                    width: this.setPercentage(10),
                },
                {
                    title: 'Default',
                    field: 'set_default',
                    headerCell: 'uppercaseHeader',
                    width: this.setPercentage(10),
                    cell: 'toggleCell',
                },
                {
                    title: 'Active',
                    field: 'set_active',
                    headerCell: 'uppercaseHeader',
                    width: this.setPercentage(10),
                    cell: 'toggleCell',
                },
                {
                    title: 'Actions',
                    field: 'id',
                    headerCell: 'uppercaseHeader',
                    width: this.setPercentage(10),
                    cell: 'actionCell',
                },
            ];
            return fixedColumns;
        },
        pageable() {
            return {
                buttonCount: 5,
                info: false,
                type: 'numeric',
                pageSizes: [10, 15, 20, 50, 100],
                previousNext: true,
                pageSizeValue: this.resource.state.pageable.pageSizeValue || 10,
            };
        },
        getCoursesList() {
            return this.filterScopes.courses || [];
        },
        computedTemplateFilters() {
            return this.templateFilters;
        },
    },
    methods: {
        openAddUnitWindow() {
            this.addingUnits = true;
        },
        iscurrentSorting(field) {
            return null;
        },
        handleSort() {
            return;
        },
        setPercentage(percentage) {
            return Math.round(this.gridWidth / 100) * percentage;
        },
        AddNewTemplate() {
            this.setUnits({});
            this.setCourseSubjects({});
            this.setCurrentTemplate({});
            this.templateForm = true;
        },
        editTemplate(item) {
            this.loadTemplateDetails(item);
            this.templateForm = true;
        },
        loadTemplateDetails(template) {
            const templateId = template.id || 0;
            this.loadingTemplate = true;
            $http
                .get(route('spa.coursetemplates.details', [templateId]))
                .then((resp) => {
                    if (resp['success']) {
                        this.setUnitVariables(resp.data.units);
                        this.setCurrentTemplate(resp.data.template);
                        this.setCurrentCourse(resp.data.template.course);
                    }
                    this.loadingTemplate = false;
                })
                .catch((error) => {
                    //console.log(error);
                    console.log('Error while loading template.');
                })
                .finally(() => {
                    this.loadingTemplate = false;
                });
        },
        closeForm() {
            this.setUnits({});
            this.setCourseSubjects({});
            this.setCurrentTemplate({});
            this.templateForm = false;
            this.loadingTemplate = false;
        },
        reloadGrid() {
            this.resource.fetch();
            return;
        },
        filterChange(event) {
            this.coursesList = filterBy(this.formInits.courses.slice(), event.filter);
        },
        getCourseName(code, name) {
            return `${code} - ${name}`;
        },
        handleActionsClick(e, dataItem) {
            switch (e.target.closest('[data-item]').dataset.item) {
                case 'edit_course_template':
                    this.editTemplate(dataItem);
                    break;
                case 'delete_course_template':
                    this.deleteTemplate(dataItem);
                    break;
            }
        },
        pageChangeHandler: function (event) {
            const page = parseInt(event.page.skip / event.page.take);
            this.resource.state.filters.page = isNaN(page) ? 1 : page + 1;
            this.resource.state.filters.take = event.page.take;
            return true;
        },
        updateFilters() {
            this.resource.state.filters.page = 1;
            this.resource.state.filters.course = this.templateFilters.course || null;
            this.resource.state.filters.status = this.templateFilters.status || null;
            this.resource.state.filters.search = this.templateFilters.search || null;
        },
    },
    watch: {
        formSeeds: {
            handler(newval) {
                this.setInits(newval);
                this.coursesList = newval?.courses;
            },
            deep: true,
        },
        'templateFilters.course': function (newVal, oldVal) {
            this.updateFilters();
        },
        'templateFilters.status': function (newVal, oldVal) {
            this.updateFilters();
        },
        'templateFilters.search': function (newVal, oldVal) {
            this.updateFilters();
        },
    },
};
</script>
