<template>
    <div class="new-header__fixed space-y-8 overflow-y-auto bg-white p-12">
        <div class="tw-header__wrapper space-y-4">
            <h1 class="text-5xl font-bold text-gray-700">Kitchen Sink</h1>
        </div>
        <div class="space-y-12">
            <AsyncDataView :dataItems="dataItems" :paginator="paginator" :show-header="true">
                <template #list="{ items }">
                    <div class="space-y-4">
                        <Card v-for="item in items" :key="item.id">
                            <template #content>
                                {{ item.name }}
                            </template>
                        </Card>
                    </div>
                </template>
                <template #grid="{ items }">
                    <div class="grid grid-cols-3 gap-4">
                        <Card v-for="item in items" :key="item.id">
                            <template #content>
                                {{ item.name }}
                            </template>
                        </Card>
                    </div>
                </template>
            </AsyncDataView>
            <!-- <ks-multiple-dropdown />
            <ks-phone-masked-input />
            <ks-action-tooltip />
            <ks-upload />
            <ks-avatar />
            <ks-icons />
            <ks-date-time-picker />
            <ks-notification />
            <ks-loader />
            <ks-switch />
            <ks-highlght-box />
            <ks-badge />
            <ks-manage-columns />
            <ks-grid-table />
            <ks-grid-template />
            <ks-nodata />
            <ks-buttons />
            <ks-icon-input />
            <ks-sidebar-drawer />
            <ks-dropdown-button />
            <ks-dropdown />
            <ks-dialog />
            <ks-checkbox />
            <ks-tabstrip />
            <ks-toggle />
            <ks-splitter />
            <ks-svg />
            <ks-mail-sms />
            <ks-copy />
            <ks-animation />
            <ks-card />
            <ks-label-value /> -->
        </div>
    </div>
</template>
<script>
// import KitchenButtons from "@spa/components/KitchenSink/KitchenButtons";
// import KitchenSearchInput from "@spa/components/KitchenSink/KitchenSearchInput.vue";
// import KitchenActionTooltip from "@spa/components/KitchenSink/KitchenActionTooltip.vue";
// import KitchenSidebarDrawer from "../../components/KitchenSink/KitchenSidebarDrawer.vue";
// import KitchenDropdownButton from "../../components/KitchenSink/KitchenDropdownButton.vue";
// import KitchenDropdown from "../../components/KitchenSink/KitchenDropdown.vue";
// import KitchenDialog from "../../components/KitchenSink/KitchenDialog.vue";
// import KitchenCheckbox from "../../components/KitchenSink/KitchenCheckbox.vue";
// import KitchenTabStrip from "../../components/KitchenSink/KitchenTabStrip.vue";
// import KitchenToggle from "../../components/KitchenSink/KitchenToggle";
// import KitchenSplitter from "../../components/KitchenSink/KitchenSplitter.vue";
// import KitchenSvg from "../../components/KitchenSink/KitchenSvg.vue";
// import KitchenMailSms from "../../components/KitchenSink/KitchenMailSms.vue";
// import KitchenCopy from "../../components/KitchenSink/KitchenCopy.vue";
// import KitchenNoData from "../../components/KitchenSink/KitchenNoData";
// import KitchenGridTable from "../../components/KitchenSink/KitchenGridTable.vue";
// import KitchenManageColumns from "../../components/KitchenSink/KitchenManageColumns.vue";
// import KithenAnimation from "../../components/KitchenSink/KithenAnimation.vue";
// import KitchenCards from "../../components/KitchenSink/KitchenCards.vue";
// import KitchenGridTemplates from "../../components/KitchenSink/KitchenGridTemplates.vue";
// import KitchenBadgeVue from "../../components/KitchenSink/KitchenBadge.vue";
// import KitchenHighlightBoxVue from "../../components/KitchenSink/KitchenHighlightBox.vue";
// import KitchenSwitchVue from "../../components/KitchenSink/KitchenSwitch.vue";
// import KitchenLoader from "../../components/KitchenSink/KitchenLoader.vue";
// import KitchenLabelValue from "../../components/KitchenSink/KitchenLabelValue.vue";
// import KitchenNotification from "../../components/KitchenSink/KitchenNotification.vue";
// import KitchenDateTimePicker from "../../components/KitchenSink/KitchenDateTimePicker.vue";
// import KitchenIcons from "../../components/KitchenSink/KitchenIcons.vue";
// import KitchenAvatar from "../../components/KitchenSink/KitchenAvatar.vue";
// import KitchenUpload from "../../components/KitchenSink/KitchenUpload.vue";
// import KitchenPhoneMaskedInput from "@spa/components/KitchenSink/KitchenPhoneMaskedInput.vue";
// import KitchenMultipleDropdown from "@spa/components/KitchenSink/KitchenMultipleDropdown.vue";
import AsyncDataView from '@spa/components/AsyncComponents/DataView/AsyncDataView.vue';
import Card from '@spa/components/Card/Card.vue';

export default {
    components: {
        AsyncDataView,
        Card,
        // "ks-manage-columns": KitchenManageColumns,
        // "ks-buttons": KitchenButtons,
        // "ks-icon-input": KitchenSearchInput,
        // "ks-sidebar-drawer": KitchenSidebarDrawer,
        // "ks-dropdown-button": KitchenDropdownButton,
        // "ks-dropdown": KitchenDropdown,
        // "ks-dialog": KitchenDialog,
        // "ks-checkbox": KitchenCheckbox,
        // "ks-tabstrip": KitchenTabStrip,
        // "ks-toggle": KitchenToggle,
        // "ks-splitter": KitchenSplitter,
        // "ks-svg": KitchenSvg,
        // "ks-mail-sms": KitchenMailSms,
        // "ks-copy": KitchenCopy,
        // "ks-nodata": KitchenNoData,
        // "ks-grid-table": KitchenGridTable,
        // "ks-animation": KithenAnimation,
        // "ks-card": KitchenCards,
        // "ks-grid-template": KitchenGridTemplates,
        // "ks-badge": KitchenBadgeVue,
        // "ks-highlght-box": KitchenHighlightBoxVue,
        // "ks-switch": KitchenSwitchVue,
        // "ks-loader": KitchenLoader,
        // "ks-label-value": KitchenLabelValue,
        // "ks-notification": KitchenNotification,
        // "ks-date-time-picker": KitchenDateTimePicker,
        // "ks-icons": KitchenIcons,
        // "ks-avatar": KitchenAvatar,
        // "ks-upload": KitchenUpload,
        // "ks-action-tooltip": KitchenActionTooltip,
        // "ks-phone-masked-input": KitchenPhoneMaskedInput,
        // "ks-multiple-dropdown": KitchenMultipleDropdown,
    },
    data() {
        return {
            dataItems: [
                {
                    id: 1,
                    name: 'Item 1',
                },
                {
                    id: 2,
                    name: 'Item 2',
                },
                {
                    id: 3,
                    name: 'Item 3',
                },
                {
                    id: 4,
                    name: 'Item 4',
                },
                {
                    id: 5,
                    name: 'Item 5',
                },
                {
                    id: 6,
                    name: 'Item 6',
                },
                {
                    id: 7,
                    name: 'Item 7',
                },
                {
                    id: 8,
                    name: 'Item 8',
                },
                {
                    id: 9,
                    name: 'Item 9',
                },
                {
                    id: 10,
                    name: 'Item 10',
                },
                {
                    id: 11,
                    name: 'Item 11',
                },
                {
                    id: 12,
                    name: 'Item 12',
                },
                {
                    id: 13,
                    name: 'Item 13',
                },
                {
                    id: 14,
                    name: 'Item 14',
                },
                {
                    id: 15,
                    name: 'Item 15',
                },
            ],
            paginator: {
                skip: 0,
                take: 10,
                total: 15,
                butttonCount: 5,
                info: true,
                type: 'numeric',
                pageSizes: true,
                previousNext: true,
                pageSizeDefs: [10, 20, 50, 100, 150, 200],
            },
        };
    },
};
</script>
<style lang=""></style>
