<template lang="">
    <Layout :noSpacing="true">
        <template v-slot:pageTitleContent>
            <PageTitleContent :title="'Notifications'" :back="false" />
        </template>
        <div class="space-y-6 px-4 py-6 md:px-8">
            <HeaderTab
                ref="headerTabRef"
                :filters="resetFiltersData"
                :hastextsearch="false"
                @filter="updateFilter"
                @reset="handleResetFilters"
                :width="'w-[300px]'"
                :dateRangeWidth="'w-[300px]'"
            />
            <div class="overflow-hidden rounded-lg bg-white shadow-md">
                <div
                    class="flex items-center justify-between border-b border-gray-200 px-4 py-4 sm:px-6"
                    v-if="unreadNotificsations.length > 0"
                >
                    <div class="flex items-center">
                        <input
                            type="checkbox"
                            id="select-all"
                            class="k-checkbox"
                            :checked="allSelected"
                            @change="toggleAllNotifications"
                        />
                        <label for="select-all" class="ml-2 text-sm text-gray-700"
                            >Select All</label
                        >
                    </div>

                    <div class="flex space-x-3">
                        <Button
                            class="disabled:cursor-not-allowed disabled:opacity-50"
                            @click="markSelectedAsRead"
                            :disabled="selectedNotifications.length === 0"
                            :size="'sm'"
                            :variant="'link'"
                        >
                            <icon name="tickmark" :width="20" :height="20" :fill="'currentColor'" />

                            <span> Mark selected as read</span></Button
                        >
                    </div>
                </div>

                <div class="divide-y divide-gray-200">
                    <!-- <template #loaderTemplate>
                    <TableLoader v-if="loaderStore.contextLoaders['grid-loader']" />
                 </template> -->
                    <GlobalContextLoader context="notification-grid" :overlay="true">
                        <template v-if="Object.keys(groupedNotifications).length > 0">
                            <div
                                v-for="(notifications, date) in groupedNotifications"
                                :key="date"
                                class="border-b border-gray-200 p-4"
                            >
                                <h2 class="mb-2 bg-white py-1 text-sm font-medium text-gray-500">
                                    {{ date }}
                                </h2>

                                <div class="space-y-4">
                                    <div
                                        v-for="notification in notifications"
                                        :key="notification.id"
                                        :class="[
                                            notification.is_read ? 'bg-white' : 'bg-bluegray-50',
                                            'relative rounded-lg border border-gray-200 p-4',
                                        ]"
                                    >
                                        <div class="flex items-start space-x-4">
                                            <div
                                                v-if="!notification.is_read"
                                                class="flex-shrink-0 self-center"
                                            >
                                                <input
                                                    type="checkbox"
                                                    :id="`notification-${notification.id}`"
                                                    :value="notification.id"
                                                    v-model="selectedNotifications"
                                                    class="k-checkbox"
                                                />
                                            </div>

                                            <div class="flex-shrink-0">
                                                <div
                                                    v-html="notification.icon"
                                                    :class="[
                                                        notification.bg_class || 'bg-gray-500',
                                                        'flex h-10 w-10 items-center justify-center rounded-full shadow-sm',
                                                    ]"
                                                ></div>
                                            </div>

                                            <div class="min-w-0 flex-1">
                                                <div
                                                    class="text-sm text-gray-900"
                                                    v-html="notification.message"
                                                ></div>

                                                <div
                                                    class="mt-1 flex items-center text-xs text-gray-500"
                                                >
                                                    <span>{{ notification.formatted_time }}</span>

                                                    <span
                                                        v-if="!notification.is_read"
                                                        class="ml-2 inline-flex items-center rounded-full bg-blue-100 px-2 py-0.5 text-xs font-medium text-blue-800"
                                                    >
                                                        New
                                                    </span>
                                                </div>
                                            </div>

                                            <div class="flex flex-shrink-0 self-center">
                                                <Button
                                                    v-if="!notification.is_read"
                                                    @click="markAsRead(notification.id)"
                                                    variant="link"
                                                >
                                                    <span
                                                        v-if="loadingNotifications[notification.id]"
                                                    >
                                                        <i class="fas fa-spinner fa-spin mr-1"></i
                                                        >Loading...
                                                    </span>
                                                    <span v-else>Mark as read</span>
                                                </Button>

                                                <a
                                                    v-if="
                                                        notification.action_url &&
                                                        notification.action_url !== '#'
                                                    "
                                                    :href="notification.action_url"
                                                    class="ml-4 text-sm text-gray-600 hover:text-gray-800"
                                                >
                                                    View details
                                                </a>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </template>
                        <template v-else>
                            <div class="p-12 text-center">
                                <div
                                    class="mb-4 inline-block rounded-full bg-gray-100 p-6 text-gray-500"
                                >
                                    <i class="far fa-bell-slash text-5xl"></i>
                                </div>
                                <p class="text-xl font-medium text-gray-500">
                                    No notifications found
                                </p>
                                <p class="mt-2 text-gray-400">
                                    <template v-if="filters.status === 'unread'">
                                        You don't have any unread notifications
                                    </template>
                                    <template v-else-if="filters.status === 'read'">
                                        You don't have any read notifications
                                    </template>
                                    <template v-else-if="filters.startDate || filters.endDate">
                                        No notifications found in the selected date range
                                    </template>
                                    <template v-else>
                                        When you receive notifications, they will appear here
                                    </template>
                                </p>
                            </div>
                        </template>
                    </GlobalContextLoader>
                </div>
            </div>

            <!-- Pagination -->
            <div v-if="Object.keys(groupedNotifications).length > 0" class="mt-6">
                <div class="course-pagination tw-pager my-4">
                    <Pager
                        :take="pageable.pageSizeValue"
                        :total="pageable.totalItems"
                        @changedPage="handlePageChange"
                        @changedPageSize="handlePagesizeChange"
                        :button-count="pageable.buttonCount"
                        :info="pageable.info"
                        :previous-next="true"
                        :type="'numeric'"
                        :page-sizes="pageable.pageSizes"
                        :pager-render="'myTemplate'"
                        :responsive="false"
                    >
                        <template v-slot:myTemplate="{ props }">
                            <PagerTemplate
                                :current-page="props.current"
                                :page-size="props.perpage"
                                :take="props.take"
                                :size-defs="props.pageSizes"
                                :total-pages="pageable.totalPages"
                                :total-items="props.total"
                            />
                        </template>
                    </Pager>
                </div>
            </div>
        </div>
    </Layout>
</template>

<script setup>
import { ref, reactive, computed, onMounted, watch, nextTick } from 'vue';
import apiClient from '@spa/services/api.client';
import Layout from '@spa/pages/Layouts/Layout.vue';
import PageTitleContent from '@spa/pages/Layouts/PageTitleContent.vue';
import { useLoaderStore } from '@spa/stores/modules/global-loader';
import GlobalContextLoader from '@spa/components/Loader/GlobalContextLoader.vue';
import PagerTemplate from '@spa/components/ListViewPagination.vue';
import { Pager } from '@progress/kendo-vue-data-tools';
import HeaderTab from '@agentportal/payments/partials/HeaderTab.vue';
import Button from '@spa/components/Buttons/Button.vue';

const props = defineProps({
    filtersData: {
        type: Object,
        required: false,
        default: () => ({}),
    },
    responseData: {
        type: Object,
        required: false,
        default: () => ({}),
    },
});

// Store
const loaderStore = useLoaderStore();

// State
const notifications = ref([]);
const groupedNotifications = ref({});
const pageable = reactive({
    totalItems: 0,
    totalPages: 0,
    currentPage: 1,
    buttonCount: 5,
    info: true,
    type: 'numeric',
    pageSizes: [15, 20, 50, 100],
    previousNext: true,
    pageSizeValue: 15,
});
const filters = ref({
    status: 'all',
    startDate: null,
    endDate: null,
    perPage: 15,
});
const counts = ref({
    unread: 0,
    total: 0,
});
const selectedNotifications = ref([]);
const localFilters = reactive({ ...filters.value });
const isMarkingRead = ref(false);
const loadingNotifications = ref({});

// Header tab reference and reset functionality
const headerTabRef = ref(null);
const resetTrigger = ref(0);

// Create reactive filters data that can be reset
const resetFiltersData = computed(() => {
    // Force reactivity by including resetTrigger
    resetTrigger.value;

    // Create a deep copy of filtersData with reset values when needed
    if (!props.filtersData || Object.keys(props.filtersData).length === 0) {
        return {};
    }

    const resetData = JSON.parse(JSON.stringify(props.filtersData));

    // Apply reset values to the filters
    if (Array.isArray(resetData)) {
        resetData.forEach((filter) => {
            if (filter.type === 'radio' && filter.field === 'status') {
                filter.trace = localFilters.status || 'all';
            } else if (filter.type === 'daterange') {
                filter.trace = {
                    start: localFilters.startDate || null,
                    end: localFilters.endDate || null,
                };
            }
        });
    }

    return resetData;
});

// Computed
const allSelected = computed(() => {
    const unreadNotifications = notifications.value.filter((n) => !n.is_read);
    return (
        unreadNotifications.length > 0 &&
        selectedNotifications.value.length === unreadNotifications.length
    );
});

// const getFilters = computed(() => {
//     return this.filtersData;
// });

const getSearchText = computed(() => {
    return '';
});

const getActionBtns = computed(() => {
    return [
        {
            label: 'Reset Filters',
            action: 'reset',
            icon: 'fas fa-undo',
            variant: 'secondary',
        },
    ];
});
// Methods
function toggleAllNotifications() {
    if (allSelected.value) {
        selectedNotifications.value = [];
    } else {
        selectedNotifications.value = notifications.value
            .filter((notification) => !notification.is_read)
            .map((notification) => notification.id);
    }
}

const updateFilter = (filters) => {
    console.log('updateFilter called with:', filters);

    // Update local filters based on the received filter values
    if (filters.status !== undefined) {
        localFilters.status = filters.status || 'all';
    }

    // Handle date range filters - the service uses start_date and end_date as field names
    if (filters.start_date !== undefined) {
        localFilters.startDate = filters.start_date;
    }

    if (filters.end_date !== undefined) {
        localFilters.endDate = filters.end_date;
    }

    // Legacy support for start/end naming (if used elsewhere)
    if (filters.start !== undefined) {
        localFilters.startDate = filters.start;
    }

    if (filters.end !== undefined) {
        localFilters.endDate = filters.end;
    }

    // Reset to page 1 when filters change
    localFilters.page = 1;
    pageable.currentPage = 1;

    console.log('Updated localFilters:', { ...localFilters });

    // Apply the filters by fetching data with the updated filters
    fetchNotifications();
};

// Function to fetch notifications with current filters
const fetchNotifications = () => {
    // Show loading state
    loaderStore.startContextLoading('notification-grid');

    // Prepare filter parameters for the API request
    const params = {
        status: localFilters.status,
        start_date: localFilters.startDate,
        end_date: localFilters.endDate,
        page: pageable.currentPage || 1,
        per_page: pageable.pageSizeValue || 15,
    };

    // Make API request to get filtered notifications
    apiClient
        .get(route('spa.notifications.index'), { params })
        .then((response) => {
            // Update notifications data with the response
            notifications.value = response.notifications;
            groupedNotifications.value = response.groupedNotifications;

            // Update pagination data
            if (response.pagination) {
                updatePagination(response.pagination);
            }

            // Update counts
            counts.value = response.counts;

            // Reset selected notifications when filters change
            selectedNotifications.value = [];
        })
        .catch((error) => {
            console.error('Error fetching notifications:', error);
        })
        .finally(() => {
            if (window.Livewire) {
                window.Livewire.dispatch('refreshNotificationCount');
            }

            loaderStore.stopContextLoading('notification-grid');
        });
};

const unreadNotificsations = computed(() => {
    return notifications.value.filter((notification) => !notification.is_read);
});

function updatePagination(paginationData) {
    if (!paginationData) return;

    const currentPage = parseInt(paginationData.current_page || 1);
    const perPage = parseInt(paginationData.per_page || 15);

    pageable.totalItems = paginationData.total || 0;
    pageable.totalPages = paginationData.last_page || 0;
    pageable.currentPage = currentPage;
    pageable.pageSizeValue = perPage;

    // Update page sizes list if needed
    pageable.pageSizes = getPageSizes(pageable.totalItems);
}

function getPageSizes(totalItems) {
    const pageSizes = [15, 20, 50, 100];
    if (totalItems <= 15) return [15];
    if (totalItems <= 20) return [15, 20];
    if (totalItems <= 50) return [15, 20, 50];
    return pageSizes;
}

function handlePageChange(event) {
    pageable.currentPage = event;
    fetchNotifications();
    // Scroll to top of the page for better UX
    window.scrollTo({
        top: 0,
        behavior: 'smooth',
    });
}

function handlePagesizeChange(event) {
    pageable.pageSizeValue = event;
    pageable.currentPage = 1;
    fetchNotifications();
}

async function markSelectedAsRead() {
    if (selectedNotifications.value.length === 0) return;

    try {
        // Create form data for the request
        const formData = new FormData();
        selectedNotifications.value.forEach((id) => {
            formData.append('notification_ids[]', id);
        });

        // Make the API request
        const response = await apiClient.post('spa/notifications/mark-selected-as-read', formData);

        if (response.success) {
            // Show success message
            if (window.Fire) {
                window.Fire.emit('axiosResponseSuccess', {
                    message: 'Notifications marked as read',
                });
            }

            // Refresh notifications and clear selection
            await fetchNotifications();
            selectedNotifications.value = [];
        }
    } catch (error) {
        console.error('Error marking notifications as read:', error);
        // Error handling is done by apiClient
    }
}

async function markAllAsRead() {
    isMarkingRead.value = true;

    try {
        // Make the API request
        const response = await apiClient.post('spa/notifications/mark-all-as-read');

        if (response.success) {
            // Show success message
            if (window.Fire) {
                window.Fire.emit('axiosResponseSuccess', {
                    message: 'All notifications marked as read',
                });
            }

            // Refresh notifications
            await fetchNotifications();
        }
    } catch (error) {
        console.error('Error marking all notifications as read:', error);
        // Error handling is done by apiClient
    }
    isMarkingRead.value = false;
}

async function markAsRead(id) {
    // Set loading state for this specific notification
    loadingNotifications.value[id] = true;

    try {
        // Make the API request
        const response = await apiClient.post(`spa/notifications/${id}/read`);

        if (response.success) {
            // Show success message
            if (window.Fire) {
                window.Fire.emit('axiosResponseSuccess', {
                    message: 'Notification marked as read',
                });
            }

            // Refresh notifications
            await fetchNotifications();
        }
    } catch (error) {
        console.error('Error marking notification as read:', error);
        // Error handling is done by apiClient
    } finally {
        // Clear loading state for this specific notification
        loadingNotifications.value[id] = false;
    }
}

function applyFilters() {
    // Reset to first page when applying filters
    pageable.currentPage = 1;
    fetchNotifications();
}

// Handle reset from HeaderTab component
function handleResetFilters() {
    console.log('Reset filters triggered from HeaderTab');

    // Reset all filter values to their defaults
    localFilters.status = 'all';
    localFilters.startDate = null;
    localFilters.endDate = null;
    localFilters.page = 1;

    // Reset pagination to defaults
    pageable.pageSizeValue = 15;
    pageable.currentPage = 1;

    // Clear selected notifications
    selectedNotifications.value = [];

    // Trigger reactivity update for HeaderTab
    resetTrigger.value++;

    // Force HeaderTab to update its visual state
    nextTick(() => {
        if (headerTabRef.value) {
            // Force the HeaderTab component to re-render with reset values
            headerTabRef.value.$forceUpdate();
        }

        // Apply the reset filters by fetching data
        fetchNotifications();
    });
}

// Legacy function for backward compatibility
function resetFilters() {
    handleResetFilters();
}

// Lifecycle hooks
onMounted(() => {
    fetchNotifications();
});
</script>

<style lang=""></style>
