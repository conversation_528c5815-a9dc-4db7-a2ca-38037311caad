<template>
    <form-element class="flex flex-col items-center justify-center space-y-6">
        <fieldset class="k-form-fieldset space-y-6">
            <div class="mb-3">
                <field
                    :id="'data_type'"
                    :name="'data_type'"
                    :label="'Data Type:'"
                    :component="'myTemplate'"
                    :text-field="'label'"
                    :data-item-key="'value'"
                    :valueField="'value'"
                    :valuePrimitive="true"
                    :default-item="dataType[0]"
                    :data-items="dataType"
                    :pt="getFieldClass"
                    :orientation="'horizontal'"
                >
                    <template v-slot:myTemplate="{ props }">
                        <FormDropDown
                            v-bind="props"
                            @customEvent="handleFilterUpdate"
                            @blur="props.onBlur"
                            @focus="props.onFocus"
                        />
                    </template>
                </field>
            </div>
            <div class="mb-3">
                <field
                    :id="'offshore_flag'"
                    :name="'offshore_flag'"
                    :label="'Offshore Flag:'"
                    :component="'myTemplate'"
                    :text-field="'label'"
                    :data-item-key="'value'"
                    :valueField="'value'"
                    :valuePrimitive="true"
                    :default-item="offshoreFlag[0]"
                    :data-items="offshoreFlag"
                    :pt="getFieldClass"
                    :orientation="'horizontal'"
                >
                    <template v-slot:myTemplate="{ props }">
                        <FormDropDown
                            v-bind="props"
                            @customEvent="handleFilterUpdate"
                            @blur="props.onBlur"
                            @focus="props.onFocus"
                        />
                    </template>
                </field>
            </div>
            <div class="mb-3">
                <field
                    :id="'year'"
                    :name="'year'"
                    :label="'Year:'"
                    :component="'myTemplate'"
                    :text-field="'label'"
                    :data-item-key="'value'"
                    :valueField="'value'"
                    :valuePrimitive="true"
                    :default-item="year[0]"
                    :data-items="year"
                    :pt="getFieldClass"
                    :orientation="'horizontal'"
                >
                    <template v-slot:myTemplate="{ props }">
                        <FormDropDown
                            v-bind="props"
                            @customEvent="handleFilterUpdate"
                            @blur="props.onBlur"
                            @focus="props.onFocus"
                        />
                    </template>
                </field>
            </div>
        </fieldset>
        <div class="k-form-buttons">
            <kbutton variant="primary" :class="'h-9 min-w-[100px]'" :type="'submit'">
                View
            </kbutton>
        </div>
    </form-element>
</template>
<script>
import { Field, FormElement } from '@progress/kendo-vue-form';
import FormDropDown from '@spa/components/KendoInputs/FormDropDown.vue';
import Button from '@spa/components/Buttons/Button.vue';
import { requiredtrue } from '@spa/services/validators/kendoCommonValidator.js';
import FileUploader from '@spa/components/Uploader/FileUploader.vue';

export default {
    props: {
        data: {
            type: Object,
            default: {},
        },
        dataType: {
            type: Array,
            default: [],
        },
        year: {
            type: Array,
            default: [],
        },
        offshoreFlag: {
            type: Array,
            default: [],
        },
    },
    components: {
        field: Field,
        'form-element': FormElement,
        kbutton: Button,
        FormDropDown,
        FileUploader,
    },
    computed: {
        getFieldClass() {
            return {
                label: 'text-sm leading-7 text-gray-700 w-32 shrink-0 grow-1 !font-normal justify-end',
                root: 'w-full !flex items-center gap-4',
                wrap: 'w-full',
                field: '!flex !flex-row !gap-6 !w-96',
            };
        },
    },
    methods: {
        requiredtrue,
        handleFilterUpdate(values) {
            this.$emit('onChnageFilter', values);
        },
    },
};
</script>
