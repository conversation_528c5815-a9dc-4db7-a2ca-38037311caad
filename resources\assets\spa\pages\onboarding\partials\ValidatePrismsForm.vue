<template>
    <form-element class="flex flex-col items-center justify-center space-y-6">
        <fieldset class="k-form-fieldset space-y-6">
            <div class="mb-3 flex items-center gap-4">
                <div class="min-w-32 text-gray-700">
                    Upload New File :
                    <span class="text-sm text-red-500">*</span>
                </div>
                <field
                    :id="'upload_new_file'"
                    :name="'upload_new_file'"
                    :component="'myTemplate'"
                    :label="'Upload New File:'"
                    :placeholder="'Upload New File'"
                    :orientation="'horizontal'"
                    :pt="getFieldClass"
                    :showValidationMessage="true"
                    :indicaterequired="true"
                    :data-items="accountType"
                    :text-field="'label'"
                    :data-item-key="'value'"
                    :default-item="accountType[0]"
                    :class="'prismsfile-uploader'"
                >
                    <template v-slot:myTemplate="{ props }">
                        <FileUploader
                            v-bind="props"
                            @change="props.onChange"
                            @blur="props.onBlur"
                            @focus="props.onFocus"
                            :restrictions="{
                                allowedExtensions: ['.pdf', '.docx'],
                                maxFileSize: 1000000,
                            }"
                            :buttonLabel="'Upload File'"
                            :isCustomUploader="false"
                            :isPadding="true"
                            :iconName="'download_arrow_up'"
                            :isAgencyUploadForm="true"
                            :class="'w-full'"
                        />
                    </template>
                </field>
            </div>
            <div class="mb-3 flex items-center gap-4">
                <field
                    :id="'existing_file'"
                    :name="'existing_file'"
                    :component="'myTemplate'"
                    :label="'Select Exising File:'"
                    :placeholder="'Select Exising File'"
                    :orientation="'horizontal'"
                    :pt="getFieldClass"
                    :showValidationMessage="true"
                    :indicaterequired="true"
                    :data-items="accountType"
                    :text-field="'label'"
                    :data-item-key="'value'"
                    :default-item="accountType[0]"
                >
                    <template v-slot:myTemplate="{ props }">
                        <FormDropDown
                            v-bind="props"
                            @change="handleFilterUpdate($event)"
                            @blur="props.onBlur"
                            @focus="props.onFocus"
                        />
                    </template>
                </field>
                <div class="k-form-buttons">
                    <kbutton variant="primary" :class="'h-9 min-w-[100px]'" :type="'submit'">
                        Compare
                    </kbutton>
                </div>
            </div>
        </fieldset>
    </form-element>
</template>
<script>
import { Field, FormElement } from '@progress/kendo-vue-form';
import FormDropDown from '@spa/components/KendoInputs/FormDropDown.vue';
import Button from '@spa/components/Buttons/Button.vue';
import { requiredtrue } from '@spa/services/validators/kendoCommonValidator.js';
import FileUploader from '@spa/components/Uploader/FileUploader.vue';

export default {
    props: {
        data: {
            type: Object,
            default: {},
        },
        accountType: {
            type: Array,
            default: [],
        },
    },
    components: {
        field: Field,
        'form-element': FormElement,
        kbutton: Button,
        FormDropDown,
        FileUploader,
    },
    computed: {
        getFieldClass() {
            return {
                label: 'text-sm leading-7 text-gray-700 w-32 shrink-0 grow-1 !font-normal',
                root: 'w-full !flex items-center gap-4',
                wrap: 'w-full',
                field: '!flex !flex-row !gap-6 !w-96',
            };
        },
    },
    methods: {
        requiredtrue,
        handleFilterUpdate(values) {
            this.$emit('onChnageFilter', values);
        },
    },
};
</script>
