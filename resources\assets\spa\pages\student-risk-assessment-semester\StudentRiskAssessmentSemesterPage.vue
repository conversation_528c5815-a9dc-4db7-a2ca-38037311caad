<script setup>
import Layout from '@spa/pages/Layouts/Layout';
import { Head } from '@inertiajs/vue3';
import PageTitleContent from '@spa/pages/Layouts/PageTitleContent.vue';
import StudentRiskAssessmentSemesterListComponent from '@spa/modules/student-risk-assessment-semester/StudentRiskAssessmentSemesterListComponent.vue';
</script>

<template>
    <Layout :no-spacing="true">
        <Head title="Student Risk Assessment Semesters" />
        <template v-slot:pageTitleContent>
            <PageTitleContent title="Student Risk Assessment Semesters" :back="false" />
        </template>
        <div class="h-screen-header flex flex-col space-y-4 px-8 py-6">
            <StudentRiskAssessmentSemesterListComponent />
        </div>
    </Layout>
</template>

<style scoped></style>
