<script setup>
import StudentRiskAssessmentSettingsComponent from '@spa/modules/student-risk-assessment-settings/StudentRiskAssessmentSettingsComponent.vue';
import Layout from '@spa/pages/Layouts/Layout';
import { Head } from '@inertiajs/vue3';
import PageTitleContent from '@spa/pages/Layouts/PageTitleContent.vue';
</script>

<template>
    <Layout :no-spacing="true">
        <Head title="Student Risk Assessment Settings" />
        <template v-slot:pageTitleContent>
            <PageTitleContent title="Student Risk Assessment Settings" :back="false" />
        </template>
        <div class="h-screen-header flex flex-col px-8 py-6">
            <StudentRiskAssessmentSettingsComponent />
        </div>
    </Layout>
</template>

<style scoped></style>
