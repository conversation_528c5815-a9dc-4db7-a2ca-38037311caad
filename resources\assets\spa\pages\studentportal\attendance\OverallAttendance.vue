<template>
    <Layout :noSpacing="true" :loading="false">
        <template v-slot:pageTitleContent>
            <PageTitleContent :title="'Overall Attendance'" :back="false" />
        </template>
        <template #tabs>
            <header-tab currentTab="overall" />
        </template>
        <div class="space-y-6 px-4 py-6 md:px-8 lg:space-y-6">
            <div class="space-y-4">
                <div class="flex items-center justify-between">
                    <div class="flex items-center gap-2">
                        <FilterByBatch
                            :filters="filters"
                            :textsearch="true"
                            :pull="['filters', 'batch', 'data', 'infocard']"
                        />
                    </div>
                    <!--
                    <div class="flex w-fit items-center rounded-lg bg-gray-200 p-1">
                        <toggle-button
                            label="List View"
                            :isActive="activeList"
                            @toggle="toggleView(true)"
                        />
                        <toggle-button
                            label="Calendar View"
                            :isActive="activeCalendar"
                            @toggle="toggleView(false)"
                        />
                    </div>
                    -->
                </div>
                <info-card v-if="infocard.length > 0" v-bind="infocard" />
            </div>
            <overall-grid :report-data="data?.attendance?.data" />
        </div>
    </Layout>
</template>
<script>
import ToggleButtonVue from '@spa/components/Buttons/ToggleButton.vue';
import Layout from '@spa/pages/Layouts/Layout.vue';
import PageTitleContent from '@spa/pages/Layouts/PageTitleContent.vue';
import HeaderTabsVue from '@studentportal/attendance/partials/HeaderTabs.vue';
import OverallAttendanceGridVue from '@studentportal/attendance/partials/OverallAttendanceGrid.vue';
import InfoCard from '@studentportal/attendance/InfoCard.vue';

import { DropDownList } from '@progress/kendo-vue-dropdowns';
import { filterBy } from '@progress/kendo-data-query';
import FilterByBatchV2 from '@spa/components/filters/FilterByBatchV2.vue';

export default {
    props: {
        data: { type: Object, default: {} },
        filters: { type: Object, default: {} },
        infocard: { type: Object, default: {} },
    },
    components: {
        Layout,
        PageTitleContent,
        'header-tab': HeaderTabsVue,
        'toggle-button': ToggleButtonVue,
        'overall-grid': OverallAttendanceGridVue,
        'info-card': InfoCard,
        FilterByBatch: FilterByBatchV2,
    },
    data() {
        return {
            activeList: true,
            activeCalendar: false,
            popupSettings: {
                className: 'tw-fixed-width',
                animate: false,
                offset: { left: 150, top: 50 },
            },
            result: null,
            course: null,
        };
    },
    computed: {
        // getCourses() {
        //     return Object.keys(this.courses).map((key) => ({
        //         value: key,
        //         label: this.courses[key],
        //     }));
        // },
    },
    methods: {
        toggleView(isList) {
            this.activeList = isList;
            this.activeCalendar = !isList;
        },
    },
};
</script>
<style lang=""></style>
