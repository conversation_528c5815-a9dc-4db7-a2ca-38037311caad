<template>
    <Layout :noSpacing="true" :loading="false">
        <template v-slot:pageTitleContent>
            <PageTitleContent :title="'Weekly Attendance'" :back="false" />
        </template>
        <template #tabs>
            <header-tab :currentTab="'weekly'" />
        </template>
        <div class="space-y-6 px-4 py-6 md:px-8">
            <div class="space-y-4">
                <div class="flex gap-2">
                    <FilterByBatch
                        :filters="filters"
                        :textsearch="true"
                        :pull="['filters', 'batch', 'data', 'infocard']"
                    />
                </div>
                <info-card v-if="infocard.title" v-bind="infocard" />
            </div>
            <weekly-grid :report-data="data?.attendance?.data" />
        </div>
    </Layout>
</template>
<script>
import Layout from '@spa/pages/Layouts/Layout.vue';
import PageTitleContent from '@spa/pages/Layouts/PageTitleContent.vue';
import HeaderTabsVue from '@studentportal/attendance/partials/HeaderTabs.vue';
import WeeklyAttendanceGridVue from '@studentportal/attendance/partials/WeeklyAttendanceGrid.vue';
import { DropDownList } from '@progress/kendo-vue-dropdowns';
import InfoCard from '@studentportal/attendance/InfoCard.vue';
import FilterByBatchV2 from '@spa/components/filters/FilterByBatchV2.vue';

export default {
    props: {
        data: { type: Object, default: {} },
        courses: { type: Object, default: {} },
        filters: { type: Object, default: {} },
        infocard: { type: Object, default: {} },
    },
    components: {
        Layout,
        PageTitleContent,
        'weekly-grid': WeeklyAttendanceGridVue,
        'header-tab': HeaderTabsVue,
        'k-dropdown-list': DropDownList,
        'info-card': InfoCard,
        FilterByBatch: FilterByBatchV2,
    },
    data() {
        return {
            popupSettings: {
                className: 'tw-fixed-width',
                animate: false,
                offset: { left: 150, top: 50 },
            },
            result: null,
            course: null,
            semester: null,
            term: null,
            semesters: {},
            terms: {},
        };
    },
    computed: {
        getCourses() {
            return Object.keys(this.courses).map((key) => ({
                value: key,
                label: this.courses[key],
            }));
        },
        getSemesters() {
            return Object.keys(this.semesters).map((key) => ({
                value: key,
                label: this.semesters[key],
            }));
        },
        getTerms() {
            return Object.keys(this.terms).map((key) => ({
                value: key,
                label: this.terms[key],
            }));
        },
    },
    methods: {
        handleCourseChange(e) {
            const reqData = {
                courseId: e.value,
                studentId: this.data.studentId,
            };

            $http
                .post(route('student-attendance-ajaxAction'), {
                    action: 'getSemesterList',
                    data: reqData,
                })
                .then((response) => {
                    // Handle the response as needed
                    console.log('response', response);
                    this.semesters = response;
                })
                .catch((error) => {
                    console.error('error', error);
                });
        },
        handleSemesterChange(e) {
            console.log('semester', this.course);
            const reqData = {
                courseId: this.course.value,
                studentId: this.data.studentId,
                semesterId: e.value,
            };
            $http
                .post(route('student-attendance-ajaxAction'), {
                    action: 'getTermList',
                    data: reqData,
                })
                .then((response) => {
                    // Handle the response as needed
                    console.log('response', response);
                    this.terms = response;
                })
                .catch((error) => {
                    console.error('error', error);
                });
        },
        featchWeeklyAttendance(e) {
            const reqData = {
                courseId: this.course.value,
                studentId: this.data.studentId,
                semesterId: this.semester.value,
                termId: e.value,
            };
            $http
                .post(route('student-attendance-ajaxAction'), {
                    action: 'getWeeklyAttendanceList',
                    data: reqData,
                })
                .then((response) => {
                    // Handle the response as needed
                    console.log('response', response);
                    this.result = response;
                })
                .catch((error) => {
                    console.error('error', error);
                });
        },
    },
};
</script>
<style lang=""></style>
