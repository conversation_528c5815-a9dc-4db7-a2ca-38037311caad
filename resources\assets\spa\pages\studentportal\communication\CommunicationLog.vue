<template lang="">
    <Layout :noSpacing="true" :loading="false">
        <template v-slot:pageTitleContent>
            <PageTitleContent :title="'Communication Log'" :back="false" />
        </template>
        <template #tabs>
            <header-tab :currentTab="'logs'" />
        </template>
        <div class="space-y-4 px-4 py-6 md:px-8">
            <communication-log-grid
                :data="communicationData"
                :filters="this.resource.state.filters"
                :pagination="this.resource.state.pageable"
                @changepage="handlePageChange"
                @filter="handleSearch"
            />
        </div>
    </Layout>
</template>
<script>
import { ref, computed, watch } from 'vue';
import Layout from '@spa/pages/Layouts/Layout.vue';
import PageTitleContent from '@spa/pages/Layouts/PageTitleContent.vue';
import CommunicationLogGrid from '@studentportal/communication/partials/CommunicationLogGrid.vue';
import Button from '@spa/components/Buttons/Button.vue';
import IconInput from '@spa/components/IconInput.vue';
import { IconEdit24Regular } from '@iconify-prerendered/vue-fluent';
import HeaderTabs from '@studentportal/communication/partials/HeaderTabs.vue';
import { router, usePage } from '@inertiajs/vue3';
import { useLoaderStore } from '@spa/stores/modules/global-loader';
import useStudentResource from '@spa/services/studentportal/useStudentResource.js';

export default {
    setup(props) {
        const resource = useStudentResource({
            filters: {
                search: props.query?.search || '',
                take: props.query?.take || 10,
                page: props.query?.page || 1,
                sort: props.query?.sort || null,
                dir: props.query?.dir || null,
            },
            only: ['gridData', 'data'],
        });
        watch(
            () => resource.state.filters,
            (val) => {
                resource.fetch();
            },
            { deep: true }
        );
        return {
            resource,
        };
    },
    props: {
        data: { type: Object, default: {} },
        gridData: { type: Object, default: {} },
    },
    data() {
        return {
            search: null,
        };
    },
    mounted() {
        this.resource.setPagination(this.gridData);
    },
    computed: {
        communicationData() {
            return this.prepareTableData(this.gridData.data);
        },
    },
    components: {
        Layout,
        PageTitleContent,
        'communication-log-grid': CommunicationLogGrid,
        'tw-button': Button,
        'search-bar': IconInput,
        'icon-pen': IconEdit24Regular,
        'header-tab': HeaderTabs,
    },
    methods: {
        prepareTableData(data) {
            return data.map((item) => ({
                record_time: item.created_at,
                type: item.logName,
                // status: item.status,
                log: item.log,
                user_name: item.user_name,
            }));
        },
        handlePageChange(number, take) {
            this.resource.state.filters.take = take;
            this.resource.state.filters.page = number;
        },
        handleSearch(search) {
            this.resource.state.filters.search = search;
        },
    },
    watch: {
        grid: {
            handler(newval) {
                this.resource.setPagination(this.grid);
            },
            deep: true,
        },
    },
};
</script>
<style lang=""></style>
