<template lang="">
    <Layout :noSpacing="true" :loading="false">
        <template v-slot:pageTitleContent>
            <PageTitleContent :title="'Email Feedback'" :back="false" />
        </template>
        <template #tabs>
            <header-tab :currentTab="'feedback'" />
        </template>
        <div class="space-y-4 px-4 py-6 md:px-8">
            <div class="flex items-center justify-between">
                <search-bar
                    v-model.lazy="search"
                    :pt="{ root: 'w-52' }"
                    placeholder="Search"
                    :debounce="300"
                    :autocomplete="'off'"
                />
                <feedback-email :topics="getTopics" :emails="getEmails" :user="user" />
            </div>
            <email-feedback-grid
                :data="gridData.data"
                :filters="this.resource.state.filters"
                :pagination="this.resource.state.pageable"
                @changepage="handlePageChange"
                @filter="handleSearch"
            />
        </div>
    </Layout>
</template>
<script>
import { ref, computed, watch } from 'vue';
import Layout from '@spa/pages/Layouts/Layout.vue';
import PageTitleContent from '@spa/pages/Layouts/PageTitleContent.vue';
import EmailFeedbackGrid from '@studentportal/communication/partials/EmailFeedbackGrid.vue';
import Button from '@spa/components/Buttons/Button.vue';
import IconInput from '@spa/components/IconInput.vue';
import HeaderTabs from '@studentportal/communication/partials/HeaderTabs.vue';
import DrawerEmail from '@studentportal/communication/partials/DrawerEmail.vue';
import useStudentResource from '@spa/services/studentportal/useStudentResource.js';
import { debounce } from 'lodash';

export default {
    setup(props) {
        const resource = useStudentResource({
            filters: {
                search: props.query?.search || '',
                take: props.query?.take || 10,
                page: props.query?.page || 1,
                sort: props.query?.sort || null,
                dir: props.query?.dir || null,
            },
            only: ['gridData', 'data'],
        });
        watch(
            () => resource.state.filters,
            (val) => {
                resource.fetch();
            },
            { deep: true }
        );
        return {
            resource,
        };
    },
    props: {
        user: { type: Object, default: [] },
        topics: { type: Object, default: [] },
        emails: { type: Object, default: [] },
        gridData: { type: Object, default: {} },
    },
    data() {
        return {
            search: this.resource.state.filters.search || null,
            debouncedEmitFilter: debounce(function (newval) {
                if (newval.search === '') {
                    newval.search = null;
                }
                this.handleSearch(newval);
            }, 300),
        };
    },
    mounted() {
        this.resource.setPagination(this.gridData);
    },
    computed: {
        getTopics() {
            return Object.entries(this.topics).map(([key, value]) => ({
                label: value,
                value: key,
            }));
        },
        getEmails() {
            return Object.entries(this.emails).map(([key, value]) => ({
                label: value,
                value: value.email,
            }));
        },
    },
    components: {
        Layout,
        PageTitleContent,
        'email-feedback-grid': EmailFeedbackGrid,
        'tw-button': Button,
        'search-bar': IconInput,
        'header-tab': HeaderTabs,
        'feedback-email': DrawerEmail,
    },
    methods: {
        //     prepareTableData(data) {
        //         return data.map((item) => ({
        //             record_time: item.created_at,
        //             to: item.log_type,
        //             status: item.status,
        //             log: item.log,
        //         }));
        //     },
        handlePageChange(number, take) {
            this.resource.state.filters.take = take;
            this.resource.state.filters.page = number;
        },
        handleSearch(search) {
            this.resource.state.filters.search = search;
        },
    },
    watch: {
        grid: {
            handler(newval) {
                this.resource.setPagination(newval);
            },
            deep: true,
        },
        search(newValue, oldValue) {
            this.debouncedEmitFilter(newValue);
        },
    },
};
</script>
<style lang=""></style>
