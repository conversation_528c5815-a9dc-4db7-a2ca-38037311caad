<template lang="">
    <Layout :noSpacing="true" :loading="false">
        <template v-slot:pageTitleContent>
            <PageTitleContent :title="'Email To Trainer'" :back="false" />
        </template>
        <template #tabs>
            <header-tab :currentTab="'trainer'" />
        </template>
        <div class="space-y-4 px-4 py-6 md:px-8">
            <div class="flex items-center justify-between">
                <search-bar
                    v-model.lazy="search"
                    :pt="{ root: 'w-52' }"
                    placeholder="Search"
                    :debounce="300"
                    :autocomplete="'off'"
                />
                <drawer-email
                    :studentId="studentId"
                    :trainers="getTrainers"
                    :courses="getCourses"
                    :subjects="getSubjects"
                    :user="user"
                    :type="'trainer'"
                />
            </div>
            <email-trainer-grid
                :data="gridData.data"
                :filters="this.resource.state.filters"
                :pagination="this.resource.state.pageable"
                @changepage="handlePageChange"
                @filter="handleSearch"
            />
        </div>
    </Layout>
</template>
<script>
import { ref, computed, watch } from 'vue';
import Layout from '@spa/pages/Layouts/Layout.vue';
import PageTitleContent from '@spa/pages/Layouts/PageTitleContent.vue';
import EmailToTrainerGrid from '@studentportal/communication/partials/EmailToTrainerGrid.vue';
import Button from '@spa/components/Buttons/Button.vue';
import IconInput from '@spa/components/IconInput.vue';
import { IconEdit24Regular } from '@iconify-prerendered/vue-fluent';
import HeaderTabs from '@studentportal/communication/partials/HeaderTabs.vue';
import DrawerEmail from '@studentportal/communication/partials/DrawerEmail.vue';
import useStudentResource from '@spa/services/studentportal/useStudentResource.js';
import { debounce } from 'lodash';

export default {
    setup(props) {
        const resource = useStudentResource({
            filters: {
                search: props.query?.search || '',
                take: props.query?.take || 10,
                page: props.query?.page || 1,
                sort: props.query?.sort || null,
                dir: props.query?.dir || null,
            },
            only: ['gridData', 'data'],
        });
        watch(
            () => resource.state.filters,
            (val) => {
                resource.fetch();
            },
            { deep: true }
        );
        return {
            resource,
        };
    },
    props: {
        studentId: '',
        user: { type: Object, default: [] },
        courses: { type: Object, default: {} },
        trainers: { type: Object, default: {} },
        courses: { type: Object, default: {} },
        subjects: { type: Object, default: {} },
        gridData: { type: Object, default: {} },
    },
    data() {
        return {
            search: this.resource.state.filters.search || null,
            debouncedEmitFilter: debounce(function (newval) {
                if (newval.search === '') {
                    newval.search = null;
                }
                this.handleSearch(newval);
            }, 300),
        };
    },
    mounted() {
        this.resource.setPagination(this.gridData);
    },
    computed: {
        getCourses() {
            return this.getSelectOptions(this.courses);
        },
        getSubjects() {
            return Object.values(this.subjects).map((subject) => ({
                label: Object.values({
                    code: subject.subject_code,
                    name: subject.subject_name,
                }),
                value: subject.subject_id,
            }));
        },
        getTrainers() {
            return Object.values(this.trainers).map((trainer) => ({
                label: Object.values({
                    name: `${trainer.name_title} ${trainer.first_name} ${trainer.last_name}`,
                    email: trainer.email,
                }),
                value: trainer.teacher_id,
            }));
        },
    },
    components: {
        Layout,
        PageTitleContent,
        'email-trainer-grid': EmailToTrainerGrid,
        'tw-button': Button,
        'search-bar': IconInput,
        'icon-pen': IconEdit24Regular,
        'header-tab': HeaderTabs,
        'drawer-email': DrawerEmail,
    },
    methods: {
        getSelectOptions(data = {}) {
            return Object.entries(data).map(([value, label]) => ({
                label,
                value,
            }));
        },
        handlePageChange(number, take) {
            this.resource.state.filters.take = take;
            this.resource.state.filters.page = number;
        },
        handleSearch(search) {
            this.resource.state.filters.search = search;
        },
    },
    watch: {
        grid: {
            handler(newval) {
                this.resource.setPagination(this.grid);
            },
            deep: true,
        },
        search(newValue, oldValue) {
            this.debouncedEmitFilter(newValue);
        },
    },
};
</script>
<style lang=""></style>
