<template lang="">
    <k-form-element class="h-full overflow-y-auto">
        <fieldset class="k-form-fieldset">
            <div class="flex min-h-[40px] items-center gap-4 px-4 py-2 shadow-inner-line">
                <span class="w-16 text-sm leading-5 text-gray-500">From:</span>
                <k-field
                    :id="'from_email'"
                    :name="'from_email'"
                    :component="'fromTemplate'"
                    :validator="requiredtrue"
                    :value="user.email"
                    :disabled="true"
                >
                    <template #fromTemplate="{ props }">
                        <FormInput
                            v-bind="props"
                            @change="props.onChange"
                            @blur="props.onBlur"
                            @focus="props.onFocus"
                            :pt="{
                                input: 'tw-input-borderless',
                                root: 'w-full',
                            }"
                        />
                    </template>
                </k-field>
            </div>
        </fieldset>
        <fieldset class="k-form-fieldset">
            <div class="flex min-h-[40px] items-center gap-4 px-4 py-2 shadow-inner-line">
                <span class="w-16 text-sm leading-5 text-gray-500">Courses:</span>
                <k-field
                    :component="'toTemplate'"
                    :style="{ width: '100%' }"
                    :id="'course'"
                    :name="'course'"
                    :data-items="courses"
                    :default-item="defaults.course"
                    :text-field="'label'"
                    :data-item-key="'value'"
                    :value-field="'value'"
                    :valuePrimitive="true"
                    :validator="requiredtrue"
                    :popup-settings="popupSettings"
                    :class="fieldClass"
                    :value-render="'valueRender'"
                    :item-render="'itemRender'"
                    @change="handleCourseDropdownChange"
                >
                    <template #toTemplate="{ props }">
                        <FormDropDown
                            v-bind="props"
                            @change="props.onChange"
                            @blur="props.onBlur"
                            @focus="props.onFocus"
                        >
                        </FormDropDown>
                    </template>
                </k-field>
            </div>
        </fieldset>
        <fieldset class="k-form-fieldset">
            <div class="flex min-h-[40px] items-center gap-4 px-4 py-2 shadow-inner-line">
                <span class="w-16 text-sm leading-5 text-gray-500">Subject:</span>
                <k-field
                    :component="'topicsTemplate'"
                    :style="{ width: '100%' }"
                    :id="'type'"
                    :name="'type'"
                    :data-items="subjects"
                    :default-item="defaults.to"
                    :text-field="'label'"
                    :data-item-key="'value'"
                    :value-field="'value'"
                    :value="subject"
                    :value-primitive="true"
                    :validator="requiredtrue"
                    @change="handleSubjectDropdownChange"
                    :popup-settings="popupSettings"
                    :class="fieldClass"
                    :item-render="'itemRender'"
                    :value-render="'valueRender'"
                >
                    <template #topicsTemplate="{ props }">
                        <DropDownList
                            v-bind="props"
                            @change="props.onChange"
                            @blur="props.onBlur"
                            @focus="props.onFocus"
                        >
                            <template #valueRender="{ props }">
                                <div v-bind="props">
                                    <div class="flex items-center gap-2">
                                        <span>{{ props.value.label }}</span>
                                    </div>
                                </div>
                            </template>
                            <template #itemRender="{ props }">
                                <li v-bind="props">
                                    <div class="flex items-center gap-2">
                                        <tw-badge
                                            :pt="{
                                                root: 'w-fit text-xs py-1.5',
                                            }"
                                            :variant="'secondary'"
                                            ><span class="leading-none">{{
                                                props.dataItem.code
                                            }}</span></tw-badge
                                        >
                                        <span>{{ props.dataItem.label }}</span>
                                    </div>
                                </li>
                            </template>
                        </DropDownList>
                    </template>
                </k-field>
            </div>
        </fieldset>
        <fieldset class="k-form-fieldset">
            <div class="flex min-h-[40px] items-center gap-4 px-4 py-2 shadow-inner-line">
                <span class="w-16 text-sm leading-5 text-gray-500">Trainer:</span>
                <k-field
                    :component="'toTemplate'"
                    :style="{ width: '100%' }"
                    :id="'trainer'"
                    :name="'trainer[]'"
                    :data-items="trainers"
                    :placeholder="'Select Trainer(s)'"
                    :text-field="'label'"
                    :data-item-key="'value'"
                    :value-field="'value'"
                    :value="trainer"
                    :valuePrimitive="true"
                    :validator="requiredtrue"
                    :popup-settings="popupSettings"
                    :class="fieldClass"
                    class="tw-input-borderless"
                    :item-render="'itemRender'"
                    @change="handleDropdownChange($event, 'trainer')"
                    :tag-render="'tagRender'"
                >
                    <template #toTemplate="{ props }">
                        <MultiSelect
                            v-bind="props"
                            @change="props.onChange"
                            @blur="props.onBlur"
                            @focus="props.onFocus"
                        >
                            <template #tagRender="{ props }">
                                <div v-bind="props">
                                    {{ props.length }}
                                    <Chip
                                        :text="props.tagData.text[0]"
                                        :rounded="'full'"
                                        :removable="props.removable"
                                        @remove="props.onRemove"
                                    ></Chip>
                                </div>
                            </template>
                            <template #itemRender="{ props }">
                                <li v-bind="props">
                                    <div class="flex items-center gap-2">
                                        <span>{{ props.dataItem.label[0] }}</span>
                                        <tw-badge
                                            :pt="{
                                                root: 'w-fit text-xs py-1.5',
                                            }"
                                            :variant="'secondary'"
                                            ><span class="leading-none">{{
                                                props.dataItem.label[1]
                                            }}</span></tw-badge
                                        >
                                    </div>
                                </li>
                            </template>
                        </MultiSelect>
                    </template>
                </k-field>
            </div>
        </fieldset>
        <fieldset class="k-form-fieldset">
            <div class="flex min-h-[40px] items-center gap-4 px-4 py-2 shadow-inner-line">
                <span class="w-16 text-sm leading-5 text-gray-500">Topic:</span>
                <k-field
                    :id="'subject'"
                    :name="'subject'"
                    :component="'subjectTemplate'"
                    :validator="requiredtrue"
                    :placeholder="'Enter Topic'"
                >
                    <template #subjectTemplate="{ props }">
                        <FormInput
                            v-bind="props"
                            @change="props.onChange"
                            @blur="props.onBlur"
                            @focus="props.onFocus"
                            :pt="{
                                root: 'w-full',
                                input: 'tw-input-borderless',
                            }"
                        />
                    </template>
                </k-field>
            </div>
        </fieldset>
        <fieldset class="k-form-fieldset">
            <k-editor
                :tools="tools"
                ref="editor"
                @change="handleDropdownChange($event, 'log')"
                @blur="handleDropdownChange($event, 'log')"
                class="trainer-custom-editor-style"
            />
        </fieldset>
        <fieldset class="k-form-fieldset">
            <div class="mail__attach p-4 shadow-inner-line">
                <tw-file-uploader @add="onFileAdd" @remove="onFileRemove" />
            </div>
        </fieldset>
        <fieldset class="k-form-fieldset">
            <div class="mail__checkbox items-center bg-slate-50 p-4 pr-1 shadow-inner-line">
                <Checkbox
                    @change="handleDropdownChange($event, 'send_copy')"
                    :label="' Send a Copy to Myself'"
                />
            </div>
        </fieldset>
    </k-form-element>
    <div
        class="absolute bottom-0 z-[999] flex w-full justify-end gap-2 border-t border-gray-200 bg-white px-6 py-4"
    >
        <tw-button size="sm" variant="secondary" @click="cancelProcess"
            ><span class="leading-none">Cancel</span></tw-button
        >
        <tw-button size="sm" :pt="{ root: 'min-w-[10rem]' }" type="submit">
            <span v-if="isSubmitting">
                <icon name="loading-spinner" :width="'16'" :height="'16'" /> </span
            ><span class="leading-none">Send Mail</span></tw-button
        >
    </div>
</template>
<script>
import { Field, FormElement } from '@progress/kendo-vue-form';
import { Input } from '@progress/kendo-vue-inputs';
import { DropDownList } from '@progress/kendo-vue-dropdowns';
import { Editor } from '@progress/kendo-vue-editor';

import FormInput from '@spa/components/KendoInputs/FormInput.vue';
import FormDropDown from '@spa/components/KendoInputs/FormDropDown.vue';
import Button from '@spa/components/Buttons/Button.vue';
import Badge from '@spa/components/badges/Badge.vue';
import { Checkbox } from '@progress/kendo-vue-inputs';
import FileUploader from '@spa/components/Uploader/FileUploader.vue';
import { buildFormData, createFileFromData } from '@spa/helpers';
import { MultiSelect } from '@progress/kendo-vue-dropdowns';
import { Chip } from '@progress/kendo-vue-buttons';

export default {
    props: {
        studentId: '',
        user: { type: Object, default: [] },
        courses: { type: Object, default: [] },
        subjects: { type: Object, default: [] },
        trainers: { type: Object, default: [] },
    },
    components: {
        'tw-button': Button,
        'k-editor': Editor,
        'k-field': Field,
        'k-form-element': FormElement,
        'k-input': Input,
        FormInput,
        FormDropDown,
        DropDownList,
        MultiSelect,
        Checkbox,
        'tw-badge': Badge,
        'tw-file-uploader': FileUploader,
        Chip,
    },
    inject: {
        kendoForm: { default: {} },
    },
    emit: {
        close: null,
    },
    data() {
        return {
            emailTo: null,
            subject: null,
            trainer: null,
            subjects: [],
            trainers: [],
            localImageFiles: [],
            loading: {
                subject: false,
                trainer: false,
            },
            send_copy: false,
            tools: [
                ['Bold', 'Italic', 'Underline'],
                ['Undo', 'Redo'],
                ['Link', 'Unlink'],
                ['AlignLeft', 'AlignCenter', 'AlignRight'],
                ['OrderedList', 'UnorderedList', 'Indent', 'Outdent'],
            ],
            files: {},
            defaults: {
                to: {
                    label: 'Select Subject',
                    value: null,
                },
                course: {
                    label: 'Select Course',
                    value: null,
                },
                trainer: {
                    label: 'Select Trainer',
                    value: null,
                },
                topic: {
                    label: 'Select Topic',
                    value: null,
                },
            },
            popupSettings: {
                animate: false,
                offset: { left: 150, top: 50 },
            },
        };
    },
    methods: {
        handleDropdownChange(e, name) {
            const value = e.value || null;

            if (name === 'log') {
                this.kendoForm.onBlur('log', {
                    value: this.$refs.editor.getHTML(),
                });
                this.kendoForm.onChange('log', {
                    value: this.$refs.editor.getHTML(),
                });
            } else if (name === 'trainer') {
                this.trainer = value;
                this.kendoForm.onChange('trainer', {
                    value: this.trainer,
                });
            } else {
                this.kendoForm.onChange(name, {
                    value: value,
                });
            }
        },
        async handleCourseDropdownChange(e) {
            const value = e.value || null;
            this.subject = null;
            this.trainer = null;
            this.subjects = [];
            this.trainers = [];
            this.loading.subject = true;
            this.loading.trainer = true;

            this.kendoForm.onChange('course', {
                value: value,
            });

            let postData = {
                courseId: value,
                studentId: this.studentId,
                action: 'getSubjectData',
            };
            const response = await $http
                .post(route('spa.studentportal.ajaxAction'), postData)
                .then((resp) => {
                    let res = Object.values(resp).map((subject) => ({
                        // label: Object.values({ code: subject.subject_code, name: subject.subject_name}),
                        label: subject.subject_code + ':' + subject.subject_name,
                        code: subject.subject_code,
                        value: subject.subject_id,
                    }));
                    this.subjects = res;
                })
                .catch((error) => {
                    console.error('Get Teacher Data Error', error);
                })
                .finally(() => {
                    this.loading.subject = false;
                    this.loading.trainer = false;
                });
        },
        async handleSubjectDropdownChange(e) {
            const value = e.value || null;
            this.subject = value;
            this.trainer = null;
            this.trainers = [];
            this.loading.trainer = true;

            this.kendoForm.onChange('type', {
                value: this.subject,
            });

            let postData = {
                subjectId: value,
                studentId: this.studentId,
                action: 'getTeacherDataFromSubject',
            };
            const response = await $http
                .post(route('spa.studentportal.ajaxAction'), postData)
                .then((resp) => {
                    let res = Object.values(resp).map((trainer) => ({
                        label: Object.values({
                            name: `${trainer.name_title} ${trainer.first_name} ${trainer.last_name}`,
                            email: trainer.email,
                        }),
                        value: trainer.email,
                    }));
                    this.trainers = res;
                })
                .catch((error) => {
                    console.error('Get Teacher Data Error', error);
                })
                .finally(() => {
                    this.loading.trainer = false;
                });
        },
        onFileAdd(e, files) {
            this.files = files; // Store the array of files
            // Iterate over each file in the array
            this.localImageFiles = [];
            this.files.forEach((file) => {
                const rawFileContent = file.getRawFile();
                const createdFile = createFileFromData(file.name, file.extension, rawFileContent);

                // Update Kendo form for each file
                this.localImageFiles.push(createdFile);
            });
            console.log(this.localImageFiles);
            this.kendoForm.onChange('images[]', {
                value: this.localImageFiles,
            });
        },
        onFileRemove(e, file) {
            this.files = file[0];
            const rawFileContent = file[0].getRawFile();
            const actualFile = createFileFromData(
                this.files.name,
                this.files.extension,
                rawFileContent
            );

            this.kendoForm.onChange('images[]', {
                value: actualFile,
            });
        },
        cancelProcess() {
            this.$emit('close');
        },
    },
};
</script>
<style lang=""></style>
