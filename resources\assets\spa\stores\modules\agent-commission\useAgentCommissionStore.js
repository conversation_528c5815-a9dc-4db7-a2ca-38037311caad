import { defineStore } from 'pinia';
import useCommonStore from '@spa/stores/modules/commonStore.js';
import { ref } from 'vue';

export const useAgentCommissionStore = defineStore('useAgentCommissionStore', () => {
    const storeUrl = ref('v2/tenant/agent-commission');
    const commonStoreProps = useCommonStore(storeUrl.value);
    const { formData } = commonStoreProps;

    const addAgentCommissionBulk = async (payload) => {
        try {
            return await $http.post(`/api/${storeUrl.value}/bulk`, formData.value);
        } catch (e) {
            console.error('Error adding agent commission:', e);
            throw e;
        }
    };

    return {
        ...commonStoreProps,
        addAgentCommissionBulk,
    };
});
