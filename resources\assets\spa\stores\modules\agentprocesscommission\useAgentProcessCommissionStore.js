import { defineStore } from 'pinia';
import useCommonStore from '@spa/stores/modules/commonStore.js';
import { ref } from 'vue';

export const useAgentProcessCommissionStore = defineStore('useAgentProcessCommissionStore', () => {
    const storeUrl = ref('v2/tenant/agentprocesscommission');
    const commonStoreProps = useCommonStore(storeUrl.value);
    return {
        ...commonStoreProps,
    };
});
