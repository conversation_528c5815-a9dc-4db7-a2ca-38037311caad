import { defineStore } from 'pinia';
import useCommonStore from '@spa/stores/modules/commonStore.js';
import { ref } from 'vue';

export const useCreditBonusAllocationStore = defineStore('useCreditBonusAllocationStore', () => {
    const storeUrl = ref('v2/tenant/credit-bonus-allocation');
    const commonStoreProps = useCommonStore(storeUrl.value);
    const showStudentPaymentAgentCredit = ref(false);
    return {
        ...commonStoreProps,
        showStudentPaymentAgentCredit,
    };
});
