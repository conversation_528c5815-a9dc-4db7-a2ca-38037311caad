import { defineStore } from 'pinia';
import { ref } from 'vue';
import useCommonStore from '@spa/stores/modules/commonStore.js';
import apiClient from '@spa/services/api.client.js';

export const useSemesterStore = defineStore('useSemesterStore', () => {
    const storeUrl = ref('v2/tenant/semester');

    // Form constants
    const courseTypes = ref([]);
    const calendarTypes = ref([]);
    const years = ref([]);

    const {
        serverPagination,
        filters,
        loading,
        enableLoader,
        progresses,
        all,
        form_rows,
        formRef,
        formDialog,
        formData,
        errors,
        statusOptions,
        selected,
        getAll,
        onRequest,
        store,
        update,
        remove,
        createFunction,
        closeFunction,
        edit,
        confirmDelete,
        fetchPaged,
        clearFunction,
        toggleStatus,
        notifySuccess,
        notifyError,
        jsonToFormData,
        changeStatusOtherColumn,
        isFormValid,
        submitKendoForm,
    } = useCommonStore(storeUrl.value);

    // Load form constants
    const loadFormConstants = async () => {
        try {
            if (enableLoader.value) {
                loading.value = true;
            }
            const response = await apiClient.get(`/api/${storeUrl.value}/form-constants`);

            if (response.success) {
                courseTypes.value = response.data.courseTypes || [];
                calendarTypes.value = response.data.calendarTypes || [];
                years.value = response.data.years || [];
            }
        } catch (error) {
            console.error('Error loading form constants:', error);
            notifyError('Failed to load form constants');
        } finally {
            if (enableLoader.value) {
                loading.value = false;
            }
        }
    };

    // Convert date to DD-MM-YYYY format
    const formatDateForAPI = (date) => {
        if (!date) return null;
        if (date instanceof Date) {
            // Format as YYYY-MM-DD without timezone conversion
            // Using local date components to avoid UTC conversion issues
            const year = date.getFullYear();
            const month = String(date.getMonth() + 1).padStart(2, '0');
            const day = String(date.getDate()).padStart(2, '0');
            return `${year}-${month}-${day}`;
        }
        return date;
    };

    // Convert string date to Date object for form fields
    const parseStringToDate = (dateString) => {
        if (!dateString) return null;
        if (dateString instanceof Date) return dateString;

        try {
            if (typeof dateString === 'string') {
                // Handle DD-MM-YYYY format from API (from parseDate function)
                if (dateString.includes('-') && dateString.split('-')[0].length <= 2) {
                    const parts = dateString.split('-');
                    if (parts.length === 3) {
                        // Convert DD-MM-YYYY to YYYY-MM-DD for proper Date parsing
                        const isoDate = `${parts[2]}-${parts[1]}-${parts[0]}`;
                        const date = new Date(isoDate);
                        if (!isNaN(date.getTime())) {
                            return date;
                        }
                    }
                }
                // Handle YYYY-MM-DD format from API
                else {
                    const date = new Date(dateString);
                    if (!isNaN(date.getTime())) {
                        return date;
                    }
                }
            }
        } catch (error) {
            console.warn('Error parsing date:', dateString, error);
        }
        return null;
    };

    // Override the edit function to handle date conversion
    const editSemester = async (item) => {
        // Ensure form constants are loaded before editing
        if (
            courseTypes.value.length === 0 ||
            calendarTypes.value.length === 0 ||
            years.value.length === 0
        ) {
            await loadFormConstants();
        }

        // Convert string dates to Date objects and ensure proper data types
        const editData = { ...item };

        // Convert date fields from string to Date objects
        if (editData.semester_start) {
            editData.semester_start = parseStringToDate(editData.semester_start);
        }
        if (editData.semester_finish) {
            editData.semester_finish = parseStringToDate(editData.semester_finish);
        }

        // Handle calendar_type mapping - the form field 'calendar_type' should contain the ID value
        // The API returns calendar_type_id as the actual ID and calendar_type as the text
        if (editData.calendar_type_id !== null && editData.calendar_type_id !== undefined) {
            editData.calendar_type = Number(editData.calendar_type_id);
        }

        // Ensure dropdown values are numbers to match dropdown value types (kendify produces numeric values)
        if (editData.course_type_id !== null && editData.course_type_id !== undefined) {
            editData.course_type_id = Number(editData.course_type_id);
        }
        if (editData.year !== null && editData.year !== undefined) {
            editData.year = Number(editData.year);
        }

        // Additional validation - ensure the calendar_type value is properly set
        if (!editData.calendar_type && editData.calendar_type_id) {
            editData.calendar_type = Number(editData.calendar_type_id);
        }

        // Check if values exist in the options
        const courseTypeExists = courseTypes.value.find(
            (ct) => ct.value === editData.course_type_id
        );
        const calendarTypeExists = calendarTypes.value.find(
            (ct) => ct.value === editData.calendar_type
        );
        const yearExists = years.value.find((y) => y.value === editData.year);

        // Call the original edit function
        edit(editData);
    };

    // Override the create function to ensure form constants are loaded
    const createSemester = async () => {
        // Ensure form constants are loaded before creating
        if (
            courseTypes.value.length === 0 ||
            calendarTypes.value.length === 0 ||
            years.value.length === 0
        ) {
            await loadFormConstants();
        }

        // Call the original create function
        createFunction();
    };

    // Custom submit method that handles date conversion
    const submitSemesterForm = async () => {
        try {
            if (enableLoader.value) {
                loading.value = true;
            }

            // Create a copy of formData and convert dates
            const submissionData = { ...formData.value };

            // Convert date fields to DD-MM-YYYY format
            if (submissionData.semester_start) {
                submissionData.semester_start = formatDateForAPI(submissionData.semester_start);
            }
            if (submissionData.semester_finish) {
                submissionData.semester_finish = formatDateForAPI(submissionData.semester_finish);
            }

            // Convert to FormData
            const formDataToSubmit = jsonToFormData(submissionData);

            let response;
            if (formData.value.id) {
                // Update existing semester
                response = await apiClient.post(
                    `/api/${storeUrl.value}/${formData.value.id}`,
                    formDataToSubmit
                );
            } else {
                // Create new semester
                response = await apiClient.post(`/api/${storeUrl.value}`, formDataToSubmit);
            }

            if (response.success) {
                notifySuccess(response.message || 'Semester saved successfully');
                closeFunction();
                await fetchPaged();
            } else {
                if (response.errors) {
                    errors.value = response.errors;
                }
                notifyError(response.message || 'Failed to save semester');
            }
        } catch (error) {
            console.error('Error submitting semester form:', error);
        } finally {
            if (enableLoader.value) {
                loading.value = false;
            }
        }
    };

    return {
        // Common store properties
        serverPagination,
        filters,
        loading,
        enableLoader,
        progresses,
        all,
        form_rows,
        formRef,
        formDialog,
        formData,
        errors,
        statusOptions,
        selected,

        // Common store methods
        getAll,
        onRequest,
        store,
        update,
        remove,
        createFunction: createSemester, // Override with custom create method
        closeFunction,
        edit: editSemester, // Override with custom edit method
        submitFormData: submitSemesterForm, // Override with custom submit method
        confirmDelete,
        fetchPaged,
        clearFunction,
        toggleStatus,
        notifySuccess,
        notifyError,
        jsonToFormData,
        changeStatusOtherColumn,
        isFormValid,
        submitKendoForm,

        // Form constants
        courseTypes,
        calendarTypes,
        years,

        // Custom methods
        loadFormConstants,
        submitSemesterForm,
        formatDateForAPI,
        parseStringToDate,
        editSemester,
        createSemester,
    };
});
