import { defineStore } from 'pinia';
import useCommonStore from '@spa/stores/modules/commonStore.js';
import { ref } from 'vue';

export const useStudentAgentCommissionStore = defineStore('useStudentAgentCommissionStore', () => {
    const storeUrl = ref('v2/tenant/student-agent-commission');
    const commonStoreProps = useCommonStore(storeUrl.value);
    return {
        ...commonStoreProps,
    };
});
