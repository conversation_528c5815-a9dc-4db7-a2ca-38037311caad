import { defineStore } from 'pinia';
import useCommonStore from '@spa/stores/modules/commonStore.js';
import { ref } from 'vue';

export const useStudentPaymentAgentCreditStore = defineStore(
    'useStudentPaymentAgentCreditStore',
    () => {
        const storeUrl = ref('v2/tenant/student-payment-agent-credit');
        const commonStoreProps = useCommonStore(storeUrl.value);
        return {
            ...commonStoreProps,
        };
    }
);
