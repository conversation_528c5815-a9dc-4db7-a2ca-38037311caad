import { defineStore } from 'pinia';
import useCommonStore from '@spa/stores/modules/commonStore.js';
import { ref } from 'vue';

export const useStudentRiskAssessmentSemesterStore = defineStore(
    'useStudentRiskAssessmentSemesterStore',
    () => {
        const storeUrl = ref('v2/tenant/student-risk-assessment-semesters');
        const commonStoreProps = useCommonStore(storeUrl.value);
        return {
            ...commonStoreProps,
        };
    }
);
