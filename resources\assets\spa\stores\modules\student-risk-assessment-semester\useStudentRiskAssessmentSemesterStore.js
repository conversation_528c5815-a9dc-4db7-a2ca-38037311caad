import { defineStore } from 'pinia';
import useCommonStore from '@spa/stores/modules/commonStore.js';
import { ref } from 'vue';
import api from '@spa/services/api.client.js';

export const useStudentRiskAssessmentSemesterStore = defineStore(
    'useStudentRiskAssessmentSemesterStore',
    () => {
        const storeUrl = ref('v2/tenant/student-risk-assessment-semesters');
        const commonStoreProps = useCommonStore(storeUrl.value);

        // Additional state for grouped view
        const viewMode = ref('grouped'); // 'grouped' or 'detailed'
        const selectedStudent = ref(null);
        const studentDetails = ref([]);
        const loadingDetails = ref(false);

        // Override fetchPaged to support grouped view
        const originalFetchPaged = commonStoreProps.fetchPaged;
        const fetchPaged = (callback = () => {}) => {
            if (viewMode.value === 'grouped') {
                fetchGroupedData(callback);
            } else {
                originalFetchPaged(callback);
            }
        };

        // Fetch grouped student data
        const fetchGroupedData = (callback = () => {}) => {
            if (commonStoreProps.enableLoader.value) {
                commonStoreProps.loading.value = true;
            }
            commonStoreProps.contextLoading('fetch-paged', true);

            api.get(`/api/${storeUrl.value}/grouped`, {
                params: {
                    ...commonStoreProps.serverPagination.value,
                    filters: JSON.stringify(commonStoreProps.filters.value),
                },
            })
            .then((response) => {
                commonStoreProps.all.value = response.data;
                commonStoreProps.form_rows.value = _.cloneDeep(response.data);

                // Handle pagination
                if (response.meta && response.meta.total) {
                    commonStoreProps.serverPagination.value.rowsNumber = response.meta.total;
                }

                callback();
            })
            .catch((error) => {
                commonStoreProps.all.value = [];
                commonStoreProps.form_rows.value = [];
                commonStoreProps.serverPagination.value.rowsNumber = 0;
            })
            .finally(() => {
                if (commonStoreProps.enableLoader.value) {
                    commonStoreProps.loading.value = false;
                }
                commonStoreProps.contextLoading('fetch-paged', false);
            });
        };

        // Fetch detailed data for a specific student
        const fetchStudentDetails = async (studentId) => {
            loadingDetails.value = true;
            try {
                const response = await api.get(`/api/${storeUrl.value}/student/${studentId}`);
                studentDetails.value = response.data;
                selectedStudent.value = studentId;
                return response.data;
            } catch (error) {
                console.error('Failed to fetch student details:', error);
                studentDetails.value = [];
                throw error;
            } finally {
                loadingDetails.value = false;
            }
        };

        // Switch between view modes
        const setViewMode = (mode) => {
            viewMode.value = mode;
            if (mode === 'grouped') {
                fetchPaged();
            }
        };

        // Clear student details
        const clearStudentDetails = () => {
            selectedStudent.value = null;
            studentDetails.value = [];
        };

        return {
            ...commonStoreProps,
            viewMode,
            selectedStudent,
            studentDetails,
            loadingDetails,
            fetchPaged, // Override with our custom implementation
            fetchGroupedData,
            fetchStudentDetails,
            setViewMode,
            clearStudentDetails,
        };
    }
);
