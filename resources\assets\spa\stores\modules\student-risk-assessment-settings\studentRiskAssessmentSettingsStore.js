import { defineStore } from 'pinia';
import { ref } from 'vue';
import useCommonStore from '@spa/stores/modules/commonStore.js';
import apiClient from '@spa/services/api.client.js';

export const useStudentRiskAssessmentSettingsStore = defineStore(
    'studentRiskAssessmentSettings',
    () => {
        const storeUrl = ref('v2/tenant/student-risk-assessment-settings');
        const commonStore = useCommonStore(storeUrl.value);

        // Form data with default values
        const formData = ref({
            // Main toggle
            risk_assessment_enabled: true,

            // Category toggles
            attendance_enabled: true,
            moodle_enabled: true,
            results_enabled: true,
            payment_enabled: true,

            // Risk Assessment Type Configuration
            category_low_risk_weeks: 4,
            category_medium_risk_weeks: 2,
            category_high_risk_weeks: 1,
            category_low_risk_email: null,
            category_medium_risk_email: null,
            category_high_risk_email: null,

            // Student Attendance thresholds
            attendance_low_risk: 80,
            attendance_medium_risk: 60,
            attendance_high_risk: 40,

            // Moodle Access thresholds
            moodle_low_risk: 75,
            moodle_medium_risk: 50,
            moodle_high_risk: 25,

            // Student Results thresholds
            results_low_risk: 70,
            results_medium_risk: 50,
            results_high_risk: 30,

            // Student Fee Payment criteria (checkbox selections)
            payment_low_risk_criteria: [],
            payment_medium_risk_criteria: [],
            payment_high_risk_criteria: [],
        });

        // Email templates for dropdowns
        const emailTemplates = ref([]);

        // Loading states
        const isLoading = ref(false);
        const isSubmitting = ref(false);

        // Initialize store
        const initialize = async () => {
            try {
                isLoading.value = true;
                await loadEmailTemplates();
                await loadExistingSettings();
            } catch (error) {
                console.error('Failed to initialize store:', error);
                notifyError('Failed to load settings. Please refresh the page.');
            } finally {
                isLoading.value = false;
            }
        };

        // Load email templates for dropdowns
        const loadEmailTemplates = async () => {
            try {
                const response = await apiClient.get(
                    '/api/v2/tenant/student-risk-assessment-settings/email-templates'
                );

                if (response.success) {
                    emailTemplates.value = response.data || [];
                }
            } catch (error) {
                console.error('Failed to load email templates:', error);
            }
        };

        // Load existing settings
        const loadExistingSettings = async () => {
            try {
                const response = await apiClient.get(
                    '/api/v2/tenant/student-risk-assessment-settings'
                );

                if (response.success && response.data) {
                    // Map API response structure to form data structure
                    const apiData = response.data;

                    // Map main settings
                    if (apiData.riskAssessmentEnabled !== undefined) {
                        formData.value.risk_assessment_enabled = apiData.riskAssessmentEnabled;
                    }
                    if (apiData.lowRiskWeeks !== undefined) {
                        formData.value.category_low_risk_weeks = parseInt(apiData.lowRiskWeeks);
                    }
                    if (apiData.mediumRiskWeeks !== undefined) {
                        formData.value.category_medium_risk_weeks = parseInt(
                            apiData.mediumRiskWeeks
                        );
                    }
                    if (apiData.highRiskWeeks !== undefined) {
                        formData.value.category_high_risk_weeks = parseInt(apiData.highRiskWeeks);
                    }
                    if (apiData.lowRiskEmailTemplate !== undefined) {
                        formData.value.category_low_risk_email = apiData.lowRiskEmailTemplate;
                    }
                    if (apiData.mediumRiskEmailTemplate !== undefined) {
                        formData.value.category_medium_risk_email = apiData.mediumRiskEmailTemplate;
                    }
                    if (apiData.highRiskEmailTemplate !== undefined) {
                        formData.value.category_high_risk_email = apiData.highRiskEmailTemplate;
                    }

                    // Map category parameters if they exist
                    if (response.category_parameters) {
                        const categoryParams = response.category_parameters;

                        // Map attendance parameters
                        if (categoryParams.attendance?.parameters) {
                            const attendanceParams = categoryParams.attendance.parameters;
                            if (attendanceParams.enabled !== undefined) {
                                formData.value.attendance_enabled = attendanceParams.enabled;
                            }
                            if (
                                attendanceParams.low_risk_threshold !== undefined &&
                                attendanceParams.low_risk_threshold !== null
                            ) {
                                formData.value.attendance_low_risk = parseInt(
                                    attendanceParams.low_risk_threshold
                                );
                            }
                            if (
                                attendanceParams.medium_risk_threshold !== undefined &&
                                attendanceParams.medium_risk_threshold !== null
                            ) {
                                formData.value.attendance_medium_risk = parseInt(
                                    attendanceParams.medium_risk_threshold
                                );
                            }
                            if (
                                attendanceParams.high_risk_threshold !== undefined &&
                                attendanceParams.high_risk_threshold !== null
                            ) {
                                formData.value.attendance_high_risk = parseInt(
                                    attendanceParams.high_risk_threshold
                                );
                            }
                        }

                        // Map moodle parameters
                        if (categoryParams.moodle?.parameters) {
                            const moodleParams = categoryParams.moodle.parameters;
                            if (moodleParams.enabled !== undefined) {
                                formData.value.moodle_enabled = moodleParams.enabled;
                            }
                            if (
                                moodleParams.low_risk_threshold !== undefined &&
                                moodleParams.low_risk_threshold !== null
                            ) {
                                formData.value.moodle_low_risk = parseInt(
                                    moodleParams.low_risk_threshold
                                );
                            }
                            if (
                                moodleParams.medium_risk_threshold !== undefined &&
                                moodleParams.medium_risk_threshold !== null
                            ) {
                                formData.value.moodle_medium_risk = parseInt(
                                    moodleParams.medium_risk_threshold
                                );
                            }
                            if (
                                moodleParams.high_risk_threshold !== undefined &&
                                moodleParams.high_risk_threshold !== null
                            ) {
                                formData.value.moodle_high_risk = parseInt(
                                    moodleParams.high_risk_threshold
                                );
                            }
                        }

                        // Map results parameters
                        if (categoryParams.results?.parameters) {
                            const resultsParams = categoryParams.results.parameters;
                            if (resultsParams.enabled !== undefined) {
                                formData.value.results_enabled = resultsParams.enabled;
                            }
                            if (
                                resultsParams.low_risk_threshold !== undefined &&
                                resultsParams.low_risk_threshold !== null
                            ) {
                                formData.value.results_low_risk = parseInt(
                                    resultsParams.low_risk_threshold
                                );
                            }
                            if (
                                resultsParams.medium_risk_threshold !== undefined &&
                                resultsParams.medium_risk_threshold !== null
                            ) {
                                formData.value.results_medium_risk = parseInt(
                                    resultsParams.medium_risk_threshold
                                );
                            }
                            if (
                                resultsParams.high_risk_threshold !== undefined &&
                                resultsParams.high_risk_threshold !== null
                            ) {
                                formData.value.results_high_risk = parseInt(
                                    resultsParams.high_risk_threshold
                                );
                            }
                        }

                        // Map payment parameters
                        if (categoryParams.payment?.parameters) {
                            const paymentParams = categoryParams.payment.parameters;
                            if (paymentParams.enabled !== undefined) {
                                formData.value.payment_enabled = paymentParams.enabled;
                            }
                            if (paymentParams.low_risk_threshold !== undefined) {
                                formData.value.payment_low_risk_criteria = Array.isArray(
                                    paymentParams.low_risk_threshold
                                )
                                    ? paymentParams.low_risk_threshold
                                    : [];
                            }
                            if (paymentParams.medium_risk_threshold !== undefined) {
                                formData.value.payment_medium_risk_criteria = Array.isArray(
                                    paymentParams.medium_risk_threshold
                                )
                                    ? paymentParams.medium_risk_threshold
                                    : [];
                            }
                            if (paymentParams.high_risk_threshold !== undefined) {
                                formData.value.payment_high_risk_criteria = Array.isArray(
                                    paymentParams.high_risk_threshold
                                )
                                    ? paymentParams.high_risk_threshold
                                    : [];
                            }
                        }
                    }
                }
            } catch (error) {
                console.error('Failed to load existing settings:', error);
            }
        };

        // Submit form data
        const submitFormData = async () => {
            try {
                isSubmitting.value = true;
                console.log('formData.value before submit:', formData.value);
                console.log(
                    'category_low_risk_weeks value:',
                    formData.value.category_low_risk_weeks
                );
                console.log(
                    'typeof category_low_risk_weeks:',
                    typeof formData.value.category_low_risk_weeks
                );

                const response = await apiClient.post(
                    '/api/v2/tenant/student-risk-assessment-settings',
                    formData.value
                );

                if (response.success) {
                    return response;
                }
            } catch (error) {
                console.error('Failed to submit form:', error);
            } finally {
                isSubmitting.value = false;
            }
        };

        // Notification helpers
        const notifySuccess = (message) => {
            commonStore.notifySuccess(message);
        };

        const notifyError = (message) => {
            commonStore.notifyError(message);
        };

        // Reset form to defaults
        const resetForm = () => {
            Object.assign(formData.value, {
                risk_assessment_enabled: true,
                attendance_enabled: true,
                moodle_enabled: true,
                results_enabled: true,
                payment_enabled: true,
                category_low_risk_weeks: 4,
                category_medium_risk_weeks: 2,
                category_high_risk_weeks: 1,
                category_low_risk_email: null,
                category_medium_risk_email: null,
                category_high_risk_email: null,
                attendance_low_risk: 80,
                attendance_medium_risk: 60,
                attendance_high_risk: 40,
                moodle_low_risk: 75,
                moodle_medium_risk: 50,
                moodle_high_risk: 25,
                results_low_risk: 70,
                results_medium_risk: 50,
                results_high_risk: 30,
                payment_low_risk_criteria: [],
                payment_medium_risk_criteria: [],
                payment_high_risk_criteria: [],
            });
        };

        return {
            // State
            formData,
            emailTemplates,
            isLoading,
            isSubmitting,
            loading: isSubmitting, // Alias for compatibility

            // Actions
            initialize,
            loadEmailTemplates,
            loadExistingSettings,
            submitFormData,
            resetForm,
            notifySuccess,
            notifyError,
        };
    }
);
