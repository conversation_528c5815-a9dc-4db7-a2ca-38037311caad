import { defineStore } from 'pinia';
import useCommonStore from '@spa/stores/modules/commonStore.js';
import { ref, watch } from 'vue';
import api from '@spa/services/api.client.js';

export const useStudentRiskAssessmentStore = defineStore('useStudentRiskAssessmentStore', () => {
    const storeUrl = ref('v2/tenant/student-risk-assessments');

    // Create our own state instead of using common store's reactive refs
    // This prevents conflicts with common store's watchers
    const serverPagination = ref({
        page: 1,
        rowsNumber: 0,
        sortBy: 'id',
        descending: true,
        rowsPerPage: 25,
    });
    const filters = ref({});
    const loading = ref(false);
    const enableLoader = ref(true);
    const all = ref([]);
    const form_rows = ref([]);
    const selected = ref([]);
    const ctxLoading = ref({});

    // Get only the methods we need from common store (not reactive state)
    const commonStoreMethods = useCommonStore(storeUrl.value);
    const {
        formRef,
        formDialog,
        formData,
        errors,
        statusOptions,
        getAll,
        onRequest,
        store,
        update,
        remove,
        createFunction,
        closeFunction,
        edit,
        submitFormData,
        confirmDelete,
        clearFunction,
        toggleStatus,
        notifySuccess,
        notifyError,
        jsonToFormData,
        changeStatusOtherColumn,
        show,
        showDialog,
        viewFormData,
        clearSelection,
        confirmBulkDelete,
        resetForm,
    } = commonStoreMethods;

    // Additional state for summary data
    const summaryData = ref({
        total: 0,
        high_risk: 0,
        medium_risk: 0,
        low_risk: 0,
    });

    // Context loading helper function
    const contextLoading = (context, val) => {
        if (typeof val !== 'undefined') {
            ctxLoading.value[context] = val;
            return;
        }
        return ctxLoading.value[context];
    };

    // Debounce mechanism to prevent duplicate calls
    let fetchTimeout = null;
    let isCurrentlyFetching = false;

    // Custom fetchPaged implementation that handles summary data
    const fetchPaged = (callback = () => {}) => {
        // If already fetching, ignore this call
        if (isCurrentlyFetching) {
            return;
        }

        // Clear any pending timeout
        if (fetchTimeout) {
            clearTimeout(fetchTimeout);
        }

        // Debounce the fetch call
        fetchTimeout = setTimeout(() => {
            isCurrentlyFetching = true;

            if (enableLoader.value) {
                loading.value = true;
            }
            contextLoading('fetch-paged', true);

            api.get(`/api/` + storeUrl.value, {
                params: {
                    ...serverPagination.value,
                    filters: JSON.stringify(filters.value),
                },
            })
            .then((response) => {
                if (enableLoader.value) {
                    loading.value = false;
                }

                // Handle the data
                all.value = response.data;
                form_rows.value = _.cloneDeep(response.data);

                // Handle pagination
                if (response.meta && response.meta.total) {
                    serverPagination.value.rowsNumber = response.meta.total;
                }

                // Handle summary data
                if (response.summary) {
                    summaryData.value = response.summary;
                }

                callback();
            })
            .catch((error) => {
                all.value = [];
                form_rows.value = [];
                serverPagination.value.rowsNumber = 0;
            })
            .finally(() => {
                if (enableLoader.value) {
                    loading.value = false;
                }
                contextLoading('fetch-paged', false);
                isCurrentlyFetching = false; // Reset fetching flag
            });
        }, 100); // 100ms debounce
    };

    // Watch filters and trigger fetchPaged (replaces the common store's watcher)
    let lastFetchedFilters = null;
    let isInitialized = false;

    watch(
        () => filters.value,
        (newFilters) => {
            const newFiltersStr = JSON.stringify(newFilters);

            // Only fetch if filters actually changed from the last fetch
            // and the store has been properly initialized
            if (lastFetchedFilters !== newFiltersStr && isInitialized) {
                lastFetchedFilters = newFiltersStr;
                fetchPaged();
            } else if (!isInitialized) {
                // Mark as initialized after first filter change
                isInitialized = true;
                lastFetchedFilters = newFiltersStr;
                fetchPaged();
            }
        },
        { deep: true }
    );

    return {
        serverPagination,
        filters,
        loading,
        enableLoader,
        all,
        form_rows,
        formRef,
        formDialog,
        formData,
        errors,
        statusOptions,
        selected,
        getAll,
        onRequest,
        store,
        update,
        remove,
        createFunction,
        closeFunction,
        edit,
        submitFormData,
        confirmDelete,
        clearFunction,
        toggleStatus,
        notifySuccess,
        notifyError,
        jsonToFormData,
        changeStatusOtherColumn,
        ctxLoading,
        contextLoading, // Our own implementation
        show,
        showDialog,
        viewFormData,
        clearSelection,
        confirmBulkDelete,
        resetForm,
        summaryData,
        fetchPaged, // Our custom implementation
    };
});
