import { defineStore } from 'pinia';
import useCommonStore from '@spa/stores/modules/commonStore.js';
import { ref } from 'vue';

export const useStudentRiskAssessmentStore = defineStore('useStudentRiskAssessmentStore', () => {
    const storeUrl = ref('v2/tenant/student-risk-assessments');
    const commonStoreProps = useCommonStore(storeUrl.value);
    return {
        ...commonStoreProps,
    };
});
