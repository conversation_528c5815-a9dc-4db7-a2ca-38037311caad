import { defineStore } from 'pinia';
import useCommonStore from '@spa/stores/modules/commonStore.js';
import { ref } from 'vue';
import api from '@spa/services/api.client.js';

export const useStudentRiskAssessmentStore = defineStore('useStudentRiskAssessmentStore', () => {
    const storeUrl = ref('v2/tenant/student-risk-assessments');
    const commonStoreProps = useCommonStore(storeUrl.value);

    // Additional state for summary data
    const summaryData = ref({
        total: 0,
        high_risk: 0,
        medium_risk: 0,
        low_risk: 0,
    });

    // Override fetchPaged to handle summary data
    const originalFetchPaged = commonStoreProps.fetchPaged;
    const fetchPaged = (callback = () => {}) => {
        if (commonStoreProps.enableLoader.value) {
            commonStoreProps.loading.value = true;
        }
        commonStoreProps.contextLoading('fetch-paged', true);

        api.get(`/api/` + storeUrl.value, {
            params: {
                ...commonStoreProps.serverPagination.value,
                filters: JSON.stringify(commonStoreProps.filters.value),
            },
        })
        .then((response) => {
            if (commonStoreProps.enableLoader.value) {
                commonStoreProps.loading.value = false;
            }

            // Handle the data
            commonStoreProps.all.value = response.data;
            commonStoreProps.form_rows.value = _.cloneDeep(response.data);

            // Handle pagination
            if (response.meta && response.meta.total) {
                commonStoreProps.serverPagination.value.rowsNumber = response.meta.total;
            }

            // Handle summary data
            if (response.summary) {
                summaryData.value = response.summary;
            }

            callback();
        })
        .catch((error) => {
            commonStoreProps.all.value = [];
            commonStoreProps.form_rows.value = [];
            commonStoreProps.serverPagination.value.rowsNumber = 0;
        })
        .finally(() => {
            if (commonStoreProps.enableLoader.value) {
                commonStoreProps.loading.value = false;
            }
            commonStoreProps.contextLoading('fetch-paged', false);
        });
    };

    return {
        ...commonStoreProps,
        summaryData,
        fetchPaged, // Override with our custom implementation
    };
});
