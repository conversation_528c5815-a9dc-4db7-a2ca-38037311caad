import { defineStore } from 'pinia';
import useCommonStore from '@spa/stores/modules/commonStore.js';
import { ref } from 'vue';

export const useStudentInitialPaymentTransactionStore = defineStore(
    'useStudentInitialPaymentTransactionStore',
    () => {
        const storeUrl = ref('v2/tenant/studentinitialpaymenttransaction');
        const commonStoreProps = useCommonStore(storeUrl.value);
        return {
            ...commonStoreProps,
        };
    }
);
