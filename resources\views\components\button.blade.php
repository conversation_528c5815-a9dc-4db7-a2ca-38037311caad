@props(['loading' => false, 'target' => false])

<button 
{{ $attributes->twMerge('tw-btn-primary h-9')}}
{{ $attributes->merge([
    'type' => 'submit',
    /* 'class' =>
        'inline-flex items-center justify-center rounded-lg bg-primary-blue-500 px-8 h-9 shadow hover:shadow-lg focus:ring-2 focus:ring-primary-blue-600 focus:ring-offset-2 focus:ring-offset-white text-white', */
]) }}
        {{--  class="{{ $attributes['class'] ? $attributes['class'] : 'inline-flex items-center justify-center rounded-lg bg-primary-blue-500 px-8 h-9 shadow hover:shadow-lg focus:ring-2 focus:ring-primary-blue-600 focus:ring-offset-2 focus:ring-offset-white text-white' }}" --}}
        wire:loading.class="disabled">
    @php
        if (!$target) {
            $target = $attributes->wire('click')->value();
        }
    @endphp
    @if ($loading && $target)
        <div wire:loading.remove
             wire:target="{{ $target }}">{{ $slot }}</div>
        <div wire:loading
             wire:target="{{ $target }}">{{ $loading }}</div>
    @else
        {{ $slot }}
    @endif
</button>
