@props([
    'layout' => 'default',
    'maxWidth' => '1920px',
    'padding' => '0',
    'gap' => '1.5rem',
    'title' => '',
    'showHeader' => false,
    'showTabs' => false,
    'showFooter' => false,
    'responsive' => true,
])

@php
    $containerClasses = collect([
        'flexible-container',
        "layout-{$layout}",
        $responsive ? 'responsive' : '',
    ])->filter()->implode(' ');
    $maxWidth = $responsive ? $maxWidth : '100%';
@endphp

<div {{$attributes->twMerge($containerClasses) }}>
    @if($showTabs || isset($tabs))
        <div {{$attributes->twMergeFor('tabs', 'flexible-tabs')}}>
            {{ $tabs }}
        </div>
    @endisset

    @if ($showHeader || isset($header))
        <header {{$attributes->twMergeFor('header', 'flexible-header') }}>
            @isset($header)
                {{ $header }}
            @else
                @if($title)
                    <h1 {{$attributes->twMergeFor('title', 'flexible-title') }}>{{ $title }}</h1>
                @endif
            @endisset
        </header>
    @endif

    <main {{$attributes->twMergeFor('main', 'flex-content mx-auto')->merge(['style' =>
        "max-width: {$maxWidth}; padding: {$padding}; gap: {$gap};"
      ]) }}>
        <article {{$attributes->twMergeFor('content', 'flexible-content')}}>
            {{ $slot }}
        </article>

        @isset($secondary)
            <aside {{$attributes->twMergeFor('secondary', 'flexible-secondary')}}>
                {{ $secondary }}
            </aside>
        @endisset
    </main>

    @if ($showFooter || isset($footer))
        <footer {{ $attributes->twMergeFor('footer', 'flexible-footer') }}>
            {{ $footer }}
        </footer>
    @endif
</div>