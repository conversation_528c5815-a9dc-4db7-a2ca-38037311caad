@props([
'overlay' => false
])

<div {{$attributes->twMerge('tw-ajax-loader h-no-tabs tw-ajax-loader--riskmatrix px-8 py-6 bg-gray-100
    relative hidden')}}>
    <div class="space-y-6 py-4 px-6 bg-white shadow rounded-md w-full">
        <div class="flex space-x-4 items-center justify-between w-full">
            <div class="flex items-center justify-start">
                <p class="text-lg leading-7 font-normal text-gray-900">
                <div class="w-32 h-7 bg-gray-200 rounded animate-pulse"></div>
                </p>
            </div>
            <div class="h-full">
                <div class="w-20 h-7 bg-gray-200 rounded animate-pulse"></div>
            </div>
        </div>
        <div class="w-full h-12 bg-gray-200 rounded animate-pulse"></div>
        <div class="flex justify-between mb-4">
            <x-v2.skeleton.buttongroup :autoHide="false" count="4" />
            <x-v2.skeleton.buttongroup :autoHide="false" count="1" />
        </div>
        <div class="flex">
            <x-v2.skeleton.buttongroup :autoHide="false" count="1" class:button="w-[325px]" />
        </div>
        <div class=" w-10/12 space-y-4">
            <div class="w-[300px] h-5 bg-gray-200 rounded animate-pulse"></div>
            <div class="flex space-x-2 items-start justify-start">
                <div class="w-10 h-10 bg-gray-200 rounded-full animate-pulse"></div>
                <div class="col-span-2 flex-col flex justify-center gap-2">
                    <div class="w-[300px] h-5 bg-gray-200 rounded animate-pulse"></div>
                    <div class="w-[180px] h-4 bg-gray-200 rounded animate-pulse"></div>
                    <div class="w-[280px] h-4 bg-gray-200 rounded animate-pulse"></div>
                </div>
            </div>
            <div class="flex space-x-2 items-start justify-start">
                <div class="w-10 h-10 bg-gray-200 rounded-full animate-pulse"></div>
                <div class="col-span-2 flex-col flex justify-center gap-2">
                    <div class="w-[300px] h-5 bg-gray-200 rounded animate-pulse"></div>
                    <div class="w-[180px] h-4 bg-gray-200 rounded animate-pulse"></div>
                    <div class="w-[280px] h-4 bg-gray-200 rounded animate-pulse"></div>
                </div>
            </div>
        </div>
    </div>
    @if($overlay)
    <x-v2.loader :showText="false" class="absolute bg-white/45 w-full h-full inset-0 p-0 h-no-tabs"
        class:spinner="w-12 h-12" />
    @endif
</div>