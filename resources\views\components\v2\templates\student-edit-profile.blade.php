<div class="grid grid-cols-12 gap-6 relative" x-data="scrollToSection()" x-init="init()">
    <div class="col-span-12 md:col-span-3 hidden md:block">
        <x-v2.card class="sticky top-6 py-4 px-0">
            <x-slot name="content">
                <ul class="item-list">
                    <li class="item-list__item font-medium"><a href="#"
                            :class="activeSection === 'editProfileDiv' ?
                                'border-primary-blue-500 text-primary-blue-500 bg-primary-blue-50' :
                                'border-transparent'"
                            class="flex px-3 py-2 border-l-4" x-on:click="scrollTo('editProfileDiv')">Edit
                            Profile</a></li>
                    <li class="item-list__item font-medium"><a href="#"
                                                               :class="activeSection === 'emergencyContactDiv' ?
                                'border-primary-blue-500 text-primary-blue-500 bg-primary-blue-50' :
                                'border-transparent'"
                                                               class="flex px-3 py-2 border-l-4" x-on:click="scrollTo('emergencyContactDiv')">Emergency Contact</a></li>
                    <li class="item-list__item font-medium"><a href="#"
                            :class="activeSection === 'residentialAddressDiv' ?
                                'border-primary-blue-500 text-primary-blue-500 bg-primary-blue-50' :
                                'border-transparent'"
                            class="flex px-3 py-2 border-l-4" x-on:click="scrollTo('residentialAddressDiv')">Australian
                            Residential
                            Address</a></li>
                    <li class="item-list__item font-medium"><a href="#"
                            :class="activeSection === 'usiDiv' ?
                                'border-primary-blue-500 text-primary-blue-500 bg-primary-blue-50' :
                                'border-transparent'"
                            class="flex px-3 py-2 border-l-4" x-on:click="scrollTo('usiDiv')">USI:
                            Unique Student Identifier</a></li>
                    <li class="item-list__item font-medium"><a href="#"
                            :class="activeSection === 'changePasswordDiv' ?
                                'border-primary-blue-500 text-primary-blue-500 bg-primary-blue-50' :
                                'border-transparent'"
                            class="flex px-3 py-2 border-l-4" x-on:click="scrollTo('changePasswordDiv')">Change
                            Password</a></li>
                </ul>
            </x-slot>
        </x-v2.card>
    </div>
    <div class="col-span-12 space-y-6 md:col-span-9">
        <x-v2.card class="scrolling-div scroll-mt-6 p-0 border border-gray-200" id="editProfileDiv">
            <x-slot name="title" class="flex items-center justify-between px-6 py-4 border-b border-gray-200">
                <h2 class="text-base md:text-lg font-medium">Edit Profile</h2>
                <div class="flex items-center gap-4 flex-10 justify-end">
                    <button class="btn-secondary min-w-fit" id="profileHistoryBtn"><span>Profile History</span></button>
                    <button class="btn-tertiary" x-on:click="editSection('editProfileDiv')"
                        x-show="!isEditing('editProfileDiv')"><span>Edit</span></button>
                </div>
            </x-slot>
            <x-slot name="content" class="p-6 space-y-0">
                <div id="studentDetailDiv" class="space-y-6" style="display: none;"
                    x-show="isEditing('editProfileDiv')">
                    <div class="space-y-1">
                        <p class="text-sm font-medium leading-5 text-gray-700">Profile Picture</p>
                        <div class="inline-flex gap-4 items-center">
                            <div class="w-16 h-16 cursor-pointer display_picture" id="display_picture">
                                <?php if ($studentDetail['profile_pic'] == '') {
                                    $fullName = strtoupper($studentDetail['full_name']);
                                    $nameParts = preg_split('/\s+/', $fullName);
                                    $shortName = substr($nameParts[0], 0, 1).substr($nameParts[1], 0, 1); ?>
                                <div class='flex user-profile-pic w-16 h-16 !rounded-lg bg-blue-500 items-center'>
                                    <span
                                        class='text-2xl flex justify-center items-center leading-6 px-1 w-full'>{{ $shortName }}</span>
                                </div>
                                <?php } else { ?>
                                <div class="w-16 h-16 !rounded-lg">
                                    <img class="w-16 h-16 !rounded-lg object-cover object-top" src="{{ $studentDetail['profile_pic'] }}" />
                                </div>
                                <?php } ?>
                            </div>
                            <div class="flex space-x-1 items-center justify-start upload-wrapper">
                                <!-- <input type="file" name="file" id="upload_picture" class="hidden" />
                                <label for="upload_picture" class="btn-secondary min-w-fit upload_picture textt-xs">Upload</label> -->
                                <input type="file" class="hidden" name="files" id="stud_profile_pic" />
                                <label for="stud_profile_pic"
                                    class="flex justify-center px-3 py-2 border border-gray-300 shadow rounded-lg text-sm font-normal leading-5 text-gray-700 cursor-pointer"><img
                                        src="{{ asset('v2/img/upload.svg') }}" class="mr-2" alt="" />Upload
                                    Photo</label>
                            </div>
                        </div>
                    </div>
                    <input type="hidden" id="student_id" value="{{ $studentDetail['id'] }}" name="student_id">

                    <form id="studentDetailForm" class="tw-kendo-form w-full max-w-2xl tw-kendo-form__max-w">
                    </form>
                </div>
                <div class="space-y-6" x-show="!isEditing('editProfileDiv')">
                    <div class="space-y-1">
                        <p class="text-sm font-medium leading-5 text-gray-700">Profile Picture</p>
                        <div class="inline-flex gap-4 items-center">
                            <div class="w-16 h-16 cursor-pointer display_picture" id="display_picture">
                                <?php if ($studentDetail['profile_pic'] == '') {
                                    $fullName = strtoupper($studentDetail['full_name']);
                                    $nameParts = preg_split('/\s+/', $fullName);
                                    $shortName = substr($nameParts[0], 0, 1).substr($nameParts[1], 0, 1); ?>
                                <div class='flex user-profile-pic w-16 h-16 !rounded-lg bg-blue-500 items-center'>
                                    <span
                                        class='text-2xl flex justify-center items-center leading-6 px-1 w-full'>{{ $shortName }}</span>
                                </div>
                                <?php } else { ?>
                                <div class="w-16 h-16 rounded-lg">
                                    <img class="w-16 h-16 rounded-lg object-cover object-top"
                                        src="{{ $studentDetail['profile_pic'] }}" />
                                </div>
                                <?php } ?>

                            </div>
                            <div class="space-y-1">
                                <p class="text-base font-medium text-gray-700 full_name_value">
                                    {{ $studentDetail['full_name'] }}</p>
                                <p class="text-xs font-medium text-gray-500 generated_stud_id_value">ID:
                                    {{ $studentDetail['generated_stud_id'] }}</p>
                            </div>
                        </div>
                    </div>
                    <div id="studentDetailInfo">
                        <x-v2.label-value class:value="font-normal"
                            class="grid-cols-2 lg:grid-cols-[250px_1fr] items-center full_name_value" label="Name">
                            {{ $studentDetail['full_name'] }}
                        </x-v2.label-value>
                        {{-- <x-v2.label-value class:value="font-normal"
                            class="grid-cols-2 lg:grid-cols-[250px_1fr] items-center generated_stud_id_value"
                            label="Username">
                            <span class="text-gray-7000">{{ $studentDetail['generated_stud_id'] }}</span>
                        </x-v2.label-value> --}}
                        <x-v2.label-value class:value="font-normal"
                            class="grid-cols-2 lg:grid-cols-[250px_1fr] items-center email_value" label="Email Address">
                            <span class="text-gray-7000">{{ $studentDetail['email'] }}</span>
                        </x-v2.label-value>
                        <x-v2.label-value class:value="font-normal"
                            class="grid-cols-2 lg:grid-cols-[250px_1fr] items-center optional_email_value"
                            label="Optional Email">
                            <span class="text-gray-7000">{{ $studentDetail['optional_email'] }}</span>
                        </x-v2.label-value>
                        <x-v2.label-value class:value="font-normal"
                            class="grid-cols-2 lg:grid-cols-[250px_1fr] items-center current_mobile_phone_value"
                            label="Mobile No.">
                            <span class="text-gray-7000">{{ $studentDetail['current_mobile_phone'] }}</span>
                        </x-v2.label-value>
                    </div>
                </div>
            </x-slot>
        </x-v2.card>
        <x-v2.card class="scrolling-div scroll-mt-6 p-0 border border-gray-200" id="emergencyContactDiv">
            <x-slot name="title" class="flex items-center justify-between px-6 py-4 border-b border-gray-200">
                <h2 class="text-base md:text-lg font-medium">Emergency Contact</h2>
                <div class="flex items-center gap-4 flex-10 justify-end">
                    <button class="btn-secondary min-w-fit" id="emergencyDetailsHistoryBtn"><span>Emergency Contact History</span></button>
                    <button class="btn-tertiary" x-on:click="editSection('emergencyContactDiv')"
                            x-show="!isEditing('emergencyContactDiv')"><span>Edit</span></button>
                </div>
            </x-slot>
            <x-slot name="content" class="p-6 space-y-0">
                <div id="emergencyContactDiv" class="space-y-6" style="display: none;"
                     x-show="isEditing('emergencyContactDiv')">
                    <input type="hidden" id="student_id" value="{{ $studentDetail['id'] }}" name="student_id">

                    <form id="emergencyContactForm" class="tw-kendo-form w-full max-w-2xl tw-kendo-form__max-w">
                    </form>
                </div>
                <div class="space-y-6" x-show="!isEditing('emergencyContactDiv')">
                    <div id="studentDetailInfo">
                        <x-v2.label-value class:value="font-normal"
                                          class="grid-cols-2 lg:grid-cols-[250px_1fr] items-center email_value" label="Contact Person">
                            <span class="text-gray-7000">{{ $studentDetail['emergency_contact_person'] }}</span>
                        </x-v2.label-value>
                        <x-v2.label-value class:value="font-normal"
                                          class="grid-cols-2 lg:grid-cols-[250px_1fr] items-center relationship_value" label="Relationship">
                            {{ $studentDetail['emergency_relationship'] }}
                        </x-v2.label-value>
                        <x-v2.label-value class:value="font-normal"
                                          class="grid-cols-2 lg:grid-cols-[250px_1fr] items-center current_mobile_phone_value"
                                          label="Phone">
                            <span class="text-gray-7000">{{ $studentDetail['emergency_phone'] }}</span>
                        </x-v2.label-value>
                        <x-v2.label-value class:value="font-normal"
                                          class="grid-cols-2 lg:grid-cols-[250px_1fr] items-center email_value"
                                          label="Email">
                            <span class="text-gray-7000">{{ $studentDetail['emergency_email'] ?? 'N/A' }}</span>
                        </x-v2.label-value>
                        <x-v2.label-value class:value="font-normal"
                                          class="grid-cols-2 lg:grid-cols-[250px_1fr] items-center address_value"
                                          label="Address">
                            <span class="text-gray-7000">{{ $studentDetail['emergency_address'] ?? 'N/A' }}</span>
                        </x-v2.label-value>

                    </div>
                </div>
            </x-slot>
        </x-v2.card>
        <x-v2.card class="scrolling-div scroll-mt-6 p-0 border border-gray-200" id="residentialAddressDiv">
            <x-slot name="title" class="flex items-center justify-between px-6 py-4 border-b border-gray-200">
                <h2 class="text-base md:text-lg font-medium">Australian Residential Address</h2>
                <div class="flex items-center gap-4 flex-10 justify-end">
                    <button class="btn-secondary min-w-fit" id="addressHistoryBtn"><span>Address
                            History</span></button>
                    <button class="btn-tertiary" x-on:click="editSection('residentialAddressDiv')"
                        x-show="!isEditing('residentialAddressDiv')"><span>Edit</span></button>
                </div>
            </x-slot>
            <x-slot name="content" class="p-6 space-y-0">
                <div id="residentialAddressDetailDiv" style="display: none;"
                    x-show="isEditing('residentialAddressDiv')">
                    <input type="hidden" id="student_id" value="{{ $studentDetail['id'] }}" name="student_id">

                    <form id="residentialAddressForm" method="post"
                        class="tw-kendo-form w-full max-w-2xl tw-kendo-form__max-w">
                    </form>
                </div>
                <div x-show="!isEditing('residentialAddressDiv')">
                    <div id="residentialAddressInfo">
                        <x-v2.label-value class:value="font-normal"
                            class="grid-cols-2 lg:grid-cols-[250px_1fr] items-center county_name_value"
                            label="Country">
                            <span class="text-gray-7000">{{ $studentDetail['country_name'] }}</span>
                        </x-v2.label-value>
                        <x-v2.label-value class:value="font-normal"
                            class="grid-cols-2 lg:grid-cols-[250px_1fr] items-center current_building_name_value"
                            label="Building/Property Name">
                            <span class="text-gray-7000">{{ $studentDetail['current_building_name'] }}</span>
                        </x-v2.label-value>
                        <x-v2.label-value class:value="font-normal"
                            class="grid-cols-2 lg:grid-cols-[250px_1fr] items-center current_unit_detail_value"
                            label="Flat/Unit">
                            <span class="text-gray-7000">{{ $studentDetail['current_unit_detail'] }}</span>
                        </x-v2.label-value>
                        <x-v2.label-value class:value="font-normal"
                            class="grid-cols-2 lg:grid-cols-[250px_1fr] items-center current_street_no_value"
                            label="Street Number">
                            <span class="text-gray-7000">{{ $studentDetail['current_street_no'] }}</span>
                        </x-v2.label-value>
                        <x-v2.label-value class:value="font-normal"
                            class="grid-cols-2 lg:grid-cols-[250px_1fr] items-center current_street_no_value"
                            label="Street name">
                            <span class="text-gray-7000">{{ $studentDetail['current_street_name'] }}</span>
                        </x-v2.label-value>
                        <x-v2.label-value class:value="font-normal"
                            class="grid-cols-2 lg:grid-cols-[250px_1fr] items-center current_city_value"
                            label="City/Town/suburb">
                            <span class="text-gray-7000">{{ $studentDetail['current_city'] }}</span>
                        </x-v2.label-value>
                        <x-v2.label-value class:value="font-normal"
                            class="grid-cols-2 lg:grid-cols-[250px_1fr] items-center current_state_value"
                            label="State/Province">
                            <span class="text-gray-7000">{{ $studentDetail['current_state'] }} </span>
                        </x-v2.label-value>
                        <x-v2.label-value class:value="font-normal"
                            class="grid-cols-2 lg:grid-cols-[250px_1fr] items-center current_street_no_value"
                            label="Post Code">
                            <span class="text-gray-7000">{{ $studentDetail['current_postcode'] }}</span>
                        </x-v2.label-value>
                    </div>
                </div>
            </x-slot>
        </x-v2.card>
        <x-v2.card class="scrolling-div scroll-mt-6 p-0 border border-gray-200" id="usiDiv">
            <x-slot name="title" class="flex items-center justify-between px-6 py-4 border-b border-gray-200">
                <h2 class="text-base md:text-lg font-medium">USI: Unique Student Identifier</h2>
                @if (!$studentDetail['is_usi_verified'])
                    <button class="btn-tertiary" x-on:click="editSection('usiDiv')"
                        x-show="!isEditing('usiDiv')"><span>Edit</span></button>
                @endif
            </x-slot>
            <x-slot name="content" class="p-6 space-y-0">
                <p class="text-gray-600 mb-4">You may already have a USI if you have done any nationally recognised
                    training,
                    which could
                    include training at work,
                    completing a first aid course or RSA (Responsible Service of Alcohol) course, getting a white card,
                    or studying at a
                    TAFE or training organisation. It is important that you try to find out whether you already have a
                    USI before
                    attempting
                    to create a new one. You should not have more than one USI. To check if you already have a USI, use
                    the 'Forgotten
                    USI'
                    link on the USI website at <a href="https://www.usi.gov.au/faqs/i-have-forgotten-my-usi/"
                        class="text-primary-blue-500 hover:underline"
                        target="_blank">https://www.usi.gov.au/faqs/i-have-forgotten-my-usi/.</a>
                </p>
                @if (!$studentDetail['is_usi_verified'])
                    <div class="grid grid-cols-2 max-md:grid-cols-1 gap-6 max-w-2xl" x-show="!isEditing('usiDiv')">
                        <div class="space-y-1">
                            <p class="text-gray-900 text-sm font-medium">USI</p>
                            <div class="k-input k-textbox k-input-solid k-input-md k-rounded-md !bg-gray-50">
                                <span class="pl-1 usiValueFront"></span>
                            </div>
                            <span class="text-xs text-gray-600">Enter your 10-digit alphanumeric USI</span>
                            <div class="mb-4">
                                <div
                                    class="tw-highlightbox border flex items-center p-4 gap-3 border-red-500 bg-red-50 text-red-500 rounded-md">
                                    <span class="tw-highlightbox__icon">
                                        <svg width="20" height="20" viewBox="0 0 20 20" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path fill-rule="evenodd" clip-rule="evenodd"
                                                d="M10 18C14.4183 18 18 14.4183 18 10C18 5.58172 14.4183 2 10 2C5.58172 2 2 5.58172 2 10C2 14.4183 5.58172 18 10 18ZM8.70711 7.29289C8.31658 6.90237 7.68342 6.90237 7.29289 7.29289C6.90237 7.68342 6.90237 8.31658 7.29289 8.70711L8.58579 10L7.29289 11.2929C6.90237 11.6834 6.90237 12.3166 7.29289 12.7071C7.68342 13.0976 8.31658 13.0976 8.70711 12.7071L10 11.4142L11.2929 12.7071C11.6834 13.0976 12.3166 13.0976 12.7071 12.7071C13.0976 12.3166 13.0976 11.6834 12.7071 11.2929L11.4142 10L12.7071 8.70711C13.0976 8.31658 13.0976 7.68342 12.7071 7.29289C12.3166 6.90237 11.6834 6.90237 11.2929 7.29289L10 8.58579L8.70711 7.29289Z"
                                                fill="currentColor"></path>
                                        </svg>
                                    </span>
                                    <div class="tw-highlightbox__content">
                                        Invalid USI. Please contact your college.
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div id="saveUSIDetailsDiv" x-show="isEditing('usiDiv')">
                        <input type="hidden" id="student_id" value="{{ $studentDetail['id'] }}" name="student_id">
                        <input type="hidden" id="first_name" value="{{ $studentDetail['first_name'] }}"
                            name="first_name">
                        <input type="hidden" id="family_name" value="{{ $studentDetail['family_name'] }}"
                            name="family_name">
                        <input type="hidden" id="DOB" value="{{ $studentDetail['DOB'] }}" name="DOB">

                        <form id="usiDetailsForm" class="tw-kendo-form w-full max-w-2xl tw-kendo-form__max-w">
                        </form>

                    </div>
                @else
                    <div>
                        <x-v2.label-value class:value="font-normal"
                            class="grid-cols-2 lg:grid-cols-[250px_1fr] items-center shadow-none USI_value"
                            label="USI Number">
                            <div class="flex items-center gap-1">
                                <span class="text-gray-7000">{{ $studentDetail['USI'] }}</span>
                                <x-v2.badge variant="success" class="gap-1">
                                    <span class="text-green-500">
                                        <svg width="16" height="16" viewBox="0 0 16 16" fill="none"
                                            xmlns="http://www.w3.org/2000/svg">
                                            <path
                                                d="M11.3349 2.06544L11.4114 2.21801L11.9878 3.75099C12.0331 3.87171 12.1284 3.96697 12.2491 4.01226L13.7292 4.56753C14.3787 4.81121 14.7277 5.50524 14.5546 6.1619L14.513 6.29259L13.8244 7.81541C13.7711 7.9328 13.7711 8.06751 13.8244 8.1849L14.4783 9.62411C14.7653 10.2557 14.5213 10.9932 13.9346 11.3352L13.7821 11.4116L12.2491 11.9881C12.1284 12.0333 12.0331 12.1286 11.9878 12.2493L11.4325 13.7294C11.1889 14.3789 10.4948 14.7279 9.83817 14.5549L9.70748 14.5132L8.18466 13.8246C8.06727 13.7713 7.93256 13.7713 7.81517 13.8246L6.37596 14.4786C5.74439 14.7655 5.00683 14.5216 4.66489 13.9349L4.58847 13.7823L4.01202 12.2493C3.96672 12.1286 3.87147 12.0333 3.75075 11.9881L2.27067 11.4328C1.62117 11.1891 1.27215 10.4951 1.44522 9.83842L1.48686 9.70773L2.17543 8.1849C2.22877 8.06751 2.22877 7.9328 2.17543 7.81541L1.5215 6.3762C1.23453 5.74463 1.47849 5.00708 2.0652 4.66513L2.21776 4.58871L3.75075 4.01226C3.87147 3.96697 3.96672 3.87171 4.01202 3.75099L4.56729 2.27092C4.81096 1.62141 5.505 1.27239 6.16165 1.44546L6.29234 1.48711L7.81517 2.17567C7.93256 2.22901 8.06727 2.22901 8.18466 2.17567L9.62387 1.52174C10.2554 1.23477 10.993 1.47874 11.3349 2.06544ZM10.1638 5.70607L6.97816 9.34681L5.81571 8.18436C5.6413 8.00995 5.35853 8.00995 5.18412 8.18436C5.00971 8.35877 5.00971 8.64154 5.18412 8.81595L6.68412 10.316C6.86679 10.4986 7.16589 10.4887 7.33602 10.2942L10.836 6.29425C10.9984 6.10862 10.9796 5.82648 10.794 5.66405C10.6084 5.50163 10.3262 5.52044 10.1638 5.70607Z"
                                                fill="currentColor" />
                                        </svg>
                                    </span>
                                    <span class="text-green-800">Verified</span>
                                </x-v2.badge>
                            </div>
                        </x-v2.label-value>
                    </div>
                @endif
            </x-slot>
        </x-v2.card>
        <x-v2.card class="scrolling-div scroll-mt-6 p-0 border border-gray-200" id="changePasswordDiv">
            <x-slot name="title" class="flex items-center justify-between px-6 py-4 border-b border-gray-200">
                <h2 class="text-base md:text-lg font-medium">Change Password</h2>
                <button id="changePasswordEditBtn" class="btn-tertiary" x-on:click="editSection('changePasswordDiv')"
                    x-show="!isEditing('changePasswordDiv')"><span>Edit</span></button>
            </x-slot>
            <x-slot name="content" class="p-6 space-y-0">
                <div :class="!isEditing('changePasswordDiv') ? 'tw-disable-password' : 'tw-enable-password'">
                    <input hidden name="user_id" value="{{ $userId }}" id="user_id">
                    <form id="passwordForm" class="tw-kendo-form w-full tw-kendo-form__max-w">
                    </form>
                </div>
            </x-slot>
        </x-v2.card>
    </div>
</div>

<script>
    function scrollToSection() {
        return {
            activeSection: 'editProfileDiv',
            editingSection: null,
            isDisabled: true,

            init() {
                this.updateActiveSection();

                window.addEventListener('formSubmitted', (event) => {
                    const {
                        sectionId
                    } = event.detail;
                    this.cancelEdit(sectionId);
                });

                // Create an IntersectionObserver to watch the sections
                const observer = new IntersectionObserver((entries) => {
                    entries.forEach(entry => {
                        if (entry.isIntersecting) {
                            this.activeSection = entry.target.id;
                        }
                    });
                }, {
                    threshold: 0.5
                });

                document.querySelectorAll('.scrolling-div').forEach(section => {
                    observer.observe(section);

                    window.addEventListener('scroll', () => {
                        this.updateActiveSection()
                    })
                });
            },

            scrollTo(sectionId) {
                setTimeout(() => {
                    this.activeSection = sectionId;
                }, 500);

                const sectionElement = document.getElementById(sectionId);

                if (sectionElement) {
                    sectionElement.scrollIntoView({
                        behavior: 'smooth'
                    });
                }
            },

            updateActiveSection() {
                const sections = document.querySelectorAll('.scrolling-div');
                const scrollPosition = window.scrollY + window.innerHeight;


                sections.forEach(section => {
                    const sectionTop = section.offsetTop;
                    const sectionBottom = sectionTop + section.offsetHeight / 2;

                    if (scrollPosition >= sectionTop && scrollPosition < sectionBottom) {
                        this.activeSection = section.id;
                    }
                });
            },

            isEditing(id) {
                return this.editingSection === id;
            },

            editSection(id) {
                this.editingSection = id;
            },
            cancelEdit(id) {
                this.editingSection = null;
            },
            saveChanges(id) {
                this.cancelEdit(id);
            },
        };
    }
</script>

<script id="usiErrorAlert" type="text/html">
    <x-v2.highlight-box variant="error">
        <x-slot name="icon">
            <svg width="20" height="20" viewBox="0 0 20 20" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd"
                    d="M10 18C14.4183 18 18 14.4183 18 10C18 5.58172 14.4183 2 10 2C5.58172 2 2 5.58172 2 10C2 14.4183 5.58172 18 10 18ZM8.70711 7.29289C8.31658 6.90237 7.68342 6.90237 7.29289 7.29289C6.90237 7.68342 6.90237 8.31658 7.29289 8.70711L8.58579 10L7.29289 11.2929C6.90237 11.6834 6.90237 12.3166 7.29289 12.7071C7.68342 13.0976 8.31658 13.0976 8.70711 12.7071L10 11.4142L11.2929 12.7071C11.6834 13.0976 12.3166 13.0976 12.7071 12.7071C13.0976 12.3166 13.0976 11.6834 12.7071 11.2929L11.4142 10L12.7071 8.70711C13.0976 8.31658 13.0976 7.68342 12.7071 7.29289C12.3166 6.90237 11.6834 6.90237 11.2929 7.29289L10 8.58579L8.70711 7.29289Z"
                    fill="currentColor" />
            </svg>
        </x-slot>
        <x-slot name="content">
            #: message #
        </x-slot>
    </x-v2.highlight-box>
</script>

<script id="usiValidDiv" type="text/html">
    <x-v2.label-value class:value="font-normal"
        class="grid-cols-2 lg:grid-cols-[250px_1fr] items-center shadow-none USI_value" label="USI Number">
        <div class="flex items-center gap-1">
            <span class="text-gray-7000 usiNumber">#: usi #</span>
            <x-v2.badge variant="success" class="gap-1">
                <span class="text-green-500">
                    <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path
                            d="M11.3349 2.06544L11.4114 2.21801L11.9878 3.75099C12.0331 3.87171 12.1284 3.96697 12.2491 4.01226L13.7292 4.56753C14.3787 4.81121 14.7277 5.50524 14.5546 6.1619L14.513 6.29259L13.8244 7.81541C13.7711 7.9328 13.7711 8.06751 13.8244 8.1849L14.4783 9.62411C14.7653 10.2557 14.5213 10.9932 13.9346 11.3352L13.7821 11.4116L12.2491 11.9881C12.1284 12.0333 12.0331 12.1286 11.9878 12.2493L11.4325 13.7294C11.1889 14.3789 10.4948 14.7279 9.83817 14.5549L9.70748 14.5132L8.18466 13.8246C8.06727 13.7713 7.93256 13.7713 7.81517 13.8246L6.37596 14.4786C5.74439 14.7655 5.00683 14.5216 4.66489 13.9349L4.58847 13.7823L4.01202 12.2493C3.96672 12.1286 3.87147 12.0333 3.75075 11.9881L2.27067 11.4328C1.62117 11.1891 1.27215 10.4951 1.44522 9.83842L1.48686 9.70773L2.17543 8.1849C2.22877 8.06751 2.22877 7.9328 2.17543 7.81541L1.5215 6.3762C1.23453 5.74463 1.47849 5.00708 2.0652 4.66513L2.21776 4.58871L3.75075 4.01226C3.87147 3.96697 3.96672 3.87171 4.01202 3.75099L4.56729 2.27092C4.81096 1.62141 5.505 1.27239 6.16165 1.44546L6.29234 1.48711L7.81517 2.17567C7.93256 2.22901 8.06727 2.22901 8.18466 2.17567L9.62387 1.52174C10.2554 1.23477 10.993 1.47874 11.3349 2.06544ZM10.1638 5.70607L6.97816 9.34681L5.81571 8.18436C5.6413 8.00995 5.35853 8.00995 5.18412 8.18436C5.00971 8.35877 5.00971 8.64154 5.18412 8.81595L6.68412 10.316C6.86679 10.4986 7.16589 10.4887 7.33602 10.2942L10.836 6.29425C10.9984 6.10862 10.9796 5.82648 10.794 5.66405C10.6084 5.50163 10.3262 5.52044 10.1638 5.70607Z"
                            fill="currentColor" />
                    </svg>
                </span>
                <span class="text-green-800">Verified</span>
            </x-v2.badge>
        </div>
    </x-v2.label-value>
</script>
