@extends('frontend.layouts.frontend')
@section('title', $pagetitle )

@section('content')

<div class="main-conntent">
    @section('content-header')
    <!--<section class="content-header">
        <div class="pull-left">
            <ol class="breadcrumb">
                <li><a href="{{ route('user_dashboard') }}"><i class="fa fa-book"></i> Home</a></li>
                <li class="active">Agents view</li>
            </ol>
            <h1>Marketing
                <small>Agents view</small>
            </h1>
        </div>
        <div style="clear: both;"></div>
    </section>-->
    @endsection

    <section class="content">
        <div class="row">
<!--            <div class="col-md-12">
                <div class="custom-header">
                    <h3 class="box-title">Search Agent</h3>
                </div>
            </div>-->

<!--            <div class="col-md-12">
                <div class="box box-info">
                    {{ Form::model(array('method' => 'post'), ['class' => 'form-horizontal vertical-add-form','id'=>'filterForm']) }}
                    <div class="box-body">
                        <div class="row">
                            <div class="col-md-12">
                                <div class="form-group no-margin">
                                    <label class="control-label col-md-2 text-right label-lh-center"> Search by </label>
                                    <div class="col-sm-3">
                                        {{ Form::hidden('searchField_hidden', (empty($agentData->searchField) ? null : $agentData->searchField), ['class' => '','id'=>'searchField_hidden']) }}
                                        {{ Form::hidden('searchValue_hidden', (empty($agentData->searchValue) ? null : $agentData->searchValue), ['class' => '','id'=>'searchValue_hidden']) }}
                                        {{ Form::select('searchField', $searchFieldArr, null, array('class' => 'form-control', 'id' => 'searchField')) }}
                                    </div>
                                    <div class="col-sm-3 statusValue" style="display: none;">
                                        {{ Form::select('statusValue', $agentStatusArr, null, array('class' => 'form-control', 'id' => 'statusValue')) }}
                                    </div>
                                    <div class="col-sm-3 searchValue">
                                        {{ Form::text('searchValue', null, array('class' => 'form-control', 'id' => 'searchValue', 'placeholder' => 'Enter Search')) }}
                                    </div>
                                    <div class="col-md-2">
                                        <button type="submit" class="btn disable btn-info pull-right filterSubmit">Search</button>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    {{ Form::close() }}
                </div>
            </div>-->

            <div class="col-md-12">
<!--                <div class="custom-header">
                    <h3 class="box-title">Agent List</h3>
                </div>-->

                <div class="navbar-custom-menu data-setting column-navbar margin-t-30 space-x-2" style="display: flex;">
                    <ul class="nav navbar-nav space-x-2">
                        <li>
                            <span class="pull-right add-btn-block">
                                <a href="javascript:void(0);" class="link-black text-sm pull-right" id="agentExport" data-toggle="tooltip" data-original-title="&nbsp;Download Excel&nbsp;">
                                    <div class="btn-add">
                                        <i class="fa fa-file-excel"></i>
                                    </div>
                                </a>
                            </span>
                        </li>
                        @if($isZohoConnect)
                        <li>
                            <span class="pull-right add-btn-block">
                                <a href="javascript:void(0);" class="link-black text-sm pull-right" id="syncAgentFromZoho" data-toggle="tooltip" data-original-title="&nbsp;Sync From Zoho&nbsp;">
                                    <div class="btn-add">
                                        <i class="fa fa-arrow-down"></i>
                                    </div>
                                </a>
                            </span>
                        </li>
                        <li>
                            <span class="pull-right add-btn-block">
                                <a href="javascript:void(0);" class="link-black text-sm pull-right" id="syncAgentToZoho" data-toggle="tooltip" data-original-title="&nbsp;Sync To Zoho&nbsp;">
                                    <div class="btn-add">
                                        <i class="fa fa-sync"></i>
                                    </div>
                                </a>
                            </span>
                        </li>
                        @endif

                        <li class="dropdown">
                            <a href="javascript:;" class="dropdown-toggle link-black dropdown-toggle-column">Columns <i class="fa fa-angle-down"></i>
                            </a>
                            <ul class="dropdown-menu" style="background: #eee;">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <div class="col-md-12 toggle-label"><label class="control-label">Toggle Column</label></div>
                                        <div class="col-md-12">
                                            <div class="form-group"><input type='checkbox' name='column_1' value='1' class='custom-column' data-default-status='true' data-column="1" id="agentId" checked /> <label for="agentId">Agent ID</label> </div>
                                            <div class="form-group"><input type='checkbox' name='column_2' value='2' class='custom-column' data-default-status='true' data-column="2" id="agentTitle" checked /> <label for="agentTitle">Agent Name</label></div>
                                            <div class="form-group"><input type='checkbox' name='column_3' value='3' class='custom-column' data-default-status='true' data-column="3" id="person" checked /> <label for="person">Person</label></div>
                                            <div class="form-group"><input type='checkbox' name='column_4' value='4' class='custom-column' data-default-status='true' data-column="4" id="phone" checked/> <label for="phone">Phone</label></div>
                                            <div class="form-group"><input type='checkbox' name='column_5' value='5' class='custom-column' data-default-status='true' data-column="5" id="email" checked/> <label for="email">Email</label></div>
                                            <div class="form-group"><input type='checkbox' name='column_6' value='6' class='custom-column' data-default-status='true' data-column="6" id="agentCode" checked/> <label for="agentCode">Agent Code</label></div>
                                            <div class="form-group"><input type='checkbox' name='column_7' value='7' class='custom-column' data-default-status='true' data-column="7" id="superAgent" checked/> <label for="superAgent">Super Agent</label></div>
                                            <div class="form-group"><input type='checkbox' name='column_8' value='8' class='custom-column' data-default-status='true' data-column="8" id="state" checked/> <label for="state">State</label></div>
                                            <!-- <div class="form-group"><input type='checkbox' name='column_8' value='8' class='custom-column' data-default-status='true' data-column="8" id="username" checked/> <label for="username">Username</label></div>  -->
                                            <div class="form-group"><input type='checkbox' name='column_9' value='9' class='custom-column' data-default-status='true' data-column="9" id="accountManager" checked/> <label for="accountManager">Account Manager</label></div>
                                            <div class="form-group"><input type='checkbox' name='column_10' value='10' class='custom-column' data-default-status='true' data-column="10" id="status" checked/> <label for="status">Status</label></div>
                                            <div class="form-group"><input type='checkbox' name='column_11' value='11' class='custom-column' data-default-status='true' data-column="11" id="lockUnlock" checked/> <label for="lockUnlock">Lock/Unlock</label></div>
                                        </div>
                                    </div>
                                </div>
                            </ul>
                        </li>
                    </ul>
                </div>
                <div class="table-responsive no-padding margin-t-30">
                    <table class="table table-hover table-custom" id="agentListArr">
                        <thead>
                            <tr>
                                <th scope="col">
                                    {{ Form::checkbox('check_or_not', 1, false, array('class' => 'check_or_not', 'id' => 'check_or_not')) }}
                                </th>
                                <th scope="col">Agent ID</th>
                                <th scope="col">Agent Name</th>
                                <th scope="col">Person</th>
                                <th scope="col">Phone</th>
                                <th scope="col">Email</th>
                                <th scope="col">Agent Code</th>
                                <th scope="col">Super Agent</th>
                                <th scope="col">State</th>
                                <!-- <th scope="col">Username</th> -->
                                <th scope="col">Account Manager</th>
                                <th scope="col">Status</th>
                                <th scope="col">Lock/Unlock</th>
                            </tr>
                        </thead>
                        <tbody>

                        </tbody>
                    </table>
                </div>
                <div class="text-center" id="noRecords" style="display: none;">
                    <table class="table table-hover table-custom">
                        <tbody>
                            <tr><td colspan="12" style="text-align: center">
                                <p style="color:red;">No Record Found</p>
                            </td></tr>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>       
        <div class="modal fade" id="deleteModal" role="dialog">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button aria-label="Close" data-dismiss="modal" class="close" type="button">
                            <span aria-hidden="true">×</span>
                        </button>
                        <h4 class="modal-title">Delete Record</h4>
                    </div>
                    <div class="modal-body">
                        <div class="box box-info">
                            <div class="box-body">
                                <p> You want to delete recorde. Are you sure?</p>
                                <input type="hidden" name="_token" value="{{ csrf_token() }}">
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button data-dismiss="modal" class="btn btn-danger" type="button">No</button>
                        <button class="btn btn-success yes-sure" type="button">Yes</button>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal fade" id="deleteModalAgency" role="dialog">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button aria-label="Close" data-dismiss="modal" class="close" type="button">
                            <span aria-hidden="true">×</span>
                        </button>
                        <h4 class="modal-title">Delete Recorde</h4>
                    </div>
                    <div class="modal-body">
                        <div class="box box-info">
                            <div class="box-body">
                                <p> You want to delete recorde. Are you sure?</p>
                                <input type="hidden" name="_token" value="{{ csrf_token() }}">
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button data-dismiss="modal" class="btn btn-danger" type="button">No</button>
                        <button class="btn btn-success deleteStatus" type="button">Yes</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="modal fade" id="editAgentModal" role="dialog">
            {{ Form::open( array('class' => 'form-horizontal vertical-add-form','method' => 'post','id'=>'editAgentCodeForm')) }}
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button aria-label="Close" data-dismiss="modal" class="close" type="button">
                            <span aria-hidden="true">×</span>
                        </button>
                        <h4 class="modal-title">Update Agent Code</h4>
                    </div>
                    <div class="modal-body">
                        <div class="box box-info">
                            <div class="box-body">
                                <div class="row">
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label for="visa_number" class="col-sm-4 control-label">Agent Name:</label>
                                            <div class="col-sm-5 label-value-view">
                                                <span class="agentnames"></span>
                                            </div>
                                        </div>

                                        <input type="hidden" name="_token" value="{{ csrf_token() }}">
                                        {{ Form::hidden('id', null, array('class' => 'form-control editId', 'placeholder' => '', 'id' => 'editId')) }}

                                        <div class="form-group margin-minus-15">
                                            <label for="agent_code" class="col-sm-4 control-label">Agent Code:  <span id="" class="required-field">*<div></div></span></label>
                                            <div class="col-sm-5">
                                                {{ Form::text('agent_code', null, array('class' => 'form-control agentCode', 'placeholder' => 'Enter Agent Code', 'id' => 'agent_code')) }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button data-dismiss="modal" class="btn btn-danger" type="button">No</button>
                        <input type="submit" class="btn btn-success" name="submit" value="Update Now">
                    </div>
                </div>
            </div>
            {{ Form::close() }}
        </div>
        <div class="modal fade" id="createUserModel" role="dialog">
            {{ Form::open( array('class' => 'form-horizontal vertical-add-form','method' => 'post','id'=>'editAgentCodeFrm'  ,'route'=> 'create-agent-user')) }}
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button aria-label="Close" data-dismiss="modal" class="close" type="button">
                            <span aria-hidden="true">×</span>
                        </button>
                        <h4 class="modal-title">Create Agent User Account</h4>
                    </div>
                    <div class="modal-body">
                        <div class="box box-info">
                            <div class="box-body">
                                <div class="row">
                                    <input type="hidden" name="_token" value="{{ csrf_token() }}">
                                    {{ Form::hidden('agentId', null, array('class' => 'form-control agentId', 'placeholder' => '', 'id' => 'agentId')) }}
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label for="agent_code" class="col-sm-4 control-label">Email:  <span id="" class="required-field">*<div></div></span></label>
                                            <div class="col-sm-5">
                                                <label id="agent_email1"></label>
                                                {{ Form::hidden('agent_email', null, array('class' => 'form-control', 'placeholder' => 'Enter Email', 'id' => 'agent_email')) }}
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="agent_code" class="col-sm-4 control-label">Password:  <span id="" class="required-field">*<div></div></span></label>
                                            <div class="col-sm-5">
                                                <!--{{ Form::password('password', null, array('class' => 'form-control', 'placeholder' => 'Enter Password', 'id' => 'password')) }}-->
                                                {!! Form::password('password', ['class'=>'form-control','placeholder' => 'Enter Password', 'id' => 'password']) !!}
                                            </div>
                                        </div>
                                       <!--  <div class="form-group">
                                            <label for="agent_code" class="col-sm-4 control-label">Security Question:  <span id="" class="required-field">*<div></div></span></label>
                                            <div class="col-sm-5">
                                                {{ Form::select('securityQuestion', $arrSecurityQuestion, null , array('class' => 'form-control securityQuestion', 'id' => 'securityQuestion')) }}
                                            </div>
                                        </div>
                                        <div class="form-group margin-minus-15">
                                            <label for="agent_code" class="col-sm-4 control-label">Security Answer:  <span id="" class="required-field">*<div></div></span></label>
                                            <div class="col-sm-5">
                                                {{ Form::text('securityAnswer', null, array('class' => 'form-control', 'placeholder' => 'Enter Security Answer', 'id' => 'securityAnswer')) }}
                                            </div>
                                        </div> -->
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button data-dismiss="modal" class="btn btn-danger" type="button">No</button>
                        <input type="submit" class="btn btn-success" name="submit" value="Create New Account">
                    </div>
                </div>
            </div>
            {{ Form::close() }}
        </div>


        <div class="modal fade" id="addAgentStatus" role="dialog">
            {{ Form::open( array('class' => 'form-horizontal vertical-add-form','method' => 'post','id'=>'AddAgentStatusForm'  ,'route'=> 'add-agent-agency-status')) }}
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button aria-label="Close" data-dismiss="modal" class="close" type="button">
                            <span aria-hidden="true">×</span>
                        </button>
                        <h4 class="modal-title">Add Agent Status</h4>
                    </div>
                    <div class="modal-body">
                        <div class="box box-info">
                            <div class="box-body">
                                <div class="row">
                                    <input type="hidden" name="_token" value="{{ csrf_token() }}">
                                    {{ Form::hidden('agentId', null, array('class' => 'form-control agencyAgentId', 'placeholder' => '', 'id' => 'agencyAgentId')) }}
                                    <div class="col-md-12">
                                        <div class="form-group">
                                            <label for="agent_code" class="col-sm-4 control-label">Status:  <span id="" class="required-field">*<div></div></span></label>
                                            <div class="col-sm-5">
                                                {{ Form::select('status', $agentStatusArr, (empty($agenStatusEditDataFetch->status) ? null : $agenStatusEditDataFetch->status), ['class' => 'form-control', 'id' => 'agency_status']) }}
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="agent_code" class="col-sm-4 control-label">Start date:  <span id="" class="required-field">*<div></div></span></label>
                                            <div class="col-sm-5">
                                                {{ Form::text('start_date', (empty($agenStatusEditDataFetch->start_date) ? date('d-m-Y') : date('d-m-Y', strtotime($agenStatusEditDataFetch->start_date))), array('class' => 'form-control dateField', 'id' => 'start_date', 'placeholder' => 'dd-mm-yyyy','readonly')) }}
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="agent_code" class="col-sm-4 control-label">Expiry date:  <span id="" class="required-field">*<div></div></span></label>
                                            <div class="col-sm-5">
                                                {{ Form::text('end_date', (empty($agenStatusEditDataFetch->end_date) ? null : date('d-m-Y', strtotime($agenStatusEditDataFetch->end_date))), array('class' => 'form-control dateField', 'id' => 'end_date', 'placeholder' => 'dd-mm-yyyy','readonly')) }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    
                    <div class="modal-footer">
                        <div class="row">
                            <div class="col-md-12">
                                <button data-dismiss="modal" class="btn btn-danger" type="button">No</button>
                                <input type="submit" class="btn btn-success" name="submit" value="Add Status">
                            </div>
                            <div class="col-md-12 padding-t-30">
                                <div class="table-responsive no-padding">
                                    <table class="table table-hover table-custom" id="agentStatusList">
                                        <thead>
                                            <tr>
                                                <th scope="col">Status</th>
                                                <th scope="col">StartDate</th>
                                                <th scope="col">End Date</th>
                                                <!--<th scope="col">Action</th>-->
                                            </tr>
                                        </thead>
                                        <tbody>
                                            <tr>
                                                <td colspan="3" style="text-align: center">
                                                    <p style="color:red;">No Record Found</p>
                                                </td>
                                            </tr>
                                        </tbody>
                                    </table>
                                </div>
                            </div>
                        </div>
                    </div>
                    <!--<div class="text-center" id="noRecordsAgency" style="display: none;">
                        <p style="color:red;">No Record Found</p>
                    </div>-->
                </div>
                
            </div>
            {{ Form::close() }}
        </div>

        <div class="modal fade" id="confirmAgentExportModal" role="dialog">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button aria-label="Close" data-dismiss="modal" class="close" type="button">
                            <span aria-hidden="true">×</span>
                        </button>
                        <h4 class="modal-title">Export All Agents</h4>
                    </div>
                    <div class="modal-body">
                        <div class="box box-info">
                            <div class="box-body">
                                <p> Are you sure you want to export all agents data ?</p>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button data-dismiss="modal" class="btn btn-danger" type="button">No</button>
                        <button class="btn btn-success allExportConfirm" type="button">Yes</button>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal fade" id="syncAgentFromZohoModal" role="dialog">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button aria-label="Close" data-dismiss="modal" class="close" type="button">
                            <span aria-hidden="true">×</span>
                        </button>
                        <h4 class="modal-title">Sync From Zoho</h4>
                    </div>
                    <div class="modal-body">
                        <div class="box box-info">
                            <div class="box-body">
                                <p> Are you sure you want to sync from Zoho ?</p>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button data-dismiss="modal" class="btn btn-danger" type="button">No</button>
                        <button class="btn btn-success fromZohoSyncConfirm" type="button">Yes</button>
                    </div>
                </div>
            </div>
        </div>
        <div class="modal fade" id="syncAgentToZohoModal" role="dialog">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button aria-label="Close" data-dismiss="modal" class="close" type="button">
                            <span aria-hidden="true">×</span>
                        </button>
                        <h4 class="modal-title">Sync To Zoho</h4>
                    </div>
                    <div class="modal-body">
                        <div class="box box-info">
                            <div class="box-body">
                                <p> Are you sure you want to sync this selected item to Zoho ?</p>
                                <input type="hidden" name="ids" class="agentIds" value="" />
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button data-dismiss="modal" class="btn btn-danger" type="button">No</button>
                        <button class="btn btn-success toZohoSyncConfirm" type="button">Yes</button>
                    </div>
                </div>
            </div>
        </div>

        <!-- Zoho Sync Results Modal -->
        <div class="modal fade" id="zohoAgentSyncResultsModal" role="dialog">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <button aria-label="Close" data-dismiss="modal" class="close" type="button">
                            <span aria-hidden="true">×</span>
                        </button>
                        <h4 class="modal-title">Zoho Sync Results</h4>
                    </div>
                    <div class="modal-body">
                        <div class="table-responsive" style="max-height: 400px; overflow-y: auto; border: 1px solid #ddd;">
                            <table class="table table-striped table-bordered" style="margin-bottom: 0;">
                                <thead style="position: sticky; top: 0; background-color: #f8f9fa; z-index: 10; border-bottom: 2px solid #dee2e6;">
                                    <tr>
                                        <th style="min-width: 120px;">Agent ID</th>
                                        <th style="min-width: 150px;">Agent Name</th>
                                        <th style="min-width: 120px;">Sync Status</th>
                                        <th style="min-width: 140px;">Sync Date/Time</th>
                                        <th style="min-width: 200px;">Remarks</th>
                                    </tr>
                                </thead>
                                <tbody id="agentSyncResultsTableBody">
                                    <!-- Results will be populated here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button data-dismiss="modal" class="btn btn-primary" type="button">Close</button>
                    </div>
                </div>
            </div>
        </div>
    </section>
</div>

@endsection
