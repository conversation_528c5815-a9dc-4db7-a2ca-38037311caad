<!--<div class="box box-info">-->
    <div class="custom-header">
        <div class="row">
            <div class="col-md-11">
                <h3 class="box-title">Class list of {{$subjectCode}} : {{$day}} : Class ({{$teacher}}) on date: {{$date}}</h3>
                @if(!empty($isHoliday))
                    <h3 class="box-title" style="color:red;font-size: 13px;">{{$isHoliday}}</h3>
                @endif
            </div>
            @php $editAttendance=0; @endphp
            @if(count($arrStudents))
                @foreach($arrStudents as $student)
                    @if(isset($arrAttendance[$student['student_id']]['total_hours']))
                     @for($i=0;$i<$noTimeSlots;$i++)
                        @if(isset($arrAttendance[$student['student_id']]['timeslot']) && in_array($i,array_keys($arrAttendance[$student['student_id']]['timeslot'])))
                           @php  $editAttendance++ @endphp
                        @else 

                        @endif
                     @endfor
                    @endif
                @endforeach
            
            @endif
                <div class="col-md-1">
                    <span  class="pull-right add-btn-block">
                        <a id='attendance_update' class="link-black text-sm" data-toggle="tooltip" data-original-title="Allow to change or enter attendance" style="font-size: 18px;" href="javascript:;"><i class="fa fa-edit"></i> </a> 
                    </span>
                </div>
        </div>
    </div>
    <div class="box-body table-responsive">
        {{ Form::hidden('timetable_id',  $timetableId , array('id' => 'timetable_id')) }}
        {{ Form::hidden('attendance_day',  $day , array('id' => 'attendance_day')) }}
        {{ Form::hidden('break_from',  (isset($break_from))?$break_from:'' , array('id' => 'break_from')) }}
        {{ Form::hidden('break_to',  (isset($break_to))?$break_to:'' , array('id' => 'break_to')) }}
        {{ Form::hidden('attendance_type',  (isset($attendance_type))?$attendance_type:'' , array('id' => 'attendance_type')) }}
        {{ Form::hidden('attendance_date',  $date , array('id' => 'attendance_date')) }}
        {{ Form::hidden('time_slots',  $noTimeSlots , array('id' => 'time_slots')) }}
        <table class="table table-hover table-custom">
            <thead>
                <tr>
                    <th>Student Id</th>
                    <th>Name</th>
                    <th>Status</th>
                    @for($i=0;$i<$noTimeSlots;$i++)
                        <th id="main_timeslot_{{$i}}">
                            @foreach($breakTime as $b)
                                 @if((strtotime($b['break_from'])>=strtotime($arrTimeSlot[$i])) && (strtotime($b['break_to'])<=strtotime($arrTimeSlot[$i+1])) && !empty($b['break_from']) && !empty($b['break_to'])) 
                                    <i class="fa fa-info-circle" data-toggle="tooltip" title=" Break Time : {{ date('h:i A', strtotime($b['break_from']))}} - {{ date('h:i A', strtotime($b['break_to']))}}" data-id="{{$timetableId}}"></i>
                                 @endif
                            @endforeach
                            <br>
                        {{$arrTimeSlot[$i]}} - {{$arrTimeSlot[$i+1]}}</th>
                    @endfor
                    <th>Total Attd. Hours</th>
                    <th>Action</th>                   
                </tr>
            </thead>
            <tbody>
                @if(!empty($arrTodayHoliday))
                <tr>
                    <td colspan="10" style="text-align:center;"><p style="color:red;">{{$arrTodayHoliday[0]}}</p></td>
                </tr>
                @else
                    @if(count($arrStudents) > 0)
                    @foreach($arrStudents as $student)
                    <tr>
                        <td>{{$student['generated_stud_id']}}</td>
                        <td>{{$student['first_name']}} {{$student['family_name']}}</td>
                        <td>{{$student['status']}}</td>
                        @for($i=0;$i<$noTimeSlots;$i++)
                        <td>
                            <span class="showtimeslot_{{$student['student_id']}}">
                                  @if(isset($arrAttendance[$student['student_id']]['total_hours']))
                                  {{ Form::checkbox('time_slot['.$student['student_id'].'][]', $arrTimeSlot[$i].'-'.$arrTimeSlot[$i+1], (isset($arrAttendance[$student['student_id']]['timeslot']) && in_array($i,array_keys($arrAttendance[$student['student_id']]['timeslot'])))?true:false, array('class' => 'form-check-input', 'id'=>'time_slot_'.$student['student_id'].'_'.$i,'disabled')) }}
                                  @else
                                  {{ Form::checkbox('time_slot['.$student['student_id'].'][]', $arrTimeSlot[$i].'-'.$arrTimeSlot[$i+1], (isset($arrAttendance[$student['student_id']]['timeslot']) && in_array($i,array_keys($arrAttendance[$student['student_id']]['timeslot'])))?true:false, array('class' => 'form-check-input', 'id'=>'time_slot_'.$student['student_id'].'_'.$i)) }}
                                  @endif
                                    <span id="timeslot_{{$student['student_id']}}_{{$i}}">
                                        @if(isset($arrAttendance[$student['student_id']]['timeslot']) && !in_array($arrTimeSlot[$i].'-'.$arrTimeSlot[$i+1],$arrAttendance[$student['student_id']]['timeslot']))
                                        {{isset($arrAttendance[$student['student_id']]['timeslot'][$i])?$arrAttendance[$student['student_id']]['timeslot'][$i]:''}}
                                        @endif 
                                    </span>
                                </span>
                                <span class="showtimeslotdropdown_{{$student['student_id']}}" style="display:none;">
                                      {{ Form::select('from_time_slot['.$student['student_id'].'][]',$arrTimeIntervalSlotFrom[$i],isset($arrAttendance[$student['student_id']]['from_time'][$i])?$arrAttendance[$student['student_id']]['from_time'][$i]:$arrTimeSlot[$i] , array('class' => 'form-control','id'=>'from_time_slot_'.$student['student_id'].'_'.$i)) }} - 
                                      {{ Form::select('to_time_slot['.$student['student_id'].'][]',$arrTimeIntervalSlotTo[$i],isset($arrAttendance[$student['student_id']]['to_time'][$i])?$arrAttendance[$student['student_id']]['to_time'][$i]:$arrTimeSlot[$i+1] , array('class' => 'form-control', 'id' => 'to_time_slot_'.$student['student_id'].'_'.$i)) }}
                                </span>
                            {{ Form::hidden('time_slots_interval['.$i.']',  $arrTimeSlot[$i]."-".$arrTimeSlot[$i+1] , array('id' => 'time_slots_interval', 'class'=>'time_slots_interval')) }}
                        </td>
                        @endfor
                        <td>{{isset($arrAttendance[$student['student_id']]['total_hours'])?number_format($arrAttendance[$student['student_id']]['total_hours'],2):0}}</td>
                        <td>
                            <span id="edit_{{$student['student_id']}}">
                                  <a id='' data-id="{{$student['student_id']}}" class="link-black text-sm edit_attendance" data-toggle="tooltip" data-original-title="Modify attended hours for the student" href="javascript:;"><i class="fa fa-pencil-alt"></i> </a> 
                            </span>
                            <span id="update_{{$student['student_id']}}" style='display: none;'>
                                  <a id='save_edit' class="link-black text-sm save_edit" data-id="{{$student['student_id']}}" data-attendanceid="{{ isset($arrAttendance[$student['student_id']]['id'])?$arrAttendance[$student['student_id']]['id']:'' }}" data-toggle="tooltip" data-original-title="Update attended hours for the student" href="javascript:;"><i class="fa fa-check"></i> </a> 
                                <a id='cancel_edit' class="link-black text-sm cancel_edit" data-id="{{$student['student_id']}}" data-toggle="tooltip" data-original-title="Cancel hours editing"  href="javascript:;"><i class="fa fa-times"></i> </a> 
                            </span>
                            {{ Form::hidden('new_from['.$student['student_id'].'][]',  null , array('class'=>'new_timeslot_from')) }}          
                            {{ Form::hidden('new_to['.$student['student_id'].'][]',  null , array('class'=>'new_timeslot_to')) }}
                            {{ Form::hidden('student[]',  $student['student_id'] , array('class'=>'students')) }}
                        </td>
                    <tr>    

                        @endforeach
                        @else
                    <tr>
                        <td colspan="10" style="text-align:center;"><p style="color:red;">No Record Found</p></td>
                    </tr>
                    @endif
                
                @endif
            </tbody>
        </table>
    </div>                 
<!--</div>-->  