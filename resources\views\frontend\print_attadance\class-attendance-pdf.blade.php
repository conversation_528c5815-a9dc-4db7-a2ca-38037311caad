<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head id="Head1">
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <title>Attendance Data</title>
        <style>
            .btachdetailtable{
                border: 1px solid black;
                border-collapse: collapse;
            }
            body {
                background: #666666;
                margin: 0;
                padding: 0;
                text-align: center;
                color: #333;
                font-family: Arial, Helvetica, sans-serif;
                font-size: 65%;
            }
            .break {page-break-after: always}

        </style>
    </head>
    <body style="background-color: White;">
        <table style="width: 100%;" class="break-1">
            <tr style="width: 100%;">
                <td style="width: 50%;">
                    {!! $collegeLogoImg !!}
                </td>
                <td style="width: 50%;">
                    <b>{{ $collegeDetail->college_name ?? "" }}</b><br/>
                    {{ $venueDetail->unit_detail ?? "" }}<br/>
                    {{ $venueDetail->sub_urb ?? "" }}, {{ $venueDetail->state ?? "" }}, {{ $venueDetail->postcode ?? "" }}<br/>
                    Phone: {{ $venueDetail->contact_no ?? "" }}<br/>
                    Email: {{ $collegeDetail->contact_email ?? "" }}
                </td>
            </tr>
            <tr style="width: 100%;">
                <td style="width: 100%; text-align:center;" colspan="2">
                    <h1>Weekly Class Attendance Roll</h1>
                </td>
            </tr>
            <tr style="width: 100%;">
                <td style="width: 100%;" colspan="2">
                    <table class="btachdetailtable" style="width: 100%;">
                        <tr>
                            <td  colspan="2" style="width: 100%; background:#666666;color:#FFFFFF;"><b>BATCH DETAIL</b></td>
                        </tr>
                        <tr style="width: 100%;">
                            <td  class="btachdetailtable" style="width: 100%;">
                                <b>Batch Name:</b> {{ $batch }}
                            </td>
                            <td  class="btachdetailtable"  style="width: 100%;">
                                <b>Class Start Time:</b> {{ $startTime }}
                            </td>
                        </tr>
                        <tr style="width: 100%;">
                            <td  class="btachdetailtable" style="width: 100%;">
                                <b>Unit Name:</b> {{ $subjectCode }} {{ $subjectName }}
                            </td>
                            <td  class="btachdetailtable" style="width: 100%;">
                                <b>Class Finish Time:</b> {{ $endTime }}
                            </td>
                        </tr>
                        <tr style="width: 100%;">
                            <td  class="btachdetailtable" style="width: 100%;">
                                <b>Trainer Name:</b> {{ $teacherName }}
                            </td>
                            <td  class="btachdetailtable" style="width: 100%;">
                                <b>Classroom:</b> {{ $roomId }}
                            </td>
                        </tr>
                        <tr style="width: 100%;">
                            <td  class="btachdetailtable" style="width: 100%;">
                                <b>Unit Start Week:</b> {{  date("d-M-Y", strtotime($batchDate['firstdate'])) }}
                            </td>
                            <td  class="btachdetailtable"  style="width: 100%;">
                                <b>Unit Finish Week:</b> {{  date("d-M-Y", strtotime($batchDate['lastdate'])) }}
                            </td>
                        </tr>
                    </table>
                </td>
            </tr>
            <tr style="width: 100%;margin-top:25px;">
                <td style="width: 100%;" colspan="2">
                    @foreach($getAllDays as $dayRowKey => $dayRow)
                        <table  class="@if(!$loop->last) break @endif"  style="width: 100%;">
                            <tr>
                                <td colspan="2">
                                    <h1>Week: {{ $dayRowKey }}
                                    @if(in_array($dayRowKey, $holidayWeek))
                                        <span style="color: red; font-size: 13px;">(Holiday Week)</span>
                                    @endif
                                    </h1>
                                </td>
                            </tr>
                            <tr>
                                <td colspan="2" style="width: 100%;">
                                    <?php $studentChunks = array_chunk($studentList, 15); ?>
                                    @foreach($studentChunks as $index => $chunk)
                                        @if($index > 0)
                                            <div class="break"></div>
                                        @endif

                                        <table style="width: 100%;margin:0px;" class="btachdetailtable">
                                            <tr>
                                                <td colspan="3" class="btachdetailtable" style="width: 50%;background:#666666;color:#ffffff;">
                                                    ATTENDANCE DELIVERY DATES
                                                </td>
                                                @foreach ($dayRow as $rowDate)
                                                    <td colspan="2" class="btachdetailtable">
                                                        {{ date("d-M-Y", strtotime($rowDate['timetable_date'])) }} ( {{ $rowDate['day'] }} )
                                                    </td>
                                                @endforeach
                                            </tr>
                                            <tr>
                                                <td style="background:#666666;color:#FFFFFF;width: 10px;">SN</td>
                                                <td style="background:#666666;color:#FFFFFF;">STUDENT ID NO.</td>
                                                <td style="background:#666666;color:#FFFFFF;">STUDENT NAME </td>
                                                @foreach ($dayRow as $rowDate)
                                                    <td style="background:#666666;color:#FFFFFF;">SIGN IN</td>
                                                    <td style="background:#666666;color:#FFFFFF;">SIGN OUT</td>
                                                @endforeach
                                            </tr>
                                            <?php $i = 1 + ($index * 15); ?>
                                            @foreach($chunk as $row)
                                                <tr>
                                                    <td class="btachdetailtable">{{ $i }}</td>
                                                    <td class="btachdetailtable">{{ $row['generated_stud_id'] }}</td>
                                                    <td class="btachdetailtable">{{ $row['first_name'] . ' ' . $row['family_name'] }}</td>
                                                    @foreach ($dayRow as $rowDate)
                                                        <td class="btachdetailtable" style="padding: 20px;"></td>
                                                        <td class="btachdetailtable"></td>
                                                    @endforeach
                                                </tr>
                                                <?php $i++; ?>
                                            @endforeach
                                        </table>
                                    @endforeach
                                </td>
                            </tr>
                            <tr>
                                <td colspan="2"><h3>Trainer Signature: _____________________</h3></td>
                            </tr>
                            <tr>
                                <td colspan="2"><h3>{{ $teacherName }}</h3></td>
                            </tr>
                        </table>
                    @endforeach
                </td>
            </tr>
        </table>
    </body>
</html>
