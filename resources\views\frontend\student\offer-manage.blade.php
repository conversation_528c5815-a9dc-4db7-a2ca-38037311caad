@extends('frontend.layouts.frontend')
@section('title', $pagetitle )

@section('content')

<!-- Content Wrapper. Contains page content -->
<!-- Content Header (Page header) -->
<!-- Main content -->
<div class="main-conntent">
    <section class="content">
        <!-- Info boxes -->
        <div class="row">
            <div class="col-md-12">
                <div class="box box-info">
                    {{ Form::model(['method' => 'post'], ['class' => 'form-horizontal vertical-add-form', 'id' =>
                    'filetringIntervention']) }}
                    <div class="box-body">
                        <div class="row">
                            <div class="col-md-12">
                                {{-- Course Type --}}
                                <div class="form-group row">
                                    <label class="col-md-3 col-form-label">Course Type</label>
                                    <div class="col-md-5">
                                        {{ Form::select('course_type_id', $arrCourseType, null, ['class' =>
                                        'form-control course_type_id', 'id' => 'course_type_id']) }}
                                    </div>
                                </div>

                                {{-- Course --}}
                                <div class="form-group row">
                                    <label class="col-md-3 col-form-label">Course</label>
                                    <div class="col-md-5">
                                        {{ Form::select('course_id', $arrCourses, null, ['class' => 'form-control
                                        searchDropdown', 'id' => 'course_id']) }}
                                    </div>
                                </div>



                                {{-- Application Status Filter --}}
                                <div class="form-group row">
                                    <label class="col-md-3 col-form-label">Application Status</label>
                                    <div class="col-md-5">
                                        {{ Form::select('status', $arrCourseStatusMail, null, ['class' => 'form-control
                                        ', 'id' => 'status']) }}
                                    </div>
                                </div>

                                {{-- Intake Date --}}
                                <div class="form-group row intake_date_div hidden">
                                    <label class="col-md-3 col-form-label">Intake Date</label>
                                    <div class="col-md-5">
                                        {{ Form::text('input_intake_date','' , ['class' => 'form-control dateField',
                                        'id' => 'input_intake_date', 'placeholder' => 'dd-mm-yyyy']) }}
                                    </div>
                                </div>
                                {{-- Intake name --}}
                                <div class="form-group row intake_name_div hidden">
                                    <label class="col-md-3 col-form-label">Intake Year</label>
                                    <div class="col-md-5">
                                        {{ Form::select('intake_year',[], null, ['class' => 'form-control
                                        searchDropdown', 'id' => 'intake_year']) }}
                                    </div>
                                </div>
                                <div class="form-group row intake_name_div hidden">
                                    <label class="col-md-3 col-form-label">Intake</label>
                                    <div class="col-md-5">
                                        {{ Form::select('intake_name',[], null, ['class' => 'form-control
                                        searchDropdown', 'id' => 'intake_name']) }}
                                    </div>
                                </div>
                                <div class="form-group row intake_name_div hidden">
                                    <label class="col-md-3 col-form-label">Campus</label>
                                    <div class="col-md-5">
                                        {{ Form::select('campus',[], null, ['class' => 'form-control searchDropdown',
                                        'id' => 'campus']) }}
                                    </div>
                                </div>
                                <div class="form-group row">
                                    <label class="col-md-3 col-form-label">Applied Date</label>
                                    <div class="col-md-5">
                                        {{ Form::text('applied_date_from','' , ['class' => 'form-control dateField',
                                        'id' => 'applied_date_from', 'placeholder' => 'Form Date']) }}
                                    </div>
                                    <div class="col-md-5 mt-10">
                                        {{ Form::text('applied_date_to','' , ['class' => 'form-control dateField', 'id'
                                        => 'applied_date_to', 'placeholder' => 'To Date']) }}
                                    </div>
                                </div>

                                {{-- Buttons --}}
                                <div class="form-group row flex items-center gap-4">
                                    <button type="button" class="btn btn-info searchData">Search</button>
                                    <button type="button" class="btn disable btn-info clearData">Clear</button>
                                </div>
                            </div>
                        </div>

                    </div>
                    {{ Form::close() }}
                </div>
            </div>
            <div class="col-md-12">
                <div class="navbar-custom-menu data-setting margin-t-30 column-navbar" style="display: flex;">
                    <div class="add-btn-block flex space-x-1" style="display: flex;">
                        <span data-toggle="modal" data-target="#offerLabel" style="display: flex;">
                            <a href="javascript:;" class="update-offer-label" id="update-offer-label"
                                data-toggle="tooltip" data-original-title="Update Offer Label">
                                <div class="btn-add" style="padding:3px;">
                                    <svg xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24"
                                        stroke-width="1.5" stroke="currentColor" class="w-6 h-6">
                                        <path stroke-linecap="round" stroke-linejoin="round"
                                            d="M9.568 3H5.25A2.25 2.25 0 003 5.25v4.318c0 .597.237 1.17.659 1.591l9.581 9.581c.699.699 1.78.872 2.607.33a18.095 18.095 0 005.223-5.223c.542-.827.369-1.908-.33-2.607L11.16 3.66A2.25 2.25 0 009.568 3z" />
                                        <path stroke-linecap="round" stroke-linejoin="round" d="M6 6h.008v.008H6V6z" />
                                    </svg>
                                </div>
                            </a>
                        </span>
                        <a href="javascript:void(0);" class="bulk_transcript" id="offerManageExport"
                            data-toggle="tooltip" data-original-title="Download Excel">
                            <div class="btn-add">
                                <i class="fa fa-file-excel"></i>
                            </div>
                        </a>

                        @if($isXeroConnect)
                        <a href="javascript:void(0);" class="bulk_sync_to_xero" data-toggle="tooltip"
                            data-original-title="&nbsp;Bulk Sync To Xero&nbsp;">
                            <div class="btn-add">
                                <i class="fa fa-refresh"></i>
                            </div>
                        </a>
                        @endif

                        @if($isZohoConnect)
                        <a href="javascript:void(0);" class="bulk_sync_to_zoho" data-toggle="tooltip"
                            data-original-title="&nbsp;Bulk Sync To Zoho&nbsp;">
                            <div class="btn-add">
                                <i class="fa fa-refresh"></i>
                            </div>
                        </a>
                        @endif

                    </div>
                    <ul class="nav navbar-nav">
                        <li class="dropdown">
                            <a href="javascript:;" class="dropdown-toggle link-black dropdown-toggle-column">
                                Columns <i class="fa fa-angle-down"></i>
                                <!--<i class="fa fa-cog light-gray-color"></i>-->
                            </a>
                            <ul class="dropdown-menu" style="background: #eee;">
                                <div class="col-md-12">
                                    <div class="form-group">
                                        <div class="col-md-12 toggle-label"><label class="control-label">Toggle
                                                Column</label></div>
                                        <div class="col-md-12">
                                            <div class="form-group"><input type='checkbox' name='column_1' value='1'
                                                    class='custom-column' data-default-status='true' data-column="1"
                                                    id="ID" checked disabled /> <label for="ID">ID</label> </div>
                                            <div class="form-group"><input type='checkbox' name='column_2' value='2'
                                                    class='custom-column' data-default-status='true' data-column="2"
                                                    id="student_id" checked /> <label for="student_id">Student ID
                                                </label></div>
                                            <div class="form-group"><input type='checkbox' name='column_3' value='3'
                                                    class='custom-column' data-default-status='true' data-column="3"
                                                    id="date_applied" checked /> <label for="date_applied">Date Applied
                                                </label></div>
                                            <div class="form-group"><input type='checkbox' name='column_4' value='4'
                                                    class='custom-column' data-default-status='true' data-column="4"
                                                    id="applicant_name" checked /> <label for="applicant_name">Applicant
                                                    Name </label></div>
                                            <div class="form-group"><input type='checkbox' name='column_5' value='5'
                                                    class='custom-column' data-default-status='true' data-column="5"
                                                    id="agent" checked /> <label for="agent">Agent </label></div>
                                            <div class="form-group"><input type='checkbox' name='column_6' value='6'
                                                    class='custom-column' data-default-status='true' data-column="6"
                                                    id="created_by" checked /> <label for="created_by">Created
                                                    By</label></div>
                                            <div class="form-group"><input type='checkbox' name='column_7' value='7'
                                                    class='custom-column' data-default-status='true' data-column="7"
                                                    id="student_type" /> <label for="student_type">Student Type </label>
                                            </div>
                                            <div class="form-group"><input type='checkbox' name='column_8' value='8'
                                                    class='custom-column' data-default-status='true' data-column="8"
                                                    id="campus_name" checked /> <label for="campus_name">Caumpus Name
                                                </label></div>
                                            <div class="form-group"><input type='checkbox' name='column_9' value='9'
                                                    class='custom-column' data-default-status='true' data-column="9"
                                                    id="course_applied" checked /> <label for="course_applied">Course
                                                    Applied </label></div>
                                            <div class="form-group"><input type='checkbox' name='column_10' value='10'
                                                    class='custom-column' data-default-status='true' data-column="10"
                                                    id="status" checked /> <label for="status">Status </label></div>
                                            <div class="form-group"><input type='checkbox' name='column_11' value='11'
                                                    class='custom-column' data-default-status='true' data-column="11"
                                                    id="offer_label_column" checked /> <label
                                                    for="offer_label_column">Offer Label</label></div>
                                            <div class="form-group"><input type='checkbox' name='column_12' value='12'
                                                    class='custom-column' data-default-status='false' data-column="12"
                                                    id="study_period" /> <label for="study_period">Study Period </label>
                                            </div>
                                            <div class="form-group"><input type='checkbox' name='column_13' value='13'
                                                    class='custom-column' data-default-status='false' data-column="13"
                                                    id="reserved_id" /> <label for="reserved_id">Reserve Id </label>
                                            </div>
                                            <div class="form-group"><input type='checkbox' name='column_14' value='14'
                                                    class='custom-column' data-default-status='false' data-column="14"
                                                    id="coe_name" /> <label for="coe_name">Coe Name </label></div>
                                            <div class="form-group"><input type='checkbox' name='column_15' value='15'
                                                    class='custom-column' data-default-status='false' data-column="15"
                                                    id="usi" /> <label for="usi">USI</label></div>
                                            <div class="form-group"><input type='checkbox' name='column_16' value='16'
                                                    class='custom-column' data-default-status='false' data-column="16"
                                                    id="ref_id" /> <label for="ref_id">Application Ref ID</label></div>
                                            <div class="form-group"><input type='checkbox' name='column_17' value='17'
                                                    class='custom-column' data-default-status='false' data-column="17"
                                                    id="intake_date" /> <label for="intake_date">Intake Date</label>
                                            </div>
                                            {{--<div class="form-group"><input type='checkbox' name='column_18'
                                                    value='18' class='custom-column' data-default-status='false'
                                                    data-column="18" id="sync_status" /> <label for="sync_status">Sync
                                                    Status</label></div>--}}
                                        </div>
                                    </div>
                                </div>
                            </ul>
                        </li>
                    </ul>
                </div>
                <div class="table-responsive no-padding margin-t-30">
                    <table id="offerManageStudent" class="table table-hover table-custom" data-page-length='25'>
                        <thead>
                            <tr style="white-space:nowrap;">
                                <th>&nbsp;{{ Form::checkbox('studentId', 'all', FALSE, array('class' => 'studentId')) }}
                                </th>
                                <th>ID</th>
                                <th>Student ID</th>
                                <th>Date Applied
                                    <i class="fas fa-filter blue-font showFooter padding-t-7 openDropdown"
                                        style="margin-left: 20px;" data-toggle="collapse" data-target="#date-filter"
                                        data-original-title="Show Status"></i>
                                    <div class="custom-animation-dropdown">
                                        <ul class="dropdown-menu" id="date-filter" style="background: #eee;">
                                            <div class="col-md-12">
                                                <div class="form-group">
                                                    <div class="col-md-12 toggle-label"><label
                                                            class="control-label">Select Date</label></div>
                                                    <div class="col-md-12">
                                                        <div class="form-group flex form-group-wrapper">
                                                            <span class="control-label col-md-3">From: </span>
                                                            <input type="text"
                                                                class="applied-from-date dateinput col-md-6"
                                                                placeholder="From Date">
                                                        </div>
                                                        <div class="form-group flex form-group-wrapper">
                                                            <span class="control-label col-md-3">To: </span>
                                                            <input type="text"
                                                                class="applied-to-date dateinput col-md-6"
                                                                placeholder="To Date">
                                                        </div>
                                                        <div class="flex">
                                                            <button
                                                                class="btn-danger col-md-3 resetfilter mb-2 ml-2 mt-2">Reset</button>
                                                            <button
                                                                class="btn-info col-md-3 appleyeddatefilter mb-2 ml-2 mt-2">Filter</button>
                                                        </div>
                                                    </div>
                                                </div>
                                            </div>
                                        </ul>
                                    </div>
                                </th>
                                <th>Applicant Name</th>
                                <th>Agent</th>
                                <th>Created By</th>
                                <th>Student Type</th>
                                <th class="filteractive">Campus Name
                                    <i class="fas fa-filter blue-font showFooter padding-t-7 openDropdown"
                                        style="margin-left: 40px;" data-toggle="collapse"
                                        data-target="#animation-dropdown-campus" data-original-title="Show Status"></i>
                                    <div class="custom-animation-dropdown">
                                        <ul class="dropdown-menu" id="animation-dropdown-campus"
                                            style="background: #eee;">
                                            <div class="col-md-12">
                                                <div class="form-group">
                                                    <div class="col-md-12 toggle-label"><label
                                                            class="control-label">Toggle Column</label></div>
                                                    <div class="col-md-12 checkboxlist">
                                                        @foreach($arrCollegeCampus as $campusid=>$campus)
                                                        <div class="form-group">
                                                            <input type="checkbox" value="{{$campusid}}"
                                                                class="custom-column-filter campusf" name="campus"
                                                                id="{{$campusid}}">
                                                            <label for="{{$campusid}}">{{$campus}}</label>
                                                        </div>
                                                        @endforeach
                                                    </div>
                                                </div>
                                            </div>
                                        </ul>
                                    </div>
                                </th>
                                <th>Course Applied</th>
                                <th class="filteractive">Status
                                    <i class="fas fa-filter blue-font showFooter padding-t-7 openDropdown"
                                        style="margin-left: 40px;" data-toggle="collapse"
                                        data-target="#animation-dropdown" data-original-title="Show Status"></i>
                                    <div class="custom-animation-dropdown">
                                        <ul class="dropdown-menu" id="animation-dropdown" style="background: #eee;">
                                            <div class="col-md-12">
                                                <div class="form-group">
                                                    <div class="col-md-12 toggle-label">
                                                        <label class="control-label">Toggle Column</label>
                                                    </div>
                                                    <div class="col-md-12 checkboxlist">
                                                        <div class="form-group">
                                                            <input type="checkbox" value="In Application"
                                                                class="custom-column-filter statusf" name="status"
                                                                id="new-application-request">
                                                            <label for="new-application-request">New Application
                                                                Request</label>
                                                        </div>
                                                        <div class="form-group">
                                                            <input type="checkbox" value="Reconsider"
                                                                class="custom-column-filter statusf" name="status"
                                                                id="reconsider">
                                                            <label for="reconsider">Reconsider</label>
                                                        </div>
                                                        <div class="form-group">
                                                            <input type="checkbox" value="Rejected"
                                                                class="custom-column-filter statusf" name="status"
                                                                id="rejected">
                                                            <label for="rejected">Rejected</label>
                                                        </div>
                                                        <div class="form-group">
                                                            <input type="checkbox" value="Offered"
                                                                class="custom-column-filter statusf" name="status"
                                                                id="offered">
                                                            <label for="offered">Offered</label>
                                                        </div>
                                                        <div class="form-group">
                                                            <input type="checkbox" value="Pending"
                                                                class="custom-column-filter statusf" name="status"
                                                                id="pending">
                                                            <label for="pending">Pending</label>
                                                        </div>

                                                    </div>
                                                </div>
                                            </div>
                                        </ul>
                                    </div>
                                </th>
                                <th class="filteractive">Offer Label
                                    <i class="fas fa-filter blue-font showFooter padding-t-7 openDropdown"
                                        style="margin-left: 40px;" data-toggle="collapse"
                                        data-target="#animation-dropdown-offerlabel"
                                        data-original-title="Show Status"></i>
                                    <div class="custom-animation-dropdown">
                                        <ul class="dropdown-menu" id="animation-dropdown-offerlabel"
                                            style="background: #eee;">
                                            <div class="col-md-12">
                                                <div class="form-group">
                                                    <div class="col-md-12 toggle-label"><label
                                                            class="control-label">Select Offer Label</label></div>
                                                    <div class="col-md-12 checkboxlist">
                                                        @foreach($arrOfferLabel as $offerLabelid=>$offerLabelName)
                                                        <div class="form-group">
                                                            <input type="checkbox" value="{{$offerLabelid}}"
                                                                class="custom-column-filter offerlabelcheckbox"
                                                                name="offerlabel" id="offer-{{$offerLabelid}}">
                                                            <label
                                                                for="offer-{{$offerLabelid}}">{{$offerLabelName}}</label>
                                                        </div>
                                                        @endforeach
                                                    </div>
                                                </div>
                                            </div>
                                        </ul>
                                    </div>
                                </th>
                                <th>Study Period</th>
                                <th>Reserve Id</th>
                                <th>Coe Name</th>
                                <th>USI</th>
                                <th>Application Ref ID</th>
                                <th>Intake Date</th>
                            </tr>
                        </thead>
                        <tbody>
                            {{ Form::hidden('_token', csrf_token(), array()) }}
                        </tbody>
                    </table>
                </div>
            </div>
        </div>


        <div class="modal fade" id="deleteModal" role="dialog">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button aria-label="Close" data-dismiss="modal" class="close" type="button">
                            <span aria-hidden="true">×</span>
                        </button>
                        <h4 class="modal-title">Delete Record</h4>
                    </div>
                    <div class="modal-body">
                        <p> You want to delete record. Are you sure?</p>

                    </div>
                    <div class="modal-footer">
                        <button data-dismiss="modal" class="btn btn-danger" type="button">No</button>
                        <button class="btn btn-success yes-sure" type="button">Yes</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="modal fade" id="resetOfferLabel" role="dialog">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button aria-label="Close" data-dismiss="modal" class="close" type="button">
                            <span aria-hidden="true">×</span>
                        </button>
                        <h4 class="modal-title">Reset Offer Label</h4>
                    </div>
                    <div class="modal-body">
                        <p> You want to Reset offer label record. Are you sure?</p>

                    </div>
                    <div class="modal-footer">
                        <button data-dismiss="modal" class="btn btn-danger" type="button">No</button>
                        <button class="btn btn-success resetlabelbutton" type="button">Yes</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="modal fade" id="syncToXeroModel" role="dialog">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button aria-label="Close" data-dismiss="modal" class="close" type="button">
                            <span aria-hidden="true">×</span>
                        </button>
                        <h4 class="modal-title">Sync To Xero</h4>
                    </div>
                    <div class="modal-body">
                        <p> Are you sure you want to sync this student to Xero? </p>
                    </div>
                    <div class="modal-footer">
                        <button data-dismiss="modal" class="btn btn-danger" type="button">No</button>
                        <button class="btn btn-success syncStudent" type="button">Yes</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="modal fade" id="syncDataModel" role="dialog">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button aria-label="Close" data-dismiss="modal" class="close" type="button">
                            <span aria-hidden="true">×</span>
                        </button>
                        <h4 class="modal-title">Sync Detail</h4>
                    </div>
                    <div class="modal-body">
                        <div class="success-update"></div>
                        <div class="fail-update"></div>
                    </div>
                </div>
            </div>
        </div>

        <div class="modal fade" id="unallocatedCreditModel" role="dialog">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button aria-label="Close" data-dismiss="modal" class="close" type="button">
                            <span aria-hidden="true">×</span>
                        </button>
                        <h4 class="modal-title">Pre-payment & Unallocated Amount</h4>
                    </div>
                    <div class="modal-body row">
                        <div class="form-group">
                            <label class="col-sm-5 control-label">Prepayment Received:</label>
                            <div class="col-sm-5 flex space-x-4">
                                <label class="control-label outStandingBalance">$0.00</label>
                                <div class="py-2.5">
                                    <a class="refreshOutStandingBalanceIcon cursor-pointer"
                                        title="Refresh Outstanding Balance" data-student-id="">
                                        <img src="{{ asset('v2/img/arrow-sync.svg') }}" class="h-6 w-6"
                                            alt="Arrow Sync">
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-5 control-label">
                                Unallocated Credit Amount:
                            </label>
                            <div class="col-sm-5 flex space-x-4">
                                <label class="control-label outUnallocatedBalanceText">$0.00</label>
                                <div class="py-2.5">
                                    <a class="refreshUnallocatedCreditIcon cursor-pointer"
                                        title="Refresh Outstanding Balance" data-student-id="">
                                        <img src="{{ asset('v2/img/arrow-sync.svg') }}" class="h-6 w-6"
                                            alt="Arrow Sync">
                                    </a>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="modal fade" id="coeModal" role="dialog">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button aria-label="Close" data-dismiss="modal" class="close" type="button">
                            <span aria-hidden="true">×</span>
                        </button>
                        <h4 class="modal-title coe-title"></h4>
                    </div>

                    {{ Form::open( array('method' => 'post', 'files'=>true, 'class' => 'form-horizontal
                    vertical-add-form' , 'id' => 'addCOEformOfferManage' ,'route'=> 'add-coe-offer-manage' )) }}
                    <div class="modal-body coeModalBody">
                        {{ Form::hidden('oldFile', NULL, array('class' => 'form-control', 'id' => 'oldFile')) }}
                        {{ Form::hidden('student_id', NULL, array('class' => 'form-control student_id', 'id' =>
                        'student_id')) }}
                        {{ Form::hidden('offer_id', NULL, array('class' => 'form-control', 'id' => 'offer_id')) }}
                        {{ Form::hidden('id', NULL, array('class' => 'form-control', 'id' => 'id')) }}

                        <div class="form-group" style="margin-left: 33px !important;">
                            {{ Form::checkbox('is_applicable', 0, NULL, ['class' => 'flat-red label-value-view
                            applicable custom-checkbox-input single','id' => 'is_applicable']) }}
                            <label for="is_applicable" class="col-sm-4 control-label custom-checkbox"> If Applicable :
                            </label>
                        </div>

                        <div class="form-group">
                            <label for="inputEmail3" class="col-sm-4 control-label"> Enter Code No : <span id=""
                                    class="required-field">*<div></div></span></label>
                            <div class="col-sm-5">
                                {{ Form::text('code_no' , NULL , array('class' => 'form-control', 'id' => 'code_no',
                                'placeholder' => 'Enter Code No')) }}
                            </div>
                        </div>
                        <div class="form-group no-margin">
                            <label for="inputEmail3" class="col-sm-4 control-label">Select COE : </label>
                            <div class="col-sm-5">
                                <!--{{ Form::file('images[]', ['multiple' => 'multiple'], NULL, array('class' => 'form-control', 'id' => 'images')) }}-->
                                <input type="file" name="images[]" id="images" class="inputfile inputfile-2"
                                    data-multiple-caption="{count} files selected" multiple />
                                <label for="images" class="file-foreach-line"><i class="fa fa-upload"></i><span
                                        class='file-name'> Choose a File&hellip;</span></label>
                            </div>
                        </div>
                        <div class="form-group no-margin">
                            <div class="col-sm-4"></div>
                            <div class="col-sm-5" id="downloadFile" style="word-break: break-all;"></div>
                        </div>

                        @if($isXeroConnect)
                        <div class="form-group no-margin">
                            <label class="col-sm-5 control-label">Prepayment Received:</label>
                            <div class="col-sm-5 flex space-x-4">
                                <label class="control-label outStandingBalance">$0.00</label>
                                <div class="py-2.5">
                                    <a class="refreshOutStandingBalanceIcon cursor-pointer"
                                        title="Refresh Outstanding Balance" data-student-id="">
                                        <img src="{{ asset('v2/img/arrow-sync.svg') }}" class="h-6 w-6"
                                            alt="Arrow Sync">
                                    </a>
                                </div>
                            </div>
                        </div>
                        <div class="form-group">
                            <label class="col-sm-5 control-label">
                                Unallocated Credit Amount:
                            </label>
                            <div class="col-sm-5 flex space-x-4">
                                <label class="control-label outUnallocatedBalanceText">$0.00</label>
                                <div class="py-2.5">
                                    <a class="refreshUnallocatedCreditIcon cursor-pointer"
                                        title="Refresh Outstanding Balance" data-student-id="">
                                        <img src="{{ asset('v2/img/arrow-sync.svg') }}" class="h-6 w-6"
                                            alt="Arrow Sync">
                                    </a>
                                </div>
                            </div>
                        </div>
                        @endif

                        {{-- TODO::GNG-3182 (Remove option for sync to xero from add COE) --}}
                        <!--<div class="form-group no-margin">
                            <label for="is_sync" class="col-sm-4 control-label">Sync to xero?</label>
                            <div class="col-sm-1 py-3">
                            {{-- {{ Form::checkbox('is_sync', 1, null, ['class' => '','id' => 'is_sync']) }}--}}
                            </div>
                            <div class="col-sm-7 py-3">
                                <div class="text-green-600">
                                    <span class="sync-data"></span>
                                </div>
                            </div>
                        </div>-->
                    </div>
                    <div class="modal-footer">
                        <button data-dismiss="modal" class="btn btn-danger" type="button">No</button>
                        <button type="submit" class="btn btn-info">Save</button>
                    </div>
                    {{ Form::close() }}
                </div>
            </div>
        </div>

        <div class="modal fade" id="bulkSyncStudentToXero" role="dialog">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button aria-label="Close" data-dismiss="modal" class="close" type="button">
                            <span aria-hidden="true">×</span>
                        </button>
                        <h4 class="modal-title">Bulk Sync To Xero</h4>
                    </div>
                    <div class="form-horizontal">
                        <div class="modal-body">
                            {{ Form::hidden('studentIdArr', NULL, array('id' => 'studentIdArr')) }}
                            <p>Are you sure you want to sync all selected items to Xero?</p>
                        </div>
                        <div class="modal-footer">
                            <button data-dismiss="modal" class="btn btn-danger" type="button">Cancel</button>
                            <button type="save" class="btn btn-info confirm_sync_xero">Sync</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Xero Sync Results Modal -->
        <div class="modal fade" id="xeroSyncResultsModal" role="dialog">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <button aria-label="Close" data-dismiss="modal" class="close" type="button">
                            <span aria-hidden="true">×</span>
                        </button>
                        <h4 class="modal-title">Xero Sync Results</h4>
                    </div>
                    <div class="modal-body">
                        <div class="table-responsive" style="max-height: 400px; overflow-y: auto; border: 1px solid #ddd;">
                            <table class="table table-striped table-bordered" style="margin-bottom: 0;">
                                <thead style="position: sticky; top: 0; background-color: #f8f9fa; z-index: 10; border-bottom: 2px solid #dee2e6;">
                                    <tr>
                                        <th style="min-width: 120px;">Student ID</th>
                                        <th style="min-width: 150px;">Name</th>
                                        <th style="min-width: 120px;">Status</th>
                                        <th style="min-width: 140px;">Sync Date/Time</th>
                                        <th style="min-width: 200px;">Remarks</th>
                                    </tr>
                                </thead>
                                <tbody id="xeroSyncResultsTableBody">
                                    <!-- Results will be populated here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button data-dismiss="modal" class="btn btn-primary" type="button">Close</button>
                    </div>
                </div>
            </div>
        </div>

        <div class="modal fade" id="bulkSyncStudentToZoho" role="dialog">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button aria-label="Close" data-dismiss="modal" class="close" type="button">
                            <span aria-hidden="true">×</span>
                        </button>
                        <h4 class="modal-title">Bulk Sync To Zoho</h4>
                    </div>
                    <div class="form-horizontal">
                        <div class="modal-body">
                            {{ Form::hidden('studentIdArr', NULL, array('id' => 'studentIdArr')) }}
                            <p>Are you sure you want to sync all selected items to Zoho?</p>
                        </div>
                        <div class="modal-footer">
                            <button data-dismiss="modal" class="btn btn-danger" type="button">Cancel</button>
                            <button type="save" class="btn btn-info confirm_sync_zoho">Sync</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <!-- Zoho Sync Results Modal -->
        <div class="modal fade" id="zohoSyncResultsModal" role="dialog">
            <div class="modal-dialog modal-lg">
                <div class="modal-content">
                    <div class="modal-header">
                        <button aria-label="Close" data-dismiss="modal" class="close" type="button">
                            <span aria-hidden="true">×</span>
                        </button>
                        <h4 class="modal-title">Zoho Sync Results</h4>
                    </div>
                    <div class="modal-body">
                        <div class="table-responsive" style="max-height: 400px; overflow-y: auto; border: 1px solid #ddd;">
                            <table class="table table-striped table-bordered" style="margin-bottom: 0;">
                                <thead style="position: sticky; top: 0; background-color: #f8f9fa; z-index: 10; border-bottom: 2px solid #dee2e6;">
                                    <tr>
                                        <th style="min-width: 120px;">Student ID</th>
                                        <th style="min-width: 150px;">Student Name</th>
                                        <th style="min-width: 120px;">Sync Status</th>
                                        <th style="min-width: 140px;">Sync Date/Time</th>
                                        <th style="min-width: 200px;">Remarks</th>
                                    </tr>
                                </thead>
                                <tbody id="syncResultsTableBody">
                                    <!-- Results will be populated here -->
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>
        </div>

        <div class="modal fade" id="offerLabel" role="dialog">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button aria-label="Close" data-dismiss="modal" class="close" type="button">
                            <span aria-hidden="true">×</span>
                        </button>
                        <h4 class="modal-title">Change Offer Label</h4>
                    </div>
                    <div class="form-horizontal">
                        <div class="modal-body">
                            {{ Form::hidden('studentIdArr', NULL, array('id' => 'studentIdArr')) }}
                            <div class="form-group">
                                <label for="inputEmail3" class="col-sm-4 control-label"> Enter Offer Label : <span id=""
                                        class="required-field">*<div></div></span></label>
                                <div class="col-sm-5">
                                    {{ Form::select('offer_label', [''=>'--Select Offer Label--'] + $arrOfferLabel
                                    ,null,array('class' => 'form-control', 'id' => 'offer_label')) }}
                                </div>
                            </div>

                        </div>
                        <div class="modal-footer">
                            <button data-dismiss="modal" class="btn btn-danger" type="button">No</button>
                            <button type="submit" class="btn btn-info save-offer-label">Save</button>
                        </div>
                    </div>
                </div>

            </div>
        </div>

        <div class="modal fade" id="generateIDModal" role="dialog">
            <div class="modal-dialog">

                <div class="modal-content">
                    <div class="modal-header">
                        <button aria-label="Close" data-dismiss="modal" class="close" type="button">
                            <span aria-hidden="true">×</span>
                        </button>
                        <h4 class="modal-title coe-title">Generate Permanent student ID from Application ID : <span
                                id='app_id'></span> </h4>
                    </div>
                    {{ Form::open( array('class' => 'form-horizontal vertical-add-form','method' => 'post','id'=>'')) }}
                    <div class="modal-body">
                        <div class="box box-info">
                            <div class="box-body">
                                <div class="row">
                                    <div class="col-sm-12">
                                        {{ Form::hidden('_token', csrf_token(), array()) }}

                                        <?php echo Form::hidden('student_id', null, ['class' => 'form-control student_id', 'id' => 'student_id']); ?>
                                        <?php echo Form::hidden('auto_no', null, ['class' => 'form-control', 'id' => 'auto_no']); ?>
                                        <?php echo Form::hidden('idType', null, ['class' => 'form-control', 'id' => 'idType']); ?>
                                        <?php echo Form::hidden('primaryID', null, ['class' => 'form-control', 'id' => 'primaryID']); ?>

                                        <div class="form-group">
                                            <label for="inputGeneratedID" class="col-sm-4 control-label"> Recommended
                                                student ID : </label>
                                            <div class="col-sm-4">
                                                {{ Form::text('generated_id' , NULL , array('class' => 'form-control
                                                generated_id', 'id' => 'generated_id')) }}
                                            </div>
                                            <div class="col-sm-3">
                                                <a class="check_avail" data-id-type="generate" id="check_avail"
                                                    style="cursor: pointer;">Check Available ?</a>
                                            </div>
                                        </div>
                                        <div class="form-group">
                                            <label for="inputEmail3" class="col-sm-4 control-label"> Student ID format
                                                legend :</label>
                                            <div class="col-sm-4">
                                                <span id='generateIDFormate'></span>
                                                {{ Form::text('id_formate' , NULL , array('class' => 'form-control',
                                                'id' => 'id_formate', 'disabled','placeholder'=>'Student ID format Exa:
                                                20180000')) }}
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="modal-footer">
                        <button data-dismiss="modal" class="btn btn-danger" type="button">Cancel</button>
                        <button type="button" class="btn btn-info generateIDNow" data-status="true">Generate
                            Now</button>
                    </div>
                    {{ Form::close() }}
                </div>

            </div>
        </div>

        <div class="modal fade" id="reserveIDModal" role="dialog">
            <div class="modal-dialog">
                <div class="modal-content">
                    <div class="modal-header">
                        <button aria-label="Close" data-dismiss="modal" class="close" type="button">
                            <span aria-hidden="true">×</span>
                        </button>
                        <h4 class="modal-title coe-title">Generate Reserve ID </h4>
                    </div>
                    <div class="form-horizontal">
                        <div class="modal-body">
                            {{ Form::hidden('_token', csrf_token(), array()) }}

                            <?php echo Form::hidden('student_id', null, ['class' => 'form-control', 'id' => 'student_id']); ?>
                            <?php echo Form::hidden('auto_no', null, ['class' => 'form-control', 'id' => 'auto_no']); ?>

                            <div class="form-group">
                                <label for="inputGeneratedID" class="col-sm-4 control-label"> Offer ID : </label>
                                <div class="col-sm-4">
                                    <span id="offer_id" class="offer_id"></span>
                                    <!--<label class="col-sm-4 control-label offer_id"> </label>-->
                                </div>
                            </div>
                            <div class="form-group">
                                <label for="inputEmail3" class="col-sm-4 control-label"> Suggested Reserve ID :</label>
                                <div class="col-sm-4">
                                    {{ Form::text('reserve_id' , NULL , array('class' => 'form-control generated_id',
                                    'id' => 'reserve_id')) }}
                                </div>
                                <div class="col-sm-3">
                                    <a class="check_available" data-id-type="reserve" id="check_available">Check
                                        Available ?</a>
                                </div>
                            </div>
                        </div>
                        <div class="modal-footer">
                            <button data-dismiss="modal" class="btn btn-danger" type="button">Cancel</button>
                            <button type="submit" class="btn btn-info reserveIDNow">Reserve Now</button>
                        </div>
                    </div>
                </div>
            </div>
        </div>
        <!-- /.row -->
    </section>
</div>

<style>
    .error {
        border: 1px solid red !important;
    }

    .add-btn-block {
        padding-top: 5px !important;
        padding-right: 10px !important;
        display: inline-block;
    }

    .checkboxlist .form-group {
        padding-left: 15px !important;
    }

    .checkboxlist .form-group label {
        padding-left: 0px !important;
    }

    #animation-dropdown-offerlabel.in {
        visibility: inherit;
    }

    #date-filter.in {
        visibility: inherit;
    }

    .dateinput {
        padding: 5px;
        border: 1px solid #dfe2e5;
    }

    .form-group-wrapper {
        padding: 10px 10px 10px 0px !important;
    }

    .appleyeddatefilter {
        height: 25px;
    }

    .resetfilter {
        border-radius: 25px;
        -webkit-box-shadow: none;
        box-shadow: none;
        border: 0px solid transparent !important;
        font-size: inherit;
        background-image: linear-gradient(to bottom, rgba(255, 255, 255, 0.5), rgba(255, 255, 255, 0));
        font-weight: 500;
        font-style: inherit;
        font-stretch: inherit;
        letter-spacing: inherit;
        text-align: center;
        color: #ffffff;
    }

    #offerManageStudent_paginate {
        margin-bottom: 60px;
    }

    .big-checkbox {
        display: inline-block;
        width: 16px;
        height: 16px;
        position: relative;
        vertical-align: middle;
        border: 2px solid #ccc;
        border-radius: 5px;
        cursor: pointer;
    }
</style>
<!-- /.content -->
<!-- /.content-wrapper -->
<script>
    const SHORT_COURSE_TYPE_ID = "{{ $SHORT_COURSE_TYPE_ID }}";
</script>
@endsection