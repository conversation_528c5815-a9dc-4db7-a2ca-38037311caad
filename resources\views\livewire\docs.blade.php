<div x-data="{ toggle: false }"
     x-on:keydown.escape.window="toggle=false">

    <a href="#"
       x-on:click.prevent="toggle=true;"
       title="{{ $this->getTooltipText() }}"
       class="text-blue-500 transition-colors duration-200 hover:text-blue-700">
        @if ($title)
        {{ $title}}
        @else
            <svg width="16"
                 height="16"
                 viewBox="0 0 16 16"
                 fill="none"
                 xmlns="http://www.w3.org/2000/svg">
                <path d="M8 0C12.4183 0 16 3.58172 16 8C16 12.4183 12.4183 16 8 16C3.58172 16 0 12.4183 0 8C0 3.58172 3.58172 0 8 0ZM8 1C4.13401 1 1 4.13401 1 8C1 11.866 4.13401 15 8 15C11.866 15 15 11.866 15 8C15 4.13401 11.866 1 8 1ZM8 11.5C8.41421 11.5 8.75 11.8358 8.75 12.25C8.75 12.6642 8.41421 13 8 13C7.58579 13 7.25 12.6642 7.25 12.25C7.25 11.8358 7.58579 11.5 8 11.5ZM8 3.5C9.38071 3.5 10.5 4.61929 10.5 6C10.5 6.72959 10.1848 7.40774 9.6513 7.8771L9.49667 8.00243L9.27817 8.16553L9.19065 8.23718C9.1348 8.28509 9.08354 8.33373 9.03456 8.38592C8.69627 8.74641 8.5 9.24223 8.5 10C8.5 10.2761 8.27614 10.5 8 10.5C7.72386 10.5 7.5 10.2761 7.5 10C7.5 8.98796 7.79312 8.24747 8.30535 7.70162C8.41649 7.5832 8.53202 7.47988 8.66094 7.37874L8.90761 7.19439L9.02561 7.09468C9.325 6.81435 9.5 6.42206 9.5 6C9.5 5.17157 8.82843 4.5 8 4.5C7.17157 4.5 6.5 5.17157 6.5 6C6.5 6.27614 6.27614 6.5 6 6.5C5.72386 6.5 5.5 6.27614 5.5 6C5.5 4.61929 6.61929 3.5 8 3.5Z"
                      fill="#9CA3AF" />
            </svg>
        @endif
    </a>
    {{-- In work, do what you enjoy. --}}
    <div x-show="toggle"
         x-cloak
         x-trap.noscroll.inert="toggle"
         class="relative z-10"
         aria-labelledby="modal-title"
         role="dialog"
         aria-modal="true">
        <!--
          Background backdrop, show/hide based on modal state.

          Entering: "ease-out duration-300"
            From: "opacity-0"
            To: "opacity-100"
          Leaving: "ease-in duration-200"
            From: "opacity-100"
            To: "opacity-0"
        -->
        <div class="fixed inset-0 bg-gray-500/75 transition-opacity"
             aria-hidden="true"></div>

        <div class="fixed inset-0 z-10 w-screen overflow-y-auto">
            <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-center sm:p-0">
                <!--
              Modal panel, show/hide based on modal state.

              Entering: "ease-out duration-300"
                From: "opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                To: "opacity-100 translate-y-0 sm:scale-100"
              Leaving: "ease-in duration-200"
                From: "opacity-100 translate-y-0 sm:scale-100"
                To: "opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
            -->
                <div
                     class="relative transform max-h-[90vh] overflow-y-auto flex flex-col rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-2xl sm:p-6">
                    <div class="absolute right-4 top-4">
                        <button type="button"
                                title="Close"
                                x-on:click.prevent="toggle=false;"
                                class="rounded-md bg-white text-gray-500 hover:text-gray-900">
                            <span class="sr-only">Close</span>
                            <svg class="h-6 w-6"
                                 fill="none"
                                 viewBox="0 0 24 24"
                                 stroke-width="1.5"
                                 stroke="currentColor"
                                 aria-hidden="true">
                                <path stroke-linecap="round"
                                      stroke-linejoin="round"
                                      d="M6 18L18 6M6 6l12 12" />
                            </svg>
                        </button>
                    </div>
                    <div class="ck-content flex-1 overflow-y-auto">
                        {{-- <div class="mx-auto flex size-12 items-center justify-center rounded-full bg-green-100">
                            <svg class="size-6 text-green-600"
                                 fill="none"
                                 viewBox="0 0 24 24"
                                 stroke-width="1.5"
                                 stroke="currentColor"
                                 aria-hidden="true"
                                 data-slot="icon">
                                <path stroke-linecap="round"
                                      stroke-linejoin="round"
                                      d="m4.5 12.75 6 6 9-13.5" />
                            </svg>
                        </div> --}}
                        <div class="mt-3 sm:mt-5">
                            {{--                            <h3 id="modal-title" class="text-base font-semibold text-gray-900"> --}}
                            {{--                                {{ ucfirst(pathinfo($path, PATHINFO_FILENAME)) }} Setup Guide --}}
                            {{--                            </h3> --}}
                            <div class="docs-content mt-2">
                                {!! $this->content !!}
                            </div>
                        </div>
                    </div>
                    <div class="mt-5 sm:mt-6">
                        {{-- <button type="button" class="inline-flex w-full justify-center rounded-md bg-indigo-600 px-3 py-2 text-sm font-semibold text-white shadow-sm hover:bg-indigo-500 focus-visible:outline focus-visible:outline-2 focus-visible:outline-offset-2 focus-visible:outline-indigo-600 sm:col-start-2">Deactivate</button> --}}
                        <div class="flex w-full items-center justify-between">
                            <p class="text-xs text-gray-500">Press <span
                                      class="rounded border border-gray-200 bg-gray-100 px-1 py-0.5 font-mono">Esc</span>
                                to close</p>
                            <a type="button"
                               href="#"
                               x-on:click.prevent="toggle=false;"
                               class="ml-auto inline-flex justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50">Close</a>
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>
