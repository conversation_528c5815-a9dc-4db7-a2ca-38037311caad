@extends('frontend.layouts.frontend')

@section('title', 'Student Risk Assessment Settings')

@section('content')

<div class="mx-auto my-8 bg-white rounded-xl shadow-lg overflow-hidden" x-data="{
            riskAssessmentEnabled: {{ old('risk_assessment_enabled', true) ? 'true' : 'false' }},
            attendanceEnabled: {{ old('attendance_enabled', true) ? 'true' : 'false' }},
            moodleEnabled: {{ old('moodle_enabled', true) ? 'true' : 'false' }},
            resultsEnabled: {{ old('results_enabled', true) ? 'true' : 'false' }},
            paymentEnabled: {{ old('payment_enabled', true) ? 'true' : 'false' }}
        }">
        

        <div class="p-10">
            

            <form method="POST" action="{{ route('student-risk-assessment-settings.store') }}">
                @csrf

                <!-- Enable/Disable Toggle -->
                <div class="mb-8 border border-gray-200 rounded-2xl overflow-hidden bg-gray-50">
                    <div class="bg-gray-100 px-8 py-5 border-b border-gray-200">
                        <h2 class="text-xl font-semibold text-gray-700 flex items-center">
                            <svg class="w-6 h-6 mr-3 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M12 6V4m0 2a2 2 0 100 4m0-4a2 2 0 110 4m-6 8a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4m6 6v10m6-2a2 2 0 100-4m0 4a2 2 0 100 4m0-4v2m0-6V4"></path>
                            </svg>
                            Risk Assessment Settings
                        </h2>
                    </div>
                    <div class="p-8">
                        <div class="flex items-center justify-between">
                            <div>
                                <h3 class="text-lg font-semibold text-gray-700 mb-2">Enable Risk Assessment</h3>
                                <p class="text-gray-600">Turn on automated risk assessment monitoring for students</p>
                            </div>
                            <div class="flex items-center">
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" name="risk_assessment_enabled" value="1"
                                           {{ old('risk_assessment_enabled', true) ? 'checked' : '' }}
                                           class="sr-only peer"
                                           x-model="riskAssessmentEnabled">
                                    <div class="w-14 h-7 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-0.5 after:left-[4px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-6 after:w-6 after:transition-all peer-checked:bg-blue-600"></div>
                                    <span class="ml-3 text-sm font-medium text-gray-700" x-text="riskAssessmentEnabled ? 'Enabled' : 'Disabled'"></span>
                                </label>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Risk Assessment Configuration (Hidden when disabled) -->
                <div x-show="riskAssessmentEnabled" x-transition:enter="transition ease-out duration-300" x-transition:enter-start="opacity-0 transform scale-95" x-transition:enter-end="opacity-100 transform scale-100" x-transition:leave="transition ease-in duration-200" x-transition:leave-start="opacity-100 transform scale-100" x-transition:leave-end="opacity-0 transform scale-95">

                <!-- Risk Assessment Type Section -->
                <div class="mb-8 border border-gray-200 rounded-2xl overflow-hidden bg-gray-50">
                    <div class="bg-gray-100 px-8 py-5 border-b border-gray-200">
                        <h2 class="text-xl font-semibold text-gray-700 flex items-center">
                            <svg class="w-6 h-6 mr-3 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10.325 4.317c.426-1.756 2.924-1.756 3.35 0a1.724 1.724 0 002.573 1.066c1.543-.94 3.31.826 2.37 2.37a1.724 1.724 0 001.065 2.572c1.756.426 1.756 2.924 0 3.35a1.724 1.724 0 00-1.066 2.573c.94 1.543-.826 3.31-2.37 2.37a1.724 1.724 0 00-2.572 1.065c-.426 1.756-2.924 1.756-3.35 0a1.724 1.724 0 00-2.573-1.066c-1.543.94-3.31-.826-2.37-2.37a1.724 1.724 0 00-1.065-2.572c-1.756-.426-1.756-2.924 0-3.35a1.724 1.724 0 001.066-2.573c-.94-1.543.826-3.31 2.37-2.37.996.608 2.296.07 2.572-1.065z"></path>
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M15 12a3 3 0 11-6 0 3 3 0 016 0z"></path>
                            </svg>
                            Risk Assessment Type
                        </h2>
                    </div>
                    <div class="p-8">
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6 text-blue-800 italic">
                            Risk Assessment Type: Configure automated monitoring and email notifications
                        </div>
                        <div class="grid grid-cols-1 md:grid-cols-3 gap-6">
                            <div class="bg-white rounded-xl p-6 shadow-sm border-2 border-green-500 hover:-translate-y-1 transition-transform">
                                <div class="flex items-center mb-5 font-semibold text-lg">
                                    <div class="w-3 h-3 bg-green-500 rounded-full mr-3"></div>
                                    Low Risk
                                </div>
                                <div class="mb-5">
                                    <label class="block mb-2 font-medium text-gray-700">Check After Weeks:</label>
                                    <div class="flex items-center gap-2">
                                        <input type="number" name="category_low_risk_weeks" value="{{ old('category_low_risk_weeks', 3) }}" min="1" max="52" required
                                               class="flex-1 px-3 py-2 border-2 border-gray-300 rounded-lg focus:border-indigo-500 focus:outline-none">
                                        <span class="font-medium text-gray-500">weeks</span>
                                    </div>
                                </div>
                                <div class="mb-5">
                                    <label class="block mb-2 font-medium text-gray-700">Triggered Email:</label>
                                    <select name="category_low_risk_email" class="w-full px-3 py-2 border-2 border-gray-300 rounded-lg focus:border-indigo-500 focus:outline-none bg-white">
                                        <option value="">Select Email Template</option>
                                        <option value="template1" {{ old('category_low_risk_email') == 'template1' ? 'selected' : '' }}>Low Risk Warning Template</option>
                                        <option value="template2" {{ old('category_low_risk_email') == 'template2' ? 'selected' : '' }}>Early Intervention Template</option>
                                        <option value="template3" {{ old('category_low_risk_email') == 'template3' ? 'selected' : '' }}>Support Resources Template</option>
                                    </select>
                                </div>
                            </div>
                            <div class="bg-white rounded-xl p-6 shadow-sm border-2 border-amber-500 hover:-translate-y-1 transition-transform">
                                <div class="flex items-center mb-5 font-semibold text-lg">
                                    <div class="w-3 h-3 bg-amber-500 rounded-full mr-3"></div>
                                    Medium Risk
                                </div>
                                <div class="mb-5">
                                    <label class="block mb-2 font-medium text-gray-700">Check After Weeks:</label>
                                    <div class="flex items-center gap-2">
                                        <input type="number" name="category_medium_risk_weeks" value="{{ old('category_medium_risk_weeks', 6) }}" min="1" max="52" required
                                               class="flex-1 px-3 py-2 border-2 border-gray-300 rounded-lg focus:border-indigo-500 focus:outline-none">
                                        <span class="font-medium text-gray-500">weeks</span>
                                    </div>
                                </div>
                                <div class="mb-5">
                                    <label class="block mb-2 font-medium text-gray-700">Triggered Email:</label>
                                    <select name="category_medium_risk_email" class="w-full px-3 py-2 border-2 border-gray-300 rounded-lg focus:border-indigo-500 focus:outline-none bg-white">
                                        <option value="">Select Email Template</option>
                                        <option value="template1" {{ old('category_medium_risk_email') == 'template1' ? 'selected' : '' }}>Medium Risk Alert Template</option>
                                        <option value="template2" {{ old('category_medium_risk_email') == 'template2' ? 'selected' : '' }}>Academic Support Template</option>
                                        <option value="template3" {{ old('category_medium_risk_email') == 'template3' ? 'selected' : '' }}>Counseling Referral Template</option>
                                    </select>
                                </div>
                            </div>
                            <div class="bg-white rounded-xl p-6 shadow-sm border-2 border-red-500 hover:-translate-y-1 transition-transform">
                                <div class="flex items-center mb-5 font-semibold text-lg">
                                    <div class="w-3 h-3 bg-red-500 rounded-full mr-3"></div>
                                    High Risk
                                </div>
                                <div class="mb-5">
                                    <label class="block mb-2 font-medium text-gray-700">Check After Weeks:</label>
                                    <div class="flex items-center gap-2">
                                        <input type="number" name="category_high_risk_weeks" value="{{ old('category_high_risk_weeks', 12) }}" min="1" max="52" required
                                               class="flex-1 px-3 py-2 border-2 border-gray-300 rounded-lg focus:border-indigo-500 focus:outline-none">
                                        <span class="font-medium text-gray-500">weeks</span>
                                    </div>
                                </div>
                                <div class="mb-5">
                                    <label class="block mb-2 font-medium text-gray-700">Triggered Email:</label>
                                    <select name="category_high_risk_email" class="w-full px-3 py-2 border-2 border-gray-300 rounded-lg focus:border-indigo-500 focus:outline-none bg-white">
                                        <option value="">Select Email Template</option>
                                        <option value="template1" {{ old('category_high_risk_email') == 'template1' ? 'selected' : '' }}>High Risk Urgent Template</option>
                                        <option value="template2" {{ old('category_high_risk_email') == 'template2' ? 'selected' : '' }}>Immediate Intervention Template</option>
                                        <option value="template3" {{ old('category_high_risk_email') == 'template3' ? 'selected' : '' }}>Administrator Alert Template</option>
                                    </select>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                <!-- Risk Assessment Category Section -->
                <div class="mb-8 border border-gray-200 rounded-2xl overflow-hidden bg-gray-50">
                    <div class="bg-gray-100 px-8 py-5 border-b border-gray-200">
                        <h2 class="text-xl font-semibold text-gray-700 flex items-center">
                            <svg class="w-6 h-6 mr-3 text-gray-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2m-3 7h3m-3 4h3m-6-4h.01M9 16h.01"></path>
                            </svg>
                            Risk Assessment Category
                        </h2>
                    </div>
                    <div class="p-8">
                        <div class="bg-blue-50 border border-blue-200 rounded-lg p-4 mb-6 text-blue-800 italic">
                            Configure risk criteria and thresholds for different assessment categories
                        </div>

                        <!-- Student Attendance Subsection -->
                        <div class="mb-8">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-semibold text-gray-700 flex items-center">
                                    <span class="bg-blue-100 p-2 rounded-lg mr-3">
                                        <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 19v-6a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2a2 2 0 002-2zm0 0V9a2 2 0 012-2h2a2 2 0 012 2v10m-6 0a2 2 0 002 2h2a2 2 0 002-2m0 0V5a2 2 0 012-2h2a2 2 0 012 2v14a2 2 0 01-2 2h-2a2 2 0 01-2-2z"></path>
                                        </svg>
                                    </span>
                                    Student Attendance
                                </h3>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" name="attendance_enabled" value="1"
                                           {{ old('attendance_enabled', true) ? 'checked' : '' }}
                                           class="sr-only peer"
                                           x-model="attendanceEnabled">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                    <span class="ml-2 text-sm font-medium text-gray-700" x-text="attendanceEnabled ? 'Enabled' : 'Disabled'"></span>
                                </label>
                            </div>
                            <div x-show="attendanceEnabled" x-transition>
                                <div class="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4 text-blue-800 text-sm italic">
                                    Applicable Policy: Monitor student attendance patterns to identify at-risk students
                                </div>
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    <div class="bg-white rounded-lg p-4 shadow-sm border-2 border-green-500">
                                        <div class="flex items-center mb-3 font-semibold">
                                            <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                                            Low Risk
                                        </div>
                                        <div>
                                            <label class="block mb-1 font-medium text-gray-700 text-sm">Threshold Value:</label>
                                            <div class="flex items-center gap-2">
                                                <input type="number" name="attendance_low_risk" value="{{ old('attendance_low_risk', 10) }}" min="0" max="100" required
                                                       class="flex-1 px-2 py-1 border-2 border-gray-300 rounded focus:border-indigo-500 focus:outline-none text-sm">
                                                <span class="font-semibold text-gray-500 text-sm">%</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="bg-white rounded-lg p-4 shadow-sm border-2 border-amber-500">
                                        <div class="flex items-center mb-3 font-semibold">
                                            <div class="w-3 h-3 bg-amber-500 rounded-full mr-2"></div>
                                            Medium Risk
                                        </div>
                                        <div>
                                            <label class="block mb-1 font-medium text-gray-700 text-sm">Threshold Value:</label>
                                            <div class="flex items-center gap-2">
                                                <input type="number" name="attendance_medium_risk" value="{{ old('attendance_medium_risk', 50) }}" min="0" max="100" required
                                                       class="flex-1 px-2 py-1 border-2 border-gray-300 rounded focus:border-indigo-500 focus:outline-none text-sm">
                                                <span class="font-semibold text-gray-500 text-sm">%</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="bg-white rounded-lg p-4 shadow-sm border-2 border-red-500">
                                        <div class="flex items-center mb-3 font-semibold">
                                            <div class="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                                            High Risk
                                        </div>
                                        <div>
                                            <label class="block mb-1 font-medium text-gray-700 text-sm">Threshold Value:</label>
                                            <div class="flex items-center gap-2">
                                                <input type="number" name="attendance_high_risk" value="{{ old('attendance_high_risk', 80) }}" min="0" max="100" required
                                                       class="flex-1 px-2 py-1 border-2 border-gray-300 rounded focus:border-indigo-500 focus:outline-none text-sm">
                                                <span class="font-semibold text-gray-500 text-sm">%</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Moodle Access Subsection -->
                        <div class="mb-8">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-semibold text-gray-700 flex items-center">
                                    <span class="bg-blue-100 p-2 rounded-lg mr-3">
                                        <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9.75 17L9 20l-1 1h8l-1-1-.75-3M3 13h18M5 17h14a2 2 0 002-2V5a2 2 0 00-2-2H5a2 2 0 00-2 2v10a2 2 0 002 2z"></path>
                                        </svg>
                                    </span>
                                    Moodle Access
                                </h3>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" name="moodle_enabled" value="1"
                                           {{ old('moodle_enabled', true) ? 'checked' : '' }}
                                           class="sr-only peer"
                                           x-model="moodleEnabled">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                    <span class="ml-2 text-sm font-medium text-gray-700" x-text="moodleEnabled ? 'Enabled' : 'Disabled'"></span>
                                </label>
                            </div>
                            <div x-show="moodleEnabled" x-transition>
                                <div class="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4 text-blue-800 text-sm italic">
                                    Applicable Policy: Track student engagement with learning management system
                                </div>
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    <div class="bg-white rounded-lg p-4 shadow-sm border-2 border-green-500">
                                        <div class="flex items-center mb-3 font-semibold">
                                            <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                                            Low Risk
                                        </div>
                                        <div class="bg-yellow-50 border border-yellow-300 rounded-lg p-3 text-yellow-800 italic text-center text-sm">
                                            Need to discuss threshold criteria
                                        </div>
                                    </div>
                                    <div class="bg-white rounded-lg p-4 shadow-sm border-2 border-amber-500">
                                        <div class="flex items-center mb-3 font-semibold">
                                            <div class="w-3 h-3 bg-amber-500 rounded-full mr-2"></div>
                                            Medium Risk
                                        </div>
                                        <div class="bg-yellow-50 border border-yellow-300 rounded-lg p-3 text-yellow-800 italic text-center text-sm">
                                            Need to discuss threshold criteria
                                        </div>
                                    </div>
                                    <div class="bg-white rounded-lg p-4 shadow-sm border-2 border-red-500">
                                        <div class="flex items-center mb-3 font-semibold">
                                            <div class="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                                            High Risk
                                        </div>
                                        <div class="bg-yellow-50 border border-yellow-300 rounded-lg p-3 text-yellow-800 italic text-center text-sm">
                                            Need to discuss threshold criteria
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Student Results Subsection -->
                        <div class="mb-8">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-semibold text-gray-700 flex items-center">
                                    <span class="bg-blue-100 p-2 rounded-lg mr-3">
                                        <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M7 12l3-3 3 3 4-4M8 21l4-4 4 4M3 4h18M4 4h16v12a1 1 0 01-1 1H5a1 1 0 01-1-1V4z"></path>
                                        </svg>
                                    </span>
                                    Student Results
                                </h3>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" name="results_enabled" value="1"
                                           {{ old('results_enabled', true) ? 'checked' : '' }}
                                           class="sr-only peer"
                                           x-model="resultsEnabled">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                    <span class="ml-2 text-sm font-medium text-gray-700" x-text="resultsEnabled ? 'Enabled' : 'Disabled'"></span>
                                </label>
                            </div>
                            <div x-show="resultsEnabled" x-transition>
                                <div class="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4 text-blue-800 text-sm italic">
                                    Applicable Policy: Monitor academic performance to identify struggling students
                                </div>
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    <div class="bg-white rounded-lg p-4 shadow-sm border-2 border-green-500">
                                        <div class="flex items-center mb-3 font-semibold">
                                            <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                                            Low Risk
                                        </div>
                                        <div>
                                            <label class="block mb-1 font-medium text-gray-700 text-sm">Threshold:</label>
                                            <div class="flex items-center gap-2">
                                                <input type="number" name="results_low_risk" value="{{ old('results_low_risk', 10) }}" min="0" max="100" required
                                                       class="flex-1 px-2 py-1 border-2 border-gray-300 rounded focus:border-indigo-500 focus:outline-none text-sm">
                                                <span class="font-semibold text-gray-500 text-sm">%</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="bg-white rounded-lg p-4 shadow-sm border-2 border-amber-500">
                                        <div class="flex items-center mb-3 font-semibold">
                                            <div class="w-3 h-3 bg-amber-500 rounded-full mr-2"></div>
                                            Medium Risk
                                        </div>
                                        <div>
                                            <label class="block mb-1 font-medium text-gray-700 text-sm">Threshold:</label>
                                            <div class="flex items-center gap-2">
                                                <input type="number" name="results_medium_risk" value="{{ old('results_medium_risk', 30) }}" min="0" max="100" required
                                                       class="flex-1 px-2 py-1 border-2 border-gray-300 rounded focus:border-indigo-500 focus:outline-none text-sm">
                                                <span class="font-semibold text-gray-500 text-sm">%</span>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="bg-white rounded-lg p-4 shadow-sm border-2 border-red-500">
                                        <div class="flex items-center mb-3 font-semibold">
                                            <div class="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                                            High Risk
                                        </div>
                                        <div>
                                            <label class="block mb-1 font-medium text-gray-700 text-sm">Threshold:</label>
                                            <div class="flex items-center gap-2">
                                                <input type="number" name="results_high_risk" value="{{ old('results_high_risk', 60) }}" min="0" max="100" required
                                                       class="flex-1 px-2 py-1 border-2 border-gray-300 rounded focus:border-indigo-500 focus:outline-none text-sm">
                                                <span class="font-semibold text-gray-500 text-sm">%</span>
                                            </div>
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>

                        <!-- Student Fee Payment Subsection -->
                        <div class="mb-8">
                            <div class="flex items-center justify-between mb-4">
                                <h3 class="text-lg font-semibold text-gray-700 flex items-center">
                                    <span class="bg-blue-100 p-2 rounded-lg mr-3">
                                        <svg class="w-5 h-5 text-blue-600" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M17 9V7a2 2 0 00-2-2H5a2 2 0 00-2 2v6a2 2 0 002 2h2m2 4h10a2 2 0 002-2v-6a2 2 0 00-2-2H9a2 2 0 00-2 2v6a2 2 0 002 2zm7-5a2 2 0 11-4 0 2 2 0 014 0z"></path>
                                        </svg>
                                    </span>
                                    Student Fee Payment
                                </h3>
                                <label class="relative inline-flex items-center cursor-pointer">
                                    <input type="checkbox" name="payment_enabled" value="1"
                                           {{ old('payment_enabled', true) ? 'checked' : '' }}
                                           class="sr-only peer"
                                           x-model="paymentEnabled">
                                    <div class="w-11 h-6 bg-gray-200 peer-focus:outline-none peer-focus:ring-4 peer-focus:ring-blue-300 rounded-full peer peer-checked:after:translate-x-full peer-checked:after:border-white after:content-[''] after:absolute after:top-[2px] after:left-[2px] after:bg-white after:border-gray-300 after:border after:rounded-full after:h-5 after:w-5 after:transition-all peer-checked:bg-blue-600"></div>
                                    <span class="ml-2 text-sm font-medium text-gray-700" x-text="paymentEnabled ? 'Enabled' : 'Disabled'"></span>
                                </label>
                            </div>
                            <div x-show="paymentEnabled" x-transition>
                                <div class="bg-blue-50 border border-blue-200 rounded-lg p-3 mb-4 text-blue-800 text-sm italic">
                                    Applicable Policy: Track payment status to identify financial risk factors
                                </div>
                                <div class="grid grid-cols-1 md:grid-cols-3 gap-4">
                                    <div class="bg-white rounded-lg p-4 shadow-sm border-2 border-green-500">
                                        <div class="flex items-center mb-3 font-semibold">
                                            <div class="w-3 h-3 bg-green-500 rounded-full mr-2"></div>
                                            Low Risk
                                        </div>
                                        <div class="bg-yellow-50 border border-yellow-300 rounded-lg p-3 text-yellow-800 italic text-center text-sm">
                                            Need to check if payment is paid
                                        </div>
                                    </div>
                                    <div class="bg-white rounded-lg p-4 shadow-sm border-2 border-amber-500">
                                        <div class="flex items-center mb-3 font-semibold">
                                            <div class="w-3 h-3 bg-amber-500 rounded-full mr-2"></div>
                                            Medium Risk
                                        </div>
                                        <div class="bg-yellow-50 border border-yellow-300 rounded-lg p-3 text-yellow-800 italic text-center text-sm">
                                            Need to check if payment is paid
                                        </div>
                                    </div>
                                    <div class="bg-white rounded-lg p-4 shadow-sm border-2 border-red-500">
                                        <div class="flex items-center mb-3 font-semibold">
                                            <div class="w-3 h-3 bg-red-500 rounded-full mr-2"></div>
                                            High Risk
                                        </div>
                                        <div class="bg-yellow-50 border border-yellow-300 rounded-lg p-3 text-yellow-800 italic text-center text-sm">
                                            Need to check if payment is paid
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>

                </div> <!-- End of conditional div -->

                <div class="text-center mt-10">
                    <button type="submit" class="bg-gradient-to-r from-indigo-600 to-purple-600 text-white px-10 py-4 text-lg font-semibold rounded-xl hover:-translate-y-1 hover:shadow-xl transition-all duration-300 shadow-lg">
                        Save Configuration
                    </button>
                </div>
            </form>
        </div>
    </div>

@endsection
