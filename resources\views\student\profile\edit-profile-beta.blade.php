<x-v2.layouts.v2-layout :newKendo="true">
    <x-slot name="cssHeader">
        <link rel="stylesheet" href="{{ asset('v2/css/student/student-view.css') }}">
    </x-slot>

    @section('title', $pagetitle)
    <div class="p-6 w-full space-y-8">
        <x-v2.templates.student-edit-profile :userId="$user_id" :studentDetail="$studentDetail" />

    </div>

    <div id="addressHistoryModal" style="display: none;" class="!pb-6">
        <x-v2.grid-table id="addressHistoryGrid" :borderless="true"></x-v2.grid-table>
    </div>
    <div id="profileHistoryModal" style="display: none;" class="!pb-6">
        <x-v2.grid-table id="profileHistoryGrid" :borderless="true"></x-v2.grid-table>
    </div>
    <div id="emergencyDetailsHistoryModal" style="display: none;" class="!pb-6">
        <x-v2.grid-table id="emergencyDetailsHistoryGrid" :borderless="true"></x-v2.grid-table>
    </div>
    <x-slot name="jsFooter">
        <script src="{{ asset('v2/js/student/student-edit.js') }}"></script>
        <script type="text/javascript"
            src="https://maps.googleapis.com/maps/api/js?key={{ Config::get('constants.google_place_key') }}&libraries=places"
            defer>
        </script>

    </x-slot>
    <x-slot name="fixVariables">
        let studentDetail = @json($studentDetail);
        let user_id = {{ $user_id }};
        let dateFormatFrontSideJS = "{{ Config::get('app.dateFormatFrontSideJS') }}";

    </x-slot>

</x-v2.layouts.v2-layout>