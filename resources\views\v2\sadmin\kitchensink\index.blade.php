<x-v2.layouts.v2-layout>
    @section('title', 'Kitchen Sink')
    <x-v2.container>
        <x-slot name="tabs">
            <x-v2.tabstrip-item label="Buttons" class="k-state-active" labelClass="itemLabelClass" id="itemId">
            </x-v2.tabstrip-item>
            <x-v2.tabstrip-item label="Cards" labelClass="itemLabelClass" id="itemId">
            </x-v2.tabstrip-item>
        </x-slot>
        <h1 class="text-2xl font-bold leading-7 text-gray-800">Kitchen Sink</h1>
        <span class="k-d-inline-block">Phone Number</span>
        <div class="tw-phone-mask-input flex">
            <input id="countries" />
            <input id="phone_number" />
        </div>
        <div class="p-6 w-full space-y-8">
            {{--
            <x-v2.templates.action-tooltip /> --}}
            {{--
            <x-v2.templates.trainer-edit-profile /> --}}
            {{-- <section class="agent-staff-invitaion">
                <div
                    style="padding: 60px;background: #fff; display: flex; justify-content: center; flex-direction: column; max-width: 600px; margin: 0 auto 32px; border-radius: 10px">
                    <h1 style="color: #111827; font-weight: 400; font-size: 34px;margin-bottom:24px">You are invited to
                        Galaxy</h1>
                    <p style="margin-bottom:16px; font-size: 14px; font-weight: 400; color: #374151">You are invited to
                        galaxy
                        system as Agent
                    </p>
                    <p style="margin-bottom:16px; font-size: 14px; font-weight: 400; color: #374151">
                        Please create your profile using below link
                    </p>
                    <a href="#" target="_blank"
                        style="background: #1890FF; padding: 11px 32px;border-radius: 8px; color: #fff; font-weight: 500; max-width: 176px;cursor: pointer;margin-bottom:16px; text-transform: uppercase; font-size: 14px; text-align: center">
                        Setup Account
                    </a>
                    <p style="font-size: 14px; font-weight: 400; max-width: 110px; line-height: 20px">Thank you, Galaxy</p>
                </div>
                <div style="text-align: center">
                    <p style="color: #374151; margin-bottom:16px; font-size:14px">
                        Questions? <span style="color: #6B7280">get in touch with us</span> <br> <a
                            href="mailto: <EMAIL>" style="color: #1890FF"><EMAIL></a>
                    </p>
                </div>
            </section> --}}
            {{--
            <x-v2.templates.agent-edit-profile /> --}}
            {{-- <x-v2.skeleton.email></x-v2.skeleton.email> --}}
            {{--
            <x-v2.sync-bar /> --}}
            {{-- <div class="flex items-center">
                {TEXT TO COPY}
                <x-v2.copy data-text="Text to copy" />
            </div> --}}
            {{-- <x-notification-message variant="info">
                Successfully uploaded
                <x-slot name="cancel">
                    <x-v2.icons name="icon-cancel" width="16" height="16" class="text-current" />
                </x-slot>
            </x-notification-message> --}}
            {{-- Button --}}
            <x-v2.button loading="Saving..." size="lg" variant="secondary" id="buttonId" class="px-8">
                {{-- <img src="{{ asset('v2/img/moodle-logo.png') }}" class="mr-1" alt="searchIcon" /> --}}
                <span>Button Label</span>
            </x-v2.button>

            {{-- Checkbox --}}
            <x-v2.checkbox name="example_checkbox" label="Example Checkbox" :isChecked="false"></x-v2.checkbox>

            {{-- Modal --}}
            <div class="flex items-center justify-center p-6 border rounded-lg">

                <button class="tw-btn-primary tw-modal-trigger" data-modal="addNoteModal">Modal ID</button>
                {{-- Trigger
                button with id of Modal as data-modal attr --}}
            </div>
            <x-v2.modal id="addNoteModal" data-title="Add Note 0" data-width="60" data-top="20" data-left="30">
                <div class="space-y-4">
                    <h2 class="text-lg font-medium">Are you sure?</h2>
                    <p>Lorem ipsum dolor sit amet consectetur adipisicing elit. Ipsa quis distinctio recusandae sapiente
                        voluptatem commodi quam mollitia, velit at sit!</p>
                    <div class="container-stretch flex gap-4 items-center justify-end py-4 px-6 shadow-inner-top">
                        <button type="button" class="tw-modal-close btn-secondary">
                            <p class="text-sm font-medium leading-4 text-gray-700">CANCEL</p>
                        </button>
                        <button type="button" class="saveQuickActivityNote btn-primary">
                            <p class="text-sm font-medium leading-4 text-white">ADD NOTE</p>
                        </button>
                    </div>
                </div>
            </x-v2.modal>

            {{-- Drawer --}}
            <div class="flex items-center justify-center p-6 border rounded-lg">
                <button class="tw-btn-primary tw-drawer-trigger" data-drawer="addNoteDrawer">Drawer Open</button>
                {{--
                Trigger button with id of Modal as data-drawer attr --}}
            </div>
            <x-v2.drawer id="addNoteDrawer" data-title="Add Note 0" data-width="60">
                <div class="space-y-4">
                    <div class="tw-dialog-content mb-16">
                        <h2 class="text-lg font-medium">Are you sure?</h2>
                        <p>Lorem ipsum dolor sit amet consectetur adipisicing elit. Ipsa quis distinctio recusandae sapiente
                            voluptatem commodi quam mollitia, velit at sit!</p>
                    </div>

                    <div
                            class="tw-dialog-footer container-stretch absolute bottom-0 right-0 w-full flex gap-4 items-center justify-end py-4 px-6 shadow-inner-top bg-gray-50">
                        <button type="button" class="tw-drawer-close btn-secondary">
                            <p class="text-sm font-medium leading-4 text-gray-700">Cancel</p>
                        </button>
                        <button type="button" class="saveQuickActivityNote btn-primary">
                            <p class="text-sm font-medium leading-4 text-white">Add Note</p>
                        </button>
                    </div>
                </div>
            </x-v2.drawer>

            {{-- Dropdown --}}
            @php
                $options = ['Adelaide Campus', 'Brisbane Campus', 'Parramatta Campus'];
            @endphp
            {{--
            <x-v2.dropdown dropdownId="dropdownId" size="xs" :options="$options" buttonId="get" /> --}}

            {{-- Search Input --}}
            <x-v2.search-input id="" class="searchInActivityTabAll" style="width: 300px">
                <img src="{{ asset('v2/img/search.svg') }}" class="h-4 w-4" alt="searchIcon" />
            </x-v2.search-input>

            {{-- TabStrip --}}
            <x-v2.tabstrip id="tabstrip" class="tailwind or unique class">
                <x-slot:tabHeader>
                    <x-v2.tabstrip-item label="Paris" class="k-state-active" labelClass="tailwind or uniqueclass"
                                        id="uniqueId">
                        <x-slot:iconSlot>
                            <img src="{{ asset('v2/img/search.svg') }}" class="h-4 w-4" alt="searchIcon" />
                        </x-slot:iconSlot>
                    </x-v2.tabstrip-item>
                    <x-v2.tabstrip-item label="NewYork" labelClass="itemLabelClass" id="itemId">
                    </x-v2.tabstrip-item>
                </x-slot:tabHeader>
                <x-slot:tabContent>
                    <div class="tw-tabcontent-1">
                        <span class="rainy">&nbsp;</span>
                        <div class="weather">
                            <h2>17<span>&ordm;C</span></h2>
                            <p>Rainy weather in Paris.</p>
                        </div>
                    </div>
                    <div class="tw-tabcontent-2">
                        <span class="sunny">&nbsp;</span>
                        <div class="weather">
                            <h2>29<span>&ordm;C</span></h2>
                            <p>Sunny weather in New York.</p>
                        </div>
                    </div>
                </x-slot>
            </x-v2.tabstrip>

            {{-- ToggleButton --}}
            <x-v2.togglebutton></x-v2.togglebutton>

            {{-- Badge --}}
            <x-v2.badge variant="info">
                Badge Content
            </x-v2.badge>
            <x-v2.highlight-box variant="danger">
                <x-slot name="icon">

                </x-slot>
                <x-slot name="content">
                    This is the contnet of highlight box. The varaint
                    is
                </x-slot>
            </x-v2.highlight-box>
        </div>
    </x-v2.container>
    <x-slot name="jsFooter">
        {{-- <script src="{{ asset('v2/js/student/student-edit.js') }}"></script> --}}
        {{-- <script src="{{ asset('v2/js/agent/trainer.js') }}"></script>
        <script src="{{ asset('v2/js/agent/agent-profile.js') }}"></script> --}}
        <script src="{{ asset('v2/js/action-tooltip.js') }}"></script>
    </x-slot>
</x-v2.layouts.v2-layout>