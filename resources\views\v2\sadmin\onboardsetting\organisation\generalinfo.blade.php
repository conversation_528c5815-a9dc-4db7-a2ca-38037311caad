<x-v2.layouts.onboard>

    @section('title', $title)
    @section('keywords', $keywords)
    @section('description', $description)
    @section('mainmenu', $mainmenu)

    <x-slot name="cssHeader">
        <link rel="stylesheet" href="{{ asset('v2/css/sadmin/onboard.css') }}">
    </x-slot>

    <x-v2.onboardsetting.container>
        <x-slot name="formOpen">
            {{ Form::open( array('method' => 'post', 'class' => 'form-horizontal vertical-add-form', 'id' =>
            'general_info')) }}
            <input type="hidden" name="id" value="{{ (isset($id)) ? $id : '' }}" id="college_id" />
        </x-slot>
        <x-slot name="lwProgress">
            <livewire:onboarding.progress form="organization.general_info" />
        </x-slot>
        <x-slot name="content" class="!pr-6 !pl-8 !pt-6">
            <x-v2.onboardsetting.form-card>
                <x-slot name="title">
                    <p class="text-lg font-medium leading-normal text-gray-900">Business</p>
                </x-slot>
                <x-slot name="content" class="max-w-5xl">
                    <div class="inline-flex flex-col space-y-1 items-start justify-start flex-1">
                        <label for="abn" class="text-sm font-medium leading-5 text-gray-700">Australian Business Number
                            (ABN)</label>
                        <input type="text" name="ABN" id="abn"
                            value="{{ (isset($collegedetail['ABN'])) ? $collegedetail['ABN'] : '' }}"
                            class="text-sm leading-5 text-gray-700 px-3 py-2 bg-white shadow-sm border rounded-lg border-gray-300 w-full cusInput"
                            id="abn" validationMessage="Enter {0} Value" required>
                    </div>
                    <div class="inline-flex flex-col space-y-1 items-start justify-start flex-1">
                        <p class="text-sm font-medium leading-5 text-gray-700">RTO Code</p>
                        <input type="text" name="RTO_code" value="{{ (isset($RTO_code)) ? $RTO_code : '' }}"
                            class="text-sm leading-5 text-gray-700 px-3 py-2 bg-white shadow-sm border rounded-lg border-gray-300 w-full cusInput"
                            id="rto_code" validationMessage="Enter RTO Code" required>
                    </div>
                    <div class="inline-flex flex-col space-y-1 items-start justify-start flex-1">
                        <div class="inline-flex space-x-1 items-start justify-between w-full">
                            <p class="text-sm font-medium leading-5 text-gray-700">CRICOS Provider No</p>
                            <p class="text-sm font-medium leading-5 text-right text-gray-500">Optional</p>
                        </div>
                        <input type="text" name="CRICOS_code" value="{{ (isset($CRICOS_code)) ? $CRICOS_code : '' }}"
                            class="text-sm leading-5 text-gray-700 px-3 py-2 bg-white shadow-sm border rounded-lg border-gray-300 w-full cusInput">
                    </div>
                </x-slot>
            </x-v2.onboardsetting.form-card>
            <x-v2.onboardsetting.form-card>
                <x-slot name="title">
                    <p class="text-lg font-medium leading-normal text-gray-900">URL</p>
                </x-slot>
                <x-slot name="content" class="w-full">
                    <div class="flex justify-between w-full">
                        <p name="college_url" class="text-sm leading-5 font-normal">
                            Your current Galaxy360 organization account URL is
                            @if(isset($college_url) && !empty($college_url))
                            <a href="{{ $college_url }}" class="cursor-pointer text-blue-500" target="_blank"><span
                                    class="added_college_url">{{
                                    $college_url }}</span></a>
                            @else
                            <span class="text-blue-500">N/A</span>
                            @endif
                        </p>
                        <!-- <button type="button" class="changeUrl btn-primary px-4 py-2">
                            <p class="text-sm font-medium leading-5 text-white">Change Organisation URL</p>
                        </button> -->
                    </div>
                </x-slot>
            </x-v2.onboardsetting.form-card>
            <x-v2.onboardsetting.form-card>
                <x-slot name="title">
                    <p class="text-lg font-medium leading-normal text-gray-900">Organisation</p>
                </x-slot>
                <x-slot name="content" class="flex-col max-w-2xl">
                    <div class="flex flex-col space-y-1 items-start justify-start">
                        <p class="text-sm font-medium leading-tight text-gray-700">Company Logo</p>
                        <div class="inline-flex  items-center justify-start">
                            <div class="w-12 h-12" id="display_logo">
                                <img src="{{ $logo_picture_url}}" alt="searchIcon" />
                            </div>
                            <div class="flex space-x-2 items-center justify-start upload-wrapper">
                                <input type="file" name="file" id="college_logo" />
                                <label for="college_logo" class="btn-2 college_logo">Upload</label>
                                <p class="text-xs leading-4 text-center text-gray-500">PNG, JPG up to 2 MB (max
                                    800x800px)</p>
                            </div>
                        </div>
                    </div>
                    <div class="flex flex-col space-y-1 items-start justify-start w-full">
                        <p class="text-sm font-medium leading-tight text-gray-700">College Signature</p>
                        <div class="inline-flex  items-center justify-start">
                            <div class="w-12 h-12" id="signature_logo">
                                <img src="{{ $college_signature_url }}" alt="searchIcon" />
                            </div>
                            <div class="flex space-x-2 items-center justify-start upload-wrapper">
                                <input type="file" name="file" id="college_signature" />
                                <label for="college_signature" class="btn-2 college_signature">Upload</label>
                                <p class="text-xs leading-4 text-center text-gray-500">PNG, JPG up to 2 MB (max
                                    800x800px)</p>
                            </div>
                        </div>
                    </div>
                    <div class="flex flex-col space-y-1 items-start justify-start w-full">
                        <p class="text-sm font-medium leading-tight text-gray-700">Dean Signature</p>
                        <div class="inline-flex  items-center justify-start">
                            <div class="w-12 h-12" id="dean_logo">
                                <img src="{{ $college_dean_signature_url }}" alt="searchIcon" />
                            </div>
                            <div class="flex space-x-2 items-center justify-start upload-wrapper">
                                <input type="file" name="file" id="dean_signature" />
                                <label for="dean_signature" class="btn-2 college_signature">Upload</label>
                                <p class="text-xs leading-4 text-center text-gray-500">PNG, JPG up to 2 MB (max
                                    800x800px)</p>
                            </div>
                        </div>
                    </div>
                    <div class="flex flex-col space-y-1 items-start justify-start w-full">
                        <p class="text-sm font-medium leading-tight text-gray-700">Admission manager</p>
                        <div class="inline-flex  items-center justify-start">
                            <div class="w-12 h-12" id="admission_logo">
                                <img src="{{ $college_admission_signature_url }}" alt="searchIcon" />
                            </div>
                            <div class="flex space-x-2 items-center justify-start upload-wrapper">
                                <input type="file" name="file" id="admission_manager_signature" />
                                <label for="admission_manager_signature" class="btn-2 college_signature">Upload</label>
                                <p class="text-xs leading-4 text-center text-gray-500">PNG, JPG up to 2 MB (max
                                    800x800px)</p>
                            </div>
                        </div>
                    </div>
                    <div class="flex flex-col space-y-1 items-start justify-start w-full">
                        <p class="text-sm font-medium leading-tight text-gray-700">Student support</p>
                        <div class="inline-flex  items-center justify-start">
                            <div class="w-12 h-12" id="student_support_logo">
                                <img src="{{ $college_student_support_signature_url }}" alt="searchIcon" />
                            </div>
                            <div class="flex space-x-2 items-center justify-start upload-wrapper">
                                <input type="file" name="file" id="student_support_signature" />
                                <label for="student_support_signature" class="btn-2 college_signature">Upload</label>
                                <p class="text-xs leading-4 text-center text-gray-500">PNG, JPG up to 2 MB (max
                                    800x800px)</p>
                            </div>
                        </div>
                    </div>
                    <div class="flex flex-col space-y-1 items-start justify-start">
                        <p class="text-sm font-medium leading-tight text-gray-700">College Name</p>
                        <input type="text" name="college_name" value="{{ (isset($college_name)) ? $college_name : '' }}"
                            class="text-sm leading-5 text-gray-700 px-3 py-2 bg-white shadow-sm border rounded-lg border-gray-300 w-full cusInput"
                            id="college_name" validationMessage="Enter College Name" required>
                    </div>
                    <div class="flex flex-col space-y-1 items-start justify-start">
                        <p class="text-sm font-medium leading-tight text-gray-700">Dean/CEO Name</p>
                        <input type="text" name="dean_name" placeholder="Enter Dean/CEO Name"
                               id="dean_name" value="{{ (isset($dean_name)) ? $dean_name : '' }}"
                               class="text-sm leading-5 text-gray-700 px-3 py-2 bg-white shadow-sm border rounded-lg border-gray-300 w-full cusInput"
                               validationMessage="Enter Dean/CEO Name" required />
                    </div>
                    <div class="flex flex-col space-y-1 items-start justify-start">
                        <p class="text-sm font-medium leading-tight text-gray-700">TimeZone</p>
                        {{ Form::select('timezone', $arrTimeZoneList, ((isset($timezone) && !empty($timezone)) ?
                        $timezone : ''), array('class'=>'w-full', 'id'=>'timezone')) }}
                    </div>
                    <div class="flex flex-col space-y-1 items-start justify-start">
                        <p class="text-sm font-medium leading-tight text-gray-700">Legal Name</p>
                        <input type="text" name="legal_name" value="{{ (isset($legal_name)) ? $legal_name : '' }}"
                            class="text-sm leading-5 text-gray-700 px-3 py-2 bg-white shadow-sm border rounded-lg border-gray-300 w-full cusInput"
                            validationMessage="Enter Legal Name" required>
                    </div>
                    <div class="flex flex-col space-y-1 items-start justify-start training-organisation">
                        <label for="training_organisation_type_id"
                            class="text-sm leading-5 text-gray-700 font-medium">Training Organisation
                            Type</label>
                        {{ Form::select('training_organisation_type_id', $arrTrainingOrg , ((isset($collegedetail) &&
                        !empty($collegedetail['training_organisation_type_id'])) ?
                        $collegedetail['training_organisation_type_id'] : ''), array('class' => 'w-full', 'id' =>
                        'training_organisation_type_id' )) }}
                    </div>
                </x-slot>
            </x-v2.onboardsetting.form-card>
            <x-v2.onboardsetting.form-card>
                <x-slot name="title">
                    <p class="text-lg font-medium leading-normal text-gray-900">Contact</p>
                </x-slot>
                <x-slot name="content" class="flex-col max-w-2xl">
                    <div class="flex flex-col space-y-1 items-start justify-start w-full">
                        <p class="text-sm font-medium leading-5 text-gray-700">Contact Person</p>
                        <input type="text" name="contact_person"
                            value="{{ (isset($contact_person)) ? $contact_person : '' }}"
                            class="text-sm leading-5 text-gray-700 px-3 py-2 bg-white shadow-sm border rounded-lg border-gray-300 w-full cusInput"
                            validationMessage="Enter Contact Person" required>
                    </div>
                    <div class="inline-flex space-x-6 items-start justify-start w-full">
                        <div class="inline-flex flex-col space-y-1 items-start justify-start flex-1">
                            <p class="text-sm font-medium leading-5 text-gray-700">Phone Number</p>
                            <input type="text" name="contact_phone"
                                value="{{ (isset($contact_phone)) ? $contact_phone : '' }}"
                                class="text-sm leading-5 text-gray-700 px-3 py-2 bg-white shadow-sm border rounded-lg border-gray-300 w-full cusInput"
                                validationMessage="Enter Phone Number" required>
                        </div>
                        <div class="inline-flex flex-col space-y-1 items-start justify-start flex-1">
                            <p class="text-sm font-medium leading-5 text-gray-700">Fax</p>
                            <input type="text" name="fax"
                                value="{{ (isset($collegedetail['fax'])) ? $collegedetail['fax'] : '' }}"
                                class="text-sm leading-5 text-gray-700 px-3 py-2 bg-white shadow-sm border rounded-lg border-gray-300 w-full cusInput"
                                validationMessage="Enter {0}" required>
                        </div>
                    </div>
                    <div class="flex flex-col space-y-1 items-start justify-start w-full">
                        <p class="text-sm font-medium leading-5 text-gray-700">Contact Email</p>
                        <input type="text" name="contact_email"
                            value="{{ (isset($contact_email)) ? $contact_email : '' }}"
                            class="text-sm leading-5 text-gray-700 px-3 py-2 bg-white shadow-sm border rounded-lg border-gray-300 w-full cusInput"
                            validationMessage="Enter Contact Email" required>
                    </div>
                    <div id="another_email" class="w-full">
                        @php $emailCount = 0 @endphp
                        @if(isset($collegedetail['it_email']) && !empty(($collegedetail['it_email'])))
                        <div class="flex flex-col space-y-1 items-start justify-start w-full mt-4 it_email">
                            <p class="text-sm font-medium leading-5 text-gray-700">IT Email</p>
                            <input type="text" name="it_email" value="{{ $collegedetail['it_email'] }}"
                                placeholder="Enter IT email"
                                class="text-sm leading-5 text-gray-700 px-3 py-2 bg-white shadow-sm border rounded-lg border-gray-300 w-full cusInput">
                        </div>
                        @php $emailCount++; @endphp
                        @endif
                        @if(isset($collegedetail['marketing_email']) && !empty(($collegedetail['marketing_email'])))
                        <div class="flex flex-col space-y-1 items-start justify-start w-full mt-4 marketing_email">
                            <p class="text-sm font-medium leading-5 text-gray-700">Marketing Email</p>
                            <input type="text" name="marketing_email" value="{{ $collegedetail['marketing_email'] }}"
                                placeholder="Enter Marketing email"
                                class="text-sm leading-5 text-gray-700 px-3 py-2 bg-white shadow-sm border rounded-lg border-gray-300 w-full cusInput">
                        </div>
                        @php $emailCount++; @endphp
                        @endif
                        @if(isset($collegedetail['acedemic_email']) && !empty(($collegedetail['acedemic_email'])))
                        <div class="flex flex-col space-y-1 items-start justify-start w-full mt-4 acedemic_email">
                            <p class="text-sm font-medium leading-5 text-gray-700">Academic Email</p>
                            <input type="text" name="acedemic_email" value="{{ $collegedetail['acedemic_email'] }}"
                                placeholder="Enter Academic email"
                                class="text-sm leading-5 text-gray-700 px-3 py-2 bg-white shadow-sm border rounded-lg border-gray-300 w-full cusInput">
                        </div>
                        @php $emailCount++; @endphp
                        @endif
                        @if(isset($collegedetail['admission_email']) && !empty(($collegedetail['admission_email'])))
                        <div class="flex flex-col space-y-1 items-start justify-start w-full mt-4 admission_email">
                            <p class="text-sm font-medium leading-5 text-gray-700">Admission Email</p>
                            <input type="text" name="admission_email" value="{{ $collegedetail['admission_email'] }}"
                                placeholder="Enter Admission email"
                                class="text-sm leading-5 text-gray-700 px-3 py-2 bg-white shadow-sm border rounded-lg border-gray-300 w-full cusInput">
                        </div>
                        @php $emailCount++; @endphp
                        @endif
                        @if(isset($collegedetail['account_email']) && !empty(($collegedetail['account_email'])))
                        <div class="flex flex-col space-y-1 items-start justify-start w-full mt-4 account_email">
                            <p class="text-sm font-medium leading-5 text-gray-700">Accounts Email</p>
                            <input type="text" name="account_email" value="{{ $collegedetail['account_email'] }}"
                                placeholder="Enter Accounts email"
                                class="text-sm leading-5 text-gray-700 px-3 py-2 bg-white shadow-sm border rounded-lg border-gray-300 w-full cusInput">
                        </div>
                        @php $emailCount++; @endphp
                        @endif
                    </div>
                    <button type="button" class="addAnotherEmail btn-tertiary w-fit py-2 pl-2 pr-2.5" {{ (($emailCount>=
                        5) ?
                        'disabled' : '') }}>
                        <span class="k-icon k-i-plus k-icon-add"></span>
                        <p class="text-sm font-medium leading-4 text-primary-blue-500 w-fit">Add another email</p>
                    </button>
                </x-slot>
            </x-v2.onboardsetting.form-card>
        </x-slot>
        <x-slot name="formClose">
            {{ Form::close() }}
        </x-slot>
    </x-v2.onboardsetting.container>
    <div id="changeUrlModal" class="" style="display: none;">
        <form id="changeUrlForm"></form>
    </div>
    <div style="transition: height 0.2s;"
        class="bottomaction flex items-center justify-start px-6 bg-white shadow border-gray-300 py-4 defaultHide">
        <button type="button"
            class="saveFormData inline-flex items-center justify-center px-8 py-2 bg-primary-blue-500 hover:shadow-md rounded-lg shadow hover:shadow-lg rounded-lg focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-primary-blue-600">
            <p class="text-base font-medium leading-normal text-white">Save & Continue</p>
        </button>
    </div>

    <x-slot name="jsFooter">
        <script src="{{ asset('v2/js/sadmin/onboardsetting-profile.js') }}"></script>
        <script>
            $(document).ready(function() {
                const $apiCheckbox = $('#enable_shortcourse_api');
                const $apiSettingsFields = $('#api_settings_fields');

                // Function to toggle visibility
                function toggleApiSettings() {
                    if ($apiCheckbox.is(':checked')) {
                        $apiSettingsFields.removeClass('hidden');
                    } else {
                        $apiSettingsFields.addClass('hidden');
                    }
                }

                // Initial state
                toggleApiSettings();

                // Add event listener
                $apiCheckbox.on('change', toggleApiSettings);
            });
        </script>
    </x-slot>

    <x-slot name="fixVariables">
        var api_token = "{{ (isset($api_token) ? "Bearer $api_token" : "") }}"
    </x-slot>

</x-v2.layouts.onboard>