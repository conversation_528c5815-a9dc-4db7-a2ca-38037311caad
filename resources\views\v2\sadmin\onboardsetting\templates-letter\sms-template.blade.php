<x-v2.layouts.onboard>

    @section('title', $title)
    @section('keywords', $keywords)
    @section('description', $description)
    @section('mainmenu', $mainmenu)

    <x-slot name="cssHeader">
        <link rel="stylesheet" href="{{ asset('v2/css/sadmin/onboard-settings.css') }}">
        <style>
            .k-wizard {
                padding: 0px !important;
            }
        </style>
    </x-slot>

    <livewire:onboarding.progress form="templates_letters.sms-template-list" />

    <div class="flex flex-row h-full">
    </div>

    <script id="commonTemplate" type="text/x-kendo-template">
        <div class="flex flex-col items-start justify-start w-full">
            <div class="inline-flex flex-col bg-white w-full">
            <div class="inline-flex space-x-6 items-start justify-between w-full">
                <div class="flex space-x-4 items-center justify-start">
                    <div class="inline-flex flex-col space-y-2 items-start justify-start">
                        <p class="text-2xl font-bold leading-7 text-gray-900">#=template_name# </p>
                        <input type="hidden"  id="template_name" value="#=template_name#" />
                        <input type="hidden"  id="recipient" value="#=recipient#" />
                        <input type="hidden"  id="content" value="#=content#" />
                        <input type="hidden"  id="id" value="#=id#" />
                        <p class="text-sm leading-5 text-gray-500"> edited status</p>
                    </div>
                </div>
            </div>
        </div>
    </script>

    <div class="flex flex-row w-full p-6 defaultHide hidden">
        <x-v2.onboardsetting.template-container>
            <x-slot name="sidebar">
                <div class="px-4 pb-4 space-y-4 border-b border-gray-200">
                    <p class="text-2xl font-bold leading-7 text-gray-900">SMS Template</p>
                    <x-v2.button class="addSMSTemplate w-full h-9" size="sm" variant="primary">
                        <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M6.00039 0.399902C6.44222 0.399902 6.80039 0.758075 6.80039 1.1999V5.1999H10.8004C11.2422 5.1999 11.6004 5.55807 11.6004 5.9999C11.6004 6.44173 11.2422 6.7999 10.8004 6.7999H6.80039V10.7999C6.80039 11.2417 6.44222 11.5999 6.00039 11.5999C5.55856 11.5999 5.20039 11.2417 5.20039 10.7999V6.7999H1.20039C0.758563 6.7999 0.400391 6.44173 0.400391 5.9999C0.400391 5.55807 0.758563 5.1999 1.20039 5.1999H5.20039V1.1999C5.20039 0.758075 5.55856 0.399902 6.00039 0.399902Z"
                                fill="white" />
                        </svg>
                        <p class="text-sm font-normal text-white">New SMS Template</p>
                    </x-v2.button>
                </div>
                <div class="space-y-4 py-4 px-4">
                    <div class="flex space-x-2 items-center justify-end h-9">
                        <x-v2.search-input wrapperClass="w-full" id="" class="searchInputSMSTemplateMaster w-full"
                            placeholder="Search" data-grid-id="studentList">
                            <img src="{{ asset('v2/img/search.svg') }}" class="h-4 w-4" alt="searchIcon" />
                        </x-v2.search-input>
                    </div>
                    <div id="smsDocumentStepper" class="w-full smsDocumentStepper"></div>
                </div>
            </x-slot>
            <x-slot name="content">
                <div class="flex flex-col space-y-6 items-center justify-center py-8 rounded-lg w-full bg-white noSmsDataDiv"
                    style="display: none">
                    <div class="flex flex-col space-y-4 items-center justify-start">
                        <span class="text-primary-blue-500 text-center">
                            <svg width="48" height="48" viewBox="0 0 48 48" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M14.4 11.7002C14.4 10.5599 15.2059 9.63552 16.2 9.63552H30.6C31.5941 9.63552 32.4 10.5599 32.4 11.7002V15.8296C32.4 16.9699 31.5941 17.8943 30.6 17.8943H16.2C15.2059 17.8943 14.4 16.9699 14.4 15.8296V11.7002ZM17.1 14.7973H29.7V12.7326H17.1V14.7973ZM9 8.60317C9 5.75241 11.0147 3.44141 13.5 3.44141H34.2C36.6853 3.44141 38.7 5.75241 38.7 8.60317V38.0252C38.7 38.8805 38.0956 39.5738 37.35 39.5738H11.7C11.7 40.7141 12.5059 41.6385 13.5 41.6385H37.35C38.0956 41.6385 38.7 42.3318 38.7 43.187C38.7 44.0422 38.0956 44.7355 37.35 44.7355H13.5C11.0147 44.7355 9 42.4245 9 39.5738V8.60317ZM11.7 36.4767H36V8.60317C36 7.46287 35.1941 6.53847 34.2 6.53847H13.5C12.5059 6.53847 11.7 7.46287 11.7 8.60317V36.4767Z"
                                    fill="currentColor"></path>
                            </svg>
                        </span>
                        <div class="text-center text-gray-700">
                            <p class="text-sm leading-5 text-gray-700">You have not added sms template.</p>
                        </div>
                    </div>
                    <button class="btn-primary py-2 pl-2.5 pr-3 addSMSTemplate">
                        <img src="http://iit.galaxy360.com/v2/img/plus-white.svg" class="" alt="">
                        <p class="text-sm font-medium leading-4 text-white uppercase">New SMS Template</p>
                    </button>
                </div>
                <div id="smsTemplateWizard" class="smsDataDiv !mt-0" style="display: none;"></div>
                <div class="rounded border border-gray-200 bg-white p-6 smsDataDiv" style="display: none;">
                    <div id="editSMSTemplateForm" class="w-2/3">
                    </div>
                </div>
            </x-slot>
        </x-v2.onboardsetting.template-container>
    </div>

    <div id="addSMSTemplateModal" style="display:none;">
        <form id="addSMSTemplateForm" class="w-full">
        </form>
    </div>

    <div id="confirmDeleteModal"></div>
    <script id="listMenuItem" type="text/html">
        <span class="w-full truncate">
        <strong class="action-div">#:label#</strong>
    </span>
        <button id="deleteSMSTemplate" class="tw-btn-icon cursor-pointer deleteSMSTemplate action-autohide" data-id="#:id#">
            <x-v2.icons name="icon-delete" width="16" height="16" class="text-red-500" />
        </button>
    </script>

    <x-slot name="jsFooter">
        <script src="{{ asset('v2/js/sadmin/onboard-settings/templates-letter/sms-template.js') }}"></script>
    </x-slot>

    <x-slot name="fixVariables">
        var api_token = "{{ isset($api_token) ? "Bearer $api_token" : '' }}"
    </x-slot>

</x-v2.layouts.onboard>