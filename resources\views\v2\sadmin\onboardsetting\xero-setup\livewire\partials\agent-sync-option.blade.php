<div class="flex h-auto w-full flex-col items-start justify-start space-y-2 rounded-lg bg-white p-6 shadow">
    <div class="flex-col items-start justify-start space-y-4">
        <div class="inline-flex w-full items-center justify-between">
            <div class="flex items-center justify-start space-x-1">
                <p class="text-base font-medium leading-5 text-gray-900">Agent Commission Sync Option</p>
            </div>
        </div>
        <div class="inline-flex w-full bg-primary-blue-100 p-2 rounded">
            <span class="text-gray-700 text-sm leading-5">
                Choose to create a Purchase Order or directly create a bill in XERO using GALAXY360.
            </span>
        </div>
        <div class="w-full">
            {{--@if (session()->has('message'))
                <div class="alert alert-success">
                    {{ session('message') }}
                </div>
            @endif--}}
            <form wire:submit.prevent="saveAgentCommissionSyncOption" id="agentCommissionSyncOptionForm" class="m-0 space-y-2">
                <div class="form-group space-y-2">
                    <label for="agent_commission_sync_option" class="font-medium">Please select the agent commission sync option:</label>
                    <div class="space-y-2">
                        <div class="flex items-center space-x-2">
                            <input type="radio" class="k-radio" id="createPO" name="agent_commission_sync_option" value="createPO" wire:model.defer="agent_commission_sync_option">
                            <label for="createPO">Create PO first, then convert to bill</label>
                        </div>
                        <div class="flex items-center space-x-2">
                            <input type="radio" class="k-radio" id="directSync" name="agent_commission_sync_option" value="directSync" wire:model.defer="agent_commission_sync_option">
                            <label for="directSync">Direct sync agent commission as bill</label>
                        </div>
                    </div>
                {{--@error('agent_commission_sync_option') <span class="text-danger">{{ $message }}</span> @enderror--}}
                </div>

                <div class="modal-footer w-full inline-flex space-x-4 items-center justify-end">
                    <div class="float-right flex space-x-4 items-center justify-end">
                        <x-button type="submit" class="btn-primary px-3" loading="Saving..">
                            <p class="text-sm font-medium leading-none text-white">Save Changes</p>
                        </x-button>
                    </div>
                </div>
            </form>
        </div>
    </div>
</div>