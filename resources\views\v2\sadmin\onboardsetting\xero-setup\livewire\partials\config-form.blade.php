<div class="flex flex-col justify-start items-start bg-white p-6 space-y-6 rounded-lg shadow">
    <div class="flex items-center justify-start space-x-1">
        <p class="text-base font-medium leading-5 text-gray-900">Configuration Setup</p>
        <livewire:docs path=".docs/xero.md" :data="[
            'redirectUrl' => route('xero-connect'),
            'webhookUrl' => route('xero-webhook')
        ]"/>
    </div>
    <div class="flex items-center justify-start space-x-1">
        <p class="text-sm font-normal text-gray-500">Please visit
        <a class="text-sm font-normal text-primary-blue-500" href="https://developer.xero.com/myapps">XERO APPS DASHBOARD</a>
        and create your app using the urls below. Update your client id and secret on below fields </p>
    </div>
    <div class="flex justify-between items-start w-full">
        <div class="flex flex-col justify-start items-start space-y-5">
            <div class="flex justify-start items-center">
                <div class="text-sm font-medium leading-5 text-gray-700 w-24">Redirect URL</div>
                <div class="flex justify-start items-center space-x-1">
                    <div class="text-sm font-normal text-primary-blue-500">{{ route('xero-connect') }}</div>        
                    <a href="javascript:void(0);" class="copy_data cursor-pointer rounded" title="Copy" data-text="{{ route('xero-connect') }}">   
                        <svg width="12" height="16" viewBox="0 0 12 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M4 0C2.89543 0 2 0.895431 2 2V12C2 13.1046 2.89543 14 4 14H10C11.1046 14 12 13.1046 12 12V2C12 0.89543 11.1046 0 10 0H4ZM3 2C3 1.44772 3.44772 1 4 1H10C10.5523 1 11 1.44772 11 2V12C11 12.5523 10.5523 13 10 13H4C3.44772 13 3 12.5523 3 12V2ZM0 4.00001C0 3.25973 0.402199 2.61339 1 2.26758V12.5C1 13.8807 2.11929 15 3.5 15H9.73244C9.38663 15.5978 8.74028 16 8 16H3.5C1.567 16 0 14.433 0 12.5V4.00001Z" fill="#9CA3AF"/>
                        </svg>
                    </a>    
                </div>
            </div>
            <div class="flex justify-start items-center">
                <div class="text-sm font-medium leading-5 text-gray-700 w-24">Webhook URL</div>
                <div class="flex justify-start items-center space-x-1 cursor-pointer">
                    <div class="text-sm font-normal text-primary-blue-500">{{ route('xero-webhook') }}</div>
                    <a href="javascript:void(0);" class="copy_data cursor-pointer rounded" title="Copy" data-text="{{ route('xero-webhook') }}">   
                        <svg width="12" height="16" viewBox="0 0 12 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path d="M4 0C2.89543 0 2 0.895431 2 2V12C2 13.1046 2.89543 14 4 14H10C11.1046 14 12 13.1046 12 12V2C12 0.89543 11.1046 0 10 0H4ZM3 2C3 1.44772 3.44772 1 4 1H10C10.5523 1 11 1.44772 11 2V12C11 12.5523 10.5523 13 10 13H4C3.44772 13 3 12.5523 3 12V2ZM0 4.00001C0 3.25973 0.402199 2.61339 1 2.26758V12.5C1 13.8807 2.11929 15 3.5 15H9.73244C9.38663 15.5978 8.74028 16 8 16H3.5C1.567 16 0 14.433 0 12.5V4.00001Z" fill="#9CA3AF"/>
                        </svg>
                    </a>
                </div>
            </div>
        </div>
        <div class="flex justify-end items-center">
            <span id="popupNotification"></span>  
        </div>
    </div>
    <div class="flex justify-start items-center border-b w-full">
    </div>
    {{-- @if(!$xero_connected) --}}
    @php
    $organisationArr = $config->webhook_key;
    @endphp
    @if($organisationArr == "")
        <div class="p-4 bg-yellow-50 rounded-md border border-yellow-400 justify-start items-center space-x-2 inline-flex configureAPIBox">  
            <svg width="16" height="14" viewBox="0 0 16 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path fill-rule="evenodd" clip-rule="evenodd" d="M6.25608 1.09858C7.02069 -0.260724 8.97778 -0.260725 9.74238 1.09858L15.3227 11.0191C16.0726 12.3523 15.1092 13.9996 13.5795 13.9996H2.41893C0.889274 13.9996 -0.0741535 12.3523 0.675776 11.0191L6.25608 1.09858ZM8.99915 10.9998C8.99915 11.552 8.55143 11.9998 7.99915 11.9998C7.44686 11.9998 6.99915 11.552 6.99915 10.9998C6.99915 10.4475 7.44686 9.99976 7.99915 9.99976C8.55143 9.99976 8.99915 10.4475 8.99915 10.9998ZM7.99915 2.99976C7.44686 2.99976 6.99915 3.44747 6.99915 3.99976V6.99976C6.99915 7.55204 7.44686 7.99976 7.99915 7.99976C8.55143 7.99976 8.99915 7.55204 8.99915 6.99976V3.99976C8.99915 3.44747 8.55143 2.99976 7.99915 2.99976Z" fill="#FBBF24"/>
            </svg>
              <div class="text-sm font-medium leading-5 text-yellow-800">You're yet to configure your API Keys</div>
        </div>
    @endif
    <div class="grid grid-cols-2 gap-6 w-4/5">
        <div class="flex w-full flex-col items-start justify-start space-y-1">
            <p class="text-sm font-medium leading-5 text-gray-700">Client ID</p>
            <input type="text"
                   name="client_id"
                   {{ $xero_connected ? 'readonly' : ''}}
                   wire:model.defer="config.client_id"
                   class="w-full rounded-lg border border-gray-300 {{ $xero_connected ? 'bg-gray-100' : 'bg-white'}} px-3 py-2 text-sm leading-5 text-gray-700 shadow-sm" />
            @error('config.client_id')
                <p class="text-xs text-red-500">{{ $message }}</p>
            @enderror
        </div>
        <div class="flex w-full flex-col items-start justify-start space-y-1">
            <p class="text-sm font-medium leading-5 text-gray-700">Client Secret</p>
            <input type="password"
                   name="client_secret"
                   {{ $xero_connected ? 'readonly' : ''}}
                   wire:model.defer="config.client_secret"
                   class="w-full rounded-lg border border-gray-300 {{ $xero_connected ? 'bg-gray-100' : 'bg-white'}} px-3 py-2 text-sm leading-5 text-gray-700 shadow-sm" />
            @error('config.client_secret')
                <p class="text-xs text-red-500">{{ $message }}</p>
            @enderror
        </div>
        <div class="flex w-full flex-col items-start justify-start space-y-1">
            <p class="text-sm font-medium leading-5 text-gray-700">Webhook Key</p>
            <input type="text"
                   name="webhook_key"
                   {{ $xero_connected ? 'readonly' : ''}}
                   wire:model.defer="config.webhook_key"
                   class="webhook_key w-full rounded-lg border border-gray-300 {{ $xero_connected ? 'bg-gray-100' : 'bg-white'}} px-3 py-2 text-sm leading-5 text-gray-700 shadow-sm" />
            @error('config.webhook_key')
                <p class="text-xs text-red-500">{{ $message }}</p>
            @enderror
        </div>
    </div>
    @if(!$xero_connected)
    <x-button type="button"
              target="saveConfig"
              wire:click.prevent="saveConfig"
              loading="Saving..">
        Save Changes
    </x-button>
    @endif
</div>
