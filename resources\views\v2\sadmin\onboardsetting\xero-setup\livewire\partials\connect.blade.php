<div class="flex flex-col rounded-lg bg-white shadow p-6">
    <div class="flex w-full items-center justify-between">
        <div class="flex items-center space-x-2">
            <p class="text-base font-medium text-gray-900">Xero Connection Status</p>
            <livewire:docs path=".docs/xero.md"/>
        </div>
        @if (@$xero_connected)
            <a href="{{ route('xero-disconnect') }}"
            class="inline-flex items-center justify-center rounded-lg px-4 h-9 border border-red-400 text-red-400 hover:text-red-500 hover:shadow focus:ring-2 focus:ring-red-500 focus:ring-offset-2 focus:ring-offset-white font-medium">
            <img src="{{ asset('v2/img/xero-logo.png') }}" class="mr-1" alt="searchIcon" />
            XERO DISCONNECT</a>
        @else
            <a href="{{ route('xero-connect') }}"
               class="inline-flex items-center justify-center rounded-lg px-4 h-9 border border-gray-300 hover:shadow focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 focus:ring-offset-white text-gray-700 font-medium">
               <img src="{{ asset('v2/img/xero-logo.png') }}" class="mr-1" alt="searchIcon" />
            XERO CONNECT</a>
        @endif
    </div>

    <p class="text-sm font-normal text-gray-500">
        @if (@$xero_connected)
            You are connected to Xero
        @else
            You are not connected to Xero
        @endif
    </p>

    @if ($xero_connected)
        <div class="flex flex-wrap gap-4 mt-4">
            <div class="flex rounded-md shadow-sm">
                <div class="flex w-16 h-16 shrink-0 items-center justify-center rounded-l-md bg-green-500 text-white">
                    <x-v2.icons name="icon-check" width="28" height="28" class="text-current" />
                </div>
                <div class="flex flex-col justify-center bg-white border border-gray-200 rounded-r-md px-4 py-2">
                    <p class="font-medium text-gray-900">Galaxy -> Xero</p>
                    <p class="text-sm text-gray-500">Connected</p>
                </div>
            </div>

            <div class="flex rounded-md shadow-sm">
                <div class="flex w-16 h-16 shrink-0 items-center justify-center rounded-l-md bg-{{ $config->webhook_linked ? 'green' : 'red' }}-500 text-white">
                    <x-v2.icons name="{{ $config->webhook_linked ? 'icon-check' : 'error-circle' }}" width="28" height="28" viewBox="{{$config->webhook_linked ? '0 0 20 20' : '0 0 24 24'}}" class="text-current" />
                </div>
                <div class="flex flex-col justify-center bg-white border border-gray-200 rounded-r-md px-4 py-2">
                    <p class="font-medium text-gray-900">Xero -> Galaxy</p>
                    <p class="text-sm text-gray-500">{{ $config->webhook_linked ? 'Connected' : 'Not Connected' }}</p>
                </div>
            </div>
        </div>
    @endif
</div>