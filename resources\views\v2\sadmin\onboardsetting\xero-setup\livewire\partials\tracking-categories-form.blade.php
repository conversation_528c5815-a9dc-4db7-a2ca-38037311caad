<div class="flex flex-col items-start justify-start space-y-6 rounded-lg bg-white p-6 shadow">
    <div class="flex w-full items-center justify-start gap-2">
        <p class="text-base font-medium leading-5 text-gray-900">Agent Tracking Categories Setup</p>
        <livewire:docs path=".docs/xero.md"/>
        <x-button type="button"
                  title="Sync Tracking Categories From Xero"
                  class="ml-auto flex-10"
                  target="syncTrackingCategories"
                  wire:click.prevent="syncTrackingCategories"
                  loading="Syncing..">
            <span class="k-icon k-i-refresh k-icon-refresh mb-1"></span>
            Sync
        </x-button>
    </div>
    @if (!$config->tracking_data || !isset($config->tracking_data['categories']))
        @if (@$config->tracking_data['last_synced_at'])
            <div class="mb-4 w-full rounded-md bg-red-50 p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-red-400"
                             viewBox="0 0 20 20"
                             fill="currentColor"
                             aria-hidden="true">
                            <path fill-rule="evenodd"
                                  d="M8.485 2.495c.673-1.167 2.357-1.167 3.03 0l6.28 10.875c.673 1.167-.17 2.625-1.516 2.625H3.72c-1.347 0-2.189-1.458-1.515-2.625L8.485 2.495zM10 5a.75.75 0 01.75.75v3.5a.75.75 0 01-1.5 0v-3.5A.75.75 0 0110 5zm0 9a1 1 0 100-2 1 1 0 000 2z"
                                  clip-rule="evenodd"></path>
                        </svg>

                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-red-800">Xero Action Required</h3>
                        <div class="mt-2 text-sm text-red-700">
                            <p>Looks like you don't have any tracking category setup on your xero account.
                                Click <a href="https://go.xero.com/Setup/Tracking.aspx">HERE</a> to create your tracking
                                category.<br /><br />
                                After that please click the sync button to get started.
                            </p>
                        </div>
                    </div>
                </div>
            </div>
        @else
            <div class="w-full rounded-md bg-yellow-50 p-4">
                <div class="flex">
                    <div class="flex-shrink-0">
                        <svg class="h-5 w-5 text-yellow-400"
                             viewBox="0 0 20 20"
                             fill="currentColor"
                             aria-hidden="true">
                            <path fill-rule="evenodd"
                                  d="M8.485 2.495c.673-1.167 2.357-1.167 3.03 0l6.28 10.875c.673 1.167-.17 2.625-1.516 2.625H3.72c-1.347 0-2.189-1.458-1.515-2.625L8.485 2.495zM10 5a.75.75 0 01.75.75v3.5a.75.75 0 01-1.5 0v-3.5A.75.75 0 0110 5zm0 9a1 1 0 100-2 1 1 0 000 2z"
                                  clip-rule="evenodd"></path>
                        </svg>

                    </div>
                    <div class="ml-3">
                        <h3 class="text-sm font-medium text-yellow-800">Galaxy Action Required</h3>
                        <div class="mt-2 text-sm text-yellow-700">
                            <p>Before you can setup agent tracking for invoice please sync the xero tracking categories first by clicking the sync button.</p>
                        </div>
                    </div>
                </div>
            </div>
        @endif
    @else
        <form wire:submit.prevent="saveTrackingCategory"
              class="block w-full"
              x-data="{ editing: @entangle('uiStates.trackingCategoriesFormEditing').live }">
            <label class="bold mb-2 block">Select Category To Track Agent</label>
            <select x-bind:class="{ 'appearance-none border-none text-gray-700 opacity-100': editing, 'bg-white shadow-sm': editing }"
                    wire:model.live="form.agent_tracking_category"
                    class="w-full rounded-lg border border-gray-300 px-4 py-2 text-sm leading-5 text-gray-700">
                <option value="">Choose </option>
                @foreach ($config->tracking_data['categories'] as $category)
                    <option value="{{ @$category['TrackingCategoryID'] }}">
                        {{ $category['Name'] }}
                    </option>
                @endforeach
            </select>

            @if ($form['agent_tracking_category'])
                <p class="mt-4">
                    {{-- <strong>Agents In Galaxy : </strong><br /> --}}
                    <strong>Total Uploaded Options : </strong>
                    {{ $this->agentTrackingCategoryOptions ? count($this->agentTrackingCategoryOptions) : 0 }}<br />

                    @if (@$config->tracking_data['last_synced_at'])
                        <strong>Last Synced At : </strong>
                        {{ date('Y-m-d, h:i a', strtotime($config->tracking_data['last_synced_at'])) }}<br />
                    @endif
                </p>
                @if ($uiStates['uploadTrackingCategory'])
                    <p class="mt-4">
                        <x-button type="button"
                                  title="Sync Agent For Tracking Categories"
                                  class="ml-auto"
                                  target="syncAgentTracking"
                                  wire:click.prevent="syncAgentTracking"
                                  loading="Uploading..">
                            <span class="k-icon k-i-refresh k-icon-refresh mb-1"></span>
                            Upload Agents Option
                        </x-button>
                    </p>
                @endif
            @endif
        </form>
    @endif

</div>
