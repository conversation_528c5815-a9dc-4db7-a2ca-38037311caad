<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head id="Head1">
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <title>TOCA.pdf</title>
        <!--<link id="linkStyleSheet" href="http://localhost/laravel_pdf/public/css/offer-letter.css" rel="stylesheet" type="text/css" media="screen" />-->
        <style>
            @page { margin: 0px;}
            body {
                /* background: #666666; */
                margin: 0;
                padding: 0;
                text-align: center;
                color: #333;
                font-family: Arial, Helvetica, sans-serif;
                font-size: 85%;
            }

            .content-block .heading  {
                text-align: center;
                line-height: 25px;
                font-size: 16px;
                font-weight: bolder;
                margin-top: 185px;
                margin-right: auto;
                margin-bottom: 0px;
                margin-left: auto;
                padding-bottom: 0px;
            }

            .break {page-break-after: always}

            #container {
                /*width: 800px;*/
                /* background: #FFFFFF;  */
                border: 0px solid #000000;
                text-align: left;
                margin-top: 0;
                /*margin-right: auto;*/
                margin-bottom: 0;
                /*margin-left: auto;*/
            }
            .content-block {
                /* background: #FFFFFF; */
                padding-top: 0;
                padding-right: 20px;
                padding-bottom: 0;
                padding-left: 20px;
                margin-top: 5px;
                margin-right: 15px;
                margin-bottom: 5px;
                margin-left: 15px;
                /*height:100% auto;*/
            }
            .content-block h1 {
                border-bottom-width: 1px;
                border-bottom-style: solid;
                border-bottom-color: #333333;
                font-size: 119%;
            }
            .content-block h2 {
                font-size: 95%;
            }
            .content-block h3 {
                font-size: 90%;
            }
            .content-block h4 {
                font-size: 85%;
            }
            .content-block h2 span {
                font-size: 14px;
                font-weight: normal;
            }
            .content-block li {
                margin: 4px;
            }
            #Footer
            {
                background-color: #004669;
                font-family: Tahoma, Arial;
                font-size: 0.7em;
                color: White;
                position: relative;
                height: 4em;
            }
            .spacer {
                height: 250px; /* Set the desired height for the space */
            }
            #watermark {
                position: fixed;
                bottom:   0px;
                left:     0px;
                top:     0px;
                /** The width and height may change
                    according to the dimensions of your letterhead
                **/

                width:    21cm;
                height:   29.7cm;

                /** Your watermark should be behind every content**/
                z-index:  -1000;
            }
        </style>
    </head>
    <body style="background-color: White;">
        @if ($isDownload == 2)
            <div id="watermark">
                @if(!empty($letterSettingData->watermark))
                <!-- <img height="100%" src="{{ empty($letterSettingData->watermark) ? null : url($watermarkPath.$letterSettingData->watermark) }}" width="100%" /> -->
                <img height="100%" src="{{ $watermarkPath }}" width="100%" />

                @else
                <div height="100%" width="100%" style="background-color: White"> </div>
                @endif
            </div>
        @endif
        @if ($isDownload == 3)

            <table style="width:100%;position:absolute;top:10;left:10;margin-left:20px;">
                <tr style="width:100%;">
                    <td style="text-align:left;">
                        <img style="width:auto;max-width:200px;" src="{{$college_logo }}">
                        <!-- <img style="width:auto;max-width:200px;" src="data:image/png;base64,{{ base64_encode(file_get_contents(public_path($college_logo))) }}"> -->
                    </td>
                </tr>
            </table>

        @endif
        @for ($i = 0; $i < count($studentCourseInfo); $i++)

        <div id="offer-letter-1">
            <div id="container">
                <div class="content-block">
                    @if($i != 0)
                        <div class="break"></div>
                    @endif
                    <br/>
                    <table style="width:100%;margin-top:210px;">
                        {{-- <tr>
                            <td>
                                RECORD OF RESULT
                            </td>
                        </tr> --}}
                        {{-- <tr>
                            <td>
                                <b> Certificate Number : </b>
                                {{ $studentCourseInfo[$i]['certificateType'] }}
                            </td>
                        </tr> --}}
                        <tr>
                            <td>
                                <b> Student Number : </b>
                                {{ $studentCourseInfo[$i]['generated_id'] }}
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <b> Student Name : </b>
                                {{--{{ $studentCourseInfo[$i]['name_title'] . ' ' . $studentCourseInfo[$i]['first_name'] . ' ' . $studentCourseInfo[$i]['middel_name'] .' '. $studentCourseInfo[$i]['family_name'] }}--}}
                                {{ $studentCourseInfo[$i]['first_name'] . ' ' . $studentCourseInfo[$i]['middel_name'] .' '. $studentCourseInfo[$i]['family_name'] }}
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <b> Date of Birth : </b>
                                {{  date('d F Y', strtotime($studentCourseInfo[$i]['DOB']))  }}
                            </td>
                        </tr>
                        <tr>
                            <td>
                                <b> Qualification : </b>
                                {{ $studentCourseInfo[$i]['course_code'] . ' - '. $studentCourseInfo[$i]['course_name'] }}
                            </td>
                        </tr>
                        {{-- <tr>
                            <td>
                                <b>  Start Date : </b>
                                {{ date('d F Y', strtotime($studentCourseInfo[$i]['start_date'])) }}

                            </td>
                        </tr>
                        <tr>
                            <td>
                                <b>  End Date : </b>
                                {{ date('d F Y', strtotime($studentCourseInfo[0]['completionDate'])) }}
                            </td>
                        </tr> --}}
                        <tr style="width:100%;">
                            <td style="width:100%;">

                            </td>
                        </tr>
                    </table>
                    <table style="width:100%;">
                        <tr style="width:100%;">
                            <th style="text-align: left;width:20%;padding-left: 10px; background:#000000;color:#FFFFFF;">Unit Code</th>
                            <th style="text-align: left;width:60%;padding-left: 10px; background:#000000;color:#FFFFFF;">Unit Name</th>
                            <th style="text-align: left;width:10%; background:#000000;color:#FFFFFF;">Results</th>
                            <th style="text-align: left;width:10%;padding-left: 10px; background:#000000;color:#FFFFFF;">Year</th>
                        </tr>
                        @php $j = 1; @endphp
                        @if(($studentCourseInfo[$i]['enrollList'] != null) && (count($studentCourseInfo[$i]['enrollList']) > 0))
                        @foreach($studentCourseInfo[$i]['enrollList'] as $enrollData)
                            @php $setFlag = ($j == 15 && count($studentCourseInfo[$i]['enrollList']) > 15)? true:false; @endphp
                            <tr class="@if($setFlag) break @endif" style="width:100%;">
                                <td style="padding-left: 10px;width:20%;text-align: left;">{{ $enrollData['unit_code'] }}</td>
                                <td style="padding-left: 10px;width:60%;text-align: left;">{{ $enrollData['unit_name'] }}</td>
                                <td style="padding-left: 10px;width:10%;text-align: left;">{{ $enrollData['final_outcome'] }}</td>
                                <td style="padding-left: 10px;width:10%;text-align: left;">{{ (date('Y',strtotime($enrollData['year'])) == '1970')?'-':date('Y',strtotime($enrollData['year'])) }}</td>
                            </tr>
                            @if($setFlag)

                            <tr style="width:100%;"><td class="spacer">&nbsp;</td></tr>

                            <tr style="width:100%;margin-top: 185px;">
                                <th style="text-align: left;width:20%;padding-left: 10px; background:#000000;color:#FFFFFF;">Unit Code</th>
                                <th style="text-align: left;width:60%;padding-left: 10px; background:#000000;color:#FFFFFF;">Unit Name</th>
                                <th style="text-align: left;width:10%; background:#000000;color:#FFFFFF;">Results</th>
                                <th style="text-align: left;width:10%;padding-left: 10px; background:#000000;color:#FFFFFF;">Year</th>
                            </tr>
                            @endif
                            @php $j++; @endphp
                        @endforeach
                        @endif
                        <tr><td>&nbsp;</td></tr>
                    </table>
                    <table style="width:100%;">
                        <tr style="width:100%;">
                            <td style="border-bottom: 1px black solid;">&nbsp;</td>
                        </tr>
                        <tr style="width:100%;"><td style="text-align:center;"> THESE UNITS HAVE BEEN DELIVERED AND ASSESSED IN ENGLISH</td></tr>
                        <tr style="width:100%;"><td>&nbsp;</td></tr>
                        <tr><td>&nbsp;</td></tr>
                        <tr><td style="text-align:center;">Key to Results: C: Competent, RPL: Recognition of Prior Learning, RCC: Recognition of </td></tr>
                        <tr><td style="text-align:center;">Current Competency, CT: Credit Transfer, WD: Withdrawn </td></tr>
                        <tr><td style="text-align:center;"> NYC: Not Yet Competent, CE: Continuing Enrolment</td></tr>
                        <tr><td style="text-align:center;font-style:italic;">Issued without alteration or erasure</td></tr>
                    </table>

                    <table style="width:90%;position:absolute;bottom:190;">
                        <tr style="width:100%;">
                            <td style="text-align:left;"><b>{{ date('d F Y',strtotime($studentCourseInfo[$i]['issueDate'])) }}</b></td>
                            <td style="text-align:center;">
                            <img style="width:110px;" src="{{$college_signature }}">

                                <!-- @if($college_signature != "" && file_exists(public_path($college_signature)))
                                    <img style="width:110px;" src="data:image/png;base64,{{ base64_encode(file_get_contents(public_path($college_signature))) }}">
                                @else
                                    <p>Image not available</p>
                                @endif -->
                            </td>
                            <td style="text-align:right;"><b>{{ $studentCourseInfo[$i]['certificateType'] }}</b></td>
                        </tr>
                        <tr style="width:100%;">
                            <td style="border-bottom: 1px black solid;"></td>
                            <td style="border-bottom: 1px black solid;"></td>
                            <td style="border-bottom: 1px black solid;"></td>
                        </tr>
                        <tr style="width:100%;">
                            <td style="text-align:left;"><b>Issue Date</b></td>
                            <td style="text-align:center;"><b>{{ !empty($objCollegeDetails['contact_person']) ? $objCollegeDetails['contact_person']  : '' }} - Principal</b></td>
                            <td style="text-align:right;"><b>Certificate Number</b></td>
                        </tr>
                    </table>
                    <br />
                    <br />
                    @if ($isDownload == 3)

                        <table style="width:90%;position:absolute;bottom:90;">
                            <tr style="width:100%;">
                                <td style="text-align:left;">
                                    <img class="logo-left" style="width:140px;" src="data:image/png;base64,{{ base64_encode(file_get_contents(public_path('/v2/img/aqf-colour-logo.JPG'))) }}">
                                </td>
                                <td style="text-align:center;"><b> </b></td>
                                <td style="text-align:right;">
                                    <img class="logo-right" style="width:130px;" src="data:image/png;base64,{{ base64_encode(file_get_contents(public_path('/v2/img/NRT_logo.jpg'))) }}">
                                </td>
                            </tr>
                        </table>

                    @endif
                </div>
            </div>
        </div>
        @if((count($studentCourseInfo)-1)!=$i)
        <span style="page-break-before: always"></span>
        @endif
        @endfor

    </body>
</html>