<!DOCTYPE html PUBLIC "-//W3C//DTD XHTML 1.0 Transitional//EN" "http://www.w3.org/TR/xhtml1/DTD/xhtml1-transitional.dtd">
<html xmlns="http://www.w3.org/1999/xhtml">
    <head id="Head1">
        <meta http-equiv="Content-Type" content="text/html; charset=utf-8" />
        <title>SOA.pdf</title>
        <!--<link id="linkStyleSheet" href="http://localhost/laravel_pdf/public/css/offer-letter.css" rel="stylesheet" type="text/css" media="screen" />-->
        <style>

            body {
                background: #666666;
                margin: 0; 
                padding: 0;
                text-align: center; 
                color: #333;
                font-family: Arial, Helvetica, sans-serif;
                font-size: 85%;
            }
            #container {
                /*width: 800px;*/  
                background: #FFFFFF; 
                border: 0px solid #000000;
                text-align: left; 
                margin-top: 0;
                margin-bottom: 0;
            }
            .content-block {
                text-align: center;
                line-height: 25px;
                background: #FFFFFF;
                padding-top: 0;
                padding-right: 20px;
                padding-bottom: 0px;
                padding-left: 20px;
                margin-top: 20px;
                margin-right: 15px;
                margin-bottom: 5px;
                margin-left: 15px;
            }
            .content-block .label1 {
                font-size: 16px;
                font-weight: bolder;
            }
            .content-block .label2 {
                font-size: 20px;
                font-weight: bolder;
            }
            .content-block .label3 {
                font-size: 20px;
            }
            .content-block .label4 {
                font-size: 16px;
            }
            .content-block .label5 {
                font-size: 14px;
            }
            .content-block .label6 {
                font-size: 14px;
                font-weight: bolder;
            }
            .content-block .label7 {
                font-size: 12px;
            }
            .page_break { page-break-before: always; }
        </style>
    </head>
    <body style="background-color: White;">
                <div id="container">
                    <div class="content-block">
                        <div  style="margin-left: 110px;" >
                        <br/><br/><br/><br/><br/><br/><br/>
                        <span class="label3">
                            This is a statement that
                        </span>
                        <br/><br/>
                        <span class="label2" style="font-family:Snickles;font-style: italic;font-weight: 600;">
                            <i>{{ $studentCourseInfo['first_name'] .' '. $studentCourseInfo['middel_name'] .' '.strtoupper($studentCourseInfo['family_name']) }}</i>
                        </span>
                        <br/>
                        <span class="label4">
                            Student Number: {{ $studentCourseInfo['generated_id']}}
                        </span>
                        <br/>
                        <span class="label4">
                            has attained  
                        </span>
                        <br/><br/>
                        <span class="">
                            <table style="width:100%;border-bottom:1px solid #000">
                                <thead style="border-bottom:1px solid #000;">
                                    <tr>
                                        <th style="width:25%;text-align: left;">Unit Code</th>
                                        <th style="width:75%;text-align: left;">Unit Description</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    @if(count($studentCourseInfo['enrollList']) > 0)
                                    @foreach($studentCourseInfo['enrollList'] as $enrollData)
                                    <tr style="line-height: 15px;" >
                                        <td>{{ $enrollData['unit_code'] }}</td>
                                        <td>{{ $enrollData['unit_name'] }}</td>
                                    </tr>
                                    @endforeach
                                    @else
                                    <tr style="text-align: center;color: maroon;">
                                        <td colspan="3">No Records Found</td>
                                    </tr>
                                    @endif
                                </tbody>
                            </table>
                        </span>
                        <br/><br/>
                        <span class="label3">
                            These competencies form part of the
                        </span>
                        <br/>
                        <span class="label2">
                            {{ $studentCourseInfo['course_code'] .' '. $studentCourseInfo['course_name'] }}   
                        </span>
                        <br/><br/>
                        <span class="label4">
                            Issue Date : {{ !empty($studentCourseInfo['issueDate']) ? date('d F Y', strtotime($studentCourseInfo['issueDate']))  : '' }}
                        </span>
                        <br/>
                        <span class="label4">
                            Credential Number: {{ $studentCourseInfo['certificateType'] }}
                        </span>

                        <br/><br/>
                            
                            <img style="width:70px;" src='{{ $college_signature}}'></img>
                            <br/>--------------------------
                            <br/>
                        <span class="label7">
                           {{ !empty($objCollegeDetails['contact_person']) ? $objCollegeDetails['contact_person']  : '' }}
                        </span>
                        <br/>
                        <span class="label7">
                          CEO
                        </span>
                        <br/>
                    </div>
                </div>
            </div>
    </body>
</html>