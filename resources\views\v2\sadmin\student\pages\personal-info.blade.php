<div id="editStudentModal" style="display: none; overflow: scroll;">
    <div class="flex bg-gray-100 p-6 editDetailss space-x-12">
        <div class="flex w-1/4">
            <div
                class="flex flex-col space-y-4 items-center justify-start py-4 bg-white shadow border rounded-md border-gray-200 w-1/4 fixed">
                <div class="inline-flex items-start justify-start w-full px-4 pb-4 border-b">
                    <div class="flex space-x-2 items-center justify-start flex-1">
                        <div class="w-16 h-16 rounded-md">
                            <div id="userIconTab"></div>
                        </div>
                        <div class="inline-flex flex-col items-start justify-center">
                            <p class="text-sm font-bold leading-5 text-gray-900 user-display-name"></p>
                            <div class="flex items-center gap-2">
                                <div class="inline-flex items-center gap-1 text-xs leading-4 text-gray-400">
                                    <span class="">ID:</span>
                                    <p class=" user-display-generated-id"></p>
                                </div>
                                <x-v2.copy class="user-display-id-copy" width="16" height="16" data-text="" />
                            </div>
                        </div>
                    </div>
                </div>
                <ul class="flex flex-col items-start justify-start w-full bg-white right-bar">
                    <li class="inline-flex items-center justify-start w-full">
                        <a href="#basicInfoTab"
                            class="viewDetailTab flex space-x-3 items-center justify-start flex-1 px-3 py-2 active">
                            <p class="text-sm font-medium leading-5 text-gray-500">Basic Information</p>
                        </a>
                    </li>
                    <li class="inline-flex items-center justify-start w-full">
                        <a href="#passportDetailsTab"
                            class="viewDetailTab flex space-x-3 items-center justify-start flex-1 px-3 py-2">
                            <p class="text-sm font-medium leading-5 text-gray-500">Passport Details</p>
                        </a>
                    </li>
                    <li class="inline-flex items-center justify-start w-full">
                        <a href="#visaDetailsTab"
                            class="viewDetailTab flex space-x-3 items-center justify-start flex-1 px-3 py-2">
                            <p class="text-sm font-medium leading-5 text-gray-500">Visa Details</p>
                        </a>
                    </li>
                    <li class="inline-flex items-center justify-start w-full">
                        <a href="#residentialAddressTab"
                            class="viewDetailTab flex space-x-3 items-center justify-start flex-1 px-3 py-2">
                            <p class="text-sm font-medium leading-5 text-gray-500">Residential Address</p>
                        </a>
                    </li>
                    <li class="inline-flex items-center justify-start w-full">
                        <a href="#postalAddressTab"
                            class="viewDetailTab flex space-x-3 items-center justify-start flex-1 px-3 py-2">
                            <p class="text-sm font-medium leading-5 text-gray-500">Postal Address</p>
                        </a>
                    </li>
                    <li class="inline-flex items-center justify-start w-full">
                        <a href="#permanentAddressTab"
                            class="viewDetailTab flex space-x-3 items-center justify-start flex-1 px-3 py-2">
                            <p class="text-sm font-medium leading-5 text-gray-500">Overseas/Permanent Address</p>
                        </a>
                    </li>
                    <li class="inline-flex items-center justify-start w-full">
                        <a href="#contactDetailsTab"
                            class="viewDetailTab flex space-x-3 items-center justify-start flex-1 px-3 py-2">
                            <p class="text-sm font-medium leading-5 text-gray-500">Contact Details</p>
                        </a>
                    </li>
                    <li class="inline-flex items-center justify-start w-full">
                        <a href="#emergencyDetailsTab"
                            class="viewDetailTab flex space-x-3 items-center justify-start flex-1 px-3 py-2">
                            <p class="text-sm font-medium leading-5 text-gray-500">Emergency Details</p>
                        </a>
                    </li>
                    <li class="inline-flex items-center justify-start w-full">
                        <a href="#surveyDetailsTab"
                            class="viewDetailTab flex space-x-3 items-center justify-start flex-1 px-3 py-2">
                            <p class="text-sm font-medium leading-5 text-gray-500">Survey</p>
                        </a>
                    </li>
                    <li class="inline-flex items-center justify-start w-full">
                        <a href="#usiDetailsTab"
                            class="viewDetailTab flex space-x-3 items-center justify-start flex-1 px-3 py-2">
                            <p class="text-sm font-medium leading-5 text-gray-500">USI: Unique Student Identifier</p>
                        </a>
                    </li>
                </ul>
            </div>
        </div>
        <div class="flex flex-row h-full bg-gray-100 w-3/4">
            <div class="space-y-6 w-full editStudentModal-wrap">
                <div class="page-section" id="basicInfoTab"></div>
                <div class="page-section" id="passportDetailsTab"></div>
                <div class="page-section" id="visaDetailsTab"></div>
                <div class="page-section" id="residentialAddressTab"></div>
                <div class="page-section" id="postalAddressTab"></div>
                <div class="page-section" id="permanentAddressTab"></div>
                <div class="page-section" id="contactDetailsTab"></div>
                <div class="page-section" id="emergencyDetailsTab"></div>
                <div class="page-section" id="surveyDetailsTab"></div>
                <div class="page-section" id="usiDetailsTab"></div>
            </div>
        </div>
    </div>
</div>
<div id="addressHistoryModal" style="display: none;">
    <x-v2.grid-table id="addressHistoryGrid" :borderless="true"></x-v2.grid-table>
</div>
<div id="emergencyDetailsHistoryModal" style="display: none;">
    <x-v2.grid-table id="emergencyDetailsHistoryGrid" :borderless="true"></x-v2.grid-table>
</div>
<div id="profileHistoryModal" style="display: none;">
    <x-v2.grid-table id="profileHistoryGrid" :borderless="true"></x-v2.grid-table>
</div>
<script type="text/javascript"
    src="https://maps.googleapis.com/maps/api/js?key={{ Config::get('constants.google_place_key') }}&libraries=places"
    defer>
</script>

{{-- TEMPLATE:RENDER --}}
@include('v2.sadmin.student.templates.common.personal-info')