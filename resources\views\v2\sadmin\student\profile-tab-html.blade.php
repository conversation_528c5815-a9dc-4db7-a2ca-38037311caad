@if ($currentTab=='summary')
<x-v2.skeleton.templates.summary />
<div class="summary-li-detail-ajax">
    @include('v2.sadmin.student.tabs.summary')
</div>
@endif

@if ($currentTab=='course')
<x-v2.skeleton.templates.course />
<div class="course-li-detail-ajax">
    @include('v2.sadmin.student.tabs.course')
</div>
@endif

@if ($currentTab=='attendance')
<x-v2.skeleton.templates.attendance />
<div class="attendance-li-detail-ajax">
    @include('v2.sadmin.student.tabs.attendance')
</div>
@endif

@if ($currentTab=='documents')
<x-v2.skeleton.templates.document />
<div class="documents-li-detail-ajax">
    @include('v2.sadmin.student.tabs.document')
</div>
@endif

@if ($currentTab=='timetable')
<x-v2.skeleton.templates.timetable />
<div class="timetable-li-detail-ajax">
    @include('v2.sadmin.student.tabs.timetable')
</div>

@endif
@if ($currentTab=='result')
<x-v2.skeleton.templates.result />
<div class="result-li-detail-ajax">
    @include('v2.sadmin.student.tabs.result')
</div>

@endif
@if ($currentTab=='payments')
<x-v2.skeleton.templates.payment />
<div class="payments-li-detail-ajax">
    @include('v2.sadmin.student.tabs.payments')
</div>
@endif

@if ($currentTab=='placement')
<x-v2.skeleton.templates.placement />
<div class="placement-li-detail-ajax">
    @include('v2.sadmin.student.tabs.placement')
</div>
@endif

@if ($currentTab=='activitylog')
<x-v2.skeleton.templates.activity />
<div class="activitylog-li-detail-ajax">
    @include('v2.sadmin.student.tabs.activity-tab')
</div>
@endif

@if ($currentTab=='riskmatrix')
<x-v2.skeleton.templates.riskmatrix />
<div class="riskmatrix-li-detail-ajax">
    @include('v2.sadmin.student.tabs.riskmatrix-tab')
</div>
@endif
