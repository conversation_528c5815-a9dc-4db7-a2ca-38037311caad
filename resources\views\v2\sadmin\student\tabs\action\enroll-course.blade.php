<style>
    #enrollNewCourseModal .k-dialog-actions {
        display: none !important;
    }
</style>
{{-- Enroll New Course Modal --}}
<div id="enrollNewCourseModal" style="display: none; overflow: scroll;">
    <form id="enrollNewCourseForm" name="enrollNewCourseForm" method="POST">
        <div class="grid grid-cols-1 lg:flex bg-gray-100 px-6 pt-6 pb-24 space-x-0 lg:space-x-12 gap-2 lg:gap-0">
            <div class="flex w-full lg:w-1/4">
                <div
                    class="flex flex-col space-y-4 items-center justify-start py-4 relative lg:fixed w-full lg:w-1/4 bg-white rounded-lg">
                    <ul class="enroll-course-bar ">
                        <li class="inline-flex items-center justify-start w-full">
                            <a href="#generalDetailsTab"
                                class="enrollCourseTab flex space-x-3 items-center justify-start flex-1 px-3 py-2 active">
                                <p class="text-sm font-medium leading-5 text-gray-500">General Details</p>
                            </a>
                        </li>
                        <li class="inline-flex items-center justify-start w-full">
                            <a href="#intakeDetailsTab"
                                class="enrollCourseTab flex space-x-3 items-center justify-start flex-1 px-3 py-2">
                                <p class="text-sm font-medium leading-5 text-gray-500">Intake Details</p>
                            </a>
                        </li>
                        <li class="inline-flex items-center justify-start w-full">
                            <a href="#feesDetailsTab"
                                class="enrollCourseTab flex space-x-3 items-center justify-start flex-1 px-3 py-2">
                                <p class="text-sm font-medium leading-5 text-gray-500">Fees Details</p>
                            </a>
                        </li>
                        <li class="inline-flex items-center justify-start w-full defaultContractScheduleSetTab">
                            <a href="#defaultContractScheduleSetTab"
                                class="enrollCourseTab flex space-x-3 items-center justify-start flex-1 px-3 py-2">
                                <p class="text-sm font-medium leading-5 text-gray-500">Default Contract Schedule Set</p>
                            </a>
                        </li>
                        <li class="inline-flex items-center justify-start w-full">
                            <a href="#classTypeTab"
                                class="enrollCourseTab flex space-x-3 items-center justify-start flex-1 px-3 py-2">
                                <p class="text-sm font-medium leading-5 text-gray-500">Class Type</p>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="flex flex-row h-full bg-gray-100 w-full lg:w-3/4">
                <div class="space-y-6 w-full enrollNewCourseModal-wrap">
                    <div class="enroll-course-section dummy-html" id="generalDetailsTab">
                        <div class="w-full holder">
                            <div
                                class="inline-flex flex-col space-y-6 items-start justify-start p-6 bg-white border rounded-lg border-gray-200 w-full">
                                <div class="flex flex-col space-y-4 items-start justify-start w-full border-b pb-4">
                                    <div class="inline-flex items-center justify-between">
                                        <div class="flex space-x-2 items-center justify-start">
                                            <p class="text-lg font-medium leading-6 text-gray-900">General Details</p>
                                            <img src="{{ asset('v2/img/question.svg') }}" class="" alt="Help">
                                        </div>
                                    </div>
                                </div>
                                <div class="flex space-x-4 my-5 items-center">
                                    <div class="flex w-16 h-16 display_profile_pic">
                                        <div class="w-16 h-16 rounded-md">
                                            <div class="rounded-md">
                                                <div
                                                    class='flex user-profile-pic w-16 h-16 !rounded-full bg-blue-500 items-center'>
                                                    <span
                                                        class='text-2xl flex justify-center items-center leading-6 px-1 w-md'>XXX</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="flex flex-col items-start space-y-1 justify-center">
                                        <p class="text-sm font-bold leading-5 text-gray-900"></p>
                                        <p class="text-xs leading-4 text-gray-400">XXXXX</p>
                                    </div>
                                </div>
                                <div class="inline-flex items-center justify-start w-1/2">
                                    <div class="inline-flex flex-col items-start justify-start w-full">
                                        <p class="text-sm font-medium leading-5 text-gray-700 w-full">AVETMISS Claim
                                            (Funded)</p>
                                        <p class="text-sm leading-5 text-gray-500 w-full switchText">Yes</p>
                                    </div>
                                    <div class="flex items-center justify-end customSwitchButton">
                                        <input type="checkbox" name="is_claim" value="1" id="is_claim" checked />
                                    </div>
                                </div>
                                <div class="inline-flex items-center justify-start w-1/2">
                                    <div class="inline-flex flex-col items-start justify-start w-full">
                                        <p class="text-sm font-medium leading-5 text-gray-700 w-full">Is Full
                                            qualification</p>
                                        <p class="text-sm leading-5 text-gray-500 w-full switchText">Yes</p>
                                    </div>
                                    <div class="flex items-center justify-end customSwitchButton">
                                        <input type="checkbox" name="" value="1" id="is_qualification" disabled />
                                        <input type="hidden" name="is_qualification" id="is_qualification_hidden" />
                                    </div>
                                </div>
                                <div class="inline-flex items-center justify-start w-1/2">
                                    <div class="inline-flex flex-col items-start justify-start w-full">
                                        <p class="text-sm font-medium leading-5 text-gray-700 w-full">Certificate Issued
                                        </p>
                                        <p class="text-sm leading-5 text-gray-500 w-full switchText">No</p>
                                    </div>
                                    <div class="flex items-center justify-end customSwitchButton">
                                        <input type="checkbox" name="is_certificate" value="1" id="is_certificate" />
                                    </div>
                                </div>
                                <div class="inline-flex items-center justify-start w-1/2">
                                    <div class="inline-flex flex-col items-start justify-start w-full">
                                        <p class="text-sm font-medium leading-5 text-gray-700 w-full">Module of Delivery
                                        </p>
                                        <p class="text-sm leading-5 text-gray-500 w-full switchText">No</p>
                                    </div>
                                    <div class="flex items-center justify-end customSwitchButton">
                                        <input type="checkbox" name="mode_of_delivery" value="1" id="mode_of_delivery"
                                            checked />
                                    </div>
                                </div>
                                <div class="inline-flex space-x-4 items-start justify-start w-full">
                                    <div class="inline-flex flex-col space-y-1 items-start justify-start w-1/2">
                                        <p class="text-sm font-medium leading-5 text-gray-700">Offer Id</p>
                                        <div class="w-full">
                                            <input name="offer_id" id="offer_id_add" required />
                                        </div>
                                    </div>
                                    <div class="inline-flex flex-col space-y-1 items-start justify-start w-1/2">
                                        <p class="text-sm font-medium leading-5 text-gray-700">Course</p>
                                        <div class="w-full">
                                            <input id="course_id_add" name="course_id" required />
                                        </div>
                                    </div>
                                </div>
                                <div class="inline-flex space-x-4 items-start justify-start w-full">
                                    <div class="inline-flex flex-col space-y-1 items-start justify-start w-1/2">
                                        <p class="text-sm font-medium leading-5 text-gray-700">Which Campus?</p>
                                        <div class="w-full">
                                            <input id="campus_id" name="campus_id" required />
                                        </div>
                                    </div>
                                    <div class="inline-flex flex-col space-y-1 items-start justify-start w-1/2">
                                        <p class="text-sm font-medium leading-5 text-gray-700">Result Calculation
                                            Method</p>
                                        <div class="w-full">
                                            <input id="res_cal_method" name="res_cal_method" required />
                                        </div>
                                    </div>
                                </div>
                                <div class="inline-flex space-x-4 items-start justify-start w-1/2">
                                    <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                                        <p class="text-sm font-medium leading-5 text-gray-700">Agent</p>
                                        <div class="w-full">
                                            <input id="agent_id" name="agent_id" required />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="enroll-course-section" id="intakeDetailsTab"></div>
                    <div class="enroll-course-section" id="feesDetailsTab"></div>
                    <div class="enroll-course-section defaultContractScheduleSetTab" id="defaultContractScheduleSetTab">
                    </div>
                    <div class="enroll-course-section" id="classTypeTab"></div>
                </div>
            </div>
        </div>
        <div
            class="modal-footer w-full flex space-x-4 items-center justify-end px-6 py-4 border-t rounded-md bg-white fixed bottom-0">
            <div class="float-right flex space-x-4 items-center justify-end">
                <button type="button" class="btn-secondary px-4 py-2 cancelBtn">
                    <p class="text-sm font-medium leading-5 text-gray-700">Cancel</p>
                </button>
                <button type="button" class="btn-primary px-4 py-2" id="createEnrollNewCourseBtn" disabled>
                    <p class="text-sm font-medium leading-5 text-white">Add Course</p>
                </button>
            </div>
        </div>
    </form>
</div>

{{-- Enroll New Course Sections --}}



{{-- Edit Enroll Course Modal --}}
<div id="enrollEditCourseModal" style="display: none; overflow: scroll;">
    <div id="enrollEditCourseForm" name="enrollEditCourseForm">
        <input type="hidden" name="student_id" value="{{ isset($studentId) ? $studentId : '' }}" />
        <div class="flex bg-gray-100 px-6 pt-6 pb-24 space-x-12">
            <div class="flex w-1/4">
                <div
                    class="flex flex-col space-y-4 items-center justify-start py-4 bg-white shadow border rounded-md border-gray-200 w-1/4 fixed">
                    <ul class="flex flex-col items-start justify-start w-full bg-white edit-enroll-course-bar">
                        <li class="inline-flex items-center justify-start w-full">
                            <a href="#1"
                                class="editEnrollCourseTab flex space-x-3 items-center justify-start flex-1 px-3 py-2 active">
                                <p class="text-sm font-medium leading-5 text-gray-500">General Details</p>
                            </a>
                        </li>
                        <li class="inline-flex items-center justify-start w-full">
                            <a href="#2"
                                class="editEnrollCourseTab flex space-x-3 items-center justify-start flex-1 px-3 py-2">
                                <p class="text-sm font-medium leading-5 text-gray-500">Intake Details</p>
                            </a>
                        </li>
                        <li class="inline-flex items-center justify-start w-full">
                            <a href="#3"
                                class="editEnrollCourseTab flex space-x-3 items-center justify-start flex-1 px-3 py-2">
                                <p class="text-sm font-medium leading-5 text-gray-500">Fees Details</p>
                            </a>
                        </li>
                        <li class="inline-flex items-center justify-start w-full editDefaultContractScheduleSetTab">
                            <a href="#4"
                                class="editEnrollCourseTab flex space-x-3 items-center justify-start flex-1 px-3 py-2">
                                <p class="text-sm font-medium leading-5 text-gray-500">Default Contract Schedule Set
                                </p>
                            </a>
                        </li>
                        <li class="inline-flex items-center justify-start w-full">
                            <a href="#5"
                                class="editEnrollCourseTab flex space-x-3 items-center justify-start flex-1 px-3 py-2">
                                <p class="text-sm font-medium leading-5 text-gray-500">Class Type</p>
                            </a>
                        </li>
                    </ul>
                </div>
            </div>
            <div class="flex flex-row h-full bg-gray-100 w-3/4">
                <div class="space-y-6 w-full enrollEditCourseModal-wrap">
                    <div class="edit-enroll-course-section" id="editGeneralDetailsTab">
                        <div class="w-full holder">
                            <div
                                class="inline-flex flex-col space-y-6 items-start justify-start p-6 bg-white border rounded-lg border-gray-200 w-full">
                                <div class="flex flex-col space-y-4 items-start justify-start w-full border-b pb-4">
                                    <div class="inline-flex items-center justify-between">
                                        <div class="flex space-x-2 items-center justify-start">
                                            <p class="text-lg font-medium leading-6 text-gray-900">General Details</p>
                                            <img src="{{ asset('v2/img/question.svg') }}" class="" alt="Help">
                                        </div>
                                    </div>
                                </div>
                                <div class="flex space-x-4 my-5 items-center">
                                    <div class="flex w-16 h-16 display_profile_pic">
                                        <div class="w-16 h-16 rounded-md">
                                            <div class="rounded-md">
                                                <div
                                                    class='flex user-profile-pic w-16 h-16 !rounded-full bg-blue-500 items-center'>
                                                    <span
                                                        class='text-2xl flex justify-center items-center leading-6 px-1 w-full'>XXX</span>
                                                </div>
                                            </div>
                                        </div>
                                    </div>
                                    <div class="flex flex-col items-start space-y-1 justify-center">
                                        <p class="text-sm font-bold leading-5 text-gray-900"></p>
                                        <p class="text-xs leading-4 text-gray-400">XXXXX</p>
                                    </div>
                                </div>
                                <div class="inline-flex items-center justify-start w-1/2">
                                    <div class="inline-flex flex-col items-start justify-start w-full">
                                        <p class="text-sm font-medium leading-5 text-gray-700 w-full">AVETMISS Claim
                                            (Funded)</p>
                                        <p class="text-sm leading-5 text-gray-500 w-full switchText">Yes</p>
                                    </div>
                                    <div class="flex items-center justify-end customSwitchButton">
                                        <input type="checkbox" name="is_claim" value="1" id="is_claim" checked />
                                    </div>
                                </div>
                                <div class="inline-flex items-center justify-start w-1/2">
                                    <div class="inline-flex flex-col items-start justify-start w-full">
                                        <p class="text-sm font-medium leading-5 text-gray-700 w-full">Is Full
                                            qualification</p>
                                        <p class="text-sm leading-5 text-gray-500 w-full switchText">Yes</p>
                                    </div>
                                    <div class="flex items-center justify-end customSwitchButton">
                                        <input type="checkbox" name="" value="1" id="is_qualification" disabled />
                                        <input type="hidden" name="is_qualification" id="is_qualification_hidden" />
                                    </div>
                                </div>
                                <div class="inline-flex items-center justify-start w-1/2">
                                    <div class="inline-flex flex-col items-start justify-start w-full">
                                        <p class="text-sm font-medium leading-5 text-gray-700 w-full">Certificate
                                            Issued</p>
                                        <p class="text-sm leading-5 text-gray-500 w-full switchText">No</p>
                                    </div>
                                    <div class="flex items-center justify-end customSwitchButton">
                                        <input type="checkbox" name="is_certificate" value="1" id="is_certificate" />
                                    </div>
                                </div>
                                <div class="inline-flex items-center justify-start w-1/2">
                                    <div class="inline-flex flex-col items-start justify-start w-full">
                                        <p class="text-sm font-medium leading-5 text-gray-700 w-full">Module of
                                            Delivery</p>
                                        <p class="text-sm leading-5 text-gray-500 w-full switchText">No</p>
                                    </div>
                                    <div class="flex items-center justify-end customSwitchButton">
                                        <input type="checkbox" name="mode_of_delivery" value="1" id="mode_of_delivery"
                                            checked />
                                    </div>
                                </div>
                                <div class="inline-flex space-x-4 items-start justify-start w-full">
                                    <div class="inline-flex flex-col space-y-1 items-start justify-start w-1/2">
                                        <p class="text-sm font-medium leading-5 text-gray-700">Offer Id</p>
                                        <div class="w-full">
                                            <input name="offer_id" id="offer_id_add" required />
                                        </div>
                                    </div>
                                    <div class="inline-flex flex-col space-y-1 items-start justify-start w-1/2">
                                        <p class="text-sm font-medium leading-5 text-gray-700">Course</p>
                                        <div class="w-full">
                                            <input id="course_id_add" name="course_id" required />
                                        </div>
                                    </div>
                                </div>
                                <div class="inline-flex space-x-4 items-start justify-start w-full">
                                    <div class="inline-flex flex-col space-y-1 items-start justify-start w-1/2">
                                        <p class="text-sm font-medium leading-5 text-gray-700">Which Campus?</p>
                                        <div class="w-full">
                                            <input id="campus_id" name="campus_id" required />
                                        </div>
                                    </div>
                                    <div class="inline-flex flex-col space-y-1 items-start justify-start w-1/2">
                                        <p class="text-sm font-medium leading-5 text-gray-700">Result Calculation
                                            Method</p>
                                        <div class="w-full">
                                            <input id="res_cal_method" name="res_cal_method" required />
                                        </div>
                                    </div>
                                </div>
                                <div class="inline-flex space-x-4 items-start justify-start w-1/2">
                                    <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                                        <p class="text-sm font-medium leading-5 text-gray-700">Agent</p>
                                        <div class="w-full">
                                            <input id="agent_id" name="agent_id" required />
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="edit-enroll-course-section" id="editIntakeDetailsTab"></div>
                    <div class="edit-enroll-course-section" id="editFeesDetailsTab"></div>
                    <div class="edit-enroll-course-section editDefaultContractScheduleSetTab"
                        id="editDefaultContractScheduleSetTab"></div>
                    <div class="edit-enroll-course-section !mb-32" id="editclassTypeTab"></div>
                </div>
            </div>
        </div>
        <div
            class="modal-footer w-full flex space-x-4 items-center justify-end px-6 py-4 border-t rounded-md bg-white fixed bottom-0">
            <div class="float-right flex space-x-4 items-center justify-end">
                <button
                    class="flex items-center justify-center h-full px-4 py-2 bg-white shadow border hover:shadow-xl rounded-xl border-gray-300 focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-gray-400 cancelBtn"
                    type="button">
                    <p class="text-sm leading-6 text-gray-700">Cancel</p>
                </button>
                <button
                    class="flex items-center justify-center h-full px-4 py-2 bg-primary-blue-500 shadow rounded-xl hover:shadow-xl rounded-lg focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-primary-blue-600"
                    id="updateEnrollCourseBtn" type="button">
                    <p class="text-sm leading-6 text-white">Save</p>
                </button>
                <button
                    class="flex items-center justify-center h-full px-4 py-2 bg-primary-blue-500 shadow rounded-xl hover:shadow-xl rounded-lg focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-primary-blue-600"
                    id="updateAndCloseEnrollCourseBtn" type="button">
                    <p class="text-sm leading-6 text-white">Save and Close</p>
                </button>
            </div>
        </div>
    </div>
</div>

{{-- Enroll Edit Course Sections --}}


{{-- Enroll Subject Modal --}}
<div id="enrollSubjectsModal" style="display: none;">
    <div class="inline-flex flex-col p-6 items-start justify-start bg-gray-100 h-full w-full">
        <div class="studentCourseDetailHeader w-full">
        </div>
        <div class="flex items-center justify-start py-6 space-x-2">
            <button type="button" class="EnrollSubjectsExcelExport btn-secondary">
                <svg width="12" height="16" viewBox="0 0 12 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M5.64645 0.146447C5.84171 -0.0488155 6.15829 -0.0488155 6.35355 0.146447L10.3536 4.14645C10.5488 4.34171 10.5488 4.65829 10.3536 4.85355C10.1583 5.04882 9.84171 5.04882 9.64645 4.85355L6.5 1.70711V12.5C6.5 12.7761 6.27614 13 6 13C5.72386 13 5.5 12.7761 5.5 12.5V1.70711L2.35355 4.85355C2.15829 5.04882 1.84171 5.04882 1.64645 4.85355C1.45118 4.65829 1.45118 4.34171 1.64645 4.14645L5.64645 0.146447ZM0 15.5C0 15.2239 0.223858 15 0.5 15H11.5C11.7761 15 12 15.2239 12 15.5C12 15.7761 11.7761 16 11.5 16H0.5C0.223858 16 0 15.7761 0 15.5Z"
                        fill="#9CA3AF" />
                </svg>
                <p class="text-xs leading-4 text-gray-700 truncate">EXPORT</p>
            </button>
            <button type="button" class="trainingPlanBtn btn-secondary">
                <svg width="13" height="14" viewBox="0 0 13 14" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M3 2.00195C3 0.897382 3.89543 0.00195312 5 0.00195312H8.58196C8.97969 0.00195312 9.36114 0.159912 9.64243 0.441098L12.5605 3.35806C12.8419 3.63938 13 4.021 13 4.41892V11.998C13 13.1026 12.1046 13.998 11 13.998H7.66586C8.06858 13.7141 8.43145 13.3774 8.74446 12.998H11C11.5523 12.998 12 12.5503 12 11.998V5.0029H9.49951C8.67108 5.0029 7.99951 4.33133 7.99951 3.5029V1.00195H5C4.44771 1.00195 4 1.44967 4 2.00195V4.02242C3.65659 4.05337 3.32228 4.11588 3 4.20703V2.00195ZM8.99951 1.21237V3.5029C8.99951 3.77904 9.22337 4.0029 9.49951 4.0029H11.7911L8.99951 1.21237ZM9 9.5C9 11.9853 6.98528 14 4.5 14C2.01472 14 0 11.9853 0 9.5C0 7.01472 2.01472 5 4.5 5C6.98528 5 9 7.01472 9 9.5ZM4.14605 11.8532L4.14857 11.8557C4.19602 11.9026 4.25051 11.938 4.30861 11.9621C4.36669 11.9861 4.4303 11.9996 4.497 12L4.5 12L4.503 12C4.5697 11.9996 4.63331 11.9861 4.69139 11.9621C4.75036 11.9377 4.80561 11.9015 4.85355 11.8536L6.85355 9.85355C7.04882 9.65829 7.04882 9.34171 6.85355 9.14645C6.65829 8.95118 6.34171 8.95118 6.14645 9.14645L5 10.2929V7.5C5 7.22386 4.77614 7 4.5 7C4.22386 7 4 7.22386 4 7.5L4 10.2929L2.85355 9.14645C2.65829 8.95118 2.34171 8.95118 2.14645 9.14645C1.95118 9.34171 1.95118 9.65829 2.14645 9.85355L4.14605 11.8532Z"
                        fill="#9CA3AF" />
                </svg>
                <p class="text-xs leading-4 text-gray-700">GENERATE TRAINING PLAN</p>
            </button>
            {{-- <button type="button" class="generateCertificateBtn btn-secondary">
                <svg width="16" height="15" viewBox="0 0 16 15" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M0 2C0 0.895431 0.895431 0 2 0H14C15.1046 0 16 0.895431 16 2V5.14584C15.7001 4.87745 15.3641 4.64868 15 4.46776V2C15 1.44772 14.5523 1 14 1H2C1.44772 1 1 1.44772 1 2V10C1 10.5523 1.44772 11 2 11H9.25777C9.33361 11.1133 9.41446 11.223 9.5 11.3287V12H2C0.895431 12 0 11.1046 0 10V2ZM16.5 8.5C16.5 9.45377 16.1185 10.3184 15.4998 10.9497L15.5 15C15.5 15.412 15.0296 15.6472 14.7 15.4L13.3 14.35C13.1222 14.2167 12.8778 14.2167 12.7 14.35L11.3 15.4C10.9704 15.6472 10.5 15.412 10.5 15V10.9495C9.88141 10.3182 9.5 9.45365 9.5 8.5C9.5 6.567 11.067 5 13 5C14.933 5 16.5 6.567 16.5 8.5ZM13 12C12.4629 12 11.9549 11.8794 11.5 11.6632V13.75L12.7428 13.0043C12.9011 12.9093 13.0989 12.9093 13.2572 13.0043L14.5 13.75V11.6632C14.0454 11.8792 13.5368 12 13 12ZM13 11C14.3807 11 15.5 9.88071 15.5 8.5C15.5 7.11929 14.3807 6 13 6C11.6193 6 10.5 7.11929 10.5 8.5C10.5 9.88071 11.6193 11 13 11ZM3 3.5C3 3.22386 3.22386 3 3.5 3H12.5C12.7761 3 13 3.22386 13 3.5C13 3.77614 12.7761 4 12.5 4H3.5C3.22386 4 3 3.77614 3 3.5ZM3.5 8C3.22386 8 3 8.22386 3 8.5C3 8.77614 3.22386 9 3.5 9H7.5C7.77614 9 8 8.77614 8 8.5C8 8.22386 7.77614 8 7.5 8H3.5Z"
                        fill="#9CA3AF" />
                </svg>
                <p class="text-xs leading-4 text-gray-700">GENERATE CERTIFICATE</p>
            </button> --}}
        </div>
        <div
            class="inline-flex flex-col items-start justify-start p-6 bg-white border rounded-lg border-gray-200 w-full">
            <div class="flex flex-col space-y-2 items-start justify-start w-full">
                <div class="inline-flex space-x-4 items-center justify-between w-full">
                    <p class="text-lg font-medium leading-6 text-gray-900">Unit Enrollment</p>
                    <button type="button" class="addUnitSubjectBtn btn-primary">
                        <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M6.00039 0.400391C6.44222 0.400391 6.80039 0.758563 6.80039 1.20039V5.20039H10.8004C11.2422 5.20039 11.6004 5.55856 11.6004 6.00039C11.6004 6.44222 11.2422 6.80039 10.8004 6.80039H6.80039V10.8004C6.80039 11.2422 6.44222 11.6004 6.00039 11.6004C5.55856 11.6004 5.20039 11.2422 5.20039 10.8004V6.80039H1.20039C0.758563 6.80039 0.400391 6.44222 0.400391 6.00039C0.400391 5.55856 0.758563 5.20039 1.20039 5.20039H5.20039V1.20039C5.20039 0.758563 5.55856 0.400391 6.00039 0.400391Z"
                                fill="white" />
                        </svg>
                        <p class="text-sm font-medium leading-4 text-white uppercase">Add Units</p>
                    </button>
                </div>
                <div id="enrollSubjectList"
                    class="tw-table tw-table--scrollbar-hide tw-table__bordered--rounded tw-table__header--borderless">
                </div>
            </div>
        </div>
    </div>
</div>

<div id="addUnitSubjectModal" style="display: none;">
    <div class="studentCourseDetailHeader w-full p-6 bg-gray-100">
    </div>
    <form id="addUnitSubjectForm" name="addUnitSubjectForm">
        <div class="flex px-6 pt-6 pb-24 flex-col space-y-6 bg-gray-100">
            <div
                class="inline-flex flex-col space-y-6 items-start justify-start p-6 bg-white border rounded-lg border-gray-200">
                <div class="flex flex-col space-y-4 items-start justify-start w-full border-b pb-4">
                    <div class="inline-flex items-center justify-between">
                        <div class="flex space-x-2 items-center justify-start">
                            <p class="text-lg font-medium leading-6 text-gray-900">Units </p>
                            <img src="{{ asset('v2/img/question.svg') }}" class=" " alt=" ">
                        </div>
                    </div>
                </div>
                <div class="inline-flex space-x-4 items-center justify-start w-4/5">
                    <div class="inline-flex flex-col space-y-1 items-start justify-start w-1/2">
                        <p class="text-sm font-medium leading-5 text-gray-700">Select Semester</p>
                        <input id="subject_enroll_semester" name="semester_id" required />
                    </div>
                    <div class="inline-flex flex-col space-y-1 items-start justify-start w-1/2">
                        <p class="text-sm font-medium leading-5 text-gray-700">Select Term</p>
                        <input id="subject_enroll_term" name="term" required />
                    </div>
                </div>
                <div class="flex flex-col space-y-2 items-start justify-center w-full hidden">
                    <p class="text-base font-medium leading-normal text-gray-700">Type of Enrollment</p>
                    <div class="inline-flex space-x-4 items-start justify-start w-1/2">
                        <div
                            class="student_type_class form-check flex items-center ps-4 w-1/2 border rounded-md cursor-pointer border-primary-blue-500 bg-primary-blue-50 z-10">
                            <input id="enrollment_course_stage" type="radio" value="subject" name="type_of_enrollment"
                                class="type_of_enrollment input-radio mt-0">
                            <label for="enrollment_course_stage"
                                class="text-sm font-medium leading-5 text-gray-700 cursor-pointer py-4 ms-2 w-full">Course
                                Stage</label>
                        </div>
                        <div
                            class="student_type_class form-check flex items-center ps-4 w-1/2 border rounded-md cursor-pointer">
                            <input id="enrollment_subject" type="radio" value="unit" name="type_of_enrollment"
                                class="type_of_enrollment input-radio mt-0" checked>
                            <label for="enrollment_subject"
                                class="text-sm font-medium leading-5 text-gray-700 cursor-pointer py-4 ms-2 w-full">Subject</label>
                        </div>
                    </div>
                </div>
                <div class="inline-flex flex-col space-y-1 items-start justify-start w-4/5 courseStageDiv hidden">
                    <p class="text-sm font-medium leading-snug text-gray-700">Course Stage</p>
                    <input type="text" name="course_stage" readonly value="1"
                        class="text-sm leading-5 text-gray-700 px-3 py-2 bg-white shadow-sm border rounded-lg border-gray-300 w-1/2 cusInput"
                        id="" placeholder="Course Stage">
                </div>
                <!-- <div class="inline-flex space-x-16 items-start justify-start w-5/6 selectSubjectDiv">
                    <div class="searchdata inline-flex flex-col space-y-4 items-start justify-start p-6 bg-gray-50 border rounded-md border-gray-300 w-1/2">
                        <p class="text-sm font-medium leading-tight text-gray-700">Select Subject To Enroll</p>
                        <div class="flex space-x-2 items-center justify-start w-full px-3 py-2 bg-white border rounded-lg border-gray-300 hover:shadow-lg cusInput">
                            <img src="{{ asset('v2/img/search.svg') }}" class="h-4 w-4" alt="searchIcon" />
                            <input type="text" id="" class="text-sm leading-5 font-normal text-gray-400" placeholder="Search">
                        </div>
                        <div class="rounded-lg w-full">
                            <div class="flex flex-col items-start justify-start w-full sdsdsd">
                                <select id="selectSubject"></select>
                            </div>
                        </div>
                    </div>
                    <div class="inline-flex flex-col space-y-4 items-start justify-start p-6 bg-primary-blue-50 border rounded-md border-primary-blue-500 w-1/2">
                        <div class="inline-flex space-x-4 items-center justify-between w-full">
                            <div class="flex w-full">
                                <p class="text-sm font-medium leading-5 font-medium text-gray-700">Selected Subjects</p>
                            </div>
                            <div class="flex items-center justify-center px-2.5 py-0.5 bg-primary-blue-200 rounded-lg">
                                <p class="text-xs leading-tight text-center text-primary-blue-800 countOfSelectedSub"></p>
                            </div>
                        </div>
                        <div class="flex flex-col items-start justify-start w-full selectedIds">
                            <div class="flex flex-col items-start justify-start w-full selectedContent">
                                <select id="selectedSubject" name="subject_id[]" multiple="multiple"></select>
                            </div>
                        </div>
                    </div>
                </div> -->
                <div class="tw-listbox flex gap-16 border-t pt-4 align-top selectUnitDiv w-full max-w-5xl">
                    <div class="searchdata relative w-1/2 space-y-4 rounded-md border bg-bluegray-50 p-6">
                        <p class="text-sm font-medium leading-none text-gray-700">Select Unit To Enroll</p>
                        <div class="relative w-full">
                            <img src="{{ asset('v2/img/search.svg') }}"
                                class=" h-4 w-4 absolute left-3 top-1/2 -translate-y-1/2" alt="searchIcon" />
                            <input type="text" id="" class="selectUnitSearch tw-input-text pl-8" placeholder="Search">
                        </div>
                        <div class="rounded-lg w-full">
                            <div class="flex flex-col items-start justify-start w-full">
                                <select id="selectUnit"></select>
                            </div>
                        </div>
                    </div>
                    <div class="w-1/2 space-y-4 rounded-md border border-primary-blue-500 bg-primary-blue-50 p-6">
                        <div class="flex justify-between items-center">
                            <p class="text-sm font-medium leading-none text-gray-700">Selected Units</p>
                            <p
                                class="text-xs leading-tight text-center text-primary-blue-800 px-2.5 py-0.5 bg-primary-blue-200 rounded-lg countOfSelectedUnit">
                            </p>
                        </div>
                        <div class="flex flex-col items-start justify-start w-full selectedContent">
                            <select id="selectedUnit" name="unit_id[]"></select>
                        </div>
                    </div>
                </div>
            </div>
            <div
                class="inline-flex flex-col space-y-6 items-start justify-start p-6 bg-white border rounded-lg border-gray-200">
                <div class="flex flex-col space-y-4 items-start justify-start w-full border-b pb-4">
                    <div class="inline-flex items-center justify-between">
                        <div class="flex space-x-2 items-center justify-start">
                            <p class="text-lg font-medium leading-6 text-gray-900">Other info</p>
                            <img src="{{ asset('v2/img/question.svg') }}" class=" " alt=" ">
                        </div>
                    </div>
                </div>
                {{-- <div class="inline-flex flex-col space-y-1 items-start justify-start w-4/5">
                    <p class="text-sm font-medium leading-snug text-gray-700">Class Batch</p>
                    <div
                        class="inline-flex space-x-2 items-center justify-between px-3 py-2 bg-white shadow border rounded-lg border-gray-300 w-1/2">
                        <p class="text-sm leading-tight text-gray-700">Select Batch</p>
                        <img class="w-5 h-full rounded-lg" src="https://via.placeholder.com/20x20" />
                    </div>
                </div> --}}
                <div class="inline-flex items-center justify-start w-4/5 grid grid-cols-2 gap-4">
                    <div class="inline-flex flex-col space-y-1 items-start justify-start w-full col-span-1">
                        <p class="text-sm font-medium leading-5 text-gray-700">Activity Start Date</p>
                        <input type="text" name="activity_start_date" value=""
                            class="text-sm leading-5 text-gray-700 w-full date_field" id="activity_start_date"
                            required />
                    </div>
                    <div class="inline-flex flex-col space-y-1 items-start justify-start w-full col-span-1">
                        <p class="text-sm font-medium leading-5 text-gray-700">Activity Finish Date</p>
                        <input type="text" name="activity_finish_date" value=""
                            class="text-sm leading-5 text-gray-700 w-full date_field" id="activity_finish_date"
                            required />
                    </div>
                    <input id="sub_enroll_start_date" name="start_date" type="hidden" value="">
                    <input id="sub_enroll_finish_date" name="finish_date" type="hidden" value="">
                </div>
                <div class="inline-flex items-center justify-start w-4/5 grid grid-cols-2 gap-4">
                    <div class="inline-flex flex-col space-y-1 items-start justify-start w-full col-span-1">
                        <p class="text-sm font-medium leading-5 text-gray-700">Study Reason</p>
                        <input id="study_reason" name="study_reason" required />
                    </div>
                    <div class="inline-flex flex-col space-y-1 items-start justify-start w-full col-span-1">
                        <p class="text-sm font-medium leading-5 text-gray-700">Venue/Training Location</p>
                        <input id="venue_location" name="venue_location" required />
                    </div>
                </div>
                <div class="inline-flex items-center justify-start w-4/5 grid grid-cols-2 gap-4">
                    <div class="inline-flex flex-col space-y-1 items-start justify-start w-full col-span-1">
                        <p class="text-sm font-medium leading-5 text-gray-700">Funding Source State</p>
                        <input id="funding_source_state" name="funding_source_state" required />
                    </div>
                    <div class="inline-flex flex-col space-y-1 items-start justify-start w-full col-span-1">
                        <p class="text-sm font-medium leading-5 text-gray-700">Funding Source NAT</p>
                        <input id="funding_source_state_nat" name="funding_source_nat" required />
                    </div>
                </div>
                <div class="inline-flex items-center justify-start w-4/5 grid grid-cols-2 gap-4">
                    <div class="inline-flex flex-col space-y-1 items-start justify-start w-full col-span-1">
                        <p class="text-sm font-medium leading-5 text-gray-700">Delivery Mode</p>
                        <input id="delivery_mode" name="delivery_mode" required />
                    </div>
                    <div
                        class="inline-flex flex-col space-y-1 items-start justify-start w-full col-span-1 outcome_identifier">
                        <p class="text-sm font-medium leading-5 text-gray-700">Outcome Identifier – Training
                            Organisation</p>
                        <input type="text" name="outcome_identifier"
                            class="text-sm leading-5 text-gray-700 px-3 py-2 bg-white shadow-sm border rounded-lg border-gray-300 w-full cusInput"
                            id="outcome_identifier" placeholder="Outcome Identifier" required />
                    </div>
                </div>
                <div class="inline-flex items-center justify-start w-4/5 grid grid-cols-2 gap-4">
                    <div
                        class="inline-flex flex-col space-y-1 items-start justify-start w-full col-span-1 markOutcomeDiv">
                        <p class="text-sm font-medium leading-5 text-gray-700">Select Mark Outcome</p>
                        <input name="mark_outcome" id="mark_outcome" required />
                    </div>
                    <div
                        class="inline-flex flex-col space-y-1 items-start justify-start w-full col-span-1 finalOutcomeDiv">
                        <p class="text-sm font-medium leading-5 text-gray-700">Select Final Outcome</p>
                        <input name="final_outcome" id="final_outcome" required />
                    </div>
                </div>
            </div>
        </div>
        <div class="flex modal-footer w-full inline-flex space-x-4  justify-end py-5 border-t bg-white fixed bottom-0">
            <div class="float-right flex space-x-4 items-center justify-end pr-6">
                <button type="button" class="cancelBtn btn-secondary px-3">
                    <p class="text-sm font-medium leading-4 text-gray-700">CANCEL</p>
                </button>
                <button class="addUnitSubjectsBtn btn-primary px-3" type="button">
                    <p class="text-sm font-medium leading-4 text-white">ADD UNITS</p>
                </button>
            </div>
        </div>
    </form>
</div>


{{-- Delete Student Certificate Modal --}}
<div id="deleteStudentCertificateModal"></div>

{{-- Upload Student Certificate Modal --}}
<div id="uploadStudentCertificateModal" style="display: none;">
    <div class="inline-flex flex-col items-start justify-start p-4 bg-white border rounded-lg border-gray-200 w-full">
        <div class="flex flex-col items-center justify-start w-full">
            <div class="flex flex-col items-start justify-center py-2 w-full">
                <div class="inline-flex space-x-4 items-center justify-end w-full">
                    <p class="w-1/4 text-sm font-medium leading-5 text-gray-500 text-right">Type :</p>
                    <p class="text-sm leading-5 text-gray-900 w-3/4 studentCertificateType">N/A</p>
                </div>
            </div>
            <div class="flex flex-col items-start justify-center py-2 w-full">
                <div class="inline-flex space-x-4 items-center justify-end w-full">
                    <p class="w-1/4 text-sm font-medium leading-5 text-gray-500 text-right">Document No :</p>
                    <p class="text-sm leading-5 text-gray-900 w-3/4 studentCertificateNo">-</p>
                </div>
            </div>
            <div class="flex flex-col items-start justify-center py-2 w-full">
                <div class="w-full">
                    <input type="file" name="student_certificate" id="certificateFile" />
                </div>
            </div>
        </div>
    </div>
</div>

<div id="courseIsSupersededModel"></div>
<div id="courseIsSupersededExpiredModel"></div>

{{-- TEMPLATE:RENDER Enroll New Course Sections --}}
@include('v2.sadmin.student.templates.common.enroll-generate-details')
@include('v2.sadmin.student.templates.common.enroll-fees-details')
@include('v2.sadmin.student.templates.common.enroll-general-details')
@include('v2.sadmin.student.templates.common.enroll-intake-details')
@include('v2.sadmin.student.templates.common.enroll-default-contracts')
@include('v2.sadmin.student.templates.common.enroll-class-type')

{{-- TEMPLATE:RENDER Enroll Edit Course Sections --}}
@include('v2.sadmin.student.templates.common.enroll-edit-fees-details')
@include('v2.sadmin.student.templates.common.enroll-edit-intake-details')
@include('v2.sadmin.student.templates.common.enroll-edit-default-contracts')
@include('v2.sadmin.student.templates.common.enroll-edit-class-type')

{{-- TEMPLATES: Enroll Subject Template --}}
@include('v2.sadmin.student.templates.common.enroll-subject')
@include('v2.sadmin.student.templates.common.enroll-student-certificate')