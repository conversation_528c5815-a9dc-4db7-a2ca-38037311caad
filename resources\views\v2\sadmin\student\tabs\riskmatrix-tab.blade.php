<div class="tw-riskmatrix-grid grid grid-cols-12">
    <div id="studRiskMatrixTab" class="bg-gray-100 col-span-12">
        <div class="flex py-6 px-8 w-full">
            <div class="flex flex-col p-6 space-y-6 bg-white justify-start items-center w-full shadow rounded-md overflow-y-auto max-h-full">
                <!-- Header Section -->
                <div class="flex items-center space-x-4 justify-between w-full">
                    <div class="flex space-x-2">
                        <p class="text-lg leading-5 font-medium text-gray-900">Risk Assessment Matrix</p>
                    </div>
                    <div class="addNewNotes">
                        <button type="button" class="btn-tertiary h-8 leading-8">
                            <p class="text-xs font-normal leading-4 text-primary-blue-500">Refresh</p>
                            <img src="{{ asset('v2/img/refresh-icon.svg') }}" class="w-3 h-3" alt="Refresh Icon">
                        </button>
                    </div>
                </div>

                <!-- Risk Summary Cards -->
                <div id="riskSummaryCards" class="grid grid-cols-1 md:grid-cols-4 gap-4 w-full">
                    <!-- Cards will be populated by JavaScript -->
                </div>

                <!-- Filter Section -->
                <div class="flex items-center justify-between w-full">
                    <div class="flex space-x-4">
                        <div class="flex items-center space-x-2">
                            <label class="text-sm font-medium text-gray-700">Risk Level:</label>
                            <select id="riskLevelFilter" class="form-select text-sm border-gray-300 rounded-md">
                                <option value="">All Levels</option>
                                <option value="1">Low Risk</option>
                                <option value="2">Medium Risk</option>
                                <option value="3">High Risk</option>
                            </select>
                        </div>
                        <div class="flex items-center space-x-2">
                            <label class="text-sm font-medium text-gray-700">Category:</label>
                            <select id="categoryFilter" class="form-select text-sm border-gray-300 rounded-md">
                                <option value="">All Categories</option>
                                <option value="attendance">Attendance</option>
                                <option value="payment">Payment</option>
                                <option value="result">Results</option>
                                <option value="moodle_activity">Moodle Activity</option>
                            </select>
                        </div>
                    </div>
                </div>

                <!-- Risk Assessment Grid -->
                <div class="w-full">
                    <div id="riskMatrixGrid" class="w-full"></div>
                </div>

                <!-- Risk Assessment Details Modal -->
                <div id="riskAssessmentModal" class="hidden">
                    <div class="p-6">
                        <h3 class="text-lg font-medium text-gray-900 mb-4">Risk Assessment Details</h3>
                        <div id="riskAssessmentDetails">
                            <!-- Details will be populated by JavaScript -->
                        </div>
                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<!-- Risk Matrix Templates -->
<script type="text/x-kendo-template" id="riskSummaryCardTemplate">
    <div class="bg-white border border-gray-200 rounded-lg p-4 shadow-sm">
        <div class="flex items-center justify-between">
            <div>
                <p class="text-sm font-medium text-gray-600">#= title #</p>
                <p class="text-2xl font-bold #= colorClass #">#= count #</p>
            </div>
            <div class="p-2 rounded-full #= bgColorClass #">
                <div class="w-6 h-6 #= iconColorClass #">#= icon #</div>
            </div>
        </div>
    </div>
</script>

<script type="text/x-kendo-template" id="actionTemplateRiskMatrix">
    <div class="p-4">
        <button type="button" class="btn-primary w-full mb-2" onclick="refreshRiskMatrix()">
            Refresh Data
        </button>
        <button type="button" class="btn-secondary w-full" onclick="exportRiskMatrix()">
            Export Report
        </button>
    </div>
</script>
