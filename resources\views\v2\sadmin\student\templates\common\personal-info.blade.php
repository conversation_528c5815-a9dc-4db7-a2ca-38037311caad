<script id="userIconTemplate" type="text/html">
    # var profilePic = (typeof(arr.profile_pic) != "undefined" && arr.profile_pic !== null && arr.profile_pic != "null") ? arr.profile_pic : ''; #
    # if (profilePic == '') { let name = arr.full_name.toUpperCase().split(/\s+/);
    let shortName = name[0].charAt(0) + name[1].charAt(0); #
    <div class='flex user-profile-pic w-16 h-16 !rounded-full bg-blue-500 items-center'><span
            class='text-2xl flex justify-center items-center leading-6 px-1 w-full'>#= shortName #</span></div>
    # } else { #
    <img class="w-16 h-16 rounded-md object-cover object-top" src="#= profilePic #" />
    # } #
</script>

<script id="basicInfoTemplate" type="text/html">
    <div id="1" class="w-full holder">
        <div class="bg-white shadow rounded-lg w-full border border-gray-200 basicInfo">
            <div class="w-full px-6 py-4 border-b">
                <div class="flex flex-col space-y-4 items-start justify-start w-full ">
                    <div class="inline-flex space-x-2 items-center justify-between w-full">
                        <div class="inline-flex space-x-2 items-center justify-start">
                            <p class="text-lg font-medium leading-6 text-gray-900">Basic Information</p>
                        </div>
                        <a href="javascript:;" class="editBasicInfoBtn btn-tertiary h-8">
                            <img src="{{ asset('v2/img/edit-pencil.svg') }}" class="" alt="" />
                            <p class="text-xs font-medium leading-4 text-primary-blue-500">Edit</p>
                        </a>
                    </div>
                </div>
            </div>
            <div class="flex flex-col items-start justify-center px-6 w-full">
                <div class="flex space-x-4 my-5 items-center">
                    <div class="flex w-20 h-20 display_profile_pic">
                        # var profilePic = (typeof(arr.profile_pic) != "undefined" && arr.profile_pic !== null && arr.profile_pic != "null") ? arr.profile_pic : ''; #
                        # if (profilePic == '') { let name = arr.full_name.toUpperCase().split(/\s+/); let
                        shortName = name[0].charAt(0) + name[1].charAt(0); #
                        <div class="rounded-md">
                            <div class='flex w-20 h-20 rounded-full bg-blue-500 items-center'><span
                                    class='text-4xl text-white flex justify-center items-center leading-6 px-1 w-full'>#=
                                    shortName #</span></div>
                        </div>
                        # } else { #
                        <div class="w-20 h-20 rounded-md">
                            <img class="w-20 h-20 rounded-md" src="#= profilePic #" />
                        </div>
                        # } #
                    </div>
                    <div class="flex flex-col items-start space-y-1 justify-center">
                        <p class="text-sm font-bold leading-5 text-gray-900">#: arr.full_name #</p>
                        <div class="flex items-center gap-2">
                            <div class="inline-flex items-center gap-1 text-xs leading-4 text-gray-400">
                                <span>ID:</span>
                                <p class="text-xs leading-4 text-gray-400">#: arr.generated_stud_id #</p>
                            </div>
                            <x-v2.copy width="16" height="16" data-text="#: arr.generated_stud_id #" />
                        </div>
                    </div>
                </div>
                <div class="inline-flex items-center justify-start py-4 border-b w-full">
                    <p class="w-80 text-sm font-medium leading-5 text-gray-500">Student Origin</p>
                    # let student_type = (typeof(arr.student_type) != "undefined" && arr.student_type !== null) ?
                    arr.student_type : ''; #
                    <p class="text-sm leading-5 font-normal text-gray-900">#= student_type #</p>
                </div>
                <div class="inline-flex items-center justify-start py-4 border-b w-full">
                    <p class="w-80 text-sm font-medium leading-5 text-gray-500">First Name</p>
                    <p class="text-sm leading-5 font-normal text-gray-900">#= arr.first_name#</p>
                </div>
                <div class="inline-flex items-center justify-start py-4 border-b w-full">
                    <p class="w-80 text-sm font-medium leading-5 text-gray-500">Last Name</p>
                    # let familyName = (typeof(arr.family_name) != "undefined" && arr.family_name !== null) ? arr.family_name : '-'; #
                    <p class="text-sm leading-5 font-normal text-gray-900">#= familyName #</p>
                </div>
                <div class="inline-flex items-center justify-start py-4 border-b w-full">
                    <p class="w-80 text-sm font-medium leading-5 text-gray-500">Gender</p>
                    # let gender = (typeof(arr.gender) != "undefined" && arr.gender !== null) ? arr.gender : ''; #
                    <p class="text-sm leading-5 font-normal text-gray-900">#= gender #</p>
                </div>
                {{-- <div class="inline-flex items-center justify-start py-4 border-b w-full">
                    <p class="w-80 text-sm font-medium leading-5 text-gray-500">Email Address</p>
                    # let email = (typeof(arr.email) != "undefined" && arr.email !== null) ? arr.email : ''; #
                    <p class="text-sm leading-5 font-normal text-gray-900">#= email #</p>
                </div>
                <div class="inline-flex items-center justify-start pt-4 pb-6 w-full">
                    <p class="w-80 text-sm font-medium leading-5 text-gray-500">Date of birth</p>
                    # let dob = (typeof(arr.DOB) != "undefined" && arr.DOB !== null) ? arr.DOB : ''; #
                    <p class="text-sm leading-5 font-normal text-gray-900">#= dob #</p>
                </div> --}}
            </div>
        </div>
        <form id="basicInfoForm" name="basicInfoForm">
            <div class="bg-white shadow rounded-lg w-full border border-gray-200 hidden editBasicInfo">
                <div class="w-full px-6 py-4 border-b">
                    <div class="inline-flex space-x-1 items-center justify-start w-full">
                        <p class="text-lg font-medium leading-7 text-gray-900">Basic Information</p>
                    </div>
                </div>
                <div class="flex flex-col space-y-4 items-start justify-start p-6 w-full">
                    <div class="flex flex-col space-y-4 items-start justify-start w-full">
                        <div class="flex space-x-3 items-center justify-start w-full">
                            <div class="flex w-20 h-20 display_profile_pic" id="display_logo">
                                # var profilePic = (typeof(arr.profile_pic) != "undefined" && arr.profile_pic !== null && arr.profile_pic != "null") ? arr.profile_pic : ''; #
                                # if (profilePic == '') { let name = arr.full_name.toUpperCase().split(/\s+/);
                                let shortName = name[0].charAt(0) + name[1].charAt(0); #
                                <div class="rounded-md">
                                    <div class='flex w-20 h-20 rounded-full bg-blue-500 items-center'><span
                                            class='text-4xl text-white flex justify-center items-center leading-6 px-1 w-full'>#=
                                            shortName #</span></div>
                                </div>
                                # } else { #
                                <div class="w-20 h-20 rounded-md">
                                    <img class="w-20 h-20 rounded-md" src="#= profilePic #" />
                                </div>
                                # } #
                            </div>
                            <div class="flex items-center justify-start upload-wrapper">
                                <input type="file" class="hidden" name="files" id="stud_profile_pic" />
                                <label for="stud_profile_pic"
                                    class="flex justify-center px-3 py-2 border border-gray-300 shadow rounded-lg text-sm font-normal leading-5 text-gray-700 cursor-pointer"><img
                                        src="{{ asset('v2/img/upload.svg') }}" class="mr-2" alt="" />Upload
                                    Photo</label>
                            </div>
                        </div>
                        <input class="hidden" name="full_name" id="full_name" value="#: arr.full_name #" />
                        <input class="hidden" name="generated_stud_id" id="generated_stud_id"
                            value="#: arr.generated_stud_id #" />
                        <input class="hidden" name="profile_pic" id="profile_pic" value="#= arr.profile_pic #" />
                        <div class="space-y-2 w-full">
                            <p class="text-sm font-medium leading-5 text-gray-700">Student Origin</p>
                            <ul class="tw-radio-group w-2/3 text-sm font-medium text-gray-700 bg-white rounded-lg">
                                # let offshoreActiveClass = (typeof(arr.student_type) != "undefined" && arr.student_type
                                == 'Offshore') ? 'border-primary-blue-200 bg-primary-blue-50 z-10' : ''; #
                                <li
                                    class="form-check student_type_check w-full border border-gray-300 rounded-t-lg pl-4  #= offshoreActiveClass#">
                                    <div class="flex items-center">
                                        # let offshorevalue = (typeof(arr.student_type) != "undefined" &&
                                        arr.student_type == 'Offshore') ? 'checked' : ''; #
                                        <input id="offshore" type="radio" value="Offshore" name="student_type"
                                            class="form-check-input appearance-none rounded-full h-4 w-4 border border-gray-300 bg-white checked:bg-primary-blue-500 checked:border-primary-blue-500 focus:outline-none transition duration-200 align-top bg-no-repeat bg-center bg-contain float-left mr-2 cursor-pointer"
                                            #= offshorevalue # />
                                        <label for="offshore"
                                            class="text-sm font-normal leading-5 text-gray-700 cursor-pointer inline-block w-full py-4 pr-4">Overseas
                                            Student (Offshore)</label>
                                    </div>
                                </li>
                                # let onshoreActiveClass = (typeof(arr.student_type) != "undefined" && arr.student_type
                                == 'Onshore') ? 'border-primary-blue-200 bg-primary-blue-50 z-10' : ''; #
                                <li
                                    class="form-check student_type_check w-full border border-gray-300 pl-4 #= onshoreActiveClass#">
                                    <div class="flex items-center">
                                        # let onshorevalue = (typeof(arr.student_type) != "undefined" &&
                                        arr.student_type == 'Onshore') ? 'checked' : ''; #
                                        <input id="onshore" type="radio" value="Onshore" name="student_type"
                                            class="form-check-input appearance-none rounded-full h-4 w-4 border border-gray-300 bg-white checked:bg-primary-blue-500 checked:border-primary-blue-500 focus:outline-none transition duration-200 align-top bg-no-repeat bg-center bg-contain float-left mr-2 cursor-pointer"
                                            #= onshorevalue # />
                                        <label for="onshore"
                                            class="text-sm font-normal leading-5 text-gray-700 cursor-pointer inline-block w-full py-4 pr-4">Overseas
                                            Student in Australia (Onshore)</label>
                                    </div>
                                </li>
                                # let domesticActiveClass = (typeof(arr.student_type) != "undefined" && arr.student_type
                                == 'Domestic') ? 'border-primary-blue-200 bg-primary-blue-50 z-10' : ''; #
                                <li
                                    class="form-check student_type_check w-full border border-gray-300 rounded-b-lg pl-4 #= domesticActiveClass#">
                                    <div class="flex items-center">
                                        # let domesticvalue = (typeof(arr.student_type) != "undefined" &&
                                        arr.student_type == 'Domestic') ? 'checked' : ''; #
                                        <input id="domestic" type="radio" value="Domestic" name="student_type"
                                            class="form-check-input appearance-none rounded-full h-4 w-4 border border-gray-300 bg-white checked:bg-primary-blue-500 checked:border-primary-blue-500 focus:outline-none transition duration-200 align-top bg-no-repeat bg-center bg-contain float-left mr-2 cursor-pointer"
                                            #= domesticvalue # />
                                        <label for="domestic"
                                            class="text-sm font-normal leading-5 text-gray-700 cursor-pointer inline-block w-full py-4 pr-4">Resident
                                            Student (Domestic)</label>
                                    </div>
                                </li>
                            </ul>
                        </div>

                    </div>
                    <div class="inline-flex space-x-4 items-start justify-start">
                        <div class="flex flex-col space-y-4 items-start justify-start">
                            <p class="text-sm font-medium leading-5 text-gray-700">Enter Your Full Name</p>
                            <p class="text-sm leading-5 text-gray-500">Please write the name that you used when you
                                applied for your Unique Student Identifier (USI), including any middle names. If you do
                                not yet
                                have a USI please write your name exactly as written in the identity document you choose
                                to use</p>
                        </div>
                    </div>
                    <div class="flex space-x-4 items-start justify-start w-full">
                        <div class="flex flex-col space-y-1 items-start justify-start flex-1">
                            <p class="text-sm font-medium leading-5 text-gray-700">First Name</p>
                            # let fNameText = (typeof(arr.first_name) != "undefined" && arr.first_name !== null) ?
                            arr.first_name : ''; #
                            <input required type="text"
                                    onkeydown="return /[a-z ]/i.test(event.key)"
                                    name="first_name"
                                    value="#= fNameText #"
                                    class="text-sm leading-5 text-gray-700 px-3 py-2 bg-white shadow-sm border rounded-lg border-gray-300 w-full cusInput"
                                    id="firstname">
                        </div>
                        <div class="flex flex-col space-y-1 items-start justify-start flex-1">
                            <p class="text-sm font-medium leading-5 text-gray-700">Middle Name</p>
                            # let mNameText = (typeof(arr.middel_name) != "undefined" && arr.middel_name !== null) ?
                            arr.middel_name : ''; #
                            <input type="text" onkeydown="return /[a-z ]/i.test(event.key)" name="middel_name"
                                value="#= mNameText #"
                                class="text-sm leading-5 text-gray-700 px-3 py-2 bg-white shadow-sm border rounded-lg border-gray-300 w-full cusInput"
                                id="middlename">
                        </div>
                        <div class="flex flex-col space-y-1 items-start justify-start flex-1">
                            <p class="text-sm font-medium leading-5 text-gray-700">Last Name</p>
                            # let lNameText = (typeof(arr.family_name) != "undefined" && arr.family_name !== null) ?
                            arr.family_name : ''; #
                            <input  type="text" name="family_name" value="#= lNameText #" onkeydown="return /[a-z ]/i.test(event.key)"
                                class="text-sm leading-5 text-gray-700 px-3 py-2 bg-white shadow-sm border rounded-lg border-gray-300 w-full cusInput"
                                id="lastname">
                        </div>
                    </div>
                    <div class="flex flex-col space-y-2 items-start justify-start w-full">
                        <p class="text-sm font-medium leading-5 text-gray-700">Gender</p>
                        <div class="inline-flex space-x-4 items-start justify-start w-full">
                            <div class="flex space-x-2 items-center justify-start">
                                # let malevalue = (typeof(arr.gender) != "undefined" && arr.gender == 'Male') ?
                                'checked' : ''; #
                                <div class="form-check">
                                    <input
                                        class="form-check-input appearance-none rounded-full h-4 w-4 border border-gray-300 bg-white checked:bg-blue-600 checked:border-blue-600 focus:outline-none transition duration-200 mt-1 align-top bg-no-repeat bg-center bg-contain float-left mr-2 cursor-pointer"
                                        type="radio" name="gender" value="Male" id="male" #=malevalue #>
                                    <label for="male"
                                        class="text-sm font-normal leading-5 text-gray-700 cursor-pointer">Male</label>
                                </div>
                            </div>
                            # let femalevalue = (typeof(arr.gender) != "undefined" && arr.gender == 'Female') ?
                            'checked' : ''; #
                            <div class="flex space-x-2 items-center justify-start">
                                <div class="form-check">
                                    <input
                                        class="form-check-input appearance-none rounded-full h-4 w-4 border border-gray-300 bg-white checked:bg-blue-600 checked:border-blue-600 focus:outline-none transition duration-200 mt-1 align-top bg-no-repeat bg-center bg-contain float-left mr-2 cursor-pointer"
                                        type="radio" name="gender" value="Female" id="female" #=femalevalue #>
                                    <label for="female"
                                        class="text-sm font-normal leading-5 text-gray-700 cursor-pointer">Female</label>
                                </div>
                            </div>
                            # let gendervalue = (typeof(arr.gender) != "undefined" && arr.gender == 'Not Specified') ?
                            'checked' : ''; #
                            <div class="flex space-x-2 items-center justify-start">
                                <div class="form-check">
                                    <input
                                        class="form-check-input appearance-none rounded-full h-4 w-4 border border-gray-300 bg-white checked:bg-blue-600 checked:border-blue-600 focus:outline-none transition duration-200 mt-1 align-top bg-no-repeat bg-center bg-contain float-left mr-2 cursor-pointer"
                                        type="radio" name="gender" value="Other" id="other" #=gendervalue #>
                                    <label for="other"
                                        class="text-sm font-normal leading-5 text-gray-700 cursor-pointer">Other</label>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                        # let isVerified = (typeof(arr.is_usi_verified) != "undefined" && arr.is_usi_verified == 1) ? 'disabled' : ''; #
                       # if(typeof(arr.is_usi_verified) != "undefined" && arr.is_usi_verified == 1){ #
                            <input type="text" name="is_usi_verified" hidden value=" #= arr.is_usi_verified #">
                            <input type="text" name="DOB" hidden value=" #= arr.DOB #" class="text-sm leading-5 text-gray-700  w-full">
                        # } #
                            <p class="text-sm font-medium leading-5 text-gray-700">Date of Birth</p>
                            <input type="text" name="DOB" required value=" #= arr.DOB #" #= isVerified # class="text-sm leading-5 text-gray-700  w-full date_field" id="dob">


                    </div>
                </div>
                <div class="w-full px-6 py-3 bg-gray-50 rounded-b-lg border-t border-gray-200">
                    <div class="flex flex-col items-end justify-center w-full">
                        <div class="inline-flex space-x-4 items-center justify-end">
                            <button class="backToBasicInfo btn-secondary px-4 py-2">
                                <p class="text-sm font-medium leading-5 text-gray-700">Cancel</p>
                            </button>
                            <button class="saveBasicInfo btn-primary px-4 py-2">
                                <p class="text-sm font-medium leading-5 text-white">Save</p>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</script>

<script id="passportDetailsTemplate" type="text/html">
    <div id="2" class="w-full holder">
        <div class="bg-white shadow rounded-lg w-full border border-gray-200 passportDetails">
            <div class="w-full px-6 py-4 border-b">
                <div class="flex flex-col space-y-4 items-start justify-start w-full ">
                    <div class="inline-flex space-x-2 items-center justify-between w-full">
                        <div class="inline-flex space-x-2 items-center justify-start">
                            <p class="text-lg font-medium leading-6 text-gray-900">Passport Details</p>
                        </div>
                        <a href="javascript:;" class="editPassportDetailsBtn btn-tertiary h-8">
                            <img src="{{ asset('v2/img/edit-pencil.svg') }}" class="" alt="" />
                            <p class="text-xs font-medium leading-4 text-primary-blue-500">Edit</p>
                        </a>
                    </div>
                </div>
            </div>
            <div class="flex flex-col items-start justify-center px-6  w-full">
                <div class="inline-flex items-center justify-start py-4 border-b w-full">
                    <p class="w-80 text-sm font-medium leading-5 text-gray-500">Country of Birth </p>
                    # let birth_country_name = (typeof(arr.birth_country_name) != "undefined" && arr.birth_country_name
                    !== null) ? arr.birth_country_name : ''; #
                    <p class="text-sm leading-5 font-normal text-gray-900">#= birth_country_name #</p>
                </div>
                <div class="inline-flex items-center justify-start py-4 border-b w-full">
                    <p class="w-80 text-sm font-medium leading-5 text-gray-500">Nationality </p>
                    # let birth_nationality_name = (typeof(arr.birth_nationality_name) != "undefined" &&
                    arr.birth_nationality_name !== null) ? arr.birth_nationality_name : ''; #
                    <p class="text-sm leading-5 font-normal text-gray-900">#= birth_nationality_name #</p>
                </div>
                <div class="inline-flex items-center justify-start py-4 border-b w-full">
                    <p class="w-80 text-sm font-medium leading-5 text-gray-500">Passport Number</p>
                    # let passport_no = (typeof(arr.passport_no) != "undefined" && arr.passport_no !== null) ?
                    arr.passport_no : ''; #
                    <p class="text-sm leading-5 font-normal text-gray-900">#= passport_no #</p>
                </div>
                <div class="inline-flex items-center justify-start pt-4 pb-6 w-full">
                    <p class="w-80 text-sm font-medium leading-5 text-gray-500">Expire Date</p>
                    # let passport_expiry = (typeof(arr.passport_expiry) != "undefined" && arr.passport_expiry !== null)
                    ? arr.passport_expiry : '';#
                    <p class="text-sm leading-5 font-normal text-gray-900">#= convertJsDateFormat(passport_expiry) #</p>
                </div>
            </div>
        </div>
        <form id="passportDetailsForm" name="passportDetailsForm">
            <div class="bg-white shadow rounded-lg w-full border border-gray-200 hidden editPassportDetails">
                <div class="w-full px-6 py-4 border-b">
                    <div class="inline-flex space-x-1 items-center justify-start w-full">
                        <p class="text-lg font-medium leading-7 text-gray-900">Passport Details</p>
                    </div>
                </div>
                <div class="flex flex-col space-y-4 items-start justify-start p-6 w-2/3">
                    <div class="inline-flex space-x-6 items-start justify-start w-full">
                        <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                            <p class="text-sm font-medium leading-5 text-gray-700">Country of Birth</p>
                            <div class=" w-full">
                                <input type="hidden" id="birth_country_name" name="birth_country_name" />
                                <input id="country_of_birth" name="country_of_birth" />
                                {{-- <select id="country_of_birth" name="birth_country"
                                    class="country_list form-select form-select-sm appearance-none block w-full px-2 py-2 text-sm font-normal text-gray-700 bg-white bg-clip-padding bg-no-repeat border border-solid border-gray-300 rounded-lg transition  ease-in-out m-0"
                                    aria-label=".form-select-sm example">
                                </select> --}}
                            </div>
                        </div>
                        <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                            <p class="text-sm font-medium leading-5 text-gray-700">Nationality</p>
                            <div class="w-full">
                                <input type="hidden" id="birth_nationality_name" name="birth_nationality_name" />
                                <input id="nationality_of_birth" name="nationality" />
                                {{-- <select id="nationality_of_birth" name="nationality"
                                    class="nationality_list form-select form-select-sm appearance-none block w-full px-2 py-2 text-sm font-normal text-gray-700 bg-white bg-clip-padding bg-no-repeat border border-solid border-gray-300 rounded-lg transition  ease-in-out m-0"
                                    aria-label=".form-select-sm example">
                                </select> --}}
                            </div>
                        </div>
                    </div>
                    <div class="inline-flex space-x-6 items-start justify-start w-full">
                        <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                            <p class="text-sm font-medium leading-5 text-gray-700">Passport Number</p>
                            # let passportText = (typeof(arr.passport_no) != "undefined" && arr.passport_no !== null) ?
                            arr.passport_no : ''; #
                            <input type="text" name="passport_no" required value="#= passportText #"
                                class="text-sm leading-5 text-gray-700 px-3 py-2 bg-white shadow-sm border rounded-lg border-gray-300 w-full cusInput">
                        </div>
                        <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                            <p class="text-sm font-medium leading-5 text-gray-700">Expire Date</p>
                            # let exDateText = (typeof(arr.passport_expiry) != "undefined" && arr.passport_expiry !==
                            null) ? arr.passport_expiry : ''; #
                            <input type="text" name="passport_expiry" required value="#= exDateText #"
                                class="text-sm leading-5 text-gray-700  w-full date_field" id="passport_expiry">
                        </div>
                    </div>
                </div>
                <div class="w-full px-6 py-3 bg-gray-50 rounded-b-lg border-t border-gray-200">
                    <div class="flex flex-col items-end justify-center w-full">
                        <div class="inline-flex space-x-4 items-center justify-end">
                            <button class="backToPassportDetails btn-secondary px-4 py-2">
                                <p class="text-sm font-medium leading-5 text-gray-700">Cancel</p>
                            </button>
                            <button class="savePassportDetails btn-primary px-4 py-2">
                                <p class="text-sm font-medium leading-5 text-white">Save</p>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</script>

<script id="visaDetailsTemplate" type="text/html">
    <div id="3" class="w-full holder">
        <div class="bg-white shadow rounded-lg w-full border border-gray-200 visaDetails">
            <div class="w-full px-6 py-4 border-b">
                <div class="flex flex-col space-y-4 items-start justify-start w-full ">
                    <div class="inline-flex space-x-2 items-center justify-between w-full">
                        <div class="inline-flex space-x-2 items-center justify-start">
                            <p class="text-lg font-medium leading-6 text-gray-900">Visa Details</p>
                        </div>
                        <a href="javascript:;" class="editVisaDetailsBtn btn-tertiary h-8">
                            <img src="{{ asset('v2/img/edit-pencil.svg') }}" class="" alt="" />
                            <p class="text-xs font-medium leading-4 text-primary-blue-500">Edit</p>
                        </a>
                    </div>
                </div>
            </div>
            <div class="flex flex-col items-start justify-center px-6  w-full">
                <div class="inline-flex items-center justify-start py-4 border-b w-full">
                    <p class="w-80 text-sm font-medium leading-5 text-gray-500">VISA Number</p>
                    # let visa_number = (typeof(arr.visa_number) != "undefined" && arr.visa_number !== null) ?
                    arr.visa_number : ''; #
                    <p class="text-sm leading-5 font-normal text-gray-900">#= visa_number #</p>
                </div>
                <div class="inline-flex items-center justify-start pt-4 pb-6 w-full">
                    <p class="w-80 text-sm font-medium leading-5 text-gray-500">Expire Date</p>
                    # let visa_expiry_date = (typeof(arr.visa_expiry_date) != "undefined" && arr.visa_expiry_date !==
                    null) ? arr.visa_expiry_date : '';#
                    <p class="text-sm leading-5 font-normal text-gray-900">#= convertJsDateFormat(visa_expiry_date) #
                    </p>
                </div>
            </div>
        </div>
        <form id="visaDetailsForm" name="visaDetailsForm">
            <div class="bg-white shadow rounded-lg w-full border border-gray-200 hidden editVisaDetails">
                <div class="w-full px-6 py-4 border-b">
                    <div class="inline-flex space-x-1 items-center justify-start w-full">
                        <p class="text-lg font-medium leading-7 text-gray-900">Visa Details</p>
                    </div>
                </div>
                <div class="flex flex-col space-y-4 items-start justify-start p-6 w-2/3">
                    {{-- <div class="inline-flex space-x-6 items-start justify-start w-full">
                        <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                            <p class="text-sm font-medium leading-5 text-gray-700">Visa Status</p>
                            <div class="bg-white shadow rounded-lg border-gray-300 w-full">
                                <input id="visa_status" name="visa_status" />
                                <select id="visa_status" name="visa_status"
                                    class="form-select form-select-sm appearance-none block w-full px-2 py-2 text-sm font-normal text-gray-700 bg-white bg-clip-padding bg-no-repeat border border-solid border-gray-300 rounded-lg transition  ease-in-out m-0"
                                    aria-label=".form-select-sm example">

                                </select>
                            </div>
                        </div>
                    </div> --}}
                    <div class="inline-flex space-x-6 items-start justify-start w-full">
                        <div class="inline-flex flex-col space-y-1 items-start justify-start w-full mt-1">
                            <p class="text-sm font-medium leading-5 text-gray-700">Visa Number</p>
                            # let visaText = (typeof(arr.visa_number) != "undefined" && arr.visa_number !== null) ?
                            arr.visa_number : ''; #
                            <input type="text" required name="visa_number" value="#= visaText #"
                                class="text-sm leading-5 text-gray-700 px-3 py-2 bg-white shadow-sm border rounded-lg border-gray-300 w-full cusInput">
                        </div>
                        <div class=" relative inline-flex flex-col space-y-1 items-start justify-start w-full">
                            <div class="absolute inset-y-0 left-0 flex items-center pl-3 mt-6 pointer-events-none">
                            </div>
                            <p class="text-sm font-medium leading-5 text-gray-700">Visa Expire Date</p>
                            # let visaExDateText = (typeof(arr.visa_expiry_date) != "undefined" && arr.visa_expiry_date
                            !== null) ? arr.visa_expiry_date : ''; #
                            <input type="text" required name="visa_expiry_date" value="#= visaExDateText #"
                                class="text-sm leading-5 text-gray-700 w-full date_field" id="visa_expiry_date">
                        </div>
                    </div>
                </div>
                <div class="w-full px-6 py-3 bg-gray-50 rounded-b-lg border-t border-gray-200">
                    <div class="flex flex-col items-end justify-center w-full">
                        <div class="inline-flex space-x-4 items-center justify-end">
                            <button class="backToVisaDetails btn-secondary px-4 py-2">
                                <p class="text-sm font-medium leading-5 text-gray-700">Cancel</p>
                            </button>
                            <button class="saveVisaDetails btn-primary px-4 py-2">
                                <p class="text-sm font-medium leading-5 text-white">Save</p>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</script>

<script id="residentialAddressTemplate" type="text/html">
    <div id="4" class="w-full holder">
        <div class="bg-white shadow rounded-lg w-full border border-gray-200 residentialAddress">
            <div class="w-full px-6 py-4 border-b">
                <div class="flex flex-col space-y-4 items-start justify-start w-full ">
                    <div class="inline-flex space-x-2 items-center justify-between w-full">
                        <div class="inline-flex space-x-2 items-center justify-start">
                            <p class="text-lg font-medium leading-6 text-gray-900">Residential Address</p>
                        </div>
                        <div class="flex items-center gap-4 flex-10 justify-end">
                            <button class="btn-secondary min-w-fit" id="addressHistoryBtn"><span>Address
                                    History</span></button>
                            <a href="javascript:;" class="editResidentialAddressBtn btn-tertiary h-8">
                                <img src="{{ asset('v2/img/edit-pencil.svg') }}" class="" alt="" />
                                <p class="text-xs font-medium leading-4 text-primary-blue-500">Edit</p>
                            </a>
                        </div>
                    </div>
                </div>
            </div>
            <div class="flex flex-col items-start justify-center px-6 w-full">
                <div class="inline-flex items-center justify-start py-4 border-b w-full">
                    <p class="w-80 text-sm font-medium leading-5 text-gray-500">Country</p>
                    # let cName = (typeof(arr.current_country_name) != "undefined" && arr.current_country_name !== null)
                    ? arr.current_country_name : ''; #
                    <p class="text-sm leading-5 font-normal text-gray-900">#= cName #</p>
                </div>
                <div class="inline-flex items-center justify-start py-4 border-b w-full">
                    <p class="w-80 text-sm font-medium leading-5 text-gray-500">Building/Property Name</p>
                    # let cbName = (typeof(arr.current_building_name) != "undefined" && arr.current_building_name !==
                    null) ? arr.current_building_name : ''; #
                    <p class="text-sm leading-5 font-normal text-gray-900">#= cbName #</p>
                </div>
                <div class="inline-flex items-center justify-start py-4 border-b w-full">
                    <p class="w-80 text-sm font-medium leading-5 text-gray-500">Flat/Unit</p>
                    # let cudName = (typeof(arr.current_unit_detail) != "undefined" && arr.current_unit_detail !== null)
                    ? arr.current_unit_detail : ''; #
                    <p class="text-sm leading-5 font-normal text-gray-900">#= cudName #</p>
                </div>
                <div class="inline-flex items-center justify-start py-4 border-b w-full">
                    <p class="w-80 text-sm font-medium leading-5 text-gray-500">Street Number</p>
                    # let csnName = (typeof(arr.current_street_no) != "undefined" && arr.current_street_no !== null) ?
                    arr.current_street_no : ''; #
                    <p class="text-sm leading-5 font-normal text-gray-900">#= csnName #</p>
                </div>
                <div class="inline-flex items-center justify-start py-4 border-b w-full">
                    <p class="w-80 text-sm font-medium leading-5 text-gray-500">Street Name</p>
                    # let sName = (typeof(arr.current_street_name) != "undefined" && arr.current_street_name !== null) ?
                    arr.current_street_name : ''; #
                    <p class="text-sm leading-5 font-normal text-gray-900">#= sName #</p>
                </div>
                <div class="inline-flex items-center justify-start py-4 border-b w-full">
                    <p class="w-80 text-sm font-medium leading-5 text-gray-500">City/Town/Suburb</p>
                    # let ccName = (typeof(arr.current_city) != "undefined" && arr.current_city !== null) ?
                    arr.current_city : ''; #
                    <p class="text-sm leading-5 font-normal text-gray-900">#= ccName #</p>
                </div>
                <div class="inline-flex items-center justify-start py-4 border-b w-full">
                    <p class="w-80 text-sm font-medium leading-5 text-gray-500">State/Province</p>
                    # let spName = (typeof(arr.current_state) != "undefined" && arr.current_state !== null) ?
                    arr.current_state : ''; #
                    <p class="text-sm leading-5 font-normal text-gray-900">#= spName #</p>
                </div>
                <div class="inline-flex items-center justify-start pt-4 pb-6 w-full">
                    <p class="w-80 text-sm font-medium leading-5 text-gray-500">Post Code</p>
                    # let cpdName = (typeof(arr.current_postcode) != "undefined" && arr.current_postcode !== null) ?
                    arr.current_postcode : ''; #
                    <p class="text-sm leading-5 font-normal text-gray-900">#= cpdName #</p>
                </div>
            </div>
        </div>
        <form id="residentialAddressForm" name="residentialAddressForm">
            <div class="bg-white shadow rounded-lg w-full border border-gray-200 hidden editResidentialAddress">
                <div class="w-full px-6 py-4 border-b">
                    <div class="inline-flex space-x-1 items-center justify-start w-full">
                        <p class="text-lg font-medium leading-7 text-gray-900">Residential Address</p>
                    </div>
                </div>
                <div class="flex flex-col space-y-4 items-start justify-start p-6 w-full">
                    <div class="inline-flex flex-col space-y-16 items-start justify-center w-full">
                        <div class="flex flex-col space-y-6 items-start justify-start w-full">
                            <div class="inline-flex space-x-4 items-start justify-start w-full">
                                <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                                    <p class="text-sm font-medium leading-5 text-gray-700">Address AutoFill</p>
                                    <div class="w-full">
                                        # let autoResidentAddress = (typeof(arr.addautofill) != "undefined" &&
                                        arr.addautofill !== null) ? arr.addautofill : ''; #
                                        <input type="text" name="addautofill"
                                            class="text-sm leading-5 text-gray-700 px-3 py-2 bg-white shadow-sm border rounded-lg border-gray-300 w-full cusInput"
                                            id="autocompleteResidentialAddress" value="#= autoResidentAddress #">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="inline-flex flex-col space-y-16 items-start justify-center w-full">
                        <div class="flex flex-col space-y-6 items-start justify-start w-full">
                            <div class="inline-flex space-x-4 items-start justify-start w-full">
                                <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                                    <p class="text-sm font-medium leading-5 text-gray-700">Country</p>
                                    <div class="w-full">
                                        # let currentCountryName = (typeof(arr.current_country_name) != "undefined" &&
                                        arr.current_country_name !== null) ? arr.current_country_name : ''; #
                                        <input type="hidden" id="current_country_name" name="current_country_name"
                                            value="#= currentCountryName #" />
                                        <input id="current_country" name="current_country" />
                                        {{-- <select id="current_country" name="current_country"
                                            class="country_list form-select form-select-sm appearance-none block w-full px-2 py-2 text-sm font-normal text-gray-700 bg-white bg-clip-padding bg-no-repeat border border-solid border-gray-300 rounded-lg transition  ease-in-out m-0"
                                            aria-label=".form-select-sm example">

                                        </select> --}}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="inline-flex flex-col space-y-16 items-start justify-center w-full">
                        <div class="flex flex-col space-y-6 items-start justify-start w-full">
                            <div class="inline-flex space-x-4 items-start justify-start w-full">
                                <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                                    <p class="text-sm font-medium leading-5 text-gray-700">Building/Property Name
                                        (Optional)</p>
                                    # let cbnName2 = (typeof(arr.current_building_name) != "undefined" &&
                                    arr.current_building_name !== null) ? arr.current_building_name : ''; #
                                    <input type="text" id="current_building_name" name="current_building_name"
                                        value="#= cbnName2 #"
                                        class="text-sm leading-5 text-gray-700 px-3 py-2 bg-white shadow-sm border rounded-lg border-gray-300 w-full cusInput">
                                </div>
                                <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                                    <p class="text-sm font-medium leading-5 text-gray-700">Flat/Unit</p>
                                    # let cudName2 = (typeof(arr.current_unit_detail) != "undefined" &&
                                    arr.current_unit_detail !== null) ? arr.current_unit_detail : ''; #
                                    <input type="text" id="current_unit_detail" name="current_unit_detail"
                                        value="#= cudName2 #"
                                        class="text-sm leading-5 text-gray-700 px-3 py-2 bg-white shadow-sm border rounded-lg border-gray-300 w-full cusInput">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="inline-flex flex-col space-y-16 items-start justify-center w-full">
                        <div class="flex flex-col space-y-6 items-start justify-start w-full">
                            <div class="inline-flex space-x-4 items-start justify-start w-full">
                                <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                                    <p class="text-sm font-medium leading-5 text-gray-700">Street Number</p>
                                    # let csnName2 = (typeof(arr.current_street_no) != "undefined" &&
                                    arr.current_street_no !== null) ? arr.current_street_no : ''; #
                                    <input type="text" required id="current_street_no" name="current_street_no"
                                        value="#= csnName2 #"
                                        class="text-sm leading-5 text-gray-700 px-3 py-2 bg-white shadow-sm border rounded-lg border-gray-300 w-full cusInput">
                                </div>
                                <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                                    <p class="text-sm font-medium leading-5 text-gray-700">Street Name</p>
                                    # let sName2 = (typeof(arr.current_street_name) != "undefined" &&
                                    arr.current_street_name !== null) ? arr.current_street_name : ''; #
                                    <input type="text" required id="current_street_name" name="current_street_name"
                                        value="#= sName2 #"
                                        class="text-sm leading-5 text-gray-700 px-3 py-2 bg-white shadow-sm border rounded-lg border-gray-300 w-full cusInput">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="inline-flex flex-col space-y-16 items-start justify-center w-full">
                        <div class="flex flex-col space-y-6 items-start justify-start w-full">
                            <div class="inline-flex space-x-4 items-start justify-start w-full">
                                <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                                    <p class="text-sm font-medium leading-5 text-gray-700">City/Town/suburb</p>
                                    # let ccName2 = (typeof(arr.current_city) != "undefined" && arr.current_city !==
                                    null) ? arr.current_city : ''; #
                                    <input type="text" required name="current_city" value="#= ccName2 #"
                                        class="text-sm leading-5 text-gray-700 px-3 py-2 bg-white shadow-sm border rounded-lg border-gray-300 w-full cusInput"
                                        id="current_city">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="inline-flex flex-col space-y-16 items-start justify-center w-full">
                        <div class="flex flex-col space-y-6 items-start justify-start w-full">
                            <div class="inline-flex space-x-4 items-start justify-start w-full">
                                <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                                    <p class="text-sm font-medium leading-5 text-gray-700">State/Province</p>
                                    # let csName2 = (typeof(arr.current_state) != "undefined" && arr.current_state !==
                                    null) ? arr.current_state : ''; #
                                    <input type="text" required id="current_state" name="current_state"
                                        value="#= csName2 #"
                                        class="text-sm leading-5 text-gray-700 px-3 py-2 bg-white shadow-sm border rounded-lg border-gray-300 w-full cusInput">
                                </div>
                                <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                                    <p class="text-sm font-medium leading-5 text-gray-700">Post Code</p>
                                    # let cpName2 = (typeof(arr.current_postcode) != "undefined" && arr.current_postcode
                                    !== null) ? arr.current_postcode : ''; #
                                    <input type="text" required id="current_postcode" name="current_postcode"
                                        value="#= cpName2 #"
                                        class="postalCode text-sm leading-5 text-gray-700 px-3 py-2 bg-white shadow-sm border rounded-lg border-gray-300 w-full cusInput">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="w-full px-6 py-3 bg-gray-50 rounded-b-lg border-t border-gray-200">
                    <div class="flex flex-col items-end justify-center w-full">
                        <div class="inline-flex space-x-4 items-center justify-end">
                            <button class="backToResidentialAddress btn-secondary px-4 py-2">
                                <p class="text-sm font-medium leading-5 text-gray-700">Cancel</p>
                            </button>
                            <button class="saveResidentialAddress btn-primary px-4 py-2">
                                <p class="text-sm font-medium leading-5 text-white">Save</p>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</script>

<script id="postalAddressTemplate" type="text/html">
    <div id="5" class="w-full holder">
        <div class="bg-white shadow rounded-lg w-full border border-gray-200 postalAddress">
            <div class="w-full px-6 py-4 border-b">
                <div class="flex flex-col space-y-4 items-start justify-start w-full ">
                    <div class="inline-flex space-x-2 items-center justify-between w-full">
                        <div class="inline-flex space-x-2 items-center justify-start">
                            <p class="text-lg font-medium leading-6 text-gray-900">Postal Address</p>
                        </div>
                        <a href="javascript:;" class="editPostalAddressBtn btn-tertiary h-8">
                            <img src="{{ asset('v2/img/edit-pencil.svg') }}" class="" alt="" />
                            <p class="text-xs font-medium leading-4 text-primary-blue-500">Edit</p>
                        </a>
                    </div>
                </div>
            </div>
            <div class="flex flex-col items-start justify-center px-6 w-full">
                <div class="inline-flex items-center justify-start py-4 border-b w-full">
                    <p class="w-80 text-sm font-medium leading-5 text-gray-500">Country</p>
                    # let pcName = (typeof(arr.postal_country_name) != "undefined" && arr.postal_country_name !== null)
                    ? arr.postal_country_name : ''; #
                    <p class="text-sm leading-5 font-normal text-gray-900">#= pcName #</p>
                </div>
                <div class="inline-flex items-center justify-start py-4 border-b w-full">
                    <p class="w-80 text-sm font-medium leading-5 text-gray-500">Building/Property Name</p>
                    # let pBuildingName = (typeof(arr.postal_building_name) != "undefined" && arr.postal_building_name
                    !== null) ? arr.postal_building_name : ''; #
                    <p class="text-sm leading-5 font-normal text-gray-900">#= pBuildingName #</p>
                </div>
                <div class="inline-flex items-center justify-start py-4 border-b w-full">
                    <p class="w-80 text-sm font-medium leading-5 text-gray-500">Flat/Unit</p>
                    # let pUnitDetail = (typeof(arr.postal_unit_detail) != "undefined" && arr.postal_unit_detail !==
                    null) ? arr.postal_unit_detail : ''; #
                    <p class="text-sm leading-5 font-normal text-gray-900">#= pUnitDetail #</p>
                </div>
                <div class="inline-flex items-center justify-start py-4 border-b w-full">
                    <p class="w-80 text-sm font-medium leading-5 text-gray-500">Street Number</p>
                    # let pStreetNo = (typeof(arr.postal_street_no) != "undefined" && arr.postal_street_no !== null) ?
                    arr.postal_street_no : ''; #
                    <p class="text-sm leading-5 font-normal text-gray-900">#= pStreetNo #</p>
                </div>
                <div class="inline-flex items-center justify-start py-4 border-b w-full">
                    <p class="w-80 text-sm font-medium leading-5 text-gray-500">Street Name</p>
                    # let pStreetName = (typeof(arr.postal_street_name) != "undefined" && arr.postal_street_name !==
                    null) ? arr.postal_street_name : ''; #
                    <p class="text-sm leading-5 font-normal text-gray-900">#= pStreetName #</p>
                </div>
                <div class="inline-flex items-center justify-start py-4 border-b w-full">
                    <p class="w-80 text-sm font-medium leading-5 text-gray-500">City/Town/Suburb</p>
                    # let postalCity = (typeof(arr.postal_city) != "undefined" && arr.postal_city !== null) ?
                    arr.postal_city : ''; #
                    <p class="text-sm leading-5 font-normal text-gray-900">#= postalCity #</p>
                </div>
                <div class="inline-flex items-center justify-start py-4 border-b w-full">
                    <p class="w-80 text-sm font-medium leading-5 text-gray-500">State/Province</p>
                    # let psValue = (typeof(arr.postal_state) != "undefined" && arr.postal_state !== null) ?
                    arr.postal_state : ''; #
                    <p class="text-sm leading-5 font-normal text-gray-900">#= psValue #</p>
                </div>
                <div class="inline-flex items-center justify-start pt-4 pb-6 w-full">
                    <p class="w-80 text-sm font-medium leading-5 text-gray-500">Post Code</p>
                    # let ppCode = (typeof(arr.postal_postcode) != "undefined" && arr.postal_postcode !== null) ?
                    arr.postal_postcode : ''; #
                    <p class="text-sm leading-5 font-normal text-gray-900">#= ppCode #</p>
                </div>
            </div>
        </div>
        <form id="postalAddressForm" name="postalAddressForm">
            <div class="bg-white shadow rounded-lg w-full border border-gray-200 hidden editPostalAddress">
                <div class="w-full px-6 py-4 border-b">
                    <div class="inline-flex space-x-1 items-center justify-start w-full">
                        <p class="text-lg font-medium leading-7 text-gray-900">Postal Address</p>
                    </div>
                </div>
                <div class="flex flex-col space-y-4 items-start justify-start p-6 w-full">
                    <div class="flex items-center gap-2">
                        <input type="checkbox" class="k-checkbox" id="sameAsResidential" />
                        <label class="text-gray-500" for="sameAsResidential">Same as residential address</label>
                    </div>
                    <div class="inline-flex flex-col space-y-16 items-start justify-center w-full">
                        <div class="flex flex-col space-y-6 items-start justify-start w-full">
                            <div class="inline-flex space-x-4 items-start justify-start w-full">
                                <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                                    <p class="text-sm font-medium leading-5 text-gray-700">Address AutoFill</p>
                                    <div class="w-full">
                                        # let autoResidentAddress = (typeof(arr.addautofill) != "undefined" &&
                                        arr.addautofill !== null) ? arr.addautofill : ''; #
                                        <input type="text" name="addautofill"
                                            class="text-sm leading-5 text-gray-700 px-3 py-2 bg-white shadow-sm border rounded-lg border-gray-300 w-full cusInput"
                                            id="autocompletePostalAddress" value="#= autoResidentAddress #">
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="inline-flex flex-col space-y-16 items-start justify-center w-full">
                        <div class="flex flex-col space-y-6 items-start justify-start w-full">
                            <div class="inline-flex space-x-4 items-start justify-start w-full">
                                <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                                    <p class="text-sm font-medium leading-5 text-gray-700">Country</p>
                                    <div class="w-full">
                                        # let postalCountryName = (typeof(arr.postal_country_name) != "undefined" &&
                                        arr.postal_country_name !== null) ? arr.postal_country_name : ''; #
                                        <input type="hidden" id="postal_country_name" name="postal_country_name"
                                            value="#= postalCountryName#" />
                                        <input id="postal_country" name="postal_country" />
                                        {{-- <select id="postal_country" name="postal_country"
                                            class="country_list form-select form-select-sm appearance-none block w-full px-2 py-2 text-sm font-normal text-gray-700 bg-white bg-clip-padding bg-no-repeat border border-solid border-gray-300 rounded-lg transition  ease-in-out m-0"
                                            aria-label=".form-select-sm example">

                                        </select> --}}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="inline-flex flex-col space-y-16 items-start justify-center w-full">
                        <div class="flex flex-col space-y-6 items-start justify-start w-full">
                            <div class="inline-flex space-x-4 items-start justify-start w-full">
                                <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                                    <p class="text-sm font-medium leading-5 text-gray-700">Building/Property Name</p>
                                    # let pBuildingName2 = (typeof(arr.postal_building_name) != "undefined" &&
                                    arr.postal_building_name !== null) ? arr.postal_building_name : ''; #
                                    <input type="text" id="postal_building_name" name="postal_building_name"
                                        value="#= pBuildingName2 #"
                                        class="text-sm leading-5 text-gray-700 px-3 py-2 bg-white shadow-sm border rounded-lg border-gray-300 w-full cusInput">
                                </div>
                                <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                                    <p class="text-sm font-medium leading-5 text-gray-700">Flat/Unit</p>
                                    # let pUnitDetail2 = (typeof(arr.postal_unit_detail) != "undefined" &&
                                    arr.postal_unit_detail !== null) ? arr.postal_unit_detail : ''; #
                                    <input type="text" id="postal_unit_detail" name="postal_unit_detail"
                                        value="#= pUnitDetail2 #"
                                        class="text-sm leading-5 text-gray-700 px-3 py-2 bg-white shadow-sm border rounded-lg border-gray-300 w-full cusInput">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="inline-flex flex-col space-y-16 items-start justify-center w-full">
                        <div class="flex flex-col space-y-6 items-start justify-start w-full">
                            <div class="inline-flex space-x-4 items-start justify-start w-full">
                                <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                                    <p class="text-sm font-medium leading-5 text-gray-700">Street Number</p>
                                    # let psNumber2 = (typeof(arr.postal_street_no) != "undefined" &&
                                    arr.postal_street_no !== null) ? arr.postal_street_no : ''; #
                                    <input type="text" required id="postal_street_no" name="postal_street_no"
                                        value="#= psNumber2 #"
                                        class="text-sm leading-5 text-gray-700 px-3 py-2 bg-white shadow-sm border rounded-lg border-gray-300 w-full cusInput">
                                </div>
                                <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                                    <p class="text-sm font-medium leading-5 text-gray-700">Street Name</p>
                                    # let psNane2 = (typeof(arr.postal_street_name) != "undefined" &&
                                    arr.postal_street_name !== null) ? arr.postal_street_name : ''; #
                                    <input type="text" required id="postal_street_name" name="postal_street_name"
                                        value="#= psNane2 #"
                                        class="text-sm leading-5 text-gray-700 px-3 py-2 bg-white shadow-sm border rounded-lg border-gray-300 w-full cusInput">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="inline-flex flex-col space-y-16 items-start justify-center w-full">
                        <div class="flex flex-col space-y-6 items-start justify-start w-full">
                            <div class="inline-flex space-x-4 items-start justify-start w-full">
                                <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                                    <p class="text-sm font-medium leading-5 text-gray-700">City/Town/suburb</p>
                                    # let pCity2 = (typeof(arr.postal_city) != "undefined" && arr.postal_city !== null)
                                    ? arr.postal_city : ''; #
                                    <input type="text" required id="postal_city" name="postal_city" value="#= pCity2 #"
                                        class="text-sm leading-5 text-gray-700 px-3 py-2 bg-white shadow-sm border rounded-lg border-gray-300 w-full cusInput">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="inline-flex flex-col space-y-16 items-start justify-center w-full">
                        <div class="flex flex-col space-y-6 items-start justify-start w-full">
                            <div class="inline-flex space-x-4 items-start justify-start w-full">
                                <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                                    <p class="text-sm font-medium leading-5 text-gray-700">State/Province</p>
                                    # let pState2 = (typeof(arr.postal_state) != "undefined" && arr.postal_state !==
                                    null) ? arr.postal_state : ''; #
                                    <input type="text" required id="postal_state" name="postal_state"
                                        value="#= pState2 #"
                                        class="text-sm leading-5 text-gray-700 px-3 py-2 bg-white shadow-sm border rounded-lg border-gray-300 w-full cusInput">
                                </div>
                                <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                                    <p class="text-sm font-medium leading-5 text-gray-700">Post Code</p>
                                    # let ppCode2 = (typeof(arr.postal_postcode) != "undefined" && arr.postal_postcode
                                    !== null) ? arr.postal_postcode : ''; #
                                    <input type="text" required id="postal_postcode" name="postal_postcode"
                                        value="#= ppCode2 #"
                                        class="postalCode text-sm leading-5 text-gray-700 px-3 py-2 bg-white shadow-sm border rounded-lg border-gray-300 w-full cusInput">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="w-full px-6 py-3 bg-gray-50 rounded-b-lg border-t border-gray-200">
                    <div class="flex flex-col items-end justify-center w-full">
                        <div class="inline-flex space-x-4 items-center justify-end">
                            <button class="backToPostalAddress btn-secondary px-4 py-2">
                                <p class="text-sm font-medium leading-5 text-gray-700">Cancel</p>
                            </button>
                            <button class="savePostalAddress btn-primary px-4 py-2">
                                <p class="text-sm font-medium leading-5 text-white">Save</p>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</script>

<script id="permanentAddressTemplate" type="text/html">
    <div id="6" class="w-full holder">
        <div class="bg-white shadow rounded-lg w-full border border-gray-200 permanentAddress">
            <div class="w-full px-6 py-4 border-b">
                <div class="flex flex-col space-y-4 items-start justify-start w-full ">
                    <div class="inline-flex space-x-2 items-center justify-between w-full">
                        <div class="inline-flex space-x-2 items-center justify-start">
                            <p class="text-lg font-medium leading-6 text-gray-900">Overseas/Permanent Address</p>
                        </div>
                        <a href="javascript:;" class="editPermanentAddressBtn btn-tertiary h-8">
                            <img src="{{ asset('v2/img/edit-pencil.svg') }}" class="" alt="" />
                            <p class="text-xs font-medium leading-4 text-primary-blue-500">Edit</p>
                        </a>
                    </div>
                </div>
            </div>
            <div class="flex flex-col items-start justify-center px-6  w-full">
                <div class="inline-flex items-center justify-start py-4 border-b w-full">
                    <p class="w-80 text-sm font-medium leading-5 text-gray-500">Country</p>
                    # let ppcName = (typeof(arr.permanent_country_name) != "undefined" && arr.permanent_country_name !==
                    null) ? arr.permanent_country_name : ''; #
                    <p class="text-sm leading-5 font-normal text-gray-900">#= ppcName #</p>
                </div>
                <div class="inline-flex items-center justify-start pt-4 pb-6 w-full">
                    <p class="w-80 text-sm font-medium leading-5 text-gray-500">Overseas Address</p>
                    # let psName = (typeof(arr.permanent_street_name) != "undefined" && arr.permanent_street_name !==
                    null) ? arr.permanent_street_name : ''; #
                    <p class="text-sm leading-5 font-normal text-gray-900">#= psName #</p>
                </div>
            </div>
        </div>
        <form id="permanentAddressForm" name="permanentAddressForm">
            <div class="bg-white shadow rounded-lg w-full border border-gray-200 hidden editPermanentAddress">
                <div class="w-full px-6 py-4 border-b">
                    <div class="inline-flex space-x-1 items-center justify-start w-full">
                        <p class="text-lg font-medium leading-7 text-gray-900">Overseas/Permanent Address</p>
                    </div>
                </div>
                <div class="flex flex-col space-y-4 items-start justify-start p-6 w-2/3">
                    <div class="inline-flex flex-col space-y-16 items-start justify-center w-full">
                        <div class="flex flex-col space-y-6 items-start justify-start w-full">
                            <div class="inline-flex space-x-4 items-start justify-start w-full">
                                <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                                    <p class="text-sm font-medium leading-5 text-gray-700">Select Country</p>
                                    <div class="w-full">
                                        <input type="hidden" id="permanent_country_name"
                                            name="permanent_country_name" />
                                        <input id="permanent_country" name="permanent_country" />
                                        {{-- <select id="permanent_country" name="permanent_country"
                                            class="country_list form-select form-select-sm appearance-none block w-full px-2 py-2 text-sm font-normal text-gray-700 bg-white bg-clip-padding bg-no-repeat border border-solid border-gray-300 rounded-lg transition  ease-in-out m-0"
                                            aria-label=".form-select-sm example">

                                        </select> --}}
                                    </div>
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="inline-flex flex-col space-y-16 items-start justify-center w-full">
                        <div class="flex flex-col space-y-6 items-start justify-start w-full">
                            <div class="inline-flex space-x-4 items-start justify-start w-full">
                                <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                                    <p class="text-sm font-medium leading-5 text-gray-700">Overseas Address</p>
                                    # let psName2 = (typeof(arr.permanent_street_name) != "undefined" &&
                                    arr.permanent_street_name !== null) ? arr.permanent_street_name : ''; #
                                    <textarea id="message" value="#= psName2 #" name="permanent_street_name" rows="4"
                                        class="block p-2.5 w-full text-sm text-gray-700 rounded-lg border border-gray-300 cusInput"
                                        placeholder="Your message...">#= psName2 #</textarea>
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="w-full px-6 py-3 bg-gray-50 rounded-b-lg border-t border-gray-200">
                    <div class="flex flex-col items-end justify-center w-full">
                        <div class="inline-flex space-x-4 items-center justify-end">
                            <button class="backToPermanentAddress btn-secondary px-4 py-2">
                                <p class="text-sm font-medium leading-5 text-gray-700">Cancel</p>
                            </button>
                            <button class="savePermanentAddress btn-primary px-4 py-2">
                                <p class="text-sm font-medium leading-5 text-white">Save</p>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</script>

<script id="contactDetailsTemplate" type="text/html">
    <div id="7" class="w-full holder">
        <div class="bg-white shadow rounded-lg w-full border border-gray-200 contactDetails">
            <div class="w-full px-6 py-4 border-b">
                <div class="flex flex-col space-y-4 items-start justify-start w-full ">
                    <div class="inline-flex space-x-2 items-center justify-between w-full">
                        <div class="inline-flex space-x-2 items-center justify-start">
                            <p class="text-lg font-medium leading-6 text-gray-900">Contact Details</p>
                        </div>
                        <div class="flex items-center gap-4 flex-10 justify-end">
                            <button class="btn-secondary min-w-fit" id="profileHistoryBtn"><span>Contact
                                    History</span></button>
                            <a href="javascript:;" class="editContactDetailsBtn btn-tertiary h-8">
                                <img src="{{ asset('v2/img/edit-pencil.svg') }}" class="" alt="" />
                                <p class="text-xs font-medium leading-4 text-primary-blue-500">Edit</p>
                            </a>
                        </div>

                    </div>
                </div>
            </div>
            <div class="flex flex-col items-start justify-center px-6 w-full">
                <div class="inline-flex items-center justify-start py-4 border-b w-full">
                    <p class="w-80 text-sm font-medium leading-5 text-gray-500">Contact Email Address</p>
                    # let cemail = (typeof(arr.email) != "undefined" && arr.email !== null) ? arr.email : ''; #
                    <p class="text-sm leading-5 font-normal text-gray-900">#= cemail #</p>
                </div>
                <div class="inline-flex items-center justify-start py-4 border-b w-full">
                    <p class="w-80 text-sm font-medium leading-5 text-gray-500">Alternate Email Address</p>
                    # let optional_email = (typeof(arr.optional_email) != "undefined" && arr.optional_email !== null) ?
                    arr.optional_email : ''; #
                    <p class="text-sm leading-5 font-normal text-gray-900">#= optional_email #</p>
                </div>
                <div class="inline-flex items-center justify-start py-4 border-b  w-full">
                    <p class="w-80 text-sm font-medium leading-5 text-gray-500">Mobile Number</p>
                    # let current_mobile_phone = (typeof(arr.current_mobile_phone) != "undefined" &&
                    arr.current_mobile_phone !== null) ? arr.current_mobile_phone : ''; #
                    <p class="text-sm leading-5 font-normal text-gray-900">#= current_mobile_phone #</p>
                </div>
                <div class="inline-flex items-center justify-start py-4 pb-6 w-full">
                    <p class="w-80 text-sm font-medium leading-5 text-gray-500">Home Phone</p>
                    # let current_home_phone = (typeof(arr.current_home_phone) != "undefined" && arr.current_home_phone
                    !== null) ? arr.current_home_phone : ''; #
                    <p class="text-sm leading-5 font-normal text-gray-900">#= current_home_phone #</p>
                </div>

            </div>
        </div>
        <form id="contactDetailsForm" name="contactDetailsForm">
            <div class="bg-white shadow rounded-lg w-full border border-gray-200 hidden editContactDetails">
                <div class="w-full px-6 py-4 border-b">
                    <div class="inline-flex space-x-1 items-center justify-start w-full">
                        <p class="text-lg font-medium leading-7 text-gray-900">Contact Details</p>
                    </div>
                </div>
                <div class="flex flex-col space-y-4 items-start justify-start p-6 w-2/3">
                    <div class="inline-flex space-x-4 items-start justify-start w-full">
                        <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                            <p class="text-sm font-medium leading-5 text-gray-700">Contact Email Address</p>
                            # let emailText = (typeof(arr.email) != "undefined" && arr.email !== null) ? arr.email : '';
                            #
                            <input type="email" required name="email" value="#= emailText#"
                                class="text-sm leading-5 text-gray-700 px-3 py-2 bg-white shadow-sm border rounded-lg border-gray-300 w-full cusInput">
                        </div>
                        <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                            <div class="flex space-x-1">
                                <p class="text-sm font-medium leading-5 text-gray-700">Alternate Email Address</p>
                                <p class="text-sm font-medium leading-5 text-primary-blue-500">(Optional)</p>
                            </div>
                            # let opEmailText = (typeof(arr.optional_email) != "undefined" && arr.optional_email !==
                            null) ? arr.optional_email : ''; #
                            <input type="email" name="optional_email" value="#= opEmailText #"
                                class="text-sm leading-5 text-gray-700 px-3 py-2 bg-white shadow-sm border rounded-lg border-gray-300 w-full cusInput">
                        </div>
                    </div>
                    <div class="inline-flex space-x-4 items-start justify-start w-full">
                        <div class="inline-flex flex-col space-y-1 items-start justify-start flex-1">
                            <p class="text-sm font-medium leading-5 text-gray-700">Mobile Number</p>
                            # let mPhoneHomeText = (typeof(arr.current_mobile_phone) != "undefined" &&
                            arr.current_mobile_phone !== null) ? arr.current_mobile_phone : ''; #
                            <input type="text" required name="current_mobile_phone" value="#= mPhoneHomeText #"
                                class="mobileNumber text-sm leading-5 text-gray-700 px-3 py-2 bg-white shadow-sm border rounded-lg border-gray-300 w-full cusInput"
                                id="current_mobile_phone">
                        </div>
                        <div class="inline-flex flex-col space-y-1 items-start justify-start flex-1">
                            <div class="flex space-x-1">
                                <p class="text-sm font-medium leading-5 text-gray-700">Home Phone</p>
                                <p class="text-sm font-medium leading-5 text-primary-blue-500">(Optional)</p>
                            </div>
                            # let cPhoneHomeText = (typeof(arr.current_home_phone) != "undefined" &&
                            arr.current_home_phone !== null) ? arr.current_home_phone : ''; #
                            <input type="text" name="current_home_phone" value="#= cPhoneHomeText #"
                                class="mobileNumber text-sm leading-5 text-gray-700 px-3 py-2 bg-white shadow-sm border rounded-lg border-gray-300 w-full cusInput">
                        </div>
                    </div>
                </div>
                <div class="w-full px-6 py-3 bg-gray-50 rounded-b-lg border-t border-gray-200">
                    <div class="flex flex-col items-end justify-center w-full">
                        <div class="inline-flex space-x-4 items-center justify-end">
                            <button class="backToContactDetails btn-secondary px-4 py-2">
                                <p class="text-sm font-medium leading-5 text-gray-700">Cancel</p>
                            </button>
                            <button class="saveContactDetails btn-primary px-4 py-2">
                                <p class="text-sm font-medium leading-5 text-white">Save</p>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</script>

<script id="emergencyDetailsTemplate" type="text/html">
    <div id="8" class="w-full holder">
        <div class="bg-white shadow rounded-lg w-full border border-gray-200 emergencyDetails">
            <div class="w-full px-6 py-4 border-b">
                <div class="flex flex-col space-y-4 items-start justify-start w-full ">
                    <div class="inline-flex space-x-2 items-center justify-between w-full">
                        <div class="inline-flex space-x-2 items-center justify-start">
                            <p class="text-lg font-medium leading-6 text-gray-900">Emergency Details</p>
                        </div>
                        <div class="flex items-center gap-4 flex-10 justify-end">
                            <button class="btn-secondary min-w-fit" id="emergencyDetailsHistoryBtn"><span>Emergency Details History</span></button>
                            <a href="javascript:;" class="editEmergencyDetailsBtn btn-tertiary h-8">
                                <img src="{{ asset('v2/img/edit-pencil.svg') }}" class="" alt="" />
                                <p class="text-xs font-medium leading-4 text-primary-blue-500">Edit</p>
                            </a>
                        </div>

                    </div>
                </div>
            </div>
            <div class="flex flex-col items-start justify-center px-6 w-full">
                <div class="inline-flex items-center justify-start py-4 border-b w-full">
                    <p class="w-80 text-sm font-medium leading-5 text-gray-500">Contact Person</p>
                    # let ecp = (typeof(arr.emergency_contact_person) != "undefined" && arr.emergency_contact_person !==
                    null) ? arr.emergency_contact_person : ''; #
                    <p class="text-sm leading-5 font-normal text-gray-900">#= ecp #</p>
                </div>
                <div class="inline-flex items-center justify-start py-4 border-b w-full">
                    <p class="w-80 text-sm font-medium leading-5 text-gray-500">Relationship</p>
                    # let er =(typeof(arr.emergency_relationship) != "undefined" && arr.emergency_relationship !== null)
                    ? arr.emergency_relationship : ''; #
                    <p class="text-sm leading-5 font-normal text-gray-900">#= er #</p>
                </div>
                <div class="inline-flex items-center justify-start py-4 border-b w-full">
                    <p class="w-80 text-sm font-medium leading-5 text-gray-500">Phone</p>
                    # let ep = (typeof(arr.emergency_phone) != "undefined" && arr.emergency_phone !== null) ?
                    arr.emergency_phone : '';#
                    <p class="text-sm leading-5 font-normal text-gray-900">#= ep #</p>
                </div>
                <div class="inline-flex items-center justify-start py-4 border-b w-full">
                    <p class="w-80 text-sm font-medium leading-5 text-gray-500">Email</p>
                    # let ee = (typeof(arr.emergency_email) != "undefined" && arr.emergency_email !== null) ?
                    arr.emergency_email : ''; #
                    <p class="text-sm leading-5 font-normal text-gray-900">#= ee #</p>
                </div>
                <div class="inline-flex items-center justify-start pt-4 pb-6 w-full">
                    <p class="w-80 text-sm font-medium leading-5 text-gray-500">Address</p>
                    # let ea = (typeof(arr.emergency_address) != "undefined" && arr.emergency_address !== null) ?
                    arr.emergency_address : ''; #
                    <p class="text-sm leading-5 font-normal text-gray-900">#= ea #</p>
                </div>
            </div>
        </div>
        <form id="emergencyDetailsForm" name="emergencyDetailsForm">
            <div class="bg-white shadow rounded-lg w-full border border-gray-200 hidden editEmergencyDetails">
                <div class="w-full px-6 py-4 border-b">
                    <div class="inline-flex space-x-1 items-center justify-start w-full">
                        <p class="text-lg font-medium leading-7 text-gray-900">Emergency Details</p>
                    </div>
                </div>
                <div class="flex flex-col space-y-4 items-start justify-start p-6 w-2/3">
                    <div class="inline-flex flex-col space-y-16 items-start justify-center w-full">
                        <div class="flex flex-col space-y-6 items-start justify-start w-full">
                            <div class="inline-flex space-x-4 items-start justify-start w-full">
                                <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                                    <p class="text-sm font-medium leading-5 text-gray-700">Contact Person</p>
                                    # let ecp2 = (typeof(arr.emergency_contact_person) != "undefined" &&
                                    arr.emergency_contact_person !== null) ? arr.emergency_contact_person : ''; #
                                    <input type="text" required name="emergency_contact_person" value="#= ecp2 #"
                                        class="text-sm leading-5 text-gray-700 px-3 py-2 bg-white shadow-sm border rounded-lg border-gray-300 w-full cusInput">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="inline-flex flex-col space-y-16 items-start justify-center w-full">
                        <div class="flex flex-col space-y-6 items-start justify-start w-full">
                            <div class="inline-flex space-x-4 items-start justify-start w-full">
                                <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                                    <p class="text-sm font-medium leading-5 text-gray-700">Relationship</p>
                                    # let er2 = (typeof(arr.emergency_relationship) != "undefined" &&
                                    arr.emergency_relationship !== null) ? arr.emergency_relationship : ''; #
                                    <input type="text" required name="emergency_relationship" value="#= er2 #"
                                        class="text-sm leading-5 text-gray-700 px-3 py-2 bg-white shadow-sm border rounded-lg border-gray-300 w-full cusInput">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="inline-flex flex-col space-y-16 items-start justify-center w-full">
                        <div class="flex flex-col space-y-6 items-start justify-start w-full">
                            <div class="inline-flex space-x-4 items-start justify-start w-full">
                                <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                                    <p class="text-sm font-medium leading-5 text-gray-700">Phone</p>
                                    # let ep2 = (typeof(arr.emergency_phone) != "undefined" && arr.emergency_phone !==
                                    null) ? arr.emergency_phone : ''; #
                                    <input type="text" required name="emergency_phone" value="#= ep2 #"
                                        class="mobileNumber text-sm leading-5 text-gray-700 px-3 py-2 bg-white shadow-sm border rounded-lg border-gray-300 w-full cusInput">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="inline-flex flex-col space-y-16 items-start justify-center w-full">
                        <div class="flex flex-col space-y-6 items-start justify-start w-full">
                            <div class="inline-flex space-x-4 items-start justify-start w-full">
                                <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                                    <p class="text-sm font-medium leading-5 text-gray-700">Email</p>
                                    # let ee2 = (typeof(arr.emergency_email) != "undefined" && arr.emergency_email !==
                                    null) ? arr.emergency_email : ''; #
                                    <input type="email" required name="emergency_email" value="#= ee2 #"
                                        class="text-sm leading-5 text-gray-700 px-3 py-2 bg-white shadow-sm border rounded-lg border-gray-300 w-full cusInput">
                                </div>
                            </div>
                        </div>
                    </div>
                    <div class="inline-flex flex-col space-y-16 items-start justify-center w-full">
                        <div class="flex flex-col space-y-6 items-start justify-start w-full">
                            <div class="inline-flex space-x-4 items-start justify-start w-full">
                                <div class="inline-flex flex-col space-y-1 items-start justify-start w-full">
                                    <p class="text-sm font-medium leading-5 text-gray-700">Address</p>
                                    # let ea2 = (typeof(arr.emergency_address) != "undefined" && arr.emergency_address
                                    !== null) ? arr.emergency_address : '';#
                                    <input type="text" required name="emergency_address" value="#= ea2 #"
                                        class="text-sm leading-5 text-gray-700 px-3 py-2 bg-white shadow-sm border rounded-lg border-gray-300 w-full cusInput">
                                </div>
                            </div>
                        </div>
                    </div>
                </div>
                <div class="w-full px-6 py-3 bg-gray-50 rounded-b-lg border-t border-gray-200">
                    <div class="flex flex-col items-end justify-center w-full">
                        <div class="inline-flex space-x-4 items-center justify-end">
                            <button type="button" class="backToEmergencyDetails btn-secondary px-4 py-2">
                                <p class="text-sm font-medium leading-5 text-gray-700">Cancel</p>
                            </button>
                            <button type="button" class="saveEmergencyDetails btn-primary px-4 py-2">
                                <p class="text-sm font-medium leading-5 text-white">Save</p>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</script>
<script id="surveyDetailsTemplate" type="text/html">
    <div id="8" class="w-full holder">
        <div class="bg-white shadow rounded-lg w-full border border-gray-200 surveyDetails">
            <div class="w-full px-6 py-4 border-b">
                <div class="flex flex-col space-y-4 items-start justify-start w-full ">
                    <div class="inline-flex space-x-2 items-center justify-between w-full">
                        <div class="inline-flex space-x-2 items-center justify-start">
                            <p class="text-lg font-medium leading-6 text-gray-900">Survey Contact Status</p>
                        </div>
                        <a href="javascript:;" class="editSurveyDetailsBtn btn-tertiary h-8">
                            <img src="{{ asset('v2/img/edit-pencil.svg') }}" class="" alt="" />
                            <p class="text-xs font-medium leading-4 text-primary-blue-500">Edit</p>
                        </a>
                    </div>
                </div>
            </div>
            <div class="flex flex-col items-start justify-center px-6 w-full">
                <div class="inline-flex items-center justify-start py-4 border-b w-full">
                    <p class="w-80 text-sm font-medium leading-5 text-gray-500">Survey Contact Status</p>
                    # let ecp = (typeof(arr.scs_value) != "undefined" && arr.scs_value !==
                    null) ? arr.scs_value : ''; #
                    <p class="text-sm leading-5 font-normal text-gray-900">#= ecp #</p>
                </div>

            </div>
        </div>
        <form id="surveyDetailsForm" name="surveyDetailsForm">
            <div class="bg-white shadow rounded-lg w-full border border-gray-200 hidden editSurveyDetails">
                <div class="w-full px-6 py-4 border-b">
                    <div class="inline-flex space-x-1 items-center justify-start w-full">
                        <p class="text-lg font-medium leading-7 text-gray-900">Survey Contact Status</p>
                    </div>
                </div>
                <div class="flex flex-col space-y-4 items-start justify-start p-6 w-full">

                            #if (data.surveyContactData.data && data.surveyContactData.data.length > 0) {#
                                # data.surveyContactData.data.forEach(function(item) { #
                                    <div class="flex space-x-2 items-center justify-start">
                                        <div class="form-check">
                                            <input
                                                class="form-check-input appearance-none rounded-full h-4 w-4 border border-gray-300 bg-white checked:bg-blue-600 checked:border-blue-600 focus:outline-none transition duration-200 mt-1 align-top bg-no-repeat bg-center bg-contain float-left mr-2 cursor-pointer"
                                                type="radio"
                                                name="scs"
                                                value="#= item.Id #"
                                                id="survey_contact_status_#= item.Id #"
                                                # if (item.Id === arr.scs) { # checked # } #
                                            >
                                            <label for="survey_contact_status_#= item.Id #"
                                                class="text-sm font-normal leading-5 text-gray-700 cursor-pointer">#= item.Name #</label>
                                        </div>
                                    </div>
                                # }); #
                            # } #
                </div>
                <div class="w-full px-6 py-3 bg-gray-50 rounded-b-lg border-t border-gray-200">
                    <div class="flex flex-col items-end justify-center w-full">
                        <div class="inline-flex space-x-4 items-center justify-end">
                            <button type="button" class="backToSurveyDetails btn-secondary px-4 py-2">
                                <p class="text-sm font-medium leading-5 text-gray-700">Cancel</p>
                            </button>
                            <button type="button" class="saveSurveyDetails btn-primary px-4 py-2">
                                <p class="text-sm font-medium leading-5 text-white">Save</p>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</script>
<script id="usiDetailsTemplate" type="text/html">
    <div id="9" class="w-full holder">
        <div class="bg-white shadow rounded-lg w-full border border-gray-200 usiDetails">
            <div class="w-full px-6 py-4 border-b">
                <div class="flex flex-col space-y-4 items-start justify-start w-full ">
                    <div class="inline-flex space-x-2 items-center justify-between w-full">
                        <div class="inline-flex space-x-2 items-center justify-start">
                            <p class="text-lg font-medium leading-6 text-gray-900">USI: Unique Student Identifier</p>
                        </div>
                        <a href="javascript:;" class="editUsiDetailsBtn btn-tertiary h-8">
                            <img src="{{ asset('v2/img/edit-pencil.svg') }}" class="" alt="" />
                            <p class="text-xs font-medium leading-4 text-primary-blue-500">Edit</p>
                        </a>
                    </div>
                </div>
            </div>
            <div class="p-6 w-full">
                <p class="text-sm leading-5 text-gray-500">You may already have a USI if you have done any nationally
                    recognised training, which could include training at work, completing a first aid course or RSA
                    (Responsible Service of Alcohol) course, getting a white card, or studying at a TAFE or training
                    organisation. It is important that you try to find out whether you already have a USI before
                    attempting to create a new one. You should not have more than one USI. To check if you already have
                    a USI, use the ‘Forgotten USI’ link on the USI website at <a
                        class="text-xs font-medium leading-4 text-primary-blue-500"
                        href="https://www.usi.gov.au/faqs/i-have-forgotten-my-usi/"
                        target="_blank">https://www.usi.gov.au/faqs/i-have-forgotten-my-usi/.</a></p>
                {{-- <div class="inline-flex items-center justify-start pt-3 pb-4 border-b-2 w-full">
                    <p class="w-80 text-sm font-medium leading-5 text-gray-500">Do you already have a USI</p>
                    # let iau = (typeof(arr.is_apply_usi) != "undefined" && arr.is_apply_usi !== null) ?
                    arr.is_apply_usi : ''; #
                    <p class="text-sm leading-5 font-normal text-gray-900">#= iau #</p>
                </div> --}}
                <div class="inline-flex items-center justify-start pt-4 w-full">
                    <p class="w-80 text-sm font-medium leading-5 text-gray-500">USI Number</p>
                    # let USI = (typeof(arr.USI) != "undefined" && arr.USI !== null) ? arr.USI : ''; #
                    <p class="text-sm leading-5 font-normal text-gray-900">#= USI #</p>
                    # isVerify = (arr.is_usi_verified == 1) ? '':'hidden' #
                    # notVerify = (arr.is_usi_verified == 1) ? 'hidden':'' #
                    <div
                        class="#= isVerify # ml-1 tw-badge inline-flex items-center justify-center px-3 py-1 w-fit text-xs border-none leading-normal bg-green-100 text-green-800 rounded-md gap-1 verifyUsi">
                        <span class="text-green-500">
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M11.3349 2.06544L11.4114 2.21801L11.9878 3.75099C12.0331 3.87171 12.1284 3.96697 12.2491 4.01226L13.7292 4.56753C14.3787 4.81121 14.7277 5.50524 14.5546 6.1619L14.513 6.29259L13.8244 7.81541C13.7711 7.9328 13.7711 8.06751 13.8244 8.1849L14.4783 9.62411C14.7653 10.2557 14.5213 10.9932 13.9346 11.3352L13.7821 11.4116L12.2491 11.9881C12.1284 12.0333 12.0331 12.1286 11.9878 12.2493L11.4325 13.7294C11.1889 14.3789 10.4948 14.7279 9.83817 14.5549L9.70748 14.5132L8.18466 13.8246C8.06727 13.7713 7.93256 13.7713 7.81517 13.8246L6.37596 14.4786C5.74439 14.7655 5.00683 14.5216 4.66489 13.9349L4.58847 13.7823L4.01202 12.2493C3.96672 12.1286 3.87147 12.0333 3.75075 11.9881L2.27067 11.4328C1.62117 11.1891 1.27215 10.4951 1.44522 9.83842L1.48686 9.70773L2.17543 8.1849C2.22877 8.06751 2.22877 7.9328 2.17543 7.81541L1.5215 6.3762C1.23453 5.74463 1.47849 5.00708 2.0652 4.66513L2.21776 4.58871L3.75075 4.01226C3.87147 3.96697 3.96672 3.87171 4.01202 3.75099L4.56729 2.27092C4.81096 1.62141 5.505 1.27239 6.16165 1.44546L6.29234 1.48711L7.81517 2.17567C7.93256 2.22901 8.06727 2.22901 8.18466 2.17567L9.62387 1.52174C10.2554 1.23477 10.993 1.47874 11.3349 2.06544ZM10.1638 5.70607L6.97816 9.34681L5.81571 8.18436C5.6413 8.00995 5.35853 8.00995 5.18412 8.18436C5.00971 8.35877 5.00971 8.64154 5.18412 8.81595L6.68412 10.316C6.86679 10.4986 7.16589 10.4887 7.33602 10.2942L10.836 6.29425C10.9984 6.10862 10.9796 5.82648 10.794 5.66405C10.6084 5.50163 10.3262 5.52044 10.1638 5.70607Z"
                                    fill="currentColor"></path>
                            </svg>
                        </span>
                        <span class="text-green-800">Verified</span>
                    </div>
                    <div
                        class="#= notVerify # ml-1 tw-badge inline-flex items-center justify-center px-3 py-1 w-fit text-xs border-none leading-normal bg-red-100 text-red-800 rounded-md gap-1 notVerifyUsi">
                        <span class="text-red-500">
                            <svg width="16" height="16" viewBox="0 0 16 16" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M8 2C11.3137 2 14 4.68629 14 8C14 11.3137 11.3137 14 8 14C4.68629 14 2 11.3137 2 8C2 4.68629 4.68629 2 8 2ZM6.53431 5.83859C6.33944 5.7036 6.07001 5.72288 5.89645 5.89645L5.83859 5.96569C5.7036 6.16056 5.72288 6.42999 5.89645 6.60355L7.293 8L5.89645 9.39645L5.83859 9.46569C5.7036 9.66056 5.72288 9.92999 5.89645 10.1036L5.96569 10.1614C6.16056 10.2964 6.42999 10.2771 6.60355 10.1036L8 8.707L9.39645 10.1036L9.46569 10.1614C9.66056 10.2964 9.92999 10.2771 10.1036 10.1036L10.1614 10.0343C10.2964 9.83944 10.2771 9.57001 10.1036 9.39645L8.707 8L10.1036 6.60355L10.1614 6.53431C10.2964 6.33944 10.2771 6.07001 10.1036 5.89645L10.0343 5.83859C9.83944 5.7036 9.57001 5.72288 9.39645 5.89645L8 7.293L6.60355 5.89645L6.53431 5.83859Z"
                                    fill="currentColor"></path>
                            </svg>
                        </span>
                        <span class="text-red-800">Not Verified</span>
                    </div>

                </div>
                #if(arr.usi_invalid_reason){#
                <div class="mt-4 felx w-full #= notVerify #">
                    <div
                        class="tw-highlightbox border flex items-center p-4 gap-3 border-red-500 bg-red-50 text-red-500 rounded-md">
                        <span class="tw-highlightbox__icon">
                            <svg width="20" height="20" viewBox="0 0 20 20" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                    d="M10 18C14.4183 18 18 14.4183 18 10C18 5.58172 14.4183 2 10 2C5.58172 2 2 5.58172 2 10C2 14.4183 5.58172 18 10 18ZM8.70711 7.29289C8.31658 6.90237 7.68342 6.90237 7.29289 7.29289C6.90237 7.68342 6.90237 8.31658 7.29289 8.70711L8.58579 10L7.29289 11.2929C6.90237 11.6834 6.90237 12.3166 7.29289 12.7071C7.68342 13.0976 8.31658 13.0976 8.70711 12.7071L10 11.4142L11.2929 12.7071C11.6834 13.0976 12.3166 13.0976 12.7071 12.7071C13.0976 12.3166 13.0976 11.6834 12.7071 11.2929L11.4142 10L12.7071 8.70711C13.0976 8.31658 13.0976 7.68342 12.7071 7.29289C12.3166 6.90237 11.6834 6.90237 11.2929 7.29289L10 8.58579L8.70711 7.29289Z"
                                    fill="currentColor"></path>
                            </svg>
                        </span>
                        <div class="notVerifyUsiReason">#= arr.usi_invalid_reason #</div>
                    </div>
                </div>
                #}else{#
                <div class="mt-4 felx w-full #= notVerify #">
                    <div
                        class="tw-highlightbox border flex items-center p-4 gap-3 border-red-500 bg-red-50 text-red-500 rounded-md">
                        <span class="tw-highlightbox__icon">
                            <svg width="20" height="20" viewBox="0 0 20 20" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path fill-rule="evenodd" clip-rule="evenodd"
                                    d="M10 18C14.4183 18 18 14.4183 18 10C18 5.58172 14.4183 2 10 2C5.58172 2 2 5.58172 2 10C2 14.4183 5.58172 18 10 18ZM8.70711 7.29289C8.31658 6.90237 7.68342 6.90237 7.29289 7.29289C6.90237 7.68342 6.90237 8.31658 7.29289 8.70711L8.58579 10L7.29289 11.2929C6.90237 11.6834 6.90237 12.3166 7.29289 12.7071C7.68342 13.0976 8.31658 13.0976 8.70711 12.7071L10 11.4142L11.2929 12.7071C11.6834 13.0976 12.3166 13.0976 12.7071 12.7071C13.0976 12.3166 13.0976 11.6834 12.7071 11.2929L11.4142 10L12.7071 8.70711C13.0976 8.31658 13.0976 7.68342 12.7071 7.29289C12.3166 6.90237 11.6834 6.90237 11.2929 7.29289L10 8.58579L8.70711 7.29289Z"
                                    fill="currentColor"></path>
                            </svg>
                        </span>
                        <div class="notVerifyUsiReason">USI is invalid</div>
                    </div>
                </div>
                #}#
            </div>
        </div>
        <form id="usiDetailsForm" name="usiDetailsForm">
            <div class="bg-white shadow rounded-lg w-full border border-gray-200 hidden editUsiDetails">
                <div class="w-full px-6 py-4 border-b">
                    <div class="inline-flex space-x-1 items-center justify-start w-full">
                        <p class="text-lg font-medium leading-7 text-gray-900">USI: Unique Student Identifier</p>
                    </div>
                </div>
                <div class="space-y-4 p-6 w-full">
                    <p class="text-sm leading-5 text-gray-500">You may already have a USI if you have done any
                        nationally recognised training, which could include training at work, completing a first aid
                        course or RSA (Responsible Service of Alcohol) course, getting a white card, or studying at a
                        TAFE or training organisation. It is important that you try to find out whether you already have
                        a USI before attempting to create a new one. You should not have more than one USI. To check if
                        you already have a USI, use the ‘Forgotten USI’ link on the USI website at <a
                            class="text-xs font-medium leading-4 text-primary-blue-500"
                            href="https://www.usi.gov.au/faqs/i-have-forgotten-my-usi/"
                            target="_blank">https://www.usi.gov.au/faqs/i-have-forgotten-my-usi/.</a></p>
                    <div class="w-full space-y-4">
                        <div class="flex space-x-4 items-start justify-start w-full">
                            <div  id="first_name_wrapper" class="flex flex-col space-y-1 items-start justify-start flex-1">
                                <p class="text-sm font-medium leading-5 text-gray-700 firstNameInputLabel">First Name</p>
                                # let fNameText = (typeof(arr.first_name) != "undefined" && arr.first_name !== null) ?
                                arr.first_name : ''; #

                                  <label id="firstNameLabel" >#= fNameText #</label>
                                <input hidden required type="text"  onkeydown="return /[a-z]/i.test(event.key)"
                                    name="first_name" value="#= fNameText #"
                                    class="text-sm leading-5 text-gray-700 px-3 py-2 bg-white shadow-sm border rounded-lg border-gray-300 w-full cusInput"
                                    id="firstname">
                            </div>
                            <div id="family_name_wrapper" class="flex flex-col space-y-1 items-start justify-start flex-1">
                                <p class="text-sm font-medium leading-5 text-gray-700">Last Name</p>
                                # let lNameText = (typeof(arr.family_name) != "undefined" && arr.family_name !== null) ?
                                arr.family_name : ''; #
                                  <label id="lastNameLabel" >#= lNameText #</label>
                                <input hidden type="text" name="family_name" value="#= lNameText #"
                                    class="onlyLettersAllowed text-sm leading-5 text-gray-700 px-3 py-2 bg-white shadow-sm border rounded-lg border-gray-300 w-full cusInput"
                                    id="lastname">
                            </div>

                            <div class="flex flex-col space-y-1 items-start justify-start flex-1">
                                <p class="text-sm font-medium leading-5 text-gray-700">Date of birth</p>
                                # let dob = (typeof(arr.DOB) != "undefined" && arr.DOB !== null) ? arr.DOB : '';
                                    if(!dob){
                                        dob = (typeof(arr.dateOfBirth) != "undefined" && arr.dateOfBirth !== null) ? arr.dateOfBirth : ''
                                    }
                                #

                                # console.log(dob) #
                                # console.log(arr) #
                                <label id="dobLabel" >#= dob #</label>
                                <input hidden type="text" id="dob" readonly name="dateOfBirth" value="#= dob #">
                            </div>
                        </div>
                        <div class="flex space-x-4 items-start justify-start w-full">
                            <div class="inline-flex flex-col space-y-1 items-start justify-start flex-1">
                                <p class="text-sm font-medium leading-5 text-gray-700">USI Number</p>
                                # let USI2 = (typeof(arr.USI) != "undefined" && arr.USI !== null) ? arr.USI : ''; #
                                <input required type="text" name="USI" value="#= USI2 #"
                                    class="text-sm leading-5 text-gray-700 px-3 py-2 bg-white shadow-sm border rounded-lg border-gray-300 w-full cusInput">
                            </div>
                        </div>
                        <div class="flex space-x-4 items-start justify-start w-full">
                            <div class="flex items-start justify-start flex-1">
                                # let dsob = (arr.family_name != '') ? 'checked' : ''; #
                                <input type="checkbox" # dsob # name="isSingleName" value="yes"
                                    class="text-sm leading-5 text-gray-700 px-3 py-2 bg-white shadow-sm border rounded-lg border-gray-300 date_field"
                                    id="singlename"
                                >
                                <p class="ml-2 text-sm font-medium leading-5 text-gray-700" for="singlename">Single Name
                                </p>
                            </div>
                        </div>
                        <button class="verifyUSINumber btn-primary px-4 py-2 mt-6">
                            <p class="text-sm font-medium leading-5 text-white">Verify</p>
                        </button>
                    </div>
                    <div id="responceTable" class="w-full tw-default-table">
                    </div>
                </div>
                <div class="w-full px-6 py-3 bg-gray-50 rounded-b-lg border-t border-gray-200">
                    <div class="flex flex-col items-end justify-center w-full">
                        <div class="inline-flex space-x-4 items-center justify-end">
                            <button class="backToUsiDetails btn-secondary px-4 py-2">
                                <p class="text-sm font-medium leading-5 text-gray-700">Cancel</p>
                            </button>
                            <button class="saveUsiDetails btn-primary px-4 py-2">
                                <p class="text-sm font-medium leading-5 text-white">Save</p>
                            </button>
                        </div>
                    </div>
                </div>
            </div>
        </form>
    </div>
</script>
<script id="studentUsiTemplate" type="text/html">
    <div class="px-4 py-3 border border-primary-blue-500 rounded-md max-w-[500px]">
        <x-v2.label-value class:value="font-normal" class:label="font-normal"
            class="grid-cols-[200px_1fr] items-center py-1.5 shadow-none" label="USI Status">
            <span class="text-gray-7000">#: result #</span>
            # if(variant == 'success') { #
            <svg
                    class="w-4 h-4 ml-1 fill-green-500"
                    viewBox="0 0 512 512"
            ><path d="M434.7 82.7 480 128 192 416 32 256l45.3-45.3L192 325.5z"></path>
            </svg>
            # } else { #
            <svg
                    class="w-4 h-4 ml-1 fill-red-500"
                    viewBox="0 0 512 512"><path d="M416 141.3 301.3 256 416 370.7 370.7 416 256 301.3 141.3 416 96 370.7 210.7 256 96 141.3 141.3 96 256 210.7 370.7 96z"></path>
            </svg>
            # } #
{{--            # if(variant == 'success') { #--}}
{{--            <x-v2.badge variant="success" dark="true"><span>#: result #</span></x-v2.badge>--}}
{{--            # } else { #--}}
{{--            <x-v2.badge variant="error" dark="true"><span>#: result #</span></x-v2.badge>--}}
{{--            # } #--}}
            {{--<x-v2.badge variant="#: variant #" dark="true"><span>#: result #</span></x-v2.badge>--}}
        </x-v2.label-value>
        # if(firstName) { #
        <x-v2.label-value class:value="font-normal" class:label="font-normal"
            class="grid-cols-[200px_1fr] items-center py-1.5 shadow-none rounded-md" label="First Name">
            <span class="text-gray-7000">#: showNoMatch(firstName) #</span>
            # if(showNoMatch(firstName) == 'Match') { #
                <svg
                        class="w-4 h-4 ml-1 fill-green-500"
                        viewBox="0 0 512 512"
                ><path d="M434.7 82.7 480 128 192 416 32 256l45.3-45.3L192 325.5z"></path>
            </svg>
            # } else if(showNoMatch(firstName) == 'No Match') { #
                <svg
                        class="w-4 h-4 ml-1 fill-red-500"
                        viewBox="0 0 512 512"><path d="M416 141.3 301.3 256 416 370.7 370.7 416 256 301.3 141.3 416 96 370.7 210.7 256 96 141.3 141.3 96 256 210.7 370.7 96z"></path>
                </svg>
            # } #
        </x-v2.label-value>
        # } #
        # if(familyName) { #
        <x-v2.label-value class:value="font-normal" class:label="font-normal"
            class="grid-cols-[200px_1fr] items-center py-1.5 shadow-none" label="Family Name">
            <span class="text-gray-7000">#: showNoMatch(familyName) #</span>
            # if(showNoMatch(familyName) == 'Match') { #
            <svg
                    class="w-4 h-4 ml-1 fill-green-500"
                    viewBox="0 0 512 512"
            ><path d="M434.7 82.7 480 128 192 416 32 256l45.3-45.3L192 325.5z"></path>
            </svg>
            # } else if(showNoMatch(familyName) == 'No Match') { #
            <svg
                    class="w-4 h-4 ml-1 fill-red-500"
                    viewBox="0 0 512 512"><path d="M416 141.3 301.3 256 416 370.7 370.7 416 256 301.3 141.3 416 96 370.7 210.7 256 96 141.3 141.3 96 256 210.7 370.7 96z"></path>
            </svg>
            # } #
        </x-v2.label-value>
        # } #
        # if(singleName) { #
        <x-v2.label-value class:value="font-normal" class:label="font-normal"
            class="grid-cols-[200px_1fr] items-center py-1.5 shadow-none rounded-md" label="Single Name">
            <span class="text-gray-7000">#: showNoMatch(singleName) #</span>
            # if(showNoMatch(singleName) == 'Match') { #
            <svg
                    class="w-4 h-4 ml-1 fill-green-500"
                    viewBox="0 0 512 512"
            ><path d="M434.7 82.7 480 128 192 416 32 256l45.3-45.3L192 325.5z"></path>
            </svg>
            # } else if(showNoMatch(singleName) == 'No Match') { #
            <svg
                    class="w-4 h-4 ml-1 fill-red-500"
                    viewBox="0 0 512 512"><path d="M416 141.3 301.3 256 416 370.7 370.7 416 256 301.3 141.3 416 96 370.7 210.7 256 96 141.3 141.3 96 256 210.7 370.7 96z"></path>
            </svg>
            # } #
        </x-v2.label-value>
        # } #
        # if(firstName||singleName ) { #
        <x-v2.label-value class:value="font-normal" class:label="font-normal"
            class="grid-cols-[200px_1fr] items-center py-1.5 shadow-none" label="Date Of Birth">
            <span class="text-gray-7000">#: showNoMatch(dob) #</span>
            # if(showNoMatch(dob) == 'Match') { #
            <svg
                    class="w-4 h-4 ml-1 fill-green-500"
                    viewBox="0 0 512 512"
            ><path d="M434.7 82.7 480 128 192 416 32 256l45.3-45.3L192 325.5z"></path>
            </svg>
            # } else if(showNoMatch(dob) == 'No Match') { #
            <svg
                    class="w-4 h-4 ml-1 fill-red-500"
                    viewBox="0 0 512 512"><path d="M416 141.3 301.3 256 416 370.7 370.7 416 256 301.3 141.3 416 96 370.7 210.7 256 96 141.3 141.3 96 256 210.7 370.7 96z"></path>
            </svg>
            # } #
        </x-v2.label-value>
        # } #
    </div>
</script>