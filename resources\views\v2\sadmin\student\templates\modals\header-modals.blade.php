<div id="addCOENumberModal" style="display:none;">
    <form id="addCOENumberForm" method="POST" accept-charset="UTF-8" enctype="multipart/form-data">
        <div class="flex flex-col space-y-4 p-6">
            <div class="flex flex-col space-y-1 items-start justify-start flex-1">
                <p class="text-sm font-medium leading-5 text-gray-700">Enter Code No</p>
                <input type="text" name="code_no"
                    class="text-sm leading-5 text-gray-700 px-3 py-2 bg-white shadow-sm border rounded-lg border-gray-300 w-full cusInput"
                    id="code_no">
            </div>
            <div class="inline-flex items-center justify-start py-2 bg-gray-50 space-x-2 w-full border-b">
                <input class="" id="COEDocument" name="COEDocument" type="file" hidden>
                <label for="COEDocument"
                    class="flex space-x-2 items-center justify-center py-2 px-3 bg-white border rounded-lg border-gray-300 cursor-pointer">
                    <img src="{{ asset('v2/img/activity-attechment.svg') }}" class="" alt="searchIcon" />
                    <span class='block text-xs font-medium leading-4 text-gray-700 attachFileCOEDocument'>Attach
                        Files</span>
                </label>
                <div id="downLoadCoeFile"> </div>
            </div>
            <div class="flex flex-col space-y-2 items-start justify-start w-full">
                <p class="text-sm font-medium leading-5 text-gray-700">If Applicable ?</p>
                <div class="form-check">
                    <input id="is_applicable"
                        class="form-check-input applicable rounded-full h-4 w-4 border border-gray-300 bg-white checked:bg-blue-600 checked:border-blue-600 focus:outline-none transition duration-200 mt-1 align-top bg-no-repeat bg-center bg-contain float-left mr-2 cursor-pointer"
                        type="checkbox" value="" name="is_applicable">
                    <label for="male" class="text-sm font-medium leading-5 text-gray-700 cursor-pointer"></label>
                </div>
            </div>
        </div>
    </form>
    <div class="inline-flex space-x-4 items-center justify-between py-4 px-6 bg-gray-50 w-full border-t">
        <div></div>
        <div class="flex space-x-4 items-center justify-end">
            <button type="button"
                class="cancelBtn flex justify-center px-6 py-2 bg-white shadow-sm border hover:shadow-md rounded-lg  border-gray-300 focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-gray-400">
                <p class="text-sm font-medium leading-4 text-gray-700">CANCEL</p>
            </button>
            <button type="button"
                class="saveCOENumberBtn flex justify-center h-full px-6 py-2 bg-primary-blue-500 hover:shadow-md rounded-lg focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-primary-blue-600">
                <p class="text-sm font-medium leading-4 text-white ">SAVE</p>
            </button>
        </div>
    </div>
</div>

<div id="studentTrainingPlanModal" style="display: none;">
    <div class="inline-flex flex-col p-6 items-start space-y-6 justify-start bg-gray-100 h-full w-full">
        <div class="studentCourseDetailHeader w-full">

        </div>
        <div
            class="flex flex-col items-start justify-start p-6 bg-white shadow-sm border rounded-lg border-gray-200 w-full noDataAndAddTrainingPlan">
            <div class="flex flex-col space-y-4 items-start justify-start w-full">
                <div class="inline-flex space-x-1 items-center justify-start">
                    <p class="text-lg font-medium leading-6 text-gray-700"> Training Plan</p>
                    <img src="{{ asset('v2/img/question.svg') }}" class="" alt="Help" />
                </div>
                <div class="flex flex-col space-y-6 items-center justify-center py-8 bg-gray-50 rounded-lg w-full">
                    <div class="flex flex-col space-y-6 items-center justify-start">
                        <img src="{{ asset('v2/img/training_plan.svg') }}" class="" alt="" />
                        <p class="text-sm leading-5 font-normal text-gray-700">You have not added training plan.</p>
                    </div>
                    <div class="inline-flex space-x-2 items-start justify-center">
                        <button
                            class="addTrainingPlanBtn flex space-x-2 items-center justify-center py-2 pl-2.5 pr-3 bg-primary-blue-500 rounded-lg hover:shadow-md focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-primary-blue-600">
                            <img src="{{ asset('v2/img/plus-white.svg') }}" class="" alt="" />
                            <p class="text-sm font-medium leading-4 text-white uppercase">Add Training Plan</p>
                        </button>
                    </div>
                </div>
            </div>
        </div>
        <div
            class="flex flex-col items-start justify-start p-6 bg-white border rounded-lg border-gray-200 w-full h-full trainingPlanListDiv">
            <div class="flex flex-col space-y-4 items-start justify-start w-full h-full">
                <div class="flex flex-col space-y-4 items-start justify-start w-full ">
                    <div class="inline-flex space-x-4 items-center justify-between w-full">
                        <p class="text-lg font-medium leading-6 text-gray-900"> Training Plan</p>
                        <button
                            class="addTrainingPlanBtn flex space-x-2 items-center justify-center py-2 pl-2.5 pr-3 bg-primary-blue-500 rounded-lg hover:shadow-md focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-primary-blue-600">
                            <img src="{{ asset('v2/img/plus-white.svg') }}" class="" alt="" />
                            <p class="text-sm font-medium leading-4 text-white uppercase">Add New Training Plan</p>
                        </button>
                    </div>
                </div>
                <div id="trainingPlanList" class="h-full"></div>
            </div>
        </div>
    </div>
</div>

<div id="viewCOEHistoryModal" style="display:none;">
    <div id="viewCOEHistoryList" class="tw-table tw-table__borderless tw-table__header--borderless mb-4"></div>
</div>

<div id="studentDocumentListModal" style="display:none;">
    <div class="inline-flex flex-col space-y-6 items-center justify-start bg-gray-100 w-full p-6 h-full">
        <div class="flex flex-row w-full  bg-white border rounded-lg border-gray-200">
            <div
                class="flex flex-col space-y-2 items-start justify-start w-72 bg-white border-l-0 border-t-0 border-b-0 border h-full">
                <div id="studentDocumentListStepper" class="w-full studentDocumentListStepper"></div>
            </div>
            <div class="flex flex-col w-full">
                <div id="studentDocumentListStepperWizard"></div>
                <div class="p-5 showupLoadedStudentDocument" style="display: none;">
                    <div id="uploadedStudentDocumentList" class="">

                    </div>
                </div>
            </div>
        </div>
    </div>
</div>

<div id="currentCourseHistoryModal" style="display: none;">
    <div id="currentCourseHistoryDiv"></div>
</div>

<div id="previewDocumentModal" style="display:none;"></div>

<div id="loadingDialog" style="display:none;">
    <div class="flex flex-col items-center justify-center gap-6">
        <div class="flex items-center gap-4">
            <x-v2.loading />
            <div class="text-base font-medium">1/1 genrated</div>
        </div>
        <div class="cancel-generate-pdf btn-secondary cursor-pointer">
            <span class="text-gray-700">Cancel Operation</span>
        </div>
    </div>
</div>
<div id="alreadyDownloaded">
</div>
<div id="unitConfirmationDiv">
</div>
<div id="certificateRequirementsModal">
</div>
<div id="certificateOverrideConfirmModal">
</div>
<div id="certificateRegisterModal" style="display: none;">
    <div
        class="inline-flex flex-col items-start justify-start py-6 px-4 bg-white border rounded-lg border-gray-200 w-full">
        <div class="flex flex-col space-y-4 items-start justify-start w-full">
            <div class="inline-flex space-x-4 items-center justify-start w-full">
                <p class="text-lg font-medium leading-6 text-gray-900 w-96">Student Certificate Issue</p>
                <div class="inline-flex space-x-4 items-center justify-end  w-full">
                    <button type="button"
                        class="addCertificateBtn flex space-x-2 items-center justify-center py-2 pl-2.5 pr-3 bg-primary-blue-500 rounded-lg hover:shadow-md focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-primary-blue-600">
                        <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M6.00039 0.400391C6.44222 0.400391 6.80039 0.758563 6.80039 1.20039V5.20039H10.8004C11.2422 5.20039 11.6004 5.55856 11.6004 6.00039C11.6004 6.44222 11.2422 6.80039 10.8004 6.80039H6.80039V10.8004C6.80039 11.2422 6.44222 11.6004 6.00039 11.6004C5.55856 11.6004 5.20039 11.2422 5.20039 10.8004V6.80039H1.20039C0.758563 6.80039 0.400391 6.44222 0.400391 6.00039C0.400391 5.55856 0.758563 5.20039 1.20039 5.20039H5.20039V1.20039C5.20039 0.758563 5.55856 0.400391 6.00039 0.400391Z"
                                fill="white" />
                        </svg>
                        <p class="text-sm font-medium leading-4 text-white">Add Certificate</p>
                    </button>
                    <button type="button"
                        class="generateCertificateBtn flex space-x-2 items-center justify-center py-2 pl-2.5 pr-3 bg-primary-blue-500 rounded-lg hover:shadow-md focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-primary-blue-600">
                        <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M6.00039 0.400391C6.44222 0.400391 6.80039 0.758563 6.80039 1.20039V5.20039H10.8004C11.2422 5.20039 11.6004 5.55856 11.6004 6.00039C11.6004 6.44222 11.2422 6.80039 10.8004 6.80039H6.80039V10.8004C6.80039 11.2422 6.44222 11.6004 6.00039 11.6004C5.55856 11.6004 5.20039 11.2422 5.20039 10.8004V6.80039H1.20039C0.758563 6.80039 0.400391 6.44222 0.400391 6.00039C0.400391 5.55856 0.758563 5.20039 1.20039 5.20039H5.20039V1.20039C5.20039 0.758563 5.55856 0.400391 6.00039 0.400391Z"
                                fill="white" />
                        </svg>
                        <p class="text-sm font-medium leading-4 text-white">Generate Certificate</p>
                    </button>
                    <button type="button"
                        class="generateCertificateBetaBtn flex space-x-2 items-center justify-center py-2 pl-2.5 pr-3 bg-primary-blue-500 rounded-lg hover:shadow-md focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-primary-blue-600">
                        <svg width="12" height="12" viewBox="0 0 12 12" fill="none" xmlns="http://www.w3.org/2000/svg">
                            <path fill-rule="evenodd" clip-rule="evenodd"
                                d="M6.00039 0.400391C6.44222 0.400391 6.80039 0.758563 6.80039 1.20039V5.20039H10.8004C11.2422 5.20039 11.6004 5.55856 11.6004 6.00039C11.6004 6.44222 11.2422 6.80039 10.8004 6.80039H6.80039V10.8004C6.80039 11.2422 6.44222 11.6004 6.00039 11.6004C5.55856 11.6004 5.20039 11.2422 5.20039 10.8004V6.80039H1.20039C0.758563 6.80039 0.400391 6.44222 0.400391 6.00039C0.400391 5.55856 0.758563 5.20039 1.20039 5.20039H5.20039V1.20039C5.20039 0.758563 5.55856 0.400391 6.00039 0.400391Z"
                                fill="white" />
                        </svg>
                        <p class="text-sm font-medium leading-4 text-white ">Generate Certificate Beta</p>
                    </button>
                </div>
            </div>

            <x-v2.grid-table id="studentCertificateIssueList" :rounded="true"></x-v2.grid-table>
        </div>
    </div>
</div>

<div id="addCertificateModal" style="display: none;">
    <form id="addCertificateForm" class="p-5"></form>
</div>

<div id="generateCertificateModal" style="display: none;">
    <div class="inline-flex p-6 items-start justify-start bg-gray-100 h-full w-full grid grid-cols-5 gap-6">
        <div
            class="inline-flex flex-col space-y-6 items-start justify-start pt-6 bg-white shadow-sm border rounded-lg border-gray-200 col-span-2 w-full">
            <div class="inline-flex space-x-4 items-center justify-start px-4 w-full">
                <div class="inline-flex flex-col space-y-2 items-start justify-start">
                    <p class="text-2xl font-bold leading-7 text-gray-900">Generate Certificate</p>
                </div>
            </div>
            <div class="flex flex-col items-start justify-start w-full min-h-[300px]">
                <div class="inline-flex space-x-4 items-center justify-start px-4 py-2 w-full">
                    <p class="text-sm leading-5 text-gray-500 w-40">Certificate type</p>
                    <div class=" w-full">
                        <input id="generate_certificate_list" name="generate_certificate_list" required />
                        <input id="subject_enrollment_id" name="subject_enrollment_id" type="hidden" />
                        <div class="err-msg text-red-500 text-[10px]" style="display: none">This field is required
                        </div>
                    </div>
                </div>
                <div class="inline-flex space-x-4 items-center justify-start px-4 py-2 w-full">
                    <p class="text-sm leading-5 text-gray-500 w-40">Completion Date</p>
                    <input type="text" name="completion_date" id="completion_date" class="w-full" />
                </div>
                <div class="inline-flex space-x-4 items-center justify-start px-4 py-2 w-full">
                    <p class="text-sm leading-5 text-gray-500 w-40">Issued Date</p>
                    <input type="text" name="issued_date" id="issued_date" class="w-full" />
                </div>
                <div class="flex flex-col items-end justify-center px-4 py-2 w-full">
                    <div class="inline-flex space-x-2 items-center justify-start w-full transcriptDiv hidden">
                        <div class="inline-flex flex-col items-start justify-start w-full">
                            <p class="w-full text-sm leading-tight text-gray-500">Include Failed Subject(s)/ Unit(s)/
                                Grade(s)</p>
                            <p class="text-sm leading-5 text-gray-700 w-full switchText">No</p>
                        </div>
                        <div class="flex items-center justify-end customSwitchButton">
                            <input type="checkbox" name="include_failed_subject" value="Y"
                                id="include_failed_subject" />
                        </div>
                    </div>
                    <div class="inline-flex space-x-2 items-center justify-start w-full certificateDiv hidden">
                        <div class="inline-flex flex-col items-start justify-start w-full">
                            <p class="w-full text-sm leading-tight text-gray-500">Convert Student Course Status
                                Completed ?</p>
                            <p class="text-sm leading-5 text-gray-700 w-full switchText">No</p>
                        </div>
                        <div class="flex items-center justify-end customSwitchButton">
                            <input type="checkbox" name="convert_course_status" id="convert_course_status" />
                        </div>
                    </div>
                </div>
            </div>
            <div
                class="inline-flex space-x-4 items-center justify-end px-4 py-4 bg-gray-50 border-t w-full rounded-b-lg">
                <div class="flex gap-2 items-center justify-end flex-wrap">
                    <button type="button"
                        class="cancelBtn flex items-center justify-center w-20 px-3 py-2 bg-white shadow border rounded-lg border-gray-300 hover:shadow-md border-gray-300 focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-gray-400">
                        <p class="text-sm font-medium leading-4 text-gray-700">Cancel</p>
                    </button>
                    <button type="button"
                        class="downloadEnrollSubjectsCertificatePdf flex space-x-2 items-center justify-center py-2 pl-2.5 pr-3 bg-primary-blue-500 rounded-lg hover:shadow-md focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-primary-blue-600">
                        <p class="text-sm font-medium leading-4 text-white">Download Certificate</p>
                    </button>
                    <button type="button"
                        class="downloadECertificatePdf flex space-x-2 items-center justify-center py-2 pl-2.5 pr-3 bg-primary-blue-500 rounded-lg hover:shadow-md focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-primary-blue-600">
                        <p class="text-sm font-medium leading-4 text-white">Download E Certificate</p>
                    </button>
                    <button type="button"
                        class="downloadCertificateWithWaterMarkPdf flex space-x-2 items-center justify-center py-2 pl-2.5 pr-3 bg-primary-blue-500 rounded-lg hover:shadow-md focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-primary-blue-600">
                        <p class="text-sm font-medium leading-4 text-white">Download Certificate with Watermark</p>
                    </button>
                </div>
            </div>
        </div>
        <div
            class="flex flex-col items-center justify-center bg-gray-200 border rounded-lg border-gray-200 col-span-3 w-full h-full overflow-hidden">
            <div class="embed-content px-4 pb-4 w-full h-full overflow-auto" style="display: none; position: relative;">
                <div class="bg-gray-200 h-24 forceDiv text-xl py-3 text-center justify-center"
                    style="position: absolute;top: 0;left: 0;right: 0;z-index: 10; height:51px !important;">Preview
                </div>
                <embed class="w-full studentEnrollDocumentsPreview" type="text/html" width="500" height="100%">
            </div>
            <div
                class="enroll-certificate-preview-placeholder inline-flex flex-col space-y-2 items-center justify-start w-full">
                <svg width="28" height="34" viewBox="0 0 28 34" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M5.50039 17.425C5.50039 16.7208 6.07123 16.15 6.77539 16.15C7.47955 16.15 8.05039 16.7208 8.05039 17.425C8.05039 18.1292 7.47955 18.7 6.77539 18.7C6.07123 18.7 5.50039 18.1292 5.50039 17.425ZM6.77539 21.25C6.07123 21.25 5.50039 21.8208 5.50039 22.525C5.50039 23.2292 6.07123 23.8 6.77539 23.8C7.47955 23.8 8.05039 23.2292 8.05039 22.525C8.05039 21.8208 7.47955 21.25 6.77539 21.25ZM5.50039 27.625C5.50039 26.9208 6.07123 26.35 6.77539 26.35C7.47955 26.35 8.05039 26.9208 8.05039 27.625C8.05039 28.3292 7.47955 28.9 6.77539 28.9C6.07123 28.9 5.50039 28.3292 5.50039 27.625ZM11.8754 16.15C11.1712 16.15 10.6004 16.7208 10.6004 17.425C10.6004 18.1292 11.1712 18.7 11.8754 18.7H21.2254C21.9296 18.7 22.5004 18.1292 22.5004 17.425C22.5004 16.7208 21.9296 16.15 21.2254 16.15H11.8754ZM10.6004 22.525C10.6004 21.8208 11.1712 21.25 11.8754 21.25H21.2254C21.9296 21.25 22.5004 21.8208 22.5004 22.525C22.5004 23.2292 21.9296 23.8 21.2254 23.8H11.8754C11.1712 23.8 10.6004 23.2292 10.6004 22.525ZM11.8754 26.35C11.1712 26.35 10.6004 26.9208 10.6004 27.625C10.6004 28.3292 11.1712 28.9 11.8754 28.9H21.2254C21.9296 28.9 22.5004 28.3292 22.5004 27.625C22.5004 26.9208 21.9296 26.35 21.2254 26.35H11.8754ZM26.6042 10.9038L16.6949 0.9962C16.6695 0.970782 16.6407 0.948736 16.6121 0.926831C16.5909 0.910646 16.5699 0.894537 16.5504 0.8772C16.4297 0.7684 16.3107 0.6613 16.1781 0.5712C16.1351 0.542201 16.0881 0.519308 16.0413 0.496521C16.014 0.483241 15.9868 0.469996 15.9605 0.4556C15.9321 0.439609 15.9038 0.423243 15.8755 0.406854C15.7834 0.353549 15.6908 0.300008 15.5933 0.2584C15.2584 0.119 14.898 0.0493001 14.5325 0.0237999C14.499 0.0216619 14.4658 0.0171341 14.4325 0.0125959C14.3864 0.00630786 14.3402 0 14.2928 0H3.80039C1.92359 0 0.400391 1.5232 0.400391 3.4V30.6C0.400391 32.4768 1.92359 34 3.80039 34H24.2004C26.0772 34 27.6004 32.4768 27.6004 30.6V13.3076C27.6004 12.4066 27.2417 11.5413 26.6042 10.9038ZM25.0504 30.6C25.0504 31.0675 24.6696 31.45 24.2004 31.45H3.80039C3.33119 31.45 2.95039 31.0675 2.95039 30.6V3.4C2.95039 2.9325 3.33119 2.55 3.80039 2.55H14.0004V10.2C14.0004 12.0768 15.5236 13.6 17.4004 13.6H25.0504V30.6ZM16.5504 4.4557L23.143 11.05H17.4004C16.9312 11.05 16.5504 10.6675 16.5504 10.2V4.4557Z"
                        fill="#9CA3AF" />
                </svg>
                <p class="text-lg leading-5 text-gray-900">Letter will be Previewed here</p>
            </div>
        </div>
    </div>
</div>
<div id="generateCertificateBetaModal" style="display: none;">
    <div class="inline-flex p-6 items-start justify-start bg-gray-100 h-full w-full grid grid-cols-5 gap-6">
        <div
            class="inline-flex flex-col space-y-6 items-start justify-start pt-6 bg-white shadow-sm border rounded-lg border-gray-200 col-span-2 w-full">
            <div class="inline-flex space-x-4 items-center justify-start px-4 w-full">
                <div class="inline-flex flex-col space-y-2 items-start justify-start">
                    <p class="text-2xl font-bold leading-7 text-gray-900">Generate Certificate</p>
                </div>
            </div>
            <div class="flex flex-col items-start justify-start w-full min-h-[300px]">
                <div class="inline-flex space-x-4 items-center justify-start px-4 py-2 w-full">
                    <p class="text-sm leading-5 text-gray-500 w-40">Select Certificate</p>
                    <div class=" w-full">
                        <input id="generate_certificate_list_beta" name="generate_certificate_list_beta" required />
                        <input id="subject_enrollment_id" name="subject_enrollment_id" type="hidden" />
                        <div class="err-msg text-red-500 text-[10px]" style="display: none">This field is required
                        </div>
                    </div>
                </div>
            </div>
            <div
                class="inline-flex space-x-4 items-center justify-end px-4 py-4 bg-gray-50 border-t w-full rounded-b-lg">
                <div class="flex gap-2 items-center justify-end flex-wrap">
                    <button type="button"
                        class="cancelBtn flex items-center justify-center w-20 px-3 py-2 bg-white shadow border rounded-lg border-gray-300 hover:shadow-md border-gray-300 focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-gray-400">
                        <p class="text-sm font-medium leading-4 text-gray-700">Cancel</p>
                    </button>
                    <button type="button"
                        class="downloadEnrollSubjectsBetaCertificatePdf flex space-x-2 items-center justify-center py-2 pl-2.5 pr-3 bg-primary-blue-500 rounded-lg hover:shadow-md focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-primary-blue-600">
                        <p class="text-sm font-medium leading-4 text-white">Download Certificate</p>
                    </button>
                    <!-- <button type="button"
                        class="downloadECertificatePdf flex space-x-2 items-center justify-center py-2 pl-2.5 pr-3 bg-primary-blue-500 rounded-lg hover:shadow-md focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-primary-blue-600">
                        <p class="text-sm font-medium leading-4 text-white">Download E Certificate</p>
                    </button>
                    <button type="button"
                        class="downloadCertificateWithWaterMarkPdf flex space-x-2 items-center justify-center py-2 pl-2.5 pr-3 bg-primary-blue-500 rounded-lg hover:shadow-md focus:ring-2 focus:ring-offset-2 focus:ring-offset-white focus:ring-primary-blue-600">
                        <p class="text-sm font-medium leading-4 text-white">Download Certificate with Watermark</p>
                    </button> -->
                </div>
            </div>
        </div>
        <div
            class="flex flex-col items-center justify-center bg-gray-200 border rounded-lg border-gray-200 col-span-3 w-full h-full overflow-hidden">
            <div class="embed-content px-4 pb-4 w-full h-full overflow-auto" style="display: none; position: relative;">
                <div class="bg-gray-200 h-24 forceDiv text-xl py-3 text-center justify-center"
                    style="position: absolute;top: 0;left: 0;right: 0;z-index: 10; height:51px !important;">Preview
                </div>
                <embed class="w-full studentEnrollDocumentsPreview" type="text/html" width="500" height="100%">
            </div>
            <div
                class="enroll-certificate-preview-placeholder inline-flex flex-col space-y-2 items-center justify-start w-full">
                <svg width="28" height="34" viewBox="0 0 28 34" fill="none" xmlns="http://www.w3.org/2000/svg">
                    <path
                        d="M5.50039 17.425C5.50039 16.7208 6.07123 16.15 6.77539 16.15C7.47955 16.15 8.05039 16.7208 8.05039 17.425C8.05039 18.1292 7.47955 18.7 6.77539 18.7C6.07123 18.7 5.50039 18.1292 5.50039 17.425ZM6.77539 21.25C6.07123 21.25 5.50039 21.8208 5.50039 22.525C5.50039 23.2292 6.07123 23.8 6.77539 23.8C7.47955 23.8 8.05039 23.2292 8.05039 22.525C8.05039 21.8208 7.47955 21.25 6.77539 21.25ZM5.50039 27.625C5.50039 26.9208 6.07123 26.35 6.77539 26.35C7.47955 26.35 8.05039 26.9208 8.05039 27.625C8.05039 28.3292 7.47955 28.9 6.77539 28.9C6.07123 28.9 5.50039 28.3292 5.50039 27.625ZM11.8754 16.15C11.1712 16.15 10.6004 16.7208 10.6004 17.425C10.6004 18.1292 11.1712 18.7 11.8754 18.7H21.2254C21.9296 18.7 22.5004 18.1292 22.5004 17.425C22.5004 16.7208 21.9296 16.15 21.2254 16.15H11.8754ZM10.6004 22.525C10.6004 21.8208 11.1712 21.25 11.8754 21.25H21.2254C21.9296 21.25 22.5004 21.8208 22.5004 22.525C22.5004 23.2292 21.9296 23.8 21.2254 23.8H11.8754C11.1712 23.8 10.6004 23.2292 10.6004 22.525ZM11.8754 26.35C11.1712 26.35 10.6004 26.9208 10.6004 27.625C10.6004 28.3292 11.1712 28.9 11.8754 28.9H21.2254C21.9296 28.9 22.5004 28.3292 22.5004 27.625C22.5004 26.9208 21.9296 26.35 21.2254 26.35H11.8754ZM26.6042 10.9038L16.6949 0.9962C16.6695 0.970782 16.6407 0.948736 16.6121 0.926831C16.5909 0.910646 16.5699 0.894537 16.5504 0.8772C16.4297 0.7684 16.3107 0.6613 16.1781 0.5712C16.1351 0.542201 16.0881 0.519308 16.0413 0.496521C16.014 0.483241 15.9868 0.469996 15.9605 0.4556C15.9321 0.439609 15.9038 0.423243 15.8755 0.406854C15.7834 0.353549 15.6908 0.300008 15.5933 0.2584C15.2584 0.119 14.898 0.0493001 14.5325 0.0237999C14.499 0.0216619 14.4658 0.0171341 14.4325 0.0125959C14.3864 0.00630786 14.3402 0 14.2928 0H3.80039C1.92359 0 0.400391 1.5232 0.400391 3.4V30.6C0.400391 32.4768 1.92359 34 3.80039 34H24.2004C26.0772 34 27.6004 32.4768 27.6004 30.6V13.3076C27.6004 12.4066 27.2417 11.5413 26.6042 10.9038ZM25.0504 30.6C25.0504 31.0675 24.6696 31.45 24.2004 31.45H3.80039C3.33119 31.45 2.95039 31.0675 2.95039 30.6V3.4C2.95039 2.9325 3.33119 2.55 3.80039 2.55H14.0004V10.2C14.0004 12.0768 15.5236 13.6 17.4004 13.6H25.0504V30.6ZM16.5504 4.4557L23.143 11.05H17.4004C16.9312 11.05 16.5504 10.6675 16.5504 10.2V4.4557Z"
                        fill="#9CA3AF" />
                </svg>
                <p class="text-lg leading-5 text-gray-900">Letter will be Previewed here</p>
            </div>
        </div>
    </div>
</div>
<div id="viewSentMailMessageModal" style="display: none;">
    <div id="viewSentMailContent" class="overflow-y-auto pb-6 text-gray-700 h-full">
    </div>
</div>
<div id="addTaskBetaModal" style="display: none;">
    <div id="viewSentMailContent" class="overflow-y-auto pb-6 text-gray-700 h-full">
        <div class="flex flex-col h-full justify-between">
            <form id="createTaskForm" class="flex flex-col space-y-6"></form>
        </div>
    </div>
</div>