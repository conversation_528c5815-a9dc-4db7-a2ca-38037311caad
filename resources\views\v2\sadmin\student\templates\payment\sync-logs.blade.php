<script id="syncLogsTemplate" type="text/html">
    <div class="container">
        <div class="timeline">
            # for(let i=0; i < data.length; i++) { # <div class="timeline-container primary">
                <div class="timeline-icon">
                    <img src="{{ asset('v2/img/activity-payment.svg') }}" class="" alt="overseasIcon" />
                </div>
                <div class="timeline-body relative">
                    <div class="px-2 py-1 rounded-md hover:bg-blue-gray-100 timeline-content">
                        <div class="flex flex-col space-y-1">
                            <div class="flex justify-between space-x-2 w-full">
                                <div class="flex space-x-2">
                                    # let user_name = (data[i]['causer'] != null) ? data[i]['causer']['name'] : 'System'
                                    #
                                    <p class="flex text-sm leading-5 font-medium text-gray-900">#= user_name #</p>
                                </div>
                            </div>
                            <p class="text-xs leading-5 font-normal text-gray-400">#= data[i]['created_at'] #</p>
                        </div>
                        <div class="flex flex-row space-x-1">
                            <div class="max-h-16 bg-gray-200 w-0.5"></div>
                            <div class="max-h-16 overflow-y-auto w-full">
                                <p>#= data[i]['description'] #</p>
                            </div>
                        </div>
                        # if (data[i]['event'] === 'updated' && ['rsipd', 'rsac', 'rsmp', 'rssrvp', 'rsscho', 'rsd'].includes(data[i]['subject_type'])) { #
                        <div class="log-grid mb-6" style="display: none">
                            <div class="grid grid-cols-6 gap-4 bg-gray-50 rounded-md p-2 text-xs">
                                <span class="col-span-2">Field</span>
                                <span class="col-span-4">Recent Update</span>
                            </div>
                            # for(let j=0; j < data[i]['properties'].length; j++) { # <div
                                class="grid grid-cols-6 gap-4 border-b border-gray-200 py-2 px-2">
                                <div class="col-span-2">
                                    <x-v2.badge variant="info" dark>#:data[i]['properties'][j]['field_name'] #
                                    </x-v2.badge>
                                </div>
                                <div class="col-span-4">
                                    <div class="flex items-center gap-2">
                                        <x-v2.badge variant="success" class="text-gray-600">
                                            #:data[i]['properties'][j]['old'] #</x-v2.badge>
                                        <x-v2.icons name="arrow-right" width="14" height="14" viewBox="0 0 20 20" />
                                        <x-v2.badge variant="danger" class="text-gray-400">
                                            #:data[i]['properties'][j]['attributes'] #</x-v2.badge>

                                    </div>
                                </div>
                        </div>
                        #}#
                    </div>
                    <button class="view-detail-log tw-btn-ghost">View Detail</button>
                    # } #
                </div>
        </div>
    </div>
    # } #
    </div>
    </div>
</script>