<x-v2.layouts.default :optimized="true" :back="true">
    @section('title', $title)
    @section('keywords', $keywords)
    @section('description', $description)
    @section('mainmenu', $mainmenu)

    <x-slot name="cssHeader">
        <link rel="stylesheet" href="{{ asset('v2/css/sadmin/student-view.css') }}">
        <link rel="stylesheet" href="https://cdnjs.cloudflare.com/ajax/libs/tagify/4.17.8/tagify.min.css"
            integrity="sha512-Ft73YZFLhxI8baaoTdSPN8jKRPhYu441A8pqlqf/CvGkUOaLCLm59ZWMdls8lMBPjs1OZ31Vt3cmZsdBa3EnMw=="
            crossorigin="anonymous" referrerpolicy="no-referrer" />
        <link rel="stylesheet" type="text/css"
            href="https://cdn.jsdelivr.net/gh/ycodetech/horizontal-timeline-2.0@2/css/horizontal_timeline.2.0.min.css">
    </x-slot>

    {{-- Course Dropdownlist value template --}}
    <script id="courseDropdownList" type="text/x-kendo-template">
        #if(data.id == 0) {#
            <span class="px-1">#:course_title#</span>
        #} else {#
        <div class="flex items-center justify-start gap-1 text-sm ">
            <span class="code">#:data.course_code#:</span>
            <span class="course-name truncate">#:data.course_name#</span>
            <span class="course-status flex items-center justify-center px-2 py-1 ml-1 bg-#:data.bg_color#-100 rounded"><p class="text-xs leading-4 text-center text-#:data.bg_color#-800">#:data.status#</p>
            </span>
        </div>
        #}#
    </script>
    {{-- @dd($studentDetails) --}}

    {{-- Page Header --}}
    <div class="">
        <div id="skeleton"></div>
        <div id="invisible" class="invisible1">
            <div class="flex px-8 pt-4 pb-1 justify-between bg-white gap-4 md:gap-0">
                <div class="flex space-x-2 items-center justify-start group">
                    <div class="relative">
                        @if (empty($studentDetails[0]->profile_pic))
                        <div class="rounded-md">
                            <div class='flex user-profile-pic w-11 h-10 !rounded-md bg-blue-500 items-center'>
                                <span class='text-xl flex justify-center items-center leading-6 px-1 w-full'
                                    id="getStudentProfile">
                                    {{ strtoupper($studentDetails[0]->first_name[0]) }}{{
                                    $studentDetails[0]->family_name != '' ?
                                    strtoupper($studentDetails[0]->family_name[0]) : '' }}
                                </span>
                            </div>
                        </div>
                        @else
                        <div class="w-11 h-10 rounded-full">
                            <img id="studProfilePic" class="w-11 h-10 flex-1 rounded-md object-cover object-top"
                                src="{{ $studentDetails[0]->profile_pic }}"
                                alt="{{ $studentDetails[0]->first_name }}" />
                        </div>
                        @endif
                        <button
                            class="edit_profile glob-tooltip btn-secondary rounded-full w-[18px] h-[18px] p-[3px] absolute -top-1.5 -right-2 shadow z-50 invisible group-hover:visible opacity-0 group-hover:opacity-100 transition-all"
                            title="Edit Profile" data-tooltip-position="right">
                            <img src="/v2/img/edit-gray-pencil.svg" alt="Edit" class="w-3 h-3" />
                        </button>
                    </div>
                    <div class="flex items-baseline gap-2">
                        <p class="text-xl leading-6 text-gray-700 font-medium" id="studentNameTitle">
                            {{ $studentDetails[0]->first_name . ' ' . $studentDetails[0]->family_name }}
                        </p>
                        <div class="flex items-center gap-1">
                            <p class="text-xs text-gray-400 leading-none">ID:
                                {{ $studentDetails[0]->generated_stud_id }}
                            </p>
                            <x-v2.copy width="16" height="16" data-text="{{ $studentDetails[0]->generated_stud_id }}" />
                        </div>
                    </div>
                </div>
                <x-v2.skeleton.buttongroup count="5" />
                <div class="tw-button-group hidden">
                    <div class="flex space-x-0 md:space-x-2 items-center justify-end gap-2 md:gap-0">
                        <input type="hidden" name="student_id" id="student_id" value="" />
                        <input type="hidden" name="student_course_id" id="student_course_id" value="" />
                        <input type="hidden" name="course_id" id="course_id" value="" />
                        <button type="button" class="addCourseVariantBtn btn-primary px-3 hidden">
                            <p class="text-xs leading-none text-white uppercase">GNG-4838</p>
                        </button>
                        <button type="button" class="sendEmailBtn btn-primary px-3 h-[1.875rem] hidden lg:flex">
                            <svg width="12" height="12" viewBox="0 0 12 10" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M0 3.03807V8C0 9.10457 0.895431 10 2 10H10C11.1046 10 12 9.10457 12 8V2C12 0.895431 11.1046 0 10 0H2C0.895431 0 0 0.89543 0 2V3.03807C0 3.03809 0 3.03804 0 3.03807ZM2 1H10C10.5523 1 11 1.44772 11 2V2.73987L6.0001 5.43212L1 2.73976V2C1 1.44772 1.44772 1 2 1ZM1 3.87552L5.76305 6.44024C5.91104 6.51992 6.08916 6.51992 6.23715 6.44024L11 3.87562V8C11 8.55229 10.5523 9 10 9H2C1.44772 9 1 8.55228 1 8V3.87552Z"
                                    fill="currentColor" />
                            </svg>
                            <p class="text-xs leading-none text-white capitalize">Send Mail</p>
                        </button>
                        <button type="button" class="sendSmsBtn btn-primary px-3 h-[1.875rem] hidden lg:flex">
                            <svg width="12" height="12" viewBox="0 0 12 12" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M4 5C4 4.72386 4.22386 4.5 4.5 4.5H7.5C7.77614 4.5 8 4.72386 8 5C8 5.27614 7.77614 5.5 7.5 5.5H4.5C4.22386 5.5 4 5.27614 4 5ZM4.5 6.5C4.22386 6.5 4 6.72386 4 7C4 7.27614 4.22386 7.5 4.5 7.5H6.5C6.77614 7.5 7 7.27614 7 7C7 6.72386 6.77614 6.5 6.5 6.5H4.5ZM1.38797e-05 6C1.38797e-05 2.68629 2.68631 0 6.00001 0C9.31372 0 12 2.68629 12 6C12 9.31371 9.31372 12 6.00001 12C4.90639 12 3.87989 11.707 2.99618 11.195L0.658128 11.9743C0.482167 12.033 0.288196 11.9894 0.154224 11.8612C0.0202529 11.7329 -0.0317031 11.541 0.0192519 11.3626L0.730859 8.87202C0.264804 8.01862 1.38797e-05 7.03957 1.38797e-05 6ZM6.00001 1C3.23859 1 1.00001 3.23858 1.00001 6C1.00001 6.93308 1.25513 7.80506 1.69913 8.55165C1.76952 8.67002 1.78797 8.81217 1.75014 8.94458L1.24119 10.7259L2.89938 10.1732C3.04199 10.1256 3.19843 10.1448 3.32536 10.2253C4.09854 10.7159 5.01533 11 6.00001 11C8.76144 11 11 8.76142 11 6C11 3.23858 8.76144 1 6.00001 1Z"
                                    fill="currentColor" />
                            </svg>
                            <p class="text-xs leading-none text-white capitalize">Send SMS</p>
                        </button>
                        <button type="button" class="issueLetter btn-primary px-3 h-[1.875rem] hidden lg:flex">
                            <svg width="12" height="12" viewBox="0 0 12 15" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M2.08535 1C2.29127 0.417403 2.84689 0 3.5 0H6.5C7.15311 0 7.70873 0.417404 7.91465 1H8.5C9.32843 1 10 1.67157 10 2.5V6.21651L9.87005 5.91359C9.70318 5.52462 9.38412 5.23219 9 5.09372V2.5C9 2.22386 8.77614 2 8.5 2H7.91465C7.70873 2.5826 7.15311 3 6.5 3H3.5C2.84689 3 2.29127 2.5826 2.08535 2H1.5C1.22386 2 1 2.22386 1 2.5V12.5C1 12.7761 1.22386 13 1.5 13H4.08301C3.9633 13.3338 3.96951 13.6856 4.07953 14H1.5C0.671573 14 0 13.3284 0 12.5V2.5C0 1.67157 0.671573 1 1.5 1H2.08535ZM3.5 1C3.22386 1 3 1.22386 3 1.5C3 1.77614 3.22386 2 3.5 2H6.5C6.77614 2 7 1.77614 7 1.5C7 1.22386 6.77614 1 6.5 1H3.5ZM8.95128 6.30785C8.87242 6.12402 8.69159 6.00489 8.49157 6.00497C8.29154 6.00506 8.11082 6.12435 8.03211 6.30823L5.91655 11.2511C5.89725 11.2846 5.88173 11.3206 5.87057 11.3585L5.03607 13.3082C4.92741 13.5621 5.04513 13.856 5.29899 13.9646C5.55286 14.0733 5.84674 13.9556 5.9554 13.7017L6.68373 12H10.3051L11.0353 13.7021C11.1442 13.9559 11.4382 14.0733 11.6919 13.9645C11.9457 13.8556 12.0632 13.5616 11.9543 13.3078L11.1179 11.3581C11.1067 11.3202 11.0911 11.2842 11.0718 11.2507L8.95128 6.30785ZM9.8761 11H7.11174L8.49231 7.77444L9.8761 11Z"
                                    fill="currentColor" />
                            </svg>
                            <p class="text-xs leading-none text-white capitalize">Send Letter</p>
                        </button>
                        <button type="button" class="quickAddNoteButton btn-primary px-3 h-[1.875rem] hidden lg:flex">
                            <svg width="12" height="12" viewBox="0 0 14 14" fill="none"
                                xmlns="http://www.w3.org/2000/svg">
                                <path
                                    d="M9 4.5C9 6.98528 6.98528 9 4.5 9C2.01472 9 0 6.98528 0 4.5C0 2.01472 2.01472 0 4.5 0C6.98528 0 9 2.01472 9 4.5ZM5 2.5C5 2.22386 4.77614 2 4.5 2C4.22386 2 4 2.22386 4 2.5V4H2.5C2.22386 4 2 4.22386 2 4.5C2 4.77614 2.22386 5 2.5 5H4L4 6.5C4 6.77614 4.22386 7 4.5 7C4.77614 7 5 6.77614 5 6.5V5H6.5C6.77614 5 7 4.77614 7 4.5C7 4.22386 6.77614 4 6.5 4H5V2.5ZM11.5 3H9.79297C9.69436 2.65136 9.56223 2.31679 9.40029 2H11.5C12.8807 2 14 3.11929 14 4.5V8.78579C14 9.18361 13.842 9.56514 13.5607 9.84645L9.84645 13.5607C9.56514 13.842 9.18361 14 8.78579 14L4.5 14C3.11929 14 2 12.8807 2 11.5V9.40029C2.31679 9.56223 2.65136 9.69436 3 9.79297V11.5C3 12.3284 3.67157 13 4.5 13L8 13V11C8 9.34315 9.34315 8 11 8H13V4.5C13 3.67157 12.3284 3 11.5 3ZM9 12.9518C9.05125 12.9275 9.09847 12.8944 9.13934 12.8536L12.8536 9.13934C12.8944 9.09847 12.9275 9.05125 12.9518 9H11C9.89543 9 9 9.89543 9 11V12.9518Z"
                                    fill="currentColor" />
                            </svg>
                            <p class="text-xs leading-none text-white capitalize">Add Note</p>
                        </button>
                        @include('v2.sadmin.student.partials.more-action-dropdown')
                    </div>
                </div>
            </div>
            {{-- Tabs Header --}}
            <div class="w-full profile-tablist bg-white" id="tabStrip">
                <ul class=" flex bg-white md:!pl-8 !pl-0 !gap-8" id="" data-tabs-toggle="" role="tablist">
                    <li id="summary_tab" data-value="summary"
                        class="k-tabstrip-item k-item k-state-default k-first {{$currentTab == 'summary' ? 'k-state-active' : ''}}">
                        <span class="inline-block text-sm leading-none font-medium px-1 py-4 text-gray-500"
                            style="font-weight: 500; font-size: 0.875rem">Summary</span>
                    </li>
                    <li id="course_tab" data-value="course"
                        class="k-tabstrip-item k-item k-state-default {{$currentTab == 'course' ? 'k-state-active' : ''}}">
                        <span class="inline-block text-sm leading-none font-medium px-1 py-4 text-gray-500"
                            style="font-weight: 500; font-size: 0.875rem">Course</span>
                    </li>
                    <li id="attendance_tab" data-value="attendance"
                        class="k-tabstrip-item k-item k-state-default {{$currentTab == 'attendance' ? 'k-state-active' : ''}}">
                        <span class="inline-block text-sm leading-none font-medium px-1 py-4 text-gray-500"
                            style="font-weight: 500; font-size: 0.875rem">Attendance</span>
                    </li>
                    <li id="documents_tab" data-value="documents"
                        class="k-tabstrip-item k-item k-state-default {{$currentTab == 'documents' ? 'k-state-active' : ''}}">
                        <span class="inline-block text-sm leading-none font-medium px-1 py-4 text-gray-500"
                            style="font-weight: 500; font-size: 0.875rem">Documents</span>
                    </li>
                    <li id="timetable_tab" data-value="timetable"
                        class="k-tabstrip-item k-item k-state-default {{$currentTab == 'timetable' ? 'k-state-active' : ''}}">
                        <span class="inline-block text-sm leading-none font-medium px-1 py-4 text-gray-500"
                            style="font-weight: 500; font-size: 0.875rem">Timetable</span>
                    </li>
                    <li id="result_tab" data-value="result"
                        class="k-tabstrip-item k-item k-state-default {{$currentTab == 'result' ? 'k-state-active' : ''}}">
                        <span class="inline-block text-sm leading-none font-medium px-1 py-4 text-gray-500"
                            style="font-weight: 500; font-size: 0.875rem">Result</span>
                    </li>
                    <li id="payments_tab" data-value="payments"
                        class="k-tabstrip-item k-item k-state-default {{$currentTab == 'payments' ? 'k-state-active' : ''}}">
                        <span class="inline-block text-sm leading-none font-medium px-1 py-4 text-gray-500"
                            style="font-weight: 500; font-size: 0.875rem">Payments</span>
                    </li>
                    <li id="placement_tab" data-value="placement"
                        class="k-tabstrip-item k-item k-state-default {{$currentTab == 'placement' ? 'k-state-active' : ''}}">
                        <span class="inline-block text-sm leading-none font-medium px-1 py-4 text-gray-500"
                            style="font-weight: 500; font-size: 0.875rem">Placement</span>
                    </li>
                    <li id="activitylog_tab" data-value="activitylog"
                        class="k-tabstrip-item k-item k-state-default k-last {{$currentTab == 'activitylog' ? 'k-state-active' : ''}}">
                        <span class="inline-block text-sm leading-none font-medium px-1 py-4 text-gray-500"
                            style="font-weight: 500; font-size: 0.875rem">Activity Log</span>
                    </li>
                    <li id="riskmatrix_tab" data-value="riskmatrix"
                        class="k-tabstrip-item k-item k-state-default k-last {{$currentTab == 'riskmatrix' ? 'k-state-active' : ''}}">
                        <span class="inline-block text-sm leading-none font-medium px-1 py-4 text-gray-500"
                            style="font-weight: 500; font-size: 0.875rem">Risk Matrix</span>
                    </li>
                </ul>
                {{-- / Tabs Header --}}
                <div class="summary-li-detail">
                    <x-v2.skeleton.templates.summary class="{{$currentTab == 'summary' ? 'block' : 'hidden' }}" />
                    @if($currentTab == 'summary')
                    @include('v2.sadmin.student.tabs.summary', [
                    'isAjax' => false
                    ])
                    @endif
                </div>
                <div class="course-li-detail">
                    <x-v2.skeleton.templates.course class="{{$currentTab == 'course' ? 'block' : 'hidden' }}" />
                    @if($currentTab == 'course')
                    @include('v2.sadmin.student.tabs.course', [
                    'isAjax' => false
                    ])
                    @endif
                </div>
                <div class="attendance-li-detail">
                    <x-v2.skeleton.templates.attendance class="{{$currentTab == 'attendance' ? 'block' : 'hidden' }}" />
                    @if($currentTab == 'attendance')
                    @include('v2.sadmin.student.tabs.attendance', [
                    'isAjax' => false
                    ])
                    @endif
                </div>
                <div class="documents-li-detail">
                    <x-v2.skeleton.templates.document class="{{$currentTab == 'documents' ? 'block' : 'hidden' }}" />
                    @if($currentTab == 'documents')
                    @include('v2.sadmin.student.tabs.document', [
                    'isAjax' => false
                    ])
                    @endif
                </div>
                <div class="timetable-li-detail">
                    <x-v2.skeleton.templates.timetable class="{{$currentTab == 'timetable' ? 'block' : 'hidden' }}" />
                    @if($currentTab == 'timetable')
                    @include('v2.sadmin.student.tabs.timetable', [
                    'isAjax' => false
                    ])
                    @endif
                </div>
                <div class="result-li-detail">
                    <x-v2.skeleton.templates.result class="{{$currentTab == 'result' ? 'block' : 'hidden' }}" />
                    @if($currentTab == 'result')
                    @include('v2.sadmin.student.tabs.result', [
                    'isAjax' => false
                    ])
                    @endif
                </div>
                <div class="payments-li-detail">
                    <x-v2.skeleton.templates.payment class="{{$currentTab == 'payments' ? 'block' : 'hidden' }}" />
                    @if($currentTab == 'payments')
                    @include('v2.sadmin.student.tabs.payments', [
                    'isAjax' => false
                    ])
                    @endif
                </div>
                <div class="placement-li-detail">
                    <x-v2.skeleton.templates.placement class="{{$currentTab == 'placement' ? 'block' : 'hidden' }}" />
                    @if($currentTab == 'placement')
                    @include('v2.sadmin.student.tabs.placement', [
                    'isAjax' => false
                    ])
                    @endif
                </div>
                <div class="activitylog-li-detail">
                    <x-v2.skeleton.templates.activity class="{{$currentTab == 'activitylog' ? 'block' : 'hidden' }}" />
                    @if($currentTab == 'activitylog')
                    @include('v2.sadmin.student.tabs.activity-tab', [
                    'isAjax' => false
                    ])
                    @endif
                </div>
                <div class="riskmatrix-li-detail">
                    <x-v2.skeleton.templates.riskmatrix class="{{$currentTab == 'riskmatrix' ? 'block' : 'hidden' }}" />
                    @if($currentTab == 'riskmatrix')
                    @include('v2.sadmin.student.tabs.riskmatrix-tab', [
                    'isAjax' => false
                    ])
                    @endif
                </div>
            </div>
        </div>
    </div>

    @include('v2.sadmin.student.pages.more-actions')

    {{-- Send email, SMS, Letter and Notes --}}
    @include('v2.sadmin.student.pages.header')

    {{-- Popup for edit Student Info --}}
    @include('v2.sadmin.student.pages.personal-info')

    {{-- Templates for all tab header button --}}
    @include('v2.sadmin.student.tabs.header-templates')

    <div id="displayProfilePicModal" style="display:none;">
        <div class="flex justify-center  w-full">
            @if (empty($studentDetails[0]->profile_pic_original))
            <div class="rounded-full">
                <div class='flex user-profile-pic w-96 h-96 bg-blue-500 items-center'><span
                        class='text-2xl flex justify-center items-center leading-6 px-1 w-full'
                        id="getStudentProfile">{{ strtoupper($studentDetails[0]->first_name[0]) }}{{
                        $studentDetails[0]->family_name != '' ? strtoupper($studentDetails[0]->family_name[0]) : ''
                        }}</span>
                </div>
            </div>
            @else
            <div class="rounded-full">
                <img class="w-96 h-96 flex-1" src="{{ $studentDetails[0]->profile_pic_original }}" />
            </div>
            @endif
        </div>
        <p class="text-center w-full pt-4 getStudentName" hidden>
            {{ $studentDetails[0]->first_name . ' ' . $studentDetails[0]->family_name }}</p>
    </div>

    <script id="previewTemplate" type="text/html">
        <div id="#= previewId #"></div>
    </script>

    <div id="previewDocumentModalNote" style="display:none;"></div>

    <div id="dateWarningModal"></div>
    <div id="createScheduleDateWarningModal"></div>
    <div id="reCreateScheduleDateWarningModal"></div>
    <div id="editScheduleDateWarningModal"></div>
    <div id="importScheduleDateWarningModal"></div>
    <div id="customCreateScheduleDateWarningModal"></div>

    {{-- Header Actions --}}
    @include('v2.sadmin.student.tabs.action.offer-service')
    @include('v2.sadmin.student.tabs.action.training-plan')
    @include('v2.sadmin.student.tabs.action.view-fee-schedule')
    @include('v2.sadmin.student.tabs.action.offer-communication')
    @include('v2.sadmin.student.templates.modals.header-modals')
    {{-- Notification Templates --}}
    @include('v2.sadmin.notification')

    {{-- TEMPLATE: Global Templates --}}
    @include('v2.sadmin.student.tabs.action.enroll-course')

    <x-slot name="jsFooter">

        <script src="{{ asset('v2/js/jszip.min.js') }}"></script>
        <script src="https://cdn.ckeditor.com/ckeditor5/35.3.2/super-build/ckeditor.js"></script>
        {{-- <script src="{{ asset('plugins/ckeditor5/ckeditor.js') }}"></script> --}}
        <script src="{{ asset('v2/js/tagify.js') }}"></script>
        {{-- <script src="{{ asset(mix('build/js/student-profile.js')) }}"></script> --}}
        {{-- @vite(['resources/assets/scripts/student-profile/index.js']) --}}
        <script src="{{ versioned_asset('/build/student-profile-build.js') }}"></script>
        {{-- <script src="{{ asset('js/template-scripts.js') }}"></script> --}}

        {{-- @include('v2.sadmin.student.js.index') --}}
        <script
            src="https://cdn.jsdelivr.net/gh/ycodetech/horizontal-timeline-2.0@2/JavaScript/horizontal_timeline.2.0.min.js">
            </x-slot>

    <x-slot name="fixVariables">
        var api_token = "{{ isset($api_token) ? "Bearer $api_token" : '' }}";
        var SMSApiKey = "{{ Config::get('constants.SMSApiKey') }}";
        var sendSmsUrl = "{{ Config::get('constants.sendSmsUrl') }}";
        var studentId = "{{ $studentId }}";
        var studentIsUser = "{{ $studentIsUser }}";
        var agentId = "{{ $agentId }}";
        var collegeId = "{{ $collegeId }}";
        var userId = "{{ $userId }}";
        var arrStudentOrigin = @json($arrStudentOrigin);
        var arrCourseCancelReason = @json($arrCourseCancelReason);

        {{--<!--var isHigherEd = "{{ $isHigherEd }}";--> --}}
        var arrCourseStatus = @json($arrCourseStatus);
        var invoice_number = {{ $invoice_number }};
        var receipt_number = {{ $receipt_number }};
        let dateFormateFontSideJS = "{{ Config::get('app.dateFormateFontSideJS') }}";
        var selectedTabText = "{{ $currentTab }}";
        var headerData = @json($headerData);
        let emailTotalAttachmentAllowed ="{{ Config::get('constants.emailTotalAttachmentAllowed') }}";
    </x-slot>

</x-v2.layouts.default>