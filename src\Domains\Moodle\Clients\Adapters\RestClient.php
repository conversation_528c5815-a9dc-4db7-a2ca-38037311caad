<?php

namespace Domains\Moodle\Clients\Adapters;

use Assert\Assertion;
use Domains\Moodle\Clients\BaseAdapter;
use Domains\Moodle\Connection;
use GuzzleHttp\Client as HttpClient;

/**
 * Class RestClient
 *
 * @method HttpClient getClient()
 */
class RestClient extends BaseAdapter
{
    const OPTION_FORMAT = 'moodlewsrestformat';

    const RESPONSE_FORMAT_JSON = 'json';

    const RESPONSE_FORMAT_XML = 'xml';

    /**
     * @var string
     */
    protected $responseFormat;

    /**
     * @var string
     */
    protected $connection;

    /**
     * RestClient constructor.
     *
     * @param  string  $responseFormat
     */
    public function __construct(Connection $connection)
    {
        $this->setResponseFormat(config('galaxy.moodle.format'));
        // $this->setConnection(config('laravel-moodle.url'), config('laravel-moodle.token'));
        // $this->setConnection($connection);
        $this->connection = $connection;

        parent::__construct($this->getConnection());
    }

    /**
     * Send API request
     *
     * @return array|bool|float|int|\SimpleXMLElement|string
     */
    public function sendRequest($function, array $arguments = [])
    {
        $configuration = [
            self::OPTION_FUNCTION => $function,
            self::OPTION_FORMAT => $this->responseFormat,
            self::OPTION_TOKEN => $this->getConnection()->getToken(),
        ];

        info('moodle http request', [array_merge($configuration, $arguments)]);

        // dd($this->getClient());

        $response = $this->getClient()->post('', [
            'form_params' => array_merge($configuration, $arguments),
        ]);
        // dd($response);

        // dd($response->getBody()->getContents());

        $formattedResponse = $this->responseFormat === self::RESPONSE_FORMAT_JSON ?
        json_decode($response->getBody(), true) :
        simplexml_load_string($response->getBody());

        info('moodle http response', [$response->getStatusCode(), $formattedResponse]);
        // dd($formattedResponse);

        $this->handleException($formattedResponse);

        return $formattedResponse;
    }

    /**
     * Build client instance
     *
     * @return HttpClient
     */
    protected function buildClient()
    {
        return new HttpClient([
            'base_url' => $this->getEndPoint(),
            'base_uri' => $this->getEndPoint(),
        ]);
    }

    /**
     * Set response format
     *
     * @param  string  $format
     */
    protected function setResponseFormat($format)
    {
        // Assertion::inArray($format, [self::RESPONSE_FORMAT_JSON, self::RESPONSE_FORMAT_XML]);
        $this->responseFormat = $format;
    }

    /**
     * @return string|Connection
     */
    protected function getConnection()
    {
        return $this->connection;
    }

    protected function setConnection($url, $token)
    {
        $this->connection = new Connection($url, $token);
    }
}
