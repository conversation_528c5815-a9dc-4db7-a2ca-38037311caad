<?php

namespace Domains\Moodle\Entities;

use App\Exceptions\ApplicationException;
use App\Model\v2\Student;
use App\Users;
use Illuminate\Support\Str;
use Support\Traits\ArrayToProps;

/**
 * Class Course
 */
class User extends BaseEntity implements Entity
{
    use ArrayToProps;

    public int $id;   // ID of the user

    public string $username;   // The username

    public string $firstname;   // The first name(s) of the user

    public string $lastname;   // The family name of the user

    public string $fullname;   // The fullname of the user

    public string $email;   // An email address - allow email as root@localhost

    public string $address;   // Postal address

    public string $phone1;   // Phone 1

    public string $phone2;   // Phone 2

    public string $department;   // department

    public string $institution;   // institution

    // public string $idnumber;   //An arbitrary ID code number perhaps from the institution
    public string $interests;   // user interests (separated by commas)

    public int $firstaccess;   // first access to the site (0 if never)

    public int $lastaccess;   // last access to the site (0 if never)

    public string $auth;   // Auth plugins include manual, ldap, etc

    public int $suspended;   // Suspend user account, either false to enable user login or true to disable it

    public int $confirmed;   // Active user: 1 if confirmed, 0 otherwise

    public string $lang;   // Language code such as "en", must exist on server

    public string $calendartype;   // Calendar type such as "gregorian", must exist on server

    public string $theme;   // Theme name such as "standard", must exist on server

    public string $timezone;   // Timezone code such as Australia/Perth, or 99 for default

    public int $mailformat;   // Mail format code is 0 for plain text, 1 for HTML etc

    public string $description;   // User profile description

    public int $descriptionformat;   // int format (1 = HTML, 0 = MOODLE, 2 = PLAIN, or 4 = MARKDOWN

    public string $city;   // Home city of the user

    public string $country;   // Home country code of the user, such as AU or CZ

    public string $profileimageurlsmall;   // User image profile URL - small version

    public string $profileimageurl;   // User image profile URL - big version

    public string $type;   // The type of the custom field - text field, checkbox...

    public string $value;   // The value of the custom field (as stored in the database)

    public string $displayvalue;   // The value of the custom field for display

    public string $name;   // The name of the custom field

    public string $shortname;   // The shortname of the custom field - to be able to build the field class in the code

    public $list;

    public $customfields; // User custom fields (also known as user profile fields)

    public $preferences;   // Users preferences

    public bool $createpassword; // True if password should be created and mailed to user.

    public string $password;
    // public string $temporaryPassword;
    // public bool $isSSOAccountMissing;

    // update fields
    public $warnings;
    // public int $item;   //ID of the user
    // public int $itemid;   //ID of the user
    // public string $message;
    // public string $warningcode;

    /* Factory Method for course */
    public static function FromUser(Users $user, $params = null)
    {
        $name = $user->first_last_name;
        $ssoId = $user->getSSSOId();
        $data = [
            // 'idnumber'       => (int) $user->id,
            'username' => $ssoId ?? $user->email,   // $user->getSSSOId()
            'firstname' => cleanNonUTF(@$name[0]),
            'lastname' => cleanNonUTF(@$name[1] ? $name[1] : $name[0]),
            'fullname' => $user->name,
            'email' => $user->email,
            'auth' => $ssoId ? 'oidc' : 'manual',    // 'oidc'
            // 'createpassword' => 1,
            // 'password' => $ssoId ? Str::random(8) : null,
        ];

        // isset($params['password']) ? $data['password'] = $params['password'] : $data['createpassword'] = 1;

        if (@$params['password']) {
            $data['password'] = $params['password'];
        } else {
            // $data['password'] = Str::random(8);
            $data['createpassword'] = true;
        }

        // $extra = collect((array) $params)->filter(fn($v) => !is_null($v))->all();
        // $data = array_merge($data, $extra);

        info('data', [$data]);

        return self::LazyFromArray($data);

        //        return self::LazyFromArray([
        //            'idnumber' => (int) $user->id,
        //            /* FOR OIDC to work, username needs to be keycloak user id */
        //            'username' => $ssoId ?? $user->email,   //$user->getSSSOId()
        //            'firstname' => cleanNonUTF(@$name[0]),
        //            'lastname' => cleanNonUTF(@$name[1] ? $name[1] : $name[0]),
        //            'email' => $user->email,
        //            'createpassword' => 1,
        //            //'password' => $ssoId ? Str::random(8) : null,
        //            'auth' => $ssoId ? 'oidc' : 'manual'    //'oidc'
        //        ]);
    }

    public static function FromStudent(Student $student)
    {
        if (! $student->userAccount) {
            throw new ApplicationException("Student doesn't have associated user account yet.");
        }
        // $name = $student->first_last_name;
        $ssoId = $student->userAccount->getSSSOId();

        return self::LazyFromArray([
            // 'idnumber' => $student->id,
            /* FOR OIDC to work, username needs to be keycloak user id */
            'username' => $ssoId ? $ssoId : $student->email,    // $student->userAccount->getSSSOId()    //TODO:: GNG-4658
            'firstname' => $student->first_name,
            'lastname' => $student->family_name,
            'email' => $student->email,
            'createpassword' => 1,
            // 'password' => $ssoId ? Str::random(8) : null,
            'auth' => $ssoId ? 'oidc' : 'manual',    // 'oidc'
        ]);
    }
}
