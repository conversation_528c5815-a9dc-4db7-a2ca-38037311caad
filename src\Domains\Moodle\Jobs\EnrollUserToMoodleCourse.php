<?php

namespace Domains\Moodle\Jobs;

use App\Exceptions\ApplicationException;
use App\Model\v2\CourseBatch;
use App\Model\v2\Student;
use App\Model\v2\StudentSubjectEnrolment;
use Domains\Moodle\Entities\Course;
use Domains\Moodle\Facades\Moodle;
use Domains\Moodle\Models\MoodleItem;
use Domains\Moodle\Moodle as MoodleMoodle;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class EnrollUserToMoodleCourse implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 1;

    public StudentSubjectEnrolment $enrollment;

    /**
     * Create a new job instance.
     */
    public function __construct(public $studentSubjectEnrolmentId) {}

    /**
     * Execute the job.
     */
    public function handle(): void
    {

        if (! Moodle::isConnected()) {
            return;
        }

        try {

            $this->enrollment = StudentSubjectEnrolment::findOrFail($this->studentSubjectEnrolmentId);

            // dd($this->enrollment);

            $meta = $this->enrollment->getMeta(StudentSubjectEnrolment::MOODLE_META_KEY);
            if (isset($meta['sync_status']) && $meta['sync_status'] == 1) {
                return;
            }

            $student = @$this->enrollment->student->associatedUserAccount;
            if (! $student) {
                throw new ApplicationException('Student does not have associated login account yet.');
            }

            $unit = $this->enrollment->unit;
            $course = $this->enrollment->course;
            $courseBatch = CourseBatch::firstOrCreate([
                'unit_id' => $this->enrollment->unit_id,
                'batch' => $this->enrollment->batch,
            ]);

            if ($student && $unit && $course && $courseBatch) {

                // if (!$course->isSyncedToMoodle()) {
                //     throw new ApplicationException("Moodle Course not synced yet. Please first sync the course.");
                // }

                $moodleCategory = $course->asMoodleItem();
                $moodleUser = $student->asMoodleItem();

                $subjectUnit = $this->enrollment->subjectUnitRevised()->where('course_id', $this->enrollment->course_id)->first();
                // dd($subjectUnit);
                if (! $subjectUnit) {
                    throw new ApplicationException('Cannot find revised subject unit for this enrollment.');
                }

                info('enroll user', [
                    'course_id' => $course->id,
                    'subjectUnitId' => $subjectUnit->id,
                    'unit_id' => $this->enrollment->unit_id,
                    'batch' => $this->enrollment->batch,
                    'course-batch' => $courseBatch->id,
                ]);
                $moodleCourse = $subjectUnit->asMoodleItem();
                $moodleGroup = null;

                try {
                    $moodleGroup = $courseBatch->asMoodleItem();
                    dispatch(new SyncBatchTimetableToMoodle($courseBatch->id));
                } catch (\Exception $e) {
                    info('adding course member group failed: '.$e->getMessage());
                }

                $this->enrollUserToCourse($moodleUser, $moodleCourse, $course->id);

                if ($moodleGroup) {
                    $this->addUserToGroup($moodleUser, $moodleGroup);
                }

                // dd([
                //     'course' => $moodleCourse->getSyncId(),
                //     'user' => $moodleUser->getSyncId(),
                //     'batch_group' => $moodleGroup ? $moodleGroup->getSyncId() : '',
                //     'category' => $moodleCategory->getSyncId(),
                // ]);
            }
        } catch (\Exception|\Throwable $e) {
            galaxy_log_to_file('enrolling user to moodle course failed', [tenant('id'), $e->getMessage(), $this->studentSubjectEnrolmentId]);
        }
    }

    public function enrollUserToCourse(MoodleItem $moodleUser, MoodleItem $moodleCourse, $courseId)
    {
        // info('test', [$moodleCourse->syncable_id, $moodleUser->syncable_id, $moodleUser->syncable->email]);
        $studentId = 'NA';
        if ($moodleUser->syncable->email) {
            $student = Student::where('email', $moodleUser->syncable->email)->first();
            if ($student) {
                $studentId = $student->id;
            }
        }
        $logTitle = sprintf('unit_enroll_%d_%d', $studentId, $courseId);

        activity()
            ->on($moodleCourse)
            ->log('Syncing enroll data with Moodle');

        try {
            Moodle::users()->enrollToCourse($moodleCourse->getSyncId(), $moodleUser->getSyncId(), MoodleMoodle::ROLE_STUDENT);
            $this->enrollment->setMeta(StudentSubjectEnrolment::MOODLE_META_KEY, [
                'sync_status' => 1,
                'synced_at' => now(),
            ]);

            $studentName = $moodleUser->syncable->name ?? 'Unknown Student';
            $unitCode = $moodleCourse->syncable->unit_code ?? 'Unknown Unit';
            $logDescriptionText = "Synced with moodle for enroll data of Student ($studentName) with Unit ($unitCode)";

            activity()
                ->on($moodleCourse)
                ->useLog($logTitle)
                ->log($logDescriptionText);

        } catch (\Exception $e) {
            activity()
                ->on($moodleCourse)
                ->useLog($logTitle)
                ->log('Syncing moodle item Failed for unit enroll with student');
            $message = $e->getMessage();
            galaxy_log_to_file('sync to moodle failed', [tenant('id'), $moodleUser, $moodleCourse, $message], 'moodle');
            $moodleCourse->syncErrorHandler($message, true);
            throw new Exception($message);
        }

    }

    public function addUserToGroup(MoodleItem $moodleUser, MoodleItem $moodleGroup)
    {
        Moodle::users()->addToGroup($moodleUser->getSyncId(), $moodleGroup->getSyncId());
    }
}
