<?php

namespace Domains\Moodle\Jobs;

use App\Exceptions\ApplicationException;
use App\Model\UserRoleType;
use App\Model\v2\Roles;
use App\Model\v2\Student;
use App\User;
use App\Users;
use Domains\Moodle\Facades\Moodle;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Str;
use SSO\Events\IdentityCreated;
use SSO\Jobs\SyncUserToKeycloak;

class SyncStudentToMoodle implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    // public $tries = 1;

    protected $password;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public $studentId,
        public $createStudentLoginAccountIfMissing = false,
        public $createSSOIfMissing = false,
        public $update = false
    ) {}

    /**
     * Execute the job.
     */
    public function handle(): void
    {

        if (! Moodle::isConnected()) {
            return;
        }
        config(['logging.channels.single.path' => base_path('storage/logs/moodle.log')]);

        try {
            $student = Student::findOrFail($this->studentId);
            $password = Str::random(6).'*@#'[rand(0, 2)].rand(0, 9);

            if (! $student->associatedUserAccount) {
                if ($this->createStudentLoginAccountIfMissing) {
                    $this->createStudentLoginAccount($student, $password);
                    $student = $student->fresh();
                } else {
                    throw new ApplicationException('Student does not have associated login details.');

                    return;
                }
            }

            $user = $student->associatedUserAccount;
            if (! $user->getSSSOId()) {
                if ($this->createSSOIfMissing) {
                    $this->createSSOAccount($user, $password);
                } else {
                    galaxy_log_to_file('Student is being syncing with Moodle without create an SSO account.');
                }
            }

            $this->syncStudent($student->fresh(), $password);
            // $this->syncStudent($student->fresh(), $this->createSSOIfMissing);
        } catch (\Exception|\Throwable $e) {
            galaxy_log_to_file('syncing student to moodle failed', [tenant('id'), $e->getMessage(), $this->studentId, $e->getTraceAsString()], 'moodle', 'moodle');
        }
    }

    public function syncStudent(Student $student, $password)
    {
        // $student->associatedUserAccount->asMoodleItem($this->update);
        $student->associatedUserAccount->asMoodleItem($this->update, [
            'password' => $password,
        ]);
        /*$student->associatedUserAccount->asMoodleItem($this->update, [
            'isSSOAccountMissing' => $isSSOAccountMissing
        ]);*/
    }

    public function createStudentLoginAccount(Student $student, $password)
    {
        /* TODO:
        - Create login record for the student.
        - Add the login details to the keycloak so the student can sso
         */
        galaxy_log_to_file('Create Student Login Account for Galaxy', ['email' => $student->email, 'password' => $password]);

        $user = new Users([
            'college_id' => $student->college_id,
            'username' => $student->generated_stud_id,
            'name' => $student->first_name.' '.$student->family_name,
            'email' => $student->email,
            'phone' => $student->phone,
            'mobile' => $student->mobile,
            'security_question' => '',
            'security_answer' => '',

        ]);
        $this->password = bcrypt($password); // bcrypt(Str::random(8));
        $user->password = $this->password;
        $user->role_id = Roles::TYPE_STUDENT;
        $user->save();

        /* TODO: I don't know why this is needed and calling model as a service is so BAD. */
        $objUserRoleType = new UserRoleType;
        $objUserRoleType->saveStudentRoleType(Roles::TYPE_STUDENT, $user->id);
    }

    public function createSSOAccount(Users $user, $password)
    {
        /* TODO:
        - Add the user login details to the keycloak if not already so the user can sso
         */
        $name = $user->first_last_name;
        galaxy_log_to_file('Create SSO account using SyncUserToKeycloak :', ['email' => $user->email, 'password' => $password]);

        dispatch(new SyncUserToKeycloak((IdentityCreated::FromArray([
            'firstName' => @$name[0],
            'lastName' => @$name[1],
            'email' => $user->email,
            'password' => $password, // $this->password ?? Str::random(8),
            'temporaryPassword' => true,
            'notify' => true,
            'user_id' => $user->id,
        ]))));
    }
}
