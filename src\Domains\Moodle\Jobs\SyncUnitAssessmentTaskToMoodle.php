<?php

namespace Domains\Moodle\Jobs;

use App\Model\v2\AssessmentTask;
use App\Model\v2\AssessmentTaskUnit;
use App\Model\v2\SubjectUnits;
use Domains\Moodle\DTO\SyncParams;
use Domains\Moodle\Facades\Moodle;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class SyncUnitAssessmentTaskToMoodle implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 1;

    /**
     * Create a new job instance.
     */
    public function __construct(public $assessmentTaskUnitId, public ?SyncParams $params = null) {}

    /**
     * Execute the job.
     */
    public function handle(): void
    {

        if (! Moodle::isConnected()) {
            return;
        }

        try {
            $assessmentTaskUnit = AssessmentTaskUnit::findOrFail($this->assessmentTaskUnitId);
            $assessmentTask = $assessmentTaskUnit->assessmentTask;
            $subjectUnit = SubjectUnits::findOrFail(@$this->params->subject_unit_id);

            if ($assessmentTask && $subjectUnit) {

                $response = Moodle::assignments()->getAll($subjectUnit->getMoodleId());

                // dd($response);
                info('assignment list response', [$response]);
                if (@$response['success'] && @$response['assignments'] && is_array($response['assignments'])) {
                    $assignments = collect($response['assignments']);
                } else {
                    $assignments = collect([]);
                }

                $assignment = $assignments->first(fn ($item) => $item->name == $assessmentTask->task_name);

                if ($assignment) {
                    $moodleItem = $assessmentTaskUnit->moodleItem()->firstOrCreate([
                        'key' => 'moodle',
                    ]);
                    $assessmentTaskUnit->saveMoodleItem($moodleItem, $assignment);

                    return;
                }
            }

            // $taskName = $assessmentTask->assessmentTask->task_name;
            // $assessmentTaskId = $assessmentTask->assessment_task_id;
            // $assessmentUnitId = @$this->params->subject_unit_id;

            // TODO:: Check if the assignment already exists in Moodle with same parent unit
            /*$response = Moodle::assignments()->getByField('idnumber', $assessmentUnitId);
            $hasSameUnit = $this->checkSameUnitId($assessmentUnitId, $response);
            $hasSameParentUnit = $this->hasSameParentUnit($assessmentTask);
            if($response->count() || !$hasSameUnit){
                $moodleItem = $assessmentTask->moodleItem()->firstOrCreate([
                    'key' => 'moodle'
                ]);
                $assessmentTask->saveMoodleItem($moodleItem, $response->first());
            } else {
                $assessmentTask->asMoodleItem(false, $this->params);
            }*/

            $assessmentTaskUnit->asMoodleItem(false, $this->params);
            // info('Assessment synced '. $this->assessmentTaskUnitId);
            // dd($assessmentTask->moodleItem()->first());

            info('Assessment synced : '.$this->assessmentTaskUnitId);
        } catch (\Exception|\Throwable $e) {
            // dd($e);
            galaxy_log_to_file('syncing assessment task to moodle failed', [tenant('id'), $e->getMessage(), $this->assessmentTaskUnitId], 'moodle');
        }
    }

    public function checkSameUnitId($assessmentUnitId, $response)
    {
        $unitResponse = Moodle::assignments()->getByField('course_id', $assessmentUnitId);
        $moodleUnitId = collect($unitResponse)
            ->first(fn ($item) => isset($item->id))
            ->id ?? null;

        $unitId = collect($response)
            ->first(fn ($item) => isset($item->categoryid))
            ->categoryid ?? null;

        return $moodleUnitId == $unitId;
    }

    public function hasSameParentUnit($assessmentTask)
    {
        $subjectUnits = SubjectUnits::where([
            'course_id' => $this->params->course_id,
            'synced_for' => $assessmentTask->unit_id,
        ])->first();

        $subjectUnitId = $subjectUnits->id;

        return $subjectUnitId == $this->params->subject_unit_id;
    }
}
