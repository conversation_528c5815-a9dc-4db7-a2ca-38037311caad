<?php

namespace Domains\Moodle\Jobs;

use App\Users;
use Domains\Moodle\Entities\User;
use Domains\Moodle\Facades\Moodle;
use Domains\Moodle\Moodle as MoodleMoodle;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use SSO\Events\IdentityCreated;
use SSO\Jobs\SyncUserToKeycloak;

class SyncUserInfoFromMoodle implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create a new job instance.
     */
    public function __construct(public $userId, public $update = false) {}

    /**
     * Execute the job.
     */
    public function handle(): void
    {

        if (! Moodle::isConnected()) {
            return;
        }

        try {
            $user = Users::findOrFail($this->userId);

            // if ($user->getMoodleId()) {

            //     if ($this->update) {
            //         // dispatch_sync(new SyncUserToMoodle($user->id, true, true));
            //     }
            //     // return;
            // }

            $response = Moodle::users()->getByField('email', $user->email);

            galaxy_log_to_file('Moodle user search response', [
                'tenant_id' => tenant('id'),
                'user_id' => $this->userId,
                'user_email' => $user->email,
                'found_users_count' => $response->count(),
            ], 'moodle');

            if ($response->count()) {
                foreach ($response as $moodleUser) {
                    galaxy_log_to_file('Syncing user info from Moodle user', [
                        'tenant_id' => tenant('id'),
                        'user_email' => $user->email,
                        'moodle_username' => $moodleUser->username ?? 'unknown',
                    ], 'moodle');

                    $this->syncUser($user, $moodleUser);
                }

                galaxy_log_to_file('User info sync from Moodle completed successfully', [
                    'tenant_id' => tenant('id'),
                    'user_email' => $user->email,
                    'synced_users_count' => $response->count(),
                ], 'moodle');
            } else {
                galaxy_log_to_file('No matching user found in Moodle', [
                    'tenant_id' => tenant('id'),
                    'user_email' => $user->email,
                ], 'moodle');
            }
        } catch (\Exception|\Throwable $e) {
            // safeDD($e);
            galaxy_log_to_file('syncing user from moodle failed', [tenant('id'), $e->getMessage(), $this->userId], 'moodle');
        }
    }

    public function syncUser(Users $user, User $moodleUser)
    {
        // $user->asMoodleItem($this->update);
        $moodleItem = $user->moodleItem()->firstOrCreate([
            'name' => MoodleMoodle::KEY,
        ]);

        $user->saveMoodleItem($moodleItem, $moodleUser);

        /* Now handle if user has first login to moodle through keycloak before galaxy */
        $ssoId = $user->getSSSOId();

        /* TODO: REVISIT
        Should we update our local sso id with moodle id if it doesn' t match ?
        I think better option is to for update the galaxy sso id to moodle so commenting
        below logic. Rather we reupload latest data to moodle passing update = true to this job.
        */
        // if ($moodleUser->username && (!$ssoId || $ssoId != $moodleUser->username)) {
        //     $user->saveSSOData([
        //         'sso_id' => $moodleUser->username
        //     ]);
        // }

        if ($this->update) {
            dispatch(new SyncUserToMoodle($user->id, true, true));
        }
    }

    public function createSSOAccount(Users $user)
    {
        /* TODO:
        - Add the user login details to the keycloak if not already so the user can sso
         */
        $name = $user->first_last_name;

        $userAr = [
            'firstName' => @$name[0],
            'lastName' => @$name[1],
            'email' => $user->email,
            'password' => $this->password ? $this->password : Str::random(8),
            'temporaryPassword' => true,
            'notify' => true,
            'user_id' => $user->id,
        ];

        Log::info('identity created', $userAr);
        dispatch(new SyncUserToKeycloak(IdentityCreated::FromArray($userAr)));
    }
}
