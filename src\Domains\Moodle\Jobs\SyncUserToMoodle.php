<?php

namespace Domains\Moodle\Jobs;

use App\Exceptions\ApplicationException;
use App\Users;
use Domains\Moodle\DTO\SyncParams;
use Domains\Moodle\Facades\Moodle;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Illuminate\Support\Str;
use SSO\Events\IdentityCreated;
use SSO\Jobs\SyncUserToKeycloak;

class SyncUserToMoodle implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 1;

    /**
     * Create a new job instance.
     */
    public function __construct(public $userId, public $createSSOIfMissing = true, public $update = false, public ?SyncParams $params = null) {}

    /**
     * Execute the job.
     */
    public function handle(): void
    {

        if (! Moodle::isConnected()) {
            return;
        }

        $user = Users::find($this->userId);
        if (! $user) {
            return;
        }
        try {

            if (! $user->getSSSOId()) {
                if ($this->createSSOIfMissing) {
                    $this->createSSOAccount($user);
                    $user = $user->fresh();
                } else {
                    // throw new ApplicationException("User doesnot have associated sso login details.");
                    // Log::info("User does not have associated sso login details.", [$user->toArray()]);
                    Log::info('User is being syncing with Moodle without create an SSO account.');
                    // return;
                }
            }
            // TODO:: check same user exist in moodle
            $response = Moodle::users()->getByField('email', $user->email);
            if ($response->count()) {
                $userEntity = $response->first();
                // TODO:: check username & SSSOId are same then call job UpdateSSOToMoodle
                /*$ssoId = $user->getSSSOId();
                if($ssoId && $ssoId != $userEntity->username){
                    dispatch(new UpdateSSOToMoodle($user->id));
                }*/
                $item = $user->moodleItem()->firstOrCreate([
                    'name' => 'moodle',
                ]);
                $user->saveMoodleItem($item, $userEntity);
            }

            $this->syncUser($user->fresh());
            // $this->syncUserV2($user->fresh(), $this->createSSOIfMissing);
        } catch (\Exception|\Throwable $e) {
            galaxy_log_to_file('syncing user to moodle failed', [tenant('id'), $e->getMessage(), $this->userId, $user->email], 'moodle.failed');
        }
    }

    public function syncUser(Users $user)
    {
        // dd($user);
        $user->asMoodleItem($this->update, $this->params);
        // $user->asMoodleItem($this->update, [ 'isSSOAccountMissing' => $isSSOAccountMissing ]);
    }

    public function createSSOAccount(Users $user)
    {
        /* TODO:
        - Add the user login details to the keycloak if not already so the user can sso
         */
        $name = $user->first_last_name;
        $password = $this->params->password ?? Str::random(8);

        $userAr = [
            'firstName' => @$name[0],
            'lastName' => @$name[1],
            'email' => $user->email,
            'password' => $password,    // $this->password ? $this->password : Str::random(8),
            'temporaryPassword' => false,
            'notify' => true,
            'user_id' => $user->id,
        ];

        Log::info('identity created', $userAr);
        dispatch_sync(new SyncUserToKeycloak(IdentityCreated::FromArray($userAr)));
    }
}
