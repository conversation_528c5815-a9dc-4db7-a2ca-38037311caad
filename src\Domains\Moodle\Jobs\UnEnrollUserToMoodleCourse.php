<?php

namespace Domains\Moodle\Jobs;

use App\Model\v2\CourseBatch;
use App\Model\v2\Student;
use App\Model\v2\StudentSubjectEnrolment;
use App\Model\v2\StudentUnitEnrollment;
use Domains\Moodle\Entities\Course;
use Domains\Moodle\Facades\Moodle;
use Domains\Moodle\Models\MoodleItem;
use Domains\Moodle\Moodle as MoodleMoodle;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class UnEnrollUserToMoodleCourse implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 1;

    public StudentSubjectEnrolment $enrollment;

    public function __construct(public $studentSubjectEnrolmentId) {}

    public function handle(): void
    {

        if (! Moodle::isConnected()) {
            return;
        }

        try {

            $this->enrollment = StudentSubjectEnrolment::findOrFail($this->studentSubjectEnrolmentId);

            $meta = $this->enrollment->getMeta(StudentSubjectEnrolment::MOODLE_META_KEY);
            if (! isset($meta['sync_status']) || $meta['sync_status'] != 1) {
                return;
            }

            $student = @$this->enrollment->student->associatedUserAccount;

            $unit = $this->enrollment->unit;
            $course = $this->enrollment->course;
            /*$courseBatch = CourseBatch::firstOrCreate([
                'unit_id' => $this->enrollment->unit_id,
                'batch' => $this->enrollment->batch
            ]);*/
            $subjectUnit = $this->enrollment->subjectUnitRevised()->where('course_id', $this->enrollment->course_id)->first();
            /*info('enroll user', [
                'course_id'     => $course->id,
                'subjectUnitId' => $subjectUnit->id,
                'unit_id'       => $this->enrollment->unit_id,
                'batch'         => $this->enrollment->batch,
                'course-batch'  => $courseBatch->id
            ]);*/

            $moodleUser = $student->asMoodleItem();
            if (! $moodleUser) {
                return;
            }

            $moodleCourse = $subjectUnit->asMoodleItem();
            if (! $moodleCourse) {
                return;
            }

            /*$moodleCourseBatch = $courseBatch->asMoodleItem();
            if(!$moodleCourseBatch){
                return;
            }*/

            $logTitle = sprintf('unit_unenroll_%d_%d', $student->id, $course->id);
            $unEnrollStatus = $this->unEnrollUserToCourse($moodleUser, $moodleCourse, $logTitle);

            if ($unEnrollStatus) {
                StudentUnitEnrollment::where('student_subject_enrollment_id', $this->studentSubjectEnrolmentId)->delete();
                StudentSubjectEnrolment::destroy($this->studentSubjectEnrolmentId);
            }

        } catch (\Exception|\Throwable $e) {
            galaxy_log_to_file('unEnrolling user to moodle course failed', [tenant('id'), $e->getMessage(), $this->studentSubjectEnrolmentId]);
        }
    }

    public function unEnrollUserToCourse(MoodleItem $moodleUser, MoodleItem $moodleCourse, $logTitle)
    {

        activity()
            ->on($moodleCourse)
            ->log('Un-Enroll data with Moodle');

        try {
            // Moodle::enrolments()->unenrolUser($moodleUser->getSyncId(), $moodleCourse->getSyncId(), MoodleMoodle::ROLE_STUDENT);
            Moodle::request('enrol_manual_unenrol_users', [
                'enrolments' => [
                    [
                        'roleid' => MoodleMoodle::ROLE_STUDENT,
                        'userid' => $moodleUser->getSyncId(),
                        'courseid' => $moodleCourse->getSyncId(),
                    ],
                ],
            ]);

            // TODO :: remove courseBatch also
            // $this->enrollment->subjectUnitRevised->getMoodleId();
            // $this->enrollment->setMeta(StudentSubjectEnrolment::MOODLE_META_KEY, []);
            /*$this->enrollment->setMeta(StudentSubjectEnrolment::MOODLE_META_KEY, [
                'sync_status' => 0
            ]);*/

            $studentName = $moodleUser->syncable->name ?? 'Unknown Student';
            $unitCode = $moodleCourse->syncable->unit_code ?? 'Unknown Unit';
            $logDescriptionText = "Un-enroll from moodle for Student ($studentName) with Unit ($unitCode)";

            activity()
                ->on($moodleCourse)
                ->useLog($logTitle)
                ->log($logDescriptionText);

            return true;

        } catch (\Exception $e) {

            activity()
                ->on($moodleCourse)
                ->useLog($logTitle)
                ->log('Moodle item Failed for unit un-enroll with student');

            $message = $e->getMessage();
            galaxy_log_to_file('Un-enroll to moodle failed', [tenant('id'), $moodleUser, $moodleCourse, $message], 'moodle');
            $moodleCourse->syncErrorHandler($message, true);
            throw new \Exception($message);
        }

    }
}
