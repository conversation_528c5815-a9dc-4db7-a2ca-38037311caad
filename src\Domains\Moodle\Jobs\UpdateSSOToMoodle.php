<?php

namespace Domains\Moodle\Jobs;

use App\Users;
use Domains\Moodle\Entities\User;
use Domains\Moodle\Facades\Moodle;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;

class UpdateSSOToMoodle implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    // public $tries = 1;
    // protected $password;

    public function __construct(public $userId, public $update = false) {}

    public function handle(): void
    {

        if (! Moodle::isConnected()) {
            return;
        }

        try {

            /* Check SSO ID to email, If user first time login */
            $user = Users::findOrFail($this->userId);
            $ssoId = $user->getSSSOId();
            if (! $ssoId) {
                return;
            }

            $moodleItem = $user->moodleItem;
            if (! $moodleItem || ! $moodleItem->getSyncId()) {
                return;
            }

            $moodleInfo = @$moodleItem->data['item'];
            if (! $moodleInfo || ! isset($moodleInfo['id'])) {
                return;
            }

            $userEntity = User::LazyFromArray($moodleInfo);

            $ssoId = $user->getSSSOId();
            info('local and moodle sso id', [$ssoId, $userEntity->username]);
            if ($ssoId && $userEntity && $userEntity->username != $ssoId) {
                $userEntity->auth = 'oidc';
                $userEntity->username = $ssoId;
                $userEntity->email = $user->email;

                $requestEntity = User::LazyFromArray([
                    'id' => $userEntity->id,
                    'auth' => 'oidc',
                    'username' => $ssoId,
                    'email' => $user->email,
                    'preferences' => [
                        [
                            'type' => 'auth_forcepasswordchange',
                            'value' => 0,
                        ],
                    ],
                ]);

                Moodle::users()->update($requestEntity);
                $user->saveMoodleItem($moodleItem, $userEntity);
            }
        } catch (\Exception|\Throwable $e) {
            // dd($e);
            galaxy_log_to_file('Failed to update user data on Moodle.', [tenant('id'), $e->getMessage(), $this->userId, $e->getTraceAsString()], 'moodle', 'moodle');
        }
    }
}
