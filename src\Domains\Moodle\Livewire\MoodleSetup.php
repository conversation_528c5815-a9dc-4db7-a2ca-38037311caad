<?php

namespace Domains\Moodle\Livewire;

use App\Events\ShortCourseEnrolledEvent;
use App\Exceptions\ApplicationException;
use App\Model\v2\AssessmentTask;
use App\Model\v2\AssessmentTaskUnit;
use App\Model\v2\CourseBatch;
use App\Model\v2\Courses;
use App\Model\v2\CourseSubject;
use App\Model\v2\Student;
use App\Model\v2\StudentCourses;
use App\Model\v2\StudentSubjectEnrolment;
use App\Model\v2\StudentUnitEnrollment;
use App\Model\v2\SubjectUnits;
use App\Model\v2\Timetable;
use App\Model\v2\TimetableDetail;
use App\Model\v2\UserAuditLog;
use App\Users;
use Domains\Moodle\DTO\Settings;
use Domains\Moodle\DTO\SyncParams;
use Domains\Moodle\Entities\Assignment;
use Domains\Moodle\Entities\CalendarEvent;
use Domains\Moodle\Entities\CourseGroup;
use Domains\Moodle\Facades\Moodle;
use Domains\Moodle\Jobs\EnrollUserToMoodleCourse;
use Domains\Moodle\Jobs\RemoveAssignmentFromMoodle;
use Domains\Moodle\Jobs\RemoveCourseFromMoodle;
use Domains\Moodle\Jobs\RemoveCourseUnitFromMoodle;
use Domains\Moodle\Jobs\SyncAllCoursesFromMoodle;
use Domains\Moodle\Jobs\SyncAllUsersFromMoodle;
use Domains\Moodle\Jobs\SyncBatchTimetableToMoodle;
use Domains\Moodle\Jobs\SyncCourseSubjectToMoodle;
use Domains\Moodle\Jobs\SyncCourseToMoodle;
use Domains\Moodle\Jobs\SyncCourseUnitToMoodle;
use Domains\Moodle\Jobs\SyncStudentCourseUnitGradeReportFromMoodle;
use Domains\Moodle\Jobs\SyncStudentToMoodle;
use Domains\Moodle\Jobs\SyncTimetableDetailToMoodle;
use Domains\Moodle\Jobs\SyncUserInfoFromMoodle;
use Domains\Moodle\Jobs\SyncUserToMoodle;
use Domains\Moodle\Jobs\UpdateSSOToMoodle;
use Domains\Moodle\Models\MoodleConfig;
use Domains\Moodle\Models\MoodleItem;
use Domains\Moodle\Moodle as MoodleMoodle;
use Exception;
use Illuminate\Support\Facades\Artisan;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Str;
use Livewire\Attributes\Computed;
use Livewire\Component;
use Support\Traits\LivewireAlert;
use Zoha\Meta\Models\Meta;

class MoodleSetup extends Component
{
    use LivewireAlert;

    public MoodleConfig $appSetting;

    public Settings $config;

    public $form = [
        'name' => '',
        'url' => '',
        'token' => '',
    ];

    public $state = [
        'old_connection_modal_open' => false,
    ];

    #[Computed()]
    public function oldConnections()
    {
        return $this->appSetting && @$this->appSetting->data['archived_config'] ? $this->appSetting->data['archived_config'] : [];
    }

    #[Computed()]
    public function docsData()
    {
        return [
            'application_id' => config('galaxy.moodle.keycloak.client_id'),
            'client_secret' => config('galaxy.moodle.keycloak.client_secret'),
            'authorization_endpoint' => rtrim(config('galaxysso.config.id_provider_url'), '/').'/'.ltrim(config('galaxy.moodle.keycloak.authorization_path'), '/'),
            'token_endpoint' => rtrim(config('galaxysso.config.id_provider_url'), '/').'/'.ltrim(config('galaxy.moodle.keycloak.token_path'), '/'),
            'scopes' => config('galaxy.moodle.keycloak.scopes'),
            'moodle_url' => $this->config->url ?? 'MOODLE_URL',
            'galaxy_url' => url('/'),
        ];
    }

    public function mount()
    {
        // dd(Moodle::request('core_course_get_courses', []));
        $this->init();
    }

    public function init()
    {
        $this->appSetting = MoodleConfig::Settings(false);
        $this->config = Settings::lazyFromArray(@$this->appSetting->data['config'] ?? []);
        // dd($this->config);
        // $this->form = mergeFirstArray($this->form, $this->config->toArray(false));
    }

    public function getIsConnectedProperty()
    {
        return ! is_null($this->config->connected) && (int) $this->config->connected;
    }

    public function getIsFilledProperty()
    {
        // return $this->config['name'] && $this->form['url'] && $this->form['token'];
        return $this->config->name && $this->config->url && $this->config->token;
    }

    public function saveConfig()
    {
        $this->validate([
            'config.url' => 'required|url',
            'config.name' => 'required',
            'config.token' => 'required',
        ]);

        // dd($this->config);

        $this->appSetting->updateJsonField([
            'config' => $this->config->toArray(true),
        ], 'data');

        $this->alert('success', 'Saved');
    }

    public function checkSameCategoryId($courseSubjectUnit, $unitResponse)
    {
        $courseResponse = Moodle::categories()->getAll('name', $courseSubjectUnit->course->course_code.':'.$courseSubjectUnit->course->course_name);
        $courseCategoryId = collect($courseResponse)
            ->first(fn ($item) => isset($item->id))
            ->id ?? null;

        $unitCategoryId = collect($unitResponse)
            ->first(fn ($item) => isset($item->categoryid))
            ->categoryid ?? null;

        dd([
            'courseCategoryId' => $courseCategoryId,
            'unitCategoryId' => $unitCategoryId,
        ]);

        return $courseCategoryId == $unitCategoryId;
    }

    public function test()
    {
        $studentId = 226;
        dispatch(new SyncStudentToMoodle($studentId));
        dd('Testing for SyncStudentToMoodle');

        /* Check user exist or not before sync */
        /*$studentId = 226;
        $password = Str::random(8).'_$';
        $student = Student::where('id', $studentId)->has('associatedUserAccount')->first();
        if ($student->associatedUserAccount) {
            $user = $student->associatedUserAccount;
            $response = Moodle::users()->getByField('email', $user->email);
            $response = Moodle::users()->getByField('email', $user->email);
            if ($response->count()) {
                $userEntity = $response->first();
                //dd(['userEntity' => $userEntity]);

                $item = $user->moodleItem()->firstOrCreate([
                    'name' => 'moodle'
                ]);
                $student->associatedUserAccount->saveMoodleItem($item, $userEntity);
            }else{
                $student->associatedUserAccount->asMoodleItem($this->update, [
                    'password' => $password
                ]);
            }
        }*/

        // dispatch_sync(new SyncUserToMoodle($user->id, false, true, SyncParams::LazyFromArray([
        //            'password' => $password
        //        ])));

        // $studentId = 226;   //226 : Marina Sauer (<EMAIL>)  //207
        // dispatch(new SyncStudentToMoodle($studentId,true, true));
        // dd('Testing for SyncStudentToMoodle');

        // dispatch(new UpdateSSOToMoodle($studentId));
        // dd('Testing for UpdateSSOToMoodle');

        /*$ssoId = "b4494109-f514-4763-aaf5-0c8b75572923";
        $instance = app(\SSO\SSO::class);
        $tokenUrl = $instance->tokenUrl();

        $token = Http::asForm()->post($tokenUrl, [
            'client_id' => 'admin-cli',
            'grant_type' => 'password',
            'username' => config('galaxysso.config.id_provider_admin.username'),
            'password' => config('galaxysso.config.id_provider_admin.password'),
        ])->json()['access_token'];

        $url = config('galaxysso.config.id_provider_url') . '/realms/' . config('galaxysso.config.id_provider_realm') . '/users/' . $ssoId;
        dd($url);
        $response = Http::withToken($token)->get($url);

        $userData = $response->json();
        dd($userData->email);*/

        /* Assessment sync & remove idnumber */
        $studentCourseId = 924;
        $studentId = 341;
        $userId = 167;          // <EMAIL>
        $courseId = 72;         // CHC50113
        $subjectUnitId = 670;   // rto_subject_units & sync_for = 221 (FNSACC601)
        $unitId = 221;          // rto_subject_unit (FNSACC601)
        $groupId = 1;
        $courseSubjectId = 57;

        $course = Courses::findOrFail($courseId);
        // dd($course->moodleItem);      // syncable_id=72 & sync_item_id=128
        // $response = Moodle::categories()->getAll('name', $course->course_code . ':' . $course->course_name);
        // dd($response);

        foreach ($course->courseUnits as $unit) {
            // dd($unit->moodleItem);      // syncable_id=670 & sync_item_id=899

            $courseSubjectUnit = SubjectUnits::findOrFail($unit->id);
            foreach ($courseSubjectUnit->assessmentUnitTasks as $unitTask) {

                $assessment = AssessmentTaskUnit::findOrFail($unitTask->id);    // 127

                $subjectUnits = SubjectUnits::where([
                    'course_id' => $courseId,
                    'synced_for' => $assessment->unit_id,
                ])->first();

                $subjectUnitId = $subjectUnits->id;
                dd($subjectUnitId);

                $moodleCourseId = $assessment->moodleItem->sync_item_id ?? null;
                // dd($moodleCourseId);
                // dd(Moodle::assignments());
                $response = Moodle::assignments()->getByField('idnumber', $unitTask->id);
                // $response = Moodle::assignments()->getByField('name', $taskName);
                /*$response = Moodle::request('mod_assign_get_assignments', [
                    //'courseids' => [$moodleCourseId],
                    'course_id' => 128, //899 //[$moodleCourseId],
                ]);*/
                dd($response);

            }

            // dd($unit->id);
            $courseSubjectUnit = SubjectUnits::findOrFail($unit->id);
            $unitResponse = Moodle::courses()->getByField('shortname', $courseSubjectUnit->unit_code.'-'.@$courseSubjectUnit->subject->subject_code);
            $aa = $this->checkSameCategoryId($courseSubjectUnit, $unitResponse);
            dd($aa);

            /*$filtered = collect($response)->filter(function ($item) {
                return isset($item->categoryid);
            });
            $moodleCourse = $filtered->first();
            $categoryId = $moodleCourse->categoryid ?? null;*/

            $categoryId = collect($response)
                ->first(fn ($item) => isset($item->categoryid))
                ->categoryid ?? null;
            dd($categoryId);

            $categoryId = collect($response)->first(function ($item) {
                $categoryResponse = Moodle::request('core_course_get_categories', [
                    'criteria' => [
                        ['key' => 'id', 'value' => $item->categoryid],
                    ],
                ]);
                info('categoryResponse', [$categoryResponse]);

                return $categoryResponse[0] ?? null;
            });
            dd($categoryId);

            if ($moodleCourse) {
                $categoryId = $moodleCourse['categoryid']; //
                dump("Moodle category ID for {$shortname} is: {$categoryId}");

                // You can fetch the category details if needed:
                $categoryDetails = Moodle::categories()->getById($categoryId);
                dump('Category name: ', $categoryDetails['name'] ?? 'N/A');
            } else {
                dump("Moodle course not found for unit: {$shortname}");
            }

            dd(11);

            // echo "$unit->synced_for<hr/>";continue;
            if ($unit->synced_for == $courseSubjectUnitId) {
                $courseSubjectUnit = SubjectUnits::findOrFail($unit->id);

                foreach ($courseSubjectUnit->assessmentUnitTasks as $unitTask) {
                    // dd($unitTask);
                }
            }
        }
        dd('EXIT');

        // dispatch_sync(new SyncStudentCourseUnitGradeReportFromMoodle(17, 8));
        // dd(1);
        $courseId = 16;
        $courseSubjectUnitId = 199;     // 6;
        $assessmentTaskUnitId = 637;    // 635;
        $assessmentTaskId = 628;    // 635;

        $course = Courses::findOrFail($courseId);
        // dd($course->courseUnits);
        // $course->asMoodleItem();

        foreach ($course->courseUnits as $unit) {
            // dd($unit);
            // echo "$unit->synced_for<hr/>";continue;
            if ($unit->synced_for == $courseSubjectUnitId) {
                $courseSubjectUnit = SubjectUnits::findOrFail($unit->id);
                // dd($courseSubjectUnit->assessmentUnitTasks);
                // $courseSubjectUnit->asMoodleItem();

                foreach ($courseSubjectUnit->assessmentUnitTasks as $unitTask) {
                    // dd($unitTask);
                    echo $unitTask->id;
                    if ($unitTask->id == $assessmentTaskUnitId) {
                        $assessmentTask = AssessmentTaskUnit::findOrFail($unitTask->id);
                        // $assessmentTask = AssessmentTask::findOrFail($assessmentTaskUnitId);
                        // dd($assessmentTask);
                        // dd($assessmentTask->moodleItem()->first()->toArray());
                        dd($assessmentTask->moodleItem);
                    }
                }
            }
        }
        dd('End');

        // $timetable = \App\Model\v2\UnitModule::whereHas('subjectLegacy.timetables')->inRandomOrder()->first()->subjectLegacy->timetables->first();
        // dd($timetable->toArray(), $timetable->alldates()->count(), $timetable->alldates()->orderBy('timetable_date', 'ASC')->get()->first()->toArray());
        // $course = Courses::inRandomOrder()->first();
        // $course = Courses::find(22);
        // dd(CourseBatch::where('batch', 'FNS40217_21T2_Anoj_U2')->get());

        // $timetable = Timetable::whereHas('unitModule.subjectUnitRevised')->first();
        // dispatch_sync(new SyncBatchTimetableToMoodle(CourseBatch::firstOrCreate([
        //     'batch' => $timetable->batch,
        //     'unit_id' => $timetable->unitModule->id
        // ])->id));
        // dispatch_sync(new SyncBatchTimetableToMoodle(147));

        // $timetableDetail = TimetableDetail::whereHas('timetable.unitModule.subjectUnitRevised')->inRandomOrder()->first();

        // // dd($timetableDetail);
        // if ($timetableDetail) {
        //     dispatch_sync(new SyncTimetableDetailToMoodle($timetableDetail->id));
        // }
        // dd(collect(Moodle::request('ltiservice_competency_list_competencies', [
        //     // 'assignmentids' => [61]
        //     'pagenum' => 1
        // ])));
        // dd(collect(Moodle::request('mod_assign_get_grades', [
        //     'assignmentids' => [61]
        // ])));

        // dd(collect(Moodle::request('core_grades_get_grade_tree', [
        //     'courseid' => 55
        // ])));

        /* GET COURSE CONTENT TO GET CMID FOR grade definition */
        // dd(collect(Moodle::request('core_course_get_contents', [
        //     'courseid' => 55
        // ])));
        // dd(collect(Moodle::request('core_grading_get_definitions', [
        //     'cmids' => [61, 62],
        //     'areaname' => 'mod_assign',
        //     'activeonly' => 0,

        // ])));

        //     dd(collect(Moodle::request('core_competency_list_competencies', ['filters' => [[
        //         "column" =>"competencyframeworkid",
        //   "value"=> "1"   // The ID of the competency framework you are filtering by
        //     ]]]))->filter(function($item){
        //         return $item/* ['scaleconfiguration'] != null */;
        //     }));

        /* ADD NEW EXTERNAL FUNCTIONALITY FROM GALAXY. */

        // dd(AssessmentTaskUnit::havingMoodleId(266)->first());

        // dd(Moodle::request('ltiservice_competency_delete_assignment', [
        //     'id' => 322,
        //     'parentid' => 311
        // ]));
    }

    public function ShortCourseEnrolled()
    {

        $studentCourseId = 1011;
        event(new ShortCourseEnrolledEvent($studentCourseId));
        dd('Testing for Short course Enrolled Event for StudentCourseID : '.$studentCourseId);

        $studentId = 341;
        $courseId = 213;
        $course = Courses::findOrFail($courseId);
        foreach ($course->courseUnits as $unit) {
            // dd($unit->id); // 1529
            // $courseSubjectUnit = SubjectUnits::findOrFail($unit->id);

            $data = StudentCourses::with([ // "student", "course", "campus",
                'intake',
                'galaxyInvoices',
            ])->find($studentCourseId);
            // dd($data);

            $whereArr = [
                'student_id' => $data->student_id,
                'student_course_id' => $data->id,
                'course_id' => $data->course_id,
                'subject_id' => $unit->course_subject_id, // $courseSubjectUnit->course_subject_id,
                'unit_id' => $unit->synced_for,         // $courseSubjectUnit->id, //$courseSubjectUnit->unit_id
            ];

            $enrolment = StudentSubjectEnrolment::where($whereArr)->first();

            if (! $enrolment) {
                $dataArr = [
                    'college_id' => $data->course->college_id,
                    'student_id' => $data->student_id,
                    'student_course_id' => $data->id,
                    'course_id' => $data->course_id,
                    'semester_id' => 0,
                    'term' => 1,
                    'enroll_type' => 'unit',
                    'course_stage' => 0,
                    'subject_id' => $unit->course_subject_id, // $courseSubjectUnit->course_subject_id,
                    'unit_id' => $unit->synced_for,        // $courseSubjectUnit->id, //$courseSubjectUnit->unit_id
                    'subject_attempt' => 1,
                    'batch' => 'no_batch',
                    'activity_start_date' => $data->start_date,
                    'activity_finish_date' => $data->finish_date,
                    'study_reason' => '@@',
                    'vanue_location' => 0,
                    'funding_source_state' => '',
                    'funding_source_nat' => '',
                    'delivery_mode' => null,
                    'outcome_identifier' => '',
                    'final_outcome' => null,
                    'mark_outcome' => null,
                    'grade' => null,
                    'vet_in_school' => '',
                    // 'created_by'            => @$data->galaxyInvoices?->user_id,
                    // 'updated_by'            => @$data->galaxyInvoices?->user_id
                ];
                $enrolment = StudentSubjectEnrolment::create($dataArr);
            }

            // dd($dataArr);
            // dd($enrolment);
            if ($enrolment) {

                $whereSubjectEnrollArr = [
                    'student_subject_enrollment_id' => $enrolment->id,
                    'unit_id' => $unit->id,
                ];

                $subjectEnroll = StudentUnitEnrollment::where($whereSubjectEnrollArr)->first();

                if (! $subjectEnroll) {
                    $unitDataArr = [
                        'student_subject_enrollment_id' => $enrolment->id,
                        'college_id' => $enrolment->college_id,
                        'compentency' => 'Enrolled',
                        'unit_id' => $unit->synced_for, // $courseSubjectUnit->id, //$courseSubjectUnit->unit_id
                        'schedule_hours' => 0,
                        'delivery_mode' => '',
                        'tution_fee' => '',
                        'competency_date' => null,
                        'amount_paid' => 0,
                        'comment' => '',
                        'attended_hour' => 0,
                        'study_from' => null,
                        'study_to' => null,
                        'created_by' => null,
                        'updated_by' => null,
                    ];
                    StudentUnitEnrollment::create($unitDataArr);
                }
                dispatch(new EnrollUserToMoodleCourse($enrolment->id));
            }
        }

        dd($enrolment->id.' : Testing for Short course Enrolled Event');
    }

    public function patchMoodlePlugin()
    {
        dd(collect(Moodle::request('ltiservice_competency_update_external_service', [
            'functions' => [
                'core_course_delete_categories',
                'core_course_delete_courses',
                'ltiservice_competency_delete_assignment',
                // 'core_calendar_create_calendar_events'
            ],
        ])));
    }

    public function syncCourse()
    {
        // dd(Courses::has('courseUnits.assessmentUnitTasks')->count());
        // $res = Courses::with()->where('id', 16)->first();
        $course = Courses::find(72);
        //        dd($course->courseUnits);
        //        $unitIds = $course->courseUnits->pluck('id')->toArray();
        //        dd($unitIds);

        // dd($course->isSyncedToMoodle());
        // $res = $course->moodleItem()->first()->getSyncId();

        // dd($course->moodleItem()->first()->toArray());
        // dd(1);

        $course = Courses::where('rto_courses.id', 16)
            ->has('courseUnits.assessmentUnitTasks')
            ->selectRaw('rto_courses.*')
            ->notSyncedWithMoodle()->first();

        // dd(Assignment::FromAssessmentTaskUnit(AssessmentTaskUnit::inRandomOrder()->first()));

        // dd(Courses::has('courseUnits.assessmentUnitTasks')
        // ->selectRaw("rto_courses.*")
        // ->notSyncedWithMoodle()->get());
        // dd($course->id);
        // $course = Courses::find(5);
        // dd(Moodle::request('core_competency_get_competency', ['id' => 1]));
        // $course->asMoodleItem(true);
        if ($course) {

            dispatch_sync(new SyncCourseToMoodle($course->id, true, SyncParams::LazyFromArray([
                'course_id' => $course->id,
            ])));

            dd(['sync completed for', $course->course_code, 'moodle units link' => $this->config->url.'/course/management.php?categoryid='.$course->fresh()->getMoodleId()]);
        } else {
            dd('course not found.');
        }
        // $subject = CourseSubject::inRandomOrder()->first();
        // dispatch_sync(new SyncCourseSubjectToMoodle($subject->id));
    }

    public function removeCourseFromMoodle()
    {
        $course = Courses::syncedWithMoodle()->selectRaw('rto_courses.*')->inRandomOrder()->first();
        if (! $course) {
            dd('Course not found.');
        }

        // dd($course->id);

        dispatch_sync(new RemoveCourseFromMoodle($course->id));
    }

    public function removeCourseSubjectUnitFromMoodle()
    {
        $unit = SubjectUnits::syncedWithMoodle('rto_subject_units')->selectRaw('rto_subject_units.*')->inRandomOrder()->first();
        if (! $unit) {
            // dd("Course subject unit not found.");
            return;
        }

        // dd($unit->id);

        dispatch_sync(new RemoveCourseUnitFromMoodle($unit->id));
    }

    public function removeUnitAssignmentFromMoodle()
    {
        $task = AssessmentTaskUnit::syncedWithMoodle('rto_assessment_tasks_units')->selectRaw('rto_assessment_tasks_units.*')->inRandomOrder()->first();
        if (! $task) {
            // dd("Course subject unit not found.");
            return;
        }

        // dd($course->id);

        dispatch_sync(new RemoveAssignmentFromMoodle($task->id));
    }

    public function syncAllCourses()
    {
        dispatch_sync(new SyncAllCoursesFromMoodle);
    }

    public function syncAllCoursesToMoodle()
    {
        Artisan::call('galaxy:moodle-sync-courses', [
            'tenant' => tenant('id'),
        ]);
        $this->alert('success', 'Courses are being synced in the background.');
    }

    public function syncStudent()
    {
        // 224 : Leland Collier  // UserID : 265
        // 226 : Marina Sauer
        // 341 : Batsal Pokhrel
        try {
            // $student = Student::where('id', 224)->has('associatedUserAccount')->first();
            $student = Student::has('associatedUserAccount')->inRandomOrder()->first();
            // dd($student->associatedUserAccount->id);
            // $student = Student::find(33);
            if (! $student->associatedUserAccount) {
                throw new ApplicationException('Student does not have associated login account.');
            }
            // $Student = Students::find(37);
            // dd($student->associatedUserAccount->id);
            $this->syncUser($student->associatedUserAccount->id);
            // $student->asMoodleItem();
            // dispatch_sync(new SyncStudentToMoodle($student->id, true));
            // dd("user synced to moodle", [
            //     $student->generated_stud_id,
            //     $student->associatedUserAccount->email,
            //     $this->config->url . '/user/profile.php?id=' . $student->associatedUserAccount->fresh()->getMoodleId()
            // ]);
        } catch (\Exception $e) {
            $this->alert('error', $e->getMessage());
        }
    }

    public function syncAllStudentsToMoodle()
    {
        Artisan::call('galaxy:moodle-sync-students', [
            'tenant' => tenant('id'),
        ]);
        $this->alert('success', 'Studnets are being synced in the background.');
    }

    public function syncStudentsFromMoodle()
    {
        dispatch_sync(new SyncAllUsersFromMoodle);
    }

    public function syncUser($id = null)
    {
        // $user = Users::findOrFail(183); //265
        if ($id) {
            $user = Users::findOrFail($id);
        } else {
            $user = Users::inRandomOrder()->first();
            // $user = Users::where('email', '<EMAIL>')->first();
        }
        // $Student = Students::find(37);

        // $user->asMoodleItem(true);
        $password = Str::random(8).'1_$';
        // dispatch_sync(new SyncUserToMoodle($user->id, false, true, $password));
        dispatch_sync(new SyncUserToMoodle($user->id, true, true, SyncParams::LazyFromArray([
            'password' => $password,
        ])));

        dd('Login to moodle using', [
            'moodle_url' => $this->config->url.'/user/profile.php?id='.$user->fresh()->getMoodleId(),
            'email' => $user->email,
            'password' => $password,
        ]);
    }

    public function updateUserUsername()
    {
        $user = Users::havingMoodleId(666)->first();
        // dd($user);

        $user->email = 'new'.$user->email;
        $user->saveSSOData([
            'sso_id' => Str::uuid(),
        ]);

        dispatch_sync(new UpdateSSOToMoodle($user->id, true));

        // $user->saveSSOData([
        //     'sso_id' => null,
        // ]);
    }

    public function syncUserInfoFromMoodle()
    {
        $user = Users::inRandomOrder()->syncedWithMoodle('rto_users')->selectRaw('rto_users.*')->first();

        dispatch_sync(new SyncUserInfoFromMoodle($user->id, true));
    }

    public function syncAllUsersToMoodle()
    {
        Artisan::call('galaxy:moodle-sync-users', [
            'tenant' => tenant('id'),
        ]);
        $this->alert('success', 'Users are being synced in the background.');
    }

    public function syncUsersFromMoodle()
    {
        dispatch_sync(new SyncAllUsersFromMoodle);
    }

    public function syncStudentCourseGradeReportFromMoodle()
    {

        $moodleUserId = 5;
        $moodleCourseId = 37;   // Unit code is ICTICT526 & Course code is ICT50120

        $user = Users::havingMoodleId($moodleUserId)->first();
        // dd($user->associatedStudent);
        $subjectUnit = SubjectUnits::havingMoodleId($moodleCourseId)->first();
        // dd($subjectUnit);
        dispatch_sync(new SyncStudentCourseUnitGradeReportFromMoodle($user->associatedStudent->id, $subjectUnit->id));
    }

    public function createCourseMemberGroup()
    {
        /* Course batch sync */
        $courseBatch = CourseBatch::has('subjectUnitRevised')->inRandomOrder()->first();
        $courseBatch->subjectUnitRevised->asMoodleItem();
        dd(@$courseBatch->asMoodleItem()->data);
    }

    public function enrollUserToMoodle()
    {

        /* TEST COURSE WITH ENROLLMENT
        ID: 177
        */
        $syncedSubject = SubjectUnits::havingMoodleId(199)->first();
        // dd($syncedSubject->id);
        $enrollment = StudentSubjectEnrolment::/* where('unit_id', $syncedSubject->synced_for) -> */ whereHas('subjectUnitRevised', function ($q) {
            return $q
                // ->where('rto_subject_units', $syncedSubject->id)
                ->whereRaw('rto_subject_units.course_id = rto_student_subject_enrolment.course_id')
                ->has('assessmentUnitTasks');
            // ->whereHas('assessmentUnitTasks', function ($q) {
            //     return $q->syncedWithMoodle('rto_assessment_tasks_units');
            // })
        })
            ->selectRaw('rto_student_subject_enrolment.*')
            // ->where('rto_student_subject_enrolment.unit_id', 275)
            // ->whereMetaNotNull(StudentSubjectEnrolment::MOODLE_META_KEY)
            ->has('student.associatedUserAccount')
            ->inRandomOrder()
            ->whereNotNull('batch')
            ->first();
        // dd($enrollment->id);
        // dd($enrollment->subjectUnitRevised);
        if ($enrollment) {

            dispatch_sync(new EnrollUserToMoodleCourse($enrollment->id));

            $enrollment = $enrollment->fresh();
            dd([
                'batch' => $enrollment->batch,
                'course' => $this->config->url.'/course/view.php?id='.$enrollment->subjectUnitRevised()->where('course_id', $enrollment->course_id)->first()->getMoodleId(),
                'user' => $this->config->url.'/user/profile.php?id='.$enrollment->student->associatedUserAccount->getMoodleId(),
            ]);
        } else {
            dd('enrollment not found.');
        }
    }

    public function createMoodleWebhook()
    {
        /*dd(route('galaxy.moodle.webhook'));

        $moodleUrl = "https://localhost.galaxy.com/webservice/rest/server.php";
        $token = "6tuuUKzBo6L3I5d5";

        $response = Http::withoutVerifying()->post($moodleUrl, [
            'wstoken' => $token,
            'wsfunction' => 'core_grades_get_grades',
            'moodlewsrestformat' => 'json',
            'courseid' => 2, // Example parameter (change as needed)
        ]);
        dd($response->json());
        return $response->json();*/

        $secret = \Illuminate\Support\Str::random(16);
        $response = Moodle::request('ltiservice_competency_add_webhook', [
            'params' => [
                'name' => 'Galaxy360 Webhook',
                'secret' => $secret,
                'url' => route('galaxy.moodle.webhook'),
            ],
        ]);

        // dd($response);
    }

    public function resetItems($type = 'courses,users')
    {
        $in = [];
        $types = explode(',', $type);
        foreach ($types as $type) {
            if ($type == 'courses') {
                $in[] = morphAlias(Courses::class);
                $in[] = $this->strReplaceCustom(morphAlias(SubjectUnits::class));
                $in[] = $this->strReplaceCustom(morphAlias(CourseSubject::class));
                $in[] = $this->strReplaceCustom(morphAlias(AssessmentTaskUnit::class));
                $in[] = $this->strReplaceCustom(morphAlias(CourseBatch::class));
                $in[] = $this->strReplaceCustom(morphAlias(TimetableDetail::class));
            }
            if ($type == 'users') {
                $in[] = morphAlias(Users::class);
            }
        }
        // dd($in);
        MoodleItem::key('moodle')->whereIn('syncable_type', $in)->delete();
        Meta::whereIn('key', [StudentSubjectEnrolment::MOODLE_META_KEY])->delete();
        Users::where('id', '>', 0)->update(['sso_data' => null]);
        // UserAuditLog::where('id', '>', 2)->delete();
    }

    private function strReplaceCustom($str = '')
    {
        // The input string contains double backslashes (\\), which need to be replaced with a single backslash (\).
        if (! empty($str)) {
            return preg_replace('/\\\\+/', '\\', $str);
        }

        return $str;
    }

    public function connect()
    {

        try {

            $this->setupWebhook();
            Moodle::updateMoodleKeycloakIdp();
            $response = Moodle::request('core_course_get_courses', []);
            if (isset($response['errorcode'])) {
                throw new Exception(@$response['message'] ?? 'Cannot connect with moodle.');
            }
            $this->config->connected = 1;
            /*
            NOTE:
            Moodle fires the webhook added webhook event before we can establish the moodle connection flag
            In the webhook controller we set the moodle -> galaxy link as successfull but will be overriden from
            above config array. So before saving we need to create new Settings object from lastest json in db as well as our local config.
            */
            $this->appSetting->fresh()->setSettings(Settings::LazyFromArray(array_merge(MoodleConfig::Settings()->toArray(true), $this->config->toArray(true))));
            // $this->appSetting->updateJsonField([
            //     'config' => $this->config->toArray(true)
            // ], 'data');
            $this->appSetting->restoreItems();
            $this->init();
            $this->alert('success', 'Connected');
        } catch (\Exception $e) {
            $this->alert('error', $e->getMessage());
        }
    }

    public function setupWebhook()
    {
        try {
            // if (@$this->appSetting->data[MoodleConfig::WEBHOOK_SECRET]) {
            //     return;
            // }

            $secret = \Illuminate\Support\Str::random(16);
            // $secret = '59a6dea86dc3e5b81f8043d224e00996';
            $response = Moodle::request('ltiservice_competency_add_webhook', [
                'params' => [
                    'name' => 'Galaxy360 Webhook',
                    'secret' => $secret,
                    'url' => route('galaxy.moodle.webhook'),
                ],
            ]);
            // dd($response);
            if (@$response['secret']) {
                //
                $this->config->webhook_token = $response['secret'];
                // $this->appSetting->updateJsonField([
                //     MoodleConfig::WEBHOOK_SECRET => @$response['secret']
                // ], 'data');
                // $this->init();
            }
            // $this->alert('success', 'Connected');
        } catch (\Exception $e) {
            // dd($e->getMessage());
            galaxy_log_to_file('moodle connection failed', [$e->getMessage(), $e->getTraceAsString()], 'moodle');
            throw new Exception('Moodle webhook plugin initialization failed. Please make sure the webhook plugin is installed and enabled in Moodle.');
        }
    }

    public function syncRandomAssignment()
    {
        try {
            $assessment = AssessmentTaskUnit::selectRaw('rto_assessment_tasks_units.*')
                /* ->notSyncedWithMoodle('rto_assessment_tasks_units') */
                ->has('subjectUnit')->inRandomOrder()->first();
            dd($assessment ? $assessment->asMoodleItem() : 'assessment not found');
        } catch (\Exception $e) {
            dd($e);
        }
    }

    public function disconnect()
    {
        $this->appSetting->archive($this->config);
        $this->config->connected = 0;
        $this->config->webhook_token = null;
        $this->config->webhook_linked = null;
        $this->appSetting->updateJsonField([
            'config' => $this->config->toArray(true),
            // MoodleConfig::WEBHOOK_SECRET => null
        ], 'data');
        $this->init();
        $this->alert('success', 'Disconnected');
    }

    public function applyOldConfig($url)
    {
        $oldConnections = $this->oldConnections();
        if (@$oldConnections && @$oldConnections[$url]) {
            $connection = $oldConnections[$url];
            $this->config = Settings::LazyFromArray($connection['config']);
            $this->saveConfig();
            if (@$connection['mapping'] && is_array($connection['mapping'])) {
                $this->appSetting->updateJsonField([
                    'vet_mapping' => $connection['mapping'],
                ], 'data');
            }
            $this->connect();
            $this->state['old_connection_modal_open'] = false;
            $this->alert('success', 'Old configuration applied.');

            return;
        }

        $this->alert('error', 'Config not found.');
    }

    public function calendarTest()
    {
        $courseId = 214;    // 243;
        $groupId = 1;       // 18;
        $userId = 308;
        $unitId = 199;
        $courseBatchId = 1;
        $batch = '2022_ICTICT526_Sanjay_LAMA';

        $response = Moodle::request('ltiservice_competency_update_external_service', [
            'functions' => [
                // 'core_course_delete_categories',
                // 'core_course_delete_courses',
                // 'ltiservice_competency_delete_assignment',
                'core_calendar_create_calendar_events',
            ],
        ]);

        dispatch_sync(new SyncBatchTimetableToMoodle($courseBatchId));
        exit('Testing Stop');

        $courseBatchData = CourseBatch::has('subjectUnitRevised')
            ->where([
                'unit_id' => $unitId,
                'batch' => $batch,
            ])
            ->first();
        if ($courseBatchData) {
            // dd($courseBatchData->unit);
            $unit = $courseBatchData->unit;
            // dd($unit->subjectLegacy);
            // dd($unit->subjectLegacy->timetables()->count());
            dd($unit->subjectLegacy->timetables()->orderBy('id', 'desc')->limit(1)->pluck('id')->toArray());
            dispatch_sync(new SyncBatchTimetableToMoodle($courseBatchId));
        }
        exit('Stop');

        $users = Users::syncedWithMoodle('rto_users')->with('moodleItem')->limit(5)->get()->toArray();
        // $users = Users::syncedWithMoodle('rto_users')->with('moodleItem')->where('rto_users.id', $userId)->get();
        // $users = Users::findOrFail($userId);
        // dd($users->getMoodleId());

        foreach ($users as $user) {
            // dd($user->moodleItem()->first());
            // dd($user->getMoodleId());
            if ($user->getMoodleId()) {
                Moodle::users()->enrollToCourse($courseId, $user->getMoodleId(), MoodleMoodle::ROLE_STUDENT);
                Moodle::users()->addGroupMembenr($user->getMoodleId(), $groupId);
            }
        }

        $response = Moodle::calendarEvents()->create(
            CalendarEvent::LazyFromArray([
                'courseid' => $courseId,
                'groupid' => $groupId,
                'eventtype' => 'course',
                'timestart' => strtotime('now'),
                'timeduration' => 3600,
                'name' => 'Test Event',
                'description' => 'Test Event Description',
            ])
        );

        // dd($response);

        // $group = Moodle::courses()->createGroup(CourseGroup::LazyFromArray([
        //     'courseid' => $courseId,
        //     'name' => 'Test Group',
        //     'description' => 'Test Group'
        // ]));

        // dd($group);

    }

    public function render()
    {
        return view('galaxy-moodle::setup.index', [
            'title' => 'Moodle',
            'mainmenu' => 'clients',
        ])
            ->layout('components.v2.layouts.onboard', []);
    }

    /* Testing function */
    public function testForUpdatingMoodleEmail()
    {
        /*$ssoId = "b4494109-f514-4763-aaf5-0c8b75572923";
        $instance = app(\SSO\SSO::class);
        $tokenUrl = $instance->tokenUrl();

        $token = Http::asForm()->post($tokenUrl, [
            'client_id' => 'admin-cli',
            'grant_type' => 'password',
            'username' => config('galaxysso.config.id_provider_admin.username'),
            'password' => config('galaxysso.config.id_provider_admin.password'),
        ])->json()['access_token'];

        $url = config('galaxysso.config.id_provider_url') . '/realms/' . config('galaxysso.config.id_provider_realm') . '/users/' . $ssoId;
        dd($url);
        $response = Http::withToken($token)->get($url);

        $userData = $response->json();
        dd($userData->email);*/

        /*dispatch(new SyncCourseUnitToMoodle(
            $subjectUnitId,
            $this->update,
            SyncParams::LazyFromArray([
                'course_id' => $courseId,
                'subject_unit_id' => $subjectUnitId
            ])
        ));*/
        // dd("10-04-2025");

        /* Testing for Sync student to Moodle with create an SSO account & same password for Moodle & Galaxy360 */
        // $studentId = 226;   //226 : Marina Sauer (<EMAIL>)  //404
        // dispatch(new SyncStudentToMoodle($studentId,true, true));
        // dd('Testing for Sync student to Moodle with create an SSO account & same password for Moodle & Galaxy360');

        /* Testing for update username on Moodle when user first login with sso and user already sync with moodle. */
        // Student ID : 226 (Marina Sauer : <EMAIL>)
        // User ID    : 397
        // Moodle ID  : 57

        /*dd(Moodle::request('core_user_get_users', [
            'criteria' => [
                //['key' => 'idnumber', 'value' => $user->moodleItem->sync_item_id]
                //['key' => 'email', 'value' => '<EMAIL>']
                ['key' => 'username', 'value' => "b4494109-f514-4763-aaf5-0c8b75572923"]
            ]
        ]));*/

        $ssoId = 'b4494109-f514-4763-aaf5-0c8b75572923';
        $ssoEmail = '<EMAIL>';
        dispatch(new UpdateSSOToMoodle($ssoId, $ssoEmail));

        dd(
            Moodle::request('core_user_get_users', [
                'criteria' => [
                    ['key' => 'username', 'value' => $ssoId],
                ],
            ])
        );
        dd('Testing for UpdateSSOToMoodle');
    }
}
