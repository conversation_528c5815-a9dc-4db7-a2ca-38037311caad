<?php

namespace Domains\Moodle;

use Domains\Moodle\Clients\Adapters\RestClient;
use Domains\Moodle\Models\MoodleConfig;
use Domains\Moodle\Repositories\Assignments;
use Domains\Moodle\Repositories\CalendarEvents;
use Domains\Moodle\Repositories\Categories;
use Domains\Moodle\Repositories\Courses;
use Domains\Moodle\Repositories\Users;
use SSO\DTO\Feature;
use SSO\Facades\SSO;
use Support\Services\GalaxyFeatureService;

class Moodle
{
    const KEY = 'moodle';

    const VIEW_PREFIX = 'galaxy-moodle';

    const ROLE_STUDENT = 5;

    const ROLE_TEACHER = 3;

    public static $clientInstance = null;

    public static function getClientInstance()
    {
        if (is_null(self::$clientInstance)) {
            $MoodleConfig = MoodleConfig::Settings();
            // self::$clientInstance = new RestClient(new Connection(config('laravel-moodle.url'), config('laravel-moodle.token')));
            self::$clientInstance = new RestClient(new Connection($MoodleConfig->url, $MoodleConfig->token));
        }

        return self::$clientInstance;
    }

    public function key()
    {
        return self::KEY;
    }

    public function isConnected(): bool
    {

        return config('features.moodle') && @MoodleConfig::Settings()->connected;
    }

    public function laravelConfig($key = null)
    {
        return config('galaxy.moodle'.($key ? '.'.$key : ''));
    }

    public function config()
    {
        // dd(config('galaxy.tp-integrations.integration_config_model'));
        return MoodleConfig::Settings();
    }

    public function courses()
    {
        return new Courses(self::getClientInstance());
    }

    public function categories()
    {
        return new Categories(self::getClientInstance());
    }

    public function assignments()
    {
        return new Assignments(self::getClientInstance());
    }

    public function users()
    {
        return new Users(self::getClientInstance());
    }

    public function calendarEvents()
    {
        return new CalendarEvents(self::getClientInstance());
    }

    public function updateMoodleKeycloakIdp()
    {
        $items = SSO::activeIdps();
        $activeIdp = $items->filter(fn ($item, $k) => $item['enabled'] && $k == 'moodle')->first();
        // dd($activeIdp);
        if ($activeIdp) {
            $this->request('ltiservice_competency_update_idp', [
                'idp_hint' => $activeIdp['kc_idp_hint'] ?? 'galaxy',
            ]);
        }
        $this->request('ltiservice_competency_update_idp', [
            'idp_hint' => $activeIdp['kc_idp_hint'] ?? 'galaxy',
        ]);
    }

    public function ssoUrl()
    {

        // dd(self::laravelConfig()['sso_login_url']);

        $settings = $this->config();
        $url = rtrim($settings->url).'/'.ltrim($this->laravelConfig('sso_login_url'), '/');

        if (! galaxy_feature('sso')) {
            return rtrim($settings->url, '/').'/auth/oidc/?source=loginpage';
            // return $settings->url;
        }

        if (! GalaxyFeatureService::HasFeature(Feature::SSO)) {
            return rtrim($settings->url, '/').'/auth/oidc/?source=loginpage';
        }

        $items = SSO::activeIdps();

        $activeIdp = $items->filter(fn ($item, $k) => $item['enabled'] && $k == 'microsoft')->first();
        // dd($activeIdp);

        return $url.'?'.http_build_query([
            'kc_idp_hint' => $activeIdp ? @$activeIdp['kc_idp_hint'] : '',
        ]);
    }

    public function request($function, array $arguments = [])
    {
        return self::getClientInstance()->sendRequest($function, $arguments);
    }

    public function testConnection(): array
    {
        try {
            // Check basic configuration first
            if (! $this->isConnected()) {
                return [
                    'connected' => false,
                    'error' => 'Moodle is not configured or enabled',
                ];
            }

            $config = $this->config();
            if (! $config || ! $config->url || ! $config->token) {
                return [
                    'connected' => false,
                    'error' => 'Moodle URL or token is not configured',
                ];
            }

            // Make a test API call
            $response = $this->request('core_webservice_get_site_info', []);

            // Check if response contains error
            if (isset($response['errorcode'])) {
                return [
                    'connected' => false,
                    'error' => $response['message'] ?? 'Unknown Moodle API error: '.$response['errorcode'],
                ];
            }

            // Check if we got expected site info
            if (! isset($response['sitename'])) {
                return [
                    'connected' => false,
                    'error' => 'Invalid response from Moodle API',
                ];
            }

            return [
                'connected' => true,
                'error' => null,
                'site_info' => [
                    'sitename' => $response['sitename'] ?? 'Unknown',
                    'release' => $response['release'] ?? 'Unknown',
                    'version' => $response['version'] ?? 'Unknown',
                ],
            ];
        } catch (\Exception $e) {
            return [
                'connected' => false,
                'error' => 'Connection test failed: '.$e->getMessage(),
            ];
        }
    }
}
