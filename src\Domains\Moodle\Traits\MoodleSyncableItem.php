<?php

namespace Domains\Moodle\Traits;

use App\Exceptions\ApplicationException;
use App\Model\v2\AssessmentTaskUnit;
use App\Model\v2\SubjectUnits;
// use App\Users;
use App\Model\v2\TimetableDetail;
use Closure;
use Domains\Moodle\Entities\Entity;
use Domains\Moodle\Facades\Moodle;
use Domains\Moodle\Models\MoodleItem;
use Exception;
use Illuminate\Database\Eloquent\Builder;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;
use Integrations\Base\Flags\SyncStatus;

trait MoodleSyncableItem
{
    public function moodleItem()
    {
        // dd(config('galaxy.tp-integrations.integration_item_model'));
        return $this->morphOne(MoodleItem::class, 'syncable')->key('moodle');
    }

    public function isSyncedToMoodle(): bool
    {
        return $this->moodleItem && $this->moodleItem->isConnected();
    }

    public function getMoodleId()
    {
        if (! $this->isSyncedToMoodle()) {
            return null;
        }

        // dd($this->moodleItem()->first());
        return $this->moodleItem()->first()->getSyncId();
    }

    public function scopeNotSyncedWithMoodle($q, $prefix = 'rto_courses'): Builder
    {
        return $q->leftJoin('galaxy_tp_integrations_items', function ($join) use ($prefix) {
            $join->on('galaxy_tp_integrations_items.syncable_id', '=', $prefix.'.id')
                ->on('galaxy_tp_integrations_items.syncable_type', '=', DB::raw("'".morphAlias(self::class)."'"));
        })
            ->where(function ($q) {
                return $q
                    ->whereNull('galaxy_tp_integrations_items.sync_item_id');
                // For testing
                // ->whereNotNull('xero_invoices.xero_invoice_id');
            });
    }

    public function scopeSyncedWithMoodle($q, $prefix = 'rto_courses'): Builder
    {
        return $q->leftJoin('galaxy_tp_integrations_items', function ($join) use ($prefix) {
            $join->on('galaxy_tp_integrations_items.syncable_id', '=', $prefix.'.id')
                ->on('galaxy_tp_integrations_items.syncable_type', '=', DB::raw("'".morphAlias(self::class)."'"));
        })
            ->where(function ($q) {
                return $q
                    ->whereNotNull('galaxy_tp_integrations_items.sync_item_id');
                // For testing
                // ->whereNotNull('xero_invoices.xero_invoice_id');
            });
    }

    public function scopeHavingMoodleId(Builder $q, $id): Builder
    {
        return $q->whereHas('moodleItem', function ($q) use ($id) {
            return $q->where('sync_item_id', $id);
        });
    }

    /* this model method will call connectTo[PROVIDER], syncTo[PROVIDER], get[Provider]Payload
    methods which should be enforced by the [PROVIDER] interface */
    public function asMoodleItem($update = false, $params = null)
    {
        $key = 'moodle';
        $item = $this->moodleItem()->firstOrCreate([
            'name' => $key,
        ]);
        $payloadMethod = 'get'.ucfirst($key).'Payload';
        // dd($item);

        if (! $item->isConnected($key)) {
            $methodName = 'connectTo'.ucfirst($key);
            if (! method_exists($this, $methodName)) {
                throw new ApplicationException("Connection Logic not defined for {$key}");
            }
            if (! method_exists($this, $payloadMethod)) {
                throw new ApplicationException("Payload method not found for {$key}");
            }

            return $this->{$methodName}($this->{$payloadMethod}($params));
        } else {

            if ($update) {
                $methodName = 'syncTo'.ucfirst($key);
                if (! method_exists($this, $methodName)) {
                    throw new ApplicationException("Connection Logic not defined for {$key}");
                }

                if (! method_exists($this, $payloadMethod)) {
                    throw new ApplicationException("Payload method not found for {$key}");
                }

                return $this->{$methodName}($this->{$payloadMethod}($params));
            }
        }

        return $item;
    }

    public function connectRepositoryMethod(): string
    {
        return 'create';
    }

    public function syncRepositoryMethod(): string
    {
        return 'update';
    }

    public function deleteRepositoryMethod(): string
    {
        return 'delete';
    }

    public function getMoodleRemovePayload($id, $params = null)
    {
        return [$id];
    }

    public function connectToMoodle(Entity $payload, ?Closure $callback = null): ?MoodleItem
    {
        $model = $this->moodleItem;
        $syncableItemType = $this->convertSyncableTypeName($model->syncable_type);
        activity()->on($this)->log("Syncing $syncableItemType with Moodle");
        // Log::info('payload-connectToMoodle', $payload->toArray(true));

        try {
            Log::info('payload-connectToMoodle', $payload->toArray(true));
            // dd($payload);
            $collection = $this->getMoodleRepository()->{$this->connectRepositoryMethod()}($payload);
            $this->saveMoodleItem($model, $collection->first());
            // dd($collection);

            return $model;
        } catch (\Exception $e) {
            // dd($e);
            activity()
                ->on($this)
                ->useLog($syncableItemType)
                ->log("Failed to sync $syncableItemType from Moodle : ".$model->getSyncId()); // Syncing moodle item Failed:
            $message = $e->getMessage();
            galaxy_log_to_file('connect to moodle failed', [tenant('id'), $payload->toArray(true), $message], 'moodle');
            $model->syncErrorHandler($message, true);
            throw new Exception($message);
        }

        //    dd($response);
    }

    public function saveMoodleItem(MoodleItem $model, Entity $payload)
    {
        $syncableItemType = $this->convertSyncableTypeName($model->syncable_type);
        info('Syncable Type', [$syncableItemType]);
        // $model->syncable_type = $syncableItemType;
        $model->fill([
            'sync_item_id' => $payload->getId(),
            'sync_status' => SyncStatus::STATUS_SYNCED,
            'synced_at' => now(),
        ]);
        $model->sync_failed_at = null;
        $model->sync_failed_message = null;
        $model->save();

        // $model->updateJsonField([
        //     'item' => $payload->toArray(true)
        // ], 'data');
        $model->syncDetailsFromProvider($payload);

        // info('$model', [$model]);
        activity()
            ->on($model)
            ->useLog($syncableItemType)
            ->log($syncableItemType.' successfully synced with Moodle. Item ID: '.$model->getSyncId());
        // ->log("Synced {$model->name} item: " . $model->getSyncId());
    }

    private function convertSyncableTypeName($type)
    {
        // info('Syncable Type', [$type]);
        /*$map = [
            'App\\Model\\v2\\AssessmentTaskUnit'  => 'assessment',
            'App\\Model\\v2\\SubjectUnits'        => 'unit',
            'course'                              => 'course',
            //morphAlias(SubjectUnits::class)         => 'unit',
            //morphAlias(AssessmentTaskUnit::class)   => 'assessment',
        ];*/
        $map = [
            morphAlias(\App\Users::class) => 'student',
            SubjectUnits::class => 'unit',
            AssessmentTaskUnit::class => 'assessment',
            TimetableDetail::class => 'Timetable',
        ];

        return $map[$type] ?? $type;
        // return ucfirst($map[$type] ?? $type);
    }

    public function syncToMoodle(Entity $payload): ?MoodleItem
    {
        activity()
            ->on($this)
            ->log('Updating to moodle');
        $model = $this->moodleItem;
        try {
            Log::info('payload-syncToMoodle', $payload->toArray(true));

            $payload->setId($model->getSyncId());
            // dd($payload);
            $this->getMoodleRepository()->{$this->syncRepositoryMethod()}($payload);

            // $this->saveMoodleItem($model, $collection->first());
            /* moodle does not return the resource on update request so just
            ignore updating our local model. */
            // $model->syncDetailsFromProvider($payload);

            return $model;
        } catch (\Exception $e) {
            // dd($e);
            activity()
                ->on($this)
                ->log('Syncing moodle item Failed: '.$model->getSyncId());
            $message = $e->getMessage();
            galaxy_log_to_file('sync to moodle failed', [tenant('id'), $payload->toArray(true), $message], 'moodle');
            $model->syncErrorHandler($message, true);
            throw new Exception($message);
        }
    }

    public function removeFromMoodle($params = []): void
    {
        activity()
            ->on($this)
            ->log('Removing from moodle');
        $model = $this->moodleItem;

        try {
            if ($model->getSyncId()) {
                $this->getMoodleRepository()->{$this->deleteRepositoryMethod()}($this->getMoodleRemovePayload($model->getSyncId(), $params));
                $model->delete();
            }
        } catch (\Exception $e) {
            // dd($e);
            report($e);
            activity()
                ->on($this)
                ->log('Syncing moodle item Failed: '.$model->getSyncId());
            $message = $e->getMessage();
            galaxy_log_to_file('remove from moodle failed', [tenant('id'), $model->getSyncId(), $message]);
            $model->syncErrorHandler($message, true);
            throw new Exception($message);
        }
    }

    /* UPDATE FROM MOODLE */
    public function updateFromMoodle(Entity $assignment) {}

    /* HANDLE COURSE CREATING LOGIC FROM MOODLE */
    public static function CreateFromMoodle(Entity $course) {}

    public function moodleSyncLogKey()
    {
        return 'moodle_item_'.$this->id;
    }
}
