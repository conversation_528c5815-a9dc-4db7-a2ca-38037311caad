<div class="mt-4">
    <div class="flex flex-col items-start justify-start space-y-6 rounded-lg bg-white p-6 shadow">
        <div class="flex items-center justify-start space-x-1">
            <p class="text-base font-medium leading-5 text-gray-900">Configuration Setup</p>
            <!--            <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                <path d="M8 0C12.4183 0 16 3.58172 16 8C16 12.4183 12.4183 16 8 16C3.58172 16 0 12.4183 0 8C0 3.58172 3.58172 0 8 0ZM8 1C4.13401 1 1 4.13401 1 8C1 11.866 4.13401 15 8 15C11.866 15 15 11.866 15 8C15 4.13401 11.866 1 8 1ZM8 11.5C8.41421 11.5 8.75 11.8358 8.75 12.25C8.75 12.6642 8.41421 13 8 13C7.58579 13 7.25 12.6642 7.25 12.25C7.25 11.8358 7.58579 11.5 8 11.5ZM8 3.5C9.38071 3.5 10.5 4.61929 10.5 6C10.5 6.72959 10.1848 7.40774 9.6513 7.8771L9.49667 8.00243L9.27817 8.16553L9.19065 8.23718C9.1348 8.28509 9.08354 8.33373 9.03456 8.38592C8.69627 8.74641 8.5 9.24223 8.5 10C8.5 10.2761 8.27614 10.5 8 10.5C7.72386 10.5 7.5 10.2761 7.5 10C7.5 8.98796 7.79312 8.24747 8.30535 7.70162C8.41649 7.5832 8.53202 7.47988 8.66094 7.37874L8.90761 7.19439L9.02561 7.09468C9.325 6.81435 9.5 6.42206 9.5 6C9.5 5.17157 8.82843 4.5 8 4.5C7.17157 4.5 6.5 5.17157 6.5 6C6.5 6.27614 6.27614 6.5 6 6.5C5.72386 6.5 5.5 6.27614 5.5 6C5.5 4.61929 6.61929 3.5 8 3.5Z" fill="#9CA3AF" />
            </svg>-->
            <livewire:docs
                path=".docs/moodle.md"
                :data="$this->docsData"
            />

        </div>
        {{-- <div class="flex items-center justify-start space-x-1">
            <p class="text-sm font-normal text-gray-500">Please visit
            <a class="text-sm font-normal text-primary-blue-500" href="https://developer.moodle.com/myapps">MOODLE APPS DASHBOARD</a>
            and create your app using the urls below. Update your client id and secret on below fields </p>
        </div> --}}
        @if ($this->isConnected)
            <div class="flex w-full items-start justify-between">
                <div class="flex flex-col items-start justify-start space-y-5">
                    <div class="flex items-center justify-start">
                        <div class="w-36 text-sm font-medium leading-5 text-gray-700">Moodle Site URL</div>
                        <div class="flex items-center justify-start space-x-1">
                            <div class="text-sm font-normal text-primary-blue-500">{{ $config->url }}</div>
                            <a
                                href="javascript:void(0);"
                                class="copy_text cursor-pointer rounded"
                                title="Copy"
                                data-text="{{ $config->url }}"
                            >
                                <svg
                                    width="12"
                                    height="16"
                                    viewBox="0 0 12 16"
                                    fill="none"
                                    xmlns="http://www.w3.org/2000/svg"
                                >
                                    <path
                                        d="M4 0C2.89543 0 2 0.895431 2 2V12C2 13.1046 2.89543 14 4 14H10C11.1046 14 12 13.1046 12 12V2C12 0.89543 11.1046 0 10 0H4ZM3 2C3 1.44772 3.44772 1 4 1H10C10.5523 1 11 1.44772 11 2V12C11 12.5523 10.5523 13 10 13H4C3.44772 13 3 12.5523 3 12V2ZM0 4.00001C0 3.25973 0.402199 2.61339 1 2.26758V12.5C1 13.8807 2.11929 15 3.5 15H9.73244C9.38663 15.5978 8.74028 16 8 16H3.5C1.567 16 0 14.433 0 12.5V4.00001Z"
                                        fill="#9CA3AF"
                                    />
                                </svg>
                            </a>
                        </div>
                    </div>
                    <div class="flex items-center justify-start">
                        <div class="w-36 text-sm font-medium leading-5 text-gray-700">Moodle API token</div>
                        <div class="flex cursor-pointer items-center justify-start space-x-1">
                            <div class="text-sm font-normal text-primary-blue-500">xxxxxxxxxxxxxxxx</div>
                            <a
                                href="javascript:void(0);"
                                class="copy_text cursor-pointer rounded"
                                title="Copy"
                                data-text="{{ $config->token }}"
                            >
                                <svg
                                    width="12"
                                    height="16"
                                    viewBox="0 0 12 16"
                                    fill="none"
                                    xmlns="http://www.w3.org/2000/svg"
                                >
                                    <path
                                        d="M4 0C2.89543 0 2 0.895431 2 2V12C2 13.1046 2.89543 14 4 14H10C11.1046 14 12 13.1046 12 12V2C12 0.89543 11.1046 0 10 0H4ZM3 2C3 1.44772 3.44772 1 4 1H10C10.5523 1 11 1.44772 11 2V12C11 12.5523 10.5523 13 10 13H4C3.44772 13 3 12.5523 3 12V2ZM0 4.00001C0 3.25973 0.402199 2.61339 1 2.26758V12.5C1 13.8807 2.11929 15 3.5 15H9.73244C9.38663 15.5978 8.74028 16 8 16H3.5C1.567 16 0 14.433 0 12.5V4.00001Z"
                                        fill="#9CA3AF"
                                    />
                                </svg>
                            </a>
                        </div>
                    </div>
                </div>
                <div class="flex items-center justify-end">
                    <span id="popupNotification"></span>
                </div>
            </div>
        @endif
        <div class="flex w-full items-center justify-start border-b">
        </div>
        {{-- @if (!$this->isConnected) --}}


        @if (!$this->isFilled)
            <div
                class="configureAPIBox inline-flex w-full items-center justify-start space-x-2 rounded-md border border-yellow-400 bg-yellow-50 p-4">
                <svg
                    width="16"
                    height="14"
                    viewBox="0 0 16 14"
                    fill="none"
                    xmlns="http://www.w3.org/2000/svg"
                >
                    <path
                        fill-rule="evenodd"
                        clip-rule="evenodd"
                        d="M6.25608 1.09858C7.02069 -0.260724 8.97778 -0.260725 9.74238 1.09858L15.3227 11.0191C16.0726 12.3523 15.1092 13.9996 13.5795 13.9996H2.41893C0.889274 13.9996 -0.0741535 12.3523 0.675776 11.0191L6.25608 1.09858ZM8.99915 10.9998C8.99915 11.552 8.55143 11.9998 7.99915 11.9998C7.44686 11.9998 6.99915 11.552 6.99915 10.9998C6.99915 10.4475 7.44686 9.99976 7.99915 9.99976C8.55143 9.99976 8.99915 10.4475 8.99915 10.9998ZM7.99915 2.99976C7.44686 2.99976 6.99915 3.44747 6.99915 3.99976V6.99976C6.99915 7.55204 7.44686 7.99976 7.99915 7.99976C8.55143 7.99976 8.99915 7.55204 8.99915 6.99976V3.99976C8.99915 3.44747 8.55143 2.99976 7.99915 2.99976Z"
                        fill="#FBBF24"
                    />
                </svg>
                <div class="text-sm font-medium leading-5 text-yellow-800">Please fill all details.</div>
            </div>
        @endif

        <div class="grid w-4/5 grid-cols-2 gap-6">
            <div class="flex w-full flex-col items-start justify-start space-y-1">
                <p class="text-sm font-medium leading-5 text-gray-700">Moodle Site Name</p>
                <input
                    type="text"
                    name="name"
                    {{ $this->isConnected ? 'readonly' : '' }}
                    wire:model.defer="config.name"
                    class="name {{ $this->isConnected ? 'bg-gray-100' : 'bg-white' }} w-full rounded-lg border border-gray-300 px-3 py-2 text-sm leading-5 text-gray-700 shadow-sm"
                />
                <x-input-error for="config.name" />
                {{-- @error('config.name')
                    <p class="text-xs text-red-500">{{ $message }}</p>
                @enderror --}}
            </div>
            <div></div>
            <div class="flex w-full flex-col items-start justify-start space-y-1">
                <p class="text-sm font-medium leading-5 text-gray-700">Moodle Site Url</p>
                <input
                    type="text"
                    name="url"
                    {{ $this->isConnected ? 'readonly' : '' }}
                    wire:model.defer="config.url"
                    class="{{ $this->isConnected ? 'bg-gray-100' : 'bg-white' }} w-full rounded-lg border border-gray-300 px-3 py-2 text-sm leading-5 text-gray-700 shadow-sm"
                />
                <x-input-error for="config.url" />
            </div>
            <div class="flex w-full flex-col items-start justify-start space-y-1">
                <p class="text-sm font-medium leading-5 text-gray-700">Moodle API Token</p>
                <div class="relative mt-1 flex w-full items-center">
                    <x-password-input
                        class="{{ $errors->has('password') ? 'errors' : '' }} {{ $this->isConnected ? 'bg-gray-100' : 'bg-white' }} block w-full"
                        :disabled="$this->isConnected"
                        name="token"
                        placeholder="API Token"
                        required
                        wire:model.defer="config.token"
                        :error="$errors->has('form.token')"
                    />
                    {{-- <x-password-input name="token"
                                      :error="$errors->has('password')"
                                      {{ $this->isConnected ? 'readonly' : '' }}
                                      wire:model.defer="form.token"
                                      class="{{ $this->isConnected ? 'bg-gray-100' : 'bg-white' }} w-full rounded-lg border border-gray-300 px-3 py-2 text-sm leading-5 text-gray-700 shadow-sm" /> --}}
                </div>
                <x-input-error for="config.token" />
            </div>

            {{-- <div class="flex items-center space-x-2">
                <label class="flex items-center space-x-2 cursor-pointer">
                    <input type="checkbox"
                           name="allow_student_dashboard"
                           wire:model.defer="config.allow_student_dashboard"
                           value="1" {{ $this->isConnected ? 'disabled' : '' }} />
                    <span class="text-sm font-medium leading-5 text-gray-700">Grant access to Student Dashboard</span>
                </label>
            </div> --}}

        </div>
        @if (!$this->isConnected)
            <x-button
                type="submit"
                target="saveConfig"
                wire:click.prevent="saveConfig"
                loading="Saving.."
            >
                SAVE
            </x-button>
        @endif
    </div>
    @if ($this->isConnected)
        <div class="gap-2 space-y-3 mt-6">
            <div class="text-base font-base leading-5 text-gray-900">Courses</div>
            <div class="flex gap-2">
                <x-button
                    type="button"
                    class="flex h-9 items-center justify-center rounded-lg bg-primary-blue-500 px-4 text-white focus:ring-2 focus:ring-primary-blue-600 focus:ring-offset-2 focus:ring-offset-white truncate"
                    target="syncCourse"
                    wire:click.prevent="syncCourse"
                    title="Fetch Modules"
                    loading="Syncing.."
                >
                    <span class="k-icon k-i-refresh k-icon-refresh mb-1 text-sm"></span>
                    Sync Course
                </x-button>
                <x-button
                    type="button"
                    class="flex h-9 items-center justify-center rounded-lg bg-primary-blue-500 px-4 text-white focus:ring-2 focus:ring-primary-blue-600 focus:ring-offset-2 focus:ring-offset-white truncate"
                    target="syncAllCourses"
                    wire:click.prevent="syncAllCourses"
                    title="Fetch Modules"
                    loading="Syncing.."
                >
                    <span class="k-icon k-i-refresh k-icon-refresh mb-1 text-sm"></span>
                    Sync All Courses From Moodle
                </x-button>
                <x-button
                    type="button"
                    class="flex h-9 items-center justify-center rounded-lg bg-primary-blue-500 px-4 text-white focus:ring-2 focus:ring-primary-blue-600 focus:ring-offset-2 focus:ring-offset-white truncate"
                    target="syncAllCoursesToMoodle"
                    wire:click.prevent="syncAllCoursesToMoodle"
                    title="Fetch Modules"
                    loading="Syncing.."
                >
                    <span class="k-icon k-i-refresh k-icon-refresh mb-1 text-sm"></span>
                    Sync All Courses To Moodle
                </x-button>
                <x-button
                    type="button"
                    class="flex h-9 items-center justify-center rounded-lg bg-primary-blue-500 px-4 text-white focus:ring-2 focus:ring-primary-blue-600 focus:ring-offset-2 focus:ring-offset-white truncate"
                    target="createCourseMemberGroup"
                    wire:click.prevent="createCourseMemberGroup"
                    title="Fetch Modules"
                    loading="Syncing.."
                >
                    <span class="k-icon k-i-refresh k-icon-refresh mb-1 text-sm"></span>
                    Create Moodle Course Member Group
                </x-button>
                <x-button
                    type="button"
                    class="flex h-9 items-center justify-center rounded-lg bg-primary-blue-500 px-4 text-white focus:ring-2 focus:ring-primary-blue-600 focus:ring-offset-2 focus:ring-offset-white truncate"
                    target="syncRandomAssignment"
                    wire:click.prevent="syncRandomAssignment"
                    title="Fetch Modules"
                    loading="Syncing.."
                >
                    <span class="k-icon k-i-refresh k-icon-refresh mb-1 text-sm"></span>
                    Sync Random Assignment
                </x-button>
            </div>
            <div class="flex gap-2">
                <x-v2.button
                    variant="danger"
                    wire:confirm="Are you sure you want to reset local moodle data?"
                    target="resetItems"
                    class="truncate"
                    wire:click.prevent="resetItems('courses,users')"
                    title="Reset Course data"
                    loading="Resetting.."
                >
                    <span class="k-icon k-i-refresh k-icon-refresh mb-1 text-sm"></span>
                    Reset Local Course Data
                </x-v2.button>
                <x-v2.button
                    variant="danger"
                    class="truncate"
                    target="patchMoodlePlugin"
                    wire:click.prevent="patchMoodlePlugin()"
                    title="Patch Moodle Hooks"
                    loading="Patching.."
                >
                    <span class="k-icon k-i-refresh k-icon-refresh mb-1 text-sm"></span>
                    Patch
                </x-v2.button>
                <x-v2.button
                    variant="danger"
                    target="test"
                    class="truncate"
                    wire:click.prevent="test"
                    title="Test Moodle Hooks"
                    loading="Syncing.."
                >
                    <span class="k-icon k-i-refresh k-icon-refresh mb-1 text-sm"></span>
                    Test
                </x-v2.button>
                <x-v2.button
                    variant="danger"
                    target="ShortCourseEnrolled"
                    class="truncate"
                    wire:click.prevent="ShortCourseEnrolled"
                    title="Test event for Short Course Enrolled"
                    loading="Syncing.."
                >
                    <span class="k-icon k-i-refresh k-icon-refresh mb-1 text-sm"></span>
                    Short Course Enrolled
                </x-v2.button>
            </div>
            <div class="w-full h-px bg-blue-200"></div>


            <div class="text-base font-base leading-5 text-gray-900">Users</div>
            <div class="flex gap-2">
                <x-button
                    type="button"
                    class="flex h-9 items-center justify-center rounded-lg bg-primary-blue-500 px-4 text-white focus:ring-2 focus:ring-primary-blue-600 focus:ring-offset-2 focus:ring-offset-white truncate"
                    target="syncUser"
                    wire:click.prevent="syncUser"
                    title="Sync User To Moodle"
                    loading="Syncing.."
                >
                    <span class="k-icon k-i-refresh k-icon-refresh mb-1 text-sm"></span>
                    Sync User
                </x-button>
                <x-button
                    type="button"
                    class="flex h-9 items-center justify-center rounded-lg bg-primary-blue-500 px-4 text-white focus:ring-2 focus:ring-primary-blue-600 focus:ring-offset-2 focus:ring-offset-white truncate"
                    target="updateUserUsername"
                    wire:click.prevent="updateUserUsername"
                    title="Update Username To Moodle"
                    loading="Syncing.."
                >
                    <span class="k-icon k-i-refresh k-icon-refresh mb-1 text-sm"></span>
                    Update User
                </x-button>
                <x-button
                    type="button"
                    class="flex h-9 items-center justify-center rounded-lg bg-primary-blue-500 px-4 text-white focus:ring-2 focus:ring-primary-blue-600 focus:ring-offset-2 focus:ring-offset-white truncate"
                    target="syncUserInfoFromMoodle"
                    wire:click.prevent="syncUserInfoFromMoodle"
                    title="Sync User Info From"
                    loading="Syncing.."
                >
                    <span class="k-icon k-i-refresh k-icon-refresh mb-1 text-sm"></span>
                    Sync User Info From Moodle
                </x-button>
                <a
                    class="flex h-9 items-center justify-center rounded-lg bg-primary-blue-500 px-4 text-white space-x-1 truncate"
                    href="{{ $appSetting->getOIDCLoginUrl() }}"
                >
                    <span class="k-icon k-i-refresh k-icon-refresh mb-1 text-sm"></span>
                    <span>Visit Moodle</span>
                </a>
                <x-button
                    type="button"
                    class="flex h-9 items-center justify-center rounded-lg bg-primary-blue-500 px-4 text-white focus:ring-2 focus:ring-primary-blue-600 focus:ring-offset-2 focus:ring-offset-white truncate"
                    target="syncAllUsers"
                    wire:click.prevent="syncUsersFromMoodle"
                    title="Sync All Users From Moodle"
                    loading="Syncing.."
                >
                    <span class="k-icon k-i-refresh k-icon-refresh mb-1 text-sm"></span>
                    Sync All Users From Moodle
                </x-button>
                <x-button
                    type="button"
                    class="flex h-9 items-center justify-center rounded-lg bg-primary-blue-500 px-4 text-white focus:ring-2 focus:ring-primary-blue-600 focus:ring-offset-2 focus:ring-offset-white truncate"
                    target="syncAllUsersToMoodle"
                    wire:click.prevent="syncAllUsersToMoodle"
                    title="Sync All Users To Modules"
                    loading="Syncing.."
                >
                    <span class="k-icon k-i-refresh k-icon-refresh mb-1 text-sm"></span>
                    Sync All Users To Moodle
                </x-button>
            </div>
            <div class="flex gap-2">
                <x-button
                    type="button"
                    class="flex h-9 items-center justify-center rounded-lg bg-primary-blue-500 px-4 text-white focus:ring-2 focus:ring-primary-blue-600 focus:ring-offset-2 focus:ring-offset-white truncate"
                    target="enrollUserToMoodle"
                    wire:click.prevent="enrollUserToMoodle"
                    title="Sync All Users To Modules"
                    loading="Syncing.."
                >
                    <span class="k-icon k-i-refresh k-icon-refresh mb-1 text-sm"></span>
                    Enroll Random User To Random Course
                </x-button>
                <x-button
                    type="button"
                    class="flex h-9 items-center justify-center rounded-lg bg-primary-blue-500 px-4 text-white focus:ring-2 focus:ring-primary-blue-600 focus:ring-offset-2 focus:ring-offset-white truncate"
                    target="syncStudentCourseGradeReportFromMoodle"
                    wire:click.prevent="syncStudentCourseGradeReportFromMoodle"
                    title="Sync User Course Grade Report From Modules"
                    loading="Syncing.."
                >
                    <span class="k-icon k-i-refresh k-icon-refresh mb-1 text-sm"></span>
                    Sync User Course Grade Report From Moodle
                </x-button>
            </div>
            <div class="w-full h-px bg-blue-200"></div>


            <div class="text-base font-base leading-5 text-gray-900">Students</div>
            <div class="flex gap-2">
                <x-button
                    type="button"
                    class="flex h-9 items-center justify-center rounded-lg bg-primary-blue-500 px-4 text-white focus:ring-2 focus:ring-primary-blue-600 focus:ring-offset-2 focus:ring-offset-white truncate"
                    target="syncStudent"
                    wire:click.prevent="syncStudent"
                    title="Fetch Modules"
                    loading="Syncing.."
                >
                    <span class="k-icon k-i-refresh k-icon-refresh mb-1 text-sm"></span>
                    Sync Student
                </x-button>
                <x-button
                    type="button"
                    class="flex h-9 items-center justify-center rounded-lg bg-primary-blue-500 px-4 text-white focus:ring-2 focus:ring-primary-blue-600 focus:ring-offset-2 focus:ring-offset-white truncate"
                    target="syncAllStudents"
                    wire:click.prevent="syncAllStudents"
                    title="Fetch Modules"
                    loading="Syncing.."
                >
                    <span class="k-icon k-i-refresh k-icon-refresh mb-1 text-sm"></span>
                    Sync All Students From Moodle
                </x-button>
                <x-button
                    type="button"
                    class="flex h-9 items-center justify-center rounded-lg bg-primary-blue-500 px-4 text-white focus:ring-2 focus:ring-primary-blue-600 focus:ring-offset-2 focus:ring-offset-white truncate"
                    target="syncAllStudentsToMoodle"
                    wire:click.prevent="syncAllStudentsToMoodle"
                    title="Sync All Users To Modules"
                    loading="Syncing.."
                >
                    <span class="k-icon k-i-refresh k-icon-refresh mb-1 text-sm"></span>
                    Sync All Students To Moodle
                </x-button>
                <x-button
                    type="button"
                    class="flex h-9 items-center justify-center rounded-lg bg-primary-blue-500 px-4 text-white focus:ring-2 focus:ring-primary-blue-600 focus:ring-offset-2 focus:ring-offset-white truncate"
                    target="calendarTest"
                    wire:click.prevent="calendarTest"
                    title="Create Calendar Events for batch"
                    loading="Syncing.."
                >
                    <span class="k-icon k-i-refresh k-icon-refresh mb-1 text-sm"></span>
                    Test Calendar Event
                </x-button>
            </div>
            <div class="w-full h-px bg-blue-200"></div>

            <div class="text-base font-base leading-5 text-gray-900">Extra</div>
            <div class="flex gap-2">
                <x-button
                    type="button"
                    class="flex h-9 items-center justify-center rounded-lg bg-primary-blue-500 px-4 text-white focus:ring-2 focus:ring-primary-blue-600 focus:ring-offset-2 focus:ring-offset-white truncate"
                    target="createMoodleWebhook"
                    wire:click.prevent="createMoodleWebhook"
                    title="Create Galaxy Moodle Webhook"
                    loading="Syncing.."
                >
                    <span class="k-icon k-i-refresh k-icon-refresh mb-1 text-sm"></span>
                    Create Galaxy Moodle Webhook
                </x-button>
            </div>
            <div class="flex gap-2">
                <x-v2.button
                    variant="danger"
                    target="removeCourseFromMoodle"
                    wire:click.prevent="removeCourseFromMoodle()"
                    title="Remove Course from moodle"
                    loading="Removing..."
                >
                    <span class="k-icon k-i-refresh k-icon-refresh mb-1 text-sm"></span>
                    Remove Course From Moodle
                </x-v2.button>
                <x-v2.button
                    variant="danger"
                    target="removeCourseSubjectUnitFromMoodle"
                    wire:click.prevent="removeCourseSubjectUnitFromMoodle()"
                    title="Remove Course Subject Unit from moodle"
                    loading="Removing..."
                >
                    <span class="k-icon k-i-refresh k-icon-refresh mb-1 text-sm"></span>
                    Remove Course Unit From Moodle
                </x-v2.button>
                <x-v2.button
                    variant="danger"
                    target="removeUnitAssignmentFromMoodle"
                    wire:click.prevent="removeUnitAssignmentFromMoodle()"
                    title="Remove Course Subject Unit from moodle"
                    loading="Removing..."
                >
                    <span class="k-icon k-i-refresh k-icon-refresh mb-1 text-sm"></span>
                    Remove Unit Assignment From Moodle
                </x-v2.button>
            </div>
        </div>
    @endif
</div>
