<div class="space-y-6 rounded-lg bg-white p-6 shadow" x-data="{expanded: @entangle('state.old_connection_modal_open')}">
    <div class="flex w-full items-center justify-between" wire:key="{{ $this->isConnected ? 'connected' : 'disconnected'}}">
        @if ($this->isConnected)
            <div class="flex flex-col items-start justify-start">
                <div class="flex items-center space-x-1">
                    <p class="text-base font-medium leading-5 text-gray-900">Moodle Connection Status</p>
                     <livewire:docs path=".docs/moodle.md" :data="$this->docsData"/>
                     {{-- <svg width="16" height="16" viewBox="0 0 16 16" fill="none" xmlns="http://www.w3.org/2000/svg">
                        <path d="M8 0C12.4183 0 16 3.58172 16 8C16 12.4183 12.4183 16 8 16C3.58172 16 0 12.4183 0 8C0 3.58172 3.58172 0 8 0ZM8 1C4.13401 1 1 4.13401 1 8C1 11.866 4.13401 15 8 15C11.866 15 15 11.866 15 8C15 4.13401 11.866 1 8 1ZM8 11.5C8.41421 11.5 8.75 11.8358 8.75 12.25C8.75 12.6642 8.41421 13 8 13C7.58579 13 7.25 12.6642 7.25 12.25C7.25 11.8358 7.58579 11.5 8 11.5ZM8 3.5C9.38071 3.5 10.5 4.61929 10.5 6C10.5 6.72959 10.1848 7.40774 9.6513 7.8771L9.49667 8.00243L9.27817 8.16553L9.19065 8.23718C9.1348 8.28509 9.08354 8.33373 9.03456 8.38592C8.69627 8.74641 8.5 9.24223 8.5 10C8.5 10.2761 8.27614 10.5 8 10.5C7.72386 10.5 7.5 10.2761 7.5 10C7.5 8.98796 7.79312 8.24747 8.30535 7.70162C8.41649 7.5832 8.53202 7.47988 8.66094 7.37874L8.90761 7.19439L9.02561 7.09468C9.325 6.81435 9.5 6.42206 9.5 6C9.5 5.17157 8.82843 4.5 8 4.5C7.17157 4.5 6.5 5.17157 6.5 6C6.5 6.27614 6.27614 6.5 6 6.5C5.72386 6.5 5.5 6.27614 5.5 6C5.5 4.61929 6.61929 3.5 8 3.5Z" fill="#9CA3AF" />
                    </svg> --}}
                </div>
                <p class="text-sm font-normal text-gray-500">You are connected to moodle</p>
            </div>
            <x-button wire:click.prevent="disconnect"
                      loading="CLOSING CONNECTION..."
                      class="inline-flex h-9 items-center justify-center rounded-lg bg-white border border-red-400 px-4 font-medium text-gray-700 hover:shadow focus:ring-2 focus:ring-red-500 focus:ring-offset-2 focus:ring-offset-white">
                {{-- <img src="{{ asset('v2/img/moodle-logo.png') }}" class="mr-1" alt="searchIcon" /> --}}
                MOODLE DISCONNECT</x-button>
        @else
            <div class="flex flex-col items-start justify-start">
                <div class="flex items-center space-x-1">
                    <p class="text-base font-medium leading-5 text-gray-900">Moodle Connection Status</p>
                     <livewire:docs path=".docs/moodle.md" :data="$this->docsData"/>
                    
                </div>
                <p class="text-sm font-normal text-gray-500">You are Not Connected to moodle</p>
            </div>
            <div class="flex gap-2">
            <x-button wire:click.prevent="connect"
                      :disabled="!$this->isFilled"
                      loading="VERIFYING CONNECTION..."
                      class="inline-flex h-9 items-center justify-center rounded-lg border border-gray-300 px-4 font-medium text-white hover:shadow focus:ring-2 focus:ring-gray-400 focus:ring-offset-2 focus:ring-offset-white">
                {{-- <img src="{{ asset('v2/img/moodle-logo.png') }}" class="mr-1" alt="searchIcon" /> --}}
                <span>CONNECT TO MOODLE</span>
            </x-button>
            
            @if($this->oldConnections && count($this->oldConnections))
            <x-v2.button outline="1" variant="primary" size="sm" x-on:click.prevent="expanded=true;">
                {{-- <img src="{{ asset('v2/img/moodle-logo.png') }}" class="mr-1" alt="searchIcon" /> --}}
                <span>CONNECT USING OLD SETUP</span>
            </x-v2.button>

            @endif
            </div>

        @endif
    </div>
    @if ($this->isConnected)
        @include('galaxy-moodle::setup.partials.link-status')
    @endif
    <div x-show="expanded"
                 x-cloak
                 class="relative z-10"
                 aria-labelledby="modal-title"
                 role="dialog"
                 aria-modal="true">
    
    
                <!--
              Background backdrop, show/hide based on modal state.
          
              Entering: "ease-out duration-300"
                From: "opacity-0"
                To: "opacity-100"
              Leaving: "ease-in duration-200"
                From: "opacity-100"
                To: "opacity-0"
            -->
    
                <div class="fixed inset-0 bg-gray-500/75 transition-opacity"
                     aria-hidden="true"></div>
    
                <div class="fixed inset-0 z-10 w-screen overflow-y-auto">
                    <div class="flex min-h-full items-end justify-center p-4 text-center sm:items-start sm:p-0">
                        <!--
                  Modal panel, show/hide based on modal state.
          
                  Entering: "ease-out duration-300"
                    From: "opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                    To: "opacity-100 translate-y-0 sm:scale-100"
                  Leaving: "ease-in duration-200"
                    From: "opacity-100 translate-y-0 sm:scale-100"
                    To: "opacity-0 translate-y-4 sm:translate-y-0 sm:scale-95"
                -->
                        <div
                             class="relative transform overflow-hidden rounded-lg bg-white px-4 pb-4 pt-5 text-left shadow-xl transition-all sm:my-8 sm:w-full sm:max-w-lg sm:p-6">
                            
                                <div>
                                   
                                    <div class="mt-3 sm:mt-5">
                                        <h3 id="modal-title"
                                            class="text-base font-semibold text-gray-900">Available Setups ({{ count($this->oldConnections)}})
                                        </h3>
                                        <div class="mt-2">
                                            <ul class="divide-y divide-gray-100">
                                                @foreach($this->oldConnections as $url => $item)
                                                    <li class="flex items-center justify-between gap-x-6 py-5">
                                                        <div>

                                                        <p class="font-semibold text-gray-900">
                                                            {{ @$item['name'] }}
                                                        </p>
                                                        <span>{{ $url }}</span>
                                                        </div>
                                                            
                                                        <span class="ms-auto ml-auto">
                                                            <x-v2.button wire:confirm="Are you sure you want to apply this config?"
                                                             loading="Applying.." target="applyOldConfig" wire:click.prevent="applyOldConfig('{{ $url }}')">Apply</x-button>
                                                        </span>
                                                    </li>
                                                @endforeach
                                            </ul>

                                            {{-- <p class="text-sm text-gray-500">Lorem ipsum, dolor sit amet consectetur adipisicing
                                        elit. Eius aliquam laudantium explicabo pariatur iste dolorem animi vitae error
                                        totam. At sapiente aliquam accusamus facere veritatis.</p> --}}
                                        </div>
                                    </div>
                                </div>
                                <div class="mt-5 flex justify-end gap-2 sm:mt-6">
                                    
                                    <a type="button"
                                       href="#"
                                       x-on:click.prevent="expanded=false;"
                                       class="mt-3 inline-flex justify-center rounded-md bg-white px-3 py-2 text-sm font-semibold text-gray-900 shadow-sm ring-1 ring-inset ring-gray-300 hover:bg-gray-50 sm:col-start-1 sm:mt-0">Close</a>
                                </div>
                            
                        </div>
                    </div>
                </div>
            </div>
</div>
