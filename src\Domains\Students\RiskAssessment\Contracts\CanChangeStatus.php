<?php

namespace Domains\Students\RiskAssessment\Contracts;

interface CanChangeStatus
{
    /**
     * Mark the assessment as low risk.
     */
    public function markAsLowRisk(): void;

    /**
     * Mark the assessment as medium risk.
     */
    public function markAsMediumRisk(): void;

    /**
     * Mark the assessment as high risk.
     */
    public function markAsHighRisk(): void;

    /**
     * Mark the assessment as critical risk.
     */
    public function markAsCriticalRisk(): void;

    /**
     * Mark the assessment with a specific warning level.
     */
    public function markAsWarning(int $level): void;

    /**
     * Get the current risk level.
     */
    public function getRiskLevel(): int;

    /**
     * Get the current warning level.
     */
    public function getWarningLevel(): int;
}
