<?php

namespace Domains\Students\RiskAssessment\Contracts;

interface CanSendNotification
{
    /**
     * Send notification for the risk assessment.
     */
    public function notify(): void;

    /**
     * Check if notification should be sent.
     */
    public function shouldNotify(): bool;

    /**
     * Get notification recipients.
     */
    public function getNotificationRecipients(): array;

    /**
     * Get notification message.
     */
    public function getNotificationMessage(): array;
}
