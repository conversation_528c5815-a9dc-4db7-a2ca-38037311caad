<?php

namespace Domains\Students\RiskAssessment\Contracts;

interface HasConfig
{
    /**
     * Get configuration value by key.
     */
    public function config(string $key, $default = null): mixed;

    /**
     * Set configuration value.
     */
    public function setConfig(string $key, $value): void;

    /**
     * Get all configuration.
     */
    public function getConfig(): array;

    /**
     * Check if configuration key exists.
     */
    public function hasConfig(string $key): bool;
}
