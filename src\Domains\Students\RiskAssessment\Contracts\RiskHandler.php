<?php

namespace Domains\Students\RiskAssessment\Contracts;

interface RiskHandler
{
    /**
     * Handle the risk assessment.
     */
    public function handle(): void;

    /**
     * Get the assessment type.
     */
    public function getType(): string;

    /**
     * Get the assessment level.
     */
    public function getLevel(): int;

    /**
     * Get the assessment model.
     */
    public function getModel(): \Domains\Students\RiskAssessment\Models\StudentRiskAssessmentSemester;

    /**
     * Check if the assessment is valid for processing.
     */
    public function isValid(): bool;

    /**
     * Get the current risk level.
     */
    public function getRiskLevel(): int;

    /**
     * Get the current warning level.
     */
    public function getWarningLevel(): int;
}
