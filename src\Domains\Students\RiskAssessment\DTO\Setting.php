<?php

namespace Domains\Students\RiskAssessment\DTO;

use Support\Contracts\ArrayableEntity;
use Support\Traits\ArrayToProps;

class Setting implements ArrayableEntity
{
    use ArrayToProps;

    // Main toggle
    public $riskAssessmentEnabled = true;

    // Risk Assessment Type Configuration (weeks)
    public $lowRiskWeeks = 3;

    public $mediumRiskWeeks = 6;

    public $highRiskWeeks = 12;

    // Email templates for each risk level
    public $lowRiskEmailTemplate;

    public $mediumRiskEmailTemplate;

    public $highRiskEmailTemplate;
}
