<?php

namespace Domains\Students\RiskAssessment\Handlers;

use Domains\Students\RiskAssessment\Contracts\CanSendNotification;
use Illuminate\Support\Facades\Log;

class AttendanceRiskAssessmentHandler extends BaseRiskAssessmentHandler implements CanSendNotification
{
    public function handle(): void
    {
        Log::info('>>> ATTENDANCE RISK HANDLER STARTED <<<', [
            'student_id' => $this->model->riskAssessment->student_id,
            'assessment_id' => $this->model->id,
            'risk_type' => $this->model->risk_type,
            'semester_id' => $this->model->semester_id,
        ]);

        if (! $this->isValid()) {
            Log::warning('Attendance risk assessment skipped - not valid', [
                'student_id' => $this->model->riskAssessment->student_id,
                'assessment_id' => $this->model->id,
                'reason' => 'Assessment not due or invalid',
            ]);

            return;
        }

        try {
            Log::info('Executing attendance risk assessment logic', [
                'student_id' => $this->model->riskAssessment->student_id,
                'assessment_id' => $this->model->id,
            ]);

            $this->assessAttendanceRisk();

            Log::info('>>> ATTENDANCE RISK HANDLER COMPLETED <<<', [
                'student_id' => $this->model->riskAssessment->student_id,
                'assessment_id' => $this->model->id,
                'final_risk_level' => $this->getRiskLevel(),
                'status' => 'success',
            ]);
        } catch (\Exception $e) {
            Log::error('>>> ATTENDANCE RISK HANDLER FAILED <<<', [
                'student_id' => $this->model->riskAssessment->student_id,
                'assessment_id' => $this->model->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            $this->addRemarks("Assessment failed: {$e->getMessage()}");
            throw $e;
        }
    }

    /**
     * Assess attendance risk based on student's attendance record.
     */
    protected function assessAttendanceRisk(): void
    {
        $student = $this->model->riskAssessment->student;
        $course = $this->model->riskAssessment->course;
        $semester = $this->model->semester;

        // Get student's attendance data (you may need to adjust this based on your attendance model)
        $attendanceData = $this->getAttendanceData($student, $course, $semester);

        if (empty($attendanceData)) {
            $this->markAsNoRisk();
            $this->addRemarks('No attendance data found for assessment');

            return;
        }

        $attendancePercentage = $this->calculateAttendancePercentage($attendanceData);
        $consecutiveAbsences = $this->getConsecutiveAbsences($attendanceData);
        $minimumAttendance = $this->config('minimum_attendance', 80);

        if ($this->model->isLevelFirst()) {
            // First assessment - check for early warning signs
            if ($attendancePercentage < $minimumAttendance - 10) {
                $this->markAsLowRisk();
                $this->addRemarks("Low attendance: {$attendancePercentage}% (below {$minimumAttendance}%)");
                $this->notify();
            } elseif ($attendancePercentage < $minimumAttendance) {
                $this->markAsLowRisk();
                $this->addRemarks("Below minimum attendance: {$attendancePercentage}% (minimum {$minimumAttendance}%)");
                $this->notify();
            } else {
                $this->markAsNoRisk();
                $this->addRemarks("Good attendance: {$attendancePercentage}%");
            }
        } elseif ($this->model->isLevelSecond()) {
            // Second assessment - stricter evaluation
            if ($attendancePercentage < $minimumAttendance - 15 || $consecutiveAbsences > 5) {
                $this->markAsCriticalRisk();
                $this->addRemarks("Critical: Attendance {$attendancePercentage}%, Consecutive absences: {$consecutiveAbsences}");
                $this->notify();
            } elseif ($attendancePercentage < $minimumAttendance - 5 || $consecutiveAbsences > 3) {
                $this->markAsHighRisk();
                $this->addRemarks("High risk: Attendance {$attendancePercentage}%, Consecutive absences: {$consecutiveAbsences}");
                $this->notify();
            } else {
                $this->markAsLowRisk();
                $this->addRemarks("Moderate: Attendance {$attendancePercentage}%");
            }
        } else {
            // Final assessment
            if ($attendancePercentage < $minimumAttendance - 10 || $consecutiveAbsences > 3) {
                $this->markAsHighRisk();
                $this->addRemarks("Final assessment - Critical: Attendance {$attendancePercentage}%, Consecutive absences: {$consecutiveAbsences}");
                $this->notify();
            } elseif ($attendancePercentage < $minimumAttendance) {
                $this->markAsMediumRisk();
                $this->addRemarks("Final assessment - High risk: Attendance {$attendancePercentage}%");
                $this->notify();
            } else {
                $this->markAsLowRisk();
                $this->addRemarks("Final assessment - Moderate: Attendance {$attendancePercentage}%");
            }
        }

        $this->updateData([
            'attendance_percentage' => $attendancePercentage,
            'consecutive_absences' => $consecutiveAbsences,
            'total_sessions' => count($attendanceData),
            'attended_sessions' => count(array_filter($attendanceData, fn ($a) => $a['present'])),
            'assessment_date' => now()->toDateString(),
        ]);
    }

    /**
     * Get attendance data for the student, course, and semester.
     * This is a placeholder - implement based on your attendance model.
     */
    protected function getAttendanceData($student, $course, $semester): array
    {
        // TODO: Implement based on your attendance model
        // Example: return $student->attendance()->where('course_id', $course->id)->where('semester_id', $semester->id)->get()->toArray();

        // For now, return mock data
        return [
            ['date' => '2024-01-01', 'present' => true],
            ['date' => '2024-01-02', 'present' => false],
            ['date' => '2024-01-03', 'present' => true],
            ['date' => '2024-01-04', 'present' => true],
            ['date' => '2024-01-05', 'present' => false],
        ];
    }

    /**
     * Calculate attendance percentage.
     */
    protected function calculateAttendancePercentage(array $attendanceData): float
    {
        if (empty($attendanceData)) {
            return 0;
        }

        $presentSessions = count(array_filter($attendanceData, function ($attendance) {
            return $attendance['present'];
        }));

        return round(($presentSessions / count($attendanceData)) * 100, 2);
    }

    /**
     * Get consecutive absences.
     */
    protected function getConsecutiveAbsences(array $attendanceData): int
    {
        $maxConsecutive = 0;
        $currentConsecutive = 0;

        foreach ($attendanceData as $attendance) {
            if (! $attendance['present']) {
                $currentConsecutive++;
                $maxConsecutive = max($maxConsecutive, $currentConsecutive);
            } else {
                $currentConsecutive = 0;
            }
        }

        return $maxConsecutive;
    }

    /**
     * Send notification for attendance risk.
     */
    public function notify(): void
    {
        if (! $this->shouldNotify()) {
            return;
        }

        try {
            $recipients = $this->getNotificationRecipients();
            $messageData = $this->getNotificationMessage();

            // TODO: Implement your notification system
            // Example: Notification::send($recipients, new AttendanceRiskNotification($messageData));

            Log::info('Attendance risk notification sent', [
                'student_id' => $this->model->riskAssessment->student_id,
                'recipients' => $recipients,
                'subject' => $messageData['subject'],
                'content' => $messageData['content'],
            ]);

            $this->addRemarks("Notification sent: {$messageData['subject']}");
        } catch (\Exception $e) {
            Log::error('Attendance risk notification failed', [
                'student_id' => $this->model->riskAssessment->student_id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Check if notification should be sent.
     */
    public function shouldNotify(): bool
    {
        return $this->getRiskLevel() >= 1; // Send notification for medium risk and above
    }

    /**
     * Get notification recipients.
     */
    public function getNotificationRecipients(): array
    {
        $student = $this->model->riskAssessment->student;

        return [
            'student' => $student->email,
            'admin' => config('mail.admin_email', '<EMAIL>'),
            // Add more recipients as needed
        ];
    }

    /**
     * Get notification message.
     */
    public function getNotificationMessage(): array
    {
        $riskLevel = $this->getRiskLevel();
        $student = $this->model->riskAssessment->student;
        $course = $this->model->riskAssessment->course;

        $subject = "Attendance Risk Alert - Level {$riskLevel}";
        $content = "Attendance Risk Alert: {$student->first_last_name} has a {$riskLevel} attendance risk for {$course->name}";

        return [
            'subject' => $subject,
            'content' => $content,
        ];
    }
}
