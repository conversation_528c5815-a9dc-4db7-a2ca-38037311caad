<?php

namespace Domains\Students\RiskAssessment\Handlers;

use Domains\Students\RiskAssessment\Contracts\CanSendNotification;
use Illuminate\Support\Facades\Log;

class AttendanceRiskAssessmentHandler extends BaseRiskAssessmentHandler implements CanSendNotification
{
    public function handle(): void
    {
        if (! $this->isValid()) {
            return;
        }

        try {
            $this->assessAttendanceRisk();
        } catch (\Exception $e) {
            Log::error('Attendance risk assessment failed', [
                'student_id' => $this->model->riskAssessment->student_id,
                'error' => $e->getMessage(),
            ]);
            $this->addRemarks("Assessment failed: {$e->getMessage()}");
        }
    }

    /**
     * Assess attendance risk based on student's attendance record.
     */
    protected function assessAttendanceRisk(): void
    {
        $student = $this->model->riskAssessment->student;
        $course = $this->model->riskAssessment->course;
        $semester = $this->model->semester;

        // Get student's attendance data (you may need to adjust this based on your attendance model)
        $attendanceData = $this->getAttendanceData($student, $course, $semester);

        if (empty($attendanceData)) {
            $this->markAsNoRisk();
            $this->addRemarks('No attendance data found for assessment');

            return;
        }

        $attendancePercentage = $this->calculateAttendancePercentage($attendanceData);
        $consecutiveAbsences = $this->getConsecutiveAbsences($attendanceData);
        $minimumAttendance = $this->config('minimum_attendance', 80);

        if ($this->model->isLevelFirst()) {
            // First assessment - check for early warning signs
            if ($attendancePercentage < $minimumAttendance - 10) {
                $this->markAsLowRisk();
                $this->addRemarks("Low attendance: {$attendancePercentage}% (below {$minimumAttendance}%)");
                $this->notify();
            } elseif ($attendancePercentage < $minimumAttendance) {
                $this->markAsLowRisk();
                $this->addRemarks("Below minimum attendance: {$attendancePercentage}% (minimum {$minimumAttendance}%)");
                $this->notify();
            } else {
                $this->markAsNoRisk();
                $this->addRemarks("Good attendance: {$attendancePercentage}%");
            }
        } elseif ($this->model->isLevelSecond()) {
            // Second assessment - stricter evaluation
            if ($attendancePercentage < $minimumAttendance - 15 || $consecutiveAbsences > 5) {
                $this->markAsCriticalRisk();
                $this->addRemarks("Critical: Attendance {$attendancePercentage}%, Consecutive absences: {$consecutiveAbsences}");
                $this->notify();
            } elseif ($attendancePercentage < $minimumAttendance - 5 || $consecutiveAbsences > 3) {
                $this->markAsHighRisk();
                $this->addRemarks("High risk: Attendance {$attendancePercentage}%, Consecutive absences: {$consecutiveAbsences}");
                $this->notify();
            } else {
                $this->markAsLowRisk();
                $this->addRemarks("Moderate: Attendance {$attendancePercentage}%");
            }
        } else {
            // Final assessment
            if ($attendancePercentage < $minimumAttendance - 10 || $consecutiveAbsences > 3) {
                $this->markAsHighRisk();
                $this->addRemarks("Final assessment - Critical: Attendance {$attendancePercentage}%, Consecutive absences: {$consecutiveAbsences}");
                $this->notify();
            } elseif ($attendancePercentage < $minimumAttendance) {
                $this->markAsMediumRisk();
                $this->addRemarks("Final assessment - High risk: Attendance {$attendancePercentage}%");
                $this->notify();
            } else {
                $this->markAsLowRisk();
                $this->addRemarks("Final assessment - Moderate: Attendance {$attendancePercentage}%");
            }
        }

        $this->updateData([
            'attendance_percentage' => $attendancePercentage,
            'consecutive_absences' => $consecutiveAbsences,
            'total_sessions' => count($attendanceData),
            'attended_sessions' => count(array_filter($attendanceData, fn ($a) => $a['present'])),
            'assessment_date' => now()->toDateString(),
        ]);
    }

    /**
     * Get attendance data for the student, course, and semester.
     * This is a placeholder - implement based on your attendance model.
     */
    protected function getAttendanceData($student, $course, $semester): array
    {
        // TODO: Implement based on your attendance model
        // Example: return $student->attendance()->where('course_id', $course->id)->where('semester_id', $semester->id)->get()->toArray();

        // For now, return mock data
        return [
            ['date' => '2024-01-01', 'present' => true],
            ['date' => '2024-01-02', 'present' => false],
            ['date' => '2024-01-03', 'present' => true],
            ['date' => '2024-01-04', 'present' => true],
            ['date' => '2024-01-05', 'present' => false],
        ];
    }

    /**
     * Calculate attendance percentage.
     */
    protected function calculateAttendancePercentage(array $attendanceData): float
    {
        if (empty($attendanceData)) {
            return 0;
        }

        $presentSessions = count(array_filter($attendanceData, function ($attendance) {
            return $attendance['present'];
        }));

        return round(($presentSessions / count($attendanceData)) * 100, 2);
    }

    /**
     * Get consecutive absences.
     */
    protected function getConsecutiveAbsences(array $attendanceData): int
    {
        $maxConsecutive = 0;
        $currentConsecutive = 0;

        foreach ($attendanceData as $attendance) {
            if (! $attendance['present']) {
                $currentConsecutive++;
                $maxConsecutive = max($maxConsecutive, $currentConsecutive);
            } else {
                $currentConsecutive = 0;
            }
        }

        return $maxConsecutive;
    }

    /**
     * Send notification for attendance risk.
     */
    public function notify(): void
    {
        if (! $this->shouldNotify()) {
            return;
        }

        try {
            $recipients = $this->getNotificationRecipients();
            $message = $this->getNotificationMessage();

            // TODO: Implement your notification system
            // Example: Notification::send($recipients, new AttendanceRiskNotification($message));

            Log::info('Attendance risk notification sent', [
                'student_id' => $this->model->riskAssessment->student_id,
                'recipients' => $recipients,
                'message' => $message,
            ]);

            $this->addRemarks("Notification sent: {$message}");
        } catch (\Exception $e) {
            Log::error('Attendance risk notification failed', [
                'student_id' => $this->model->riskAssessment->student_id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Check if notification should be sent.
     */
    public function shouldNotify(): bool
    {
        return $this->getRiskLevel() >= 1; // Send notification for medium risk and above
    }

    /**
     * Get notification recipients.
     */
    public function getNotificationRecipients(): array
    {
        $student = $this->model->riskAssessment->student;

        return [
            'student' => $student->email,
            'admin' => config('mail.admin_email', '<EMAIL>'),
            // Add more recipients as needed
        ];
    }

    /**
     * Get notification message.
     */
    public function getNotificationMessage(): string
    {
        $riskLevel = $this->getRiskLevelName();
        $student = $this->model->riskAssessment->student;
        $course = $this->model->riskAssessment->course;

        return "Attendance Risk Alert: {$student->first_last_name} has a {$riskLevel} attendance risk for {$course->name}";
    }
}
