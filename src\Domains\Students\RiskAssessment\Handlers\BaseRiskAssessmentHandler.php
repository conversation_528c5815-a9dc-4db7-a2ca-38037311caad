<?php

namespace Domains\Students\RiskAssessment\Handlers;

use Domains\Students\RiskAssessment\Contracts\CanChangeStatus;
use Domains\Students\RiskAssessment\Contracts\HasConfig;
use Domains\Students\RiskAssessment\Contracts\RiskHandler;
use Domains\Students\RiskAssessment\Models\StudentRiskAssessment;
use Domains\Students\RiskAssessment\Models\StudentRiskAssessmentSemester;
use Domains\Students\RiskAssessment\Services\ConfigService;
use Illuminate\Support\Facades\Log;

abstract class BaseRiskAssessmentHandler implements CanChangeStatus, HasConfig, RiskHandler
{
    protected StudentRiskAssessmentSemester $model;

    protected array $config = [];

    public function __construct(StudentRiskAssessmentSemester $model)
    {
        $this->model = $model;
        $this->loadConfig();
    }

    /**
     * Load configuration from the model and config file.
     */
    protected function loadConfig(): void
    {
        // Load config from model
        if ($this->model->config) {
            $this->config = array_merge($this->config, $this->model->config);
        }

        $savedParameters = ConfigService::categoryParameters($this->getType());
        $configParameters = config('riskassessment.categories.'.$this->getType().'.parameters', []);

        $this->config = array_merge($this->config, $configParameters, $savedParameters);

    }

    /**
     * Get configuration value by key.
     */
    public function config(string $key, $default = null): mixed
    {
        return $this->config[$key] ?? $default;
    }

    /**
     * Set configuration value.
     */
    public function setConfig(string $key, $value): void
    {
        $this->config[$key] = $value;
    }

    /**
     * Get all configuration.
     */
    public function getConfig(): array
    {
        return $this->config;
    }

    /**
     * Check if configuration key exists.
     */
    public function hasConfig(string $key): bool
    {
        return isset($this->config[$key]);
    }

    /**
     * Get the assessment type.
     */
    public function getType(): string
    {
        return $this->model->type;
    }

    /**
     * Get the assessment level.
     */
    public function getLevel(): int
    {
        return $this->model->level;
    }

    /**
     * Get the assessment model.
     */
    public function getModel(): StudentRiskAssessmentSemester
    {
        return $this->model;
    }

    /**
     * Check if the assessment is valid for processing.
     */
    public function isValid(): bool
    {
        return $this->model->isDueToday() || $this->model->isOverdue();
    }

    /**
     * Mark the assessment as low risk.
     */
    public function markAsNoRisk(): void
    {
        $this->updateRiskLevel(StudentRiskAssessment::RISK_LEVEL_NONE);
    }

    /**
     * Mark the assessment as low risk.
     */
    public function markAsLowRisk(): void
    {
        $this->updateRiskLevel(StudentRiskAssessment::RISK_LEVEL_LOW);
    }

    /**
     * Mark the assessment as medium risk.
     */
    public function markAsMediumRisk(): void
    {
        $this->updateRiskLevel(StudentRiskAssessment::RISK_LEVEL_MEDIUM);
    }

    /**
     * Mark the assessment as high risk.
     */
    public function markAsHighRisk(): void
    {
        $this->updateRiskLevel(StudentRiskAssessment::RISK_LEVEL_HIGH);
    }

    /**
     * Mark the assessment as critical risk.
     */
    public function markAsCriticalRisk(): void
    {
        $this->updateRiskLevel(StudentRiskAssessment::RISK_LEVEL_HIGH);
    }

    /**
     * Mark the assessment with a specific warning level.
     */
    public function markAsWarning(int $level): void
    {
        $this->model->update(['risk_level' => $level]);
        $this->model->riskAssessment->updateOverallLevels();
    }

    /**
     * Get the current risk level.
     */
    public function getRiskLevel(): int
    {
        return $this->model->risk_level;
    }

    /**
     * Get the current warning level.
     */
    public function getWarningLevel(): int
    {
        return $this->model->riskAssessment->warning_level;
    }

    /**
     * Update the risk level and save to database.
     */
    protected function updateRiskLevel(int $level): void
    {
        $this->model->update(['risk_level' => $level]);
        $this->model->riskAssessment->updateOverallLevels();

        Log::info('Risk assessment updated', [
            'student_id' => $this->model->riskAssessment->student_id,
            'course_id' => $this->model->riskAssessment->course_id,
            'type' => $this->getType(),
            'level' => $this->getLevel(),
            'risk_level' => $level,
        ]);
    }

    /**
     * Add remarks to the assessment.
     */
    protected function addRemarks(string $remarks): void
    {
        $currentRemarks = $this->model->remarks ?? '';
        $newRemarks = $currentRemarks ? $currentRemarks."\n".$remarks : $remarks;
        $this->model->update(['remarks' => $newRemarks]);
    }

    /**
     * Update assessment data.
     */
    protected function updateData(array $data): void
    {
        $currentData = $this->model->data ?? [];
        $this->model->update(['data' => array_merge($currentData, $data)]);
    }
}
