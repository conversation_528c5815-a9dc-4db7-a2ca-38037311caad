<?php

namespace Domains\Students\RiskAssessment\Handlers;

use Domains\Students\RiskAssessment\Contracts\CanSendNotification;
use Illuminate\Support\Facades\Log;

class MoodleActivityRiskAssessmentHandler extends BaseRiskAssessmentHandler implements CanSendNotification
{
    public function handle(): void
    {
        Log::info('>>> MOODLE ACTIVITY RISK HANDLER STARTED <<<', [
            'student_id' => $this->model->riskAssessment->student_id,
            'assessment_id' => $this->model->id,
            'risk_type' => $this->model->risk_type,
            'semester_id' => $this->model->semester_id,
        ]);

        if (! $this->isValid()) {
            Log::warning('Moodle activity risk assessment skipped - not valid', [
                'student_id' => $this->model->riskAssessment->student_id,
                'assessment_id' => $this->model->id,
                'reason' => 'Assessment not due or invalid',
            ]);

            return;
        }

        try {
            Log::info('Executing moodle activity risk assessment logic', [
                'student_id' => $this->model->riskAssessment->student_id,
                'assessment_id' => $this->model->id,
            ]);

            $this->assessMoodleActivityRisk();

            Log::info('>>> MOODLE ACTIVITY RISK HANDLER COMPLETED <<<', [
                'student_id' => $this->model->riskAssessment->student_id,
                'assessment_id' => $this->model->id,
                'final_risk_level' => $this->getRiskLevel(),
                'status' => 'success',
            ]);
        } catch (\Exception $e) {
            Log::error('>>> MOODLE ACTIVITY RISK HANDLER FAILED <<<', [
                'student_id' => $this->model->riskAssessment->student_id,
                'assessment_id' => $this->model->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            $this->addRemarks("Assessment failed: {$e->getMessage()}");
            throw $e;
        }
    }

    /**
     * Assess Moodle activity risk based on student's online engagement.
     */
    protected function assessMoodleActivityRisk(): void
    {
        $student = $this->model->riskAssessment->student;
        $course = $this->model->riskAssessment->course;
        $semester = $this->model->semester;

        // Get student's Moodle activity data (you may need to adjust this based on your Moodle integration)
        $activityData = $this->getMoodleActivityData($student, $course, $semester);

        if (empty($activityData)) {
            $this->markAsMediumRisk();
            $this->addRemarks('No Moodle activity data found for assessment');

            return;
        }

        $activityScore = $this->calculateActivityScore($activityData);
        $lastLoginDays = $this->getDaysSinceLastLogin($activityData);
        $minimumActivity = $this->config('minimum_activity', 70);

        if ($this->model->isLevelFirst()) {
            // First assessment - check for early warning signs
            if ($activityScore < $minimumActivity - 20 || $lastLoginDays > 14) {
                $this->markAsHighRisk();
                $this->addRemarks("Low Moodle activity: {$activityScore}% (below {$minimumActivity}%) or inactive for {$lastLoginDays} days");
                $this->notify();
            } elseif ($activityScore < $minimumActivity || $lastLoginDays > 7) {
                $this->markAsMediumRisk();
                $this->addRemarks("Below minimum Moodle activity: {$activityScore}% (minimum {$minimumActivity}%) or inactive for {$lastLoginDays} days");
                $this->notify();
            } else {
                $this->markAsLowRisk();
                $this->addRemarks("Good Moodle activity: {$activityScore}%");
            }
        } elseif ($this->model->isLevelSecond()) {
            // Second assessment - stricter evaluation
            if ($activityScore < $minimumActivity - 30 || $lastLoginDays > 21) {
                $this->markAsCriticalRisk();
                $this->addRemarks("Critical: Moodle activity {$activityScore}%, Last login: {$lastLoginDays} days ago");
                $this->notify();
            } elseif ($activityScore < $minimumActivity - 10 || $lastLoginDays > 14) {
                $this->markAsHighRisk();
                $this->addRemarks("High risk: Moodle activity {$activityScore}%, Last login: {$lastLoginDays} days ago");
                $this->notify();
            } else {
                $this->markAsMediumRisk();
                $this->addRemarks("Moderate: Moodle activity {$activityScore}%");
            }
        } else {
            // Final assessment
            if ($activityScore < $minimumActivity - 20 || $lastLoginDays > 14) {
                $this->markAsCriticalRisk();
                $this->addRemarks("Final assessment - Critical: Moodle activity {$activityScore}%, Last login: {$lastLoginDays} days ago");
                $this->notify();
            } elseif ($activityScore < $minimumActivity) {
                $this->markAsHighRisk();
                $this->addRemarks("Final assessment - High risk: Moodle activity {$activityScore}%");
                $this->notify();
            } else {
                $this->markAsMediumRisk();
                $this->addRemarks("Final assessment - Moderate: Moodle activity {$activityScore}%");
            }
        }

        $this->updateData([
            'activity_score' => $activityScore,
            'days_since_last_login' => $lastLoginDays,
            'total_activities' => count($activityData),
            'completed_activities' => count(array_filter($activityData, fn ($a) => $a['completed'])),
            'assessment_date' => now()->toDateString(),
        ]);
    }

    /**
     * Get Moodle activity data for the student, course, and semester.
     * This is a placeholder - implement based on your Moodle integration.
     */
    protected function getMoodleActivityData($student, $course, $semester): array
    {
        // TODO: Implement based on your Moodle integration
        // Example: return $student->moodleActivities()->where('course_id', $course->id)->where('semester_id', $semester->id)->get()->toArray();

        // For now, return mock data
        return [
            ['activity' => 'Assignment 1', 'completed' => true, 'score' => 85],
            ['activity' => 'Quiz 1', 'completed' => true, 'score' => 70],
            ['activity' => 'Discussion', 'completed' => false, 'score' => 0],
            ['activity' => 'Assignment 2', 'completed' => true, 'score' => 90],
            ['activity' => 'Quiz 2', 'completed' => false, 'score' => 0],
        ];
    }

    /**
     * Calculate activity score based on completed activities and scores.
     */
    protected function calculateActivityScore(array $activityData): float
    {
        if (empty($activityData)) {
            return 0;
        }

        $totalScore = 0;
        $completedActivities = 0;

        foreach ($activityData as $activity) {
            if ($activity['completed']) {
                $totalScore += $activity['score'];
                $completedActivities++;
            }
        }

        if ($completedActivities === 0) {
            return 0;
        }

        return round($totalScore / $completedActivities, 2);
    }

    /**
     * Get days since last login.
     */
    protected function getDaysSinceLastLogin(array $activityData): int
    {
        // TODO: Implement based on your Moodle integration
        // For now, return a mock value
        return 5;
    }

    /**
     * Send notification for Moodle activity risk.
     */
    public function notify(): void
    {
        if (! $this->shouldNotify()) {
            return;
        }

        try {
            $recipients = $this->getNotificationRecipients();
            $messageData = $this->getNotificationMessage();

            // TODO: Implement your notification system
            // Example: Notification::send($recipients, new MoodleActivityRiskNotification($messageData));

            Log::info('Moodle activity risk notification sent', [
                'student_id' => $this->model->riskAssessment->student_id,
                'recipients' => $recipients,
                'subject' => $messageData['subject'],
                'content' => $messageData['content'],
            ]);

            $this->addRemarks("Notification sent: {$messageData['subject']}");
        } catch (\Exception $e) {
            Log::error('Moodle activity risk notification failed', [
                'student_id' => $this->model->riskAssessment->student_id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Check if notification should be sent.
     */
    public function shouldNotify(): bool
    {
        return $this->getRiskLevel() >= 1; // Send notification for medium risk and above
    }

    /**
     * Get notification recipients.
     */
    public function getNotificationRecipients(): array
    {
        $student = $this->model->riskAssessment->student;

        return [
            'student' => $student->email,
            'admin' => config('mail.admin_email', '<EMAIL>'),
            // Add more recipients as needed
        ];
    }

    /**
     * Get notification message.
     */
    public function getNotificationMessage(): array
    {
        $riskLevel = $this->getRiskLevelName();
        $student = $this->model->riskAssessment->student;
        $course = $this->model->riskAssessment->course;

        $subject = "Moodle Activity Risk Alert - {$riskLevel} Risk";
        $content = "Moodle Activity Risk Alert: {$student->first_last_name} has a {$riskLevel} Moodle activity risk for {$course->name}";

        return [
            'subject' => $subject,
            'content' => $content,
        ];
    }
}
