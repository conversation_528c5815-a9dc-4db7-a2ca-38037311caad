<?php

namespace Domains\Students\RiskAssessment\Handlers;

use App\Model\v2\EmailTemplate;
use App\Model\v2\SmtpSetup;
use App\Model\v2\StudentInitialPaymentDetails;
use App\Repositories\StudentProfileCommonRepository;
use App\Traits\SendEmailTrait;
use Domains\Students\RiskAssessment\Contracts\CanSendNotification;
use Domains\Students\RiskAssessment\Models\StudentRiskAssessment;
use Domains\Students\RiskAssessment\Services\ConfigService;
use Illuminate\Http\Request;
use Illuminate\Support\Carbon;
use Illuminate\Support\Facades\Log;

class PaymentRiskAssessmentHandler extends BaseRiskAssessmentHandler implements CanSendNotification
{
    use SendEmailTrait {
        __construct as private traitConstructor;
    }

    public function __construct($model)
    {
        parent::__construct($model);

        // Initialize the repository for the SendEmailTrait
        $this->studentProfileCommonRepository = new StudentProfileCommonRepository;
    }

    public function handle(): void
    {
        Log::info('>>> PAYMENT RISK HANDLER STARTED <<<', [
            'student_id' => $this->model->riskAssessment->student_id,
            'assessment_id' => $this->model->id,
            'risk_type' => $this->model->risk_type,
            'semester_id' => $this->model->semester_id,
        ]);

        if (! $this->isValid()) {
            Log::warning('Payment risk assessment skipped - not valid', [
                'student_id' => $this->model->riskAssessment->student_id,
                'assessment_id' => $this->model->id,
                'reason' => 'Assessment not due or invalid',
            ]);

            return;
        }

        try {
            Log::info('Executing payment risk assessment logic', [
                'student_id' => $this->model->riskAssessment->student_id,
                'assessment_id' => $this->model->id,
            ]);

            $this->assessPaymentRisk();

            Log::info('>>> PAYMENT RISK HANDLER COMPLETED <<<', [
                'student_id' => $this->model->riskAssessment->student_id,
                'assessment_id' => $this->model->id,
                'final_risk_level' => $this->getRiskLevel(),
                'status' => 'success',
            ]);
        } catch (\Exception $e) {
            Log::error('>>> PAYMENT RISK HANDLER FAILED <<<', [
                'student_id' => $this->model->riskAssessment->student_id,
                'assessment_id' => $this->model->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            $this->addRemarks("Assessment failed: {$e->getMessage()}");
            throw $e;
        }
    }

    /**
     * Assess payment risk based on student's payment status.
     *
     * Risk Levels:
     * - Low Risk: Student has unpaid invoices but schedule is run for low risk
     * - Medium Risk: Student has unpaid invoices and schedule is run for medium risk
     * - High Risk: Student has unpaid invoices and schedule is run for high risk
     */
    protected function assessPaymentRisk(): void
    {
        $student = $this->model->riskAssessment->student;
        $course = $this->model->riskAssessment->course;
        $currentRiskType = $this->model->risk_type;
        $queueDate = $this->model->queue_date;

        // Get unpaid invoices with their details
        $unpaidInvoices = $this->getUnpaidInvoices($student->id, $course->id, $queueDate);

        if ($unpaidInvoices->isEmpty()) {
            $this->markAsNoRisk();
            $this->addRemarks('No unpaid invoices found for student');

            return;
        }

        // Get invoice numbers for the remark
        $invoiceNumbers = $unpaidInvoices->pluck('formatted_invoice_number')->toArray();
        $invoiceNumbersText = implode(', ', $invoiceNumbers);

        // Apply the risk level
        switch ($currentRiskType) {
            case StudentRiskAssessment::RISK_TYPE_LOW:
                $this->markAsLowRisk();
                $this->addRemarks("Student has unpaid invoices ({$invoiceNumbersText}) - marked as low risk.");
                break;
            case StudentRiskAssessment::RISK_TYPE_MEDIUM:
                $this->markAsMediumRisk();
                $this->addRemarks("Student has unpaid invoices ({$invoiceNumbersText}) - marked as medium risk.");
                break;
            case StudentRiskAssessment::RISK_TYPE_HIGH:
                $this->markAsHighRisk();
                $this->addRemarks("Student has unpaid invoices ({$invoiceNumbersText}) - marked as high risk.");
                break;
            default:
                $this->markAsNoRisk();
                $this->addRemarks('No risk assessment required');
        }

        // Send notification based on risk level
        $this->notify();
    }

    /**
     * Get unpaid invoices for student in rto_student_initial_payment_details table.
     */
    protected function getUnpaidInvoices(int $studentId, int $courseId, Carbon $queueDate)
    {
        return StudentInitialPaymentDetails::where('student_id', $studentId)
            ->where('course_id', $courseId)
            ->where('payment_status', StudentInitialPaymentDetails::STATUS_UNPAID)
            ->where('due_date', '<=', $queueDate)
            ->get(['id', 'invoice_number', 'due_date', 'upfront_fee_to_pay']);
    }

    /**
     * Check if student has unpaid invoices in rto_student_initial_payment_details table.
     */
    protected function hasUnpaidInvoices(int $studentId, int $courseId, Carbon $queueDate): bool
    {
        return $this->getUnpaidInvoices($studentId, $courseId, $queueDate)->isNotEmpty();
    }

    /**
     * Send notification for payment risk.
     */
    public function notify(): void
    {
        if (! $this->shouldNotify()) {
            return;
        }

        try {
            $recipients = $this->getNotificationRecipients();
            $emailData = $this->getNotificationMessage();

            // Get student and course data
            $student = $this->model->riskAssessment->student;
            $course = $this->model->riskAssessment->course;

            // Prepare email data for the trait
            $request = new Request([
                'student_id' => $student->id,
                'course_id' => $course->id,
                'email_subject' => $emailData['subject'],
                'email_content' => $emailData['content'],
                'email_type' => 'course',
                'email_from' => $this->getFromEmail(),
                'college_id' => $student->college_id,
                'user_id' => $student->created_by,
                'log_type' => 'risk_assessment',
                'offer_comm_log' => 'on',
            ]);

            // Send email using the trait
            $result = $this->sendMailToStudentTrait($request);

            if (isset($result['fail_msg']) && ! empty($result['fail_msg'])) {
                Log::error('Risk assessment email failed', [
                    'student_id' => $student->id,
                    'error' => $result['fail_msg'],
                ]);
            } else {
                Log::info('Payment risk notification sent', [
                    'student_id' => $this->model->riskAssessment->student_id,
                    'recipients' => $recipients,
                    'content' => $emailData['content'],
                    'subject' => $emailData['subject'],
                ]);

                $this->addRemarks("Notification sent: {$emailData['subject']}");
            }

        } catch (\Exception $e) {
            Log::error('Payment risk notification failed', [
                'student_id' => $this->model->riskAssessment->student_id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    public function getFromEmail(): string
    {
        $fromEmail = SmtpSetup::where('status', 1)->first();

        return $fromEmail->email ?? config('mail.admin.address');
    }

    /**
     * Check if notification should be sent.
     */
    public function shouldNotify(): bool
    {
        // Send notification for all risk levels (low, medium, high)
        return in_array($this->getRiskLevel(), [
            StudentRiskAssessment::RISK_TYPE_LOW,
            StudentRiskAssessment::RISK_TYPE_MEDIUM,
            StudentRiskAssessment::RISK_TYPE_HIGH,
        ]);
    }

    /**
     * Get notification recipients.
     */
    public function getNotificationRecipients(): array
    {
        $student = $this->model->riskAssessment->student;

        return [
            'student' => $student->email,
            'admin' => config('mail.admin.address', '<EMAIL>'),
            // Add more recipients as needed
        ];
    }

    /**
     * Get notification message with subject and content.
     */
    public function getNotificationMessage(): array
    {
        $student = $this->model->riskAssessment->student;

        // Get email template from settings
        $setting = ConfigService::globalSettings();
        $emailTemplateId = match ($this->getRiskLevel()) {
            StudentRiskAssessment::RISK_TYPE_LOW => $setting->lowRiskEmailTemplate,
            StudentRiskAssessment::RISK_TYPE_MEDIUM => $setting->mediumRiskEmailTemplate,
            StudentRiskAssessment::RISK_TYPE_HIGH => $setting->highRiskEmailTemplate,
            default => null,
        };

        if (! $emailTemplateId) {
            // Fallback to default message if no template configured
            return [
                'subject' => 'Payment Risk Alert',
                'content' => 'You have a payment risk that requires attention.',
            ];
        }

        // Get email template content from database
        $emailTemplate = EmailTemplate::where('college_id', $student->college_id)
            ->where('id', $emailTemplateId)
            ->where('status', 1)
            ->first(['email_subject', 'content']);

        if (! $emailTemplate) {
            // Fallback if template not found
            return [
                'subject' => 'Payment Risk Alert',
                'content' => 'You have a payment risk that requires attention.',
            ];
        }

        return [
            'subject' => $emailTemplate->email_subject,
            'content' => $emailTemplate->content,
        ];
    }
}
