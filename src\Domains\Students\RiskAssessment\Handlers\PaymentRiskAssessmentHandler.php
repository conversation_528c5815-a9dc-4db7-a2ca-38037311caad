<?php

namespace Domains\Students\RiskAssessment\Handlers;

use Domains\Students\RiskAssessment\Contracts\CanSendNotification;
use Domains\Students\RiskAssessment\Services\ConfigService;
use Illuminate\Support\Facades\Log;

class PaymentRiskAssessmentHandler extends BaseRiskAssessmentHandler implements CanSendNotification
{
    public function handle(): void
    {
        if (! $this->isValid()) {
            return;
        }

        try {
            $this->assessPaymentRisk();
        } catch (\Exception $e) {
            Log::error('Payment risk assessment failed', [
                'student_id' => $this->model->riskAssessment->student_id,
                'error' => $e->getMessage(),
            ]);
            $this->addRemarks("Assessment failed: {$e->getMessage()}");
        }
    }

    /**
     * Assess payment risk based on student's payment status.
     *
     * Risk Levels:
     * - Low Risk: Approved for a Payment Plan and must pay all fees for the semester by the semester census date
     * - Medium Risk: Student did not apply for / was not approved for a Payment Plan; did not pay fees in full by the semester start date; and was issued an Intention to Cancel their Enrolment (ITC)
     * - High Risk: Student did not apply for / was not approved for a Payment Plan and did not pay fees by the semester start date; and didn't (successfully) appeal the ITC ≤ 20 days of its issue date; and now requires a CoE cancellation on PRISMS
     */
    protected function assessPaymentRisk(): void
    {
        $student = $this->model->riskAssessment->student;
        $course = $this->model->riskAssessment->course;
        $semester = $this->model->semester;

        // Get payment information
        $paymentInfo = $this->getPaymentInfo($student, $course, $semester);

        if (! $paymentInfo) {
            $this->markAsNoRisk();
            $this->addRemarks('No payment information found');

            return;
        }

        // Assess risk based on payment plan and payment status
        $riskLevel = $this->determinePaymentRiskLevel($paymentInfo);

        // Apply the risk level
        switch ($riskLevel) {
            case 'low':
                $this->markAsLowRisk();
                break;
            case 'medium':
                $this->markAsMediumRisk();
                break;
            case 'high':
                $this->markAsHighRisk();
                break;
            default:
                $this->markAsNoRisk();
        }

        // Add detailed remarks
        $this->addPaymentRemarks($paymentInfo, $riskLevel);

        // Send notification if needed
        if ($riskLevel !== 'none') {
            $this->notify();
        }

        // Update assessment data
        $this->updateData([
            'payment_plan_approved' => $paymentInfo['payment_plan_approved'],
            'fees_paid_in_full' => $paymentInfo['fees_paid_in_full'],
            'payment_plan_status' => $paymentInfo['payment_plan_status'],
            'itc_issued' => $paymentInfo['itc_issued'],
            'itc_issue_date' => $paymentInfo['itc_issue_date']?->toDateString(),
            'appeal_submitted' => $paymentInfo['appeal_submitted'],
            'appeal_approved' => $paymentInfo['appeal_approved'],
            'coe_cancellation_required' => $paymentInfo['coe_cancellation_required'],
            'risk_level' => $riskLevel,
            'assessment_date' => now()->toDateString(),
        ]);
    }

    /**
     * Determine payment risk level based on payment information.
     */
    protected function determinePaymentRiskLevel(array $paymentInfo): string
    {
        // High Risk: No payment plan + no full payment + ITC issued + no successful appeal + CoE cancellation required
        if (
            ! $paymentInfo['payment_plan_approved'] &&
            ! $paymentInfo['fees_paid_in_full'] &&
            $paymentInfo['itc_issued'] &&
            ! $paymentInfo['appeal_approved'] &&
            $paymentInfo['coe_cancellation_required']
        ) {
            return 'high';
        }

        // Medium Risk: No payment plan + no full payment + ITC issued
        if (
            ! $paymentInfo['payment_plan_approved'] &&
            ! $paymentInfo['fees_paid_in_full'] &&
            $paymentInfo['itc_issued']
        ) {
            return 'medium';
        }

        // Low Risk: Payment plan approved (regardless of payment status)
        if ($paymentInfo['payment_plan_approved']) {
            return 'low';
        }

        // No Risk: Fees paid in full without payment plan
        if ($paymentInfo['fees_paid_in_full']) {
            return 'none';
        }

        // Default to medium risk for unclear situations
        return 'medium';
    }

    /**
     * Add detailed remarks based on payment information and risk level.
     */
    protected function addPaymentRemarks(array $paymentInfo, string $riskLevel): void
    {
        $remarks = [];

        if ($paymentInfo['payment_plan_approved']) {
            $remarks[] = "Payment plan approved: {$paymentInfo['payment_plan_status']}";
        } else {
            $remarks[] = 'No payment plan approved';
        }

        if ($paymentInfo['fees_paid_in_full']) {
            $remarks[] = 'Fees paid in full';
        } else {
            $remarks[] = 'Fees not paid in full';
        }

        if ($paymentInfo['itc_issued']) {
            $remarks[] = "ITC issued on: {$paymentInfo['itc_issue_date']->format('Y-m-d')}";

            if ($paymentInfo['appeal_submitted']) {
                if ($paymentInfo['appeal_approved']) {
                    $remarks[] = 'Appeal submitted and approved';
                } else {
                    $remarks[] = 'Appeal submitted but not approved';
                }
            } else {
                $remarks[] = 'No appeal submitted';
            }
        }

        if ($paymentInfo['coe_cancellation_required']) {
            $remarks[] = 'CoE cancellation required on PRISMS';
        }

        $remarks[] = 'Risk Level: '.ucfirst($riskLevel);

        $this->addRemarks(implode('; ', $remarks));
    }

    /**
     * Get comprehensive payment information for the student, course, and semester.
     * This is a placeholder - implement based on your payment system.
     */
    protected function getPaymentInfo($student, $course, $semester): ?array
    {
        // TODO: Implement based on your payment models
        // This should return an array with the following structure:

        return [
            'payment_plan_approved' => $this->hasApprovedPaymentPlan($student, $course, $semester),
            'fees_paid_in_full' => $this->hasPaidFeesInFull($student, $course, $semester),
            'payment_plan_status' => $this->getPaymentPlanStatus($student, $course, $semester),
            'itc_issued' => $this->hasITCIssued($student, $course, $semester),
            'itc_issue_date' => $this->getITCIssueDate($student, $course, $semester),
            'appeal_submitted' => $this->hasAppealSubmitted($student, $course, $semester),
            'appeal_approved' => $this->isAppealApproved($student, $course, $semester),
            'coe_cancellation_required' => $this->isCoECancellationRequired($student, $course, $semester),
        ];
    }

    /**
     * Check if student has an approved payment plan.
     */
    protected function hasApprovedPaymentPlan($student, $course, $semester): bool
    {
        // TODO: Implement based on your payment plan model
        // Example: return $student->paymentPlans()->where('course_id', $course->id)->where('semester_id', $semester->id)->where('status', 'approved')->exists();

        // For now, return mock data
        return false;
    }

    /**
     * Check if student has paid fees in full.
     */
    protected function hasPaidFeesInFull($student, $course, $semester): bool
    {
        // TODO: Implement based on your payment model
        // Example: return $student->payments()->where('course_id', $course->id)->where('semester_id', $semester->id)->where('status', 'paid')->sum('amount') >= $course->fees;

        // For now, return mock data
        return false;
    }

    /**
     * Get payment plan status.
     */
    protected function getPaymentPlanStatus($student, $course, $semester): string
    {
        // TODO: Implement based on your payment plan model
        // Example: return $student->paymentPlans()->where('course_id', $course->id)->where('semester_id', $semester->id)->first()?->status ?? 'none';

        // For now, return mock data
        return 'none';
    }

    /**
     * Check if ITC (Intention to Cancel) has been issued.
     */
    protected function hasITCIssued($student, $course, $semester): bool
    {
        // TODO: Implement based on your ITC model
        // Example: return $student->itcs()->where('course_id', $course->id)->where('semester_id', $semester->id)->exists();

        // For now, return mock data
        return false;
    }

    /**
     * Get ITC issue date.
     */
    protected function getITCIssueDate($student, $course, $semester): ?\Carbon\Carbon
    {
        // TODO: Implement based on your ITC model
        // Example: return $student->itcs()->where('course_id', $course->id)->where('semester_id', $semester->id)->first()?->issue_date;

        // For now, return mock data
        return null;
    }

    /**
     * Check if appeal has been submitted.
     */
    protected function hasAppealSubmitted($student, $course, $semester): bool
    {
        // TODO: Implement based on your appeal model
        // Example: return $student->appeals()->where('course_id', $course->id)->where('semester_id', $semester->id)->exists();

        // For now, return mock data
        return false;
    }

    /**
     * Check if appeal has been approved.
     */
    protected function isAppealApproved($student, $course, $semester): bool
    {
        // TODO: Implement based on your appeal model
        // Example: return $student->appeals()->where('course_id', $course->id)->where('semester_id', $semester->id)->where('status', 'approved')->exists();

        // For now, return mock data
        return false;
    }

    /**
     * Check if CoE cancellation is required on PRISMS.
     */
    protected function isCoECancellationRequired($student, $course, $semester): bool
    {
        // TODO: Implement based on your CoE model
        // Example: return $student->coes()->where('course_id', $course->id)->where('semester_id', $semester->id)->where('status', 'cancellation_required')->exists();

        // For now, return mock data
        return false;
    }

    /**
     * Send notification for payment risk.
     */
    public function notify(): void
    {
        if (! $this->shouldNotify()) {
            return;
        }

        try {

            $setting = ConfigService::globalSettings();

            if ($this->getRiskLevel() == \Domains\Students\RiskAssessment\Models\StudentRiskAssessment::RISK_LEVEL_LOW) {
                $emailTemplate = $setting->lowRiskEmailTemplate;
            } elseif ($this->getRiskLevel() == \Domains\Students\RiskAssessment\Models\StudentRiskAssessment::RISK_LEVEL_MEDIUM) {
                $emailTemplate = $setting->mediumRiskEmailTemplate;
            } elseif ($this->getRiskLevel() == \Domains\Students\RiskAssessment\Models\StudentRiskAssessment::RISK_LEVEL_HIGH) {
                $emailTemplate = $setting->highRiskEmailTemplate;
            }

            if (! $emailTemplate) {
                $emailTemplate = 'emails.riskassessment.payment.low';
            }

            $recipients = $this->getNotificationRecipients();
            $message = $this->getNotificationMessage();

            // TODO: Implement your notification system
            // Example: Notification::send($recipients, new PaymentRiskNotification($message));

            Log::info('Payment risk notification sent', [
                'student_id' => $this->model->riskAssessment->student_id,
                'recipients' => $recipients,
                'message' => $message,
            ]);

            $this->addRemarks("Notification sent: {$message}");
        } catch (\Exception $e) {
            Log::error('Payment risk notification failed', [
                'student_id' => $this->model->riskAssessment->student_id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    /**
     * Check if notification should be sent.
     */
    public function shouldNotify(): bool
    {
        // Send notification for medium and high risk levels
        return in_array($this->getRiskLevel(), [
            \Domains\Students\RiskAssessment\Models\StudentRiskAssessment::RISK_LEVEL_MEDIUM,
            \Domains\Students\RiskAssessment\Models\StudentRiskAssessment::RISK_LEVEL_HIGH,
        ]);
    }

    /**
     * Get notification recipients.
     */
    public function getNotificationRecipients(): array
    {
        $student = $this->model->riskAssessment->student;

        return [
            'student' => $student->email,
            'admin' => config('mail.admin.address', '<EMAIL>'),
            // Add more recipients as needed
        ];
    }

    /**
     * Get notification message.
     */
    public function getNotificationMessage(): string
    {
        $riskLevel = $this->getRiskLevel();
        $student = $this->model->riskAssessment->student;
        $course = $this->model->riskAssessment->course;

        $message = "Payment Risk Alert: {$student->first_last_name} has a {$riskLevel} payment risk for {$course->name}";

        // Add specific details based on risk level
        switch ($this->getRiskLevel()) {
            case \Domains\Students\RiskAssessment\Models\StudentRiskAssessment::RISK_LEVEL_HIGH:
                $message .= ' - CoE cancellation required on PRISMS';
                break;
            case \Domains\Students\RiskAssessment\Models\StudentRiskAssessment::RISK_LEVEL_MEDIUM:
                $message .= ' - ITC issued, action required';
                break;
            case \Domains\Students\RiskAssessment\Models\StudentRiskAssessment::RISK_LEVEL_LOW:
                $message .= ' - Payment plan approved';
                break;
        }

        return $message;
    }
}
