<?php

namespace Domains\Students\RiskAssessment\Handlers;

use Illuminate\Support\Facades\Log;

class ResultRiskAssessmentHandler extends BaseRiskAssessmentHandler
{
    public function handle(): void
    {
        if (! $this->isValid()) {
            return;
        }

        try {
            $this->assessResultRisk();
        } catch (\Exception $e) {
            Log::error('Result risk assessment failed', [
                'student_id' => $this->model->riskAssessment->student_id,
                'error' => $e->getMessage(),
            ]);
            $this->addRemarks("Assessment failed: {$e->getMessage()}");
        }
    }

    /**
     * Assess result risk based on student's academic performance.
     */
    protected function assessResultRisk(): void
    {
        $student = $this->model->riskAssessment->student;
        $course = $this->model->riskAssessment->course;

        // Get student's current results (you may need to adjust this based on your result model)
        $results = $this->getStudentResults($student, $course);

        if (empty($results)) {
            $this->markAsMediumRisk();
            $this->addRemarks('No results found for assessment');

            return;
        }

        $averageScore = $this->calculateAverageScore($results);
        $failedSubjects = $this->countFailedSubjects($results);

        if ($this->model->isLevelFirst()) {
            // First assessment - check for early warning signs
            if ($averageScore < 60) {
                $this->markAsHighRisk();
                $this->addRemarks("Low average score: {$averageScore}%");
            } elseif ($averageScore < 70) {
                $this->markAsMediumRisk();
                $this->addRemarks("Below average score: {$averageScore}%");
            } else {
                $this->markAsLowRisk();
                $this->addRemarks("Good average score: {$averageScore}%");
            }
        } elseif ($this->model->isLevelSecond()) {
            // Second assessment - stricter evaluation
            if ($averageScore < 50 || $failedSubjects > 2) {
                $this->markAsCriticalRisk();
                $this->addRemarks("Critical: Average score {$averageScore}%, Failed subjects: {$failedSubjects}");
            } elseif ($averageScore < 60 || $failedSubjects > 0) {
                $this->markAsHighRisk();
                $this->addRemarks("High risk: Average score {$averageScore}%, Failed subjects: {$failedSubjects}");
            } else {
                $this->markAsMediumRisk();
                $this->addRemarks("Moderate: Average score {$averageScore}%");
            }
        } else {
            // Final assessment
            if ($averageScore < 50 || $failedSubjects > 1) {
                $this->markAsCriticalRisk();
                $this->addRemarks("Final assessment - Critical: Average score {$averageScore}%, Failed subjects: {$failedSubjects}");
            } elseif ($averageScore < 60) {
                $this->markAsHighRisk();
                $this->addRemarks("Final assessment - High risk: Average score {$averageScore}%");
            } else {
                $this->markAsMediumRisk();
                $this->addRemarks("Final assessment - Moderate: Average score {$averageScore}%");
            }
        }

        $this->updateData([
            'average_score' => $averageScore,
            'failed_subjects' => $failedSubjects,
            'total_subjects' => count($results),
            'assessment_date' => now()->toDateString(),
        ]);
    }

    /**
     * Get student results for the course.
     * This is a placeholder - implement based on your result model.
     */
    protected function getStudentResults($student, $course): array
    {
        // TODO: Implement based on your result model
        // Example: return $student->results()->where('course_id', $course->id)->get()->toArray();

        // For now, return mock data
        return [
            ['subject' => 'Math', 'score' => 75],
            ['subject' => 'English', 'score' => 65],
            ['subject' => 'Science', 'score' => 80],
        ];
    }

    /**
     * Calculate average score from results.
     */
    protected function calculateAverageScore(array $results): float
    {
        if (empty($results)) {
            return 0;
        }

        $totalScore = array_sum(array_column($results, 'score'));

        return round($totalScore / count($results), 2);
    }

    /**
     * Count failed subjects (score < 50).
     */
    protected function countFailedSubjects(array $results): int
    {
        return count(array_filter($results, function ($result) {
            return $result['score'] < 50;
        }));
    }
}
