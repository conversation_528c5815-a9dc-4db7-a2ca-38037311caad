<?php

namespace Domains\Students\RiskAssessment\Handlers;

use App\Model\v2\EmailTemplate;
use App\Model\v2\SmtpSetup;
use App\Model\v2\StudentSubjectEnrolment;
use App\Repositories\StudentProfileCommonRepository;
use App\Traits\SendEmailTrait;
use Domains\Students\RiskAssessment\Contracts\CanSendNotification;
use Domains\Students\RiskAssessment\Models\StudentRiskAssessment;
use Domains\Students\RiskAssessment\Services\ConfigService;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Log;

class ResultRiskAssessmentHandler extends BaseRiskAssessmentHandler implements CanSendNotification
{
    use SendEmailTrait {
        __construct as private traitConstructor;
    }

    public function __construct($model)
    {
        parent::__construct($model);

        // Initialize the repository for the SendEmailTrait
        $this->studentProfileCommonRepository = new StudentProfileCommonRepository;
    }

    public function handle(): void
    {
        Log::info('>>> RESULT RISK HANDLER STARTED <<<', [
            'student_id' => $this->model->riskAssessment->student_id,
            'assessment_id' => $this->model->id,
            'risk_type' => $this->model->risk_type,
            'semester_id' => $this->model->semester_id,
        ]);

        if (! $this->isValid()) {
            Log::warning('Result risk assessment skipped - not valid', [
                'student_id' => $this->model->riskAssessment->student_id,
                'assessment_id' => $this->model->id,
                'reason' => 'Assessment not due or invalid',
            ]);

            return;
        }

        try {
            Log::info('Executing result risk assessment logic', [
                'student_id' => $this->model->riskAssessment->student_id,
                'assessment_id' => $this->model->id,
            ]);

            $this->assessResultRisk();

            Log::info('>>> RESULT RISK HANDLER COMPLETED <<<', [
                'student_id' => $this->model->riskAssessment->student_id,
                'assessment_id' => $this->model->id,
                'final_risk_level' => $this->getRiskLevel(),
                'status' => 'success',
            ]);
        } catch (\Exception $e) {
            Log::error('>>> RESULT RISK HANDLER FAILED <<<', [
                'student_id' => $this->model->riskAssessment->student_id,
                'assessment_id' => $this->model->id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);
            $this->addRemarks("Assessment failed: {$e->getMessage()}");
            throw $e;
        }
    }

    /**
     * Assess result risk based on student's academic performance.
     *
     * Risk Levels:
     * - Low Risk: Student has completed units but percentage is below low risk threshold
     * - Medium Risk: Student has completed units but percentage is below medium risk threshold
     * - High Risk: Student has completed units but percentage is below high risk threshold
     */
    protected function assessResultRisk(): void
    {
        $student = $this->model->riskAssessment->student;
        $course = $this->model->riskAssessment->course;
        $currentRiskType = $this->model->risk_type;

        // Get student's current results
        $results = $this->getStudentResults($student, $course);

        if ($results->isEmpty()) {
            $this->markAsMediumRisk();
            $this->addRemarks('No results found for assessment');

            return;
        }

        // Calculate completion percentage
        $totalUnits = $results->count();
        $completedUnits = $results->where('final_outcome', 'C')->count();
        $completionPercentage = $totalUnits > 0 ? ($completedUnits / $totalUnits) * 100 : 0;

        // Get thresholds from configuration
        $lowRiskThreshold = $this->config('low_risk_threshold');
        $mediumRiskThreshold = $this->config('medium_risk_threshold');
        $highRiskThreshold = $this->config('high_risk_threshold');

        Log::info('Result risk assessment calculation', [
            'student_id' => $student->id,
            'total_units' => $totalUnits,
            'completed_units' => $completedUnits,
            'completion_percentage' => $completionPercentage,
            'low_threshold' => $lowRiskThreshold,
            'medium_threshold' => $mediumRiskThreshold,
            'high_threshold' => $highRiskThreshold,
            'current_risk_type' => $currentRiskType,
        ]);

        // Apply the risk level based on completion percentage and current risk type
        switch ($currentRiskType) {
            case StudentRiskAssessment::RISK_TYPE_LOW:
                if ($completionPercentage < $lowRiskThreshold) {
                    $this->markAsLowRisk();
                    $this->addRemarks("Student completion rate ({$completionPercentage}%) is below low risk threshold ({$lowRiskThreshold}%) - marked as low risk.");
                } else {
                    $this->markAsNoRisk();
                    $this->addRemarks("Student completion rate ({$completionPercentage}%) meets low risk threshold ({$lowRiskThreshold}%) - no risk.");
                }
                break;

            case StudentRiskAssessment::RISK_TYPE_MEDIUM:
                if ($completionPercentage < $mediumRiskThreshold) {
                    $this->markAsMediumRisk();
                    $this->addRemarks("Student completion rate ({$completionPercentage}%) is below medium risk threshold ({$mediumRiskThreshold}%) - marked as medium risk.");
                } else {
                    $this->markAsNoRisk();
                    $this->addRemarks("Student completion rate ({$completionPercentage}%) meets medium risk threshold ({$mediumRiskThreshold}%) - no risk.");
                }
                break;

            case StudentRiskAssessment::RISK_TYPE_HIGH:
                if ($completionPercentage < $highRiskThreshold) {
                    $this->markAsHighRisk();
                    $this->addRemarks("Student completion rate ({$completionPercentage}%) is below high risk threshold ({$highRiskThreshold}%) - marked as high risk.");
                } else {
                    $this->markAsNoRisk();
                    $this->addRemarks("Student completion rate ({$completionPercentage}%) meets high risk threshold ({$highRiskThreshold}%) - no risk.");
                }
                break;

            default:
                $this->markAsNoRisk();
                $this->addRemarks('No risk assessment required');
        }

        // Send notification based on risk level
        $this->notify();
    }

    /**
     * Get student results for the course.
     */
    protected function getStudentResults($student, $course)
    {
        return StudentSubjectEnrolment::where('student_id', $student->id)
            ->where('course_id', $course->id)
            ->whereNotNull('final_outcome')
            ->where('final_outcome', '!=', 'Enrolled')
            ->get(['id', 'unit_id', 'final_outcome', 'grade', 'marks']);
    }

    /**
     * Send notification for result risk.
     */
    public function notify(): void
    {
        if (! $this->shouldNotify()) {
            return;
        }

        try {
            $recipients = $this->getNotificationRecipients();
            $emailData = $this->getNotificationMessage();

            // Get student and course data
            $student = $this->model->riskAssessment->student;
            $course = $this->model->riskAssessment->course;

            // Prepare email data for the trait
            $request = new Request([
                'student_id' => $student->id,
                'course_id' => $course->id,
                'email_subject' => $emailData['subject'],
                'email_content' => $emailData['content'],
                'email_type' => 'course',
                'email_from' => $this->getFromEmail(),
                'college_id' => $student->college_id,
                'user_id' => $student->created_by,
                'log_type' => 'risk_assessment',
                'offer_comm_log' => 'on',
            ]);

            // Send email using the trait
            $result = $this->sendMailToStudentTrait($request);

            if (isset($result['fail_msg']) && ! empty($result['fail_msg'])) {
                Log::error('Risk assessment email failed', [
                    'student_id' => $student->id,
                    'error' => $result['fail_msg'],
                ]);
            } else {
                Log::info('Result risk notification sent', [
                    'student_id' => $this->model->riskAssessment->student_id,
                    'recipients' => $recipients,
                    'content' => $emailData['content'],
                    'subject' => $emailData['subject'],
                ]);

                $this->addRemarks("Notification sent: {$emailData['subject']}");
            }

        } catch (\Exception $e) {
            Log::error('Result risk notification failed', [
                'student_id' => $this->model->riskAssessment->student_id,
                'error' => $e->getMessage(),
            ]);
        }
    }

    public function getFromEmail(): string
    {
        $fromEmail = SmtpSetup::where('status', 1)->first();

        return $fromEmail->email ?? config('mail.admin.address');
    }

    /**
     * Check if notification should be sent.
     */
    public function shouldNotify(): bool
    {
        // Send notification for all risk levels (low, medium, high)
        return in_array($this->getRiskLevel(), [
            StudentRiskAssessment::RISK_TYPE_LOW,
            StudentRiskAssessment::RISK_TYPE_MEDIUM,
            StudentRiskAssessment::RISK_TYPE_HIGH,
        ]);
    }

    /**
     * Get notification recipients.
     */
    public function getNotificationRecipients(): array
    {
        $student = $this->model->riskAssessment->student;

        return [
            'student' => $student->email,
            'admin' => config('mail.admin.address', '<EMAIL>'),
            // Add more recipients as needed
        ];
    }

    /**
     * Get notification message with subject and content.
     */
    public function getNotificationMessage(): array
    {
        $student = $this->model->riskAssessment->student;

        // Get email template from settings
        $setting = ConfigService::globalSettings();
        $emailTemplateId = match ($this->getRiskLevel()) {
            StudentRiskAssessment::RISK_TYPE_LOW => $setting->lowRiskEmailTemplate,
            StudentRiskAssessment::RISK_TYPE_MEDIUM => $setting->mediumRiskEmailTemplate,
            StudentRiskAssessment::RISK_TYPE_HIGH => $setting->highRiskEmailTemplate,
            default => null,
        };

        if (! $emailTemplateId) {
            // Fallback to default message if no template configured
            return [
                'subject' => 'Result Risk Alert',
                'content' => 'You have a result risk that requires attention.',
            ];
        }

        // Get email template content from database
        $emailTemplate = EmailTemplate::where('college_id', $student->college_id)
            ->where('id', $emailTemplateId)
            ->where('status', 1)
            ->first(['email_subject', 'content']);

        if (! $emailTemplate) {
            // Fallback if template not found
            return [
                'subject' => 'Result Risk Alert',
                'content' => 'You have a result risk that requires attention.',
            ];
        }

        return [
            'subject' => $emailTemplate->email_subject,
            'content' => $emailTemplate->content,
        ];
    }
}
