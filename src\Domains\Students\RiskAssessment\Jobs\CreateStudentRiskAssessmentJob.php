<?php

namespace Domains\Students\RiskAssessment\Jobs;

use Domains\Students\RiskAssessment\Services\RiskAssessmentService;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class CreateStudentRiskAssessmentJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 3;

    public $timeout = 300;

    /**
     * Create a new job instance.
     */
    public function __construct(
        public int $studentSubjectEnrolmentId
    ) {}

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            Log::info('CreateStudentRiskAssessmentJob started', [
                'student_subject_enrolment_id' => $this->studentSubjectEnrolmentId,
            ]);

            $riskAssessmentService = new RiskAssessmentService;
            $riskAssessmentService->createRiskAssessmentForEnrolment($this->studentSubjectEnrolmentId);

            Log::info('CreateStudentRiskAssessmentJob completed successfully', [
                'student_subject_enrolment_id' => $this->studentSubjectEnrolmentId,
            ]);
        } catch (\Exception $e) {
            Log::error('CreateStudentRiskAssessmentJob failed', [
                'student_subject_enrolment_id' => $this->studentSubjectEnrolmentId,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('CreateStudentRiskAssessmentJob permanently failed', [
            'student_subject_enrolment_id' => $this->studentSubjectEnrolmentId,
            'error' => $exception->getMessage(),
            'trace' => $exception->getTraceAsString(),
        ]);
    }
}
