<?php

namespace Domains\Students\RiskAssessment\Jobs;

use Domains\Students\RiskAssessment\Models\StudentRiskAssessmentSemester;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class ProcessRiskAssessmentsJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 1;

    public $timeout = 600; // 10 minutes

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            Log::info('Starting risk assessment processing');

            // Get all assessments due today
            $dueAssessments = StudentRiskAssessmentSemester::where('queue_date', now()->toDateString())
                ->with(['riskAssessment.student', 'riskAssessment.course', 'semester'])
                ->get();

            Log::info('Found due assessments', ['count' => $dueAssessments->count()]);

            foreach ($dueAssessments as $assessment) {
                try {
                    // Create handler and dispatch job
                    $handler = RiskJob::createHandler($assessment);
                    RiskJob::dispatch($handler);

                    Log::info('Risk assessment job dispatched', [
                        'assessment_id' => $assessment->id,
                        'type' => $assessment->type,
                        'level' => $assessment->level,
                        'student_id' => $assessment->riskAssessment->student_id,
                        'risk_level' => $handler->getRiskLevel(),
                        'warning_level' => $handler->getWarningLevel(),
                    ]);
                } catch (\Exception $e) {
                    Log::error('Failed to dispatch risk assessment job', [
                        'assessment_id' => $assessment->id,
                        'error' => $e->getMessage(),
                    ]);
                }
            }

            Log::info('Risk assessment processing completed', [
                'total_assessments' => $dueAssessments->count(),
            ]);
        } catch (\Exception $e) {
            Log::error('Risk assessment processing failed', [
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Risk assessment processing job failed permanently', [
            'error' => $exception->getMessage(),
        ]);
    }
}
