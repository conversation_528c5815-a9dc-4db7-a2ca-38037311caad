<?php

namespace Domains\Students\RiskAssessment\Jobs;

use Domains\Students\RiskAssessment\Contracts\RiskHandler;
use Domains\Students\RiskAssessment\Handlers\AttendanceRiskAssessmentHandler;
use Domains\Students\RiskAssessment\Handlers\MoodleActivityRiskAssessmentHandler;
use Domains\Students\RiskAssessment\Handlers\PaymentRiskAssessmentHandler;
use Domains\Students\RiskAssessment\Handlers\ResultRiskAssessmentHandler;
use Domains\Students\RiskAssessment\Models\StudentRiskAssessmentSemester;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;

class RiskJob implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $tries = 3;

    public $timeout = 300; // 5 minutes

    /**
     * Create a new job instance.
     */
    public function __construct(
        public RiskHandler $handler
    ) {}

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        try {
            Log::info('Processing risk assessment', [
                'type' => $this->handler->getType(),
                'level' => $this->handler->getLevel(),
                'student_id' => $this->handler->getModel()->riskAssessment->student_id,
            ]);

            $this->handler->handle();

            Log::info('Risk assessment completed successfully', [
                'type' => $this->handler->getType(),
                'level' => $this->handler->getLevel(),
                'student_id' => $this->handler->getModel()->riskAssessment->student_id,
                'risk_level' => $this->handler->getRiskLevel(),
                'warning_level' => $this->handler->getWarningLevel(),
            ]);
        } catch (\Exception $e) {
            Log::error('Risk assessment job failed', [
                'type' => $this->handler->getType(),
                'level' => $this->handler->getLevel(),
                'student_id' => $this->handler->getModel()->riskAssessment->student_id,
                'error' => $e->getMessage(),
                'trace' => $e->getTraceAsString(),
            ]);

            throw $e;
        }
    }

    /**
     * Handle a job failure.
     */
    public function failed(\Throwable $exception): void
    {
        Log::error('Risk assessment job failed permanently', [
            'type' => $this->handler->getType(),
            'level' => $this->handler->getLevel(),
            'student_id' => $this->handler->getModel()->riskAssessment->student_id,
            'error' => $exception->getMessage(),
        ]);
    }

    /**
     * Create a handler instance from assessment type.
     */
    public static function createHandler(StudentRiskAssessmentSemester $model): RiskHandler
    {
        return match ($model->type) {
            'payment' => new PaymentRiskAssessmentHandler($model),
            'result' => new ResultRiskAssessmentHandler($model),
            'attendance' => new AttendanceRiskAssessmentHandler($model),
            'moodle_activity' => new MoodleActivityRiskAssessmentHandler($model),
            default => throw new \InvalidArgumentException("Unknown assessment type: {$model->type}"),
        };
    }
}
