<?php

namespace Domains\Students\RiskAssessment\Models;

use App\Model\Students;
use App\Model\v2\Courses;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;
use Illuminate\Database\Eloquent\Relations\HasMany;

class StudentRiskAssessment extends Model
{
    use HasFactory;

    const RISK_TYPE_NONE = 0;

    const RISK_TYPE_LOW = 1;

    const RISK_TYPE_MEDIUM = 2;

    const RISK_TYPE_HIGH = 3;

    const RISK_CATEGORY_PAYMENT = 'Payment';

    const RISK_CATEGORY_RESULT = 'Result';

    const RISK_CATEGORY_ATTENDANCE = 'Attendance';

    const RISK_CATEGORY_MOODLE = 'Moodle';

    protected $table = 'student_risk_assessments';

    protected $fillable = [
        'student_id',
        'course_id',
        'risk_level',
    ];

    protected $casts = [
        'risk_level' => 'integer',
    ];

    /**
     * Get the student that owns the risk assessment.
     */
    public function student(): BelongsTo
    {
        return $this->belongsTo(Students::class, 'student_id');
    }

    /**
     * Get the course that owns the risk assessment.
     */
    public function course(): BelongsTo
    {
        return $this->belongsTo(Courses::class, 'course_id');
    }

    /**
     * Get the semester assessments for this risk assessment.
     */
    public function semesterAssessments(): HasMany
    {
        return $this->hasMany(StudentRiskAssessmentSemester::class, 'risk_assessment_id');
    }

    /**
     * Get the highest risk level from semester assessments.
     */
    public function getHighestRiskLevel(): int
    {
        return $this->semesterAssessments()->max('risk_type') ?? 0;
    }

    /**
     * Get the highest warning level from semester assessments.
     * Note: Currently using risk_type as there's no separate warning_level column
     */
    public function getHighestWarningLevel(): int
    {
        return $this->semesterAssessments()->max('risk_type') ?? 0;
    }

    /**
     * Update the overall risk and warning levels.
     */
    public function updateOverallLevels(int $level): void
    {
        $this->update([
            'risk_level' => $level,
        ]);
    }
}
