<?php

namespace Domains\Students\RiskAssessment\Models;

use App\Model\v2\Semester;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class StudentRiskAssessmentSemester extends Model
{
    use HasFactory;

    const LEVEL_FIRST = 0;

    const LEVEL_SECOND = 1;

    const LEVEL_FINAL = 2;

    protected $table = 'student_risk_assessment_semesters';

    protected $fillable = [
        'risk_assessment_id',
        'semester_id',
        'risk_category',
        'risk_type',
        'queue_date',
        'status',
        'remarks',
        'data',
        'config',
    ];

    protected $casts = [
        'risk_type' => 'integer',
        'queue_date' => 'date',
        'data' => 'array',
        'config' => 'array',
    ];

    /**
     * Get the risk assessment that owns the semester assessment.
     */
    public function riskAssessment(): BelongsTo
    {
        return $this->belongsTo(StudentRiskAssessment::class, 'risk_assessment_id');
    }

    /**
     * Get the semester that owns the assessment.
     */
    public function semester(): BelongsTo
    {
        return $this->belongsTo(Semester::class, 'semester_id');
    }

    /**
     * Check if this is the first level assessment (3 weeks).
     */
    public function isLevelFirst(): bool
    {
        return $this->risk_type === self::LEVEL_FIRST;
    }

    /**
     * Check if this is the second level assessment (6 weeks).
     */
    public function isLevelSecond(): bool
    {
        return $this->risk_type === self::LEVEL_SECOND;
    }

    /**
     * Check if this is the final level assessment.
     */
    public function isLevelFinal(): bool
    {
        return $this->risk_type === self::LEVEL_FINAL;
    }

    /**
     * Get the assessment level name.
     */
    public function getLevelName(): string
    {
        $levels = config('riskassessment.assessment_levels');
        $level = $levels[$this->risk_type] ?? null;

        return $level['name'] ?? 'unknown';
    }

    /**
     * Get the risk level name.
     */
    public function getRiskLevelName(): string
    {
        $levels = config('riskassessment.risk_types');

        return $levels[$this->risk_type] ?? 'unknown';
    }

    /**
     * Check if the assessment is due today.
     */
    public function isDueToday(): bool
    {
        return $this->queue_date->isToday();
    }

    /**
     * Check if the assessment is overdue.
     */
    public function isOverdue(): bool
    {
        return $this->queue_date->isPast();
    }
}
