<?php

namespace Domains\Students\RiskAssessment\Models;

use App\Model\v2\Semester;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Database\Eloquent\Relations\BelongsTo;

class StudentRiskAssessmentSemester extends Model
{
    use HasFactory;

    const LEVEL_FIRST = 0;

    const LEVEL_SECOND = 1;

    const LEVEL_FINAL = 2;

    protected $table = 'student_risk_assessment_semesters';

    protected $fillable = [
        'risk_assessment_id',
        'semester_id',
        'risk_category',
        'risk_type',
        'queue_date',
        'status',
        'remarks',
        'data',
        'config',
    ];

    protected $casts = [
        'risk_type' => 'integer',
        'queue_date' => 'date',
        'data' => 'array',
        'config' => 'array',
    ];

    /**
     * Get the risk assessment that owns the semester assessment.
     */
    public function riskAssessment(): BelongsTo
    {
        return $this->belongsTo(StudentRiskAssessment::class, 'risk_assessment_id');
    }

    /**
     * Get the semester that owns the assessment.
     */
    public function semester(): BelongsTo
    {
        return $this->belongsTo(Semester::class, 'semester_id');
    }

    /**
     * Check if this is the first level assessment (3 weeks).
     */
    public function isLevelFirst(): bool
    {
        return $this->risk_type === self::LEVEL_FIRST;
    }

    /**
     * Check if this is the second level assessment (6 weeks).
     */
    public function isLevelSecond(): bool
    {
        return $this->risk_type === self::LEVEL_SECOND;
    }

    /**
     * Check if this is the final level assessment.
     */
    public function isLevelFinal(): bool
    {
        return $this->risk_type === self::LEVEL_FINAL;
    }

    /**
     * Get the assessment level name.
     */
    public function getLevelName(): string
    {
        $levels = config('riskassessment.assessment_levels');
        $level = $levels[$this->risk_type] ?? null;

        return $level['name'] ?? 'unknown';
    }

    /**
     * Get the risk level name.
     */
    public function getRiskLevelName(): string
    {
        $levels = config('riskassessment.risk_types');

        return $levels[$this->risk_type] ?? 'unknown';
    }

    /**
     * Check if the assessment is due today.
     */
    public function isDueToday(): bool
    {
        return $this->queue_date->isToday();
    }

    /**
     * Check if the assessment is overdue.
     */
    public function isOverdue(): bool
    {
        return $this->queue_date->isPast();
    }

    /**
     * Initialize filters for the model.
     */
    public static function initFilters()
    {
        return static::query()->initFilters();
    }

    /**
     * Base filter scope.
     */
    public function scopeInitFilters($query)
    {
        // Apply filters from request
        $filters = request()->input('filters', []);
        if (is_string($filters)) {
            $filters = json_decode($filters, true) ?? [];
        }

        // Apply student search filter
        if (!empty($filters['student_search'])) {
            $query->whereHas('riskAssessment.student', function ($q) use ($filters) {
                $searchTerm = $filters['student_search'];
                $q->where(function ($subQuery) use ($searchTerm) {
                    $subQuery->where('first_name', 'like', "%{$searchTerm}%")
                        ->orWhere('last_name', 'like', "%{$searchTerm}%")
                        ->orWhere('student_id', 'like', "%{$searchTerm}%")
                        ->orWhereRaw("CONCAT(first_name, ' ', last_name) LIKE ?", ["%{$searchTerm}%"]);
                });
            });
        }

        // Apply course filter
        if (!empty($filters['course_id'])) {
            $query->whereHas('riskAssessment', function ($q) use ($filters) {
                $q->where('course_id', $filters['course_id']);
            });
        }

        // Apply risk type filter (multi-select)
        if (!empty($filters['risk_type']) && is_array($filters['risk_type'])) {
            $query->whereIn('risk_type', $filters['risk_type']);
        }

        // Apply status filter (multi-select)
        if (!empty($filters['status']) && is_array($filters['status'])) {
            $query->whereIn('status', $filters['status']);
        }

        return $query;
    }
}
