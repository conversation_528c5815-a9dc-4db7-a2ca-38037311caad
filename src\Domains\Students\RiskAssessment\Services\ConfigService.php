<?php

namespace Domains\Students\RiskAssessment\Services;

use Domains\Students\RiskAssessment\DTO\Setting;
use Support\DTO\MetaKey;

class ConfigService
{
    public static function globalSettings(): Setting
    {
        $settingMeta = tenant()->getMeta(MetaKey::RISK_ASSESSMENT_CONFIG);
        if ($settingMeta) {
            return Setting::LazyFromArray($settingMeta);
        }

        return new Setting;

    }

    public static function saveSetting(Setting $setting): void
    {
        tenant()->setMeta(MetaKey::RISK_ASSESSMENT_CONFIG, $setting->toArray());
        // HOW TO SAVE SETTINGS IN YOUR CONTROLLER OR LIVEWIRE METHOD

    }

    public static function categoryParameters(string $category): array
    {
        $categoryParametersMeta = tenant()->getMeta(MetaKey::RISK_ASSESSMENT_CATEGORY_PARAMETERS);
        if ($categoryParametersMeta) {
            return $categoryParametersMeta[$category] ?? [];
        }

        return [];
    }

    public static function toggleCategory(string $category): void
    {
        $key = self::categoryKey($category);
        $categoryParametersMeta = tenant()->getMeta(MetaKey::RISK_ASSESSMENT_CATEGORY_PARAMETERS);
        if (! isset($categoryParametersMeta[$key])) {
            $categoryParametersMeta[$key] = [];
        }
        $categoryParametersMeta[$key]['enabled'] = ! @$categoryParametersMeta[$key]['enabled'];
        tenant()->setMeta(MetaKey::RISK_ASSESSMENT_CATEGORY_PARAMETERS, $categoryParametersMeta);
    }

    public static function isCategoryEnabled(string $category): bool
    {
        $key = self::categoryKey($category);
        $categoryParametersMeta = tenant()->getMeta(MetaKey::RISK_ASSESSMENT_CATEGORY_PARAMETERS);

        return @$categoryParametersMeta[$key]['enabled'] ?? false;
    }

    public static function categoryKey(string $category): string
    {
        $categoryConfig = collect(config('riskassessment.categories'))->firstWhere('category', $category);
        if (! $categoryConfig) {
            throw new \Exception('Category not found');
        }

        return $category;
    }

    public static function saveCategoryParameters(string $category, array $parameters): void
    {
        $key = self::categoryKey($category);

        $categoryParametersMeta = tenant()->getMeta(MetaKey::RISK_ASSESSMENT_CATEGORY_PARAMETERS);
        $categoryParametersMeta[$key] = $parameters;
        tenant()->setMeta(MetaKey::RISK_ASSESSMENT_CATEGORY_PARAMETERS, $categoryParametersMeta);
    }

    public static function deleteCategoryParameters(string $category): void
    {
        $key = self::categoryKey($category);

        $categoryParametersMeta = tenant()->getMeta(MetaKey::RISK_ASSESSMENT_CATEGORY_PARAMETERS);
        unset($categoryParametersMeta[$key]);
        tenant()->setMeta(MetaKey::RISK_ASSESSMENT_CATEGORY_PARAMETERS, $categoryParametersMeta);
    }
}

/*
HOW TO USE THIS SERVICE IN YOUR CONTROLLER OR LIVEWIRE METHOD

// SAVING GLOBAL SETTINGS
$setting = new Setting();
$setting->lowRiskWeeks = $request->lowRiskWeeks;
$setting->lowRiskEmailTemplate = $request->lowRiskEmailTemplate;
$setting->mediumRiskWeeks = $request->mediumRiskWeeks;
$setting->mediumRiskEmailTemplate = $request->mediumRiskEmailTemplate;
$setting->highRiskWeeks = $request->highRiskWeeks;
$setting->highRiskEmailTemplate = $request->highRiskEmailTemplate;
ConfigService::saveSetting($setting);


 //Category Enable/Disable
$categoryParameters = ConfigService::toggleCategory(\Domains\Students\RiskAssessment\Models\StudentRiskAssessment::RISK_CATEGORY_PAYMENT); // key should match config('riskassessment.categories.*.category')

//check category status
$categoryParameters = ConfigService::isCategoryEnabled(\Domains\Students\RiskAssessment\Models\StudentRiskAssessment::RISK_CATEGORY_PAYMENT); // key should match config('riskassessment.categories.*.category')



// SAVING CATEGORY PARAMETERS
$categoryParameters = ConfigService::categoryParameters(\Domains\Students\RiskAssessment\Models\StudentRiskAssessment::RISK_CATEGORY_PAYMENT); // key should match config('riskassessment.categories.*.category')
$categoryParameters['days_before'] = $request->days_before;
ConfigService::saveCategoryParameters(\Domains\Students\RiskAssessment\Models\StudentRiskAssessment::RISK_CATEGORY_PAYMENT, $categoryParameters);



// USING GLOBAL SETTINGS WHEN ADDING QUEUE DATE
$setting = ConfigService::globalSettings();
$setting->lowRiskWeeks; // 3
$queueDate = now()->addWeeks($setting->lowRiskWeeks);

StudentRiskAssessmentSemester::create([
    'risk_assessment_id' => $riskAssessment->id,
    'semester_id' => $semester->id,
    'level' => StudentRiskAssessmentSemester::LEVEL_FIRST,
    'type' => \Domains\Students\RiskAssessment\Models\StudentRiskAssessment::RISK_CATEGORY_PAYMENT,
    'queue_date' => $queueDate,
]);

$setting->mediumRiskWeeks; // 6
$queueDate = now()->addWeeks($setting->mediumRiskWeeks);

StudentRiskAssessmentSemester::create([
    'risk_assessment_id' => $riskAssessment->id,
    'semester_id' => $semester->id,
    'level' => StudentRiskAssessmentSemester::LEVEL_SECOND,
    'type' => \Domains\Students\RiskAssessment\Models\StudentRiskAssessment::RISK_CATEGORY_PAYMENT,
    'queue_date' => $queueDate,
]);

*/
