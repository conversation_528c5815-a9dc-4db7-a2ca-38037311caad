<?php

namespace Domains\Students\RiskAssessment\Services;

use App\Model\v2\Semester;
use App\Model\v2\StudentSubjectEnrolment;
use Carbon\Carbon;
use Domains\Students\RiskAssessment\Models\StudentRiskAssessment;
use Domains\Students\RiskAssessment\Models\StudentRiskAssessmentSemester;

class RiskAssessmentService
{
    /**
     * Create risk assessment records for a student subject enrolment
     */
    public function createRiskAssessmentForEnrolment(int $studentSubjectEnrolmentId): void
    {
        $enrolment = StudentSubjectEnrolment::find($studentSubjectEnrolmentId);

        if (! $enrolment) {
            return;
        }

        // Check if risk assessment is globally enabled
        $globalSettings = ConfigService::globalSettings();
        if (! $globalSettings->riskAssessmentEnabled) {
            return;
        }

        // Check if risk assessment already exists for this student and course
        $existingRiskAssessment = StudentRiskAssessment::where('student_id', $enrolment->student_id)
            ->where('course_id', $enrolment->course_id)
            ->where('student_course_id', $enrolment->student_course_id)
            ->first();

        if (! $existingRiskAssessment) {
            // Create new risk assessment record
            $existingRiskAssessment = StudentRiskAssessment::create([
                'student_id' => $enrolment->student_id,
                'course_id' => $enrolment->course_id,
                'student_course_id' => $enrolment->student_course_id,
                'risk_level' => StudentRiskAssessment::RISK_TYPE_NONE,
            ]);
        }

        // Create semester assessment records for enabled categories
        $this->createSemesterAssessments($existingRiskAssessment, $enrolment);
    }

    /**
     * Create semester assessment records for enabled categories
     */
    private function createSemesterAssessments(StudentRiskAssessment $riskAssessment, StudentSubjectEnrolment $enrolment): void
    {
        $categories = config('riskassessment.categories', []);

        foreach ($categories as $categoryConfig) {
            $category = $categoryConfig['category'];
            $categoryKey = $categoryConfig['key']; // Use category key directly

            // Check if this category is enabled
            if (! ConfigService::isCategoryEnabled($category)) {
                continue;
            }

            // Check if semester assessments already exist for this category
            $existingAssessmentsCount = StudentRiskAssessmentSemester::where('risk_assessment_id', $riskAssessment->id)
                ->where('semester_id', $enrolment->semester_id)
                ->where('risk_category', $categoryKey) // Use category key directly
                ->count();

            if ($existingAssessmentsCount > 0) {
                continue;
            }

            // Get category parameters
            $categoryParameters = ConfigService::categoryParameters($category);

            // Create multiple assessment records for each risk level using config-driven approach
            $this->createAssessmentLevels($riskAssessment, $enrolment, $categoryKey, $categoryParameters);
        }
    }

    /**
     * Create assessment records for all risk levels using config-driven approach
     */
    private function createAssessmentLevels(
        StudentRiskAssessment $riskAssessment,
        StudentSubjectEnrolment $enrolment,
        string $categoryKey,
        array $categoryParameters
    ): void {
        // Get semester information to calculate queue dates from semester start
        $semester = Semester::find($enrolment->semester_id);
        if (! $semester) {
            return;
        }

        // Get global settings for weeks configuration
        $globalSettings = ConfigService::globalSettings();

        // Get assessment levels from config (config-driven approach)
        $assessmentLevelsConfig = config('riskassessment.assessment_levels', []);

        if (empty($assessmentLevelsConfig)) {
            return;
        }

        // Get semester start date as base date for queue calculations
        $semesterStartDate = Carbon::parse($semester->semester_start);

        foreach ($assessmentLevelsConfig as $levelConstant => $levelConfig) {
            // Check if this specific level already exists for this category
            $existingLevelAssessment = StudentRiskAssessmentSemester::where('risk_assessment_id', $riskAssessment->id)
                ->where('semester_id', $enrolment->semester_id)
                ->where('risk_category', $categoryKey) // Use category key directly
                ->where('risk_type', $levelConstant)
                ->first();

            if ($existingLevelAssessment) {
                continue;
            }

            // Get weeks from global settings using the setting key
            $weeksSettingKey = $levelConfig['weeks_setting_key'];
            $weeks = $globalSettings->{$weeksSettingKey} ?? $levelConfig['default_weeks'];

            // Calculate queue date from semester start date + weeks
            $queueDate = $semesterStartDate->copy()->addWeeks($weeks);

            // Create semester assessment record
            StudentRiskAssessmentSemester::create([
                'risk_assessment_id' => $riskAssessment->id,
                'semester_id' => $enrolment->semester_id,
                'risk_category' => $categoryKey, // Use category key directly
                'risk_type' => $levelConstant, // Use the constant from config
                'queue_date' => $queueDate->toDateString(),
                'is_notify' => StudentRiskAssessmentSemester::IS_NOTIFY_NO,
                'remarks' => "Auto-created from student subject enrolment - {$levelConfig['name']}",
                'data' => [
                    'student_subject_enrolment_id' => $enrolment->id,
                    'created_at' => Carbon::now()->toISOString(),
                    'level_name' => $levelConfig['name'],
                    'weeks_from_semester_start' => $weeks,
                    'semester_start_date' => $semesterStartDate->toDateString(),
                    'calculation_base' => 'semester_start_date',
                ],
                'config' => $categoryParameters,
            ]);
        }
    }
}
