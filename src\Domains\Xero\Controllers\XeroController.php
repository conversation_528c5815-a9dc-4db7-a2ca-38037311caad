<?php

namespace Domains\Xero\Controllers;

use App\Http\Controllers\Controller;
use App\Http\Middleware\IsXeroAccess;
use App\Model\v2\InvoiceSetting;
use Domains\Xero\Events\XeroConnected;
use Domains\Xero\Facades\Xero;
use Domains\Xero\Models\XeroConfig;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Log;

class XeroController extends Controller
{
    public function __construct()
    {
        $this->middleware(IsXeroAccess::class);
    }

    public function index()
    {

        if (! galaxy_feature('xero')) {
            abort(404);
        }
        $collegeId = Auth::user()->college_id;
        $syncValue = InvoiceSetting::where([
            'college_id' => $collegeId,
            'key' => 'agent_commission_sync_option',
        ])->value('value') ?? 'directSync';

        $data['title'] = 'Xero Setup';
        $data['keywords'] = 'Xero, Setup';
        $data['description'] = 'Xero Setup';
        $data['mainmenu'] = 'clients';
        $data['college_id'] = $collegeId;
        $data['xero_connected'] = Xero::isConnected();
        $data['config'] = XeroConfig::Factory();
        $data['syncOption'] = $syncValue;

        if ($data['xero_connected']) {
            // event(new XeroConnected());
        }

        // $is_xero_connect = ($this->xeroDetail->getConnectedTenant()) ? true : false;
        // $is_xero_connect = ((new StorageClass())->isConnectXero()) ? true : false;
        // $xeroTenantId = $this->getConnectedTenantID();
        // if($xeroTenantId){
        //     $savedArr = $this->xeroConstantData->getWhere(['xero_tenant_id'=>$xeroTenantId]);
        //     //$xeroTenantId = (string)$this->storage->getSession()['tenant_id'];
        //     //$xeroTenantId = $this->getConnectedTenantID();
        //     $apiInstance = $this->manageApiInstance();
        //     $apiResponse = $apiInstance->getOrganisations($xeroTenantId);
        //     $data['organisationArr'] = $apiResponse->getOrganisations()[0];
        //     $data['organisationList'] = $this->getOrganisations();
        //     $data['accountArr'] = $apiInstance->getAccounts($xeroTenantId);
        //     $data['savedArr'] = ($savedArr) ? $savedArr : [];
        //     //$data['savedArr'] = XeroConstant::all()->last();
        //     $data['xero_connect'] = true;
        // }else{
        //     $data['xero_connect'] = false;
        // }

        return view('v2.sadmin.onboardsetting.xero-setup.index', $data);
    }

    public function landing()
    {
        if (Xero::isConnected()) {
            event(new XeroConnected);

            // Check if webhook is set up
            $this->checkWebhookSetup();
        }

        return redirect()->route('xero-setup');
    }

    private function checkWebhookSetup()
    {
        try {
            // $xeroConfig = DB::table('xero_config')->first();
            $xeroConfig = XeroConfig::Factory();

            if ($xeroConfig && ! empty($xeroConfig->webhook_key)) {
                $xeroConfig->setupWebhook();
                // DB::table('xero_config')->update(['webhook_linked' => 0]);
                // $xeroConfig->webhookVerified();
                Log::info('Xero webhook setup checked on connection - '.tenant('id'));
            }
        } catch (\Exception $e) {
            Log::error('Error checking webhook setup: '.$e->getMessage());
        }
    }

    public function connect()
    {
        if (! XeroConfig::Factory()->isFilled()) {
            abort(404);
        }

        if (! Xero::isConnected()) {
            XeroConfig::Apply();

            return Xero::connect();
        } else {
            // display your tenant name

            return redirect()->route('xero-setup');
        }
    }

    public function disconnect()
    {
        if (Xero::isConnected()) {
            activity()
                ->useLog('xero_sync')
                ->log('Xero dconnection initiated');
            XeroConfig::Apply();

            try {
                Xero::disconnect();
            } catch (\Exception $e) {
                info('xero disconnect failed', [$e->getMessage()]);
            }

            XeroConfig::Factory()->cleanUp();
            activity()
                ->useLog('xero_sync')
                ->log('Xero disconnected');
        }

        return redirect('xero-setup');
    }
}
