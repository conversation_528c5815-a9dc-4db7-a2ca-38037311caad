<?php

namespace Domains\Xero\Models;

use App\Model\v2\StudentMiscellaneousPayment;
use App\Model\v2\StudentServicePayment;
use Domains\Xero\Entities\TrackingCategory;
use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;
use Support\Traits\HasJsonFields;

class XeroConfig extends Model
{
    use HasFactory;
    use HasJsonFields;

    public static $_INSTANCE = [];

    public static $_INSTANCE_CREATED_AT = null;

    protected $table = 'xero_config';

    protected $fillable = ['client_id', 'client_secret', 'webhook_key', 'webhook_linked', 'organization_data', 'default_account_code', 'paid_default_account_code', 'tax_data'];

    public $timestamps = false;

    const GLOBAL_KEY = 'GLOBAL';

    const ACCOUNT_COURSE = 'course';

    const ACCOUNT_SHORT_COURSE = 'short_course';

    const ACCOUNT_MATERIAL_FEE = 'material_fee';

    const ACCOUNT_ENROLLMENT_FEE = 'enrollment_fee';

    const ACCOUNT_MISCELLABEOUS_FEE = 'miscellaneous_fee';

    const ACCOUNT_TUTION_FEE = 'tution_fee';

    const ACCOUNT_REASSESSMENT_FEE = 'tution_fee';

    const ACCOUNT_ACCOMMODATION_FEE = 'accommodation_fee';

    const ACCOUNT_PLACEMENT_FEE = 'placement_fee';

    const ACCOUNT_AIRPORT_PICKUP_FEE = 'airport_pickup_fee';

    const ACCOUNT_OHIF = 'ohif_fee';

    const ACCOUNT_CREDITNOTE = 'creditnote';

    const AGENT_COMMISSION_GST = 'agent_commission_gst';

    const AGENT_COMMISSION_NO_GST = 'agent_commission_no_gst';

    const AGENT_BONUS = 'agent_bonus_gst';

    protected $casts = [
        'organization_data' => 'json',
        'tax_data' => 'json',
        'tracking_data' => 'json',
    ];

    public static $AccountCharts = [
        self::ACCOUNT_COURSE => 'Courses - full qualifications',
        self::ACCOUNT_SHORT_COURSE => 'Short courses',
        self::ACCOUNT_MATERIAL_FEE => 'Material fees',
        self::ACCOUNT_ENROLLMENT_FEE => 'Enrolment fees',
        self::ACCOUNT_MISCELLABEOUS_FEE => 'Miscellaneous fees',
        self::ACCOUNT_TUTION_FEE => 'Tution fees',
        self::ACCOUNT_REASSESSMENT_FEE => 'Reassessment fees',
        self::ACCOUNT_ACCOMMODATION_FEE => 'Accommodation fees',
        self::ACCOUNT_PLACEMENT_FEE => 'Placement fees',
        self::ACCOUNT_AIRPORT_PICKUP_FEE => 'Airport pick up fees',
        self::ACCOUNT_OHIF => 'Overseas health insurance fees',
        self::ACCOUNT_CREDITNOTE => 'Credit Note',
    ];

    public static $AgentAccountCharts = [
        self::AGENT_BONUS => 'Agent Bonus expenses',
        self::AGENT_COMMISSION_GST => 'Agent Commission expenses GST',
        self::AGENT_COMMISSION_NO_GST => 'Agent Commission expenses NO GST',
    ];

    public function isFilled()
    {
        return $this->client_id && $this->client_secret;
    }

    public function isMappingComplete($key = self::GLOBAL_KEY)
    {
        $accountMapConfig = @$this->organization_data['account_map'][$key];

        return $accountMapConfig && count(array_keys($accountMapConfig)) == collect(array_values($accountMapConfig))->filter(function ($item) {
            return $item != '';
        })->count();
    }

    public static function InitConfig()
    {
        self::$_INSTANCE[tenant('id')] = XeroConfig::first();
    }

    public static function Factory($params = [])
    {
        /* make sure to add galaxy tenant id to the instance array */
        $tenantId = tenant('id');
        if (! isset(self::$_INSTANCE[$tenantId]) || is_null(self::$_INSTANCE[$tenantId])) {
            self::$_INSTANCE[$tenantId] = XeroConfig::firstOrCreate($params);
            self::$_INSTANCE_CREATED_AT = now();
        }

        // if (@self::$_INSTANCE[$tenantId] && self::$_INSTANCE_CREATED_AT->addMinutes(1) < now()) {
        //     Log::info('refreshing xero config', [self::$_INSTANCE[$tenantId]->id]);
        //     self::$_INSTANCE[$tenantId] =  self::$_INSTANCE[$tenantId]->fresh();
        // }

        return self::$_INSTANCE[$tenantId];
    }

    public static function Mapped()
    {
        return self::Factory()->isMappingComplete();
    }

    public function cleanUp()
    {
        // $this->organization_data = null;
        $this->default_account_code = null;
        $this->paid_default_account_code = null;

        return $this->save();
    }

    public function setupWebhook()
    {
        $this->webhook_linked = 0;

        return $this->save();
    }

    public function webhookVerified()
    {
        $this->webhook_linked = 1;
        if (! $this->organization_data || ! isset($this->organization_data['webhook_verified_at'])) {
            $this->updateJsonField([
                'webhook_verified_at' => now()->toDateTimeString(),
            ], 'organization_data');
        }

        return $this->save();
    }

    public static function Apply()
    {

        if (XeroConfig::Factory()->isFilled()) {

            $config = config('xero');

            config(['xero' => array_merge($config, [
                'landingUri' => route('xero-landing'),
                'redirectUri' => route('xero-connect'),
                'webhookKey' => XeroConfig::Factory()->webhook_key,
                'clientId' => XeroConfig::Factory()->client_id,
                'clientSecret' => XeroConfig::Factory()->client_secret,
            ])]);
        }
    }

    public static function AccountCode($key, $campusId = null)
    {
        $campusId = $campusId ?? self::GLOBAL_KEY;
        $config = XeroConfig::Factory();
        $accountMapConfig = @$config->organization_data['account_map'];
        if ($key && $accountMapConfig && ! empty($accountMapConfig) && isset($accountMapConfig[$campusId][$key]) && $accountMapConfig[$campusId][$key] != '') {
            return $accountMapConfig[$campusId][$key];
        }

        /* if we requested for specific college and didn't find it above then check in global */
        // if ($campusId != self::GLOBAL_KEY) {
        //     $campusId = self::GLOBAL_KEY;
        //     if ($accountMapConfig && !empty($accountMapConfig) && isset($accountMapConfig[$campusId][$key]) && $accountMapConfig[$campusId][$key] != '') {
        //         return $accountMapConfig[$campusId][$key];
        //     }
        // }

        /* If we didn't find in both global and college config, check default otherwise send 200 */

        return 200;
    }

    public static function TaxCode($key, $campusId = null)
    {
        $campusId = $campusId ?? self::GLOBAL_KEY;
        $config = XeroConfig::Factory();
        $taxMapConfig = @$config->tax_data['tax_rate_map'];
        if ($taxMapConfig && ! empty($taxMapConfig) && isset($taxMapConfig[$campusId][$key]) && @$taxMapConfig[$campusId][$key] != '') {
            return $taxMapConfig[$campusId][$key];
        }

        /* if we requested for specific college and didn't find it above then check in global */
        // if ($campusId != self::GLOBAL_KEY) {
        //     $campusId = self::GLOBAL_KEY;
        //     if ($taxMapConfig && !empty($taxMapConfig) && isset($taxMapConfig[$campusId][$key]) && @$taxMapConfig[$campusId][$key] != '') {
        //         return $taxMapConfig[$campusId][$key];
        //     }
        // }

        /* If we didn't find in both global and college config, check default otherwise send 200 */

        return null;
    }

    // public function getSelectedAccountCode(){
    //     if($this->default_account_code && @$this->organization_data['accounts']){
    //         $account = collect($this->organization_data['accounts'])->filter(function($item){
    //             return $this->default_account_code == $item['AccountID'];
    //         })->first();
    //         if($account){
    //             return @$account['Code'];
    //         }
    //     }

    //     return 200;
    // }

    public static function AccountCodeForMiscellaneousPayment($type, $campusId = null)
    {
        switch ($type) {
            case StudentMiscellaneousPayment::PAYMENT_TYPE_ENROLLMENT:
                return self::AccountCode(self::ACCOUNT_ENROLLMENT_FEE, $campusId);

            case StudentMiscellaneousPayment::PAYMENT_TYPE_OSHC:
                return self::AccountCode(self::ACCOUNT_OHIF, $campusId);

            case StudentMiscellaneousPayment::PAYMMENT_TYPE_MATERIALFEE:
                return self::AccountCode(self::ACCOUNT_MATERIAL_FEE, $campusId);
        }

        return XeroConfig::Factory()->default_account_code ?? 200;
    }

    /* For now we don't have any relation so return default account code. */
    public static function AccountCodeForServicePayment(StudentServicePayment $model, $key, $campusId = null)
    {
        return self::AccountCode($key, $campusId);
    }

    public function getAgentTrackingCategory()
    {

        if ($this->tracking_data && @$this->tracking_data['categories'] && @$this->tracking_data['agent_tracking_category']) {
            $val = @collect($this->tracking_data['categories'] ?? [])->filter(function ($item) {
                return $item['TrackingCategoryID'] == $this->tracking_data['agent_tracking_category'];
            })->first();

            return $val && is_array($val) ? TrackingCategory::LazyFromArray($val) : null;
        }

        return null;
    }
}
