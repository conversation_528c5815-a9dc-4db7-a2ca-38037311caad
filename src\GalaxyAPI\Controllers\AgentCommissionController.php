<?php

namespace GalaxyAPI\Controllers;

use App\Model\v2\AgentCommission;
use GalaxyAPI\Requests\AgentCommissionBulkRequest;
use GalaxyAPI\Requests\AgentCommissionRequest;
use GalaxyAPI\Resources\AgentCommissionResource;
use GalaxyAPI\Services\AgentCommissionService;

class AgentCommissionController extends CrudBaseController
{
    public function init()
    {
        $this->withAll = [
            'agent',
            'courseItem',
        ];

        $this->loadAll = [
            'agent',
            'courseItem',
        ];

        $this->scopeWithValue = [
            'collegeId' => auth()->user()->college_id,
        ];
    }

    public function __construct()
    {
        parent::__construct(
            model: AgentCommission::class,
            storeRequest: AgentCommissionRequest::class,
            updateRequest: AgentCommissionRequest::class,
            resource: AgentCommissionResource::class,
        );
    }

    public function addAgentCommissionBulk(AgentCommissionBulkRequest $request, AgentCommissionService $service)
    {
        $data = $request->validated();
        $userId = auth()->id();
        $collegeId = auth()->user()->college_id;

        $result = $service->bulkAddCommissions($data, $userId, $collegeId);
        if ($result['success']) {
            return ajaxSuccess(['data' => $result['details']], $result['message']);
        }

        return ajaxError([], $result['message']);
    }
}
