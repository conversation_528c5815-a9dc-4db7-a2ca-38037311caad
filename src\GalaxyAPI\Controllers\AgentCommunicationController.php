<?php

namespace GalaxyAPI\Controllers;

use App\Model\v2\AgentCommunication;
use App\Model\v2\SetupSection;
use GalaxyAPI\Requests\AgentCommunicationRequest;
use GalaxyAPI\Resources\AgentCommunicationResource;
use Illuminate\Support\Facades\Auth;

class AgentCommunicationController extends CrudBaseController
{
    public function init()
    {
        $this->withAll = [
            'user',
            'agent',
            'setupType',
            'setupStatus',
        ];

        $this->scopeWithValue = [
            'collegeId' => Auth::user()?->college_id,
        ];
    }

    public function __construct()
    {
        parent::__construct(
            model: AgentCommunication::class,
            storeRequest: AgentCommunicationRequest::class,
            updateRequest: AgentCommunicationRequest::class,
            resource: AgentCommunicationResource::class,
        );
    }

    public function getFormOptions()
    {
        $data = [
            'comment_by' => Auth::user()->name,
            'category' => SetupSection::where('college_id', Auth::user()->college_id)->where('type', 2)->get(['id as value', 'value as label']),
            'status' => SetupSection::where('college_id', Auth::user()->college_id)->where('type', 3)->get(['id as value', 'value as label']),
        ];

        return ajaxSuccess([
            'data' => $data,
        ], '');
    }
}
