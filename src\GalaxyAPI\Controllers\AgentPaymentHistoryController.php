<?php

namespace GalaxyAPI\Controllers;

use App\Model\v2\StudentAgentCommission;
use GalaxyAPI\Requests\AgentPaymentHistoryRequest;
use GalaxyAPI\Resources\AgentPaymentHistoryResource;

class AgentPaymentHistoryController extends CrudBaseController
{
    public function init()
    {
        $this->withAll = [
            'transaction',
            'agent',
            'student',
            'course',
            'transaction.invoice',
        ];
    }

    public function __construct()
    {
        parent::__construct(
            model: StudentAgentCommission::class,
            storeRequest: AgentPaymentHistoryRequest::class,
            updateRequest: AgentPaymentHistoryRequest::class,
            resource: AgentPaymentHistoryResource::class,
        );
    }
}
