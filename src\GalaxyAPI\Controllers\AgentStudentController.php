<?php

namespace GalaxyAPI\Controllers;

use App\Model\v2\Student;
use GalaxyAPI\Requests\AgentStudentRequest;
use GalaxyAPI\Resources\AgentStudentResource;
use Illuminate\Support\Facades\Auth;

class AgentStudentController extends CrudBaseController
{
    public function init()
    {
        $this->scopeWithValue = [
            'collegeId' => Auth::user()?->college_id,
        ];

        $this->withAll = [
            'studentDetails',
            'studentCourses',
            'country',
        ];
    }

    public function __construct()
    {
        parent::__construct(
            model: Student::class,
            storeRequest: AgentStudentRequest::class,
            updateRequest: AgentStudentRequest::class,
            resource: AgentStudentResource::class,
        );
    }
}
