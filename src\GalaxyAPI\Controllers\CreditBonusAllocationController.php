<?php

namespace GalaxyAPI\Controllers;

use App\Model\v2\CreditBonusAllocation;
use GalaxyAPI\Requests\CreditBonusAllocationRequest;
use GalaxyAPI\Resources\CreditBonusAllocationResource;

class CreditBonusAllocationController extends CrudBaseController
{
    public function init()
    {
        $commonLoads = [
            'agent',
            'paymentMode',
        ];

        $this->withAll = [
            ...$commonLoads,
        ];
        $this->loadAll = [
            ...$commonLoads,
        ];
    }

    public function __construct()
    {
        parent::__construct(
            model: CreditBonusAllocation::class,
            storeRequest: CreditBonusAllocationRequest::class,
            updateRequest: CreditBonusAllocationRequest::class,
            resource: CreditBonusAllocationResource::class,
        );
    }
}
