<?php

namespace GalaxyAPI\Controllers;

use Exception;
use GalaxyAPI\DTO\BulkActionCallParmsDTO;
use GalaxyAPI\DTO\BulkActionDTO;
use GalaxyAPI\Enums\BulkActionSelectionTypeEnum;
use Illuminate\Database\Eloquent\Model;
use Illuminate\Foundation\Auth\Access\AuthorizesRequests;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Http\JsonResponse;
use Illuminate\Http\Request;
use Illuminate\Http\Response;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Facades\Schema;
use Illuminate\Validation\ValidationException;
use ReflectionClass;
use Symfony\Component\HttpFoundation\Response as ResponseAlias;

class CrudBaseController extends BaseController
{
    use AuthorizesRequests;
    use ValidatesRequests;

    public array $scopes = [];

    public array $scopeWithValue = [];

    public array $loadScopes = [];

    public array $loadScopeWithValue = [];

    public array $withAll = [];

    public array $withCount = [];

    public array $withAggregate = [];

    public array $loadAll = [];

    public array $loadCount = [];

    public array $loadAggregate = [];

    public bool $isApi = true; // in future we will have it for both blade and api

    public bool $forceDelete = false;

    public array $deleteScopes = [];

    public array $deleteScopeWithValue = [];

    public array $changeStatusScopes = [];

    public array $changeStatusScopeWithValue = [];

    public array $restoreScopes = [];

    public array $restoreScopeWithValue = [];

    public array $updateScopes = [];

    public array $updateScopeWithValue = [];

    public int $maxRowsPerPage = 100;

    /**
     * @var array | BulkActionDTO[]
     */
    public array $bulkActions = [];

    public function init() {}

    public function __construct(public $model, public $storeRequest, public $updateRequest, public $resource)
    {
        if (! (new $this->model instanceof Model)) {
            throw new Exception('Model is not instance of Model', Response::HTTP_INTERNAL_SERVER_ERROR);
        }
        if (! (new $this->storeRequest instanceof FormRequest)) {
            throw new Exception('StoreRequest is not instance of form request', Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        if (! (new $this->updateRequest instanceof FormRequest)) {
            throw new Exception('UpdateRequest is not instance of FormRequest', Response::HTTP_INTERNAL_SERVER_ERROR);
        }

        $constants = new ReflectionClass($this->model);

        try {
            $permissionSlug = $constants->getConstant('permissionSlug');
        } catch (Exception $e) {
            $permissionSlug = null;
        }
        if ($permissionSlug) {
            $this->middleware('permission:view-'.$this->model::permissionSlug)
                ->only(['index', 'show']);

            $this->middleware('permission:alter-'.$this->model::permissionSlug)
                ->only(['store', 'update', 'changeStatus', 'changeStatusOtherColumn', 'restore']);

            $this->middleware('permission:delete-'.$this->model::permissionSlug)
                ->only(['delete']);
        }
        // default bulk actions
        $this->bulkActions[] = new BulkActionDTO(
            label: 'Delete',
            action: 'delete',
            callback: function (BulkActionCallParmsDTO $parms) {
                return $this->deleteBulk($parms);
            },
            confirmationMessage: 'Are you sure you want to delete this records?',
            icon: 'delete',
            color: 'red',
        );
    }

    public function index()
    {
        $this->init();
        $rowsPerPage = request()->input('rowsPerPage', 10);
        if ($rowsPerPage > $this->maxRowsPerPage) {
            $rowsPerPage = $this->maxRowsPerPage;
        }
        $model = $this->model::initFilters()
            ->when(property_exists($this, 'withAll') && count($this->withAll), function ($query) {
                return $query->with($this->withAll);
            })
            ->when(property_exists($this, 'withCount') && count($this->withCount), function ($query) {
                return $query->withCount($this->withCount);
            })
            ->when(property_exists($this, 'withAggregate') && count($this->withAggregate), function ($query) {
                return $query->withAggregates($this->withAggregate);
            })
            ->when(property_exists($this, 'scopes') && count($this->scopes), function ($query) {
                foreach ($this->scopes as $value) {
                    $query->$value();
                }
            })
            ->when(property_exists($this, 'scopeWithValue') && count($this->scopeWithValue), function ($query) {
                foreach ($this->scopeWithValue as $key => $value) {
                    $query->$key($value);
                }
            });

        return $this->resource::collection($model->paginate($rowsPerPage))
            ->additional([
                'code' => Response::HTTP_OK,
                'success' => 1,
            ]);
    }

    public function store()
    {
        $data = resolve($this->storeRequest)->safe()->only((new $this->model)->getFillable());

        try {
            DB::beginTransaction();
            $model = $this->model::create($data);
            if (method_exists(new $this->model, 'afterCreateProcess')) {
                $model->afterCreateProcess();
            }

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();

            return $this->error($e->getMessage());
        }

        return $this->resource::make($model)
            ->additional([
                'code' => Response::HTTP_CREATED,
                'success' => 1,
            ]);
    }

    public function error(
        $message = 'Something went wrong',
        $data = [],
        $code = Response::HTTP_INTERNAL_SERVER_ERROR,
    ) {
        return ajaxError(
            message: $message,
            code: $code,
            data: [
                'data' => $data,
            ],
        );
    }

    public function show($id)
    {
        $this->init();
        $model = $this->model::initFilters()
            ->when(property_exists($this, 'loadAll'), function ($query) {
                return $query->with($this->loadAll);
            })
            ->when(property_exists($this, 'loadCount'), function ($query) {
                return $query->withCount($this->loadCount);
            })
            ->when(property_exists($this, 'loadAggregate'), function ($query) {
                foreach ($this->loadAggregate as $key => $value) {
                    $query->withAggregate($key, $value);
                }
            })
            ->when(property_exists($this, 'loadScopes') && count($this->loadScopes), function ($query) {
                foreach ($this->loadScopes as $value) {
                    $query->$value();
                }
            })
            ->when(property_exists($this, 'loadScopeWithValue') && count($this->loadScopeWithValue), function ($query) {
                foreach ($this->loadScopeWithValue as $key => $value) {
                    $query->$key($value);
                }
            })
            ->findOrFail($id);

        return $this->resource::make($model)->additional([
            'code' => Response::HTTP_OK,
            'success' => 1,
        ]);
    }

    public function destroy($id)
    {
        $this->init();
        $model = $this->model::initFilters()
            ->when(property_exists($this, 'deleteScopes') && count($this->deleteScopes), function ($query) {
                foreach ($this->deleteScopes as $value) {
                    $query->$value();
                }
            })
            ->when(property_exists($this, 'deleteScopeWithValue') && count($this->deleteScopeWithValue), function ($query) {
                foreach ($this->deleteScopeWithValue as $key => $value) {
                    $query->$key($value);
                }
            })
            ->findOrFail($id);
        if (method_exists(new $this->model, 'beforeDeleteProcess')) {
            $model->beforeDeleteProcess();
        }

        $this->forceDelete === true ? $model->forceDelete() : $model->delete();
        if (method_exists(new $this->model, 'afterDeleteProcess')) {
            $model->afterDeleteProcess();
        }

        return $this->success(
            message: 'Data deleted successfully',
            data: [
                'code' => ResponseAlias::HTTP_NO_CONTENT,
                'success' => 1,
            ],
            code: ResponseAlias::HTTP_NO_CONTENT
        );
    }

    public function delete()
    {
        $this->init();
        request()->validate([
            'delete_rows' => ['required', 'array'],
            'delete_rows.*' => ['required', 'exists:'.(new $this->model)->getTable().',id'],
        ]);

        try {
            DB::beginTransaction();
            foreach ((array) request()->input('delete_rows') as $item) {
                $model = $this->model::initFilters()
                    ->when(property_exists($this, 'deleteScopes') && count($this->deleteScopes), function ($query) {
                        foreach ($this->deleteScopes as $value) {
                            $query->$value();
                        }
                    })
                    ->when(property_exists($this, 'deleteScopeWithValue') && count($this->deleteScopeWithValue), function ($query) {
                        foreach ($this->deleteScopeWithValue as $key => $value) {
                            $query->$key($value);
                        }
                    })
                    ->find($item);

                if (! $model) {
                    continue;
                }

                if (method_exists(new $this->model, 'beforeDeleteProcess')) {
                    $model->beforeDeleteProcess();
                }
                $this->forceDelete === true ? $model->forceDelete() : $model->delete();
                if (method_exists(new $this->model, 'afterDeleteProcess')) {
                    $model->afterDeleteProcess();
                }
            }

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();

            return $this->error($e->getMessage());
        }

        return $this->success(
            message: 'Data deleted successfully',
            data: [
                'code' => ResponseAlias::HTTP_NO_CONTENT,
                'success' => 1,
            ],
            code: ResponseAlias::HTTP_NO_CONTENT
        );
    }

    public function success(
        $message = '',
        $data = [],
        $code = ResponseAlias::HTTP_OK,
    ): JsonResponse {
        return ajaxSuccess(
            data: [
                'code' => $code,
                'data' => $data,
            ],
            message: $message,
        );
    }

    public function changeStatusOtherColumn($id, $column)
    {
        $this->init();
        $model = $this->model::initFilters()
            ->when(property_exists($this, 'changeStatusScopes') && count($this->changeStatusScopes), function ($query) {
                foreach ($this->changeStatusScopes as $value) {
                    $query->$value();
                }
            })
            ->when(property_exists($this, 'changeStatusScopeWithValue') && count($this->changeStatusScopeWithValue), function ($query) {
                foreach ($this->changeStatusScopeWithValue as $key => $value) {
                    $query->$key($value);
                }
            })
            ->findOrFail($id);

        try {
            DB::beginTransaction();
            if (method_exists(new $this->model, 'beforeChangeStatusProcess')) {
                $model->beforeChangeStatusProcess();
            }
            if (! $this->checkFillable($model, [$column])) {
                DB::rollBack();

                throw new Exception("$column column not found in fillable");
            }
            $model->update([$column => $model->$column === 1 ? 0 : 1]);
            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();

            return $this->error($e->getMessage());
        }

        return $this->resource::make($model)->additional([
            'code' => Response::HTTP_OK,
            'success' => 1,
        ]);
    }

    protected function checkFillable($model, $columns): bool
    {
        $fillableColumns = $this->fillableColumn($model);

        $diff = array_diff($columns, $fillableColumns);

        return count($diff) > 0 ? false : true;
    }

    public function update($id)
    {
        $this->init();
        $data = resolve($this->updateRequest)->safe()->only((new $this->model)->getFillable());

        $model = $this->model::initFilters()
            ->when(property_exists($this, 'updateScopes') && count($this->updateScopes), function ($query) {
                foreach ($this->updateScopes as $value) {
                    $query->$value();
                }
            })
            ->when(property_exists($this, 'updateScopeWithValue') && count($this->updateScopeWithValue), function ($query) {
                foreach ($this->updateScopeWithValue as $key => $value) {
                    $query->$key($value);
                }
            })
            ->findOrFail($id);

        try {
            DB::beginTransaction();
            if (method_exists(new $this->model, 'beforeUpdateProcess')) {
                $model->beforeUpdateProcess();
            }
            $model->update($data);
            if (method_exists(new $this->model, 'afterUpdateProcess')) {
                $model->afterUpdateProcess();
            }

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();

            return $this->error($e->getMessage());
        }

        return $this->resource::make($model)
            ->additional([
                'code' => Response::HTTP_OK,
                'success' => 1,
            ]);
    }

    protected function fillableColumn($model): array
    {
        return Schema::getColumnListing($this->tableName($model));
    }

    protected function tableName($model): string
    {
        return $model->getTable();
    }

    public function changeStatus($id)
    {
        $this->init();
        $model = $this->model::initFilters()
            ->when(property_exists($this, 'changeStatusScopes') && count($this->changeStatusScopes), function ($query) {
                foreach ($this->changeStatusScopes as $value) {
                    $query->$value();
                }
            })
            ->when(property_exists($this, 'changeStatusScopeWithValue') && count($this->changeStatusScopeWithValue), function ($query) {
                foreach ($this->changeStatusScopeWithValue as $key => $value) {
                    $query->$key($value);
                }
            })
            ->findOrFail($id);

        try {
            DB::beginTransaction();
            if (method_exists(new $this->model, 'beforeChangeStatusProcess')) {
                $model->beforeChangeStatusProcess();
            }
            if (! $this->checkFillable($model, ['status'])) {
                DB::rollBack();

                throw new Exception('Status column not found in fillable');
            }
            $model->update(['status' => $model->status === 1 ? 0 : 1]);
            if (method_exists(new $this->model, 'afterChangeStatusProcess')) {
                $model->afterChangeStatusProcess();
            }
            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();

            return $this->error($e->getMessage());
        }

        return $this->resource::make($model)
            ->additional([
                'code' => Response::HTTP_OK,
                'success' => 1,
            ]);
    }

    public function restoreTrashed($id)
    {
        $this->init();
        $model = $this->model::initFilters()->onlyTrashed()
            ->when(property_exists($this, 'restoreScopes') && count($this->restoreScopes), function ($query) {
                foreach ($this->restoreScopes as $value) {
                    $query->$value();
                }
            })
            ->when(property_exists($this, 'restoreScopeWithValue') && count($this->restoreScopeWithValue), function ($query) {
                foreach ($this->restoreScopeWithValue as $key => $value) {
                    $query->$key($value);
                }
            })
            ->findOrFail($id);

        try {
            DB::beginTransaction();

            if (method_exists(new $this->model, 'beforeRestoreProcess')) {
                $model->beforeRestoreProcess();
            }

            $model->restore();

            if (method_exists(new $this->model, 'afterRestoreProcess')) {
                $model->afterRestoreProcess();
            }

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();

            return $this->error($e->getMessage());
        }

        return $this->resource::make($model)
            ->additional([
                'code' => Response::HTTP_OK,
                'success' => 1,
            ]);
    }

    public function restoreAllTrashed()
    {
        try {
            DB::beginTransaction();

            $this->model::initFilters()->onlyTrashed()->restore();

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();

            return $this->error($e->getMessage());
        }

        return $this->success(
            message: 'Data restored successfully',
            data: [
                'code' => Response::HTTP_OK,
                'success' => 1,
            ]
        );
    }

    public function forceDeleteTrashed($id)
    {
        $model = $this->model::initFilters()->onlyTrashed()->findOrFail($id);

        try {
            DB::beginTransaction();

            if (method_exists(new $this->model, 'beforeForceDeleteProcess')) {
                $model->beforeForceDeleteProcess();
            }

            $model->forceDelete();

            if (method_exists(new $this->model, 'afterForceDeleteProcess')) {
                $model->afterForceDeleteProcess();
            }

            DB::commit();
        } catch (Exception $e) {
            DB::rollBack();

            return $this->error($e->getMessage());
        }

        return $this->success(
            message: 'Data deleted successfully',
            data: [
                'code' => ResponseAlias::HTTP_NO_CONTENT,
                'success' => 1,
            ],
            code: ResponseAlias::HTTP_NO_CONTENT
        );
    }

    public function addBulkAction(
        BulkActionDTO $action
    ): self {
        $this->bulkActions[] = $action;

        return $this;
    }

    public function getBulkActions()
    {
        $this->init();
        $items = collect($this->bulkActions)->map(function ($item) {
            return $item->toArray();
        });

        return $this->success(
            data: $items,
        );
    }

    public function handelBulkActions(
        Request $request
    ) {
        $this->init();
        $validActions = collect($this->bulkActions);
        if ($validActions->count() === 0) {
            throw ValidationException::withMessages(['action' => 'Bulk actions not found']);
        }
        $request->validate([
            'action' => 'required|string|in:'.$validActions->pluck('action')->join(','),
            'type' => 'required|string|'.BulkActionSelectionTypeEnum::getValidationString(),
            'selectedIds' => 'required_if:type,'.BulkActionSelectionTypeEnum::selective->value.'|array',
            'excludeIds' => 'nullable|array',
        ]);
        $type = $request->get('type', 'selected');
        $action = $validActions->firstWhere('action', $request->action);
        $result = $action->call(new BulkActionCallParmsDTO(
            type: $type,
            selectedIds: $request->get('selectedIds', []),
            excludeIds: $request->get('excludeIds', []),
        ));
        if ($result) {
            return $this->success(
                message: 'Bulk action executed successfully',
            );
        }

        return $this->error(
            message: 'Bulk action failed',
        );
    }

    // todo: separate utility functions in separate service file in future
    // utilities
    public function deleteBulk(
        BulkActionCallParmsDTO $parms,
    ) {
        try {
            DB::beginTransaction();
            if ($parms->type == BulkActionSelectionTypeEnum::all->value) {
                $this->model::initFilters()
                    ->when(count($parms->excludeIds) > 0, function ($query) use ($parms) {
                        $query->whereNotIn('id', $parms->excludeIds);
                    })
                    ->when(property_exists($this, 'deleteScopes') && count($this->deleteScopes), function ($query) {
                        foreach ($this->deleteScopes as $value) {
                            $query->$value();
                        }
                    })
                    ->when(property_exists($this, 'deleteScopeWithValue') && count($this->deleteScopeWithValue), function ($query) {
                        foreach ($this->deleteScopeWithValue as $key => $value) {
                            $query->$key($value);
                        }
                    })
                    ->delete();
            } else {
                foreach ($parms->selectedIds as $id) {
                    $model = $this->model::initFilters()
                        ->when(count($parms->excludeIds) > 0, function ($query) use ($parms) {
                            $query->whereNotIn('id', $parms->excludeIds);
                        })
                        ->when(property_exists($this, 'deleteScopes') && count($this->deleteScopes), function ($query) {
                            foreach ($this->deleteScopes as $value) {
                                $query->$value();
                            }
                        })
                        ->when(property_exists($this, 'deleteScopeWithValue') && count($this->deleteScopeWithValue), function ($query) {
                            foreach ($this->deleteScopeWithValue as $key => $value) {
                                $query->$key($value);
                            }
                        })
                        ->find($id);
                    if (! $model) {
                        continue;
                    }
                    if (method_exists(new $this->model, 'beforeDeleteProcess')) {
                        $model->beforeDeleteProcess();
                    }
                    $this->forceDelete === true ? $model->forceDelete() : $model->delete();
                    if (method_exists(new $this->model, 'afterDeleteProcess')) {
                        $model->afterDeleteProcess();
                    }
                }
            }
            DB::commit();

            return true;
        } catch (Exception $e) {
            DB::rollBack();
            throw $e;
        }
    }
}
