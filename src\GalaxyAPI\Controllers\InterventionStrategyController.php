<?php

namespace GalaxyAPI\Controllers;

use App\Model\v2\InterventionStrategy;
use GalaxyAPI\Requests\InterventionStrategyRequest;
use GalaxyAPI\Resources\InterventionStrategyResource;

class InterventionStrategyController extends CrudBaseController
{
    public function __construct()
    {
        parent::__construct(
            model: InterventionStrategy::class,
            storeRequest: InterventionStrategyRequest::class,
            updateRequest: InterventionStrategyRequest::class,
            resource: InterventionStrategyResource::class,
        );
    }
}
