<?php

namespace GalaxyAPI\Controllers;

use App\Model\v2\InterventionType;
use GalaxyAPI\Requests\InterventionTypeRequest;
use GalaxyAPI\Resources\InterventionTypeResource;

class InterventionTypeController extends CrudBaseController
{
    public function __construct()
    {
        parent::__construct(
            model: InterventionType::class,
            storeRequest: InterventionTypeRequest::class,
            updateRequest: InterventionTypeRequest::class,
            resource: InterventionTypeResource::class,
        );
    }
}
