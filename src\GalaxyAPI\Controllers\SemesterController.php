<?php

namespace GalaxyAPI\Controllers;

use App\Model\v2\CourseType;
use App\Model\v2\Semester;
use GalaxyAPI\Requests\SemesterRequest;
use GalaxyAPI\Resources\SemesterResource;
use Illuminate\Support\Facades\Config;

class SemesterController extends CrudBaseController
{
    public function init()
    {
        $this->withAll = [
            'courseType',
        ];
        $this->loadAll = [
            'courseType',
        ];
        $this->scopeWithValue = [
            'collegeId' => auth()->user()->college_id,
        ];

        // Apply filters from request
        $filters = request()->input('filters', []);
        if (is_string($filters)) {
            $filters = json_decode($filters, true) ?? [];
        }

        // Apply course type filter if provided
        if (! empty($filters['course_type_id'])) {
            $this->scopeWithValue['filterCourseTypeId'] = $filters['course_type_id'];
        }

        // Apply year filter if provided
        if (! empty($filters['year'])) {
            $this->scopeWithValue['filterYear'] = $filters['year'];
        }

        // Apply calendar type filter if provided
        if (! empty($filters['calendar_type'])) {
            $this->scopeWithValue['filterCalendarType'] = $filters['calendar_type'];
        }
    }

    public function __construct()
    {
        parent::__construct(
            model: Semester::class,
            storeRequest: SemesterRequest::class,
            updateRequest: SemesterRequest::class,
            resource: SemesterResource::class,
        );
    }

    public function formConstants()
    {
        $collegeId = auth()->user()->college_id;

        // Get course types
        $arrCourseType = CourseType::where('status', 1)->whereIn('college_id', [0, $collegeId])->pluck('title', 'id')->toArray();

        // Get calendar types
        $arrCalendarType = Config::get('constants.arrCalendarTypeV2');

        // Generate years array
        $arrYear = [];
        $current = date('Y');
        for ($i = ($current - 2); $i < ($current + 30); $i++) {
            $arrYear[$i] = $i;
        }

        return ajaxSuccess(['data' => [
            'courseTypes' => kendify($arrCourseType, 'value', 'text'),
            'calendarTypes' => kendify($arrCalendarType, 'value', 'text'),
            'years' => kendify($arrYear, 'value', 'text'),
        ]], '');
    }
}
