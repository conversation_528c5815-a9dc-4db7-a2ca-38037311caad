<?php

namespace GalaxyAPI\Controllers;

use App\Model\v2\StudentAgentCommission;
use GalaxyAPI\Requests\StudentAgentCommissionRequest;
use GalaxyAPI\Resources\StudentAgentCommissionResource;

class StudentAgentCommissionController extends CrudBaseController
{
    public function init()
    {
        $this->withAll = [
            'transaction',
            'agent',
            'student',
            'course',
            'transaction.invoice',
        ];
    }

    public function __construct()
    {
        parent::__construct(
            model: StudentAgentCommission::class,
            storeRequest: StudentAgentCommissionRequest::class,
            updateRequest: StudentAgentCommissionRequest::class,
            resource: StudentAgentCommissionResource::class,
        );
    }
}
