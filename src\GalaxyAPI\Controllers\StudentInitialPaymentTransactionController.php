<?php

namespace GalaxyAPI\Controllers;

use App\Model\v2\StudentInitialPaymentTransaction;
use GalaxyAPI\Requests\StudentInitialPaymentTransactionRequest;
use GalaxyAPI\Resources\StudentInitialPaymentTransactionResource;

class StudentInitialPaymentTransactionController extends CrudBaseController
{
    public function init()
    {
        $this->withAll = [
            'course',
            'student',
            'studentCourses',
        ];
        $this->loadAll = [
            'course',
            'student',
            'studentCourses',
        ];
    }

    public function __construct()
    {
        parent::__construct(
            model: StudentInitialPaymentTransaction::class,
            storeRequest: StudentInitialPaymentTransactionRequest::class,
            updateRequest: StudentInitialPaymentTransactionRequest::class,
            resource: StudentInitialPaymentTransactionResource::class,
        );
    }
}
