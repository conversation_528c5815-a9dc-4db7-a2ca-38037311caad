<?php

namespace GalaxyAPI\Controllers;

use App\Model\v2\StudentIntervention;
use GalaxyAPI\Requests\StudentInterventionRequest;
use GalaxyAPI\Resources\StudentInterventionResource;

class StudentInterventionController extends CrudBaseController
{
    public function init()
    {
        $commonLoads = [
            'collage',
            'student',
            'course',
            'teacher',
            'studentSemester',
            'studentInterventionStrategies',
            'studentInterventionStrategies.interventionType',
            'studentInterventionStrategies.interventionStrategy',
        ];

        $this->withAll = [
            ...$commonLoads,
        ];
        $this->loadAll = [
            ...$commonLoads,
        ];
    }

    public function __construct()
    {
        parent::__construct(
            model: StudentIntervention::class,
            storeRequest: StudentInterventionRequest::class,
            updateRequest: StudentInterventionRequest::class,
            resource: StudentInterventionResource::class,
        );
    }
}
