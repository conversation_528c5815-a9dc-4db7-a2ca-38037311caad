<?php

namespace GalaxyAPI\Controllers;

use App\Model\v2\StudentPaymentAgentCredit;
use GalaxyAPI\Requests\StudentPaymentAgentCreditRequest;
use GalaxyAPI\Resources\StudentPaymentAgentCreditResource;

class StudentPaymentAgentCreditController extends CrudBaseController
{
    public function init()
    {
        $commonLoads = [
            'creditBonusAllocation',
            'studentInitialPayment',
            'studentInitialPayment.course',
            'studentInitialPayment.student',
            'studentInitialPayment.studentCourses',
        ];
        $this->withAll = [
            ...$commonLoads,
        ];
        $this->loadAll = [
            ...$commonLoads,
        ];
    }

    public function __construct()
    {
        parent::__construct(
            model: StudentPaymentAgentCredit::class,
            storeRequest: StudentPaymentAgentCreditRequest::class,
            updateRequest: StudentPaymentAgentCreditRequest::class,
            resource: StudentPaymentAgentCreditResource::class,
        );
    }
}
