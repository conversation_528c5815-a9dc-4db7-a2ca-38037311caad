<?php

namespace GalaxyAPI\Controllers;

use Domains\Students\RiskAssessment\Models\StudentRiskAssessment;
use GalaxyAPI\Requests\StudentRiskAssessmentRequest;
use GalaxyAPI\Resources\StudentRiskAssessmentResource;

class StudentRiskAssessmentController extends CrudBaseController
{
    public function __construct()
    {
        parent::__construct(
            model: StudentRiskAssessment::class,
            storeRequest: StudentRiskAssessmentRequest::class,
            updateRequest: StudentRiskAssessmentRequest::class,
            resource: StudentRiskAssessmentResource::class,
        );
    }

    public function index()
    {
        $students = [
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'course' => [
                    'code' => 'CHC50113',
                    'id' => 72,
                    'name' => 'Diploma of Early Childhood Education and Care',
                ],
                'risk_level' => 'HIGH',
                'progress' => 25,
                'attendance' => [
                    'present' => 45,
                    'present_percent' => 45 / 200 * 100,
                    'absent' => 9,
                    'total' => 200,
                ],
                'payment_status' => 'Overdue 30 days',
                'last_contact' => '3 days ago',
                'action' => 'Call Now',
            ],
            [
                'name' => '<PERSON>',
                'email' => '<EMAIL>',
                'course' => [
                    'code' => 'CHC50113',
                    'id' => 72,
                    'name' => 'Diploma of Early Childhood Education and Care',
                ],
                'risk_level' => 'MEDIUM',
                'progress' => 60,
                'attendance' => [
                    'present' => 72,
                    'present_percent' => 72 / 200 * 100,
                    'absent' => 14,
                    'total' => 200,
                ],
                'payment_status' => 'Due in 5 days',
                'last_contact' => '1 week ago',
                'action' => 'Follow Up',
            ],
            [
                'name' => 'Emma Williams',
                'email' => '<EMAIL>',
                'course' => [
                    'code' => 'CHC50113',
                    'id' => 72,
                    'name' => 'Diploma of Early Childhood Education and Care',
                ],
                'risk_level' => 'LOW',
                'progress' => 85,
                'attendance' => [
                    'present' => 92,
                    'present_percent' => 92 / 240 * 100,
                    'absent' => 22,
                    'total' => 240,
                ],
                'payment_status' => 'Paid',
                'last_contact' => '2 days ago',
                'action' => 'Encourage',
            ],
            [
                'name' => 'David Brown',
                'email' => '<EMAIL>',
                'course' => [
                    'code' => 'CHC50113',
                    'id' => 72,
                    'name' => 'Diploma of Early Childhood Education and Care',
                ],
                'risk_level' => 'HIGH',
                'progress' => 15,
                'attendance' => [
                    'present' => 38,
                    'present_percent' => 38 / 200 * 180,
                    'absent' => 7,
                    'total' => 180,
                ],
                'payment_status' => 'Overdue 15 days',
                'last_contact' => '1 week ago',
                'action' => 'Urgent Call',
            ],
            [
                'name' => 'Mike Chen',
                'email' => '<EMAIL>',
                'course' => [
                    'code' => 'CHC50113',
                    'id' => 72,
                    'name' => 'Diploma of Early Childhood Education and Care',
                ],
                'risk_level' => 'MEDIUM',
                'progress' => 60,
                'attendance' => [
                    'present' => 72,
                    'present_percent' => 72 / 200 * 100,
                    'absent' => 14,
                    'total' => 200,
                ],
                'payment_status' => 'Due in 5 days',
                'last_contact' => '1 week ago',
                'action' => 'Follow Up',
            ],
        ];

        return ajaxSuccess(['data' => $students], '');
    }
}
