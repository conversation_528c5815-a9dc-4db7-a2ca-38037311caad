<?php

namespace GalaxyAPI\Controllers;

use Domains\Students\RiskAssessment\Models\StudentRiskAssessment;
use GalaxyAPI\Requests\StudentRiskAssessmentRequest;
use GalaxyAPI\Resources\StudentRiskAssessmentResource;
use Illuminate\Support\Facades\Auth;

class StudentRiskAssessmentController extends CrudBaseController
{
    public function init()
    {
        $this->withAll = [
            'student',
            'course',
            'semesterAssessments',
        ];

        $this->loadAll = [
            'student',
            'course',
            'semesterAssessments',
        ];

        $this->scopeWithValue = [
            'collegeId' => Auth::user()?->college_id,
        ];
    }

    public function __construct()
    {
        parent::__construct(
            model: StudentRiskAssessment::class,
            storeRequest: StudentRiskAssessmentRequest::class,
            updateRequest: StudentRiskAssessmentRequest::class,
            resource: StudentRiskAssessmentResource::class,
        );
    }

    public function summary()
    {
        $this->init();
        return response()->json($this->getSummaryStatistics());
    }

    private function getSummaryStatistics(): array
    {
        try {
            $allAssessments = StudentRiskAssessment::where('college_id', $this->scopeWithValue['collegeId'] ?? 0)->get();
            $total = $allAssessments->count();
            $highRisk = $allAssessments->where('risk_level', StudentRiskAssessment::RISK_TYPE_HIGH)->count();
            $mediumRisk = $allAssessments->where('risk_level', StudentRiskAssessment::RISK_TYPE_MEDIUM)->count();
            $lowRisk = $allAssessments->where('risk_level', StudentRiskAssessment::RISK_TYPE_LOW)->count();

            return [
                'total' => $total,
                'high_risk' => $highRisk,
                'medium_risk' => $mediumRisk,
                'low_risk' => $lowRisk,
            ];
        } catch (\Exception $e) {
            return [
                'total' => 0,
                'high_risk' => 0,
                'medium_risk' => 0,
                'low_risk' => 0,
            ];
        }
    }
}
