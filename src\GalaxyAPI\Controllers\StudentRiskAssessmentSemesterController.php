<?php

namespace GalaxyAPI\Controllers;

use Domains\Students\RiskAssessment\Models\StudentRiskAssessmentSemester;
use GalaxyAPI\Requests\StudentRiskAssessmentSemesterRequest;
use GalaxyAPI\Resources\StudentRiskAssessmentSemesterResource;
use GalaxyAPI\Resources\StudentRiskAssessmentSemesterGroupedResource;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\DB;

class StudentRiskAssessmentSemesterController extends CrudBaseController
{
    public function __construct()
    {
        parent::__construct(
            model: StudentRiskAssessmentSemester::class,
            storeRequest: StudentRiskAssessmentSemesterRequest::class,
            updateRequest: StudentRiskAssessmentSemesterRequest::class,
            resource: StudentRiskAssessmentSemesterResource::class,
        );
        // Load relationships for better performance
        $this->withAll = [
            'riskAssessment.student',
            'riskAssessment.course',
            'semester',
        ];
    }

    /**
     * Get grouped student data for the main grid view
     * Endpoint: GET /api/v2/tenant/student-risk-assessment-semesters/grouped
     */
    public function grouped()
    {
        // Get pagination parameters
        $page = request()->input('page', 1);
        $rowsPerPage = request()->input('rowsPerPage', 25);
        $sortBy = request()->input('sortBy', 'student_name');
        $descending = request()->input('descending', false);
        $sortDirection = $descending ? 'desc' : 'asc';

        // Get current user's college ID for security scoping
        $collegeId = Auth::user()->college_id;

        // Apply filters
        $filters = request()->input('filters', []);
        if (is_string($filters)) {
            $filters = json_decode($filters, true) ?? [];
        }

        // Build grouped query
        $query = DB::table('student_risk_assessment_semesters as sras')
            ->join('student_risk_assessments as sra', 'sras.risk_assessment_id', '=', 'sra.id')
            ->join('rto_students as s', 'sra.student_id', '=', 's.id')
            ->join('rto_courses as c', 'sra.course_id', '=', 'c.id')
            ->where('s.college_id', $collegeId)
            ->select([
                's.id as student_id',
                's.generated_stud_id as student_number',
                DB::raw("CONCAT(s.first_name, ' ', s.family_name) as student_name"),
                's.email as student_email',
                'c.id as course_id',
                'c.course_code',
                'c.course_name',
                DB::raw('COUNT(sras.id) as total_assessments'),
                DB::raw('MAX(sras.created_at) as latest_assessment_date'),
                DB::raw('MAX(sras.risk_type) as current_risk_level'),
                DB::raw('MIN(sras.risk_type) as lowest_risk_level'),
                DB::raw('AVG(sras.risk_type) as average_risk_level'),
                // Risk trend calculation (comparing first and last assessment)
                DB::raw('(SELECT risk_type FROM student_risk_assessment_semesters WHERE risk_assessment_id = sra.id ORDER BY created_at ASC LIMIT 1) as first_risk_level'),
                DB::raw('(SELECT risk_type FROM student_risk_assessment_semesters WHERE risk_assessment_id = sra.id ORDER BY created_at DESC LIMIT 1) as last_risk_level'),
            ])
            ->groupBy(['s.id', 's.generated_stud_id', 's.first_name', 's.family_name', 's.email', 'c.id', 'c.course_code', 'c.course_name', 'sra.id']);

        // Apply filters
        if (!empty($filters['risk_type'])) {
            $query->having('current_risk_level', '=', $filters['risk_type']);
        }

        if (!empty($filters['student_name'])) {
            $query->where(DB::raw("CONCAT(s.first_name, ' ', s.family_name)"), 'like', '%' . $filters['student_name'] . '%');
        }

        // Apply sorting
        $query->orderBy($sortBy === 'student_name' ? DB::raw("CONCAT(s.first_name, ' ', s.family_name)") : $sortBy, $sortDirection);

        // Get paginated results
        $results = $query->paginate($rowsPerPage, ['*'], 'page', $page);

        // Transform the data using the grouped resource
        $students = StudentRiskAssessmentSemesterGroupedResource::collection($results->items());

        return response()->json([
            'data' => $students,
            'meta' => [
                'current_page' => $results->currentPage(),
                'last_page' => $results->lastPage(),
                'per_page' => $results->perPage(),
                'total' => $results->total(),
                'from' => $results->firstItem(),
                'to' => $results->lastItem(),
            ],
            'code' => 200,
            'success' => 1,
        ]);
    }

    /**
     * Get detailed semester assessments for a specific student
     * Endpoint: GET /api/v2/tenant/student-risk-assessment-semesters/student/{studentId}
     */
    public function studentDetails($studentId)
    {
        $collegeId = Auth::user()->college_id;

        // Get all semester assessments for the student with security scoping
        $assessments = StudentRiskAssessmentSemester::with([
            'riskAssessment.student',
            'riskAssessment.course',
            'semester'
        ])
        ->whereHas('riskAssessment.student', function ($query) use ($collegeId, $studentId) {
            $query->where('college_id', $collegeId)
                  ->where('id', $studentId);
        })
        ->orderBy('created_at', 'desc')
        ->get();

        return response()->json([
            'data' => StudentRiskAssessmentSemesterResource::collection($assessments),
            'code' => 200,
            'success' => 1,
        ]);
    }
}
