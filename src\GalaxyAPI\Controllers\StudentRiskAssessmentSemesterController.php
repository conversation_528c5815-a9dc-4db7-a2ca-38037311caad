<?php

namespace GalaxyAPI\Controllers;

use Domains\Students\RiskAssessment\Models\StudentRiskAssessmentSemester;
use GalaxyAPI\Requests\StudentRiskAssessmentSemesterRequest;
use GalaxyAPI\Resources\StudentRiskAssessmentSemesterResource;

class StudentRiskAssessmentSemesterController extends CrudBaseController
{
    public function __construct()
    {
        parent::__construct(
            model: StudentRiskAssessmentSemester::class,
            storeRequest: StudentRiskAssessmentSemesterRequest::class,
            updateRequest: StudentRiskAssessmentSemesterRequest::class,
            resource: StudentRiskAssessmentSemesterResource::class,
        );
        // Load relationships for better performance
        $this->withAll = [
            'riskAssessment.student',
            'riskAssessment.course',
            'semester',
        ];
    }
}
