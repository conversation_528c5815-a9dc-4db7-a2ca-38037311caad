<?php

namespace GalaxyAPI\Controllers;

use App\Model\v2\EmailTemplate;
use Domains\Students\RiskAssessment\DTO\Setting;
use Domains\Students\RiskAssessment\Services\ConfigService;
use GalaxyAPI\Requests\StudentRiskAssessmentSettingsRequest;
use Illuminate\Http\Request;
use Illuminate\Routing\Controller as BaseController;
use Illuminate\Support\Facades\Auth;

class StudentRiskAssessmentSettingsController extends BaseController
{
    /**
     * Get email templates for dropdowns
     */
    public function emailTemplates()
    {
        $collegeId = Auth::user()->college_id;

        $emailTemplates = EmailTemplate::where('college_id', $collegeId)
            ->where('status', 1)
            ->orderBy('template_name')
            ->get()
            ->map(function ($template) {
                return [
                    'value' => $template->id,
                    'text' => $template->template_name,
                ];
            });

        return ajaxSuccess(
            ['data' => $emailTemplates->toArray()],
            ''
        );
    }

    /**
     * Get existing settings for the college
     */
    public function index()
    {
        try {
            $settings = ConfigService::globalSettings();

            // Return only main config data from Setting DTO (no category-specific data)
            $mainConfigData = $settings->toArray();

            // Get category parameters for each category from config
            $categoryParameters = [];
            foreach ($this->getConfiguredCategories() as $categoryConfig) {
                $parameters = ConfigService::categoryParameters($categoryConfig['category']);
                $enabled = ConfigService::isCategoryEnabled($categoryConfig['category']);

                $categoryParameters[$categoryConfig['key']] = [
                    'category' => $categoryConfig['category'],
                    'parameters' => array_merge($parameters, ['enabled' => $enabled]),
                ];
            }

            return ajaxSuccess(
                data: [
                    'data' => $mainConfigData,
                    'category_parameters' => $categoryParameters,
                ],
                message: ''
            );
        } catch (\Exception $e) {
            return ajaxError(
                message: 'Failed to retrieve settings: '.$e->getMessage()
            );
        }
    }

    /**
     * Store or update settings
     */
    public function saveSettings(StudentRiskAssessmentSettingsRequest $request)
    {
        try {
            $data = $request->validated();

            // Separate main config data and category data
            $mainConfigData = $this->extractMainConfigData($data);
            $categoryData = $this->extractCategoryData($data);

            // Create Setting DTO from main config data only
            $setting = new Setting;
            $setting->riskAssessmentEnabled = $mainConfigData['risk_assessment_enabled'];
            $setting->lowRiskWeeks = $mainConfigData['category_low_risk_weeks'];
            $setting->mediumRiskWeeks = $mainConfigData['category_medium_risk_weeks'];
            $setting->highRiskWeeks = $mainConfigData['category_high_risk_weeks'];
            $setting->lowRiskEmailTemplate = $mainConfigData['category_low_risk_email'];
            $setting->mediumRiskEmailTemplate = $mainConfigData['category_medium_risk_email'];
            $setting->highRiskEmailTemplate = $mainConfigData['category_high_risk_email'];

            // Save main settings using ConfigService
            ConfigService::saveSetting($setting);

            // Process and save category parameters using saveCategoryParameters
            $savedCategoryParameters = $this->processCategoryDataFromForm($categoryData);

            return ajaxSuccess(
                data: [
                    'main_config' => $mainConfigData,
                    'category_parameters' => $savedCategoryParameters,
                ],
                message: 'Risk assessment settings saved successfully'
            );
        } catch (\Exception $e) {
            return ajaxError(
                message: 'Failed to save settings: '.$e->getMessage()
            );
        }
    }

    /**
     * Extract main configuration data from form data
     */
    private function extractMainConfigData(array $data): array
    {
        return [
            'risk_assessment_enabled' => $data['risk_assessment_enabled'],
            'category_low_risk_weeks' => $data['category_low_risk_weeks'],
            'category_medium_risk_weeks' => $data['category_medium_risk_weeks'],
            'category_high_risk_weeks' => $data['category_high_risk_weeks'],
            'category_low_risk_email' => $data['category_low_risk_email'],
            'category_medium_risk_email' => $data['category_medium_risk_email'],
            'category_high_risk_email' => $data['category_high_risk_email'],
        ];
    }

    /**
     * Extract category-specific data from form data (config-driven)
     */
    private function extractCategoryData(array $data): array
    {
        $categoryData = [];

        foreach ($this->getConfiguredCategories() as $categoryConfig) {
            $key = $categoryConfig['key'];
            $enabled = $data["{$key}_enabled"] ?? false;

            $categoryData[$key] = [
                'enabled' => $enabled,
            ];

            if ($enabled) {
                // Handle different validation types
                if ($categoryConfig['validation_type'] === 'array') {
                    // For payment-like categories with array criteria
                    $categoryData[$key]['low_risk_threshold'] = $data["{$key}_low_risk_criteria"] ?? [];
                    $categoryData[$key]['medium_risk_threshold'] = $data["{$key}_medium_risk_criteria"] ?? [];
                    $categoryData[$key]['high_risk_threshold'] = $data["{$key}_high_risk_criteria"] ?? [];
                } else {
                    // For percentage-based categories
                    $categoryData[$key]['low_risk_threshold'] = $data["{$key}_low_risk"] ?? null;
                    $categoryData[$key]['medium_risk_threshold'] = $data["{$key}_medium_risk"] ?? null;
                    $categoryData[$key]['high_risk_threshold'] = $data["{$key}_high_risk"] ?? null;
                }
            } else {
                // Set null/empty values when disabled
                if ($categoryConfig['validation_type'] === 'array') {
                    $categoryData[$key]['low_risk_threshold'] = [];
                    $categoryData[$key]['medium_risk_threshold'] = [];
                    $categoryData[$key]['high_risk_threshold'] = [];
                } else {
                    $categoryData[$key]['low_risk_threshold'] = null;
                    $categoryData[$key]['medium_risk_threshold'] = null;
                    $categoryData[$key]['high_risk_threshold'] = null;
                }
            }
        }

        return $categoryData;
    }

    /**
     * Process category data from form and save using saveCategoryParameters (config-driven)
     */
    private function processCategoryDataFromForm(array $categoryData): array
    {
        $savedParameters = [];
        $categoryMapping = $this->getCategoryMapping();

        foreach ($categoryData as $categoryKey => $data) {
            if (! isset($categoryMapping[$categoryKey])) {
                continue; // Skip unknown categories
            }

            $category = $categoryMapping[$categoryKey];

            // Prepare parameters for this category with consistent naming
            $parameters = [
                'enabled' => $data['enabled'],
                'low_risk_threshold' => $data['low_risk_threshold'],
                'medium_risk_threshold' => $data['medium_risk_threshold'],
                'high_risk_threshold' => $data['high_risk_threshold'],
            ];

            // Save category parameters using ConfigService
            ConfigService::saveCategoryParameters($category, $parameters);

            $savedParameters[$categoryKey] = [
                'category' => $category,
                'parameters' => $parameters,
            ];
        }

        return $savedParameters;
    }

    /**
     * Get category parameters for a specific category
     */
    public function getCategoryParameters(Request $request)
    {
        try {
            $category = $request->input('category');

            if (! $category) {
                return ajaxError(
                    message: 'Category parameter is required.'
                );
            }

            // Validate category exists using config
            if (! $this->isValidCategory($category)) {
                return ajaxError(
                    message: 'Invalid category specified.'
                );
            }

            $parameters = ConfigService::categoryParameters($category);
            $isEnabled = ConfigService::isCategoryEnabled($category);

            return ajaxSuccess(
                data: [
                    'category' => $category,
                    'parameters' => $parameters,
                    'enabled' => $isEnabled,
                ],
                message: ''
            );
        } catch (\Exception $e) {
            return ajaxError(
                message: 'Failed to retrieve category parameters: '.$e->getMessage()
            );
        }
    }

    /**
     * Save category parameters for a specific category
     */
    public function saveCategoryParameters(Request $request)
    {
        try {
            $category = $request->input('category');
            $parameters = $request->input('parameters', []);

            if (! $category) {
                return ajaxError(
                    message: 'Category parameter is required.'
                );
            }

            // Validate category exists using config
            if (! $this->isValidCategory($category)) {
                return ajaxError(
                    message: 'Invalid category specified.'
                );
            }

            // Validate parameters based on category
            $validatedParameters = $this->validateCategoryParameters($category, $parameters);

            if ($validatedParameters === false) {
                return ajaxError(
                    message: 'Invalid parameters provided for the specified category.'
                );
            }

            // Save category parameters
            ConfigService::saveCategoryParameters($category, $validatedParameters);

            return ajaxSuccess(
                data: [
                    'category' => $category,
                    'parameters' => $validatedParameters,
                ],
                message: 'Category parameters saved successfully.'
            );
        } catch (\Exception $e) {
            return ajaxError(
                message: 'Failed to save category parameters: '.$e->getMessage()
            );
        }
    }

    /**
     * Toggle category enabled/disabled status
     */
    public function toggleCategory(Request $request)
    {
        try {
            $category = $request->input('category');

            if (! $category) {
                return ajaxError(
                    message: 'Category parameter is required.'
                );
            }

            // Validate category exists using config
            if (! $this->isValidCategory($category)) {
                return ajaxError(
                    message: 'Invalid category specified.'
                );
            }

            ConfigService::toggleCategory($category);
            $isEnabled = ConfigService::isCategoryEnabled($category);

            return ajaxSuccess(
                data: [
                    'category' => $category,
                    'enabled' => $isEnabled,
                ],
                message: 'Category status updated successfully.'
            );
        } catch (\Exception $e) {
            return ajaxError(
                message: 'Failed to toggle category status: '.$e->getMessage()
            );
        }
    }

    /**
     * Validate category parameters based on category type (config-driven)
     */
    private function validateCategoryParameters(string $category, array $parameters): array|false
    {
        $categoryConfig = $this->getCategoryConfig($category);

        if (! $categoryConfig) {
            return false;
        }

        // Use validation type from config
        return match ($categoryConfig['validation_type']) {
            'array' => $this->validateArrayParameters($parameters),
            'percentage' => $this->validatePercentageParameters($parameters),
            default => false,
        };
    }

    /**
     * Get all configured categories from config
     */
    private function getConfiguredCategories(): array
    {
        return config('riskassessment.categories', []);
    }

    /**
     * Get category mapping (key => category constant)
     */
    private function getCategoryMapping(): array
    {
        $mapping = [];
        foreach ($this->getConfiguredCategories() as $categoryConfig) {
            $mapping[$categoryConfig['key']] = $categoryConfig['category'];
        }

        return $mapping;
    }

    /**
     * Get valid categories list
     */
    private function getValidCategories(): array
    {
        return array_column($this->getConfiguredCategories(), 'category');
    }

    /**
     * Check if category is valid
     */
    private function isValidCategory(string $category): bool
    {
        return in_array($category, $this->getValidCategories());
    }

    /**
     * Get category configuration by category constant
     */
    private function getCategoryConfig(string $category): ?array
    {
        foreach ($this->getConfiguredCategories() as $categoryConfig) {
            if ($categoryConfig['category'] === $category) {
                return $categoryConfig;
            }
        }

        return null;
    }

    /**
     * Validate array-based parameters (for payment-like categories)
     */
    private function validateArrayParameters(array $parameters): array
    {
        $validated = [];

        if (isset($parameters['low_risk_threshold'])) {
            $validated['low_risk_threshold'] = $parameters['low_risk_threshold'] ?? [];
        }

        if (isset($parameters['medium_risk_threshold'])) {
            $validated['medium_risk_threshold'] = $parameters['medium_risk_threshold'] ?? [];
        }

        if (isset($parameters['high_risk_threshold'])) {
            $validated['high_risk_threshold'] = $parameters['high_risk_threshold'] ?? [];
        }

        if (isset($parameters['enabled'])) {
            $validated['enabled'] = (bool) $parameters['enabled'];
        }

        return $validated;
    }

    /**
     * Validate percentage-based parameters (for attendance, moodle, results categories)
     */
    private function validatePercentageParameters(array $parameters): array
    {
        $validated = [];

        if (isset($parameters['low_risk_threshold'])) {
            $validated['low_risk_threshold'] = max(0, min(100, (float) $parameters['low_risk_threshold']));
        }

        if (isset($parameters['medium_risk_threshold'])) {
            $validated['medium_risk_threshold'] = max(0, min(100, (float) $parameters['medium_risk_threshold']));
        }

        if (isset($parameters['high_risk_threshold'])) {
            $validated['high_risk_threshold'] = max(0, min(100, (float) $parameters['high_risk_threshold']));
        }

        if (isset($parameters['enabled'])) {
            $validated['enabled'] = (bool) $parameters['enabled'];
        }

        return $validated;
    }
}
