<?php

namespace GalaxyAPI\Controllers;

use App\Model\Teacher;
use GalaxyAPI\Requests\TeacherRequest;
use GalaxyAPI\Resources\TeacherResource;
use Illuminate\Support\Facades\Auth;

class UsersController extends CrudBaseController
{
    public function init()
    {
        $this->scopeWithValue = [
            'collegeId' => Auth::user()->college_id,
        ];

        $this->loadAll = [
            'users',
        ];

        $this->withAll = [
            'users',
        ];
    }

    public function __construct()
    {
        parent::__construct(
            model: Teacher::class,
            storeRequest: TeacherRequest::class,
            updateRequest: TeacherRequest::class,
            resource: TeacherResource::class,
        );
    }

    public function getAuthUser()
    {
        $data = [
            'username' => Auth::user()->username,
            'email' => Auth::user()->email,
            'role_id' => Auth::user()->role_id,
            'college_id' => Auth::user()->college_id,
            'profile_picture' => Auth::user()->profile_picture,
            'uuid' => Auth::user()->uuid,
        ];

        return ajaxSuccess([
            'data' => $data,
        ], '');
    }
}
