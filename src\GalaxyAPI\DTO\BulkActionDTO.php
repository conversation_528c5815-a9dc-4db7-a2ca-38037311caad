<?php

namespace GalaxyAPI\DTO;

class BulkActionDTO
{
    /**
     * @param  callable  $callback
     */
    public function __construct(
        public string $label,
        public string $action,
        public $callback,
        public ?string $confirmationMessage = null,
        public ?string $icon = null,
        public ?string $color = null,
    ) {
        $this->confirmationMessage = $confirmationMessage ?? "Are you sure you want to $label?";
    }

    public function call(BulkActionCallParmsDTO $parms)
    {
        return call_user_func($this->callback, $parms);
    }

    public static function init(
        string $label,
        string $action,
        callable $callback,
        ?string $confirmationMessage,
        ?string $icon = null,
        ?string $color = null,
    ) {
        return new self(
            label: $label,
            action: $action,
            callback: $callback,
            confirmationMessage: $confirmationMessage,
            icon: $icon,
            color: $color,
        );
    }

    public function toArray()
    {
        return [
            'label' => $this->label,
            'confirmation_message' => $this->confirmationMessage,
            'icon' => $this->icon,
            'color' => $this->color,
            'action' => $this->action,
        ];
    }
}
