<?php

namespace GalaxyAPI\Requests;

use Illuminate\Foundation\Http\FormRequest;

class AgentCommissionBulkRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'agent_status' => 'required|integer',
            'course_status' => 'required|boolean',
            'commission_period' => 'required|integer|min:1|max:12',
            'agent_id' => 'required|integer|exists:rto_agents,id',
            'course_type' => 'required|integer|exists:rto_college_course_type,id',
            'courses' => 'required|array|min:1',
            'courses.*' => 'required|integer|exists:rto_courses,id',
            'commission' => 'required|numeric|min:0|max:100',
            'rate_valid_from' => 'required|date|before_or_equal:rate_valid_to',
            'rate_valid_to' => 'required|date|after_or_equal:rate_valid_from',
            'gst' => 'required|boolean',
        ];
    }

    // Custom validation messages (optional)
    public function messages(): array
    {
        return [
            'agent_status.required' => 'Agent status is required.',
            'agent_status.boolean' => 'Agent status must be true or false.',
            'course_status.required' => 'Course status is required.',
            'course_status.boolean' => 'Course status must be true or false.',
            'commission_period.required' => 'Commission period is required.',
            'commission_period.integer' => 'Commission period must be a number.',
            'commission_period.min' => 'Commission period must be at least 1 month.',
            'commission_period.max' => 'Commission period cannot exceed 12 months.',
            'agent_name.required' => 'Agent selection is required.',
            'agent_name.integer' => 'Invalid agent selection.',
            'agent_name.exists' => 'Selected agent does not exist.',
            'course_type.required' => 'Course type is required.',
            'course_type.integer' => 'Invalid course type.',
            'course_type.exists' => 'Selected course type does not exist.',
            'courses.required' => 'At least one course must be selected.',
            'courses.array' => 'Courses must be an array.',
            'courses.min' => 'At least one course must be selected.',
            'courses.*.required' => 'Course ID is required.',
            'courses.*.integer' => 'Course ID must be a number.',
            'courses.*.exists' => 'Selected course does not exist.',
            'commission.required' => 'Commission rate is required.',
            'commission.numeric' => 'Commission must be a number.',
            'commission.min' => 'Commission cannot be negative.',
            'commission.max' => 'Commission cannot exceed 100%.',
            'rate_valid_from.required' => 'Valid from date is required.',
            'rate_valid_from.date' => 'Valid from must be a valid date.',
            'rate_valid_from.before_or_equal' => 'Valid from date must be before or equal to valid to date.',
            'rate_valid_to.required' => 'Valid to date is required.',
            'rate_valid_to.date' => 'Valid to must be a valid date.',
            'rate_valid_to.after_or_equal' => 'Valid to date must be after or equal to valid from date.',
            'gst.required' => 'GST option is required.',
            'gst.boolean' => 'GST must be true or false.',
        ];
    }
}
