<?php

namespace GalaxyAPI\Requests;

use Illuminate\Foundation\Http\FormRequest;

class AgentCommissionRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            // Define your validation rules
            'agent_id' => 'required|integer',
            'course_type' => 'required|integer',
            'course_id' => 'required|integer',
            'commission_period' => 'required|integer',
            'commission' => 'required|numeric|min:0|max:100',
            'gst' => 'required|string',
            'rate_valid_from' => 'required|date',
            'rate_valid_to' => 'required|date',
        ];
    }
}
