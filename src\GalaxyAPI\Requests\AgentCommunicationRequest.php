<?php

namespace GalaxyAPI\Requests;

use Illuminate\Foundation\Http\FormRequest;

class AgentCommunicationRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            // Define your validation rules
            'agent_id' => 'required|integer',
            'category' => 'required|integer',
            'status' => 'required|integer',
            'comment' => 'required|string',
            'comment_by' => 'required|integer',
            'college_id' => 'required|integer',
            'created_by' => 'required|integer',
            'updated_by' => 'required|integer',
        ];
    }

    public function prepareForValidation()
    {
        $this->merge([
            'college_id' => auth()->user()->college_id,
            'created_by' => auth()->user()->id,
            'updated_by' => auth()->user()->id,
            'comment_by' => auth()->user()->id,
        ]);
    }
}
