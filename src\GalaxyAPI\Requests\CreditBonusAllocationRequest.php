<?php

namespace GalaxyAPI\Requests;

use Illuminate\Foundation\Http\FormRequest;

class CreditBonusAllocationRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            // Define your validation rules
            'agent_id' => 'required|exists:rto_agents,id',
            'type' => 'required|in:1,2,3,4',
            'amount' => 'required|numeric',
            'payment_mode' => 'required|exists:rto_payment_mode,id',
            'payment_date' => 'required|date',
            'comment' => 'required|string',
        ];
    }

    public function prepareForValidation()
    {
        $this->merge([
            'college_id' => auth()->user()->college_id,
            'created_by' => auth()->user()->id,
            'updated_by' => auth()->user()->id,
            'std_transaction_no' => 0,
            'credit_used' => 0,
        ]);
    }

    public function messages(): array
    {
        return [
            'agent_id.required' => 'Agent is required.',
            'agent_id.exists' => 'Invalid agent.',
            'type.required' => 'Type is required.',
            'type.in' => 'Invalid type.',
            'amount.required' => 'Amount is required.',
            'amount.numeric' => 'Amount must be a number.',
            'payment_mode.required' => 'Payment mode is required.',
            'payment_mode.exists' => 'Invalid payment mode.',
            'payment_date.required' => 'Payment date is required.',
            'payment_date.date' => 'Payment date must be a valid date.',
            'comment.required' => 'Comment is required.',
            'comment.string' => 'Comment must be a valid text.',
        ];
    }
}
