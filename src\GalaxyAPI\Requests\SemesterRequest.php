<?php

namespace GalaxyAPI\Requests;

use App\Model\v2\Semester;
use Carbon\Carbon;
use Illuminate\Foundation\Http\FormRequest;
use Illuminate\Support\Facades\Auth;
use Illuminate\Support\Facades\Config;
use Illuminate\Validation\Rule;

class SemesterRequest extends FormRequest
{
    public function authorize(): bool
    {
        return true;
    }

    public function rules(): array
    {
        $collegeId = Auth::user()->college_id ?? null;
        $semesterId = $this->route('id'); // For update requests
        $calendarTypes = array_keys(Config::get('constants.arrCalendarTypeV2', []));

        return [
            // Basic field validations
            'semester_name' => [
                'required',
                'string',
                'max:255',
                'min:2',
                'regex:/^[a-zA-Z0-9\s\-_\.]+$/', // Allow alphanumeric, spaces, hyphens, underscores, dots
                Rule::unique('rto_semester', 'semester_name')
                    ->where('college_id', $collegeId)
                    ->where('course_type_id', $this->input('course_type_id'))
                    ->where('year', $this->input('year'))
                    ->where('calendar_type', $this->input('calendar_type'))
                    ->ignore($semesterId),
            ],
            'course_type_id' => [
                'required',
                'integer',
                'min:1',
                'exists:rto_college_course_type,id',
            ],
            'calendar_type' => [
                'required',
                'integer',
                'min:1',
                'max:50',
                Rule::in($calendarTypes),
            ],
            'year' => [
                'required',
                'integer',
                'min:'.(date('Y') - 2),
                'max:'.(date('Y') + 30),
            ],
            'semester_start' => [
                'required',
                'date',
                'after_or_equal:'.date('Y-m-d', strtotime('-2 years')),
                'before_or_equal:'.date('Y-m-d', strtotime('+30 years')),
            ],
            'semester_finish' => [
                'required',
                'date',
                'after:semester_start',
                'after_or_equal:'.date('Y-m-d', strtotime('-2 years')),
                'before_or_equal:'.date('Y-m-d', strtotime('+30 years')),
            ],
            'last_term_date' => ['sometimes', 'date'],
            'college_id' => 'required|exists:rto_colleges,id',
            'created_by' => 'required|exists:rto_users,id',
            'updated_by' => 'required|exists:rto_users,id',
        ];
    }

    public function withValidator($validator)
    {
        $validator->after(function ($validator) {
            $this->validateYearConsistency($validator);
            $this->validateDateRange($validator);
            $this->validateSemesterOverlap($validator);
            $this->validateBusinessRules($validator);
        });

        if (! $validator->fails()) {
            $this->merge([
                'semester_start' => $this->convertDateToYmdFormat($this->input('semester_start')),
                'semester_finish' => $this->convertDateToYmdFormat($this->input('semester_finish')),
            ]);
        }
    }

    public function prepareForValidation()
    {
        $this->merge([
            'last_term_date' => $this->convertDateToYmdFormat($this->input('semester_start')),
            'college_id' => auth()->user()->college_id,
            'created_by' => auth()->user()->id,
            'updated_by' => auth()->user()->id,
        ]);
    }

    /**
     * Validate that the year matches the semester start date year
     */
    private function validateYearConsistency($validator)
    {
        $year = $this->input('year');
        $semesterStart = $this->input('semester_start');

        if ($year && $semesterStart) {
            try {
                // Try to parse the date in multiple formats
                $startDate = null;
                try {
                    $startDate = Carbon::createFromFormat('d-m-Y', $semesterStart);
                } catch (\Exception) {
                    $startDate = Carbon::parse($semesterStart);
                }

                if ((int) $year !== (int) $startDate->year) {
                    $validator->errors()->add('year', 'The selected year must match the semester start date year.');
                }
            } catch (\Exception) {
                $validator->errors()->add('semester_start', 'Invalid semester start date format.');
            }
        }
    }

    /**
     * Validate date range and duration
     */
    private function validateDateRange($validator)
    {
        $semesterStart = $this->input('semester_start');
        $semesterFinish = $this->input('semester_finish');

        if ($semesterStart && $semesterFinish) {
            try {
                // Try to parse dates in multiple formats
                $startDate = null;
                $finishDate = null;

                try {
                    $startDate = Carbon::createFromFormat('d-m-Y', $semesterStart);
                } catch (\Exception) {
                    $startDate = Carbon::parse($semesterStart);
                }

                try {
                    $finishDate = Carbon::createFromFormat('d-m-Y', $semesterFinish);
                } catch (\Exception) {
                    $finishDate = Carbon::parse($semesterFinish);
                }

                // Check minimum duration (at least 1 week)
                if ($finishDate->diffInDays($startDate) < 7) {
                    $validator->errors()->add('semester_finish', 'Semester duration must be at least 7 days.');
                }

                // Check maximum duration (not more than 2 years)
                if ($finishDate->diffInDays($startDate) > 730) {
                    $validator->errors()->add('semester_finish', 'Semester duration cannot exceed 2 years.');
                }

                // Check if dates are in the future (for new semesters)
                if (! $this->route('id') && $startDate->isPast() && $startDate->diffInDays(now()) > 30) {
                    $validator->errors()->add('semester_start', 'Semester start date cannot be more than 30 days in the past.');
                }

            } catch (\Exception $e) {
                $validator->errors()->add('semester_start', 'Invalid date format provided.');
            }
        }
    }

    /**
     * Validate semester overlap with existing semesters
     */
    private function validateSemesterOverlap($validator)
    {
        $collegeId = Auth::user()->college_id ?? null;
        $courseTypeId = $this->input('course_type_id');
        $calendarType = $this->input('calendar_type');
        $year = $this->input('year');
        $semesterStart = $this->input('semester_start');
        $semesterFinish = $this->input('semester_finish');
        $semesterId = $this->route('id');

        if (! $collegeId || ! $courseTypeId || ! $calendarType || ! $year || ! $semesterStart || ! $semesterFinish) {
            return;
        }

        try {
            // Try to parse dates in multiple formats
            $startDateCarbon = null;
            $finishDateCarbon = null;

            try {
                $startDateCarbon = Carbon::createFromFormat('d-m-Y', $semesterStart);
            } catch (\Exception) {
                $startDateCarbon = Carbon::parse($semesterStart);
            }

            try {
                $finishDateCarbon = Carbon::createFromFormat('d-m-Y', $semesterFinish);
            } catch (\Exception) {
                $finishDateCarbon = Carbon::parse($semesterFinish);
            }

            $startDate = $startDateCarbon->format('Y-m-d');
            $finishDate = $finishDateCarbon->format('Y-m-d');

            $query = Semester::where('college_id', $collegeId)
                ->where('course_type_id', $courseTypeId)
                ->where('calendar_type', $calendarType)
                ->where('year', $year);

            if ($semesterId) {
                $query->where('id', '!=', $semesterId);
            }

            // Check for overlapping semesters
            $overlappingSemesters = $query->where(function ($q) use ($startDate, $finishDate) {
                $q->where(function ($subQ) use ($startDate) {
                    // New semester starts within existing semester
                    $subQ->where('semester_start', '<=', $startDate)
                        ->where('semester_finish', '>=', $startDate);
                })->orWhere(function ($subQ) use ($finishDate) {
                    // New semester ends within existing semester
                    $subQ->where('semester_start', '<=', $finishDate)
                        ->where('semester_finish', '>=', $finishDate);
                })->orWhere(function ($subQ) use ($startDate, $finishDate) {
                    // New semester completely contains existing semester
                    $subQ->where('semester_start', '>=', $startDate)
                        ->where('semester_finish', '<=', $finishDate);
                });
            })->get();

            if ($overlappingSemesters->count() > 0) {
                $validator->errors()->add('semester_start', 'Another semester exists with overlapping dates for the same course type, calendar type, and year.');
            }

        } catch (\Exception $e) {
            $validator->errors()->add('semester_start', 'Error validating semester overlap.');
        }
    }

    /**
     * Additional business rule validations
     */
    private function validateBusinessRules($validator)
    {
        // Validate calendar type exists in constants
        $calendarType = $this->input('calendar_type');
        if ($calendarType) {
            $calendarTypes = Config::get('constants.arrCalendarTypeV2', []);
            if (! isset($calendarTypes[$calendarType])) {
                $validator->errors()->add('calendar_type', 'Invalid calendar type selected.');
            }
        }

        // Validate semester name format and uniqueness within context
        $semesterName = $this->input('semester_name');
        if ($semesterName) {
            // Check for common semester naming patterns
            if (strlen(trim($semesterName)) < 2) {
                $validator->errors()->add('semester_name', 'Semester name must be at least 2 characters long.');
            }

            // Prevent special characters that might cause issues
            if (preg_match('/[<>"\']/', $semesterName)) {
                $validator->errors()->add('semester_name', 'Semester name contains invalid characters.');
            }
        }

        // For updates, validate against last_term_date
        $this->validateLastTermDate($validator);
    }

    /**
     * Validate semester finish date against last term date for updates
     */
    private function validateLastTermDate($validator)
    {
        $semesterId = $this->route('id');
        $semesterFinish = $this->input('semester_finish');

        if ($semesterId && $semesterFinish) {
            try {
                // Try to parse date in multiple formats
                $finishDate = null;
                try {
                    $finishDate = Carbon::createFromFormat('d-m-Y', $semesterFinish);
                } catch (\Exception) {
                    $finishDate = Carbon::parse($semesterFinish);
                }
                $existingSemester = Semester::find($semesterId);

                if ($existingSemester && $existingSemester->last_term_date) {
                    $lastTermDate = Carbon::parse($existingSemester->last_term_date)->subDay();

                    if ($finishDate->lt($lastTermDate)) {
                        $validator->errors()->add('semester_finish', 'Semester finish date must be greater than the last session date ('.$lastTermDate->format('d-m-Y').').');
                    }
                }
            } catch (\Exception) {
                // Error already handled in date validation
            }
        }
    }

    private function convertDateToYmdFormat($date): string
    {
        if (! $date) {
            return '';
        }

        try {
            // First try d-m-Y format
            return Carbon::createFromFormat('d-m-Y', $date)->format('Y-m-d');
        } catch (\Exception) {
            try {
                // Try parsing JavaScript date format or other formats
                return Carbon::parse($date)->format('Y-m-d');
            } catch (\Exception) {
                return $date;
            }
        }
    }

    public function messages(): array
    {
        return [
            'semester_name.required' => 'Semester name is required.',
            'semester_name.string' => 'Semester name must be a valid text.',
            'semester_name.max' => 'Semester name cannot exceed 255 characters.',
            'semester_name.min' => 'Semester name must be at least 2 characters.',
            'semester_name.regex' => 'Semester name contains invalid characters.',
            'semester_name.unique' => 'A semester with this name already exists for the selected course type, year, and calendar type.',

            'course_type_id.required' => 'Course type is required.',
            'course_type_id.integer' => 'Course type must be a valid number.',
            'course_type_id.min' => 'Course type must be greater than 0.',
            'course_type_id.exists' => 'Selected course type does not exist.',

            'calendar_type.required' => 'Calendar type is required.',
            'calendar_type.integer' => 'Calendar type must be a valid number.',
            'calendar_type.min' => 'Calendar type must be greater than 0.',
            'calendar_type.max' => 'Calendar type value is too large.',
            'calendar_type.in' => 'Selected calendar type is invalid.',

            'year.required' => 'Year is required.',
            'year.integer' => 'Year must be a valid number.',
            'year.min' => 'Year cannot be more than 2 years in the past.',
            'year.max' => 'Year cannot be more than 30 years in the future.',

            'semester_start.required' => 'Semester start date is required.',
            'semester_start.date' => 'Semester start date must be a valid date.',
            'semester_start.after_or_equal' => 'Semester start date cannot be more than 2 years in the past.',
            'semester_start.before_or_equal' => 'Semester start date cannot be more than 30 years in the future.',

            'semester_finish.required' => 'Semester finish date is required.',
            'semester_finish.date' => 'Semester finish date must be a valid date.',
            'semester_finish.after' => 'Semester finish date must be after the start date.',
            'semester_finish.after_or_equal' => 'Semester finish date cannot be more than 2 years in the past.',
            'semester_finish.before_or_equal' => 'Semester finish date cannot be more than 30 years in the future.',
        ];
    }
}
