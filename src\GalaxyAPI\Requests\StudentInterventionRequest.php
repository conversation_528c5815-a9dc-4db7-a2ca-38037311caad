<?php

namespace GalaxyAPI\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StudentInterventionRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'recorded_date' => 'required|date',
            'student_id' => 'required|string|max:255',
            'intervention_type_ids' => 'nullable|array|min:1',
            'intervention_type_ids.*' => 'integer',
            'intervention_for' => 'required|integer',
            'course_id' => 'required|integer',
            'semester' => 'required|integer',
            'details' => 'required|string',
            'intervention_strategy_ids' => 'nullable|array|min:1',
            'intervention_strategy_ids.*' => 'integer',
            'action_taken' => 'required|string',
            'prevent_reoccurance' => 'required|string',
            'status' => 'required|in:0,1',
            'due_date' => 'required|date',
            'outcome_date' => 'required|date|after_or_equal:due_date',
            'teacher_id' => 'required|integer',
        ];
    }
}
