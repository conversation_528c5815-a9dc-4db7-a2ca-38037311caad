<?php

namespace GalaxyAPI\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StudentRiskAssessmentSemesterRequest extends FormRequest
{
    public function rules(): array
    {
        return [
            'risk_assessment_id' => 'required|integer|exists:student_risk_assessments,id',
            'semester_id' => 'nullable|integer|exists:rto_semester,id',
            'risk_category' => 'required|string|in:attendance,result,payment,moodle_activity',
            'risk_type' => 'required|integer|in:0,1,2,3',
            'queue_date' => 'required|date',
            'status' => 'required|string|in:pending,in_progress,completed,cancelled',
            'remarks' => 'nullable|string|max:1000',
            'data' => 'nullable|array',
            'config' => 'nullable|array',
        ];
    }

    public function messages(): array
    {
        return [
            'risk_assessment_id.required' => 'Risk assessment ID is required.',
            'risk_assessment_id.exists' => 'The selected risk assessment does not exist.',
            'semester_id.exists' => 'The selected semester does not exist.',
            'risk_category.required' => 'Risk category is required.',
            'risk_category.in' => 'Risk category must be one of: attendance, result, payment, moodle_activity.',
            'risk_type.required' => 'Risk type is required.',
            'risk_type.in' => 'Risk type must be between 0 and 3.',
            'queue_date.required' => 'Queue date is required.',
            'queue_date.date' => 'Queue date must be a valid date.',
            'status.required' => 'Status is required.',
            'status.in' => 'Status must be one of: pending, in_progress, completed, cancelled.',
            'remarks.max' => 'Remarks cannot exceed 1000 characters.',
        ];
    }
}
