<?php

namespace GalaxyAPI\Requests;

use Illuminate\Foundation\Http\FormRequest;

class StudentRiskAssessmentSettingsRequest extends FormRequest
{
    /**
     * Determine if the user is authorized to make this request.
     */
    public function authorize(): bool
    {
        return true;
    }

    /**
     * Get the validation rules that apply to the request.
     */
    public function rules(): array
    {
        return [
            // Main toggle
            'risk_assessment_enabled' => 'required|boolean',

            // Category toggles
            'attendance_enabled' => 'required|boolean',
            'moodle_enabled' => 'required|boolean',
            'results_enabled' => 'required|boolean',
            'payment_enabled' => 'required|boolean',

            // Risk level weeks configuration
            'category_low_risk_weeks' => 'required|integer|min:1|max:52',
            'category_medium_risk_weeks' => 'required|integer|min:1|max:52',
            'category_high_risk_weeks' => 'required|integer|min:1|max:52',

            // // Email templates for risk levels
            'category_low_risk_email' => 'required|integer',
            'category_medium_risk_email' => 'required|integer',
            'category_high_risk_email' => 'required|integer',

            // Attendance thresholds (conditional on attendance being enabled)
            'attendance_low_risk' => 'required_if:attendance_enabled,true|nullable|numeric|min:0|max:100',
            'attendance_medium_risk' => 'required_if:attendance_enabled,true|nullable|numeric|min:0|max:100',
            'attendance_high_risk' => 'required_if:attendance_enabled,true|nullable|numeric|min:0|max:100',

            // // Moodle thresholds (conditional on moodle being enabled)
            'moodle_low_risk' => 'exclude_if:moodle_enabled,false|required_if:moodle_enabled,true|nullable|numeric|min:0|max:100',
            'moodle_medium_risk' => 'exclude_if:moodle_enabled,false|required_if:moodle_enabled,true|nullable|numeric|min:0|max:100',
            'moodle_high_risk' => 'exclude_if:moodle_enabled,false|required_if:moodle_enabled,true|nullable|numeric|min:0|max:100',

            // // Results thresholds (conditional on results being enabled)
            'results_low_risk' => 'exclude_if:results_enabled,false|required_if:results_enabled,true|nullable|numeric|min:0|max:100',
            'results_medium_risk' => 'exclude_if:results_enabled,false|required_if:results_enabled,true|nullable|numeric|min:0|max:100',
            'results_high_risk' => 'exclude_if:results_enabled,false|required_if:results_enabled,true|nullable|numeric|min:0|max:100',

            // // Payment thresholds (conditional on payment being enabled)
            'payment_low_risk_criteria' => 'exclude_if:payment_enabled,false|required_if:payment_enabled,true|nullable|array',
            'payment_medium_risk_criteria' => 'exclude_if:payment_enabled,false|required_if:payment_enabled,true|nullable|array',
            'payment_high_risk_criteria' => 'exclude_if:payment_enabled,false|required_if:payment_enabled,true|nullable|array',

        ];
    }

    /**
     * Get custom error messages for validation rules.
     */
    public function messages(): array
    {
        return [
            'risk_assessment_enabled.required' => 'Risk assessment enabled status is required.',
            'risk_assessment_enabled.boolean' => 'Risk assessment enabled must be true or false.',

            'attendance_enabled.required' => 'Attendance enabled status is required.',
            'attendance_enabled.boolean' => 'Attendance enabled must be true or false.',

            'moodle_enabled.required' => 'Moodle enabled status is required.',
            'moodle_enabled.boolean' => 'Moodle enabled must be true or false.',

            'results_enabled.required' => 'Results enabled status is required.',
            'results_enabled.boolean' => 'Results enabled must be true or false.',

            'payment_enabled.required' => 'Payment enabled status is required.',
            'payment_enabled.boolean' => 'Payment enabled must be true or false.',

            'category_low_risk_weeks.required' => 'Low risk weeks configuration is required.',
            'category_low_risk_weeks.integer' => 'Low risk weeks must be a valid number.',
            'category_low_risk_weeks.min' => 'Low risk weeks must be at least 1.',
            'category_low_risk_weeks.max' => 'Low risk weeks cannot exceed 52.',

            'category_medium_risk_weeks.required' => 'Medium risk weeks configuration is required.',
            'category_medium_risk_weeks.integer' => 'Medium risk weeks must be a valid number.',
            'category_medium_risk_weeks.min' => 'Medium risk weeks must be at least 1.',
            'category_medium_risk_weeks.max' => 'Medium risk weeks cannot exceed 52.',

            'category_high_risk_weeks.required' => 'High risk weeks configuration is required.',
            'category_high_risk_weeks.integer' => 'High risk weeks must be a valid number.',
            'category_high_risk_weeks.min' => 'High risk weeks must be at least 1.',
            'category_high_risk_weeks.max' => 'High risk weeks cannot exceed 52.',

            'category_low_risk_email.required' => 'Low risk email template is required.',
            'category_low_risk_email.integer' => 'Low risk email template must be a valid ID.',
            'category_low_risk_email.exists' => 'Selected low risk email template does not exist.',

            'category_medium_risk_email.required' => 'Medium risk email template is required.',
            'category_medium_risk_email.integer' => 'Medium risk email template must be a valid ID.',
            'category_medium_risk_email.exists' => 'Selected medium risk email template does not exist.',

            'category_high_risk_email.required' => 'High risk email template is required.',
            'category_high_risk_email.integer' => 'High risk email template must be a valid ID.',
            'category_high_risk_email.exists' => 'Selected high risk email template does not exist.',

            'attendance_low_risk.required_if' => 'Attendance low risk threshold is required when attendance is enabled.',
            'attendance_low_risk.numeric' => 'Attendance low risk threshold must be a valid number.',
            'attendance_low_risk.min' => 'Attendance low risk threshold cannot be negative.',
            'attendance_low_risk.max' => 'Attendance low risk threshold cannot exceed 100.',

            'attendance_medium_risk.required_if' => 'Attendance medium risk threshold is required when attendance is enabled.',
            'attendance_medium_risk.numeric' => 'Attendance medium risk threshold must be a valid number.',
            'attendance_medium_risk.min' => 'Attendance medium risk threshold cannot be negative.',
            'attendance_medium_risk.max' => 'Attendance medium risk threshold cannot exceed 100.',

            'attendance_high_risk.required_if' => 'Attendance high risk threshold is required when attendance is enabled.',
            'attendance_high_risk.numeric' => 'Attendance high risk threshold must be a valid number.',
            'attendance_high_risk.min' => 'Attendance high risk threshold cannot be negative.',
            'attendance_high_risk.max' => 'Attendance high risk threshold cannot exceed 100.',

            'moodle_low_risk.required_if' => 'Moodle low risk threshold is required when moodle is enabled.',
            'moodle_low_risk.numeric' => 'Moodle low risk threshold must be a valid number.',
            'moodle_low_risk.min' => 'Moodle low risk threshold cannot be negative.',
            'moodle_low_risk.max' => 'Moodle low risk threshold cannot exceed 100.',

            'moodle_medium_risk.required_if' => 'Moodle medium risk threshold is required when moodle is enabled.',
            'moodle_medium_risk.numeric' => 'Moodle medium risk threshold must be a valid number.',
            'moodle_medium_risk.min' => 'Moodle medium risk threshold cannot be negative.',
            'moodle_medium_risk.max' => 'Moodle medium risk threshold cannot exceed 100.',

            'moodle_high_risk.required_if' => 'Moodle high risk threshold is required when moodle is enabled.',
            'moodle_high_risk.numeric' => 'Moodle high risk threshold must be a valid number.',
            'moodle_high_risk.min' => 'Moodle high risk threshold cannot be negative.',
            'moodle_high_risk.max' => 'Moodle high risk threshold cannot exceed 100.',

            'results_low_risk.required_if' => 'Results low risk threshold is required when results is enabled.',
            'results_low_risk.numeric' => 'Results low risk threshold must be a valid number.',
            'results_low_risk.min' => 'Results low risk threshold cannot be negative.',
            'results_low_risk.max' => 'Results low risk threshold cannot exceed 100.',

            'results_medium_risk.required_if' => 'Results medium risk threshold is required when results is enabled.',
            'results_medium_risk.numeric' => 'Results medium risk threshold must be a valid number.',
            'results_medium_risk.min' => 'Results medium risk threshold cannot be negative.',
            'results_medium_risk.max' => 'Results medium risk threshold cannot exceed 100.',

            'results_high_risk.required_if' => 'Results high risk threshold is required when results is enabled.',
            'results_high_risk.numeric' => 'Results high risk threshold must be a valid number.',
            'results_high_risk.min' => 'Results high risk threshold cannot be negative.',
            'results_high_risk.max' => 'Results high risk threshold cannot exceed 100.',

            'payment_low_risk_criteria.required_if' => 'Payment low risk criteria is required when payment is enabled.',
            'payment_low_risk_criteria.array' => 'Payment low risk criteria must be an array.',

            'payment_medium_risk_criteria.required_if' => 'Payment medium risk criteria is required when payment is enabled.',
            'payment_medium_risk_criteria.array' => 'Payment medium risk criteria must be an array.',

            'payment_high_risk_criteria.required_if' => 'Payment high risk criteria is required when payment is enabled.',
            'payment_high_risk_criteria.array' => 'Payment high risk criteria must be an array.',
        ];
    }

    /**
     * Get custom attribute names for validation errors.
     */
    public function attributes(): array
    {
        return [
            'risk_assessment_enabled' => 'risk assessment enabled',
            'attendance_enabled' => 'attendance enabled',
            'moodle_enabled' => 'moodle enabled',
            'results_enabled' => 'results enabled',
            'payment_enabled' => 'payment enabled',
            'category_low_risk_weeks' => 'low risk weeks',
            'category_medium_risk_weeks' => 'medium risk weeks',
            'category_high_risk_weeks' => 'high risk weeks',
            'category_low_risk_email' => 'low risk email template',
            'category_medium_risk_email' => 'medium risk email template',
            'category_high_risk_email' => 'high risk email template',
            'attendance_low_risk' => 'attendance low risk threshold',
            'attendance_medium_risk' => 'attendance medium risk threshold',
            'attendance_high_risk' => 'attendance high risk threshold',
            'moodle_low_risk' => 'moodle low risk threshold',
            'moodle_medium_risk' => 'moodle medium risk threshold',
            'moodle_high_risk' => 'moodle high risk threshold',
            'results_low_risk' => 'results low risk threshold',
            'results_medium_risk' => 'results medium risk threshold',
            'results_high_risk' => 'results high risk threshold',
            'payment_low_risk' => 'payment low risk threshold',
            'payment_medium_risk' => 'payment medium risk threshold',
            'payment_high_risk' => 'payment high risk threshold',
        ];
    }
}
