<?php

namespace GalaxyAPI\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Config;

class AgentCommissionResource extends JsonResource
{
    public function toArray($request): array
    {
        $arrCommissionPeriod = Config::get('constants.arrCommissionPeriod');

        return [
            'id' => $this->id ?? null,
            'origin' => 'all',
            'agent_id' => $this->agent_id ?? null,
            'agent_status' => $this->agent_status ?? null,
            'agent' => $this->whenLoaded('agent', function () {
                return [
                    'id' => $this->agent->id ?? null,
                    'name' => $this->agent->agency_name ?? null,
                ];
            }),
            'course_id' => $this->course ?? null,
            'course_type' => $this->course_type ?? null,
            'course' => $this->whenLoaded('courseItem', function () {
                return [
                    'id' => $this->courseItem->id ?? null,
                    'code' => $this->courseItem->course_code ?? null,
                    'name' => $this->courseItem->course_name ?? null,
                ];
            }),
            'commission_period' => $this->commission_period ?? null,
            'commission_period_name' => $this->commission_period ? $arrCommissionPeriod[$this->commission_period] : null,
            'commission' => $this->commission ?? null,
            'gst' => $this->gst ?? null,
            'rate_valid_from' => $this->rate_valid_from ?? null,
            'rate_valid_to' => $this->rate_valid_to ?? null,
            'created_by' => $this->created_by ?? null,
            'updated_by' => $this->updated_by ?? null,
            'created_at' => $this->created_at ?? null,
            'updated_at' => $this->updated_at ?? null,
        ];
    }
}
