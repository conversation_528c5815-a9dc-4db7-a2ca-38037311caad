<?php

namespace GalaxyAPI\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class AgentCommunicationResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            // Return transformed AgentCommunication data
            'id' => $this->id,
            'agent_id' => $this->agent_id,
            'comment_by' => $this->whenLoaded('user', function () {
                return [
                    'id' => $this->user->id,
                    'full_name' => $this->user?->name,
                    'username' => $this->user->username,
                ];
            }),
            'type_id' => $this->category,
            'type_name' => $this->whenLoaded('setupType', function () {
                return $this->setupType->value;
            }) ?? null,
            'status_id' => $this->status,
            'status_name' => $this->whenLoaded('setupStatus', function () {
                return $this->setupStatus->value;
            }) ?? null,
            'comment' => $this->comment,
            'created_by' => $this->created_by,
            'updated_by' => $this->updated_by,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
