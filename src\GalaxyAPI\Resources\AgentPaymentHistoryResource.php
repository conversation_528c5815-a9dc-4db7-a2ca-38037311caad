<?php

namespace GalaxyAPI\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class AgentPaymentHistoryResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            // Return transformed AgentPaymentHistory data
            'agent' => $this->whenLoaded('agent', function () {
                return [
                    'id' => $this->agent->id,
                    'name' => $this->agent->agency_name,
                ];
            }),
            'student' => $this->whenLoaded('student', function () {
                return [
                    'id' => $this->student->id,
                    'generated_stud_id' => $this->student->generated_stud_id,
                    'name' => $this->student->first_name.' '.$this->student?->middel_name.' '.$this->student?->family_name,
                ];
            }),
            'course_code' => $this->whenLoaded('course', function () {
                return $this->course->course_code;
            }),
            'course' => $this->whenLoaded('course', function () {
                return [
                    'id' => $this->course->id,
                    'code' => $this->course->course_code,
                    'name' => $this->course->course_name,

                ];
            }),
            'course_attempt' => 1,
            'invoice_no' => $this->invoice_no,
            'formatted_invoice_number' => $this->formatted_invoice_number,
            'upfront_fee_to_pay' => $this->whenLoaded('transaction', function () {
                return $this->transaction->invoice?->upfront_fee_to_pay;
            }),
            'paid_amount' => $this->whenLoaded('transaction', function () {
                return $this->transaction->invoice?->paid_amount;
            }),
            'paid_date' => $this->paid_date,
            'commission_payable' => $this->commission_payable,
            'gst_amount' => $this->gst_amount,
            'commission_paid' => $this->commission_paid,
            'comm_paid_date' => $this->comm_paid_date,
            'remarks' => $this->remarks,
        ];
    }
}
