<?php

namespace GalaxyAPI\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class AgentStudentResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            // Return transformed AgentStudent data
            'id' => $this->id,
            'generated_stud_id' => $this->generated_stud_id,
            'application_reference_id' => $this->application_reference_id,
            'student_name' => $this->first_name.' '.$this->family_name,
            'email' => $this->email,
            'campus' => $this->whenLoaded('studentCourses', function () {
                return $this->studentCourses->pluck('campus.name')->unique()->values();
            }),
            'current_course' => $this->whenLoaded('studentCourses', function () {
                return $this->studentCourses->map(function ($course) {
                    return [
                        'id' => $course->id,
                        'course_code' => $course->course->course_code,
                        'course_name' => $course->course->course_name,
                        'status' => $course->course->status,
                    ];
                });
            }),
        ];
    }
}
