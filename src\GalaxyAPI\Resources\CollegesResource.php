<?php

namespace GalaxyAPI\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class CollegesResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'RTO_code' => $this->RTO_code,
            'CRICOS_code' => $this->CRICOS_code,
            'college_name' => $this->college_name,
            'public_RTO' => $this->public_RTO,
            'legal_name' => $this->legal_name,
            'contact_person' => $this->contact_person,
        ];
    }
}
