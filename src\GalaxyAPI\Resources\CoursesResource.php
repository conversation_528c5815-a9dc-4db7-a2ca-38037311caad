<?php

namespace GalaxyAPI\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class CoursesResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'course_code' => $this->course_code,
            'course_name' => $this->course_name,
            'course' => $this->course_code.' - '.$this->course_name,
        ];
    }
}
