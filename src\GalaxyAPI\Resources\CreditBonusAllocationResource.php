<?php

namespace GalaxyAPI\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Config;

class CreditBonusAllocationResource extends JsonResource
{
    public function toArray($request): array
    {
        $crditAllocationType = Config::get('constants.crditAllocationType');

        return [
            'id' => $this->id,
            'college_id' => $this->college_id,
            'agent_id' => $this->agent_id,
            'agent' => $this->whenLoaded('agent', function () {
                return new AgentResource($this->agent);
            }),
            'std_transaction_no' => $this->std_transaction_no,
            'type' => $this->type,
            'type_name' => $this->type ? $crditAllocationType[$this->type] : null,
            'amount' => $this->amount,
            'payment_mode' => $this->payment_mode,
            'payment_mode_data' => $this->whenLoaded('paymentMode', function () {
                return [
                    'id' => $this->paymentMode->id,
                    'name' => $this->paymentMode->name,
                    'description' => $this->paymentMode?->description,
                ];
            }),
            'credit_used' => $this->credit_used,
            'payment_date' => $this->payment_date,
            'comment' => $this->comment,
            'created_by' => $this->created_by,
            'updated_by' => $this->updated_by,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
