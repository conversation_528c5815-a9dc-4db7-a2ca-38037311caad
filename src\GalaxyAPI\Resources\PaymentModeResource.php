<?php

namespace GalaxyAPI\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class PaymentModeResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            // Return transformed PaymentMode data
            'id' => $this->id,
            'college_id' => $this->college_id,
            'name' => $this->name,
            'description' => $this->description,
            'is_required' => $this->is_required,
            'account_no_id' => $this->account_no_id,
            'is_default_mode' => $this->is_default_mode,
            'is_default_entry' => $this->is_default_entry,
            'created_by' => $this->created_by,
            'updated_by' => $this->updated_by,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
