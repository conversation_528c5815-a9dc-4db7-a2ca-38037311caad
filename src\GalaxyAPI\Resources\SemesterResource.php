<?php

namespace GalaxyAPI\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\Config;

class SemesterResource extends JsonResource
{
    public function toArray($request): array
    {
        // Get calendar types from constants
        $calendarTypes = Config::get('constants.arrCalendarTypeV2');
        $calendarTypeText = isset($calendarTypes[$this->calendar_type]) ? $calendarTypes[$this->calendar_type] : $this->calendar_type;

        return [
            'id' => $this->id,
            'college_id' => $this->college_id,
            'course_type_id' => $this->course_type_id,
            'semester_name' => $this->semester_name,
            'year' => $this->year,
            'calendar_type' => $calendarTypeText,
            'calendar_type_id' => $this->calendar_type,
            'semester_start' => $this->semester_start ? parseDate($this->semester_start, 'd-m-Y') : null,
            'semester_finish' => $this->semester_finish ? parseDate($this->semester_finish, 'd-m-Y') : null,
            'created_by' => $this->created_by,
            'updated_by' => $this->updated_by,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
            // Include course type relationship if loaded
            'course_type' => $this->whenLoaded('courseType', function () {
                return $this->courseType->title;
            }),
        ];
    }
}
