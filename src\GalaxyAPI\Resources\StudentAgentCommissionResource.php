<?php

namespace GalaxyAPI\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class StudentAgentCommissionResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            // Return transformed StudentAgentCommission data
            'agent' => $this->whenLoaded('agent', function () {
                return [
                    'id' => $this->agent->id,
                    'name' => $this->agent->agency_name,
                ];
            }),
            'student' => $this->whenLoaded('student', function () {
                return [
                    'id' => $this->student->id,
                    'generated_stud_id' => $this->student->generated_stud_id,
                    'name' => $this->student->first_name.' '.$this->student?->middel_name.' '.$this->student?->family_name,
                ];
            }),
            'course' => $this->whenLoaded('course', function () {
                return [
                    'id' => $this->course->id,
                    'code' => $this->course->course_code,
                    'name' => $this->course->course_name,
                ];
            }),
            'transaction' => $this->whenLoaded('transaction', function () {
                return [
                    'id' => $this->transaction->invoice?->id,
                    'upfront_fee_to_pay' => $this->transaction->invoice?->upfront_fee_to_pay,
                    'paid_amount' => $this->transaction->invoice?->paid_amount,
                    'comm_paid_date' => $this->transaction->invoice?->comm_paid_date,
                ];
            }),
            'invoice_no' => $this->invoice_no,
            'formatted_invoice_number' => $this->formatted_invoice_number,
            'paid_date' => $this->paid_date,
            'due_date' => $this->due_date,
            'commission_payable' => $this->commission_payable,
            'gst_amount' => $this->gst_amount,
            'commission_paid' => $this->commission_paid,
            'remarks' => $this->remarks,
        ];
    }
}
