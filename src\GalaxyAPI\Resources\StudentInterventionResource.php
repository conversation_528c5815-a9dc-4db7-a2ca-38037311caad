<?php

namespace GalaxyAPI\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class StudentInterventionResource extends JsonResource
{
    public function toArray($request): array
    {
        $studentInterventionStrategies = $this->studentInterventionStrategies;

        return [
            'id' => $this->id,
            'recorded_date' => $this->recorded_date,
            'campus' => $this->getCampus(),
            'course' => $this->getCourse(),
            'semester' => $this->getSemester(),
            'student' => $this->getStudent(),
            'teacher' => $this->getTeacher(),
            'due_date' => $this->due_date,
            'outcome_date' => $this->outcome_date,
            'intervention_type_ids' => $studentInterventionStrategies->pluck('intervention_type_id')
                ->unique()
                ->toArray(),
            'intervention_strategy_ids' => $studentInterventionStrategies->pluck('intervention_strategy_id')
                ->unique()
                ->toArray(),
            'intervention_types' => $this->getInterventionType($studentInterventionStrategies),
            'intervention_strategies' => $this->getInterventionStrategy($studentInterventionStrategies),
            'case_detail' => $this->case_detail,
            'action_taken' => $this->action_taken,
            'reoccurance' => $this->reoccurance,
            'escalated_to' => $this->escalated_to,
            'case_status' => $this->case_status,
        ];
    }

    private function getCampus()
    {
        $campus = $this->collage;
        if (! $campus) {
            return null;
        }

        return [
            'id' => $campus->id,
            'collage_name' => $campus->collage_name,
        ];
    }

    private function getStudent()
    {
        $student = $this->student;
        if (! $student) {
            return null;
        }

        return [
            'id' => $student->id,
            'first_name' => $student->first_name,
            'family_name' => $student->family_name,
            'full_name' => $student->name_title.' '.$student->first_name.' '.$student->family_name,
            'generated_stud_id' => $student->generated_stud_id,
        ];
    }

    private function getCourse()
    {
        $course = $this->course;
        if (! $course) {
            return null;
        }

        return [
            'id' => $course->id,
            'course_code' => $course->course_code,
            'course_name' => $course->course_name,
        ];
    }

    private function getSemester()
    {
        $semester = $this->studentSemester;
        if (! $semester) {
            return null;
        }

        return [
            'id' => $semester->id,
            'semester_name' => $semester->semester_name,
        ];
    }

    private function getInterventionType($studentInterventionStrategies)
    {
        $interventionTypes = [];
        foreach ($studentInterventionStrategies as $interventionStrategy) {
            $interventionTypes[] = $interventionStrategy->interventionType->intervention_type;
        }

        return collect($interventionTypes)->unique()->toArray();
    }

    private function getInterventionStrategy($studentInterventionStrategies)
    {
        $interventionStrategies = [];
        foreach ($studentInterventionStrategies as $interventionStrategy) {
            $interventionStrategies[] = $interventionStrategy->interventionStrategy->strategy;
        }

        return collect($interventionStrategies)->unique()->toArray();
    }

    private function getTeacher()
    {
        $teacher = $this->teacher;
        if (! $teacher) {
            return null;
        }

        return [
            'id' => $teacher->id,
            'name_title' => $teacher->name_title,
            'first_name' => $teacher->first_name,
            'last_name' => $teacher->last_name,
            'full_name' => $teacher->name_title.' '.$teacher->first_name.' '.$teacher->last_name,
        ];
    }
}
