<?php

namespace GalaxyAPI\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class StudentPaymentAgentCreditResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'college_id' => $this->college_id,
            'student_id' => $this->whenLoaded('studentInitialPayment', function () {
                return [
                    'id' => $this->studentInitialPayment->student_id,
                    'generated_stud_id' => $this->studentInitialPayment->student->generated_stud_id,
                    'name' => $this->studentInitialPayment->student->first_name.' '.$this->studentInitialPayment->student?->middel_name.' '.$this->studentInitialPayment->student?->family_name,
                    'course_id' => $this->studentInitialPayment->course_id,
                    'course_name' => $this->studentInitialPayment->course?->course_name,
                    'course_code' => $this->studentInitialPayment->course?->course_code,
                    'course_attempt' => $this->studentInitialPayment->studentCourses?->course_attempt,
                    'receipt_no' => $this->studentInitialPayment->receipt_no,
                    'transection_no' => $this->studentInitialPayment->transection_no,
                ];
            }),
            'payment_transaction_id' => $this->payment_transaction_id,
            'credit_bonus_id' => $this->credit_bonus_id,
            'credit_bonus' => $this->whenLoaded('creditBonusAllocation', function () {
                return [
                    'id' => $this->creditBonusAllocation->id,
                    'amount' => $this->creditBonusAllocation->amount,
                    'credit_used' => $this->creditBonusAllocation->credit_used,
                ];
            }),
            'payment_transaction' => $this->whenLoaded('studentInitialPayment', function () {
                return [
                    'id' => $this->studentInitialPayment->id,
                    'student_id' => $this->studentInitialPayment->student_id,
                    'course_id' => $this->studentInitialPayment->course_id,
                ];
            }),
            'course_info' => $this->whenLoaded('studentInitialPayment.course', function () {
                return [
                    'id' => $this->studentInitialPayment->course?->id,
                    'code' => $this->studentInitialPayment->course?->course_code,
                    'name' => $this->studentInitialPayment->course?->course_name,
                ];
            }),
            'amount' => $this->amount,
            'created_by' => $this->created_by,
            'updated_by' => $this->updated_by,
            'created_at' => $this->created_at,
            'updated_at' => $this->updated_at,
        ];
    }
}
