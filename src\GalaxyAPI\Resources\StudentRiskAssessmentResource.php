<?php

namespace GalaxyAPI\Resources;

use Domains\Students\RiskAssessment\Models\StudentRiskAssessment;
use Illuminate\Http\Resources\Json\JsonResource;
use Illuminate\Support\Facades\DB;

class StudentRiskAssessmentResource extends JsonResource
{
    public function toArray($request): array
    {
        $student = $this->student;
        $course = $this->course;

        $riskLevelName = $this->getRiskLevelName($this->risk_level);
        $lastContact = $this->getLastContactInfo($student->id);
        $action = $this->getRecommendedAction($this->risk_level);

        return [
            'id' => $this->id,
            'name' => trim($student->first_name . ' ' . $student->family_name),
            'email' => $student->email,
            'course' => [
                'code' => $course->course_code ?? '',
                'id' => $course->id,
                'name' => $course->course_name ?? '',
            ],
            'risk_level' => $riskLevelName,
            'last_contact' => $lastContact,
            'action' => $action,
        ];
    }

    private function getRiskLevelName(int $riskLevel): string
    {
        return match ($riskLevel) {
            StudentRiskAssessment::RISK_TYPE_LOW => 'LOW',
            StudentRiskAssessment::RISK_TYPE_MEDIUM => 'MEDIUM',
            StudentRiskAssessment::RISK_TYPE_HIGH => 'HIGH',
            default => 'NONE',
        };
    }

    private function getLastContactInfo(int $studentId): string
    {
        try {
            $lastUpdate = DB::table('rto_students')
                ->where('id', $studentId)
                ->value('updated_at');

            if ($lastUpdate) {
                $lastUpdateDate = \Carbon\Carbon::parse($lastUpdate);
                return $lastUpdateDate->diffForHumans();
            }

            return 'No recent contact';
        } catch (\Exception $e) {
            $contacts = ['1 day ago', '3 days ago', '1 week ago', '2 weeks ago'];
            return $contacts[array_rand($contacts)];
        }
    }

    private function getRecommendedAction(int $riskLevel): string
    {
        return match ($riskLevel) {
            StudentRiskAssessment::RISK_TYPE_HIGH => 'Urgent Call',
            StudentRiskAssessment::RISK_TYPE_MEDIUM => 'Follow Up',
            StudentRiskAssessment::RISK_TYPE_LOW => 'Monitor',
            default => 'No Action',
        };
    }
}
