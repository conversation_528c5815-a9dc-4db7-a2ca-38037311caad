<?php

namespace GalaxyAPI\Resources;

use Illuminate\Http\Resources\Json\JsonResource;
use Carbon\Carbon;

class StudentRiskAssessmentSemesterGroupedResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'student_id' => $this->student_id,
            'student_number' => $this->student_number,
            'student_name' => $this->student_name,
            'student_email' => $this->student_email,
            'course' => [
                'id' => $this->course_id,
                'code' => $this->course_code,
                'name' => $this->course_name,
                'display_name' => $this->course_code . ' : ' . $this->course_name,
            ],
            'assessment_summary' => [
                'total_assessments' => (int) $this->total_assessments,
                'latest_assessment_date' => $this->latest_assessment_date ? Carbon::parse($this->latest_assessment_date)->format('Y-m-d') : null,
                'latest_assessment_date_formatted' => $this->latest_assessment_date ? Carbon::parse($this->latest_assessment_date)->format('d M Y') : '-',
                'current_risk_level' => (int) $this->current_risk_level,
                'current_risk_level_name' => $this->getRiskLevelName((int) $this->current_risk_level),
                'lowest_risk_level' => (int) $this->lowest_risk_level,
                'average_risk_level' => round((float) $this->average_risk_level, 1),
            ],
            'risk_trend' => [
                'trend' => $this->calculateRiskTrend(),
                'trend_display' => $this->getRiskTrendDisplay(),
                'first_risk_level' => (int) $this->first_risk_level,
                'last_risk_level' => (int) $this->last_risk_level,
            ],
            // Additional computed properties
            'has_high_risk' => (int) $this->current_risk_level >= 3,
            'needs_attention' => $this->needsAttention(),
            'days_since_last_assessment' => $this->latest_assessment_date ? Carbon::parse($this->latest_assessment_date)->diffInDays(now()) : null,
        ];
    }

    /**
     * Get risk level name from numeric value
     */
    private function getRiskLevelName(int $riskLevel): string
    {
        return match ($riskLevel) {
            1 => 'LOW',
            2 => 'MEDIUM',
            3 => 'HIGH',
            default => 'NONE',
        };
    }

    /**
     * Calculate risk trend based on first and last assessments
     */
    private function calculateRiskTrend(): string
    {
        $first = (int) $this->first_risk_level;
        $last = (int) $this->last_risk_level;

        if ($first === $last) {
            return 'stable';
        } elseif ($last > $first) {
            return 'declining'; // Higher risk level = declining performance
        } else {
            return 'improving'; // Lower risk level = improving performance
        }
    }

    /**
     * Get human-readable risk trend display
     */
    private function getRiskTrendDisplay(): string
    {
        $trend = $this->calculateRiskTrend();
        
        return match ($trend) {
            'improving' => '↗️ Improving',
            'declining' => '↘️ Declining',
            'stable' => '→ Stable',
            default => '- No Trend',
        };
    }

    /**
     * Determine if student needs attention
     */
    private function needsAttention(): bool
    {
        // High current risk level
        if ((int) $this->current_risk_level >= 3) {
            return true;
        }

        // Declining trend
        if ($this->calculateRiskTrend() === 'declining') {
            return true;
        }

        // No recent assessment (more than 30 days)
        if ($this->latest_assessment_date && Carbon::parse($this->latest_assessment_date)->diffInDays(now()) > 30) {
            return true;
        }

        return false;
    }
}
