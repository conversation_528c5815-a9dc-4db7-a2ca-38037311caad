<?php

namespace GalaxyAPI\Resources;

use Illuminate\Http\Resources\Json\JsonResource;

class StudentRiskAssessmentSemesterResource extends JsonResource
{
    public function toArray($request): array
    {
        return [
            'id' => $this->id,
            'risk_assessment_id' => $this->risk_assessment_id,
            'semester_id' => $this->semester_id,
            'risk_category' => $this->risk_category,
            'risk_type' => $this->risk_type,
            'risk_type_name' => $this->getRiskLevelName(),
            'risk_level_name' => $this->getLevelName(),
            'queue_date' => $this->queue_date?->format('Y-m-d'),
            'queue_date_formatted' => $this->queue_date?->format('d M Y'),
            'status' => $this->status,
            'status_formatted' => ucfirst(str_replace('_', ' ', $this->status)),
            'remarks' => $this->remarks,
            'data' => $this->data,
            'config' => $this->config,
            'created_at' => $this->created_at?->format('Y-m-d H:i:s'),
            'updated_at' => $this->updated_at?->format('Y-m-d H:i:s'),
            
            // Relationship data
            'risk_assessment' => $this->whenLoaded('riskAssessment', function () {
                return [
                    'id' => $this->riskAssessment->id,
                    'risk_level' => $this->riskAssessment->risk_level,
                    'student' => $this->whenLoaded('riskAssessment.student', function () {
                        return [
                            'id' => $this->riskAssessment->student->id,
                            'name' => $this->riskAssessment->student->name ?? 
                                     ($this->riskAssessment->student->first_name . ' ' . $this->riskAssessment->student->last_name),
                            'student_id' => $this->riskAssessment->student->student_id,
                            'email' => $this->riskAssessment->student->email,
                        ];
                    }),
                    'course' => $this->whenLoaded('riskAssessment.course', function () {
                        return [
                            'id' => $this->riskAssessment->course->id,
                            'name' => $this->riskAssessment->course->name,
                            'code' => $this->riskAssessment->course->code,
                        ];
                    }),
                ];
            }),
            
            'semester' => $this->whenLoaded('semester', function () {
                return [
                    'id' => $this->semester->id,
                    'semester_name' => $this->semester->semester_name,
                    'year' => $this->semester->year,
                    'semester_start' => $this->semester->semester_start?->format('Y-m-d'),
                    'semester_finish' => $this->semester->semester_finish?->format('Y-m-d'),
                ];
            }),
            
            // Computed properties
            'is_due_today' => $this->isDueToday(),
            'is_overdue' => $this->isOverdue(),
            'is_level_first' => $this->isLevelFirst(),
            'is_level_second' => $this->isLevelSecond(),
            'is_level_final' => $this->isLevelFinal(),
        ];
    }
}
