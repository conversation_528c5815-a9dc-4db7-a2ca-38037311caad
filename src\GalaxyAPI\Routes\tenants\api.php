<?php

use GalaxyAPI\Controllers\AgentController;
use GalaxyAPI\Controllers\AssignSupervisorController;
use GalaxyAPI\Controllers\CollegeDetailController;
use GalaxyAPI\Controllers\CollegesController;
use GalaxyAPI\Controllers\CommonUtilityController;
use GalaxyAPI\Controllers\CountryController;
use GalaxyAPI\Controllers\CoursesController;
use GalaxyAPI\Controllers\CourseTypeController;
use GalaxyAPI\Controllers\EmailTemplateController;
use GalaxyAPI\Controllers\EmployerController;
use GalaxyAPI\Controllers\InterventionStrategyController;
use GalaxyAPI\Controllers\InterventionTypeController;
use GalaxyAPI\Controllers\LeaveController;
use GalaxyAPI\Controllers\PayPeriodController;
use GalaxyAPI\Controllers\PdfTemplateController;
use GalaxyAPI\Controllers\RegisterImprovementController;
use GalaxyAPI\Controllers\SemesterController;
use GalaxyAPI\Controllers\SetupProviderController;
use GalaxyAPI\Controllers\SetupServiceController;
use GalaxyAPI\Controllers\SetupServicesCategoryController;
use GalaxyAPI\Controllers\SetupServicesNameController;
use GalaxyAPI\Controllers\StaffCommunicationLogController;
use GalaxyAPI\Controllers\StaffController;
use GalaxyAPI\Controllers\StaffEmailController;
use GalaxyAPI\Controllers\StaffPositionController;
use GalaxyAPI\Controllers\StudentController;
use GalaxyAPI\Controllers\StudentInterventionController;
use GalaxyAPI\Controllers\StudentRiskAssessmentController;
use GalaxyAPI\Controllers\StudentRiskAssessmentSemesterController;
use GalaxyAPI\Controllers\StudentRiskAssessmentSettingsController;
use GalaxyAPI\Controllers\StudentTrainingActivityLogController;
use GalaxyAPI\Controllers\StudentTrainingController;
use GalaxyAPI\Controllers\TeacherController;
use GalaxyAPI\Controllers\TeacherMatrixController;
use GalaxyAPI\Controllers\TimesheetApprovalController;
use GalaxyAPI\Controllers\TimesheetApprovedController;
use GalaxyAPI\Controllers\TimesheetSubmissionController;
use GalaxyAPI\Controllers\TimetableController;
use GalaxyAPI\Controllers\USIVerificationController;
use Illuminate\Support\Facades\Route;
use Stancl\Tenancy\Middleware\PreventAccessFromCentralDomains;

/*
 * Documentation:
 * Supported routes:
    Route::get('', 'index');
    Route::get('bulk-actions', 'getBulkActions');
    Route::post('bulk-actions', 'handelBulkActions');
    Route::post('', 'store');
    Route::get('{id}', 'show');
    Route::post('{id}', 'update');
    Route::put('{id}/status-change/{column}', 'changeStatusOtherColumn');
    Route::put('{id}/status-change', 'changeStatus');
    Route::delete('{id}', 'destroy');
*/

Route::middleware([PreventAccessFromCentralDomains::class])
    ->group(function () {
        Route::prefix('utilities')
            ->controller(CommonUtilityController::class)
            ->group(function () {
                Route::get('get-enum-options', 'getEnumOptions');
            });
        Route::prefix('countries')
            ->controller(CountryController::class)
            ->group(function () {
                Route::get('', 'index');
                Route::get('{id}', 'show');
            });

        Route::prefix('students')
            ->controller(StudentController::class)
            ->group(function () {
                Route::get('', 'index');
                Route::get('export/usi-students', 'exportUSIStudents');
                Route::post('', 'store');
                Route::get('{id}', 'show');
                Route::post('{id}', 'update');
            });
        Route::prefix('course-types')
            ->controller(CourseTypeController::class)
            ->group(function () {
                Route::get('', 'index');
                Route::get('{id}', 'show');
            });

        Route::prefix('usi-verifications')
            ->controller(USIVerificationController::class)
            ->group(function () {
                Route::post('verify', 'verify');
                Route::post('verify-bulk', 'verifyBulk');
            });

        Route::prefix('pdf-templates')
            ->controller(PdfTemplateController::class)
            ->group(function () {
                Route::get('', 'index');
                Route::post('', 'store');
                Route::get('{id}', 'show');
                Route::post('{id}', 'update');
                Route::post('{id}/update-default', 'updateDefaultTemplate');
            });

        Route::prefix('register-improvement')->controller(RegisterImprovementController::class)->group(function () {
            Route::get('', 'index');
            Route::get('form-constants', 'formConstants');
            Route::post('', 'store');
            Route::get('{id}', 'show');
            Route::post('delete', 'delete');
            Route::post('{id}', 'update');
        });

        Route::prefix('staff')
            ->controller(StaffController::class)
            ->group(function () {
                Route::get('', 'index');
            });

        Route::prefix('staff-position')
            ->controller(StaffPositionController::class)
            ->group(function () {
                Route::get('', 'index');
            });

        Route::prefix('staff-communication-log')
            ->controller(StaffCommunicationLogController::class)
            ->group(function () {
                Route::get('', 'index');
                Route::post('', 'store');
                Route::get('{id}', 'show');
                Route::post('delete', 'delete');
                Route::post('{id}', 'update');
            });

        Route::prefix('staff-email')->controller(StaffEmailController::class)->group(function () {
            Route::get('', 'index');
            Route::post('', 'store');
            Route::get('get-college-email', 'getCollegeEmail');
            Route::get('{id}', 'show');
            Route::post('{id}', 'update');
        });

        Route::prefix('teacher')
            ->controller(TeacherController::class)
            ->group(function () {
                Route::get('', 'index');
                Route::post('', 'store');
                Route::get('{id}', 'show');
                Route::post('delete', 'delete');
                Route::post('{id}', 'update');
                Route::post('{id}/user-id', 'updateUserId');
                Route::get('{id}/document', 'getDocument');
                Route::post('{id}/create-folder', 'createFolder');
                Route::post('{id}/upload-documents', 'uploadDocuments');
                Route::put('{id}/rename', 'renameFileFolder');
                Route::post('{id}/delete', 'deleteDocument');
                Route::post('{id}/move', 'moveDocument');
                Route::post('{id}/bookmark', 'bookmarkDocument')->name('spa.teacher.bookmarkDocument');
                Route::post('{id}/search', 'searchDocument');
                Route::get('{id}/downloadFile/{fileId}', 'downloadFile');
            });

        Route::prefix('teacher-matrix')
            ->controller(TeacherMatrixController::class)->group(function () {
                Route::get('', 'index');
                Route::post('', 'store');
                Route::get('{id}', 'show');
                Route::post('delete', 'delete');
                Route::post('{id}', 'update');
            });

        Route::prefix('pay-period')
            ->controller(PayPeriodController::class)
            ->group(function () {
                Route::get('', 'index');
                Route::get('financial-years', 'financialYears');
                Route::get('form-constants', 'formConstants');
                Route::post('', 'store');
                Route::post('delete', 'delete');
            });

        Route::prefix('assign-supervisor')
            ->controller(AssignSupervisorController::class)
            ->group(function () {
                Route::get('', 'index');
                Route::get('form-constants', 'getFormConstants');
                Route::get('supervisors', 'getSupervisors');
                Route::post('', 'store');
                Route::post('delete', 'delete');
            });

        Route::prefix('leave-info')
            ->controller(LeaveController::class)
            ->group(function () {
                Route::get('', 'index');
                Route::get('bulk-actions', 'getBulkActions');
                Route::post('bulk-actions', 'handelBulkActions');
                Route::post('', 'store');
                Route::get('{id}', 'show');
                Route::post('delete', 'delete');
                Route::post('{id}', 'update');
            });

        Route::prefix('timesheet-submission')
            ->controller(TimesheetSubmissionController::class)
            ->group(function () {
                Route::get('', 'index');
                Route::post('', 'store');
                Route::get('form-constants', 'formConstants');
                Route::post('get-pay-period-list', 'getPayPeriodList');
                Route::post('get-submitted-timesheet-list', 'getSubmittedTimesheetList');
                Route::post('auto-timesheet-submission', 'storeAutoTimesheet');
                Route::post('delete', 'delete');
            });

        Route::prefix('employer')
            ->controller(EmployerController::class)
            ->group(function () {
                Route::get('', 'index');
                Route::get('form-constants', 'formConstants');
                Route::post('', 'store');
                Route::get('{id}', 'show');
                Route::post('delete', 'delete');
                Route::post('{id}', 'update');
            });

        Route::prefix('country')
            ->controller(CountryController::class)
            ->group(function () {
                Route::get('', 'index');
                Route::post('', 'store');
                Route::get('form-constants', 'formConstants');
                Route::post('delete', 'delete');
            });

        Route::prefix('timesheet-submission')
            ->controller(StaffController::class)
            ->group(function () {
                Route::get('staff', 'index');
            });

        Route::prefix('timetable')->controller(TimetableController::class)->group(function () {
            Route::get('', 'index');
            Route::post('', 'store');
            Route::get('{id}', 'show');
            Route::post('delete', 'delete');
            Route::post('{id}', 'update');
        });

        Route::prefix('setup-services')
            ->controller(SetupServiceController::class)
            ->group(function () {
                Route::get('', 'index');
                Route::post('', 'store');
                Route::get('{id}', 'show');
                Route::post('delete', 'delete');
                Route::post('{id}', 'update');
            });
        Route::prefix('setup-service-names')
            ->controller(SetupServicesNameController::class)
            ->group(function () {
                Route::get('', 'index');
                Route::post('', 'store');
                Route::get('{id}', 'show');
                Route::post('delete', 'delete');
                Route::post('{id}', 'update');
            });
        Route::prefix('setup-service-categories')
            ->controller(SetupServicesCategoryController::class)
            ->group(function () {
                Route::get('', 'index');
                Route::post('', 'store');
                Route::get('{id}', 'show');
                Route::post('delete', 'delete');
                Route::post('{id}', 'update');
            });
        Route::prefix('setup-provider')
            ->controller(SetupProviderController::class)
            ->group(function () {
                Route::get('', 'index');
                Route::post('', 'store');
                Route::get('{id}', 'show');
                Route::post('delete', 'delete');
                Route::post('{id}', 'update');
            });
        Route::prefix('timetable')->controller(TimetableController::class)->group(function () {
            Route::get('', 'index');
            Route::post('', 'store');
            Route::get('{id}', 'show');
            Route::post('delete', 'delete');
            Route::post('{id}', 'update');
        });
        Route::prefix('timesheet-approval')
            ->controller(TimesheetApprovalController::class)
            ->group(function () {
                Route::get('', 'index');
                Route::post('pay-periods', 'getPayPeriodList');
                Route::post('approve', 'approve');
            });

        Route::prefix('email-template')
            ->controller(EmailTemplateController::class)
            ->group(function () {
                Route::get('', 'index');
            });
        Route::prefix('college-detail')
            ->controller(CollegeDetailController::class)
            ->group(function () {
                Route::get('', 'index');
            });

        Route::prefix('timesheet-approved')
            ->controller(TimesheetApprovedController::class)
            ->group(function () {
                Route::get('', 'index');
                Route::post('pay-periods', 'getPayPeriodList');
                Route::post('approved-timesheet-modal', 'getApprovedTimesheetListForModal');
            });
        Route::prefix('countries')
            ->controller(CountryController::class)
            ->group(function () {
                Route::get('', 'index');
            });

        Route::prefix('semester')
            ->controller(SemesterController::class)
            ->group(function () {
                Route::get('', 'index');
                Route::get('form-constants', 'formConstants');
                Route::post('', 'store');
                Route::get('{id}', 'show');
                Route::post('delete', 'delete');
                Route::delete('{id}', 'destroy');
                Route::post('{id}', 'update');
            });

        Route::prefix('student-training')->controller(StudentTrainingController::class)->group(function () {
            Route::get('', 'index');
            Route::post('', 'store');
            Route::get('{id}', 'show');
            Route::post('delete', 'delete');
            Route::post('{id}', 'update');
        });

        Route::prefix('admin-agent')->controller(AgentController::class)->group(function () {
            Route::get('', 'index');
            Route::post('', 'store');
            Route::get('{id}', 'show');
            Route::post('delete', 'delete');
            Route::post('{id}', 'update');
        });

        Route::prefix('student-training-activity-log')->controller(StudentTrainingActivityLogController::class)->group(function () {
            Route::get('activity-types', 'getActivityTypes');
            Route::get('', 'index');
            Route::post('', 'store');
            Route::get('{id}', 'show');
            Route::get('{id}/download', 'downloadFile');
            Route::post('delete', 'delete');
            Route::post('{id}', 'update');
        });

        Route::prefix('student-interventions')
            ->controller(StudentInterventionController::class)->group(function () {
                Route::get('', 'index');
                Route::post('', 'store');
                Route::get('{id}', 'show');
                Route::post('delete', 'delete');
                Route::post('{id}', 'update');
            });
        Route::prefix('collages')
            ->controller(CollegesController::class)->group(function () {
                Route::get('', 'index');
                Route::post('', 'store');
                Route::get('{id}', 'show');
                Route::post('delete', 'delete');
                Route::post('{id}', 'update');
            });
        Route::prefix('courses')
            ->controller(CoursesController::class)->group(function () {
                Route::get('', 'index');
                Route::post('', 'store');
                Route::get('{id}', 'show');
                Route::post('delete', 'delete');
                Route::post('{id}', 'update');
            });
        Route::prefix('intervention-strategies')
            ->controller(InterventionStrategyController::class)->group(function () {
                Route::get('', 'index');
                Route::post('', 'store');
                Route::get('{id}', 'show');
                Route::post('delete', 'delete');
                Route::post('{id}', 'update');
            });
        Route::prefix('intervention-types')
            ->controller(InterventionTypeController::class)->group(function () {
                Route::get('', 'index');
                Route::post('', 'store');
                Route::get('{id}', 'show');
                Route::post('delete', 'delete');
                Route::post('{id}', 'update');
            });
        Route::prefix('semesters')
            ->controller(SemesterController::class)->group(function () {
                Route::get('', 'index');
                Route::post('', 'store');
                Route::get('{id}', 'show');
                Route::post('delete', 'delete');
                Route::post('{id}', 'update');
            });

        Route::prefix('student-risk-assessment-settings')
            ->controller(StudentRiskAssessmentSettingsController::class)
            ->group(function () {
                Route::get('', 'index');
                Route::post('', 'saveSettings');
                Route::get('email-templates', 'emailTemplates');
                Route::get('category-parameters', 'getCategoryParameters');
                Route::post('category-parameters', 'saveCategoryParameters');
                Route::post('toggle-category', 'toggleCategory');
            });

        Route::prefix('student-risk-assessments')
            ->controller(StudentRiskAssessmentController::class)
            ->group(function () {
                Route::get('', 'index');
                Route::post('', 'store');
                Route::get('{id}', 'show');
                Route::post('delete', 'delete');
                Route::post('{id}', 'update');
            });

        Route::prefix('student-risk-assessment-semesters')
            ->controller(StudentRiskAssessmentSemesterController::class)
            ->group(function () {
                Route::get('', 'index');
                Route::post('', 'store');
                Route::get('{id}', 'show');
                Route::post('delete', 'delete');
                Route::post('{id}', 'update');
            });
    });
