<?php

namespace GalaxyAPI\Services;

use App\Model\v2\AgentCommission;
use App\Model\v2\Courses;
use Carbon\Carbon;
use Illuminate\Support\Facades\DB;

class AgentCommissionService
{
    public function bulkAddCommissions(array $data, int $userId, int $collegeId): array
    {
        $messages = [];
        $courseIds = $data['courses'] ?? [];

        DB::beginTransaction();

        try {
            $courses = $this->getCourses($courseIds, $collegeId);
            $validFrom = $this->parseDate($data['rate_valid_from'] ?? null);
            $validTo = $this->parseDate($data['rate_valid_to'] ?? null);

            $existingCommissions = $this->getExistingCommissions(
                $collegeId,
                $data['agent_id'],
                $courseIds,
                $validFrom,
                $validTo
            );

            $baseData = $this->prepareBaseData($data, $userId, $validFrom, $validTo, $collegeId);
            $results = $this->processCommissions($courses, $existingCommissions, $baseData, $courseIds);

            if (! empty($results['commissionsToCreate'])) {
                AgentCommission::insert($results['commissionsToCreate']);
            }

            DB::commit();

            return $results;

        } catch (\Exception $e) {
            DB::rollback();

            return [
                'success' => 0,
                'message' => 'Error saving agent commissions: '.$e->getMessage(),
            ];
        }
    }

    protected function getCourses(array $courseIds, int $collegeId)
    {
        return Courses::select('id', 'course_code', 'course_name')
            ->whereIn('id', $courseIds)
            ->where('college_id', $collegeId)
            ->get()
            ->keyBy('id');
    }

    protected function parseDate(?string $date): ?string
    {
        return $date ? Carbon::parse($date)->format('Y-m-d') : null;
    }

    protected function getExistingCommissions(int $collegeId, int $agentId, array $courseIds, ?string $validFrom, ?string $validTo)
    {
        return AgentCommission::where([
            'college_id' => $collegeId,
            'agent_id' => $agentId,
        ])
            ->whereIn('course', $courseIds)
            ->when($validFrom, function ($query) use ($validFrom, $validTo) {
                return $query->where(function ($q) use ($validFrom, $validTo) {
                    $q->whereBetween('rate_valid_from', [$validFrom, $validTo])
                        ->orWhereBetween('rate_valid_to', [$validFrom, $validTo])
                        ->orWhere(function ($subQ) use ($validFrom, $validTo) {
                            $subQ->where('rate_valid_from', '<=', $validFrom)
                                ->where('rate_valid_to', '>=', $validTo);
                        });
                });
            })
            ->pluck('course')
            ->toArray();
    }

    protected function prepareBaseData(array $data, int $userId, ?string $validFrom, ?string $validTo, ?int $collegeId): array
    {
        return [
            'college_id' => $collegeId ?? auth()->user()->college_id,
            'agent_status' => $data['agent_status'],
            'agent_id' => $data['agent_id'],
            'course_type' => $data['course_type'],
            'commission_period' => $data['commission_period'],
            'commission' => $data['commission'],
            'gst' => $data['gst'],
            'rate_valid_from' => $validFrom,
            'rate_valid_to' => $validTo,
            'created_by' => $userId,
            'updated_by' => $userId,
            'created_at' => now(),
            'updated_at' => now(),
        ];
    }

    protected function processCommissions($courses, array $existingCommissions, array $baseData, array $courseIds): array
    {
        $commissionsToCreate = [];
        $successCount = 0;
        $errorCount = 0;
        $invalidCourses = [];
        $duplicateCommissions = [];

        foreach ($courseIds as $courseId) {
            $course = $courses->get($courseId);

            if (! $course) {
                $errorCount++;
                $invalidCourses[] = $courseId;

                continue;
            }

            if (in_array($courseId, $existingCommissions)) {
                $errorCount++;
                $duplicateCommissions[] = $course->course_code;

                continue;
            }

            $commissionsToCreate[] = array_merge($baseData, ['course' => $courseId]);
            $successCount++;
        }

        // Build summary message
        $messageParts = [];
        $messageType = 'alert-success';

        if ($successCount > 0) {
            $messageParts[] = "Successfully processed {$successCount} courses.";
        }

        if ($errorCount > 0) {
            $errorDetails = [];

            if (! empty($invalidCourses)) {
                $errorDetails[] = count($invalidCourses).' invalid course IDs';
            }

            if (! empty($duplicateCommissions)) {
                $errorDetails[] = count($duplicateCommissions).' duplicate commissions';
            }

            $messageParts[] = "Failed to process {$errorCount} courses (".implode(', ', $errorDetails).').';

            // Adjust message type based on results
            $messageType = $successCount > 0 ? 'alert-warning' : 'alert-danger';
        }

        return [
            'success' => $errorCount === 0 ? 1 : ($successCount > 0 ? 2 : 0), // 1=all success, 2=partial, 0=all failed
            'commissionsToCreate' => $commissionsToCreate,
            'message' => implode(' ', $messageParts),
            'details' => [
                'success_count' => $successCount,
                'error_count' => $errorCount,
                'invalid_courses' => $invalidCourses,
                'duplicate_commissions' => $duplicateCommissions,
            ],
        ];
    }
}
