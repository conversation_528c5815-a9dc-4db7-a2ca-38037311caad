<?php

namespace Integrations\Base\Models;

use Illuminate\Database\Eloquent\Model;
use Illuminate\Support\Facades\Log;
use Integrations\Base\Flags\SyncStatus;
use Support\Contracts\ArrayableEntity;
use Support\Traits\HasJsonFields;

class IntegrationItem extends Model
{
    use HasJsonFields;

    const SYNCABLE_TYPE_AGENT = 'agent';

    const SYNCABLE_TYPE_STUDENT = 'student';
    // CONST SYNCABLE_TYPE_COURSE  = 'course';
    // CONST SYNCABLE_TYPE_APP_USER= 'app_users';

    protected $table = 'galaxy_tp_integrations_items';

    protected $fillable = [
        'status', 'sync_status', 'created_by', 'updated_by', 'sync_failed_message',
        'sync_failed_at', 'synced_at', 'metadata', 'data', 'sync_item_id', 'syncable_id',
        'syncable_type',  'name',
    ];

    protected $guarded = [];

    protected $casts = [
        'metadata' => 'json',
        'data' => 'json',
        'synced_at' => 'date',
    ];

    public function scopeKey($q, $key)
    {
        return $q->where('name', $key);
    }

    public function syncable()
    {
        return $this->morphTo();
    }

    public function isConnected($key)
    {
        return $this->name == $key && ! is_null($this->sync_item_id);
    }

    public function getSyncId()
    {
        return $this->sync_item_id;
    }

    public function syncDetailsFromProvider(ArrayableEntity $entity)
    {
        $this->updateJsonField([
            'item' => $entity->toArray(),
        ]);
    }

    public function clearTpFailed()
    {

        // $this->updateJsonField([
        //     'last_failed_at' => null,
        //     'last_failed_message' => null
        // ], $this->syncDataField());
        $this->sync_failed_at = null;
        $this->sync_failed_message = null;

        return $this->save();
    }

    public function tpSyncFailed($message)
    {
        // $this->status = SyncStatus::STATUS_FAILED;
        $this->sync_status = SyncStatus::STATUS_FAILED;
        $data = $this->data ?? [];
        if (! $data) {
            $data = [];
        }

        // dd($this->{$this->syncDataField()});

        // if(!isset($data['sync_failed_attempts'])){
        //     $data['sync_failed_attempts'] = 1;
        // }

        if (! isset($data['sync_failed_history'])) {
            $data['sync_failed_history'] = [];
        }
        if (count($data['sync_failed_history']) >= 5) {
            array_shift($data['sync_failed_history']);
        }

        $data['sync_failed_history'][] = [
            'failed_at' => date('Y-m-d H:i:s'),
            'error_message' => $message,
        ];

        $this->sync_failed_at = date('Y-m-d H:i:s');
        $this->sync_failed_message = $message;
        // dd($data);

        $this->updateJsonField($data);
    }

    public function syncTpErrorHandler($message, $record = false)
    {

        // $decoded = @json_decode($e->getMessage(), true);
        // // dd($decoded);
        // if ($decoded) {
        //     $message = Xero::getErrorMessage($decoded);
        // } else {
        //     $message = $e->getMessage();
        // }

        Log::info($this->name.' xero sync failed', [$message]);
        // Log::info($e->getTraceAsString());
        $this->tpSyncFailed($message);

        // safeDD($message);
        // dispatch(new ReportSyncError($message, $record ? $this : null));
        return $message;
    }
}
