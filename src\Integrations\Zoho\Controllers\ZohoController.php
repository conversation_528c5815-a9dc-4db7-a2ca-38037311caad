<?php

namespace Integrations\Zoho\Controllers;

use App\Http\Controllers\LaravelBaseController;
use Illuminate\Support\Facades\Log;
use Integrations\Zoho\Facades\Zoho;

class ZohoController extends LaravelBaseController
{
    public function landing()
    {
        return redirect('zoho.setup');
    }

    public function connect()
    {
        if (! Zoho::config()->isFilled()) {
            abort(404);
        }

        if (! Zoho::isConnected()) {
            try {
                Zoho::applyConfig();

                // dd(config('galaxy.zoho'));
                return Zoho::auth()->connect();
            } catch (\Exception $e) {
                $errorMessage = $this->getErrorMessage($e);
                Log::error('Zoho connection failed', [
                    'error' => $e->getMessage(),
                    'trace' => $e->getTraceAsString(),
                ]);

                return redirect()->route('galaxy.zoho.setup')->with('session_error', $errorMessage);
            }
        } else {
            return redirect()->route('galaxy.zoho.setup');
        }
    }

    private function getErrorMessage(\Exception $e): string
    {
        $message = $e->getMessage();

        // Try to decode JSON error response
        $decoded = @json_decode($message, true);
        if ($decoded) {
            // Use Zoho's error message handler if available
            if (method_exists(Zoho::class, 'errorMessage')) {
                return Zoho::errorMessage($decoded);
            }

            // Handle common OAuth errors
            if (isset($decoded['error'])) {
                switch ($decoded['error']) {
                    case 'invalid_client':
                        return 'Invalid client credentials. Please check your Client ID and Client Secret.';
                    case 'invalid_grant':
                        return 'Invalid authorization grant. Please try connecting again.';
                    case 'access_denied':
                        return 'Access denied. Please check your Zoho permissions.';
                    case 'unauthorized_client':
                        return 'Unauthorized client. Please verify your Zoho application settings.';
                    default:
                        return 'Authentication failed: '.$decoded['error'];
                }
            }

            // Handle other error formats
            if (isset($decoded['error_description'])) {
                return $decoded['error_description'];
            }
        }

        // Handle common error patterns in plain text
        if (stripos($message, 'invalid_client') !== false) {
            return 'Invalid client credentials. Please check your Client ID and Client Secret.';
        }

        if (stripos($message, 'wrong credentials') !== false || stripos($message, 'authentication failed') !== false) {
            return 'Wrong credentials provided. Please verify your Zoho configuration.';
        }

        // Return original message if no specific handling applies
        return 'Connection failed: '.$message;
    }

    public function disconnect()
    {
        if (Zoho::isConnected()) {
            Zoho::applyConfig();

            Zoho::auth()->disconnect();

            Zoho::config()->cleanUp();
        }

        return redirect()->route('galaxy.zoho.setup');
    }
}
