<?php

namespace Integrations\Zoho\Entities;

use App\Model\v2\Agent;
use Integrations\Zoho\Traits\ToZohoArray;
use Support\Traits\ArrayToProps;

class RecruitmentAgent implements ZohoPayload
{
    use ArrayToProps;
    use ToZohoArray;

    public $agent_id;

    public $name;

    public $phone;

    public $email;

    public $website;

    public $status;

    public $state;

    public $person;

    public $agent_code;

    public function key(): string
    {
        return 'agents';
    }

    public static function FromAgent(Agent $agent)
    {
        /* TODO: fill all details here */
        return RecruitmentAgent::LazyFromArray([
            'agent_id' => $agent->id,
            'name' => $agent->agency_name,
            'phone' => $agent->telephone ? $agent->telephone : $agent->mobile1,
            'email' => $agent->primary_email,
            'website' => $agent->website,
            'status' => $agent->status ? 'Active' : 'Inactive',
            'state' => $agent->postal_state,
            'person' => $agent->contact_person,
            'agent_code' => $agent->agent_code,
            // TODO: how to make this dynamic?
            /* Application Received, Onboarding In Process, Active, Terminated, Contract In Progress, Reference Check In Progress, Inactive */

        ]);
    }
}
