<?php

namespace Integrations\Zoho\Jobs;

use App\Exceptions\ApplicationException;
use App\Model\v2\Agent;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Log;
use Integrations\Zoho\Entities\RecruitmentAgent;
use Integrations\Zoho\Entities\ZohoPayload;
use Integrations\Zoho\Facades\Zoho;

class SyncAgentFromZoho implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    public $key = 'agents';

    /**
     * Create a new job instance.
     */
    public function __construct(public $page = 1, public $perPage = 20)
    {
        // Constructor simplified - no need to preload agent emails
    }

    /**
     * Execute the job.
     */
    public function handle(): void
    {
        if (Zoho::isConnected()) {
            Zoho::applyConfig();

            try {
                $config = Zoho::config();
                $mapped = @$config->metadata['mapped_fields'][$this->key] ?? [];
                $moduleName = @$config->metadata['mapped_modules'][$this->key];

                // dd($mapped);
                if (@$mapped['status'] && $moduleName) {

                    $response = Zoho::dynamicModules()
                        ->setResource($moduleName)
                        ->search($mapped['name'].':not_equal:Inactive', $this->page, $this->perPage);

                    //  dd($response);
                    \Log::info('ZOHO FROM DATA: ', [$response['items']]);
                    if (isset($response['items']) && count($response['items'])) {
                        foreach ($response['items'] as $zohoAgent) {
                            $this->saveZohoAgent($this->getPayloadFromZohoData($zohoAgent));
                        }
                    }

                    if (@$response['pagination']['more_records']) {
                        dispatch(new SyncAgentFromZoho($this->page + 1));
                    }

                    return;
                }

                throw new ApplicationException('Status not mapped.');
            } catch (\Exception $e) {
                Log::info('agent search failed', [$e->getMessage()]);
                throw new ApplicationException($e->getMessage());
            }
        }
    }

    public function getPayloadFromZohoData($zohoData): ZohoPayload
    {
        $payload = new RecruitmentAgent;

        $payload = $payload->toPayloadArrayFromZoho($zohoData);

        // Debug logging to identify field mapping issues
        Log::info('Zoho agent data mapping', [
            'zoho_raw_data' => $zohoData,
            'mapped_payload' => [$payload],
        ]);

        return $payload;
    }

    public function saveZohoAgent(ZohoPayload $payload)
    {
        try {
            // Create or update agent from Zoho payload
            $agent = Agent::CreateFromZohoPayload($payload);

            Log::info('Agent synced from Zoho successfully', [
                'agent_id' => $agent->id,
                'email' => $agent->primary_email,
                'name' => $agent->agency_name,
                'agent_code' => $agent->agent_code,
            ]);

            return $agent;
        } catch (\Exception $e) {
            Log::error('Failed to sync agent from Zoho', [
                'error' => $e->getMessage(),
                'payload' => $payload->toArray(),
            ]);

            // Don't throw the exception to prevent the entire job from failing
            // Just log the error and continue with other agents
            return null;
        }
    }
}
