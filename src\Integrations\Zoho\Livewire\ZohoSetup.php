<?php

namespace Integrations\Zoho\Livewire;

use App\Model\v2\Agent;
use App\Model\v2\CollegeMaterials;
use App\Model\v2\Student;
use Illuminate\Support\Facades\Config;
use Integrations\Base\Models\IntegrationConfig;
use Integrations\Base\Models\IntegrationItem;
use Integrations\Zoho\Events\ZohoConnected;
use Integrations\Zoho\Facades\Zoho;
use Integrations\Zoho\Jobs\SyncAgentFromZoho;
use Integrations\Zoho\Jobs\SyncStudentDocumentToZoho;
use Integrations\Zoho\Zoho as ZohoRoot;
use Livewire\Attributes\Computed;
use Livewire\Component;
use Support\Traits\LivewireAlert;

class ZohoSetup extends Component
{
    use LivewireAlert;

    const TEST_STUDENT_ID = 20; // TODO:: set student ID

    public $zoho_connected = false;

    public $editable = false;

    public $uiStates = [
        'moduleFormEditing' => false,
    ];

    public $areaOptions = [
        'us' => 'USA',
        'au' => 'Australia',
        'eu' => 'Europe',
        'in' => 'India',
        'cn' => 'China',
        'jp' => 'Japan',
        'ca' => 'Canada',
    ];

    public $form = [
        // 'client_id' => 'required',
        // 'client_secret' => 'required',
        // 'meta.area' => 'required'
    ];

    public $rules = [
        'config.client_id' => 'required',
        'config.client_secret' => 'required',
        'config.metadata.area' => 'required',
        // 'config.webhook_key' => 'required',
        // 'config.default_account_code' => 'required',
        // 'config.paid_default_account_code' => 'required',
    ];

    public $mappedModules = [];

    public ?IntegrationConfig $config;

    public $key;

    public $zohoModuleOptions = [];

    public function mount()
    {
        // $this->config = $config;
        $this->key = ZohoRoot::KEY;
        $this->zoho_connected = Zoho::isConnected();
        $this->initModel();
        $this->initMappedModules();
        // $this->state['open'] = isset($this->config->active) ? $this->config->active : false;

    }

    public function initModel($fresh = false)
    {
        $this->config = Zoho::config();
        if ($fresh) {
            $this->config = $this->config->fresh();
        }
        if (! $this->config->metadata) {
            $this->config->metadata = [
                'area' => 'au',
            ];
        }

        if (@$this->config->data['modules']) {
            $this->zohoModuleOptions = collect($this->config->data['modules'])
                ->sortBy('plural_label')
                ->mapWithKeys(function ($item) {
                    /* module structure
                  "id" => "6286138000000002173"
  "status" => "visible"
  "api_name" => "Home"
  "editable" => false
  "profiles" => array:2 [▶]
  "viewable" => true
  "web_link" => null
  "arguments" => []
  "creatable" => false
  "deletable" => false
  "lookupable" => false
  "visibility" => 1
  "access_type" => "org_based"
  "convertable" => false
  "description" => null
  "modified_by" => null
  "module_name" => "Home"
  "show_as_tab" => true
  "plural_label" => "Home"
  "quick_create" => false
  "api_supported" => false
  "modified_time" => null
  "parent_module" => []
  "profile_count" => 2
  "feeds_required" => false
  "generated_type" => "default"
  "singular_label" => "Home"
  "private_profile" => null
  "sequence_number" => 1
  "has_more_profiles" => false
  "presence_sub_menu" => false
  "sub_menu_available" => false
  "actual_plural_label" => "Home"
  "isBlueprintSupported" => false
  "actual_singular_label" => "Home"
  "recycle_bin_on_delete" => false
  "global_search_supported" => false
  "public_fields_configured" => false
  "business_card_field_limit" => 5
                */
                    return [$item['api_name'] => $item['plural_label']];
                });
        }

        // dd($this->config->data);
    }

    public function saveConfig()
    {
        $this->config->save();
        activity()
            ->on($this->config)
            ->log('Zoho connection settings updated');
        $this->alert('success', 'Saved');
    }

    public function syncModules()
    {

        Zoho::applyConfig();
        event(new ZohoConnected(false, true));
        $this->initModel(true);
        // Zoho::auth()->renewExpiringToken(Zoho::token());

        $this->alert('success', 'Synced');
    }

    public function syncStudent()
    {
        $student = Student::inRandomOrder()->first();
        if (! $student) {
            return $this->alert('error', 'Not student found');
        }

        try {
            $student->asZohoItem(true);
            $this->alert('success', 'Student Synced');
        } catch (\Exception $e) {
            dd($e);
            $this->alert('error', $e->getMessage());
        }

        // event(new ZohoConnected(true, false));
        // $result = Zoho::users()->getCurrentUser();
        // dd($result);
    }

    public function syncAgent($fromZoho = false)
    {
        if (! $fromZoho) {
            try {
                $item = Agent::inRandomOrder()->first()->asZohoItem(true);
                $this->alert('success', 'Agent Synced');
            } catch (\Exception $e) {
                $this->alert('error', $e->getMessage());
            }
        } else {
            dispatch_sync(new SyncAgentFromZoho);
        }
        // dd($item);

        // event(new ZohoConnected(true, false));

        // $result = Zoho::users()->getCurrentUser();
        // dd($result);
    }

    public function uploadStudentFile()
    {
        // dispatch_sync(new SyncStudentDocumentToZoho(self::TEST_STUDENT_ID));
        // dd(1);

        if (! Zoho::isWorkdriveSetupComplete()) {
            $this->alert('error', 'Workdrive Setup not complete.');

            return;
        }

        $student = Student::has('zohoItem')->inRandomOrder()->first();
        if (! $student) {
            return $this->alert('error', 'No Student found to upload.');
        }

        $key = 'StudentCOE'; // OfferFiles
        $folderName = Config::get('constants.arrCollegeRootFolder')[$key];
        if (! $folderName) {
            return $this->alert('error', 'Student document data not found.');
        }

        /*if($folderName){
            $parentId = CollegeMaterials::where(['folder_or_file' => $folderName, 'parent_id' => 0])->value('id');
            if($parentId){
                $subParentId = CollegeMaterials::where(['folder_or_file' => self::TEST_STUDENT_ID, 'parent_id' => $parentId])->value('id');
                if($subParentId){
                    $collegeMaterials = CollegeMaterials::where('parent_id', $subParentId)->get()->toArray();
                    //dd(implode(',', array_column($collegeMaterials, 'id')));
                }
            }
        }*/

        /*if($folderName){
            $parent = CollegeMaterials::where('folder_or_file', $folderName)
                ->where('parent_id', 0)
                ->with(['children' => function ($query) {
                    $query->where('folder_or_file', self::TEST_STUDENT_ID);
                }])->first();

            if ($parent && $parent->children->isNotEmpty()) {
                // Fetch the files for the sub-parent in a single query
                $subParentId = $parent->children->first()->id;
                $collegeMaterials = CollegeMaterials::where('parent_id', $subParentId * 3)->get();

                if($collegeMaterials->count() == 0){
                    return $this->alert('error', 'Student document data not found.');
                }
            }else{
                return $this->alert('error', 'Student document data not found.');
            }
        }else{
            return $this->alert('error', 'Student document data not found.');
        }*/

        $parent = CollegeMaterials::where('folder_or_file', $folderName)
            ->where('parent_id', 0)
            ->with(['children' => function ($query) {
                $query->where('folder_or_file', self::TEST_STUDENT_ID);
            }])
            ->first();

        if (! $parent || $parent->children->isEmpty()) {
            return $this->alert('error', 'Student document data not found.');
        }

        $subParentId = $parent->children->first()->id;
        $collegeMaterials = CollegeMaterials::where('parent_id', $subParentId)->get();

        if ($collegeMaterials->isEmpty()) {
            return $this->alert('error', 'Student document data not found.');
        }
        // dd($collegeMaterials);

        try {
            $student->createFolderInZoho();
            /* to update the zoho folder link */
            // $item = $student->asZohoItem(true);
            // dd($item);
            // $student->uploadFileToZoho(CollegeMaterials::inRandomOrder()->first());

            $uploadFlag = false;
            $collegeMaterials->each(function ($file) use ($student) {
                $isExist = IntegrationItem::where([
                    'syncable_id' => $file->id,
                    'syncable_type' => CollegeMaterials::class,
                ])->exists();

                if (! $isExist) {
                    $student->uploadFileToZoho($file);
                    $uploadFlag = true;
                }
            });

            if ($uploadFlag) {
                $this->alert('success', 'COE document synced successfully.');
            } else {
                $this->alert('error', 'No new files found for sync.');
            }

        } catch (\Exception $e) {
            $this->alert('error', $e->getMessage());
        }
    }

    public function convertStudent()
    {
        $student = Student::has('zohoItem')->inRandomOrder()->first();
        if (! $student) {
            return $this->alert('error', 'No Student found to convert.');
        }
        $item = $student->asZohoItem();

        // $student->convertToZohoContact();
        // $contactModel = $student->asZohoContact();
        // $response = Zoho::dynamicModules()->get("Leads/{$item->getSyncId()}/__conversion_options");
        // dd($response);
        // dd($contactModel);
        // dd($model);

        /* TODO: need to make sure unique fields match for both contact and leads. */
        /* when converting if we don't send the contact id it creates duplicate id */

        try {
            $item = $student->convertToZohoContact();
            $this->alert('success', 'Future student converted to contact.');
        } catch (\Exception $e) {
            $this->alert('error', $e->getMessage());
        }
    }

    public function initMappedModules()
    {
        $options = collect(config('galaxy.zoho.modules'));

        $mapped = @$this->config->metadata['mapped_modules'] ?? [];

        $this->mappedModules = $options->map(function ($item, $key) use ($mapped) {
            if (isset($mapped[$key])) {
                $item['module'] = $mapped[$key];
            }

            return $item;
        })->toArray();

        // dd($mapped);
    }

    public function saveModuleMap()
    {
        $toSaveMapped = collect($this->mappedModules)->map(function ($item, $key) {
            return $item['module'];
        })->toArray();

        $this->config = $this->config->fresh();
        // dd($toSaveMapped);
        $this->config->updateJsonField(['mapped_modules' => $toSaveMapped], 'metadata');
        // $this->emit('refreshDropdowns');
    }

    public function getIsMappedModuleFilledProperty()
    {
        $collection = collect($this->mappedModules);

        return $collection->count() == $collection->filter(function ($item) {
            return @$item['module'] != '';
        })->count();
    }

    #[Computed()]
    public function docsData()
    {
        return [
            'redirectUrl' => route('galaxy.zoho.connect'),
        ];
    }

    public function render()
    {
        return view(ZohoRoot::VIEW_PREFIX.'::setup.index')
            ->layout('components.v2.layouts.onboard', [
                // 'title' => 'Zoho Setup',
                // 'keywords' => '',
                // 'mainmenu' => 'clients'
            ]);
    }
}
