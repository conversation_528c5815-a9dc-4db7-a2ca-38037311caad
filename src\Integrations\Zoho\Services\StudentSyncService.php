<?php

namespace Integrations\Zoho\Services;

use App\Exceptions\ApplicationException;
use App\Model\v2\Student;
use Exception;
use Illuminate\Support\Facades\Log;
use Integrations\Zoho\Facades\Zoho;
use Integrations\Zoho\Jobs\SyncStudentDocumentToZoho;

class StudentSyncService
{
    /**
     * Complete student sync workflow: Lead → Contact → Documents
     *
     * @throws Exception
     */
    public static function syncStudentComplete(Student $student): array
    {
        $result = [
            'success' => false,
            'steps_completed' => [],
            'errors' => [],
            'lead_id' => null,
            'contact_id' => null,
            'documents_queued' => false,
        ];

        try {
            // Validate Zoho connection
            if (! Zoho::isConnected()) {
                throw new ApplicationException('Zoho is not connected. Please connect to Zoho first.');
            }

            // Step 1: Sync as Lead
            $result = self::syncAsLead($student, $result);

            // Step 2: Convert Lead to Contact
            $result = self::convertToContact($student, $result);

            // Step 3: Queue document sync (TODO::stop temporary)
            // $result = self::queueDocumentSync($student, $result);

            $result['success'] = true;

            Log::info('Student sync completed successfully', [
                'student_id' => $student->id,
                'generated_stud_id' => $student->generated_stud_id,
                'steps_completed' => $result['steps_completed'],
            ]);

        } catch (Exception $e) {
            $result['errors'][] = $e->getMessage();

            Log::error('Student sync failed', [
                'student_id' => $student->id,
                'generated_stud_id' => $student->generated_stud_id,
                'error' => $e->getMessage(),
                'steps_completed' => $result['steps_completed'],
            ]);

            throw $e;
        }

        return $result;
    }

    // Step 1: Sync student as Lead to Zoho
    private static function syncAsLead(Student $student, array $result): array
    {
        try {
            $leadItem = $student->asZohoItem(true);

            if (! $leadItem || ! $leadItem->getSyncId()) {
                throw new Exception('Failed to create or update Lead in Zoho - no sync ID returned');
            }

            $result['lead_id'] = $leadItem->getSyncId();
            $result['steps_completed'][] = 'lead_sync';

            Log::info('Student synced as Lead', [
                'student_id' => $student->id,
                'lead_id' => $leadItem->getSyncId(),
            ]);

        } catch (Exception $e) {
            throw new Exception('Lead sync failed: '.$e->getMessage());
        }

        return $result;
    }

    // Step 2: Convert Lead to Contact
    private static function convertToContact(Student $student, array $result): array
    {
        try {
            $contactItem = $student->convertToZohoContact();

            if (! $contactItem || ! $contactItem->isConvertedToContact()) {
                throw new Exception('Lead to Contact conversion failed - conversion status not confirmed');
            }

            $contactData = $contactItem->getConversionData();
            $result['contact_id'] = $contactData ? $contactData->id : null;
            $result['steps_completed'][] = 'contact_conversion';

            Log::info('Lead converted to Contact', [
                'student_id' => $student->id,
                'lead_id' => $result['lead_id'],
                'contact_id' => $result['contact_id'],
            ]);

        } catch (Exception $e) {
            throw new Exception('Contact conversion failed: '.$e->getMessage());
        }

        return $result;
    }

    // Step 3: Queue document sync
    private static function queueDocumentSync(Student $student, array $result): array
    {
        try {
            // Validate Zoho WorkDrive setup before queuing document sync
            if (self::validateZohoWorkDriveSetup()) {
                dispatch(new SyncStudentDocumentToZoho($student->id));
                $result['documents_queued'] = true;
                $result['steps_completed'][] = 'documents_queued';

                Log::info('Document sync queued', [
                    'student_id' => $student->id,
                    'contact_id' => $result['contact_id'],
                ]);
            } else {
                Log::warning('Document sync skipped - Zoho WorkDrive setup not complete', [
                    'student_id' => $student->id,
                ]);
            }

        } catch (Exception $e) {
            // Don't fail the entire process if document sync fails
            $result['errors'][] = 'Document sync failed: '.$e->getMessage();

            Log::warning('Document sync failed but continuing', [
                'student_id' => $student->id,
                'error' => $e->getMessage(),
            ]);
        }

        return $result;
    }

    // Validate Zoho WorkDrive setup for document sync
    private static function validateZohoWorkDriveSetup(): bool
    {
        try {
            if (! Zoho::isConnected()) {
                return false;
            }

            if (! Zoho::isWorkdriveSetupComplete()) {
                return false;
            }

            if (! Zoho::getRootFolder()) {
                return false;
            }

            return true;
        } catch (Exception $e) {
            Log::warning('Zoho WorkDrive validation failed', ['error' => $e->getMessage()]);

            return false;
        }
    }

    // Get human-readable status message
    public static function getStatusMessage(array $result): string
    {
        if ($result['success']) {
            $message = 'Student successfully synced to Zoho';

            if (in_array('lead_sync', $result['steps_completed'])) {
                $message .= ' as Lead';
            }

            if (in_array('contact_conversion', $result['steps_completed'])) {
                $message .= ', converted to Contact';
            }

            if (in_array('documents_queued', $result['steps_completed'])) {
                $message .= ', and documents queued for sync';
            }

            return $message;
        }

        return 'Failed to sync student: '.implode('; ', $result['errors']);
    }

    // Validate student data before sync
    public static function validateStudentForSync(Student $student): array
    {
        $errors = [];

        if (empty($student->email)) {
            $errors[] = 'Student email is required for Zoho sync';
        }

        if (empty($student->first_name)) {
            $errors[] = 'Student first name is required for Zoho sync';
        }

        if (empty($student->family_name)) {
            $errors[] = 'Student family name is required for Zoho sync';
        }

        return $errors;
    }
}
