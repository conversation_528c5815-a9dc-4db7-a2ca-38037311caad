<?php

namespace Integrations\Zoho\Traits;

use App\Exceptions\ApplicationException;
use Closure;
use Exception;
use Illuminate\Support\Facades\Log;
use Integrations\Base\Flags\SyncStatus;
use Integrations\Base\Models\IntegrationItem;
use Integrations\Zoho\DTO\ZohoResponseItem;
use Integrations\Zoho\Entities\Contact;
use Integrations\Zoho\Entities\ZohoPayload;
use Integrations\Zoho\Facades\Zoho;
use Integrations\Zoho\Models\ZohoItem;

trait ZohoSyncableItem
{
    public function zohoItem()
    {
        // throw new ApplicationException('Error.');
        Zoho::useZohoModel();

        return $this->thirdPartyItem()->key(Zoho::key());
    }

    public function asZohoItem($update = false)
    {
        Zoho::useZohoModel();

        return $this->asThirdPartyItem(Zoho::key(), $update);
    }

    public function connectToZoho(ZohoPayload $payload, ?Closure $callback = null): ?IntegrationItem
    {
        // dd($payload->moduleName());
        // dd($payload->toZohoArray());
        Zoho::applyConfig();
        /* if no zoho connection just skip */
        if (! Zoho::isConnected()) {
            return null;
        }

        activity()
            ->on($this)
            ->log("Syncing {$payload->key()}");
        $model = $this->zohoItem;
        // dd($model);

        try {
            $request = Zoho::dynamicModules()
                ->setResource($payload->moduleName());

            $duplicateFields = $payload->duplicateCheckFields();
            if (count($duplicateFields)) {
                $response = $request->createOrUpdate($payload->toZohoArray(), $payload->duplicateCheckFields());
            } else {
                $response = $request->store($payload->toZohoArray());
            }

            // dd($response);
            Log::info('Zoho Response Log :', [$response]);

            $result = @$response[0];
            // dd($result);
            if (@$result['status'] != 'success') {
                throw new Exception(@$result['message'] ?? 'Zoho sync failed.');
            }
            if (isset($result['details'])) {
                if (! is_null($callback) && is_callable($callback)) {
                    $callback($model, ZohoResponseItem::LazyFromArray($result['details']));
                } else {
                    $this->saveZohoItem($model, ZohoResponseItem::LazyFromArray($result['details']));
                }

                return $model->fresh();
            }
        } catch (\Exception $e) {
            $decoded = @json_decode($e->getMessage(), true);
            /* check for duplicate item response. Throws error by default */
            // dd($decoded);
            if (isset($decoded['data']) && isset($decoded['data'][0])) {
                $response = $decoded['data'][0];
                if (@$response['code'] == 'DUPLICATE_DATA') {
                    $result = @$response['details']['duplicate_record'];
                    if ($result) {
                        if (! is_null($callback) && is_callable($callback)) {
                            $callback($model, ZohoResponseItem::LazyFromArray($result));
                        } else {
                            $this->saveZohoItem($model, ZohoResponseItem::LazyFromArray($result));
                        }

                        return $model->fresh();
                    }
                }
            }
            // dd($e->getMessage());
            // dd($decoded);
            activity()
                ->on($this)
                ->log("Syncing {$payload->key()} item Failed: ".$model->getSyncId());
            $message = $decoded ? Zoho::errorMessage($decoded) : $e->getMessage();
            $model->syncTpErrorHandler($message, true);
            throw new Exception($message);
            // return null;
        }

        return null;
    }

    public function saveZohoItem(IntegrationItem $model, ZohoResponseItem $payload)
    {
        // dd($payload);
        $model->fill([
            'sync_item_id' => $payload->id,
            'sync_status' => SyncStatus::STATUS_SYNCED,
            'synced_at' => now(),
        ]);
        $model->clearTpFailed();

        $model->updateJsonField([
            'item' => $payload->toArray(true),
        ], 'data');

        activity()
            ->on($model)
            ->log("Synced {$model->name} item: ".$model->getSyncId());
    }

    public function syncToZoho(ZohoPayload $payload): ?IntegrationItem
    {
        // dd($payload->moduleName());
        // dd($payload->toZohoArray());
        Zoho::applyConfig();
        /* if no zoho connection just skip */
        if (! Zoho::isConnected()) {
            return null;
        }

        activity()
            ->on($this)
            ->log("Syncing (Update) {$payload->key()}");
        $model = $this->zohoItem;

        // $duplicateFields = $payload->duplicateCheckFields();

        try {
            $response = Zoho::dynamicModules()
                ->setResource($payload->moduleName())
                ->update($model->getSyncId(), $payload->toZohoArray());

            // dd($response);

            $result = @$response[0];
            // dd([$response->status()]);
            if (@$result['status'] != 'success') {
                throw new Exception(@$result['message'] ?? 'Zoho sync failed.');
            }
            if (isset($result['details'])) {

                $this->saveZohoItem($model, ZohoResponseItem::LazyFromArray($result['details']));

                return $model;
            }
        } catch (\Exception $e) {
            // dd($e);
            // dd($e->getMessage());
            $decoded = @json_decode($e->getMessage(), true);
            /* check for duplicate item response. Throws error by default */
            // dd($decoded);
            activity()
                ->on($this)
                ->log("Syncing {$payload->key()} item Failed: ".$model->getSyncId());
            $message = $decoded ? Zoho::errorMessage($decoded) : $e->getMessage();
            $model->syncTpErrorHandler($message, true);
            throw new Exception($message);
            // return null;
        }

        return null;
    }

    public function convertToZohoContact()
    {
        Zoho::applyConfig();
        /* if no zoho connection just skip */
        if (! Zoho::isConnected()) {
            return null;
        }

        $item = $this->asZohoItem();
        if ($item->isConvertedToContact()) {
            // dd([$item->meta, $item->data]);
            return $item;
        }

        $contactId = null;
        $contactResponse = $item->getConversionData();
        // dd($contactResponse);

        // dd(Contact::ForConversion($this)->toZohoArray());
        if (! $contactResponse || ! $contactResponse->id) {
            $this->connectToZoho(Contact::ForConversion($this), function (ZohoItem $model, ZohoResponseItem $item) {
                /* to preserve json structure */
                // dd($item);
                $model->fresh()->saveContactItem($item);
            });
        }

        /* using fresh here to get the fresh json data, above is inside closure */
        $contactResponse = $item->fresh()->getConversionData();
        if (! $contactResponse) {
            throw new ApplicationException('Zoho student was not found.');
        }

        $contactId = $contactResponse->id;
        try {
            $response = Zoho::dynamicModules()
                ->post("Leads/{$item->getSyncId()}/actions/convert", [
                    'data' => [
                        [
                            'overwrite' => true,
                            'Contacts' => ['id' => $contactId],
                        ],
                    ],
                ]);

            $result = @$response['body']['data'][0];
            // dd([$result, $response]);

            if (@$result['status'] != 'success') {
                throw new Exception(@$result['message'] ?? 'Zoho sync failed.');
            }

            if (isset($result['details'])) {
                $item->saveConverted(ZohoResponseItem::LazyFromArray($result['details']));

                return $item;
            }

            Log::info('converted response which failed', [$result]);
            throw new Exception('Conversion Failed.');
        } catch (\Exception $e) {
            dd(json_decode($e->getMessage()));
        }

    }
}
