<?php

return [

    'leads' => [
        'label' => 'Future Students',
        'module' => '',
        'fields' => [
            'student_id' => '',
            'first_name' => '',
            'last_name' => '',
            'course' => '',
            'dob' => '',
            'email' => '',
            'mobile' => '',
            'description' => '',
            'title' => '',
            'gender' => '',
            'usi' => '',
            'passport_number' => '',
            'start_date' => '',
            'finish_date' => '',
            'reference_id' => '',
            'account_name' => '',
            'status' => '',
            'workdrive_link_url' => '',
            'agent_id' => '',
            'agent_email' => '',
            'street' => '',
        ],
        'duplicate_check' => [
            // 'student_id',
            'email',
        ],
    ],
    'agents' => [
        'label' => 'Recruitment Agents',
        'module' => '',
        'fields' => [
            'agent_id' => '',
            'name' => '',
            'agent_code' => '',
            'phone' => '',
            'email' => '',
            'website' => '',
            'status' => '',
            'state' => '',
            'person' => '',
        ],
        'duplicate_check' => [
            'email',
        ],
    ],
    'students' => [
        'label' => 'Current Students',
        'module' => '',
        'fields' => [
            'coe' => '',
            'email' => '',
            // 'current_course' => ''
        ],
        'duplicate_check' => [
            // 'student_id',
            'email',
        ],

    ],
    'courses' => [
        'label' => 'Course',
        'module' => '',
        'fields' => [
            'name' => '',
            'code' => '',
            'cricos_code' => '',
            'fee' => '',
            'description' => '',
        ],
        'duplicate_check' => [
            // 'code'
        ],
    ],
];
