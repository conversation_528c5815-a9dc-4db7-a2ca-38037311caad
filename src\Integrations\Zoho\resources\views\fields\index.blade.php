<div>
    @php
    $stats = $this->mappedFieldsStat;
    @endphp
    <x-button class="btn-primary" x-bind:disabled="!editing" wire:click="mapFields">Map ({{
        $stats['mapped'] }} out of
        {{ $stats['total']
        }})</x-button>


    <x-dialog-modal wire:model.live="uiStates.openFieldMappingModal">
        <x-slot name="title">
            <div class="flex">

                Map Fields for {{ strtoupper($moduleKey) }}
                <x-v2.button type="button" variant="primary" class="ml-auto text-sm" target="syncFields"
                     wire:confirm="Are you sure you want to fetch latest fields from zoho for this module?"
                    wire:click.prevent="syncFields" title="Refresh Fields" loading="Refreshing..">
                    <span class="k-icon k-i-refresh text-sm k-icon-refresh mb-1"></span>
                    Refresh Fields
                </x-v2.button>
            </div>
        </x-slot>

        <x-slot name="content">
            @if ($this->isUniqueMapMissing)
            <x-notification-message class="mb-2" variant="warning">Unique fields map missing.</x-notification-message>
            @endif

            @if (is_array($duplicateFieldsMap))
            {{-- @dd($fieldsMap) --}}

            {{-- <div class="form-group">
                <div class="">
                    <label for="unique_fields">Unique Fields</label>
                    <select id="unique_fields" class="" multiple name="unique_fields"
                        wire:model.lazy="duplicateFieldsMap" placeholder="">
                        @foreach ($fieldsMap as $galaxyField => $zohoField)
                        Only allow mapped field to be choosen as unique field.
                        @if ($zohoField)
                        <option value="{{ $galaxyField }}">{{ slugToTitle($galaxyField) }}</option>
                        @endif
                        @endforeach
                    </select>
                    <p>These fields will be used to check if the field exists before creating.</p>
                </div>
            </div> --}}
            @endif


            <div class="mb-5 mt-5 grid w-full grid-cols-3 items-stretch gap-4 rounded-lg bg-gray-100 p-2">
                <div>
                    <h3 class="text-sm font-medium">Galaxy Field</h3>
                </div>
                <div>
                    <h3 class="pl-4 text-sm font-medium">Zoho Field</h3>
                </div>

            </div>

            <div class="overflow-y-auto space-y-4" style="max-height: 50vh;">
                @foreach ($fieldsMap as $galaxyField => $zohoField)
                @php
                $defaultValue = !empty($fieldOptions[$zohoField]) ? $fieldOptions[$zohoField] : 'None'
                @endphp
                <div class="grid w-full grid-cols-3 items-stretch gap-4 rounded-lg p-2">
                    <div>
                        <h3 class="text-sm font-medium">{{ slugToTitle($galaxyField) }}</h3>
                    </div>
                    <div>
                        {{-- <select wire:model.lazy="fieldsMap.{{ $galaxyField }}">
                            <option value="">None</option>
                            {!! selectOptionsFromArray($fieldOptions) !!}
                        </select> --}}
                        {{-- <x-v2.select2 selectId="fields_{{$moduleKey}}_{{ $galaxyField }}"
                            wire:model.lazy="fieldsMap.{{ $galaxyField }}" :options="$fieldOptions"
                            data-selected="{{ $defaultValue }}" :key="$moduleKey . '_' . $galaxyField"
                            :dataKey="$moduleKey" :insideModal="true">
                        </x-v2.select2> --}}
                        <x-v2.select-alpine wire:model.lazy="fieldsMap.{{ $galaxyField }}" :options="$fieldOptions"
                            data-selected="{{ $defaultValue }}" :key="$moduleKey . '_' . $galaxyField"
                            :dataKey="$moduleKey" :insideModal="true">
                        </x-v2.select-alpine>
                    </div>
                </div>
                @endforeach
            </div>
        </x-slot>

        <x-slot name="footer">
            <x-secondary-button wire:click="toggleModal(false)" wire:loading.attr="disabled">
                {{ __('Close') }}
            </x-secondary-button>

            {{-- <x-button class="ml-2" dusk="confirm-password-button" wire:click="confirmPassword"
                wire:loading.attr="disabled">
                Cancel
            </x-button> --}}
        </x-slot>
    </x-dialog-modal>
</div>