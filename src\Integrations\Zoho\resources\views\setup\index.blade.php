@section('title', 'Zoho Setup')
@section('mainmenu', 'clients')
<div>
    <div class="grid w-full grid-cols-10 flex-row gap-6 p-6">
        <div class="col-span-7 flex w-full">
            <div class="w-full space-y-6">
                {{-- Display session messages --}}
                @if (session('session_error'))
                    <x-notification-message variant="error" canhide="1">
                        <div>
                            <h3 class="w-full flex-1 grow font-bold">Connection Error</h3>
                            <p>{{ session('session_error') }}</p>
                        </div>
                    </x-notification-message>
                @endif

                @if (session('session_success'))
                    <x-notification-message variant="success" canhide="1">
                        <div>
                            <h3 class="w-full flex-1 grow font-bold">Success</h3>
                            <p>{{ session('session_success') }}</p>
                        </div>
                    </x-notification-message>
                @endif

                @if (session('session_alert'))
                    <x-notification-message variant="warning" canhide="1">
                        <div>
                            <h3 class="w-full flex-1 grow font-bold">Warning</h3>
                            <p>{{ session('session_alert') }}</p>
                        </div>
                    </x-notification-message>
                @endif

                <x-notification-message variant="info" canhide="0">
                    <div>
                        <h3 class="w-full flex-1 grow font-bold">Heads Up!!!</h3>
                        <p>
                            Zoho Integration is in <span class="mx-1 font-bold"> Beta </span> release. We are working
                            hard to move to a beta release. If you encounter any bugs please report using the feedback
                            button.
                        </p>
                    </div>
                </x-notification-message>

                @if ($config->isFilled())
                    @include('galaxy-zoho::setup.partials.connect')
                @endif

                @include('galaxy-zoho::setup.partials.config-form')

                @if (@$zoho_connected)
                    <div class="flex flex-col items-start justify-start rounded-lg bg-white p-6 shadow">
                        <div class="flex items-center justify-start space-x-1">
                            <p class="text-base font-medium leading-5 text-gray-900">Zoho Configuration</p>
                            <svg width="16"
                                 height="16"
                                 viewBox="0 0 16 16"
                                 fill="none"
                                 xmlns="http://www.w3.org/2000/svg">
                                <path d="M8 0C12.4183 0 16 3.58172 16 8C16 12.4183 12.4183 16 8 16C3.58172 16 0 12.4183 0 8C0 3.58172 3.58172 0 8 0ZM8 1C4.13401 1 1 4.13401 1 8C1 11.866 4.13401 15 8 15C11.866 15 15 11.866 15 8C15 4.13401 11.866 1 8 1ZM8 11.5C8.41421 11.5 8.75 11.8358 8.75 12.25C8.75 12.6642 8.41421 13 8 13C7.58579 13 7.25 12.6642 7.25 12.25C7.25 11.8358 7.58579 11.5 8 11.5ZM8 3.5C9.38071 3.5 10.5 4.61929 10.5 6C10.5 6.72959 10.1848 7.40774 9.6513 7.8771L9.49667 8.00243L9.27817 8.16553L9.19065 8.23718C9.1348 8.28509 9.08354 8.33373 9.03456 8.38592C8.69627 8.74641 8.5 9.24223 8.5 10C8.5 10.2761 8.27614 10.5 8 10.5C7.72386 10.5 7.5 10.2761 7.5 10C7.5 8.98796 7.79312 8.24747 8.30535 7.70162C8.41649 7.5832 8.53202 7.47988 8.66094 7.37874L8.90761 7.19439L9.02561 7.09468C9.325 6.81435 9.5 6.42206 9.5 6C9.5 5.17157 8.82843 4.5 8 4.5C7.17157 4.5 6.5 5.17157 6.5 6C6.5 6.27614 6.27614 6.5 6 6.5C5.72386 6.5 5.5 6.27614 5.5 6C5.5 4.61929 6.61929 3.5 8 3.5Z"
                                      fill="#9CA3AF" />
                            </svg>
                        </div>
                        @include('galaxy-zoho::setup.partials.modules-form')
                    </div>
                @endif
            </div>
        </div>
        <div class="col-span-3">
            <div class="space-y-6">
                <div class="flex h-40 w-full flex-col items-start justify-start space-y-2 rounded-lg bg-white p-6 shadow">
                    <p class="text-lg font-normal leading-7 text-primary-blue-500">Need Help?</p>
                    <p class="text-sm font-normal leading-5 text-gray-500">
                        View this article to help you setup Zoho CRM connection.
                    </p>
                    <livewire:docs title="Setup Zoho Configuration for Galaxy" path=".docs/zoho.md" :data="$this->docsData"/>
                </div>
                @if (@$zoho_connected)
                    <livewire:galaxy-zoho::workdrive :config="$config" />
                @endif
            </div>
        </div>
    </div>
</div>

<x-slot name="jsFooter">
    <script>
        $(document).ready(function() {
            // Show notification for session messages
            @if (session('session_error'))
                notificationDisplay(
                    '{!! addslashes(session('session_error')) !!}',
                    'Connection Error',
                    'error'
                );
            @endif

            @if (session('session_success'))
                notificationDisplay(
                    '{!! addslashes(session('session_success')) !!}',
                    'Success',
                    'success'
                );
            @endif

            @if (session('session_alert'))
                notificationDisplay(
                    '{!! addslashes(session('session_alert')) !!}',
                    'Warning',
                    'error'
                );
            @endif

            var areaOptions = {!! json_encode($areaOptions) !!};
            var formattedAreaOptions = Object.keys(areaOptions).map(function(key) {
                return {
                    text: areaOptions[key],
                    value: key
                };
            });
            $(".area-dropdown").kendoDropDownList({
                autoWidth: true,
                dataSource: formattedAreaOptions,
                dataTextField: "text",
                dataValueField: "value",
                placeholder: "Select Region",
                valuePrimitive: true,
                filter: "contains"
            });

            var moduleOptions = {!! json_encode($zohoModuleOptions) !!};
            var formattedModuleOptions = Object.keys(moduleOptions).map(function(key) {
                return {
                    text: moduleOptions[key],
                    value: key
                };
            });

            $(".custom-dropdown").kendoDropDownList({
                dataSource: formattedModuleOptions,
                dataTextField: "text",
                dataValueField: "value",
                placeholder: "Select Module",
                valuePrimitive: true,
                filter: "contains"
            });

        });
    </script>
</x-slot>
