<?php

namespace Notifications\Types\Admin;

use Illuminate\Notifications\Messages\MailMessage;
use Notifications\BaseNotification;
use Notifications\Contracts\IsInAppNotification;
use Notifications\Types\DTOs\CourseCancellationDTO;
use Notifications\Types\DTOs\InAppEntity;
use Notifications\Types\DTOs\InAppPayload;

class AdminCourseCancelledNotification extends BaseNotification implements IsInAppNotification
{
    public function __construct(
        public CourseCancellationDTO $dto
    ) {}

    // public function via($notifiable): array
    // {
    //     return ['database'];
    // }

    public function mailMessage(MailMessage $message, InAppPayload $payload, object $notifiable): ?MailMessage
    {
        return $message
            ->subject('Course Cancellation Notification')
            ->line($payload->parseMessage())
            ->line("Reason: {$this->dto->reason}")
            ->line("Cancellation Date: {$this->dto->cancelledAt}")
            ->action(
                'View Details',
                $payload->entities['course']->url ?? url('/')
            );
    }

    public function inAppPayload(): ?InAppPayload
    {
        if (! isset($this->dto->studentCourse) || $this->dto->studentCourse === null) {
            return null;
        }

        return InAppPayload::LazyFromArray([
            'message' => ':student has cancelled :course',
            'entities' => [
                'student' => InAppEntity::FromStudent(
                    $this->dto->studentCourse->student,
                    null,
                    // route('admin.students.show', $this->dto->studentCourse->student_id)
                ),
                // OR
                'user' => InAppEntity::LazyFromArray([
                    'id' => $this->dto->studentCourse->student_id,
                    'secure_id' => $this->dto->studentCourse->student->secure_id ?? $this->dto->studentCourse->student_id,
                    'label' => $this->dto->studentCourse->student->full_name,
                    'url' => '#',
                    'style_class' => null,
                ]),
                'course' => InAppEntity::FromCourse(
                    $this->dto->studentCourse->course,
                    null,
                    // route('admin.courses.show', $this->dto->studentCourse->course_id)
                ),
            ],
        ]);
    }
}
