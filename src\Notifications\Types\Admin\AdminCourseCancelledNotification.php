<?php

namespace Notifications\Types\Admin;

use Illuminate\Notifications\Messages\MailMessage;
use Notifications\BaseNotification;
use Notifications\Contracts\IsInAppNotification;
use Notifications\Types\DTOs\CourseCancellationDTO;
use Notifications\Types\DTOs\InAppEntity;
use Notifications\Types\DTOs\InAppPayload;

class AdminCourseCancelledNotification extends BaseNotification implements IsInAppNotification
{
    public function __construct(
        public CourseCancellationDTO $dto
    ) {}

    public function mailMessage(MailMessage $message, InAppPayload $payload, object $notifiable): ?MailMessage
    {
        return $message
            ->subject('Course Cancellation Notification')
            ->markdown('notification.email.sendNotificationMail', ['message' => $payload->parseMessage(), 'url' => $payload->entities['course']->url ?? url('/')]);
    }

    public function inAppPayload(): ?InAppPayload
    {
        if (! isset($this->dto->studentCourse) || $this->dto->studentCourse === null) {
            return null;
        }

        return InAppPayload::LazyFromArray([
            'message' => 'Course cancellation notification<br>- :student has cancelled :course due to :reason.',
            'entities' => [
                'student' => InAppEntity::FromStudent(
                    $this->dto->studentCourse->student,
                    null,
                    route('student-profile-view', encryptIt($this->dto->studentCourse->student_id))
                ),
                'user' => InAppEntity::LazyFromArray([
                    'id' => $this->dto->studentCourse->student_id,
                    'secure_id' => $this->dto->studentCourse->student->secure_id ?? $this->dto->studentCourse->student_id,
                    'label' => $this->dto->studentCourse->student->full_name,
                    'url' => '#',
                    'style_class' => null,
                ]),
                'course' => InAppEntity::FromCourse(
                    $this->dto->studentCourse->course,
                    null,
                    ''
                ),
            ],
        ]);
    }
}
