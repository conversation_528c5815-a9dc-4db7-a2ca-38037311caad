<?php

namespace Notifications\Types\Admin;

use App\Model\v2\StudentCourses;
use Illuminate\Notifications\Messages\MailMessage;
use Notifications\BaseNotification;
use Notifications\Contracts\IsInAppNotification;
use Notifications\Types\DTOs\InAppEntity;
use Notifications\Types\DTOs\InAppPayload;

class AdminEnrollmentNotification extends BaseNotification implements IsInAppNotification
{
    public function __construct(
        public int $studentCoursesId
    ) {}

    public function inAppPayload(): ?InAppPayload
    {
        $studentCourse = StudentCourses::findOrFail($this->studentCoursesId);

        return InAppPayload::LazyFromArray([
            'message' => 'New enrollment notification<br>- :student has enrolled in :course. <br>- Orientation preparation details: :orientation_details',
            'entities' => [
                'student' => InAppEntity::FromStudent(
                    $studentCourse->student,
                    null,
                    route('student-profile-view', encryptIt($studentCourse->student_id))
                ),
                'course' => InAppEntity::FromCourse(
                    $studentCourse->course,
                    null,
                    ''
                ),
                'orientation_details' => InAppEntity::LazyFromArray([
                    'label' => 'Orientation starts on '.now(),
                    'url' => '',
                    'style_class' => 'text-red-500 font-light',
                ]),
            ],
        ]);
    }

    public function mailMessage(MailMessage $message, InAppPayload $payload, object $notifiable): ?MailMessage
    {
        return $message
            ->subject('Student Enrollment Notification')
            ->markdown('notification.email.course_completion', ['message' => $payload->parseMessage(), 'url' => $payload->entities['course']->url ?? url('/')]);
    }
}
