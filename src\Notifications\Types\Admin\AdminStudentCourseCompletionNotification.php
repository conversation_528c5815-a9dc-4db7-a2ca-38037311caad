<?php

namespace Notifications\Types\Admin;

use App\Model\v2\StudentCourses;
use Illuminate\Notifications\Messages\MailMessage;
use Notifications\BaseNotification;
use Notifications\Contracts\IsInAppNotification;
use Notifications\Types\DTOs\InAppEntity;
use Notifications\Types\DTOs\InAppPayload;

class AdminStudentCourseCompletionNotification extends BaseNotification implements IsInAppNotification
{
    public function __construct(
        public int $studentCoursesId
    ) {}

    public function inAppPayload(): ?InAppPayload
    {
        $studentCourse = StudentCourses::findOrFail($this->studentCoursesId);

        return InAppPayload::LazyFromArray([
            'message' => 'Course completion summary for :student in the :course, including completion rate and graduation status.',
            'entities' => [
                'student' => InAppEntity::FromStudent(
                    $studentCourse->student,
                    null,
                    route('student-profile-view', encryptIt($studentCourse->student_id))
                ),
                'course' => InAppEntity::FromCourse(
                    $studentCourse->course,
                    null,
                    ''
                ),
            ],
        ]);
    }

    public function mailMessage(MailMessage $message, InAppPayload $payload, object $notifiable): ?MailMessage
    {
        return $message
            ->subject('Student Course Completion Notification')
            ->markdown('notification.email.sendNotificationMail', ['message' => $payload->parseMessage(), 'url' => $payload->entities['course']->url ?? url('/')]);
    }
}
