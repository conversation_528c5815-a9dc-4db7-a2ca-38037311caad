<?php

namespace Notifications\Types\DTOs;

use Support\Contracts\ArrayableEntity;
use Support\Traits\ArrayToProps;

class InAppPayload implements ArrayableEntity
{
    use ArrayToProps;

    public string $message;

    public array $entities = [];

    public array $style = [];

    public function isEmpty(): bool
    {
        return empty($this->message) && empty($this->entities);
    }

    public function addEntity(string $key, InAppEntity $entity): self
    {
        $this->entities[$key] = $entity;

        return $this;
    }

    public function setStyle(array $style): self
    {
        $this->style = $style;

        return $this;
    }

    public function parseMessage(?string $notificationUrl = null): string
    {
        $message = $this->message;

        foreach ($this->entities as $key => $entity) {
            if (! ($entity instanceof InAppEntity)) {
                continue;
            }

            $placeholder = ':'.$key;
            $targetUrl = '';
            if (isset($entity->url) && $entity->url !== '') {
                $targetUrl = $notificationUrl ?
                    $notificationUrl.'?redirect_uri='.urlencode($entity->url) :
                    $entity->url;
            }

            if ($targetUrl != '') {
                $replacement = sprintf(
                    '<a href="%s" class="font-medium %s">%s</a>',
                    $targetUrl,
                    $entity->style_class ?? '',
                    $entity->label
                );
            } else {
                $replacement = sprintf(
                    '<span class="font-medium %s">%s</span>',
                    $entity->style_class ?? '',
                    $entity->label
                );
            }

            $message = str_replace($placeholder, $replacement, $message);
        }

        return $message;
    }

    public static function fromDatabaseArray(array $data): self
    {
        $payload = new self;
        $payload->message = $data['message'] ?? '';
        $payload->style = $data['style'] ?? [];

        foreach ($data['entities'] ?? [] as $key => $entityData) {
            $entity = InAppEntity::LazyFromArray($entityData);
            $payload->addEntity($key, $entity);
        }

        return $payload;
    }
}
