<?php

namespace Notifications\Types;

class NotificationStyle
{
    private const BASE_STYLES = [
        'success' => [
            'default_icon' => '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" viewBox="0 0 20 20"><path fill="#fff" fill-rule="evenodd" d="M16.707 5.293c.39.39.39 1.024 0 1.414l-8 8c-.39.39-1.024.39-1.414 0l-4-4c-.39-.39-.39-1.024 0-1.414.39-.39 1.024-.39 1.414 0L8 12.586l7.293-7.293c.39-.39 1.024-.39 1.414 0z" clip-rule="evenodd"></path></svg>',
            'color' => 'green-500',
            'bg_class' => 'bg-green-400',
        ],
        'danger' => [
            'default_icon' => '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" viewBox="0 0 20 20"><path fill="#fff" fill-rule="evenodd" d="M16.707 5.293c.39.39.39 1.024 0 1.414l-8 8c-.39.39-1.024.39-1.414 0l-4-4c-.39-.39-.39-1.024 0-1.414.39-.39 1.024-.39 1.414 0L8 12.586l7.293-7.293c.39-.39 1.024-.39 1.414 0z" clip-rule="evenodd"></path></svg>',
            'color' => 'red-500',
            'bg_class' => 'bg-red-400',
        ],
        'info' => [
            'default_icon' => '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="20" fill="none" viewBox="0 0 20 20"><path fill="#fff" fill-rule="evenodd" d="M16.707 5.293c.39.39.39 1.024 0 1.414l-8 8c-.39.39-1.024.39-1.414 0l-4-4c-.39-.39-.39-1.024 0-1.414.39-.39 1.024-.39 1.414 0L8 12.586l7.293-7.293c.39-.39 1.024-.39 1.414 0z" clip-rule="evenodd"></path></svg>',
            'color' => 'primary-blue-500',
            'bg_class' => 'bg-primary-blue-400',
        ],
        'warning' => [
            'default_icon' => '<svg xmlns="http://www.w3.org/2000/svg" width="20" height="22" viewBox="0 0 20 22" fill="none"><path fill-rule="evenodd" clip-rule="evenodd" d="M8.25803 5.09858C9.02264 3.73928 10.9797 3.73928 11.7443 5.09858L17.3246 15.0191C18.0746 16.3523 17.1111 17.9996 15.5815 17.9996H4.42088C2.89123 17.9996 1.9278 16.3523 2.67773 15.0191L8.25803 5.09858ZM11.0011 14.9998C11.0011 15.552 10.5534 15.9998 10.0011 15.9998C9.44881 15.9998 9.0011 15.552 9.0011 14.9998C9.0011 14.4475 9.44881 13.9998 10.0011 13.9998C10.5534 13.9998 11.0011 14.4475 11.0011 14.9998ZM10.0011 6.99976C9.44881 6.99976 9.0011 7.44747 9.0011 7.99976V10.9998C9.0011 11.552 9.44881 11.9998 10.0011 11.9998C10.5534 11.9998 11.0011 11.552 11.0011 10.9998V7.99976C11.0011 7.44747 10.5534 6.99976 10.0011 6.99976Z" fill="#fff"/></svg>',
            'color' => 'yellow-500',
            'bg_class' => 'bg-yellow-400',
        ],
    ];

    private static function getBaseStyleForType(NotificationType $type): string
    {
        return match ($type) {
            NotificationType::PAYMENT_RECEIVED,
            NotificationType::COURSE_ENROLLED => 'success',
            NotificationType::COURSE_CANCELLED => 'danger',
            NotificationType::STUDENT_WITHDRAWAL,
            NotificationType::STUDENT_IMMIGRATION_REPORTING,
            NotificationType::STUDENT_VISA_GRANT,
            NotificationType::STUDENT_COURSE_COMPLETION,
            NotificationType::ADMIN_COURSE_ENROLLED,
            NotificationType::TASK_ASSIGNED => 'info',
            NotificationType::STUDENT_LOW_ATTENDANCE,
            NotificationType::STUDENT_LOW_COURSE_PROGRESS,
            NotificationType::STUDENT_OVERDUE_FEE => 'warning',
            default => 'info'
        };
    }

    private static function getCustomOverrides(NotificationType $type): ?array
    {
        return match ($type) {
            NotificationType::TASK_ASSIGNED => [
                'icon' => '<i class="far fa-clipboard text-white"></i>',
            ],
            NotificationType::COURSE_CANCELLED => [
                // 'icon' => '<svg width="20" height="20" color="white" xmlns="http://www.w3.org/2000/svg" viewBox="0 0 640 512"><!--!Font Awesome Free 6.7.2 by @fontawesome - https://fontawesome.com License - https://fontawesome.com/license/free Copyright 2025 Fonticons, Inc.--><path d="M608 64H32C14.3 64 0 78.3 0 96v320c0 17.7 14.3 32 32 32h576c17.7 0 32-14.3 32-32V96c0-17.7-14.3-32-32-32zM48 400v-64c35.4 0 64 28.7 64 64H48zm0-224v-64h64c0 35.4-28.7 64-64 64zm272 176c-44.2 0-80-43-80-96 0-53 35.8-96 80-96s80 43 80 96c0 53-35.8 96-80 96zm272 48h-64c0-35.4 28.7-64 64-64v64zm0-224c-35.4 0-64-28.7-64-64h64v64z"/></svg>',
                'color' => 'emerald-500',
            ],
            NotificationType::COURSE_ENROLLED => [
                'icon' => '<svg class="text-white h-6 w-6" width="24" height="24" xmlns="http://www.w3.org/2000/svg" fill="none" viewBox="0 0 24 24" stroke="currentColor" aria-hidden="true">
            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M8 16l2.879-2.879m0 0a3 3 0 104.243-4.242 3 3 0 00-4.243 4.242zM21 12a9 9 0 11-18 0 9 9 0 0118 0z"></path>
              </svg>',
                'bg_class' => 'bg-pink-500',
            ],
            default => null
        };
    }

    public static function forNotificationType(NotificationType $type): array
    {
        $baseStyleKey = self::getBaseStyleForType($type);
        $baseStyle = self::BASE_STYLES[$baseStyleKey];

        $overrides = self::getCustomOverrides($type);
        if ($overrides !== null) {
            foreach ($overrides as $key => $value) {
                $baseStyle[$key] = $value;
            }
        }

        if (! isset($baseStyle['icon'])) {
            $baseStyle['icon'] = $baseStyle['default_icon'];
        }
        unset($baseStyle['default_icon']);

        return $baseStyle;
    }
}
