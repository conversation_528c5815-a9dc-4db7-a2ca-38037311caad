<?php

namespace Notifications\Types;

use Notifications\Types\Admin\AdminCourseCancelledNotification;
use Notifications\Types\Admin\AdminEnrollmentNotification;
use Notifications\Types\Admin\AdminStudentCourseCompletionNotification;
use Notifications\Types\Admin\AdminStudentWithdrawalNotification;
use Notifications\Types\DTOs\CourseCancellationDTO;
use Notifications\Types\DTOs\StudentCourseCompletionNotificationDTO;
use Notifications\Types\DTOs\StudentImmigrationReportingNotificationDTO;
use Notifications\Types\DTOs\StudentLowAttendanceNotificationDTO;
use Notifications\Types\DTOs\StudentLowCourseProgressNotificationDTO;
use Notifications\Types\DTOs\StudentOverdueFeeNotificationDTO;
use Notifications\Types\DTOs\StudentVisaGrantNotificationDTO;
use Notifications\Types\DTOs\StudentWithdrawalNotificationDTO;
use Notifications\Types\Students\SendEnrollmentNotification;
use Notifications\Types\Students\StudentCourseCompletionNotification;
use Notifications\Types\Students\StudentImmigrationReportingNotification;
use Notifications\Types\Students\StudentLowAttendanceNotification;
use Notifications\Types\Students\StudentLowCourseProgressNotification;
use Notifications\Types\Students\StudentOverdueFeeNotification;
use Notifications\Types\Students\StudentVisaGrantNotification;
use Notifications\Types\Students\StudentWithdrawalNotification;

enum NotificationType: string
{
    case DEFAULT = 'default';
    case COURSE_CANCELLED = 'course_cancelled';
    case COURSE_ENROLLED = 'course_enrolled';
    case PAYMENT_RECEIVED = 'payment_received';
    case TASK_ASSIGNED = 'task_assigned';
    case STUDENT_OVERDUE_FEE = 'student_overdue_fee';
    case STUDENT_WITHDRAWAL = 'student_withdrawal';
    case STUDENT_IMMIGRATION_REPORTING = 'student_immigration_reporting';
    case STUDENT_LOW_ATTENDANCE = 'student_low_attendance';
    case STUDENT_LOW_COURSE_PROGRESS = 'student_low_course_progress';
    case STUDENT_VISA_GRANT = 'student_visa_grant';
    case STUDENT_COURSE_COMPLETION = 'student_course_completion';

    // Admin notification
    case ADMIN_COURSE_ENROLLED = 'admin_course_enrolled';
    case ADMIN_STUDENT_WITHDRAWAL = 'admin_student_withdrawal';
    case ADMIN_STUDENT_COURSE_COMPLETION = 'admin_student_course_completion';
    case ADMIN_COURSE_CANCELLED = 'admin_course_cancelled';
    // Add other notification types

    public function getNotificationClass(): string
    {
        return match ($this) {
            self::COURSE_CANCELLED => CourseCancelledNotification::class,
            // self::TASK_ASSIGNED => TaskAssignedNotification::class,
            self::COURSE_ENROLLED => SendEnrollmentNotification::class,
            // self::PAYMENT_RECEIVED => PaymentReceivedNotification::class,
            self::STUDENT_OVERDUE_FEE => StudentOverdueFeeNotification::class,
            self::STUDENT_WITHDRAWAL => StudentWithdrawalNotification::class,
            self::STUDENT_IMMIGRATION_REPORTING => StudentImmigrationReportingNotification::class,
            self::STUDENT_LOW_ATTENDANCE => StudentLowAttendanceNotification::class,
            self::STUDENT_LOW_COURSE_PROGRESS => StudentLowCourseProgressNotification::class,
            self::STUDENT_VISA_GRANT => StudentVisaGrantNotification::class,
            self::STUDENT_COURSE_COMPLETION => StudentCourseCompletionNotification::class,

            // Admin Notification

            self::ADMIN_COURSE_ENROLLED => AdminEnrollmentNotification::class,
            self::ADMIN_STUDENT_WITHDRAWAL => AdminStudentWithdrawalNotification::class,
            self::ADMIN_STUDENT_COURSE_COMPLETION => AdminStudentCourseCompletionNotification::class,
            self::ADMIN_COURSE_CANCELLED => AdminCourseCancelledNotification::class,

            default => throw new \InvalidArgumentException("No notification class mapping found for type: {$this->value}")
        };
    }

    public function getDtoClass(): ?string
    {
        return match ($this) {
            self::COURSE_CANCELLED => CourseCancellationDTO::class,
            // self::COURSE_ENROLLED => CourseEnrollmentDTO::class,
            // self::PAYMENT_RECEIVED => PaymentDTO::class,
            self::STUDENT_OVERDUE_FEE => StudentOverdueFeeNotificationDTO::class,
            self::STUDENT_WITHDRAWAL => StudentWithdrawalNotificationDTO::class,
            self::STUDENT_IMMIGRATION_REPORTING => StudentImmigrationReportingNotificationDTO::class,
            self::STUDENT_LOW_ATTENDANCE => StudentLowAttendanceNotificationDTO::class,
            self::STUDENT_LOW_COURSE_PROGRESS => StudentLowCourseProgressNotificationDTO::class,
            self::STUDENT_VISA_GRANT => StudentVisaGrantNotificationDTO::class,
            self::STUDENT_COURSE_COMPLETION => StudentCourseCompletionNotificationDTO::class,
            self::ADMIN_COURSE_ENROLLED => null, // Admin notifications expect studentCoursesId (int), not DTO
            self::ADMIN_STUDENT_WITHDRAWAL => null, // Admin notifications expect studentCoursesId (int), not DTO
            self::ADMIN_STUDENT_COURSE_COMPLETION => null, // Admin notifications expect studentCoursesId (int), not DTO
            self::ADMIN_COURSE_CANCELLED => null, // Admin notifications expect studentCoursesId (int), not DTO
        };
    }

    public function getStyle(): array
    {
        return NotificationStyle::forNotificationType($this);
    }

    public function getPermissionString(): string
    {
        return 'notification.'.$this->value;
    }

    public static function fromPermissionString(string $permission): ?self
    {
        if (! str_starts_with($permission, 'notification.')) {
            return null;
        }

        $type = str_replace('notification.', '', $permission);

        return self::tryFrom($type);
    }

    public function getDisplayName(): string
    {
        return match ($this) {
            self::COURSE_CANCELLED => 'Course Cancellation',
            self::COURSE_ENROLLED => 'Course Enrollment',
            self::PAYMENT_RECEIVED => 'Payment Received',
            self::TASK_ASSIGNED => 'Task Assignment',
            self::STUDENT_OVERDUE_FEE => 'Overdue Fee',
            self::STUDENT_WITHDRAWAL => 'Student Withdrawal',
            self::STUDENT_IMMIGRATION_REPORTING => 'Immigration Reporting',
            self::STUDENT_LOW_ATTENDANCE => 'Low Attendance',
            self::STUDENT_LOW_COURSE_PROGRESS => 'Low Course Progress',
            self::STUDENT_VISA_GRANT => 'Visa Grant',
            self::STUDENT_COURSE_COMPLETION => 'Course Completion',

            // Admin Notification
            self::ADMIN_COURSE_ENROLLED => 'New Enrollment',
            self::ADMIN_STUDENT_WITHDRAWAL => 'Student Withdrawal',
            self::ADMIN_STUDENT_COURSE_COMPLETION => 'Student Course Completion',
            self::ADMIN_COURSE_CANCELLED => 'Course Cancelled',
        };
    }

    public function getDescription(): string
    {
        return match ($this) {
            self::COURSE_CANCELLED => 'Receive notifications when a course is cancelled',
            self::COURSE_ENROLLED => 'Receive notifications when enrolled in a course',
            self::PAYMENT_RECEIVED => 'Receive notifications for payment confirmations',
            self::TASK_ASSIGNED => 'Receive notifications when tasks are assigned to you',
            self::STUDENT_OVERDUE_FEE => 'Receive notifications for overdue fees',
            self::STUDENT_WITHDRAWAL => 'Receive notifications for student withdrawals',
            self::STUDENT_IMMIGRATION_REPORTING => 'Receive notifications for immigration reporting',
            self::STUDENT_LOW_ATTENDANCE => 'Receive notifications for low attendance',
            self::STUDENT_LOW_COURSE_PROGRESS => 'Receive notifications for low course progress',
            self::STUDENT_VISA_GRANT => 'Receive notifications for visa grant',
            self::STUDENT_COURSE_COMPLETION => 'Receive notifications for course completion',
        };
    }
}
