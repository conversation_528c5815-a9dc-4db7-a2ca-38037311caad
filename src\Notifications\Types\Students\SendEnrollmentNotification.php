<?php

namespace Notifications\Types\Students;

use App\Model\v2\StudentCourses;
use Illuminate\Notifications\Messages\MailMessage;
use Notifications\BaseNotification;
use Notifications\Contracts\IsInAppNotification;
use Notifications\Types\DTOs\InAppEntity;
use Notifications\Types\DTOs\InAppPayload;

class SendEnrollmentNotification extends BaseNotification implements IsInAppNotification
{
    public function __construct(
        public int $studentCoursesId
    ) {}

    public function inAppPayload(): ?InAppPayload
    {
        $studentCourse = StudentCourses::findOrFail($this->studentCoursesId);

        return InAppPayload::LazyFromArray([
            'message' => 'Enrollment confirmation<br/>- :course details<br/>- :orientation_details',
            'entities' => [
                'course' => InAppEntity::FromCourse(
                    $studentCourse->course,
                    null,
                    // route('admin.courses.show', $this->dto->studentCourse->course_id)
                ),
                'orientation_details' => InAppEntity::LazyFromArray([
                    'label' => 'Orientation starts on '.now(),
                    'url' => '',
                    'style_class' => 'text-red-500 font-light',
                ]),
            ],
        ]);
    }

    public function mailMessage(MailMessage $message, InAppPayload $payload, object $notifiable): ?MailMessage
    {
        return $message
            ->subject('Enrollment Confirmation')
            ->markdown('notification.email.sendNotificationMail', ['message' => $payload->parseMessage(), 'url' => $payload->entities['course']->url ?? url('/')]);
    }
}
