<?php

namespace Notifications\Types\Students;

use Illuminate\Notifications\Messages\MailMessage;
use Notifications\BaseNotification;
use Notifications\Contracts\IsInAppNotification;
use Notifications\Types\DTOs\InAppEntity;
use Notifications\Types\DTOs\InAppPayload;
use Notifications\Types\DTOs\StudentCourseCompletionNotificationDTO;

class StudentCourseCompletionNotification extends BaseNotification implements IsInAppNotification
{
    public function __construct(
        public StudentCourseCompletionNotificationDTO $dataDto
    ) {}

    public function inAppPayload(): ?InAppPayload
    {
        return InAppPayload::LazyFromArray([
            'message' => '- Completion confirmation - You have successfully completed the course :course.<br/>- Certificate/qualification issuance - Your certificate/qualification will be issued on :certificateIssueDate.<br/>- Graduation ceremony details - Details of the graduation ceremony are available here :graduationDetailsUrl.<br/>- Further study options - You may explore further study options here :furtherStudyUrl',
            'entities' => [
                'course' => InAppEntity::LazyFromArray([
                    'id' => $this->dataDto->courseId ?? 0,
                    'label' => $this->dataDto->courseName ?? 'Course',
                    'url' => '',
                    'style_class' => 'text-orange-500',
                ]),
                'certificateIssueDate' => InAppEntity::LazyFromArray([
                    'id' => $this->dataDto->courseId ?? 0,
                    'label' => $this->dataDto->certificateIssueDate ?? 'Certificate Issue Date',
                    'url' => '',
                    'style_class' => 'text-orange-500',
                ]),
                'graduationDetailsUrl' => InAppEntity::LazyFromArray([
                    'id' => $this->dataDto->courseId ?? 0,
                    'label' => $this->dataDto->graduationDetailsUrl ?? 'Graduation Details Url',
                    'url' => $this->dataDto->graduationDetailsUrl ?? url('/'),
                    'style_class' => 'text-orange-500',
                ]),
                'furtherStudyUrl' => InAppEntity::LazyFromArray([
                    'id' => $this->dataDto->courseId ?? 0,
                    'label' => $this->dataDto->furtherStudyUrl ?? 'Further Study Url',
                    'url' => $this->dataDto->furtherStudyUrl ?? url('/'),
                    'style_class' => 'text-orange-500',
                ]),
            ],
        ]);
    }

    public function mailMessage(MailMessage $message, InAppPayload $payload, object $notifiable): ?MailMessage
    {
        return $message
            ->subject('Course Completion Confirmation')
            ->markdown('notification.email.sendNotificationMail', ['message' => $payload->parseMessage(), 'url' => $payload->entities['course']->url ?? url('/')]);
    }
}
