<?php

namespace Notifications\Types\Students;

use Illuminate\Notifications\Messages\MailMessage;
use Notifications\BaseNotification;
use Notifications\Contracts\IsInAppNotification;
use Notifications\Types\DTOs\InAppEntity;
use Notifications\Types\DTOs\InAppPayload;
use Notifications\Types\DTOs\StudentWithdrawalNotificationDTO;

class StudentWithdrawalNotification extends BaseNotification implements IsInAppNotification
{
    public function __construct(
        public StudentWithdrawalNotificationDTO $dataDto
    ) {}

    public function mailMessage(MailMessage $message, InAppPayload $payload, object $notifiable): ?MailMessage
    {
        return $message
            ->subject('Student Withdrawal Notification')
            ->markdown('notification.email.sendNotificationMail', ['message' => $payload->parseMessage(), 'url' => $payload->entities['course']->url ?? url('/')]);
    }

    public function inAppPayload(): ?InAppPayload
    {
        return InAppPayload::LazyFromArray([
            'message' => '- Withdrawal confirmation - You have withdrawn from the course :course on :withdrawnAt.<br/>- Refund information - :amount will be refunded.<br/>- Re-enrollment options - You may re-enroll here :enrolmentUrl',
            'entities' => [
                'course' => InAppEntity::LazyFromArray([
                    'id' => $this->dataDto->courseId ?? 0,
                    'label' => $this->dataDto->courseName ?? 'Course',
                    'url' => '',
                    'style_class' => 'text-orange-500',
                ]),
                'withdrawnAt' => InAppEntity::LazyFromArray([
                    'id' => $this->dataDto->courseId ?? 0,
                    'label' => $this->dataDto->withdrawnAt ?? 'Withdrawn At',
                    'url' => '',
                    'style_class' => 'text-orange-500',
                ]),
                'amount' => InAppEntity::LazyFromArray([
                    'id' => $this->dataDto->courseId ?? 0,
                    'label' => '$'.number_format($this->dataDto->amount ?? 0, 2),
                    'url' => '',
                    'style_class' => 'text-orange-500',
                ]),
                'enrolmentUrl' => InAppEntity::LazyFromArray([
                    'id' => $this->dataDto->courseId ?? 0,
                    'label' => $this->dataDto->enrolmentUrl ?? 'Enrolment Url',
                    'url' => $this->dataDto->enrolmentUrl ?? url('/'),
                    'style_class' => 'text-orange-500',
                ]),
            ],
        ]);
    }
}
