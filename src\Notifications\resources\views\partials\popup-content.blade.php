@php
    $groupedNotifications = $this->notifications->groupBy(function ($notification) {
            return Carbon::parse($notification->created_at)->format('d M Y');
        });
@endphp
<div class="z-30 w-full overflow-hidden rounded-lg bg-white">
    @if ($this->notifications->isEmpty())
        <div class="flex items-center justify-center py-8 text-gray-500">
            <div class="text-center">
                {{-- <svg class="mx-auto h-12 w-12 text-gray-400"
                     fill="none"
                     stroke="currentColor"
                     viewBox="0 0 24 24"
                     xmlns="http://www.w3.org/2000/svg">
                    <path stroke-linecap="round"
                          stroke-linejoin="round"
                          stroke-width="2"
                          d="M15 17h5l-1.405-1.405A2.032 2.032 0 0118 14.158V11a6.002 6.002 0 00-4-5.659V5a2 2 0 10-4 0v.341C7.67 6.165 6 8.388 6 11v3.159c0 .538-.214 1.055-.595 1.436L4 17h5m6 0v1a3 3 0 11-6 0v-1m6 0H9">
                    </path>
                </svg> --}}

                <i class="fa fa-bell-o fa-3x text-gray-400"></i>
                <p class="mt-4 text-sm">No new notifications</p>
            </div>
        </div>
    @else
        @foreach ($groupedNotifications as $date => $notifications)
            <p class="py-2 px-4 text-sm text-gray-500">
                {{ $date }}
            </p>
            <div class="flow-root">
                <ul>
                    @foreach ($notifications as $notification)
                        @php
                            $style = $notification->data['style'] ?? [];
                            $bgClass = is_null($notification->read_at) ? 'bg-primary-blue-50' : 'bg-white';
                            $iconBgColor = $style['bg_class'] ?? 'bg-gray-500';
                            $icon = $style['icon'] ?? '<i class="far fa-bell text-white"></i>';
                        @endphp
                        <li wire:key="notification-{{ $notification->id }}" 
                            class="{{ $bgClass }}">
                            <div class="relative border-b p-4">
                                <div class="relative flex items-start gap-3">
                                    <div class="flex h-6 w-6 items-center justify-center rounded-full {{ $iconBgColor }}">
                                        {!! $icon !!}
                                    </div>
                                    <div class="min-w-0 flex-1 space-y-2">
                                        <div class="text-sm {{ is_null($notification->read_at) ? '' : 'text-gray-800' }}">
                                            {!! $notification->parsed_message ?? '' !!}
                                        </div>
                                        <div class="flex items-center gap-4 text-xs text-gray-500">
                                            <p>
                                                {{ $notification->created_at->diffForHumans() }}
                                            </p>
                                            @if(is_null($notification->read_at))
                                                <button wire:click.stop="markAsRead('{{ $notification->id }}')" 
                                                        @click.stop    
                                                        class="tw-btn-link capitalize">
                                                    Mark as read
                                                </button>
                                            @endif
                                        </div>
                                    </div>
                                </div>
                            </div>
                        </li>
                    @endforeach
                </ul>
            </div>
        @endforeach
    @endif
</div>
