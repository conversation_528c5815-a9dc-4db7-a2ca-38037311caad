<div x-data="{ open: false }" class="relative inline-block">
    <button
        class="tw-dropdown-trigger glob-tooltip updateNotificationCount rounded-full p-1  relative {{ $layout === 'transparent' ? 'text-gray-700 hover:!bg-primary-blue-100 hover:!text-gray-700' : 'text-white' }}"
        data-id="1" data-count="0" title="Notifications" data-role="tooltip" @click="open = !open"
        @click.away="open = false">
        <span class="sr-only">View notifications</span>
        <svg xmlns="http://www.w3.org/2000/svg" width="18" height="20" viewBox="0 0 18 20">
            <path
                d="M8.99999 -0.00366211C13.0499 -0.00366211 16.3567 3.19108 16.4958 7.24539L16.5 7.49634V11.5933L17.88 14.7493C17.949 14.9072 17.9847 15.0777 17.9847 15.2501C17.9847 15.9405 17.425 16.5001 16.7347 16.5001L12 16.5016C12 18.1585 10.6568 19.5016 8.99999 19.5016C7.40231 19.5016 6.09633 18.2527 6.00508 16.6779L5.99954 16.4993L1.27485 16.5001C1.10351 16.5001 0.934005 16.4649 0.776852 16.3966C0.14365 16.1216 -0.146699 15.3853 0.12834 14.7521L1.49999 11.5943V7.49623C1.50059 3.34144 4.85208 -0.00366211 8.99999 -0.00366211ZM10.4995 16.4993L7.49999 16.5016C7.49999 17.33 8.17156 18.0016 8.99999 18.0016C9.77969 18.0016 10.4204 17.4067 10.4931 16.6461L10.4995 16.4993ZM8.99999 1.49634C5.67983 1.49634 3.00047 4.17059 2.99999 7.49634V11.906L1.65601 15.0001H16.3525L15 11.9069L15.0001 7.50919L14.9964 7.28399C14.8853 4.05052 12.2416 1.49634 8.99999 1.49634Z"
                fill="currentColor" />
        </svg>
        @if($this->unreadCount > 0)
        <span
            class="absolute -top-1 -right-1 flex h-5 w-5 items-center justify-center rounded-full bg-red-500 text-xs text-white">
            {{ $this->unreadCount }}
        </span>
        @endif
    </button>

    <div x-show="open" x-transition:enter="transition ease-out duration-100"
        x-transition:enter-start="transform opacity-0 scale-95" x-transition:enter-end="transform opacity-100 scale-100"
        x-transition:leave="transition ease-in duration-75" x-transition:leave-start="transform opacity-100 scale-100"
        x-transition:leave-end="transform opacity-0 scale-95"
        class="absolute right-0 top-full z-[9999] mt-2 origin-top-right rounded-md bg-white shadow-lg ring-1 ring-black ring-opacity-5 focus:outline-none w-[520px] max-md:w-52 max-h-[60vh] flex flex-col"
        style="display: none; z-index:1010;" @click.away="open = false">
        <div class="flex items-center justify-between border-b px-4 py-2">
            <h3 class="text-lg font-medium">Notifications</h3>
{{--            <button wire:click="markAllAsRead" class="tw-btn-link capitalize">Mark all as read</button>--}}
        </div>
        <div class=" overflow-y-auto flex-1">
            @include('galaxy-notifications::partials.popup-content')
        </div>
        <div class="p-2 text-center hover:bg-gray-50">
           <a href="{{ route('spa.notifications.index') }}" type="button" class="tw-btn-link w-full rounded transition justify-center duration-150 ease-in-out">View All Notifications</a>
        </div>

    </div>
</div>