<?php

namespace SSO\Controllers;

use App\Http\Controllers\LaravelBaseController;
use Exception;
use Illuminate\Foundation\Validation\ValidatesRequests;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\DB;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use SSO\DTO\KeycloakUserInfo;
use SSO\DTO\OauthCallbackPayload;
use SSO\DTO\OauthRegisterCallbackPayload;
use SSO\Facades\SSO;
use SSO\Middlewares\SingleSignOnEnabler;
use SSO\Processes\AuthenticationProcess;
use SSO\Processes\RegistrationProcess;

class AuthorizationController extends LaravelBaseController
{
    use ValidatesRequests;

    public function __construct()
    {
        $this->middleware(SingleSignOnEnabler::class, [
            'only' => ['redirect'],
        ]);
    }

    public function redirect(Request $request)
    {

        try {

            $hint = '';
            if ($request->idp) {
                $idp = config('galaxysso.config.idps.'.$request->idp);
                if ($idp && @$idp['enabled'] && @$idp['installable'] == true) {
                    /* WE ADD HINT IN THE MIDDLEWARE which is not defined in config by default */
                    $hint = $idp['kc_idp_hint'];
                    $request->session()->put('sso_idp', $request->idp);
                }

                /* no keycloak hint but this is verification check request than set the hint after validating */

                if ($idp && $request->verification == 1) {
                    /* make sure the verification request hint and current hint are the same otherwise remove the session so that
                    login through one idp will not verify another one */
                    $sessionValue = session()->get(KeycloakUserInfo::SSO_CONNECTED_KEY);
                    if (@$sessionValue && @$sessionValue['hint']) {
                        $hint = $sessionValue['hint'];
                    }
                }
            }

            if (! $request->verification) {
                session()->forget(KeycloakUserInfo::SSO_CONNECTED_KEY);
            }

            // dd(session()->all());

            // dd($hint);
            SSO::updateSSORedirectUrl();

            $request->session()->put('state', $state = Str::random(40));

            $params = [
                'client_id' => config(SSO::prefix().'.config.client_id'),
                'redirect_uri' => config(SSO::prefix().'.config.redirect_url'),
                'response_type' => 'code',
                'scope' => config(SSO::prefix().'.config.scopes'),
                'state' => $state,
                // 'login_hint' => url('/login')
                // 'prompt' => '', // "none", "consent", or "login"
            ];

            if ($hint) {
                $params['kc_idp_hint'] = $hint;
            } else {
                $params['prompt'] = 'login';
            }

            // dd($params);

            info('redirecting to keycloak', [SSO::authorizeUrl($params)]);

            return redirect(SSO::authorizeUrl($params));

        } catch (\Exception $e) {
            // dd($e);
            info('sso redirect error', [$e->getMessage(), $e->getTraceAsString()]);
            abort(404);
        }

    }

    public function callback(Request $request, AuthenticationProcess $authProcess, RegistrationProcess $registrationProcess)
    {
        SSO::updateSSORedirectUrl();
        // dd($request->session()->all());
        try {
            if ($request->session()->get('integration_register')) {
                return $this->handleRegister($request, $registrationProcess);
            } else {
                return $authProcess->run(new OauthCallbackPayload($request));
            }
        } catch (\Exception $e) {
            // abort(404);
            // dd($e);
            info('sso callback error', [$e->getMessage(), $e->getTraceAsString()]);

            // dd($e->getMessage());
            // safeDD($e->getMessage());
            if (! $request->session()->get('integration_register')) {
                // return redirect('/login')->with([''])
                // throw ValidationException::withMessages([
                //     'email' => $e->getMessage()
                // ]);

                /* NOTE CRITICAL
                Throwing validation exception here will redirect back to keycloak page and
                we will get infinite loop. */
                return redirect()->route('login')->withErrors([
                    'email' => $e->getMessage(),
                ]);
            }
            abort(404);
        }
    }

    public function handleRegister(Request $request, RegistrationProcess $registrationProcess)
    {
        // DB::beginTransaction();
        try {
            $response = $registrationProcess->run(new OauthRegisterCallbackPayload($request));

            // @DB::commit();
            return $response;
        } catch (\Exception $e) {
            // DB::rollBack();
            throw new Exception($e->getMessage());
        }
    }
}
