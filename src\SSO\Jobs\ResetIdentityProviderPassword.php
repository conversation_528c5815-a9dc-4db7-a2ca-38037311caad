<?php

namespace SSO\Jobs;

use App\Users;
use Exception;
use Illuminate\Bus\Queueable;
use Illuminate\Contracts\Queue\ShouldQueue;
use Illuminate\Foundation\Bus\Dispatchable;
use Illuminate\Queue\InteractsWithQueue;
use Illuminate\Queue\SerializesModels;
use Illuminate\Support\Facades\Http;
use SSO\Facades\SSO;

class ResetIdentityProviderPassword implements ShouldQueue
{
    use Dispatchable, InteractsWithQueue, Queueable, SerializesModels;

    /**
     * Create the event listener.
     */
    public function __construct(public $userId, public $password, public $temporary = true)
    {
        //
    }

    /**
     * Handle the event.
     */
    public function handle()
    {
        $user = Users::findOrFail($this->userId);
        $ssoUserId = $user->getSSSOId();
        if (! $ssoUserId) {
            return;
        }

        info('keycloak reset password request', [
            'url' => SSO::restApiUrl('users/'.$ssoUserId.'/reset-password'),
            'payload' => [
                'type' => 'password',
                'temporary' => $this->temporary,
                'value' => $this->password,
            ],
        ]);

        /* "enabled":"true","credentials":[{"type":"password","value":"test123","temporary":false}] */
        $response = Http::withHeaders([
            'Authorization' => 'Bearer '.SSO::getAdminToken(),
        ])
            ->acceptJson()
            ->put(SSO::restApiUrl('users/'.$ssoUserId.'/reset-password'), [
                'type' => 'password',
                'temporary' => $this->temporary,
                'value' => $this->password,
            ]);

        info('password reset response', [$response->json()]);

        if ($response->status() > 210 || $response->status() < 200) {
            info('password reset failed on keycloak', [$user->id, $response->json(), $this->password]);
            // throw new Exception(@$response->json()['errorMessage']);
        }
    }
}
