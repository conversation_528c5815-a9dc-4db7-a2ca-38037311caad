<?php

namespace SSO\Livewire;

use App\Model\v2\Student;
use App\Users;
use Exception;
use Illuminate\Notifications\Messages\MailMessage;
use Illuminate\Support\Str;
use Illuminate\Validation\ValidationException;
use Livewire\Attributes\Layout;
use Livewire\Component;
use SSO\DTO\IntegrationConfig;
use SSO\DTO\MetaKey;
use SSO\Facades\SSO;
use SSO\Models\Integration as IntegrationModel;
use Support\DTO\MailAddress;
use Support\Mails\EmailService;
use Support\Traits\LivewireAlert;
use Zoha\Meta\Models\Meta;

class VerifyInvitation extends Component
{
    use LivewireAlert;

    public ?IntegrationModel $model;

    public IntegrationConfig $config;

    public $state = [
        'open' => false,
        'viewState' => 'verify',
        'canSendCode' => true,
        'verificationSent' => false,
    ];

    public $form = [
        'code' => '',
        'password' => '',
        'email' => '',
    ];

    public $key;

    public $taskService;

    public $attempts = 3;

    public $ssoEmail;

    public $ssoId;

    public function mount()
    {
        try {
            $this->init();
        } catch (\Exception $e) {
            // dd($e);
            info('verification failed', [$e->getMessage(), $e->getTraceAsString()]);
            abort(404);
        }
    }

    public function init()
    {

        // $this->ssoEmail = '<EMAIL>';

        $token = session()->get(config('galaxysso.config.access_token_session_key'));
        if (! $token || ! isset($token['id_token'])) {
            throw new Exception('Invalid Token');
        }
        $decoded = SSO::decodeJwtToken($token['id_token']);

        info('decoded token', [$decoded]);

        if (! isset($decoded->tenants)) {
            throw new Exception('Cannot find tenant context.');
        }

        if (collect($decoded->tenants)->map(fn ($item) => ltrim($item, '/'))->filter(fn ($item) => $item == tenant('id'))->count() == 0) {
            throw new Exception('Unauthorized organization. Your account does not belong to current workspace');
        }
        // $this->ssoEmail = session()->get('sso_login_email');
        $this->ssoEmail = strtolower($decoded->preferred_username);
        $this->ssoId = $decoded->sub;
        // dd(SSO::getAccessTokenFromSession());
    }

    // public function sendVerificationCode()
    // {
    //     $student = Student::whereDoesntHave('associatedUserAccount')->inRandomOrder()->first();

    //     $code = strtoupper(Str::random(8));
    //     $student->setMeta(MetaKey::SSO_INVITATION_CODE, $code);

    //     dd([
    //         'student' => $student->email,
    //         'code' => $code,
    //     ]);
    // }

    public function verify()
    {

        $this->state['verificationSent'] = false;

        if ($this->attempts <= 0) {
            session()->forget(config('galaxysso.config.access_token_session_key'));

            return redirect()->route('login');
        }

        try {

            $this->validate([
                'form.code' => 'required',
            ], [
                'form.code' => 'Invitation Code is required',
            ]);
            $meta = Meta::where([
                'key' => MetaKey::SSO_INVITATION_CODE,
                'value' => strtoupper($this->form['code']),
            ])->first();

            if (! $meta) {
                throw ValidationException::withMessages([
                    'form.code' => 'Your code is invalid. Please contact support',
                ]);
            }

            $owner = $meta->owner;
            // dd($owner);

            switch (get_class($owner)) {
                case Student::class:
                    return $this->linkStudent($owner);
                case Users::class:
                    return $this->linkUser($owner);
            }
        } catch (\Exception $e) {
            $this->attempts--;
            if ($this->attempts <= 0) {
                session()->forget(config('galaxysso.config.access_token_session_key'));

                return redirect()->route('login');
            }
            // throw $e;
            info('verification failed', [$e->getMessage(), $e->getTraceAsString()]);
            $this->alert('error', 'Verification failed. Please try again.');
        }
    }

    public function linkStudent(Student $student)
    {

        $user = SSO::linkStudent($student, $this->ssoEmail);
        $user->saveSSOData([
            'sso_id' => $this->ssoId,
        ]);

        auth()->loginUsingId($user->id);

        $student->deleteMeta(MetaKey::SSO_INVITATION_CODE);

        session()->put('currentRole', $user->role_id);

        // session()->put('student.first_time_reset_pwd', $user->first_time_reset_pwd);
        if (galaxy_feature('student_portal_beta')) {
            $route = (route('spa.studentportal.dashboard'));
        } else {
            $route = (route('student_dashboard'));
        }

        return redirect($route);
    }

    public function sendInvitationCode()
    {
        $this->state['verificationSent'] = false;
        $this->validate([
            'form.email' => 'required|email',
        ], [
            'form.email' => 'Email is required',
        ]);

        $student = Student::where('email', $this->form['email'])
            ->orWhere('optional_email', $this->form['email'])
            ->first();

        if (! $student) {
            throw ValidationException::withMessages([
                'form.email' => 'Student not found',
            ]);
        }

        if (session()->has('sso_invitation_code_'.$student->id)) {
            throw ValidationException::withMessages([
                'form.email' => 'You have already requested an invitation code. Please check your email for the code. Or wait for 24 hours to request again.',
            ]);
        }

        $code = strtoupper(Str::random(8));
        $student->setMeta(MetaKey::SSO_INVITATION_CODE, $code);

        (new EmailService)
            ->subject('Invitation Code')
            ->to(new MailAddress($student->email, $student->full_name))
            ->sendUsingMailMessage(function (MailMessage $message) use ($code, $student) {

                $message
                    ->greeting('Hello '.$student->full_name)
                    ->line('Your invitation code is:')
                    ->line('**'.$code.'**')
                    ->line('Please use this code to login to Galaxy.');
            });

        session()->put('sso_invitation_code_'.$student->id, 1);

        // clear form
        $this->form['email'] = '';

        // move to verify state and show success message
        $this->state['viewState'] = 'verify';
        $this->state['verificationSent'] = true;
    }

    #[Layout('components.v2.layouts.auth')]
    public function render()
    {
        return view(SSO::prefix().'::auth.verify-invitation');
    }
}
