<?php

namespace SSO\Services\IDP;

use App\Exceptions\ApplicationException;
use Support\Collections\FilterCollection;
use Support\DTO\FilterOption;

class Microsoft extends BaseIDP
{
    const IDP = 'microsoft';

    public function form($values = [])
    {
        return (new FilterCollection([
            FilterOption::LazyFromArray([
                'label' => 'Tenant ID',
                'name' => 'tenant_id',
            ])->setAttribute('required', 1),
            FilterOption::LazyFromArray([
                'label' => 'Button Label',
                'name' => 'button_label',
                'placeholder' => 'Login with College Credentials',
                'hint' => 'This is the label of the button that will be shown on the login page.',
            ]),
        ]))->update($values, 'value');
    }

    public function key()
    {
        return 'microsoft';
    }

    public function metaKey()
    {
        return 'idp_config_'.$this->key();
    }

    public function payload($values = [])
    {
        $formValue = $this->form($values)->toValue();

        if (@$formValue['tenant_id'] == '') {
            throw new ApplicationException('Setup not complete.');
        }

        $name = $this->key();
        $tenant = tenant('id');

        return [
            'identityProviders' => [
                [
                    'alias' => $name.'-'.$tenant,
                    'providerId' => 'microsoft',
                    'enabled' => true,
                    'updateProfileFirstLoginMode' => 'on',
                    'trustEmail' => false,
                    'storeToken' => false,
                    'addReadTokenRoleOnCreate' => false,
                    'authenticateByDefault' => false,
                    'linkOnly' => false,
                    'config' => [
                        'hideOnLoginPage' => true,
                        'clientId' => $this->config('key'),
                        'acceptsPromptNoneForwardFromClient' => false,
                        'disableUserInfo' => false,
                        'tenantId' => @$formValue['tenant_id'],
                        'filteredByClaim' => false,
                        'syncMode' => 'IMPORT',
                        'clientSecret' => $this->config('secret'),
                        'guiOrder' => '1',
                        'prompt' => 'login',
                    ],
                ],
            ],
            'identityProviderMappers' => [
                [
                    'name' => $name.'-'.$tenant.'-mapper',
                    'identityProviderAlias' => $name.'-'.$tenant,
                    'identityProviderMapper' => 'oidc-hardcoded-group-idp-mapper',
                    'config' => [
                        'syncMode' => 'INHERIT',
                        'group' => '/'.$tenant,
                    ],
                ],
            ],
        ];
    }
}
