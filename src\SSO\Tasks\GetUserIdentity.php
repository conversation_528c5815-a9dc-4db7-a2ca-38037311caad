<?php

namespace SSO\Tasks;

use App\Model\v2\Student;
use Closure;
use Exception;
use Illuminate\Foundation\Auth\User;
use Illuminate\Http\Request;
use Illuminate\Support\Facades\Http;
use Illuminate\Support\Facades\Log;
use SSO\Actions\VerifyInvitationFormResponse;
use SSO\DTO\KeycloakUserInfo;
use SSO\DTO\OauthCallbackPayload;
use SSO\DTO\Token;
use SSO\Facades\SSO;
use Support\DTO\BasePayload;

class GetUserIdentity extends BasePayload
{
    public function __invoke(OauthCallbackPayload $payload, Closure $next)
    {

        $response = Http::withHeaders([
            'Authorization' => 'Bearer '.$payload->token->access_token,
        ])->get(SSO::userInfoUrl());

        // dd($response->body());
        $result = $response->json();
        Log::info('user', [$result]);
        if (! isset($result['preferred_username'])) {
            /* email_vefiried logic here */
            Log::info('User authentication failed', [$result]);
            throw new Exception('User authentication failed .');
        }

        // dd($result);
        $keyCloakUser = KeycloakUserInfo::LazyFromArray($result);

        $user = app(config(SSO::prefix().'.config.auth_model'))->whereRaw('LOWER('.config(SSO::prefix().'.config.auth_username').') = ?', [strtolower($keyCloakUser->preferred_username)])->first();
        if (! $user) {
            Log::info('User authentication failed', [$result]);

            $idp = $payload->request->session()->get('sso_idp', 'microsoft');
            // dd($idp);
            if (@$idp) {
                $user = $this->findUserFromPattern($result['preferred_username'], $idp);
            }
            if (! $user) {
                return app(VerifyInvitationFormResponse::class)->handle($payload->request, $payload->token);
            }

            // throw new Exception("User authentication failed");
        }

        $this->validateToken($user, $keyCloakUser);
        /* if token is validated, set the payload user */
        $payload->setUser($user);
        /* if token is validated, set the sso id for next task */
        $payload->setId($keyCloakUser->sub);

        return $next($payload);
    }

    public function findUserFromPattern($email, $idp)
    {
        $patterns = tenant()->getMeta($idp.'_student_email_pattern', []);

        if (count($patterns)) {
            $studentInfo = SSO::extractStudentInfoFromEmail($email, $patterns);
            $studentId = @$studentInfo['STUDENT_ID'] ?? null;
            if ($studentId) {
                $student = Student::whereRaw('LOWER(generated_stud_id) = ?', [strtolower($studentId)])->first();
                if ($student) {
                    return SSO::linkStudent($student, $email);
                }
            }
        }

        return null;
    }

    public function validateToken(User $user, KeycloakUserInfo $decoded)
    {
        try {
            // $decoded = SSO::decodeJwtToken($token->id_token);
            // dd($decoded, request()->all());

            // info('decoded', [$decoded->preferred_username, $user->{config(SSO::prefix() . '.config.auth_username')}]);

            // if (strtolower($decoded->preferred_username) != strtolower($user->{config(SSO::prefix() . '.config.auth_username')})) {
            //     // throw new Exception("ID Token mismatch.");
            //     throw new Exception('Username doesnot match our records.');
            // }

            if (! isset($decoded->tenants)) {
                throw new Exception('Cannot find tenant context.');
            }

            if (collect($decoded->tenants)->map(fn ($item) => ltrim($item, '/'))->filter(fn ($item) => $item == tenant('id'))->count() == 0) {
                throw new Exception('Unauthorized organization. Your account does not belong to current workspace');
            }

            return @$decoded->sub;
        } catch (Exception $e) {
            throw new Exception($e->getMessage());
        }
        // dd($decoded);

    }
}
