<form
    method="POST"
    action="#"
    wire:submit.prevent="sendInvitationCode"
    x-data="{ loading: false }"
    x-on:submit="loading=true;"
>
    @csrf


    <h2 class="text-lg text-center font-semibold text-gray-500 mb-3">Send Invitation Code</h2>


    <x-label
        for="email"
        class="mb-2 text-center"
    >
        <p class="text-gray-800 mb-4">
            Enter your personal email to receive an invitation code.
        </p>

    </x-label>
    <div class="relative flex w-full items-center">
        <x-input
            id="email"
            class="{{ $errors->has('form.email') ? 'errors' : '' }} mt-1 block w-full"
            type="email"
            name="email"
            placeholder="Email"
            wire:model="form.email"
            autofocus
            required
            autocomplete="off"
        />
        @if ($errors->has('email'))
            <x-error-svg />
        @endif
    </div>


    <x-validation-errors class="errSection mb-4" />




    <div class="mt-4 flex items-center justify-end">
        <a
            class="mr-auto rounded-md text-sm text-gray-600 underline hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
            x-on:click.prevent="viewState = 'verify'"
            href="{{ route('login') }}"
        >
            {{ __('Enter invitation code') }}
        </a>


        <x-button
            class="ml-4"
            loading="Sending..."
            target="sendInvitationCode"
        >
            {{ __('Send') }}
        </x-button>

    </div>

    <p class="text-gray-700 ">
        <a
            class="mr-auto rounded-md text-sm text-gray-600 underline hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
            href="{{ route('login') }}"
        >
            {{ __('Already have an account?') }}
        </a>
    </p>

</form>
