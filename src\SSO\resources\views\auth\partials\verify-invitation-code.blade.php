<div>
    @if ($attempts < 3)
        <x-notification-message
            variant="warning"
            canhide="1"
        >{{ $attempts }} {{ $attempts == 1 ? 'Attempt' : 'Attempts' }} remaiing.</x-notification-message>
    @endif

    <form
        class="mt-4"
        method="POST"
        action="#"
        wire:submit.prevent="verify"
        x-data="{ loading: false }"
        x-on:submit="loading=true;"
    >
        @csrf

        {{-- <h2 class="text-lg text-center font-semibold text-gray-500 mb-3">Invitation Verification Required</h2> --}}


        @if ($state['verificationSent'])
            <div
                id="success-message"
                class="relative max-w-xl mx-auto my-4 bg-green-100 border border-green-300 text-green-900 p-6 rounded-lg shadow text-center"
            >
                <!-- Close Button -->
                <button
                    type="button"
                    title="Close"
                    x-on:click.prevent="$wire.set('state.verificationSent', false)"
                    class="absolute top-2 right-2 text-green-800 hover:text-green-900 focus:outline-none text-lg font-bold"
                    aria-label="Close"
                >
                    &times;
                </button>

                <!-- Content -->
                <div class="text-lg font-bold mb-2">Invitation Code Sent Successfully!</div>
                <p class="text-base">
                    Your invitation code has been sent to your personal email. Please check your email for the code.
                </p>
            </div>
        @else
            <x-label
                for="code"
                class="mb-2 text-center"
            >
                <p class="font-normal text-gray-800 mb-4">
                    If you are a <span class="font-medium text-blue-700">student</span> accessing Galaxy for the first
                    time,
                    please enter the <span class="font-medium">invitation code</span> that was sent to your personal
                    email.
                    This will link your academic account to your Galaxy profile.
                </p>
                <p class="font-normal text-gray-800">
                    If you are a <span class="font-medium text-green-700">staff member</span> and are unable to access
                    your
                    account, please contact the <span class="font-medium">Galaxy system administrator</span> for further
                    assistance.
                </p>
            </x-label>
        @endif
        <div class="relative flex w-full items-center">
            <x-input
                id="code"
                class="{{ $errors->has('form.code') ? 'errors' : '' }} mt-1 block w-full"
                type="code"
                name="code"
                placeholder="Invitation Code"
                wire:model="form.code"
                autofocus
                required
                autocomplete="off"
            />
            @if ($errors->has('code'))
                <x-error-svg />
            @endif
        </div>


        <x-validation-errors class="errSection mb-4" />




        <div class="mt-4 flex items-center justify-end">
            <a
                title="Click here to send an invitation code to your personal email"
                class="mr-auto rounded-md text-sm text-gray-600 underline hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                x-on:click.prevent="viewState = 'sendInvitationCode'"
                href="{{ route('login') }}"
            >
                {{ __('Did not receive an invitation code?') }}
            </a>


            <x-button
                class="ml-4"
                loading="Verifying..."
                target="verify"
            >
                {{ __('Join Galaxy360') }}
            </x-button>

        </div>

        <p class="text-gray-700 ">
            <a
                class="mr-auto rounded-md text-sm text-gray-600 underline hover:text-gray-900 focus:outline-none focus:ring-2 focus:ring-indigo-500 focus:ring-offset-2"
                href="{{ route('login') }}"
            >
                {{ __('Already have an account?') }}
            </a>
        </p>

    </form>
</div>
