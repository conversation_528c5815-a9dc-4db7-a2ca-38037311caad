<?php

namespace Support\Commands;

use Illuminate\Console\Command;
use Illuminate\Contracts\Console\PromptsForMissingInput;
use Illuminate\Support\Facades\File;

use function Laravel\Prompts\select;

class AddReleaseNote extends Command implements PromptsForMissingInput
{
    protected $signature = 'release:note {type}';

    protected $description = 'Run this command to add new entry to unreleased release note.';

    /**
     * Prompt for missing input arguments using the returned questions.
     *
     * @return array
     */
    protected function promptForMissingArgumentsUsing()
    {
        // : The IDS of the uploaded files
        // : Weather to update whole records, this will override the id option
        return [

            'type' => fn () => select(
                label: 'Which type of release note to add?',
                options: array_merge(['feature', 'bugfix', 'improvement', 'other'])
            ),
        ];
    }

    public function handle()
    {
        $this->info('Enter the title of the release note:');
        $title = $this->ask('Title');
        $this->info('Enter the description of the release note:');
        $description = $this->ask('Description');

        // $this->info('Enter the link of the release note:');
        // $link = $this->ask('Link');
        // $this->info('Enter the version of the release note:');
        // $version = $this->ask('Version');
        // $this->info('Enter the date of the release note:');
        // $date = $this->ask('Date');

        File::put(base_path('.changelog/unreleased/'.time().'-'.$title.'-'.$this->argument('type').'.md'), $description);
    }
}
