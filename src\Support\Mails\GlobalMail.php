<?php

namespace Support\Mails;

use Illuminate\Bus\Queueable;
use Illuminate\Mail\Mailable;
use Illuminate\Queue\SerializesModels;
use Support\DTO\MailAddress;
use Support\DTO\MailAttachment;

class GlobalMail extends Mailable
{
    use Queueable, SerializesModels;

    private $service;

    /**
     * Create a new message instance.
     *
     * @return void
     */
    public function __construct(EmailService $emailService)
    {
        $this->service = $emailService;
    }

    /**
     * Build the message.
     *
     * @return $this
     */
    public function build()
    {
        if (! is_null($this->service->getFrom()) && $this->service->getFrom() instanceof MailAddress) {
            $this->from($this->service->getFrom()->email, $this->service->getFrom()->name);
        }

        $attachments = $this->service->getAttachments();
        if (! empty($attachments)) {
            foreach ($attachments as $file) {
                if ($file instanceof MailAttachment) {
                    $this->attach($file->path, [
                        'as' => $file->name,
                    ]);
                }
            }
        }

        foreach ($this->service->getInlineAttachments() as $inlineAttachment) {
            if ($inlineAttachment instanceof MailAttachment) {
                $this->attachData($inlineAttachment->contents, $inlineAttachment->name);
            }
        }

        if (! is_null($this->service->getMailMessage())) {
            return $this
                ->subject($this->service->getSubject())
                ->html($this->service->getMailMessage()->render()->toHtml());
        }

        return $this
            ->subject($this->service->getSubject())
            ->markdown($this->service->getView())->with($this->service->getData());
    }
}
