<?php

namespace Support\Models;

use Spatie\Activitylog\Models\Activity as ModelsActivity;
use Support\Jobs\ModifyActivityChanges;

class Activity extends ModelsActivity
{
    public static function boot()
    {
        parent::boot();

        static::created(function (Activity $model) {
            // dd($model);
            if (isset($model->properties['attributes']) && count($model->properties['attributes'])) {
                // dispatch(new ModifyActivityChanges($model->id));
            }
        });
    }

    public function scopeXeroLogForStudentCourse($q, $studentId, $courseId)
    {
        return $q
            ->where(function ($q) use ($studentId, $courseId) {

                $q
                    ->orWhere('log_name', 'LIKE', 'xero_sync_invoice_'.$studentId.'_'.$courseId)
                    ->orWhere('log_name', 'LIKE', 'xero_sync_contact_'.$studentId)
                    ->orWhere('log_name', 'LIKE', 'xero_sync_item_'.$courseId);
            });
    }

    public function scopeLogForStudentCoursePayments($q, $type, $studentId, $courseId, $agentId)
    {
        if ($type == 'rsac') {
            return $q->where('log_name', 'LIKE', implode('_', [$type, $studentId, $courseId, $agentId]));
        } else {
            return $q->where('log_name', 'LIKE', implode('_', [$type, $studentId, $courseId]));
        }
    }

    public function scopeLogForStudentResult($q, $studentId, $courseId)
    {
        return $q
            ->where(function ($q) use ($studentId, $courseId) {

                $q
                    ->orWhere('log_name', 'LIKE', 'rsse_'.$studentId.'_'.$courseId)
                    ->orWhere('log_name', 'LIKE', 'rsaat_'.$studentId.'_'.$courseId)
                    ->orWhere('log_name', 'LIKE', 'rssev2_'.$studentId.'_'.$courseId)
                    ->orWhere('log_name', 'LIKE', 'rsaatv2_'.$studentId.'_'.$courseId)
                    ->orWhere('log_name', 'LIKE', 'rsue_'.$studentId.'_'.$courseId)
                    ->orWhere('log_name', 'LIKE', 'rsuev2_'.$studentId.'_'.$courseId);
            });
    }

    public function scopeLogForMoodleStudentResult($q, $studentId, $courseId)
    {
        return $q->where(function ($q) use ($studentId, $courseId) {
            $q->orWhere('log_name', 'LIKE', 'unit_enroll_'.$studentId.'_'.$courseId);
            $q->orWhere('log_name', 'LIKE', 'assessment_grade_'.$studentId.'_'.$courseId);
        });
    }

    public function scopeLogForCourseVariant($q, $studentId, $courseId)
    {
        return $q->where('log_name', 'LIKE', implode('_', ['rsd', $studentId, $courseId]));
    }
}
