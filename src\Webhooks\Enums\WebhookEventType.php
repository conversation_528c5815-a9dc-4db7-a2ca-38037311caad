<?php

namespace Webhooks\Enums;

enum WebhookEventType: string
{
    // User events
    case USER_CREATED = 'user.created';
    case USER_UPDATED = 'user.updated';
    // case USER_DELETED = 'user.deleted';

    // Agent events
    // case AGENT_CREATED = 'agent.created';
    // case AGENT_UPDATED = 'agent.updated';
    // case AGENT_DELETED = 'agent.deleted';

    // Agent events
    case STUDENT_CREATED = 'student.created';
    case STUDENT_ENROLLED = 'student.enrolled';
    case STUDENT_UPDATED = 'student.updated';
    case STUDENT_DELETED = 'student.deleted';
    case STUDENT_WITHDRAWN = 'student.withdrawn';
    case STUDENT_OFFERED = 'student.offered';
    case STUDENT_DEFERRED = 'student.deferred';
    case STUDENT_CANCELLED = 'student.cancelled';
    case STUDENT_AGENT_APPLIED = 'student.agent.applied';

    // Order events
    // case ORDER_CREATED = 'order.created';
    // case ORDER_UPDATED = 'order.updated';
    // case ORDER_CANCELLED = 'order.cancelled';

    // // Payment events
    // case PAYMENT_RECEIVED = 'payment.received';
    // case PAYMENT_FAILED = 'payment.failed';

    // // Subscription events
    // case SUBSCRIPTION_CREATED = 'subscription.created';
    // case SUBSCRIPTION_UPDATED = 'subscription.updated';
    // case SUBSCRIPTION_CANCELLED = 'subscription.cancelled';

    // Test event
    case WEBHOOK_TEST = 'webhook.test';

    /**
     * Get all event types as an array
     */
    public static function getAll(): array
    {
        return array_column(self::cases(), 'value');
    }

    /**
     * Get event type by value
     */
    public static function fromValue(string $value): ?self
    {
        foreach (self::cases() as $case) {
            if ($case->value === $value) {
                return $case;
            }
        }

        return null;
    }

    /**
     * Check if a value is a valid event type
     */
    public static function isValid(string $value): bool
    {
        return self::fromValue($value) !== null;
    }

    /**
     * Get the display name for the event
     */
    public function getDisplayName(): string
    {
        return match ($this) {
            self::USER_CREATED => 'User Created',
            self::USER_UPDATED => 'User Updated',
            // self::USER_DELETED => 'User Deleted',
            // self::AGENT_CREATED => 'Agent Created',
            // self::AGENT_UPDATED => 'Agent Updated',
            // self::AGENT_DELETED => 'Agent Deleted',
            self::STUDENT_CREATED => 'Student Created',
            self::STUDENT_ENROLLED => 'Student Enrolled',
            self::STUDENT_UPDATED => 'Student Updated',
            self::STUDENT_DELETED => 'Student Deleted',
            self::STUDENT_WITHDRAWN => 'Student Withdrawn',
            self::STUDENT_OFFERED => 'Student Offered',
            self::STUDENT_DEFERRED => 'Student Deferred',
            self::STUDENT_CANCELLED => 'Student Cancelled',
            self::STUDENT_AGENT_APPLIED => 'Student Agent Applied',
            // self::ORDER_CREATED => 'Order Created',
            // self::ORDER_UPDATED => 'Order Updated',
            // self::ORDER_CANCELLED => 'Order Cancelled',
            // self::PAYMENT_RECEIVED => 'Payment Received',
            // self::PAYMENT_FAILED => 'Payment Failed',
            // self::SUBSCRIPTION_CREATED => 'Subscription Created',
            // self::SUBSCRIPTION_UPDATED => 'Subscription Updated',
            // self::SUBSCRIPTION_CANCELLED => 'Subscription Cancelled',
            self::WEBHOOK_TEST => 'Webhook Test',
        };
    }
}
