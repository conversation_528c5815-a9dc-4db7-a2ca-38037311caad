<?php

namespace Webhooks\Events\Student;

use App\Model\v2\StudentCourses;
use App\Model\v2\StudentSubjectEnrolment;
use Webhooks\Events\AbstractWebhookEvent;

abstract class StudentEvent extends AbstractWebhookEvent
{
    public function __construct(public StudentCourses $studentCourse) {}

    abstract public function getEventName(): string;

    protected function getEventData(): array
    {
        $student = $this->studentCourse->student;
        $course = $this->studentCourse->course;

        return [
            // Student information
            'name_title' => @$student->name_title,
            'generated_student_id' => $student->generated_stud_id,
            'first_name' => $student->first_name,
            'middle_name' => $student->middel_name,
            'family_name' => $student->family_name,
            'gender' => $student->gender,
            'college_id' => $student->college_id,
            'application_reference_id' => $student->application_reference_id,
            'usi_invalid_reason' => $student->usi_invalid_reason,
            'status' => $student->status,
            'student_type' => $student->student_type,
            'email' => $student->email,
            'optional_email' => $student->optional_email,
            'birthplace' => $student->birthplace,
            'birth_country' => $student->birth_country,
            'nationality' => $student->nationality,
            'passport_no' => $student->passport_no,
            'passport_expiry' => $student->passport_expiry,
            'DOB' => $student->DOB,
            'visa_status' => $student->visa_status,
            'visa_number' => $student->visa_number,
            'visa_expiry_date' => $student->visa_expiry_date,
            'is_applicant' => $student->is_applicant,
            'current_country' => $student->current_country,
            'current_building_name' => $student->current_building_name,
            'current_unit_detail' => $student->current_unit_detail,
            'current_street_no' => $student->current_street_no,
            'current_street_name' => $student->current_street_name,
            'current_city' => $student->current_city,
            'current_state' => $student->current_state,
            'current_postcode' => $student->current_postcode,
            'current_mobile_phone' => $student->current_mobile_phone,
            'current_home_phone' => $student->current_home_phone,
            'permanent_country' => $student->permanent_country,
            'permanent_street_name' => $student->permanent_street_name,
            'postal_building_name' => $student->postal_building_name,
            'postal_city' => $student->postal_city,
            'postal_country' => $student->postal_country,
            'postal_postcode' => $student->postal_postcode,
            'postal_state' => $student->postal_state,
            'postal_street_name' => $student->postal_street_name,
            'postal_street_no' => $student->postal_street_no,
            'postal_unit_detail' => $student->postal_unit_detail,
            'is_postal_address' => $student->is_postal_address,
            'USI' => $student->USI,
            'read_and_agree' => $student->read_and_agree,
            'usi_identity' => $student->usi_identity,
            'city_of_birth' => $student->city_of_birth,
            'is_apply_usi' => $student->is_apply_usi,
            'profile_picture' => $student->profile_picture,
            'is_offered' => $student->is_offered,
            'is_student' => $student->is_student,

            // Course information
            'course' => [
                'course_code' => $course->course_code,
                'course_name' => $course->course_name,
                'cricos_code' => $course->cricos_code,
                'national_code' => $course->national_code,
                'course_duration' => $course->course_duration,
                'course_type_name' => $course->courseType->title ?? null,
                'course_type_id' => $course->course_type_id,
            ],

            // Student Course information
            'student_course_id' => $this->studentCourse->id,
            'course_status' => $this->studentCourse->status,
            'offer_status' => $this->studentCourse->offer_status,
            'start_date' => $this->studentCourse->start_date,
            'finish_date' => $this->studentCourse->finish_date,
            'campus_id' => $this->studentCourse->campus_id,

            // Unit enrollments for this student course
            'enrollments' => $this->getStudentUnitEnrollments(),
        ];
    }

    /**
     * Get unit enrollments for the student course
     */
    private function getStudentUnitEnrollments()
    {
        $enrollments = StudentSubjectEnrolment::where('student_id', $this->studentCourse->student_id)
            ->where('course_id', $this->studentCourse->course_id)
            ->with(['unit', 'subject'])
            ->get();

        return $enrollments->map(function ($enrollment) {
            return [
                'enrollment_id' => $enrollment->id,
                'subject_id' => $enrollment->subject_id,
                'subject_code' => $enrollment->subject->subject_code ?? null,
                'unit_id' => $enrollment->unit_id,
                'unit_name' => $enrollment->unit->unit_name ?? null,
                'unit_code' => $enrollment->unit->unit_code ?? null,
                'vet_unit_code' => $enrollment->unit->vet_unit_code ?? null,
                'activity_start_date' => $enrollment->activity_start_date,
                'activity_finish_date' => $enrollment->activity_finish_date,
                'final_outcome' => $enrollment->final_outcome,
                'delivery_mode' => $enrollment->delivery_mode,
                'census_date' => $enrollment->census_date,
            ];
        });
    }
}
