<?php

namespace Webhooks\Models;

use Illuminate\Database\Eloquent\Factories\HasFactory;
use Illuminate\Database\Eloquent\Model;
use Webhooks\Database\Factories\WebhookLogFactory;
use Webhooks\Enums\WebhookEventType;

class WebhookLog extends Model
{
    use HasFactory;

    protected $table = 'galaxy_webhook_logs';

    protected $fillable = [
        'webhook_id',
        'event_name',
        'status',
        'code',
        'response',
        'request_payload',
        'request_headers',
        'response_headers',
        'duration_ms',
    ];

    protected $casts = [
        'response_headers' => 'array',
        'request_payload' => 'array',
        'request_headers' => 'array',
        'duration_ms' => 'integer',
    ];

    const STATUS_SUCCESS = 'success';

    const STATUS_FAILED = 'failed';

    /**
     * Create a new factory instance for the model.
     */
    protected static function newFactory()
    {
        return WebhookLogFactory::new();
    }

    /**
     * Get the webhook that this log belongs to
     */
    public function webhook()
    {
        return $this->belongsTo(Webhook::class);
    }

    /**
     * Scope to get successful webhook logs
     */
    public function scopeSuccessful($query)
    {
        return $query->where('status', self::STATUS_SUCCESS);
    }

    /**
     * Scope to get failed webhook logs
     */
    public function scopeFailed($query)
    {
        return $query->where('status', self::STATUS_FAILED);
    }

    /**
     * Scope to get logs for a specific event
     */
    public function scopeForEvent($query, $eventName)
    {
        return $query->where('event_name', $eventName);
    }

    /**
     * Scope to get recent logs
     */
    public function scopeRecent($query, $days = 7)
    {
        return $query->where('created_at', '>=', now()->subDays($days));
    }

    /**
     * Get the event type enum for this log
     */
    public function getEventTypeAttribute()
    {
        return WebhookEventType::fromValue($this->event_name);
    }

    /**
     * Get the display name for the event
     */
    public function getEventDisplayNameAttribute()
    {
        $eventType = $this->event_type;

        return $eventType ? $eventType->getDisplayName() : $this->event_name;
    }

    public function wasUnsuccessful()
    {
        return strtolower($this->status) == self::STATUS_FAILED;
    }
}
