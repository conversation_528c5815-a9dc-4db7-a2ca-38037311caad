<div>
    <div class="">
        <!-- Header -->
        {{-- <div class="flex justify-between items-center mb-6">
            <div>
                <h1 class="text-3xl font-bold text-gray-900">Webhooks</h1>
                <p class="text-gray-600 mt-1">Manage your webhook endpoints</p>
            </div>
            <a href="{{ route('settings.webhooks.create') }}" 
               class="bg-indigo-500 hover:bg-indigo-700 text-white font-bold py-2 px-4 rounded">
                Add New Webhook
            </a>
        </div> --}}

        <!-- Stats -->
        {{-- <div class="grid grid-cols-1 md:grid-cols-3 gap-6 mb-6">
            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="w-8 h-8 text-blue-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Total Webhooks</p>
                        <p class="text-2xl font-semibold text-gray-900">{{ $this->totalWebhooks() }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="w-8 h-8 text-green-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M9 12l2 2 4-4m6 2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Active</p>
                        <p class="text-2xl font-semibold text-gray-900">{{ $this->activeWebhooks() }}</p>
                    </div>
                </div>
            </div>

            <div class="bg-white rounded-lg shadow-md p-6">
                <div class="flex items-center">
                    <div class="flex-shrink-0">
                        <svg class="w-8 h-8 text-red-500" fill="none" stroke="currentColor" viewBox="0 0 24 24">
                            <path stroke-linecap="round" stroke-linejoin="round" stroke-width="2" d="M10 14l2-2m0 0l2-2m-2 2l-2-2m2 2l2 2m7-2a9 9 0 11-18 0 9 9 0 0118 0z"></path>
                        </svg>
                    </div>
                    <div class="ml-4">
                        <p class="text-sm font-medium text-gray-500">Inactive</p>
                        <p class="text-2xl font-semibold text-gray-900">{{ $this->inactiveWebhooks() }}</p>
                    </div>
                </div>
            </div>
        </div> --}}

        <!-- Filters -->
        <div class="bg-white rounded-lg shadow-md p-6 mb-6">
            <div class="flex flex-col md:flex-row md:items-center md:justify-between space-y-4 md:space-y-0">
                <div class="flex flex-col md:flex-row space-y-4 md:space-y-0 md:space-x-4">
                    <!-- Search -->
                    <div class="flex-1 md:w-64">
                        <label
                            for="search"
                            class="sr-only"
                        >Search</label>
                        <div class="relative">
                            <div class="absolute inset-y-0 left-0 pl-3 flex items-center pointer-events-none">
                                <svg
                                    class="h-5 w-5 text-gray-400"
                                    fill="none"
                                    stroke="currentColor"
                                    viewBox="0 0 24 24"
                                >
                                    <path
                                        stroke-linecap="round"
                                        stroke-linejoin="round"
                                        stroke-width="2"
                                        d="M21 21l-6-6m2-5a7 7 0 11-14 0 7 7 0 0114 0z"
                                    ></path>
                                </svg>
                            </div>
                            <x-input
                                type="text"
                                id="search"
                                wire:model.live.debounce.300ms="search"
                                class="block w-full pl-10 pr-3 py-2 border border-gray-300 rounded-md leading-5 bg-white placeholder-gray-500 focus:outline-none focus:placeholder-gray-400 focus:ring-1 focus:ring-indigo-500 focus:border-indigo-500"
                                placeholder="Search webhooks..."
                            />
                        </div>
                    </div>

                    <!-- Status Filter -->
                    <div class="md:w-48">
                        <select
                            wire:model.lazy="statusFilter"
                            class="inputbox w-full rounded-lg border border-gray-300 bg-white px-3 py-2 text-sm leading-5 text-gray-700 shadow-sm"
                        >
                            <option value="">All Status</option>
                            <option value="active">Active</option>
                            <option value="inactive">Inactive</option>
                        </select>
                    </div>
                    <div
                        wire:loading
                        class="text-gray-500 text-sm mt-2 flex h-9 items-center justify-center"
                    >
                        Searching...
                    </div>
                </div>

                <div class="flex gap-2">
                    <x-v2.button
                        wire:click="clearFilters"
                        size="sm"
                        variant="secondary"
                    >
                        Clear Filters
                    </x-v2.button>

                    <x-v2.button
                        size="sm"
                        type="link"
                        href="{{ route('settings.webhooks.create') }}"
                        variant="primary"
                    >
                        <i class="fa-solid fas-plus"></i>
                        Add New Webhook
                    </x-v2.button>
                </div>
            </div>
        </div>

        <!-- Webhooks Table -->
        <div class="bg-white shadow-md rounded-lg">
            <div class="overflow-visible">
                <table class="min-w-full divide-y divide-gray-200">
                    <thead class="bg-gray-50">
                        <tr>
                            <th
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                                wire:click="sortBy('name')"
                            >
                                <div class="flex items-center">
                                    Name
                                    @if ($sortBy === 'name')
                                        <svg
                                            class="ml-1 h-4 w-4"
                                            fill="currentColor"
                                            viewBox="0 0 20 20"
                                        >
                                            <path
                                                fill-rule="evenodd"
                                                d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                                clip-rule="evenodd"
                                            ></path>
                                        </svg>
                                    @endif
                                </div>
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                URL</th>
                            <th
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider max-w-[200px]">
                                Events</th>
                            <th
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                                wire:click="sortBy('is_active')"
                            >
                                <div class="flex items-center">
                                    Status
                                    @if ($sortBy === 'is_active')
                                        <svg
                                            class="ml-1 h-4 w-4"
                                            fill="currentColor"
                                            viewBox="0 0 20 20"
                                        >
                                            <path
                                                fill-rule="evenodd"
                                                d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                                clip-rule="evenodd"
                                            ></path>
                                        </svg>
                                    @endif
                                </div>
                            </th>
                            <th
                                class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider cursor-pointer hover:bg-gray-100"
                                wire:click="sortBy('created_at')"
                            >
                                <div class="flex items-center">
                                    Created
                                    @if ($sortBy === 'created_at')
                                        <svg
                                            class="ml-1 h-4 w-4"
                                            fill="currentColor"
                                            viewBox="0 0 20 20"
                                        >
                                            <path
                                                fill-rule="evenodd"
                                                d="M5.293 7.293a1 1 0 011.414 0L10 10.586l3.293-3.293a1 1 0 111.414 1.414l-4 4a1 1 0 01-1.414 0l-4-4a1 1 0 010-1.414z"
                                                clip-rule="evenodd"
                                            ></path>
                                        </svg>
                                    @endif
                                </div>
                            </th>
                            <th class="px-6 py-3 text-left text-xs font-medium text-gray-500 uppercase tracking-wider">
                                Actions</th>
                        </tr>
                    </thead>
                    <tbody class="bg-white divide-y divide-gray-200">
                        {{-- <tr wire:loading class="">
                            <td
                                colspan="6"
                                class="px-6 py-12 text-center"
                            >
                                <div class="flex flex-col text-gray-500 w-full">
                                    <h3 class="mt-2 text-sm font-medium text-gray-900">Searching...</h3>
                                    <p class="mt-1 text-sm text-gray-500">
                                        Please wait while we search for webhooks...
                                    </p>

                                </div>
                            </td>
                        </tr> --}}
                        @forelse($this->webhooks as $webhook)
                            <tr class="hover:bg-gray-50">
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <a
                                        href="{{ route('settings.webhooks.logs', ['webhook_id' => encryptIt($webhook->id)]) }}"
                                        class="cursor-pointer  hover:text-blue-900"
                                    >{{ $webhook->name }}
                                    </a>
                                    @if ($webhook->lastLog)
                                        <span class="text-xs text-gray-500 font-bold flex items-center gap-1">
                                            {{ $webhook->lastLog->created_at->format('M j, Y') }}

                                            @if ($webhook->lastLog->wasUnsuccessful())
                                                <span class="text-xs text-red-500 font-bold">
                                                    (Last Webhook Failed)
                                                </span>
                                            @endif
                                        </span>
                                    @endif
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div
                                        class="text-sm text-gray-500 truncate max-w-xs"
                                        title="{{ $webhook->url }}"
                                    >
                                        {{ $webhook->url }}
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <div
                                        class="flex flex-wrap gap-1"
                                        x-data="{ showAll: false }"
                                        x-cloak
                                    >
                                        @foreach ($webhook->events as $k => $event)
                                            <span
                                                x-show="showAll || {{ $k }} < 3"
                                                class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-blue-100 text-blue-800"
                                            >
                                                {{ $event }}
                                            </span>
                                        @endforeach
                                        @if (count($webhook->events) > 3)
                                            <span
                                                class="cursor-pointer inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium bg-gray-100 text-gray-800"
                                                x-on:click="showAll = !showAll"
                                                x-bind:title="showAll ? 'Show Less' : 'Show all'"
                                            >
                                                <span x-show="!showAll">
                                                    +{{ count($webhook->events) - 3 }} more
                                                </span>
                                                <span x-show="showAll">
                                                    -{{ count($webhook->events) - 3 }} less
                                                </span>
                                            </span>
                                        @endif
                                    </div>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap">
                                    <span
                                        class="inline-flex items-center px-2.5 py-0.5 rounded-full text-xs font-medium {{ $webhook->is_active ? 'bg-green-100 text-green-800' : 'bg-red-100 text-red-800' }}"
                                    >
                                        {{ $webhook->is_active ? 'Active' : 'Inactive' }}
                                    </span>
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm text-gray-500">
                                    {{ $webhook->created_at->format('M j, Y') }}
                                </td>
                                <td class="px-6 py-4 whitespace-nowrap text-sm font-medium">
                                    <x-dropdown>
                                        <x-slot name="trigger">
                                            <span class="cursor-pointer k-icon k-i-more-horizontal"></span>
                                        </x-slot>
                                        <x-slot name="content">
                                            <div
                                                class="flex flex-col space-y-2 p-2 bg-white rounded-lg shadow-md"
                                                style="z-index: 1000;"
                                            >
                                                <a
                                                    href="{{ route('settings.webhooks.edit', encryptIt($webhook->id)) }}"
                                                    class="cursor-pointer text-indigo-600 hover:text-indigo-900"
                                                >Edit</a>

                                                <a
                                                    href="{{ route('settings.webhooks.logs', ['webhook_id' => encryptIt($webhook->id)]) }}"
                                                    class="cursor-pointer text-blue-600 hover:text-blue-900"
                                                >View Logs</a>

                                                <a
                                                    wire:click.prevent="testWebhook('{{ encryptIt($webhook->id) }}')"
                                                    class="cursor-pointer text-green-600 hover:text-green-900"
                                                >
                                                    Test
                                                </a>

                                                <a
                                                    wire:click.prevent="toggleWebhook('{{ encryptIt($webhook->id) }}')"
                                                    class="cursor-pointer text-yellow-600 hover:text-yellow-900"
                                                >
                                                    {{ $webhook->is_active ? 'Disable' : 'Enable' }}
                                                </a>

                                                <a
                                                    wire:click.prevent="deleteWebhook('{{ encryptIt($webhook->id) }}')"
                                                    wire:confirm="Are you sure you want to delete this webhook?"
                                                    class="cursor-pointer text-red-600 hover:text-red-900"
                                                >
                                                    Delete
                                                </a>
                                            </div>
                                        </x-slot>
                                    </x-dropdown>
                                </td>
                            </tr>
                        @empty
                            <tr wire:loading.class="hidden">
                                <td
                                    colspan="6"
                                    class="px-6 py-12 text-center"
                                >
                                    <div class="text-gray-500">
                                        <svg
                                            class="mx-auto h-12 w-12 text-gray-400"
                                            fill="none"
                                            stroke="currentColor"
                                            viewBox="0 0 24 24"
                                        >
                                            <path
                                                stroke-linecap="round"
                                                stroke-linejoin="round"
                                                stroke-width="2"
                                                d="M9 5H7a2 2 0 00-2 2v10a2 2 0 002 2h8a2 2 0 002-2V7a2 2 0 00-2-2h-2M9 5a2 2 0 002 2h2a2 2 0 002-2M9 5a2 2 0 012-2h2a2 2 0 012 2"
                                            ></path>
                                        </svg>
                                        <h3 class="mt-2 text-sm font-medium text-gray-900">No webhooks found</h3>
                                        <p class="mt-1 text-sm text-gray-500">
                                            @if ($search || $statusFilter)
                                                Try adjusting your search or filter criteria.
                                            @else
                                                Get started by creating a new webhook.
                                            @endif
                                        </p>
                                        @if (!$search && !$statusFilter)
                                            <div class="mt-6">
                                                <a
                                                    href="{{ route('settings.webhooks.create') }}"
                                                    class="inline-flex items-center px-4 py-2 border border-transparent shadow-sm text-sm font-medium rounded-md text-white bg-indigo-600 hover:bg-indigo-700"
                                                >
                                                    Add New Webhook
                                                </a>
                                            </div>
                                        @endif
                                    </div>
                                </td>
                            </tr>
                        @endforelse
                    </tbody>
                </table>
            </div>

            <!-- Pagination -->
            @if ($this->webhooks->hasPages())
                <div class="bg-white px-4 py-3 border-t border-gray-200 sm:px-6">
                    {{ $this->webhooks->links() }}
                </div>
            @endif
        </div>
    </div>
</div>
