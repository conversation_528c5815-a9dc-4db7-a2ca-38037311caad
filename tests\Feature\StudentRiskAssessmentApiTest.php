<?php

namespace Tests\Feature;

use Tests\TestCase;
use Illuminate\Foundation\Testing\RefreshDatabase;
use Illuminate\Foundation\Testing\WithFaker;
use App\Model\v2\User;
use Domains\Students\RiskAssessment\Models\StudentRiskAssessment;
use App\Model\v2\Students;
use App\Model\v2\Course;

class StudentRiskAssessmentApiTest extends TestCase
{
    use RefreshDatabase, WithFaker;

    protected $user;
    protected $college;

    protected function setUp(): void
    {
        parent::setUp();
        
        // Create a test user with college
        $this->user = User::factory()->create([
            'college_id' => 1,
        ]);
        
        $this->actingAs($this->user);
    }

    /** @test */
    public function it_returns_paginated_student_risk_assessments_with_summary()
    {
        // Create test data
        $students = Students::factory()->count(3)->create(['college_id' => 1]);
        $course = Course::factory()->create();
        
        // Create risk assessments with different risk levels
        StudentRiskAssessment::factory()->create([
            'student_id' => $students[0]->id,
            'course_id' => $course->id,
            'risk_level' => StudentRiskAssessment::RISK_TYPE_HIGH,
        ]);
        
        StudentRiskAssessment::factory()->create([
            'student_id' => $students[1]->id,
            'course_id' => $course->id,
            'risk_level' => StudentRiskAssessment::RISK_TYPE_MEDIUM,
        ]);
        
        StudentRiskAssessment::factory()->create([
            'student_id' => $students[2]->id,
            'course_id' => $course->id,
            'risk_level' => StudentRiskAssessment::RISK_TYPE_LOW,
        ]);

        // Make API request
        $response = $this->getJson('/api/v2/tenant/student-risk-assessments');

        // Assert response structure
        $response->assertStatus(200)
                ->assertJsonStructure([
                    'data' => [
                        '*' => [
                            'id',
                            'name',
                            'email',
                            'course' => ['code', 'id', 'name'],
                            'risk_level',
                            'progress',
                            'attendance',
                            'payment_status',
                            'last_contact',
                            'action',
                        ]
                    ],
                    'meta' => [
                        'current_page',
                        'last_page',
                        'per_page',
                        'total',
                    ],
                    'summary' => [
                        'total',
                        'high_risk',
                        'medium_risk',
                        'low_risk',
                    ],
                ]);

        // Assert summary counts
        $responseData = $response->json();
        $this->assertEquals(3, $responseData['summary']['total']);
        $this->assertEquals(1, $responseData['summary']['high_risk']);
        $this->assertEquals(1, $responseData['summary']['medium_risk']);
        $this->assertEquals(1, $responseData['summary']['low_risk']);
    }

    /** @test */
    public function it_filters_by_risk_level()
    {
        // Create test data
        $students = Students::factory()->count(2)->create(['college_id' => 1]);
        $course = Course::factory()->create();
        
        StudentRiskAssessment::factory()->create([
            'student_id' => $students[0]->id,
            'course_id' => $course->id,
            'risk_level' => StudentRiskAssessment::RISK_TYPE_HIGH,
        ]);
        
        StudentRiskAssessment::factory()->create([
            'student_id' => $students[1]->id,
            'course_id' => $course->id,
            'risk_level' => StudentRiskAssessment::RISK_TYPE_LOW,
        ]);

        // Test filtering by high risk
        $response = $this->getJson('/api/v2/tenant/student-risk-assessments?' . http_build_query([
            'filters' => json_encode(['riskLevel' => StudentRiskAssessment::RISK_TYPE_HIGH])
        ]));

        $response->assertStatus(200);
        $responseData = $response->json();
        
        // Should only return high risk students
        $this->assertCount(1, $responseData['data']);
        $this->assertEquals('HIGH', $responseData['data'][0]['risk_level']);
    }

    /** @test */
    public function it_handles_pagination_parameters()
    {
        // Create test data
        $students = Students::factory()->count(30)->create(['college_id' => 1]);
        $course = Course::factory()->create();
        
        foreach ($students as $student) {
            StudentRiskAssessment::factory()->create([
                'student_id' => $student->id,
                'course_id' => $course->id,
                'risk_level' => StudentRiskAssessment::RISK_TYPE_LOW,
            ]);
        }

        // Test pagination
        $response = $this->getJson('/api/v2/tenant/student-risk-assessments?' . http_build_query([
            'page' => 2,
            'rowsPerPage' => 10,
        ]));

        $response->assertStatus(200);
        $responseData = $response->json();
        
        $this->assertEquals(2, $responseData['meta']['current_page']);
        $this->assertEquals(10, $responseData['meta']['per_page']);
        $this->assertEquals(30, $responseData['meta']['total']);
        $this->assertCount(10, $responseData['data']);
    }

    /** @test */
    public function it_only_returns_data_for_user_college()
    {
        // Create students for different colleges
        $studentCollege1 = Students::factory()->create(['college_id' => 1]);
        $studentCollege2 = Students::factory()->create(['college_id' => 2]);
        $course = Course::factory()->create();
        
        StudentRiskAssessment::factory()->create([
            'student_id' => $studentCollege1->id,
            'course_id' => $course->id,
        ]);
        
        StudentRiskAssessment::factory()->create([
            'student_id' => $studentCollege2->id,
            'course_id' => $course->id,
        ]);

        // User is from college 1, should only see college 1 data
        $response = $this->getJson('/api/v2/tenant/student-risk-assessments');

        $response->assertStatus(200);
        $responseData = $response->json();
        
        $this->assertCount(1, $responseData['data']);
        $this->assertEquals(1, $responseData['summary']['total']);
    }
}
