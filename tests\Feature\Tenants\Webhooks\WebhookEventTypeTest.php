<?php

namespace Tests\Feature\Tenants\Webhooks;

use Tests\TenantTestCase;
use Webhooks\Enums\WebhookEventType;

uses(TenantTestCase::class);

beforeEach(function () {
    // Ensure we're in the correct tenant context
    expect(tenant('id'))->toBe('iie');
});

describe('WebhookEventType Enum', function () {
    it('has correct event values', function () {
        // $this->asertEquals(WebhookEventType::USER_CREATED->value, 'user.created');        expect(WebhookEventType::USER_CREATED->value)->toBe('user.created');
        expect(WebhookEventType::USER_UPDATED->value)->toBe('user.updated');
        expect(WebhookEventType::USER_DELETED->value)->toBe('user.deleted');
        expect(WebhookEventType::AGENT_CREATED->value)->toBe('agent.created');
        expect(WebhookEventType::AGENT_UPDATED->value)->toBe('agent.updated');
        expect(WebhookEventType::AGENT_DELETED->value)->toBe('agent.deleted');
        expect(WebhookEventType::STUDENT_CREATED->value)->toBe('student.created');
        expect(WebhookEventType::STUDENT_UPDATED->value)->toBe('student.updated');
        expect(WebhookEventType::STUDENT_DELETED->value)->toBe('student.deleted');
        expect(WebhookEventType::WEBHOOK_TEST->value)->toBe('webhook.test');
    });

    it('returns all event values as array', function () {
        $allValues = WebhookEventType::getAll();

        expect($allValues)->toBeArray();
        expect($allValues)->toContain('user.created');
        expect($allValues)->toContain('user.updated');
        expect($allValues)->toContain('agent.created');
        expect($allValues)->toContain('agent.updated');
        expect($allValues)->toContain('agent.deleted');
        expect($allValues)->toContain('student.created');
        expect($allValues)->toContain('student.updated');
        expect($allValues)->toContain('student.deleted');
        expect($allValues)->toContain('webhook.test');
        expect(count($allValues))->toBe(10); // Total number of events
    });

    it('validates event types correctly', function () {
        expect(WebhookEventType::isValid('user.created'))->toBeTrue();
        expect(WebhookEventType::isValid('user.updated'))->toBeTrue();
        expect(WebhookEventType::isValid('agent.created'))->toBeTrue();
        expect(WebhookEventType::isValid('agent.updated'))->toBeTrue();
        expect(WebhookEventType::isValid('agent.deleted'))->toBeTrue();
        expect(WebhookEventType::isValid('student.created'))->toBeTrue();
        expect(WebhookEventType::isValid('student.updated'))->toBeTrue();
        expect(WebhookEventType::isValid('student.deleted'))->toBeTrue();
        expect(WebhookEventType::isValid('webhook.test'))->toBeTrue();

        expect(WebhookEventType::isValid('invalid.event'))->toBeFalse();
        expect(WebhookEventType::isValid('user.invalid'))->toBeFalse();
        expect(WebhookEventType::isValid(''))->toBeFalse();
        expect(WebhookEventType::isValid('random.string'))->toBeFalse();
    });

    it('converts values to enum instances', function () {
        expect(WebhookEventType::fromValue('user.created'))->toBe(WebhookEventType::USER_CREATED);
        expect(WebhookEventType::fromValue('user.updated'))->toBe(WebhookEventType::USER_UPDATED);
        expect(WebhookEventType::fromValue('user.deleted'))->toBe(WebhookEventType::USER_DELETED);
        expect(WebhookEventType::fromValue('agent.created'))->toBe(WebhookEventType::AGENT_CREATED);
        expect(WebhookEventType::fromValue('agent.updated'))->toBe(WebhookEventType::AGENT_UPDATED);
        expect(WebhookEventType::fromValue('agent.deleted'))->toBe(WebhookEventType::AGENT_DELETED);
        expect(WebhookEventType::fromValue('student.created'))->toBe(WebhookEventType::STUDENT_CREATED);
        expect(WebhookEventType::fromValue('student.updated'))->toBe(WebhookEventType::STUDENT_UPDATED);
        expect(WebhookEventType::fromValue('student.deleted'))->toBe(WebhookEventType::STUDENT_DELETED);
        expect(WebhookEventType::fromValue('webhook.test'))->toBe(WebhookEventType::WEBHOOK_TEST);
    });

    it('returns null for invalid values', function () {
        expect(WebhookEventType::fromValue('invalid.event'))->toBeNull();
        expect(WebhookEventType::fromValue(''))->toBeNull();
        expect(WebhookEventType::fromValue('random.string'))->toBeNull();
    });

    it('returns correct display names', function () {
        expect(WebhookEventType::USER_CREATED->getDisplayName())->toBe('User Created');
        expect(WebhookEventType::USER_UPDATED->getDisplayName())->toBe('User Updated');
        expect(WebhookEventType::USER_DELETED->getDisplayName())->toBe('User Deleted');
        expect(WebhookEventType::AGENT_CREATED->getDisplayName())->toBe('Agent Created');
        expect(WebhookEventType::AGENT_UPDATED->getDisplayName())->toBe('Agent Updated');
        expect(WebhookEventType::AGENT_DELETED->getDisplayName())->toBe('Agent Deleted');
        expect(WebhookEventType::STUDENT_CREATED->getDisplayName())->toBe('Student Created');
        expect(WebhookEventType::STUDENT_UPDATED->getDisplayName())->toBe('Student Updated');
        expect(WebhookEventType::STUDENT_DELETED->getDisplayName())->toBe('Student Deleted');
        expect(WebhookEventType::WEBHOOK_TEST->getDisplayName())->toBe('Webhook Test');
    });
});
