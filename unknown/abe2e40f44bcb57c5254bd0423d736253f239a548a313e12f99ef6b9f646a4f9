<?php

namespace App\Repositories;

use App\Helpers\Helpers;
use App\Model\StudentCourse;
use App\Model\v2\CourseSubject;
use App\Model\v2\Student;
use App\Model\v2\StudentAttendance;
use App\Model\v2\StudentCommunicationLog;
use App\Model\v2\StudentCourseExtensionHistory;
use App\Model\v2\StudentCourses;
use App\Model\v2\StudentInitialPaymentDetails;
use App\Model\v2\StudentIntervention;
use App\Model\v2\StudentSanction;
use App\Model\v2\StudentSubjectEnrolment;
use App\Model\v2\TaskNew;
use App\Traits\CommonTrait;
use Domains\Moodle\Traits\MoodleStatusTrait;
use Illuminate\Support\Facades\DB;
use SSO\Factories\TaskPayloadFactory;
use SSO\Models\Integration;
use SSO\Services\IntegrationService;
use SSO\Services\TaskService;
use Support\Models\Activity;

class StudentSummaryTabRepository
{
    use CommonTrait;
    use MoodleStatusTrait;

    public function getActivityLog($request)
    {
        if (! empty($request->month)) {
            return StudentCommunicationLog::from('rto_student_communication as rsc')
                ->leftjoin('rto_users as user', 'user.id', '=', 'rsc.updated_by')
                ->leftjoin('rto_setup_section as rss1', 'rss1.id', '=', 'rsc.type')
                ->leftjoin('rto_setup_section as rss2', 'rss2.id', '=', 'rsc.status')
                ->where(['rsc.college_id' => $request->college_id, 'rsc.student_id' => $request->student_id])
                ->where('rsc.created_at', '>=', now()->subMonths($request->month))
                ->select('rsc.*', 'rsc.type as status_type', 'user.name as user_name', 'rss1.value as type', 'rss2.value as status', DB::raw("CONCAT(MONTHNAME(rsc.created_at),' , ',year(rsc.created_at)) as activity_month"))
                ->orderBy('rsc.created_at', 'DESC')
                ->limit(10)
                ->get()
                ->toArray();
        }

        return StudentCommunicationLog::from('rto_student_communication as rsc')
            ->leftjoin('rto_users as user', 'user.id', '=', 'rsc.updated_by')
            ->leftjoin('rto_setup_section as rss1', 'rss1.id', '=', 'rsc.type')
            ->leftjoin('rto_setup_section as rss2', 'rss2.id', '=', 'rsc.status')
            ->where(['rsc.college_id' => $request->college_id, 'rsc.student_id' => $request->student_id])
            ->select('rsc.*', 'rsc.type as status_type', 'user.name as user_name', 'rss1.value as type', 'rss2.value as status', DB::raw("CONCAT(MONTHNAME(rsc.created_at),' , ',year(rsc.created_at)) as activity_month"))
            ->orderBy('rsc.created_at', 'DESC')
            ->limit(5)
            ->get()
            ->toArray();
    }

    public function getCourseDetail($studentCourseId): array
    {
        $courseDetail = StudentCourses::from('rto_student_courses as t1')
            ->leftJoin('rto_courses as t2', 't2.id', '=', 't1.course_id')
            ->where('t1.id', $studentCourseId)
            ->select('t1.id', 't2.college_id', 't1.student_id', 't1.course_id')
            ->get()->first();

        return $courseDetail ? $courseDetail->toArray() : [];
    }

    public function getCourseProgress(array $whereArray): array
    {
        $whereArrayAlias = ['rto_student_subject_enrolment.college_id' => $whereArray['a1.college_id'], 'rto_student_subject_enrolment.student_id' => $whereArray['a1.student_id'], 'rto_student_subject_enrolment.course_id' => $whereArray['a1.course_id']];

        $courseProgress = StudentSubjectEnrolment::from('rto_student_subject_enrolment')
            ->leftJoin('rto_subject_unit', 'rto_subject_unit.id', '=', 'rto_student_subject_enrolment.unit_id')
            ->where($whereArrayAlias)
            ->withStudentCourseId($whereArray['a1.student_course_id'])
            ->select(
                'rto_student_subject_enrolment.id',
                'rto_student_subject_enrolment.unit_id',
                'rto_student_subject_enrolment.final_outcome',
                'rto_subject_unit.unit_name',
                'rto_subject_unit.unit_code',
                'rto_student_subject_enrolment.activity_start_date',
                'rto_student_subject_enrolment.activity_finish_date'
            )
            ->orderBy('rto_student_subject_enrolment.activity_start_date', 'DESC')
            ->get()->toArray();
        if (count($courseProgress) == 0) {
            $courseProgress = $this->getFallbackCourseProgress(['college_id' => $whereArray['a1.college_id'], 'course_id' => $whereArray['a1.course_id']]);
        }

        return $courseProgress;
    }

    private function getFallbackCourseProgress(array $courseDetail): array
    {
        return CourseSubject::from('rto_course_subject')
            ->join('rto_subject_unit', 'rto_subject_unit.subject_id', '=', 'rto_course_subject.subject_id')
            ->where([
                'rto_course_subject.college_id' => $courseDetail['college_id'],
                'rto_course_subject.course_id' => $courseDetail['course_id'],
            ])
            ->select('rto_course_subject.id', 'rto_course_subject.subject_id as unit_id', 'rto_subject_unit.unit_name')
            ->get()->toArray();

    }

    public function getCourseDetails($studentCourseId)
    {
        $sqlDateFormat = Helpers::toMysqlDateFormat();

        $res = StudentCourses::from('rto_student_courses')
            ->leftjoin('rto_courses', 'rto_courses.id', '=', 'rto_student_courses.course_id')
            ->leftjoin('rto_campus', 'rto_campus.id', '=', 'rto_student_courses.campus_id')
            ->leftjoin('rto_agents', 'rto_agents.id', '=', 'rto_student_courses.agent_id')
            ->where('rto_student_courses.id', $studentCourseId)
            ->select(
                'rto_student_courses.id',
                'rto_courses.college_id',
                'rto_student_courses.student_id',
                'rto_student_courses.course_id',
                'rto_courses.course_code',
                'rto_courses.course_name',
                'rto_student_courses.offer_status',
                'rto_student_courses.status',
                'rto_student_courses.coe_name',
                'rto_student_courses.agent_id',
                'rto_agents.agency_name',
                DB::raw("DATE_FORMAT(rto_student_courses.issued_date, '$sqlDateFormat') as issued_date"),
                'rto_student_courses.intake_year',
                'rto_student_courses.total_weeks as course_duration',
                'rto_student_courses.course_duration_type',
                'rto_student_courses.offer_id',
                'rto_courses.work_placement_hour',
                'rto_courses.vocational_duration',
                'rto_student_courses.course_fee',
                'rto_student_courses.enroll_fee',
                'rto_student_courses.course_upfront_fee',
                'rto_campus.name as campus_name',
                // 'latest_extension.old_finish_date',
                // DB::raw("DATE_FORMAT(rto_course_extension_history.finish_date, '$sqlDateFormat') as old_finish_date"),
                DB::raw("DATE_FORMAT(rto_student_courses.created_at, '$sqlDateFormat') as applied_date"),
                DB::raw("DATE_FORMAT(rto_student_courses.start_date, '$sqlDateFormat') as start_date"),
                DB::raw("DATE_FORMAT(rto_student_courses.finish_date, '$sqlDateFormat') as finish_date"),
                DB::raw('(CASE WHEN (rto_student_courses.finish_date > now() AND rto_student_courses.start_date > now()) THEN 0 WHEN rto_student_courses.finish_date > now() THEN '.dbRawL10('DATEDIFF(now(), rto_student_courses.start_date)').' ELSE '.dbRawL10('DATEDIFF(rto_student_courses.finish_date, rto_student_courses.start_date)').' END) as days'),
                DB::raw('DATEDIFF(rto_student_courses.finish_date, rto_student_courses.start_date) as diff_days')
            )
            ->orderBy(DB::raw('(CASE WHEN rto_student_courses.status = "Current Student" THEN "a1" WHEN rto_student_courses.status = "Enrolled" THEN "a2" ELSE rto_student_courses.status END)'), 'ASC')
            ->get()
            ->first();

        // TODO::GNG-4786 (Check extend study duration or not)
        $res->old_date_range = '';
        $latestExtension = StudentCourseExtensionHistory::where('student_course_id', $studentCourseId)->latest('created_at')->first();
        if ($latestExtension && $latestExtension->start_date && $latestExtension->finish_date) {
            $startDate = \Carbon\Carbon::parse($latestExtension->start_date)->format('d M Y');
            $finishDate = \Carbon\Carbon::parse($latestExtension->finish_date)->format('d M Y');
            $res->old_date_range = "$startDate - $finishDate";
        }

        return $res;
    }

    public function getCurrentCourse($whereArray)
    {
        $whereArrayAlias = ['rto_student_subject_enrolment.college_id' => $whereArray['a1.college_id'], 'rto_student_subject_enrolment.student_id' => $whereArray['a1.student_id'], 'rto_student_subject_enrolment.course_id' => $whereArray['a1.course_id']];

        $currentCourse = StudentSubjectEnrolment::from('rto_student_subject_enrolment')
            ->leftjoin('rto_subject_unit', 'rto_subject_unit.id', '=', 'rto_student_subject_enrolment.unit_id')
            ->where($whereArrayAlias)
            ->withStudentCourseId($whereArray['a1.student_course_id'])
            ->select(
                DB::raw('(SUM(CASE WHEN rto_student_subject_enrolment.final_outcome != "" THEN 1 ELSE 0 END)) as total_unit'),
                DB::raw('(SUM(CASE WHEN rto_student_subject_enrolment.final_outcome = "C" OR rto_student_subject_enrolment.final_outcome = "NYC" THEN 1 ELSE 0 END)) as use_unit'),
                DB::raw('CONCAT(SUM(CASE WHEN rto_student_subject_enrolment.final_outcome = "C" THEN 1 ELSE 0 END), " C, ", SUM(CASE WHEN rto_student_subject_enrolment.final_outcome = "NYC" THEN 1 ELSE 0 END), " NYC") as title')
            )
            ->groupBy('rto_student_subject_enrolment.course_id')
            ->get()->toArray();
        if (empty($currentCourse)) {
            $currentCourse = $this->getFallbackCourse(['college_id' => $whereArray['a1.college_id'], 'course_id' => $whereArray['a1.course_id']]);
        }

        return $currentCourse[0];
    }

    private function getFallbackCourse($courseDetail)
    {
        $currentCourse = CourseSubject::from('rto_course_subject')
            ->join('rto_subject_unit', 'rto_subject_unit.subject_id', '=', 'rto_course_subject.subject_id')
            ->where([
                'rto_course_subject.college_id' => $courseDetail['college_id'],
                'rto_course_subject.course_id' => $courseDetail['course_id'],
            ])
            ->select(
                DB::raw('(COUNT(rto_course_subject.id)) as total_unit'),
                DB::raw('(CASE WHEN rto_course_subject.id > 0 THEN 0 ELSE 0 END) as use_unit'),
                DB::raw('(CASE WHEN rto_course_subject.id > 0 THEN "0 C, 0 NYC" ELSE "0 C, 0 NYC" END) as title')
            )
            ->get()->toArray();
        if (count($currentCourse) == 0) {
            $currentCourse = [['title' => '0 C, 0 NYC', 'total_unit' => 0, 'use_unit' => 0]];
        }

        return $currentCourse;
    }

    public function getCoursePayment($studentCourseId, $courseFee)
    {
        $resArr = StudentInitialPaymentDetails::where('student_course_id', $studentCourseId)
            // ->where($whereArray)
            ->where('student_course_id', $studentCourseId)
            ->select([
                DB::raw("$courseFee as total"),
                DB::raw('(SUM(upfront_fee_to_pay)) as toPay'),
                DB::raw('(SUM(upfront_fee_pay)) as paid'),
                DB::raw('(SUM(CASE When due_date < now() THEN upfront_fee_to_pay ELSE 0 END)) as over_due_total'),
                DB::raw('(SUM(CASE When due_date < now() THEN upfront_fee_pay ELSE 0 END)) as over_due_paid'),
            ])
            ->get()
            ->first();

        $invoicedDueAmount = (isset($resArr->toPay)) ? $resArr->toPay : 0;
        $totalCoursePaidAmount = (isset($resArr->paid)) ? $resArr->paid : 0;
        $overDueTotal = (isset($resArr->over_due_total)) ? $resArr->over_due_total : 0;
        $overDuePaid = (isset($resArr->over_due_paid)) ? $resArr->over_due_paid : 0;

        $resArr->total = $courseFee;
        $resArr->paid = $totalCoursePaidAmount;
        $resArr->unpaid = $courseFee - $totalCoursePaidAmount;
        $resArr->due = $invoicedDueAmount - $totalCoursePaidAmount;
        $resArr->overDue = $overDueTotal - $overDuePaid;

        return $resArr;
    }

    public function saveActivityNote($logData)
    {
        return StudentCommunicationLog::create($logData);
    }

    public function updateActivityNote($id, $updateArray)
    {
        $studentCommunicationLog = StudentCommunicationLog::find($id);

        return $studentCommunicationLog->update($updateArray);
    }

    public function deleteActivityNote($id)
    {
        return StudentCommunicationLog::destroy($id);
    }

    public function getStudentCourseAttendanceData($collegeId, $studentId, $courseId)
    {
        $resArr = StudentAttendance::from('rto_student_attendance as rsa')
            ->join('rto_timetable as rt', 'rt.id', '=', 'rsa.timetable_id')
            ->join('rto_student_subject_enrolment as rsse', function ($join) use ($courseId, $studentId) {
                $join->on('rsse.semester_id', '=', 'rt.semester_id')
                    ->on('rsse.term', '=', 'rt.term')
                    ->on('rsse.course_id', '=', DB::raw($courseId))
                    ->on('rsse.student_id', '=', DB::raw($studentId));
            })
            ->where(['rt.college_id' => $collegeId, 'rsa.student_id' => $studentId])
            ->whereNotNull('rsa.week_period')
            ->groupBy(['rsa.id'])
            ->orderBy('rsa.attendance_date', 'DESC')
            ->select('rsa.id', 'rsa.week_period', 'rsa.total_hours')
            ->get()
            ->toArray();

        $res = [];
        foreach ($resArr as $row) {
            if (isset($res[$row['week_period']])) {
                $res[$row['week_period']] += $row['total_hours'];
            } else {
                $res[$row['week_period']] = $row['total_hours'];
            }
        }

        $res = array_values($res);

        $data = [
            ['Name' => 'Week 1', 'Value' => $this->getAttendanceValue($res, 0), 'valueColor' => '#40A9FF'],
            ['Name' => 'Week 2', 'Value' => $this->getAttendanceValue($res, 1), 'valueColor' => '#6366F1'],
            ['Name' => 'Week 3', 'Value' => $this->getAttendanceValue($res, 2), 'valueColor' => '#F97316'],
            ['Name' => 'Week 4', 'Value' => $this->getAttendanceValue($res, 3), 'valueColor' => '#10B981'],
            ['Name' => 'Week 5', 'Value' => $this->getAttendanceValue($res, 4), 'valueColor' => '#EF4444'],
            ['Name' => 'Week 6', 'Value' => $this->getAttendanceValue($res, 5), 'valueColor' => '#F97316'],
        ];

        return $data;
    }

    private function getAttendanceValue($res, $index)
    {
        return isset($res[$index]) ? $res[$index] : 0;
    }

    public function getCourseId($studentCourseId)
    {
        return StudentCourses::find($studentCourseId);
    }

    public function getActivityLogTypeData($request)
    {
        $search = ! empty($request['searchText']) ? $request['searchText'] : '';

        $resultData = $this->getTabSyncLogData($request);

        $sql = StudentCommunicationLog::from('rto_student_communication as rsc')
            ->leftjoin('rto_users as user', 'user.id', '=', 'rsc.updated_by')
            ->leftjoin('rto_setup_section as rss1', 'rss1.id', '=', 'rsc.type')
            ->leftjoin('rto_setup_section as rss2', 'rss2.id', '=', 'rsc.status')
            ->where(['rsc.college_id' => $request['postData']['college_id'], 'rsc.student_id' => $request['postData']['student_id']]);
        //                ->where(['rsc.college_id' => $request['postData']['college_id']]);
        if (! empty($request['postData']['month'])) {
            $sql->where('rsc.created_at', '>=', now()->subMonths($request['postData']['month']));
        }
        if ($request['tabName'] != 'all') {
            $sql->where('rsc.activity_log', $request['tabName']);
        }

        if (! empty($request['page'])) {
            $result = $sql->select('rsc.*', 'rsc.type as status_type', 'user.name as user_name', 'rss1.value as type', 'rss2.value as status', DB::raw("CONCAT(MONTHNAME(rsc.created_at),' , ',year(rsc.created_at)) as activity_month"))
                ->orderBy('rsc.created_at', 'DESC')
                ->paginate(10)
                ->toArray();
        } else {
            if (! empty($request['searchText'])) {

                $sql->where(function ($query) use ($search) {
                    $query->where('rsc.log', 'LIKE', '%'.$search.'%')
                        ->orWhere('rsc.created_at', 'LIKE', '%'.$search.'%')
                        ->orWhere('rsc.today_date', 'LIKE', '%'.$search.'%');
                });

                $result['data'] = $sql->select('rsc.*', 'rsc.type as status_type', 'user.name as user_name', 'rss1.value as type', 'rss2.value as status', DB::raw("CONCAT(MONTHNAME(rsc.created_at),' , ',year(rsc.created_at)) as activity_month"))
                    ->orderBy('rsc.created_at', 'DESC')
                    ->get()
                    ->toArray();
            } else {
                $result['data'] = $sql->select('rsc.*', 'rsc.type as status_type', 'user.name as user_name', 'rss1.value as type', 'rss2.value as status', DB::raw("CONCAT(MONTHNAME(rsc.created_at),' , ',year(rsc.created_at)) as activity_month"))
                    ->orderBy('rsc.created_at', 'DESC')
                    ->get()
                    ->toArray();
            }

        }

        if (empty($result)) {
            $result = ['data' => []];
        }
        $resultS = array_merge($result['data'], $resultData);

        $result['data'] = $resultS;

        return $result;
    }

    public function getTabSyncLogData($request)
    {

        $studCourseData = StudentCourse::find($request['postData']['student_course_id']);

        $data = Activity::with(['causer'])
            ->logForStudentResult($studCourseData->student_id, $studCourseData->course_id)
            ->orderBy('id', 'desc')->get()->toArray();

        if ($this->isMoodleConnected()) { // Ensure isConnect() is called as a method
            $moodleData = Activity::with(['causer'])
                ->logForMoodleStudentResult($studCourseData->student_id, $studCourseData->course_id)
                ->orderBy('id', 'desc')
                ->get()
                ->toArray();
            $data = array_merge($data, $moodleData);
        }

        foreach ($data as $key => $value) {
            $data[$key] = $value;
            $data[$key]['log'] = nl2br($value['description']);
            $data[$key]['activity_log'] = 'result';
            $data[$key]['created_at'] = $value['created_at'];
            $data[$key]['activity_month'] = Helpers::convertDateToReadableFormat($value['created_at'], 'F Y');      // date('F, Y', strtotime($value['created_at']));
            $data[$key]['date_time'] = Helpers::convertDateTimeToReadableFormat($value['created_at']);              // date('M d, Y h:i A', strtotime($value['created_at']));
            $data[$key]['user_name'] = $value['causer']['name'] ?? null;
            $data[$key]['type'] = 'result';
            $data[$key]['status'] = '';
            $data[$key]['status_type'] = $value['event'];
            $data[$key]['note_attachment'] = '';
            $data[$key]['note_attachment_path'] = '';
            $data[$key]['log_updated_properties'] = $this->setAttributeFiled($value['properties']);
        }

        return $data;
    }

    public function getResultTabSummary($request)
    {
        $collegeId = $request->college_id;
        $studentId = $request->student_id;
        $studentCourseId = $request->student_course_id;
        $data = StudentSubjectEnrolment::from('rto_student_subject_enrolment as rsse')
            ->leftjoin('rto_semester', 'rto_semester.id', '=', 'rsse.semester_id')
            ->leftjoin('rto_student_courses', 'rto_student_courses.course_id', '=', 'rsse.course_id')
            ->where(['rsse.college_id' => $collegeId, 'rsse.student_id' => $studentId, 'rto_student_courses.id' => $studentCourseId])
            ->select([DB::raw("CONCAT(rto_semester.semester_name,' Term ', rsse.term, ' ' ) as semester_term"), 'rsse.semester_id', 'rsse.term', 'rto_student_courses.id as student_course_id', 'rsse.id'])
            ->groupBy('semester_term')
            ->orderByDesc('rto_semester.id')
            ->orderByDesc('rsse.term')
            ->get()->toArray();

        return $data;
    }

    public function getStudentTaskData($collegeId, $studentId)
    {
        $result = TaskNew::from('rto_task_new')
            ->where('rto_task_new.college_id', $collegeId)
            ->where('rto_task_new.student_id', $studentId)
            ->where('rto_task_new.subtask_id', 0)
            ->whereNull('rto_task_new.template_id')
            ->orderBy('rto_task_new.id', 'desc')
            ->limit('5')
            ->select('rto_task_new.id as taskid', 'rto_task_new.title as title', 'rto_task_new.created_at as task_lastupdate')
            ->get()
            ->toArray();

        for ($i = 0; $i < count($result); $i++) {
            $result[$i]['task_lastupdate'] = Helpers::convertDateToReadableFormat($result[$i]['task_lastupdate']); // date('M d, Y',strtotime($result[$i]['task_lastupdate']));
            $result[$i]['subtask'] = TaskNew::from('rto_task_new')->where('rto_task_new.subtask_id', $result[$i]['taskid'])->count();
        }

        $integration = IntegrationService::getIntegration(Integration::INTEGRATION_QTASK);
        if (! $integration || ! $integration->isActive()) {
            return $result;
        }
        $studentEmailId = Student::where('id', $studentId)->value('email');
        $params = TaskPayloadFactory::CreateForGetTask($studentEmailId, 5);

        $taskService = new TaskService($integration->repo());

        return $taskService->getTasks($params);

    }

    public function getStudentIntervention($collegeId, $studentId, $courseId)
    {
        return StudentIntervention::leftjoin('rto_student_intervention_strategy', 'rto_student_intervention_strategy.intervention_id', '=', 'rto_student_intervention.id')
            ->leftjoin('rto_intervention_type', 'rto_intervention_type.id', '=', 'rto_student_intervention_strategy.intervention_type_id')
            ->leftjoin('rto_intervention_strategy', 'rto_intervention_strategy.id', '=', 'rto_student_intervention_strategy.intervention_strategy_id')
            ->where(['rto_student_intervention.course_id' => $courseId, 'rto_student_intervention.college_id' => $collegeId, 'rto_student_intervention.student_id' => $studentId, 'rto_student_intervention.case_status' => '1'])
            ->select('rto_student_intervention.*', 'rto_intervention_type.intervention_type', 'rto_intervention_strategy.strategy',
                DB::raw("DATE_FORMAT(due_date, '%d-%m-%Y') as due_date"),
                DB::raw("IF(rto_student_intervention.case_status = 1, 'Open', 'Close') as case_status")
            )
            ->orderBy('id', 'desc')->first();
    }

    public function getStudentSanction($collegeId, $studentId)
    {
        $sanction = StudentSanction::where([
            'college_id' => $collegeId,
            'student_id' => $studentId,
            'is_active' => '1',
        ])
            ->select('*', DB::raw("DATE_FORMAT(active_date, '%d-%m-%Y') as active_date"))
            ->orderBy('id', 'desc')
            ->first();
        if ($sanction) {
            $arrSenctionType = config('constants.arrSenctionType');
            $sanction['typeName'] = $arrSenctionType[$sanction['type']] ?? 'Unknown';
        }

        return $sanction;

    }

    public function getQTaskListUrl($studentEmail)
    {
        $qtaskIntegration = Integration::QtaskConnection();
        // Check if Qtask integration exists and is properly configured
        if (! $qtaskIntegration) {
            return null; // or return a default URL or empty string
        }

        return $qtaskIntegration->myTaskListUrl($studentEmail);

    }
}
